//
//  CrashlyticsLogger.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 6/4/19.
//  Copyright © 2019 PayMaya Philippines, Inc. All rights reserved.
//

import Error
import FirebaseCrashlytics
import Foundation

/// Utility class for logging via crashlytics
public class CrashlyticsLogger {
    var crashlyticsInstance: Crashlytics { Crashlytics.crashlytics() }

    public init() {}

    // MARK: - Configuration methods
    public func setCollectionEnabled(_ enabled: Bool) {
        crashlyticsInstance.setCrashlyticsCollectionEnabled(enabled)
    }

    public func setUserId(_ id: String) {
        crashlyticsInstance.setUserID(id)
    }

    // MARK: - Log methods
    public func log(_ message: String) {
        #if RELEASE || CONFIGURATION_ADHOC
        crashlyticsInstance.log(message)
        #endif
    }

    public func log(controllerName: String, functionName: String = #function) {
        log("\(controllerName) - \(functionName)")
    }

    public func fullLog(controllerName: String, functionName: String = #function, fileName: String = #file, lineNumber: Int = #line) {
        let className = (fileName as NSString).lastPathComponent
        log("\(controllerName) - \(functionName) at File:\(className)/Line:\(lineNumber)")
    }

    // MARK: - Non-fatal methods
    public func logNonFatalError(_ error: ErrorLog) {
        #if RELEASE || CONFIGURATION_ADHOC
        if case .databaseError(let underlying, _, let customKeys) = error, var customKeys {
            /// Merge error's original user info into custom keys
            for (key, value) in (underlying as NSError).userInfo {
                customKeys[key] = value
            }
            logNonFatalError(underlying, customKeys: customKeys)
        } else {
            crashlyticsInstance.record(error: error)
        }
        #endif
    }

    public func logNonFatalError(_ error: Swift.Error, customKeys: [String: Any]?) {
        #if RELEASE || CONFIGURATION_ADHOC
        let nsError = error as NSError
        let err = NSError(domain: nsError.domain, code: nsError.code, userInfo: customKeys)
        crashlyticsInstance.record(error: err)
        #endif
    }
}
