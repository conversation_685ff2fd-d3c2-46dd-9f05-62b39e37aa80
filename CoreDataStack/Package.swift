// swift-tools-version:5.3
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "CoreDataStack",
    platforms: [
        .iOS(.v11)
    ],
    products: [
        .library(
            name: "CoreDataStack",
            targets: ["CoreDataStack"])
    ],
    dependencies: [
        .package(name: "Sync", url: "https://gitlab.corp.paymaya.com/issuing/consumer-app-and-features/mobile/ios/sync.git", .exact("6.5.5")),
        .package(name: "Error", path: "../Error"),
        .package(name: "Injector", path: "../Injector")
    ],
    targets: [
        .target(
            name: "CoreDataStack",
            dependencies: [
                "Sync",
                "Error",
                "Injector"
            ]
        )
    ],
    swiftLanguageVersions: [.v5]
)
