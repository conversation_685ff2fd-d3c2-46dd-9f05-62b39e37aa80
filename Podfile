platform :ios, '14.0'
source '***************************:issuing/consumer-app-and-features/mobile/ios/cocoapods-specs.git'
source 'https://cdn.cocoapods.org/'
source 'https://github.com/goinstant/pods-specs-public'
source '***************************:finserv/mayabank/consumer-savings/flutter-podspec-repository.git'
# credolab
source 'https://token:<EMAIL>/basic/credolab/proxy-sdk/cocoapods/index.git'
source 'https://github.com/getsentry/sentry-cocoa.git'

# ignore all warnings from all pods
inhibit_all_warnings!
use_frameworks!

def public_pods
  pod 'AppsFlyerFramework', '6.10.0'
  pod 'Flutter', FLUTTER_VERSION, :configurations => ['Adhoc', 'Release']
  pod 'Flutter-Debug', FLUTTER_VERSION, :configurations => ['Debug']
  pod 'LicensePlist', '3.25.1'
  pod 'SZTextView', '1.3.0'
  pod 'ShieldFraud', SHIELD_VERSION
  pod 'SwiftGen', '6.6.3'
  pod 'TensorFlowLiteSwift', TENSORFLOW_VERSION

  # KYC
  pod 'UIImageViewAlignedSwift', '0.8.1'

  # Pre-Maya 2.0
  pod 'MBProgressHUD', '1.2.0'
  pod 'TTTAttributedLabel', '2.0.0'
  
  # Credolab
  pod 'CredoAppCore', '3.4.0'
  pod 'CredoAppCalendarEvents', '3.2.0'
  pod 'CredoAppContacts', '3.1.0'
  pod 'CredoAppMedia', '3.2.0'
end

def private_pods
  pod 'VoyagerUpdaterSDK', '0.0.9'
end

def unittest_pods
  pod 'Flutter', FLUTTER_VERSION, :configurations => ['Adhoc', 'Release']
  pod 'Flutter-Debug', FLUTTER_VERSION, :configurations => ['Debug']
  pod 'Paymaya-Flutter-Dev', FLUTTER_DEV_VERSION, :configurations => ['Adhoc', 'Release']
  pod 'Paymaya-Flutter-Dev-Debug', FLUTTER_DEV_VERSION, :configurations => ['Debug']
  pod 'ShieldFraud', SHIELD_VERSION
  pod 'TensorFlowLiteSwift', TENSORFLOW_VERSION
end

def notificationcontent_pods
    pod 'CTNotificationContent', '0.1.4'
end

def notificationservice_pods
    pod 'CTNotificationService', '0.1.2'
end

def which(cmd)
  exts = ENV['PATHEXT'] ? ENV['PATHEXT'].split(';') : ['']
  ENV['PATH'].split(File::PATH_SEPARATOR).each do |path|
    exts.each { |ext|
      exe = File.join(path, "#{cmd}#{ext}")
      return exe if File.executable?(exe) && !File.directory?(exe)
    }
  end
  return nil
end

def setup
  # check if required binaries exist
  unless which("swift") && which("git")
    abort("Missing required library.")
  end

  # Pre-install scripts
  pre_install do |installer|
    # Build as static library
    installer.pod_targets.each do |pod|
      def pod.build_type;
        Pod::BuildType.static_framework
      end
    end
  end

  # Post-install scripts
  post_install do |installer|
    require 'fileutils'
    # Prepare encryption
    system("ruby VoyagerGE/VGEPrepareCommand.rb")

    # Update pod settings to remove xcode warnings
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
        if config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'].to_f < 12.0
          config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
        end
        if target.respond_to?(:product_type) and target.product_type == "com.apple.product-type.bundle"
          target.build_configurations.each do |config|
            config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
          end
        end
      end
    end
  end
end

abstract_target 'App' do
  # Pod version constants
  DEBUG_SWIFT_VERSION = '0.3.6'
  SHIELD_VERSION = '1.5.49'
  SWIFT_LINT_VERSION = '0.56.2'
  TENSORFLOW_VERSION = '2.4.0'

  DEFAULT_FLUTTER_SDK_VERSION = '0.0.6089'
  DEFAULT_FLUTTER_VERSION = '3.22.300'

  # Flutter pod versions
  # The environment variables are defined in the GitLab pipeline
  # If not defined, will use the constant instead
  FLUTTER_DEV_VERSION = (ENV['FLUTTER_DEV_VERSION'].nil? || ENV['FLUTTER_DEV_VERSION'].to_s.empty?) ? DEFAULT_FLUTTER_SDK_VERSION : ENV['FLUTTER_DEV_VERSION'].to_s
  FLUTTER_STAGE_VERSION = (ENV['FLUTTER_STAGE_VERSION'].nil? || ENV['FLUTTER_STAGE_VERSION'].to_s.empty?) ? DEFAULT_FLUTTER_SDK_VERSION : ENV['FLUTTER_STAGE_VERSION'].to_s
  FLUTTER_PROD_VERSION = (ENV['FLUTTER_PROD_VERSION'].nil? || ENV['FLUTTER_PROD_VERSION'].to_s.empty?) ? DEFAULT_FLUTTER_SDK_VERSION : ENV['FLUTTER_PROD_VERSION'].to_s
  FLUTTER_VERSION = (ENV['FLUTTER_VERSION'].nil? || ENV['FLUTTER_VERSION'].to_s.empty?) ? DEFAULT_FLUTTER_VERSION : ENV['FLUTTER_VERSION'].to_s

  # Public Pods
  public_pods

  # Voyager Pods
  private_pods

  # Setup/Postinstall scripts
  setup

  target 'PayMaya' do
    pod 'CloudHuiYanSDK_FW', :path => './PayMaya-Frameworks/CloudHuiYanSDK_FW'
    pod 'Paymaya-Flutter-Prod', FLUTTER_PROD_VERSION
  end

  target 'PayMaya-Development' do
    pod 'DebugSwift', DEBUG_SWIFT_VERSION
    pod 'CloudHuiYanSDK_FW', :path => './PayMaya-Frameworks/CloudHuiYanSDK_FW'
    pod 'Paymaya-Flutter-Dev', FLUTTER_DEV_VERSION, :configurations => ['Adhoc', 'Release']
    pod 'Paymaya-Flutter-Dev-Debug', FLUTTER_DEV_VERSION, :configurations => ['Debug']
  end

  target 'PayMaya-Sandbox' do
    pod 'DebugSwift', DEBUG_SWIFT_VERSION
    pod 'CloudHuiYanSDK_FW', :path => './PayMaya-Frameworks/CloudHuiYanSDK_FW'
    pod 'Paymaya-Flutter-Stage', FLUTTER_STAGE_VERSION, :configurations => ['Adhoc', 'Release']
    pod 'Paymaya-Flutter-Stage-Debug', FLUTTER_STAGE_VERSION, :configurations => ['Debug']
  end

  target 'PayMaya-Staging' do
    pod 'DebugSwift', DEBUG_SWIFT_VERSION
    pod 'CloudHuiYanSDK_FW', :path => './PayMaya-Frameworks/CloudHuiYanSDK_FW'
    pod 'Paymaya-Flutter-Stage', FLUTTER_STAGE_VERSION, :configurations => ['Adhoc', 'Release']
    pod 'Paymaya-Flutter-Stage-Debug', FLUTTER_STAGE_VERSION, :configurations => ['Debug']
  end

  target 'PayMaya-Test' do
    pod 'DebugSwift', DEBUG_SWIFT_VERSION
    pod 'SwiftLint', SWIFT_LINT_VERSION
    pod 'CloudHuiYanSDK_FW', :path => './PayMaya-Frameworks/CloudHuiYanSDK_FW'
    pod 'Paymaya-Flutter-Dev', FLUTTER_DEV_VERSION, :configurations => ['Adhoc', 'Release']
    pod 'Paymaya-Flutter-Dev-Debug', FLUTTER_DEV_VERSION, :configurations => ['Debug']
  end

  target 'PayMaya-Automation' do
    pod 'DebugSwift', DEBUG_SWIFT_VERSION
    pod 'CloudHuiYanSDK_FW', :path => './PayMaya-Frameworks/CloudHuiYanSDK_FW'
    pod 'Paymaya-Flutter-Dev', FLUTTER_DEV_VERSION, :configurations => ['Adhoc', 'Release']
    pod 'Paymaya-Flutter-Dev-Debug', FLUTTER_DEV_VERSION, :configurations => ['Debug']
  end
end

# Test Targets
target 'PayMaya-UnitTests' do
  unittest_pods
end

target 'PayMayaUITests' do
  inherit! :search_paths
  # Pods for UI testing
end

# App Extensions
target 'PayMaya-NotificationContent' do
  notificationcontent_pods
end

target 'PayMaya-NotificationService' do
  notificationservice_pods
end

target 'PayMaya-Sandbox-NotificationContent' do
  notificationcontent_pods
end

target 'PayMaya-Sandbox-NotificationService' do
  notificationservice_pods
end

target 'PayMaya-Staging-NotificationContent' do
  notificationcontent_pods
end

target 'PayMaya-Staging-NotificationService' do
  notificationservice_pods
end

target 'PayMaya-Test-NotificationContent' do
  notificationcontent_pods
end

target 'PayMaya-Test-NotificationService' do
  notificationservice_pods
end

target 'PayMaya-Dev-NotificationContent' do
  notificationcontent_pods
end

target 'PayMaya-Dev-NotificationService' do
  notificationservice_pods
end
