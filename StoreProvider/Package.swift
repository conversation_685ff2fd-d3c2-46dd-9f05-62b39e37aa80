// swift-tools-version:5.3
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "StoreProvider",
    platforms: [
        .iOS(.v14)
    ],
    products: [
        .library(
            name: "StoreProvider",
            targets: ["StoreProvider"])
    ],
    dependencies: [
        .package(name: "CryptoSwift", url: "https://github.com/krzyzanowskim/CryptoSwift.git", .exact("1.8.3")),
        .package(name: "ReactiveSwift", url: "https://github.com/ReactiveCocoa/ReactiveSwift.git", .exact("6.7.0")),
        .package(name: "Sync", url: "https://gitlab.corp.paymaya.com/issuing/consumer-app-and-features/mobile/ios/sync.git", .exact("6.5.5")),
        .package(name: "VoyagerGenericEncryption", url: "***************************:issuing/consumer-app-and-features/mobile/ios/voyager-generic-encryption.git", .exact("1.1.9")),
        .package(name: "CoreDataStack", path: "../CoreDataStack"),
        .package(name: "Error", path: "../Error"),
        .package(name: "Injector", path: "../Injector"),
        .package(name: "UtilityExtension", path: "../UtilityExtension")
    ],
    targets: [
        .target(
            name: "StoreProvider",
            dependencies: ["Error",
                           "CryptoSwift",
                           "ReactiveSwift",
                           "Sync",
                           "VoyagerGenericEncryption",
                           "Injector",
                           "UtilityExtension",
                           "CoreDataStack"])
    ],
    swiftLanguageVersions: [.v5]
)
