//
//  StoreLog.swift
//  
//
//  Created by <PERSON><PERSON><PERSON> on 5/11/21.
//

enum StoreLog {
    enum EncryptedDefaults {
        static let unavailableAPIError = "API is not available for this store."
        static let userInfoDeletedSuccess = "UserInfo successfully deleted"
        static let appInfoDeletedSuccess = "AppInfo successfully deleted"
        static let readMissingError = "Stored data does not exist yet."
        static let readCorruptedError = "Stored data is corrupted."
        static let readDecryptionError = "Unable to decrypt data with %@ key and %@ iv."
        static let readMappingError = "Unable to map the decrypted data to the requested type."
        static let writeSerializeError = "Unable to serialize the object to data."
        static let writeEncryptionError = "Unable to encrypt data with %@ key and %@ iv."
        static let accessEncryptionError = "Unable to access file due to protection rules."
    }

    enum UserDefaults {
        static let accessEncryptionError = "Unable to access file due to protection rules."
    }

    enum Database {
        static let accessEncryptionError = "Unable to access file due to protection rules."
    }
}
