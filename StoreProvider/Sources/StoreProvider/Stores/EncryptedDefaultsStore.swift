//
//  EncryptedDefaultsStore.swift
//  PayMaya
//
//  Created by <PERSON> on 06/07/2018.
//  Copyright © 2018 PayMaya Philippines, Inc. All rights reserved.
//

import CryptoSwift
import Error
import Foundation
import ReactiveSwift
import Sync
import VoyagerGenericEncryption

public class EncryptedDefaultsStore: CodableStore {
    public typealias Storage = EncryptedDefaultsStore

    public lazy var encoder = J<PERSON>NEncoder()
    public lazy var decoder = JSONDecoder()

    // Checking if file is locked by OS due to data protection
    public let isProtectedDataAvailableProperty: MutableProperty<Bool> = MutableProperty(false)
    private let disposeBag = CompositeDisposable()

    private var storage: SafeDictionary<String, Any>!
    private let plistURL: URL

    // MARK: Store associated types
    public typealias Storable = EncryptedDefaultsStorable
    public typealias ReadingOptions = StorableIdentifier
    public typealias WritingOptions = StorableIdentifier

    public init<T: StoreMigrator>(configuration: StoreConfiguration<T>, storeName: String) where T.Storage == EncryptedDefaultsStore {
        plistURL = FileManager.default.urls(for: .libraryDirectory, in: .userDomainMask).last!.appendingPathComponent(storeName + ".plist")

        let initializeStorage = { [weak self] in
            guard let self = self else { return }
            do {
                var propertyListFormat = PropertyListSerialization.PropertyListFormat.xml
                if !FileManager.default.fileExists(atPath: self.plistURL.path) {
                    FileManager.default.createFile(atPath: self.plistURL.path, contents: nil, attributes: nil)
                    self.storage = SafeDictionary<String, Any>(from: [:])
                } else if let plistXML = FileManager.default.contents(atPath: self.plistURL.path), !plistXML.isEmpty {
                    let plistContent = try PropertyListSerialization.propertyList(from: plistXML, options: .mutableContainersAndLeaves, format: &propertyListFormat) as! [String: Any]
                    self.storage = SafeDictionary<String, Any>(from: plistContent)
                } else {
                    self.storage = SafeDictionary<String, Any>(from: [:])
                }

                #if !DEBUG
                if let migrator = configuration.migrator {
                    self.handleMigration(migrator: migrator, previousBuildNumber: configuration.previousBuildNumber)
                }
                #endif
            } catch let error {
                print(error.localizedDescription)
            }
        }

        isProtectedDataAvailableProperty.signal.observeValues { [weak self] isAvailable in
            guard let self = self else { return }
            if self.storage == nil, isAvailable {
                initializeStorage()
            }
        }?.addToDisposeBag(disposeBag)
    }

    private func decode<T: Codable, U: StorableIdentifier>(identifier: U, data: Data, type: T.Type) -> T? {
        return try? decoder.decode(type, from: data)
    }

    private func encode<T: Codable, U: StorableIdentifier>(identifier: U, storable: T) -> Data? {
        return try? encoder.encode(storable)
    }

    // MARK: Store Methods
    public func read<T, U: StorableIdentifier & Equatable>(_ options: U, type: T.Type) -> StoreResult<T, Error> where T: Decodable, T: Encodable {
        guard isProtectedDataAvailableProperty.value else {
            let reason = StoreLog.EncryptedDefaults.accessEncryptionError
            return StoreResult.failure(Error(database: .readError(reason: reason)))
        }

        guard let value = storage[options.key()] else {
            let reason = StoreLog.EncryptedDefaults.readMissingError
            return StoreResult.failure(Error(database: .readError(reason: reason)))
        }

        guard let data = value as? Data else {
            let reason = StoreLog.EncryptedDefaults.readCorruptedError
            return StoreResult.failure(Error(database: .readError(reason: reason)))
        }

        guard
            let optionsIV = storage[options.keyIV()] as? String,
            let key = VGEncryptor.shared.key,
            let decrypted = try? AES(key: key, iv: optionsIV).decrypt(data.bytes) else {
            let reason = String(format: StoreLog.EncryptedDefaults.readDecryptionError, "\(VGEncryptor.shared.key ?? "")", "\((storage[options.keyIV()] as? String) ?? "")")
            return StoreResult.failure(Error(database: .readError(reason: reason)))
        }

        if let model = decode(identifier: options, data: Data(decrypted), type: type) {
            return StoreResult.success(model)
        } else {
            let reason = StoreLog.EncryptedDefaults.readMappingError
            return StoreResult.failure(Error(database: .readError(reason: reason)))
        }
    }

    public func fetch<U: ReadingOptions>(_ options: U, limit: Int? = nil) -> StoreResult<[Storable], Error> {
        let reason = StoreLog.EncryptedDefaults.unavailableAPIError
        return StoreResult.failure(Error(database: .readError(reason: reason)))
    }

    public func write<T, U: WritingOptions>(_ storable: T, options: U, completion: ((StoreResult<Void, Error>) -> Void)? = nil) where T: Decodable, T: Encodable {
        guard isProtectedDataAvailableProperty.value else {
            let reason = StoreLog.EncryptedDefaults.accessEncryptionError
            if let completion = completion {
                completion(StoreResult.failure(Error(database: .writeError(reason: reason))))
            }
            return
        }

        guard let data = encode(identifier: options, storable: storable) else {
            let reason = StoreLog.EncryptedDefaults.writeSerializeError
            if let completion = completion {
                completion(StoreResult.failure(Error(database: .writeError(reason: reason))))
            }
            return
        }

        let key = VGEncryptor.shared.key ?? ""
        let optionsIV = storage[options.keyIV()] as? String ?? String(UUID().uuidString.components(separatedBy: "-").joined().prefix(key.count / 2))
        guard let encrypted = try? AES(key: key, iv: optionsIV).encrypt(data.bytes) else {
            let reason = String(format: StoreLog.EncryptedDefaults.writeEncryptionError, "\(key)", "\(optionsIV)")

            if let completion = completion {
                completion(StoreResult.failure(Error(database: .writeError(reason: reason))))
            }
            return
        }

        storage[options.keyIV()] = optionsIV
        storage[options.key()] = Data(encrypted)
        updatePlist(completion: completion, key: options.key())
    }

    public func remove<U: ReadingOptions>(_ options: U, completion: ((StoreResult<Void, Error>) -> Void)?) {
        guard isProtectedDataAvailableProperty.value else {
            let reason = StoreLog.EncryptedDefaults.accessEncryptionError
            if let completion = completion {
                completion(StoreResult.failure(Error(database: .writeError(reason: reason))))
            }
            return
        }

        storage.removeValue(forKey: options.keyIV())
        storage.removeValue(forKey: options.key())
        updatePlist(completion: completion, key: options.key())
    }

    private func updatePlist(completion: ((StoreResult<Void, Error>) -> Void)?, key: String) {
        do {
            let data = try PropertyListSerialization.data(fromPropertyList: storage.store, format: .xml, options: 0)
            do {
                try data.write(to: plistURL, options: .atomic)
                if let completion = completion {
                    completion(StoreResult.success(()))
                }
            } catch let error {
                if let completion = completion {
                    completion(StoreResult.failure(Error(underlying: error)))
                }
            }
        } catch let error {
            if let completion = completion {
                completion(StoreResult.failure(Error(underlying: error)))
            }
        }
    }

    // Used when migrating fields from UserDefaults to EncryptedDefaults and/or Keys
    // Returns IV value used in encrypting and the encrypted data itself if encrypted successfully
    private func encryptStorable<T: Storable, U: WritingOptions>(_ storable: T, options: U) -> (String, Data)? {
        guard let data = encode(identifier: options, storable: storable) else {
            return nil
        }

        let key = VGEncryptor.shared.key ?? ""
        let optionsIV = String(UUID().uuidString.components(separatedBy: "-").joined().prefix(key.count / 2))
        guard let encrypted = try? AES(key: key, iv: optionsIV).encrypt(data.bytes) else {
            return nil
        }
        return (optionsIV, Data(encrypted))
    }

    // Used when migrating from old keys
    private func decryptStorable<T: EncryptedDefaultsStorable, U: ReadingOptions>(options: U, type: T.Type) -> Storable? {
        let key = VGEncryptor.shared.rawKey ?? ""
        let optionsIV = (storage[options.keyIV()] as? String) ?? String(UUID().uuidString.components(separatedBy: "-").joined().prefix(key.count / 2))
        guard let data: Data = storage[options.key()] as? Data, let decrypted = try? AES(key: key, iv: optionsIV).decrypt(data.bytes) else {
            return nil
        }
        return decode(identifier: options, data: Data(decrypted), type: type)
    }
}

extension EncryptedDefaultsStore {
    public func handleMigration<T>(migrator: T, previousBuildNumber: Int?) where T: StoreMigrator, T.Storage == EncryptedDefaultsStore {
        migrator.performMigration(storage: self, previousBuildNumber: previousBuildNumber)
    }
}
