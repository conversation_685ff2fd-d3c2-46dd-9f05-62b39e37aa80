//
//  DatabaseStore.swift
//  PayMaya
//
//  Created by <PERSON> on 21/03/2017.
//  Copyright © 2017 PayMaya Philippines, Inc. All rights reserved.
//

import CoreData
import CoreDataStack
import Error
import Foundation
import Injector
import ReactiveSwift
import Sync
import UtilityExtension

public class DatabaseStore: Store {
    public typealias Storage = DatabaseStore
    public typealias Storable = DatabaseStorable

    public var dataStack: CoreDataStackProtocol! = nil
    public var notification: DatabaseStoreNotification! = nil

    public let isProtectedDataAvailableProperty: MutableProperty<Bool> = MutableProperty(false)
    public let isInBackgroundProperty: MutableProperty<Bool> = MutableProperty(false)

    let disposeBag = CompositeDisposable()

    public init<T: StoreMigrator>(configuration: StoreConfiguration<T>, dataStack: CoreDataStackProtocol) where T.Storage == DatabaseStore {
        #if !DEBUG
        if let migrator = configuration.migrator {
            handleMigration(migrator: migrator, previousBuildNumber: configuration.previousBuildNumber)
        }
        #endif

        #if !UNIT_TEST_TARGET
        self.dataStack = dataStack
        notification = DatabaseStoreNotification.init(dataStack.mainContext)
        #endif
    }

    public struct WritingOptions: Equatable {
        let entity: Entity
        let primaryKey: String?
        let primaryKeyValue: String?
        let relationshipDictionary: [String: String]?

        public init(_ entity: Entity, primaryKeyValue: String? = nil, relationshipDictionary: [String: String]? = nil) {
            self.entity = entity
            self.primaryKey = entity.primaryKey
            self.primaryKeyValue = primaryKeyValue
            self.relationshipDictionary = relationshipDictionary
        }

        public static func == (lhs: Self, rhs: Self) -> Bool {
            return lhs.entity.identifier == rhs.entity.identifier && lhs.primaryKey == rhs.primaryKey && lhs.primaryKeyValue == rhs.primaryKeyValue && lhs.relationshipDictionary == rhs.relationshipDictionary
        }
    }

    public struct ReadingOptions: Equatable {
        let entity: Entity
        let predicate: NSPredicate?
        let sort: [NSSortDescriptor]?

        public init(_ entity: Entity, predicate: NSPredicate? = nil, sort: [NSSortDescriptor]? = nil) {
            self.entity = entity
            self.predicate = predicate
            self.sort = sort
        }

        public func filter(_ predicate: NSPredicate) -> Self {
            return Self(self.entity, predicate: predicate, sort: self.sort)
        }

        func controller<S: DatabaseStorable>(_ databaseStore: DatabaseStore) -> NSFetchedResultsController<S> {
            return NSFetchedResultsController(fetchRequest: self.request(S.self), managedObjectContext: databaseStore.dataStack.mainContext, sectionNameKeyPath: nil, cacheName: nil)
        }

        func request<ResultType: DatabaseStorable>(_ type: ResultType.Type, limit: Int? = nil) -> NSFetchRequest<ResultType> {
            let request = NSFetchRequest<ResultType>(entityName: entity.identifier)
            if let predicate {
                request.predicate = predicate
            }
            if let limit {
                request.fetchLimit = limit
            }
            if let sort {
                request.sortDescriptors = sort
            } else {
                request.sortDescriptors = []
            }
            request.fetchBatchSize = 0
            request.returnsDistinctResults = true
            return request
        }

        public func fetch(_ context: NSManagedObjectContext, limit: Int? = nil) throws -> [DatabaseStorable] {
            let request = self.request(NSManagedObject.self, limit: limit)
            var storableArray: [DatabaseStorable]?
            context.performAndWait {
                if let data = try? context.fetch(request) {
                    storableArray = data
                }
            }

            if let data = storableArray {
                return data
            } else {
                throw Error(database: .readError(reason: "Unable to fetch the requested entity(\(entity.identifier))"))
            }
        }

        public func fetchWithCompletion(_ context: NSManagedObjectContext, limit: Int? = nil, completion: ((StoreResult<[DatabaseStorable], Error>) -> Void)? = nil) {
            context.perform {
                let request = request(NSManagedObject.self, limit: limit)
                if let response = try? context.fetch(request), response.count > 0 {
                    completion?(.success(response))
                } else {
                    let result: StoreResult<[DatabaseStorable], Error> = StoreResult.failure(Error(database: .readError(reason: "No data found for the requested options with entity(\(entity.identifier)).")))
                    completion?(result)
                }
            }
        }

        public static func == (lhs: Self, rhs: Self) -> Bool {
            if let sort1 = lhs.sort, let sort2 = rhs.sort {
                return lhs.entity.identifier == rhs.entity.identifier && lhs.predicate?.predicateFormat == rhs.predicate?.predicateFormat &&
                    sort1.map { $0.key }.elementsEqual(sort2.map { $0.key }) &&
                    sort1.map { $0.ascending }.elementsEqual(sort2.map { $0.ascending })
            }
            return lhs.entity.identifier == rhs.entity.identifier && lhs.predicate?.predicateFormat == rhs.predicate?.predicateFormat
        }
    }

    public func persistToDatabase(completion: ((StoreResult<Void, Error>) -> Void)? = nil) {
        saveContext(dataStack.mainContext, completion: completion)
    }

    public func saveContext(_ context: NSManagedObjectContext, completion: ((StoreResult<Void, Error>) -> Void)? = nil) {
        guard isProtectedDataAvailableProperty.value else {
            let error = Error(database: .writeError(reason: StoreLog.Database.accessEncryptionError))
            completion?(.failure(error))
            return
        }

        context.performAndWait { [weak self] in
            guard let self else {
                completion?(.success(()))
                return
            }

            // Backround context is automatically saved by syncdb
            if (context == dataStack.mainContext || context == dataStack.rootContext) && !context.hasChanges {
                completion?(.success(()))
                return
            }

            do {
                try context.save()
                if let parentContext = context.parent {
                    saveContext(parentContext, completion: completion)
                } else {
                    completion?(.success(()))
                }
            } catch let error as NSError {
                print(error)
                completion?(.failure(Error(database: .writeError(reason: "Error in writing to database"))))
            }
        }
    }

    /// A variation of the the `saveContext(_:completion)` method that does not involve a callback closure. Instead, the error is thrown.
    public func saveContextThrowing(_ context: NSManagedObjectContext) throws {
        guard isProtectedDataAvailableProperty.value else {
            throw Error(database: .writeError(reason: StoreLog.Database.accessEncryptionError))
        }

        var underlyingError: Swift.Error?

        /// This is executed synchronously (i.e. the calling thread is blocked) so there is no need for a callback closure
        context.performAndWait { [weak self] in
            guard let self else { return }

            /// Backround context is automatically saved by syncdb
            if (context == dataStack.mainContext || context == dataStack.rootContext) && !context.hasChanges {
                return
            }

            do {
                try context.save()
                if let parentContext = context.parent {
                    try saveContextThrowing(parentContext)
                }
            } catch {
                underlyingError = error
            }
        }

        if let underlyingError = underlyingError as? NSError {
            print(underlyingError)
            throw Error.database(.writeError(reason: "Error in writing to database"))
        }
    }

    public func read(_ options: ReadingOptions) -> StoreResult<DatabaseStorable, Error> {
        guard isProtectedDataAvailableProperty.value else {
            let error = Error(database: .readError(reason: StoreLog.Database.accessEncryptionError))
            return .failure(error)
        }

        if let response: [DatabaseStorable] = try? options.fetch(dataStack.mainContext), let data = response.first {
            return .success(data)
        } else {
            return .failure(Error(database: .readError(reason: "No data found for the requested options with entity(\(options.entity.identifier)).")))
        }
    }

    public func fetch(_ options: ReadingOptions, limit: Int? = nil) -> StoreResult<[DatabaseStorable], Error> {
        guard isProtectedDataAvailableProperty.value else {
            let error = Error(database: .readError(reason: StoreLog.Database.accessEncryptionError))
            return .failure(error)
        }

        if let response: [DatabaseStorable] = try? options.fetch(dataStack.mainContext, limit: limit), response.count > 0 {
            return .success(response)
        } else {
            return .failure(Error(database: .readError(reason: "No data found for the requested options with entity(\(options.entity.identifier)).")))
        }
    }

    public func fetchWithCompletion(_ options: ReadingOptions, limit: Int? = nil, completion: ((StoreResult<[DatabaseStorable], Error>) -> Void)? = nil) {
        guard isProtectedDataAvailableProperty.value else {
            let error = Error(database: .readError(reason: StoreLog.Database.accessEncryptionError))
            completion?(.failure(error))
            return
        }

        options.fetchWithCompletion(dataStack.mainContext, limit: limit, completion: completion)
    }

    public func write(_ storable: DatabaseStorable, options: WritingOptions, completion: ((StoreResult<Void, Error>) -> Void)? = nil) {
        write(storable, options: options, context: dataStack.newBackgroundContext(), completion: completion)
    }

    public func write(_ storable: DatabaseStorable, options: WritingOptions, context: NSManagedObjectContext, completion: ((StoreResult<Void, Error>) -> Void)? = nil) {
        write(storable, options: options, context: context)
        saveContext(context, completion: completion)
    }

    /// A variation of the `write(_:options:context:completion)` method that does not involve a callback closure. Instead, the error is thrown.
    public func writeThrowing(
        _ storable: DatabaseStorable,
        options: WritingOptions,
        context: NSManagedObjectContext
    ) throws {
        write(storable, options: options, context: context)
        try saveContextThrowing(context)
    }

    public func performAndWait(for context: NSManagedObjectContext? = nil, _ block: () -> Void) {
        let context = context ?? dataStack.mainContext
        context.performAndWait {
            block()
        }
        saveContext(context)
    }

    // Helper method for multiple storeable with multiple writing options each
    public func write(_ writeStorables: [(storable: DatabaseStorable, option: WritingOptions)], context: NSManagedObjectContext? = nil, completion: ((StoreResult<Void, Error>) -> Void)? = nil) {
        let context = context ?? dataStack.newBackgroundContext()
        for writeStorable in writeStorables {
            let (storable, options) = writeStorable
            write(storable, options: options, context: context)
        }
        saveContext(context, completion: completion)
    }

    private func write(_ storable: DatabaseStorable, options: WritingOptions, context: NSManagedObjectContext) {
        guard isProtectedDataAvailableProperty.value else { return }

        context.performAndWait {
            let storables: [[String: Any]]
            if let storableDictionaryObject = storable as? NSDictionary, let storableDictionary = storableDictionaryObject as? [String: Any] {
                storables = [storableDictionary]
            } else if let storableArrayObject = storable as? NSArray, let storableArray = storableArrayObject as? [[String: Any]] {
                storables = storableArray
            } else {
                return
            }

            let entityName = options.entity.identifier
            for var _storable in storables {
                if let primaryKey = options.primaryKey, let value = options.primaryKeyValue {
                    _storable[primaryKey] = value
                }

                if let relationshipDictionary = options.relationshipDictionary {
                    _storable.merge(with: relationshipDictionary)
                }

                do {
                    try context.insertOrUpdate(_storable, inEntityNamed: entityName)
                } catch let error as NSError {
                    let errorLogger = ContainerWrapper.shared.resolve(ErrorLogger.self)
                    errorLogger.log(.databaseError(
                        underlying: error,
                        message: "Failed to write \(entityName)",
                        customKeys: ["inBackground": isInBackgroundProperty.value,
                                     "dataProtectionAvailable": isProtectedDataAvailableProperty.value]))
                }
            }
        }
    }

    // Conforms to Store protocol
    public func remove(_ options: ReadingOptions, completion: ((StoreResult<Void, Error>) -> Void)? = nil) {
        remove(options, context: dataStack.newBackgroundContext(), isCrudOperation: false, completion: completion)
    }

    // isCrudOperation is used when remove storable is used in conjunction with write storable afterwards
    // this is to preserve thread consistencies as as effect of using performBlockAndWait to MOC
    public func remove(_ options: ReadingOptions, isCrudOperation: Bool, completion: ((StoreResult<Void, Error>) -> Void)? = nil) {
        remove(options, context: dataStack.newBackgroundContext(), isCrudOperation: isCrudOperation, completion: completion)
    }

    public func remove(_ options: ReadingOptions, context: NSManagedObjectContext, isCrudOperation: Bool = false, completion: ((StoreResult<Void, Error>) -> Void)? = nil) {
        guard isProtectedDataAvailableProperty.value else {
            let error = Error(database: .writeError(reason: StoreLog.Database.accessEncryptionError))
            completion?(.failure(error))
            return
        }

        context.performAndWait {
            guard let response: [DatabaseStorable] = try? options.fetch(context), response.count > 0 else {
                completion?(StoreResult.failure(Error(database: .writeError(reason: "No data found for the requested options with entity(\(options.entity.identifier))."))))
                return
            }

            response.forEach { data in
                guard let managedObject = data as? NSManagedObject else {
                    completion?(StoreResult.failure(Error(database: .writeError(reason: "Error in removing to database."))))
                    return
                }
                context.delete(managedObject)
            }

            if isCrudOperation {
                completion?(StoreResult.success(()))
            } else {
                saveContext(context, completion: completion)
            }
        }
    }

    /// A variation of the `remove(_:context:isCrudOperation:completion)` method that rethrows the error thrown by `completion`.
    public func removeRethrowing(
        _ options: ReadingOptions,
        context: NSManagedObjectContext,
        isCrudOperation: Bool = false,
        completion: ((StoreResult<Void, Error>) throws -> Void)? = nil
    ) rethrows {
        guard isProtectedDataAvailableProperty.value else {
            let error = Error(database: .writeError(reason: StoreLog.Database.accessEncryptionError))
            try completion?(.failure(error))
            return
        }

        var result: StoreResult<Void, Error> = .success(())

        /// This is executed synchronously (i.e. the calling thread is blocked) so `result` can be assigned a value from within the closure block
        context.performAndWait {
            guard let response: [DatabaseStorable] = try? options.fetch(context), response.count > 0 else {
                result = .failure(.database(.writeError(
                    reason: "No data found for the requested options with entity(\(options.entity.identifier)).")))
                return
            }

            response.forEach { data in
                guard let managedObject = data as? NSManagedObject else {
                    result = .failure(.database(.writeError(reason: "Error in removing from database.")))
                    return
                }
                context.delete(managedObject)
            }

            if !isCrudOperation {
                do {
                    try saveContextThrowing(context)
                } catch {
                    result = .failure(.database(.writeError(reason: "Error in removing from database.")))
                }
            }
        }

        try completion?(result)
    }

    public func resetTransientStore() {
        dataStack.resetTransientStore()
    }
}

extension DatabaseStore {
    public func handleMigration<T>(migrator: T, previousBuildNumber: Int?) where T: StoreMigrator, T.Storage == DatabaseStore {
        migrator.performMigration(storage: self, previousBuildNumber: previousBuildNumber)
    }
}
