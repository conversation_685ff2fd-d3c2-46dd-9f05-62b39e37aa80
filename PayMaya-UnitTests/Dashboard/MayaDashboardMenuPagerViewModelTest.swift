//
//  MayaDashboardMenuPagerViewModelTest.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON><PERSON> on 3/3/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import AssetProvider
import ConfigurationService
import Cuckoo
import Injector
import MayaCoreData
import ReactiveSwift
import StoreProvider
import XCTest

class MayaDashboardMenuPagerViewModelTest: ViewModelTest {
    var viewModel: MayaDashboardMenuPagerViewModelProtocol!

    var inboxManager: MockInboxManagerProtocol!
    var inboxManagerProperty = MutableProperty(false)

    override func setUp() {
        super.setUp()
        inboxManager = ContainerWrapper.shared.resolve(InboxManagerProtocol.self) as? MockInboxManagerProtocol
        stub(inboxManager) { stub in
            when(stub.hasUnreadMessages.get).thenReturn(inboxManagerProperty)
        }

        viewModel = MayaDashboardMenuPagerViewModel()
        XCTAssertFalse(viewModel.hasUnreadMessages.value)
    }

    func testHasUnreadMessages() {
        inboxManagerProperty.value = false
        XCTAssertFalse(viewModel.hasUnreadMessages.value)
        inboxManagerProperty.value = true
        XCTAssertTrue(viewModel.hasUnreadMessages.value)
    }

    func testIsTaggedForEDD() {
        let stubUser = getUserWithPrefilledData()
        stubUser.eddStatus = "for_submission"
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(stubUser))
        }
        XCTAssertTrue(viewModel.isTaggedForEDD())
        verify(databaseStore, times(1)).read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))
    }

    func testLimitedAccount() {
        let stubUser = getUserWithPrefilledData()
        stubUser.accountStatus = "LIMITED"
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(stubUser))
        }
        XCTAssertTrue(viewModel.isAccountLimited())
        verify(databaseStore, times(1)).read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))
    }

    func testActiveAccount() {
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(getUserWithPrefilledData()))
        }
        XCTAssertFalse(viewModel.isAccountLimited())
        verify(databaseStore, times(1)).read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))
    }

    func testShouldNotShowReferralInputCode() {
        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.shouldShowInputInviteCode))).thenReturn(false)
        }

        XCTAssertFalse(viewModel.shouldShowReferralInputCode())
        verify(userDefaultsStore, times(1)).readRaw(isEqual(to: UserDefaultsStoreId.shouldShowInputInviteCode))
    }

    func testShouldShowReferralInputCode() {
        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.shouldShowInputInviteCode))).thenReturn(true)
        }
        XCTAssertTrue(viewModel.shouldShowReferralInputCode())

        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.shouldShowInputInviteCode))).thenReturn(nil)
        }
        XCTAssertFalse(viewModel.shouldShowReferralInputCode())

        verify(userDefaultsStore, times(2)).readRaw(isEqual(to: UserDefaultsStoreId.shouldShowInputInviteCode))
    }

    func testDuplicateAccountAlertNotShown() {
        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.dedupAlertShownForUser))).thenReturn(false)
        }
        XCTAssertFalse(viewModel.wasDuplicateAccountAlertShownForUser(), "Successfully saved user defaults for duplicate accounts")
        verify(userDefaultsStore, times(1)).readRaw(isEqual(to: UserDefaultsStoreId.dedupAlertShownForUser))
    }

    func testDuplicateAccountAlertShown() {
        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.dedupAlertShownForUser))).thenReturn(true)
        }
        XCTAssertTrue(viewModel.wasDuplicateAccountAlertShownForUser(), "Successfully saved user defaults for duplicate accounts")

        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.dedupAlertShownForUser))).thenReturn(nil)
        }
        XCTAssertFalse(viewModel.wasDuplicateAccountAlertShownForUser())

        verify(userDefaultsStore, times(2)).readRaw(isEqual(to: UserDefaultsStoreId.dedupAlertShownForUser))
    }

    func testSetUserDefaultShouldShowReferral() {
        stub(userDefaultsStore) { stub in
            when(stub.write(any(), options: isEqual(to: UserDefaultsStoreId.shouldShowInputInviteCode), completion: isNil())).thenDoNothing()
        }

        viewModel.setUserDefaultShouldShowReferral()
        verify(userDefaultsStore, times(1)).write(any(), options: isEqual(to: UserDefaultsStoreId.shouldShowInputInviteCode), completion: isNil())
    }

    func testSetUserDefaultForDuplicateAccountShown() {
        stub(userDefaultsStore) { stub in
            when(stub.write(any(), options: isEqual(to: UserDefaultsStoreId.dedupAlertShownForUser.writingOptions), completion: isNil())).thenDoNothing()
        }
        viewModel.setUserDefaultForDuplicateAccountAlertShown()
        verify(userDefaultsStore).write(any(), options: isEqual(to: UserDefaultsStoreId.dedupAlertShownForUser.writingOptions), completion: isNil())
    }

    func testIsMayaInboxFlutterEnabled() {
        stub(configurationService) { stub in
            when(stub.mayaInboxFlutterEnabled.get).thenReturn(falseConfig())
        }
        XCTAssertFalse(viewModel.isMayaInboxFlutterEnabled)

        stub(configurationService) { stub in
            when(stub.mayaInboxFlutterEnabled.get).thenReturn(trueConfig())
        }
        XCTAssertTrue(viewModel.isMayaInboxFlutterEnabled)
    }

    func testIsMayaUsernameEnabled() {
        stub(configurationService) { stub in
            when(stub.mayaSetUsernameEnabled.get).thenReturn(falseConfig())
        }
        XCTAssertFalse(viewModel.isMayaUsernameEnabled)

        stub(configurationService) { stub in
            when(stub.mayaSetUsernameEnabled.get).thenReturn(trueConfig())
        }
        XCTAssertTrue(viewModel.isMayaUsernameEnabled)
    }

    func testGetProfileUsername() {
        let stubUser = getUserWithUsernameIdentity()
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(stubUser))
        }

        stub(configurationService) { stub in
            when(stub.mayaSetUsernameEnabled.get).thenReturn(trueConfig())
        }

        XCTAssertNotNil(viewModel.getProfileUsername())
        verify(databaseStore, times(1)).read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))
    }

    func testShouldShowUpgradeAccount() {
        stub(userDefaultsStore) { stub in
            when(stub.write(any(), options: isEqual(to: UserDefaultsStoreId.shouldShowUpgradeAccountUponRegistration), completion: anyOptional())).thenDoNothing()
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.shouldShowUpgradeAccountUponRegistration))).thenReturn(true)
        }

        XCTAssertTrue(viewModel.shouldShowUpgradeAccount())
        viewModel.setUserDefaultShouldNotShowUpgradeAccount()

        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.shouldShowUpgradeAccountUponRegistration))).thenReturn(nil)
        }
        XCTAssertFalse(viewModel.shouldShowUpgradeAccount())

        verify(userDefaultsStore, times(1)).write(any(), options: isEqual(to: UserDefaultsStoreId.shouldShowUpgradeAccountUponRegistration), completion: isNil())
    }

    func testDashboardMenuArray() {
        stub(configurationService) { stub in
            when(stub.mayaLoansEnabled.get).thenReturn(trueConfig())
            when(stub.savingsEnabled.get).thenReturn(trueConfig())
            when(stub.dashboardSettings.get).thenReturn(
                AppConfig.DashboardSettings(
                    tabs: [
                        AppConfig.DashboardSettings.DashboardTab(name: "Wallet", id: "wallet"),
                        AppConfig.DashboardSettings.DashboardTab(name: "Loans", id: "loans"),
                        AppConfig.DashboardSettings.DashboardTab(name: "Credit", id: "credit"),
                        AppConfig.DashboardSettings.DashboardTab(name: "Savings", id: "savings"),
                        AppConfig.DashboardSettings.DashboardTab(name: "Cards", id: "cards")
                    ],
                    walletTiles: []
            ))
        }

        viewModel = MayaDashboardMenuPagerViewModel()
        XCTAssertEqual(viewModel.dashboardMenuArray, [.wallet, .loans, .credit, .savings, .cards])
        XCTAssertEqual(viewModel.getIndexForDashboardMenu(menu: .savings), 3)
        XCTAssertEqual(viewModel.getIndexForDashboardMenu(menu: .loans), 1)
        XCTAssertEqual(viewModel.dashboardMenuArray.count, 5)

        stub(configurationService) { stub in
            when(stub.mayaLoansEnabled.get).thenReturn(falseConfig())
            when(stub.savingsEnabled.get).thenReturn(trueConfig())
        }

        viewModel = MayaDashboardMenuPagerViewModel()
        XCTAssertEqual(viewModel.dashboardMenuArray, [.wallet, .credit, .savings, .cards])
        XCTAssertEqual(viewModel.getIndexForDashboardMenu(menu: .credit), 1)
        XCTAssertEqual(viewModel.getIndexForDashboardMenu(menu: .savings), 2)
        XCTAssertEqual(viewModel.getIndexForDashboardMenu(menu: .loans), nil)
        XCTAssertEqual(viewModel.dashboardMenuArray.count, 4)

        stub(configurationService) { stub in
            when(stub.mayaLoansEnabled.get).thenReturn(falseConfig())
            when(stub.savingsEnabled.get).thenReturn(falseConfig())
        }

        viewModel = MayaDashboardMenuPagerViewModel()
        XCTAssertEqual(viewModel.dashboardMenuArray, [.wallet, .credit, .cards])
        XCTAssertEqual(viewModel.getIndexForDashboardMenu(menu: .savings), nil)
        XCTAssertEqual(viewModel.getIndexForDashboardMenu(menu: .loans), nil)
        XCTAssertEqual(viewModel.dashboardMenuArray.count, 3)
    }

    func testGetAccountRestrictionCode() {
        stub(configurationService) { stub in
            when(stub.customerRestrictionsEnabled.get).thenReturn(falseConfig())
        }

        let stubUser = getUserWithPrefilledData()
        stubUser.accountStatus = "LIMITED"
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(stubUser))
        }

        let stubRestriction = getRestricionReason()
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.restriction)))).thenReturn(StoreResult.success(stubRestriction))
        }

        XCTAssertTrue(viewModel.isAccountLimited())
        verify(databaseStore, times(1)).read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))

        XCTAssertNotNil(viewModel.getUserRestrictionCode())
        verify(databaseStore, times(1)).read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.restriction)))
    }

    func testGetUserRestrictionCode() {
        stub(configurationService) { stub in
            when(stub.customerRestrictionsEnabled.get).thenReturn(trueConfig())
        }
        let stubRestriction = getRestricionReason()

        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.restriction))).thenReturn(stubRestriction)
        }

        XCTAssertNotNil(viewModel.getUserRestrictionCode())
        verify(userDefaultsStore, times(1)).readRaw(isEqual(to: UserDefaultsStoreId.restriction))
    }

    func testHelpUrlWithoutEncodedParameter() {
        let baseUrlString = "https://www.google.com"
        let expectedUrl = URL(string: baseUrlString)
        stub(propertyListStore) { stub in
            when(stub.read(equal(to: PropertyListStoreId.helpBaseUrl))).thenReturn(StoreResult.success(baseUrlString))
        }

        let viewModel = MayaDashboardMenuPagerViewModel()
        let result = viewModel.helpUrl

        XCTAssertEqual(result, expectedUrl)
    }

    func testHelpUrlWithEncodedParameter() {
        let stubUser = getUserWithPrefilledData()
        let baseUrlString = "https://www.google.com/"
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(stubUser))
        }
        stub(propertyListStore) { stub in
            when(stub.read(equal(to: PropertyListStoreId.helpBaseUrl))).thenReturn(StoreResult.success(baseUrlString))
        }

        let viewModel = MayaDashboardMenuPagerViewModel()
        let result = viewModel.helpUrl

        XCTAssertNotNil(result, "URL should not be nil")

        guard let components = URLComponents(url: result!, resolvingAgainstBaseURL: false),
            let queryItems = components.queryItems else {
            XCTFail("Should have item to query")
            return
        }

        let expectedQueryName = queryItems.contains(where: { $0.name == "q" })
        let expectedQueryValue = queryItems.first?.value

        XCTAssertTrue(expectedQueryName)
        XCTAssertNotNil(expectedQueryValue)
    }
}

fileprivate extension MayaDashboardMenuPagerViewModelTest {
    func getUserWithPrefilledData(cardProfile: StubCardProfile = StubCardProfile()) -> StubUser {
        let stubProfile = getProfileData()
        let stubMainIdentity = StubMainIdentity(type: "msisdn", value: "+************")
        let stubFund = StubFund(enabled: 1, id: "1234567-abcde", name: "virtual_card", status: FundSourceStatus.virtualCard.rawValue, type: FundSourceType.virtual.rawValue, cardProfile: cardProfile, purchases: nil, user: stubProfile.user)
        return StubUser(id: "user_id", accountStatus: "ACTIVE", canBeReferred: 1, canLinkCard: 1, kycStatus: "kyc1", network: "Smart", physicalCardStatus: "activated", status: "ACTIVE", token: "1234567", hasGovID: 1, activities: nil, backupIdentity: nil, balance: nil, funds: [stubFund], identities: NSSet(array: [stubMainIdentity]), privacyPolicy: StubPrivacyPolicy(version: "v1"), profile: stubProfile, type: nil)
    }

    func getStubAddress() -> StubAddress {
        return StubAddress(city: "San Jose Del Monte", country: "PH", line1: "123", line2: nil, locality: "Highway Hills", state: "NCR", zipCode: "1501", profile: StubProfile())
    }

    func getProfileData() -> StubProfile {
        let stubUser = StubUser()
        let stubAddress = getStubAddress()
        let stubProfile = StubProfile(birthDate: nil, firstName: "John", lastName: "Doe", middleName: nil, address: stubAddress, user: stubUser)
        return stubProfile
    }

    func getUserWithUsernameIdentity() -> StubUser {
        let userNameIdentity = StubMainIdentity(type: "username", value: "krischu.ferranco")
        let stubUser = StubUser(identities: [userNameIdentity])
        return stubUser
    }

    func getRestricionReason() -> StubRestriction {
        let stubRestriction = StubRestriction(code: "", reason: "FCI-100", id: "")
        return stubRestriction
    }
}
