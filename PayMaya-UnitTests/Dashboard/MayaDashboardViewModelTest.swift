//
//  MayaDashboardViewModelTest.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON> on 09/05/2018.
//  Copyright © 2018 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import AssetProvider
import ConfigurationService
import Contacts
import Cuckoo
import Error
import Injector
import MayaCoreData
import Moya
import ReactiveSwift
import StoreProvider
import XCTest

private let helpUrl = "cares.paymaya.com"
private let loanAccountID = "5e7b0b60-b2a2-406d-b4af-7ad56e8cc1e7"
private let rrn = "a3f7c9c9-7edb-4a5e-b7db-12b48d3f4627"
private let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "ACTIVE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 0, daysLate: 0)
private let suspendedMayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "SUSPENDED", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 0, daysLate: 0)
private let pastDueMayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "PAST_DUE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 0, daysLate: 0)
private let bookedLoanApplication = MayaCreditCurrentLoanApplication(id: "2416fc66-c11f-4c83-bf50-e4dc860fdc56", applicationNumber: "a844a45f-25eb-4ac3-a62d-ffc5b51418de", term: MayaCreditCurrentLoanApplication.Term(id: "3f3ead12-242a-4e8f-8de5-304648715ddb", loanAmount: 15000, currency: "PHP", feeRate: 0.1), loanAccountId: loanAccountID, status: "BOOKED")
private let notBookedLoanApplication = MayaCreditCurrentLoanApplication(id: "2416fc66-c11f-4c83-bf50-e4dc860fdc56", applicationNumber: "a844a45f-25eb-4ac3-a62d-ffc5b51418de", term: MayaCreditCurrentLoanApplication.Term(id: "3f3ead12-242a-4e8f-8de5-304648715ddb", loanAmount: 15000, currency: "PHP", feeRate: 0.1), loanAccountId: loanAccountID, status: "SUBMITTED")
private let contactReference = MayaCreditPersonDetails.ContactReference(id: "", firstName: "first", middleName: "middle", lastName: "last", relationship: "relationship", mobileNumber: "mobile number", startDate: "start date", endDate: "end date")
private let mothersMaidenName = MayaCreditPersonDetails.MothersMaidenName(firstName: "first", middleName: "middle", lastName: "last", hasNoMiddleName: false)
private let creditPersonDetails = MayaCreditPersonDetails(contactReference: contactReference, mothersMaidenName: mothersMaidenName)
private let creditPersonDetailsNoMaidenName = MayaCreditPersonDetails(contactReference: contactReference, mothersMaidenName: nil)
private let creditPersonDetailsNoContactReference = MayaCreditPersonDetails(contactReference: nil, mothersMaidenName: mothersMaidenName)
private let mayaCreditAccountActive = MayaCreditAccountStatus(status: MayaCreditLoanStatusType.loanActive, details: nil, applicationDetails: bookedLoanApplication, accountDetails: mayaCreditAccount, customerDetails: creditPersonDetails)
private let mayaCreditAccountActiveMissingInfo = MayaCreditAccountStatus(status: MayaCreditLoanStatusType.loanActiveMissingInfo, details: nil, applicationDetails: bookedLoanApplication, accountDetails: mayaCreditAccount, customerDetails: creditPersonDetails)
private let mayaCreditAccountMissing = MayaCreditAccountStatus(status: MayaCreditLoanStatusType.loanMissing, details: nil, applicationDetails: nil, accountDetails: nil, customerDetails: nil)
private let mayaCreditAccountInvalid = MayaCreditAccountStatus(status: MayaCreditLoanStatusType.loanInvalid, details: nil, applicationDetails: nil, accountDetails: nil, customerDetails: nil)

class MayaDashboardViewModelTest: ViewModelTest {
    var viewModel: MayaDashboardViewModelProtocol!

    var contactsImporter: MockContactsImporter!

    override func setUp() {
        super.setUp()
        contactsImporter = ContainerWrapper.shared.resolve(ContactsImporter.self) as? MockContactsImporter
        viewModel = MayaDashboardViewModel()
        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.reKYCInitialDateTime))).thenReturn(Date.distantPast)
            when(stub.write(any(), options: isEqual(to: UserDefaultsStoreId.reKYCInitialDateTime), completion: isNil())).thenDoNothing()
            when(stub.write(any(), options: isEqual(to: UserDefaultsStoreId.expandedReKYCAction), completion: isNil())).thenDoNothing()
            when(stub.write(any(), options: isEqual(to: UserDefaultsStoreId.actionCardsLastUpdatedOn.writingOptions), completion: isNil())).thenDoNothing()
            when(stub.remove(isEqual(to: UserDefaultsStoreId.reKYCInitialDateTime), completion: anyOptional())).thenDoNothing()
            when(stub.remove(isEqual(to: UserDefaultsStoreId.expandedReKYCAction), completion: anyOptional())).thenDoNothing()
        }
    }

    func testGetBalance() {
        let request = ActivitiesRequest(fromDate: nil, toDate: nil, limit: 25, page: 1, order: nil, sort: nil, mode: .latest)
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.balance)))).thenReturn(StoreResult.success(getBalance()))
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Activity.activities(request)))).thenReturn(getSuccessProducer())
            when(stub.request(isEqual(to: API.Balance.current))).thenReturn(getSuccessProducer())
        }

        viewModel.refreshDashboardAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to refresh")
        }

        viewModel.refreshDashboardAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Activity.activities(request)))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Balance.current))
    }

    func testSuccessActiveCreditAccount() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))).thenReturn(getSuccessProducer(creditPersonDetails))
        }

        viewModel.getMayaCreditLoanApplicationAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to loan application")
        }
        viewModel.getMayaCreditLoanAccountAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to loan account")
        }
        viewModel.getMayaCreditCustomerAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to credit customer")
        }

        viewModel.getMayaCreditLoanApplicationAction.apply().start()

        XCTAssertEqual(viewModel.creditBannerState.value, .availableCredit)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, true)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))
    }

    func testSuccessActiveCreditAccountMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))).thenReturn(getSuccessProducer(creditPersonDetails))
        }

        viewModel.getMayaCreditLoanApplicationAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to loan application")
        }
        viewModel.getMayaCreditLoanAccountAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to loan account")
        }
        viewModel.getMayaCreditCustomerAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to credit customer")
        }

        viewModel.getMayaCreditLoanApplicationAction.apply().start()

        XCTAssertEqual(viewModel.creditBannerState.value, .availableCredit)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, true)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))
    }

    func testSuccessActiveCreditAccountNoMaidenName() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))).thenReturn(getSuccessProducer(creditPersonDetailsNoMaidenName))
        }

        viewModel.getMayaCreditLoanApplicationAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to loan application")
        }
        viewModel.getMayaCreditLoanAccountAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to loan account")
        }
        viewModel.getMayaCreditCustomerAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to credit customer")
        }

        viewModel.getMayaCreditLoanApplicationAction.apply().start()

        XCTAssertEqual(viewModel.creditBannerState.value, .availableCredit)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, false)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))
    }

    func testSuccessActiveCreditAccountNoMaidenNameMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))).thenReturn(getSuccessProducer(creditPersonDetailsNoMaidenName))
        }

        viewModel.getMayaCreditLoanApplicationAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to loan application")
        }
        viewModel.getMayaCreditLoanAccountAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to loan account")
        }
        viewModel.getMayaCreditCustomerAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to credit customer")
        }

        viewModel.getMayaCreditLoanApplicationAction.apply().start()

        XCTAssertEqual(viewModel.creditBannerState.value, .availableCredit)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, false)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))
    }

    func testSuccessActiveCreditAccountNoContactReference() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))).thenReturn(getSuccessProducer(creditPersonDetailsNoContactReference))
        }

        viewModel.getMayaCreditLoanApplicationAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to loan application")
        }
        viewModel.getMayaCreditLoanAccountAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to loan account")
        }
        viewModel.getMayaCreditCustomerAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to credit customer")
        }

        viewModel.getMayaCreditLoanApplicationAction.apply().start()

        XCTAssertEqual(viewModel.creditBannerState.value, .availableCredit)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, false)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))
    }

    func testSuccessActiveCreditAccountNoContactReferenceMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))).thenReturn(getSuccessProducer(creditPersonDetailsNoContactReference))
        }

        viewModel.getMayaCreditLoanApplicationAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to loan application")
        }
        viewModel.getMayaCreditLoanAccountAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to loan account")
        }
        viewModel.getMayaCreditCustomerAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to credit customer")
        }

        viewModel.getMayaCreditLoanApplicationAction.apply().start()

        XCTAssertEqual(viewModel.creditBannerState.value, .availableCredit)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, false)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))
    }

    func testErrorActiveCreditAccount() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getBackendError())
            when(stub.request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))).thenReturn(getSuccessProducer(creditPersonDetails))
        }

        viewModel.getMayaCreditLoanApplicationAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to loan application")
        }
        viewModel.getMayaCreditLoanAccountAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to loan account")
        }

        viewModel.getMayaCreditLoanApplicationAction.apply().start()

        XCTAssertEqual(viewModel.creditBannerState.value, .hidden)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, false)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
    }

    func testErrorActiveCreditAccountMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getBackendError())
            when(stub.request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))).thenReturn(getSuccessProducer(creditPersonDetails))
        }

        viewModel.getMayaCreditLoanApplicationAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to loan application")
        }
        viewModel.getMayaCreditLoanAccountAction.completed.observe(on: UIScheduler()).observeValues { _ in
            XCTAssert(true, "Successful call to loan account")
        }

        viewModel.getMayaCreditLoanApplicationAction.apply().start()

        XCTAssertEqual(viewModel.creditBannerState.value, .hidden)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, false)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
    }

    func testSuccessSuspendedCreditAccount() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(suspendedMayaCreditAccount))
        }

        viewModel.getMayaCreditLoanApplicationAction.apply().start()

        XCTAssertEqual(viewModel.creditBannerState.value, .hidden)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, false)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
    }

    func testSuccessSuspendedCreditAccountMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(suspendedMayaCreditAccount))
        }

        viewModel.getMayaCreditLoanApplicationAction.apply().start()

        XCTAssertEqual(viewModel.creditBannerState.value, .hidden)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, false)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
    }

    func testSuccessPastDueCreditAccount() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(pastDueMayaCreditAccount))
        }

        viewModel.getMayaCreditLoanApplicationAction.apply().start()

        XCTAssertEqual(viewModel.creditBannerState.value, .hidden)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, false)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
    }

    func testSuccessPastDueCreditAccountMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(pastDueMayaCreditAccount))
        }

        viewModel.getMayaCreditLoanApplicationAction.apply().start()

        XCTAssertEqual(viewModel.creditBannerState.value, .hidden)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, false)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
    }

    func testSuccessNotBookedCreditApplication() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(notBookedLoanApplication))
        }

        viewModel.getMayaCreditLoanApplicationAction.apply().start()

        XCTAssertEqual(viewModel.creditBannerState.value, .apply)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, false)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
    }

    func testErrorContentNotFoundCreditApplication() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getBackendError(code: APIConstants.APIStatusCode.contentNotFound.rawValue))
        }

        viewModel.getMayaCreditLoanApplicationAction.apply().start()

        XCTAssertEqual(viewModel.creditBannerState.value, .apply)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, false)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
    }

    func testErrorBadRequestCreditApplication() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getBackendError(code: APIConstants.HTTPStatusCode.badRequest.rawValue))
        }

        viewModel.getMayaCreditLoanApplicationAction.apply().start()

        XCTAssertEqual(viewModel.creditBannerState.value, .hidden)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, false)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
    }

    func testSuccessfulRefresh() {
        let request = ActivitiesRequest(fromDate: nil, toDate: nil, limit: 25, page: 1, order: nil, sort: nil, mode: .latest)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Activity.activities(request)))).thenReturn(getSuccessProducer())
            when(stub.request(isEqual(to: API.Balance.current))).thenReturn(getSuccessProducer())
        }

        viewModel.refreshDashboardAction.completed.signal.observeValues {
            XCTAssert(true, "Successful call to refresh")
        }

        viewModel.refreshDashboardAction.errors.signal.observeValues { _ in
            XCTFail("Failed call to refresh")
        }

        viewModel.refreshDashboardAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Activity.activities(request)))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Balance.current))
    }

    func testDashboardErrorActivityError() {
        let request = ActivitiesRequest(fromDate: nil, toDate: nil, limit: 25, page: 1, order: nil, sort: nil, mode: .latest)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Activity.activities(request)))).thenReturn(getBackendError())
            when(stub.request(isEqual(to: API.Balance.current))).thenReturn(getSuccessProducer())
        }

        viewModel.refreshDashboardAction.errors.signal.observeValues { error in
            XCTAssertTrue(error == DashboardError.activityError)
        }

        viewModel.refreshDashboardAction.completed.signal.observeValues {
            XCTFail("Unexpected success for activity error testing")
        }

        viewModel.refreshDashboardAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Activity.activities(request)))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Balance.current))
    }

    func testDashboardErrorBalanceErrorWithCache() {
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.balance)))).thenReturn(StoreResult.success(getBalance()))
        }

        let request = ActivitiesRequest(fromDate: nil, toDate: nil, limit: 25, page: 1, order: nil, sort: nil, mode: .latest)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Activity.activities(request)))).thenReturn(getSuccessProducer())
            when(stub.request(isEqual(to: API.Balance.current))).thenReturn(getBackendError())
        }

        viewModel.refreshDashboardAction.errors.observe(on: UIScheduler()).observeValues { error in
            XCTAssertTrue(error == DashboardError.balanceErrorWithCache)
        }

        viewModel.refreshDashboardAction.completed.observe(on: UIScheduler()).observeValues {
            XCTFail("Unexpected success for balance error with cache testing")
        }

        viewModel.refreshDashboardAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Activity.activities(request)))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Balance.current))
    }

    func testDashboardErrorBalanceErrorWithoutCache() {
        let request = ActivitiesRequest(fromDate: nil, toDate: nil, limit: 25, page: 1, order: nil, sort: nil, mode: .latest)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Activity.activities(request)))).thenReturn(getSuccessProducer())
            when(stub.request(isEqual(to: API.Balance.current))).thenReturn(getBackendError())
        }

        viewModel.refreshDashboardAction.errors.signal.observeValues { error in
            XCTAssertTrue(error == DashboardError.balanceErrorWithoutCache)
        }

        viewModel.refreshDashboardAction.completed.signal.observeValues {
            XCTFail("Unexpected success for balance error without cache testing")
        }

        viewModel.refreshDashboardAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Activity.activities(request)))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Balance.current))
    }

    func testDashboardErrorBalanceAndActivityErrorWithCache() {
        let request = ActivitiesRequest(fromDate: nil, toDate: nil, limit: 25, page: 1, order: nil, sort: nil, mode: .latest)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Activity.activities(request)))).thenReturn(getBackendError())
            when(stub.request(isEqual(to: API.Balance.current))).thenReturn(getBackendError())
        }

        viewModel.refreshDashboardAction.errors.signal.observeValues { error in
            XCTAssertTrue(error == DashboardError.balanceErrorWithoutCache)
        }

        viewModel.refreshDashboardAction.completed.signal.observeValues {
            XCTFail("Unexpected success")
        }

        viewModel.refreshDashboardAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Activity.activities(request)))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Balance.current))
    }

    func testDashboardErrorNoInternetConnection() {
        let request = ActivitiesRequest(fromDate: nil, toDate: nil, limit: 25, page: 1, order: nil, sort: nil, mode: .latest)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Activity.activities(request)))).thenReturn(getBackendError(code: APIConstants.HTTPStatusCode.internalServerError.rawValue))
            when(stub.request(isEqual(to: API.Balance.current))).thenReturn(getBackendError(code: APIConstants.HTTPStatusCode.internalServerError.rawValue))
        }

        viewModel.refreshDashboardAction.errors.signal.observeValues { error in
            XCTAssertTrue(error == DashboardError.noInternetConnection)
        }

        viewModel.refreshDashboardAction.completed.signal.observeValues {
            XCTFail("Unexpected success for no internet connection error testing")
        }

        viewModel.refreshDashboardAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Activity.activities(request)))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Balance.current))
    }

    func testSuccessfulGetActionCardsAction() {
        let request = API.ActionCards.recommendations(size: 5)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: request))).thenReturn(getSuccessProducer())
        }

        viewModel.getActionCardsAction.errors.observeValues { _ in
            XCTFail("Must not send failure signal")
        }?.addToDisposeBag(disposeBag)

        viewModel.getActionCardsAction.completed.observeValues {
            XCTAssert(true, "Successful call to get action cards")
        }?.addToDisposeBag(disposeBag)

        viewModel.getActionCardsAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ActionCards.recommendationMetadata), lastUpdated: any(), dateFormatter: any(DateFormatter.self), updateRequired: any())
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: request))
        verify(userDefaultsStore, times(1)).write(any(), options: isEqual(to: UserDefaultsStoreId.actionCardsLastUpdatedOn.writingOptions), completion: isNil())
    }

    func testSuccessfulGetActionCardsActionWithUpdatedData() {
        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.actionCardsLastUpdatedOn))).thenReturn(Date.distantPast)
        }

        let request = API.ActionCards.recommendations(size: 5)
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: request))).thenReturn(getSuccessProducer())
        }

        viewModel.getActionCardsAction.errors.observeValues { _ in
            XCTFail("Must not send failure signal")
        }?.addToDisposeBag(disposeBag)

        viewModel.getActionCardsAction.completed.observeValues {
            XCTAssert(true, "Successful call to get action cards")
        }?.addToDisposeBag(disposeBag)

        viewModel.getActionCardsAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ActionCards.recommendationMetadata), lastUpdated: any(), dateFormatter: any(DateFormatter.self), updateRequired: any())
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: request))
        verify(userDefaultsStore, times(1)).write(any(), options: isEqual(to: UserDefaultsStoreId.actionCardsLastUpdatedOn.writingOptions), completion: isNil())
    }

    func testGetActionCardsError() {
        let request = API.ActionCards.recommendations(size: 5)
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: request))).thenReturn(getBackendError())
        }

        viewModel.getActionCardsAction.errors.signal.observeValues { error in
            XCTAssertEqual(error.type, .generic)
        }?.addToDisposeBag(disposeBag)

        viewModel.getActionCardsAction.completed.signal.observeValues {
            XCTFail("Must not send completed signal")
        }?.addToDisposeBag(disposeBag)

        viewModel.getActionCardsAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: request))
    }

    func testGetActionCardsDatabaseError() {
        let request = API.ActionCards.recommendations(size: 5)
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: request))).thenReturn(getDatabaseError())
        }

        viewModel.getActionCardsAction.errors.signal.observeValues { error in
            XCTAssertEqual(error.type, .generic)
        }?.addToDisposeBag(disposeBag)

        viewModel.getActionCardsAction.completed.signal.observeValues {
            XCTFail("Must not send completed signal")
        }?.addToDisposeBag(disposeBag)

        viewModel.getActionCardsAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: request))
        /// Second fetch should happen when `case .failed` falls though `case .value`
        verify(databaseStore, times(2)).fetch(
            isEqual(to: DatabaseStore.ReadingOptions(
                DatabaseEntity.actionCard,
                sort: [NSSortDescriptor(keyPath: \ActionCard.priority, ascending: true)])),
            limit: any())
    }

    func testGetProfile() {
        let user = StubUser(kycStatus: KYCStatus.approved.rawValue)
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }

        stub(configurationService) { stub in
            when(stub.verifiedSellerLevels.get).thenReturn([])
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Profile.fetch))).thenReturn(getSuccessProducer())
        }

        viewModel.userProfileAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Profile.fetch))
    }

    func testSuccessImportContacts() {
        stub(contactsImporter) { stub in
            when(stub.importDeviceContacts(completionBlock: anyClosure())).then { completion in
                completion?(nil)
            }
        }

        viewModel.importContactsAction.completed.signal.observeValues {
            XCTAssert(true, "Successful import of contacts")
        }

        viewModel.importContactsAction.errors.signal.observeValues { error in
            XCTFail("Failed import of contacts with error: \(error)")
        }

        viewModel.importContactsAction.apply().start()
        verify(contactsImporter, times(1)).importDeviceContacts(completionBlock: anyClosure())
    }

    func testHasNoActivities() {
        stub(databaseStore) { stub in
            when(stub.fetch(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.activity(.any))), limit: any())).thenReturn(StoreResult.success([StubActivity()]))
        }

        XCTAssertFalse(viewModel.hasNoActivities())
        verify(databaseStore, times(1)).fetch(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.activity(.any))), limit: any())
    }

    func testIsReachable() {
        stub(apiProvider) { stub in
            when(stub.isReachable()).thenReturn(true)
        }
        XCTAssertTrue(viewModel.isReachable())

        stub(apiProvider) { stub in
            when(stub.isReachable()).thenReturn(false)
        }
        XCTAssertFalse(viewModel.isReachable())
    }

    func testIsLoggedIn() {
        let user = StubUser(kycStatus: KYCStatus.approved.rawValue)
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }

        XCTAssertTrue(viewModel.isLoggedIn)
    }

    func testIsActionCardsEnabled() {
        stub(configurationServiceV2) { stub in
            when(stub.actionCardsEnabled.get).thenReturn(falseConfigV2())
        }

        XCTAssertFalse(viewModel.isActionCardsEnabled)

        stub(configurationServiceV2) { stub in
            when(stub.actionCardsEnabled.get).thenReturn(trueConfigV2())
        }
        XCTAssertTrue(viewModel.isActionCardsEnabled)
    }

    func testHelpUrl() {
        stub(propertyListStore) { stub in
            when(stub.read(isEqual(to: PropertyListStoreId.helpBaseUrl))).thenReturn(StoreResult.success(helpUrl))
        }

        let stubUser = getUserWithPrefilledData()
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user))))
                .thenReturn(StoreResult.success(stubUser))
        }

        XCTAssertNotNil(viewModel.helpUrl)
        verify(databaseStore, times(1)).read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))
    }

    func testNilHelpUrl() {
        stub(propertyListStore) { stub in
            when(stub.read(isEqual(to: PropertyListStoreId.helpBaseUrl))).thenReturn(StoreResult.failure(.none))
        }
        XCTAssertNil(viewModel.helpUrl)
    }

    func testHelpUrlWithoutProfile() {
        stub(propertyListStore) { stub in
            when(stub.read(isEqual(to: PropertyListStoreId.helpBaseUrl))).thenReturn(StoreResult.success(helpUrl))
        }

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user))))
                .thenReturn(StoreResult.failure(.none))
        }

        XCTAssertEqual(viewModel.helpUrl, URL(string: helpUrl))
        verify(databaseStore, times(1)).read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))
    }

    func testReKycStatusWhenBackendToggleIsOff() {
        let stubProfile = getProfileData()

        let stubUser = StubUser(
            id: "user_id", accountStatus: "ACTIVE", canBeReferred: 1, canLinkCard: 1, kycStatus: "kyc1",
            network: "Smart", physicalCardStatus: "activated", status: "ACTIVE", token: "1234567",
            hasGovID: 1, activities: nil, backupIdentity: nil, balance: nil,
            funds: [getStubFunds(stubProfile: stubProfile)], identities: NSSet(array: [getStubIdentity()]),
            privacyPolicy: StubPrivacyPolicy(version: "v1"), profile: getProfileData(),
            type: nil, reKycStatus: nil, reKycReason: nil
        )

        let rekycStatus = stubUser.reKYCStatusValue
        XCTAssertEqual(rekycStatus, .none)
    }

    func testReKycStatusWhenRejected() {
        let stubProfile = getProfileData()

        let stubUser = StubUser(
            id: "user_id", accountStatus: "ACTIVE", canBeReferred: 1, canLinkCard: 1, kycStatus: "kyc1",
            network: "Smart", physicalCardStatus: "activated", status: "ACTIVE", token: "1234567", hasGovID: 1,
            activities: nil, backupIdentity: nil, balance: nil,
            funds: [getStubFunds(stubProfile: stubProfile)], identities: NSSet(array: [getStubIdentity()]),
            privacyPolicy: StubPrivacyPolicy(version: "v1"), profile: getProfileData(), type: nil,
            reKycStatus: "rejected", reKycReason: nil
        )

        XCTAssertTrue(((stubUser.reKycStatus) != nil))
        let rekycStatus = stubUser.reKYCStatusValue
        XCTAssertEqual(rekycStatus, .rejected)
    }

    func testReKycStatusWhenApproved() {
        let stubProfile = getProfileData()

        let stubUser = StubUser(
            id: "user_id", accountStatus: "ACTIVE", canBeReferred: 1, canLinkCard: 1, kycStatus: "kyc1",
            network: "Smart", physicalCardStatus: "activated", status: "ACTIVE", token: "1234567", hasGovID: 1,
            activities: nil, backupIdentity: nil, balance: nil, funds: [getStubFunds(stubProfile: stubProfile)],
            identities: NSSet(array: [getStubIdentity()]), privacyPolicy: StubPrivacyPolicy(version: "v1"),
            profile: getProfileData(), type: nil, reKycStatus: "approved", reKycReason: nil
        )

        XCTAssertTrue(((stubUser.reKycStatus) != nil))
        let rekycStatus = stubUser.reKYCStatusValue
        XCTAssertEqual(rekycStatus, .approved)
    }

    func testReKycStatusWhenEmpty() {
        let stubProfile = getProfileData()

        let stubUser = StubUser(
            id: "user_id", accountStatus: "ACTIVE", canBeReferred: 1, canLinkCard: 1, kycStatus: "kyc1",
            network: "Smart", physicalCardStatus: "activated", status: "ACTIVE", token: "1234567", hasGovID: 1,
            activities: nil, backupIdentity: nil, balance: nil, funds: [getStubFunds(stubProfile: stubProfile)],
            identities: NSSet(array: [getStubIdentity()]), privacyPolicy: StubPrivacyPolicy(version: "v1"),
            profile: getProfileData(), type: nil, reKycStatus: "", reKycReason: nil
        )

        XCTAssertTrue(((stubUser.reKycStatus) != nil))
        let rekycStatus = stubUser.reKYCStatusValue
        XCTAssertEqual(rekycStatus, .none)
    }

    func testGenerateAdCarouselViewModel() {
        let adBanner = AdBanner(imageURLString: nil, text: "Banner", message: "Banner Message", actionURLString: nil, tag: 1, screen: "DASHBOARD_APPEAR")
        let adBannerProperty: MutableProperty<[AdBanner]> = MutableProperty([adBanner])
        let adBannerService = ContainerWrapper.shared.resolve(AdBannerServiceProtocol.self) as! MockAdBannerServiceProtocol

        stub(adBannerService) { stub in
            when(stub.adBannersProperty.get).thenReturn(adBannerProperty)
            when(stub.rotationIntervalProperty.get).thenReturn(MutableProperty(Constants.CleverTapRotationIntervalDefault))
        }

        var adCarouselViewModel = viewModel.generateAdCarouselViewModel()
        XCTAssertTrue(adCarouselViewModel.isAutoscrollEnabled)
        XCTAssertEqual(adCarouselViewModel.adWidth, AdBannerProperties.dynamicWidth)
        XCTAssertEqual(adCarouselViewModel.adHeight, AdBannerProperties.dynamicHeight(desiredHeight: AdBannerProperties.dashboardHeight))
        XCTAssertEqual(adCarouselViewModel.module, .dashboard)

        adCarouselViewModel = viewModel.generateAdCarouselViewModel()
        XCTAssertEqual(adCarouselViewModel.adHeight, AdBannerProperties.dynamicHeight(desiredHeight: AdBannerProperties.dashboardHeight))
        XCTAssertEqual(adCarouselViewModel.adWidth, AdBannerProperties.dynamicWidth)
    }

    func testIsEasyCreditBannerV2Enabled() {
        stub(configurationServiceV2) { stub in
            when(stub.mecBannerV2Enabled.get).thenReturn(falseConfigV2())
        }

        XCTAssertFalse(viewModel.isEasyCreditBannerV2Enabled)

        stub(configurationServiceV2) { stub in
            when(stub.mecBannerV2Enabled.get).thenReturn(trueConfigV2())
        }
        XCTAssertTrue(viewModel.isEasyCreditBannerV2Enabled)
    }

    func testGetMayaCreditAccountStatusActive() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getWalletBanner(rrn)), type: isEqual(to: MayaCreditAccountStatus.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccountActive))
        }

        viewModel.getMayaCreditAccountStatusAction.apply(rrn).start()

        XCTAssertEqual(viewModel.creditBannerState.value, .availableCredit)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, true)
        XCTAssertEqual(viewModel.creditAccount.value, mayaCreditAccountActive.accountDetails)
        XCTAssertEqual(viewModel.creditLoanApplication.value, mayaCreditAccountActive.applicationDetails)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getWalletBanner(rrn)), type: isEqual(to: MayaCreditAccountStatus.self), decoder: any())
    }

    func testGetMayaCreditAccountStatusMissingInfo() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getWalletBanner(rrn)), type: isEqual(to: MayaCreditAccountStatus.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccountActiveMissingInfo))
        }

        viewModel.getMayaCreditAccountStatusAction.apply(rrn).start()

        XCTAssertEqual(viewModel.creditBannerState.value, .availableCredit)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, false)
        XCTAssertEqual(viewModel.creditAccount.value, mayaCreditAccountActive.accountDetails)
        XCTAssertEqual(viewModel.creditLoanApplication.value, mayaCreditAccountActive.applicationDetails)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getWalletBanner(rrn)), type: isEqual(to: MayaCreditAccountStatus.self), decoder: any())
    }

    func testGetMayaCreditAccountStatusMissing() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getWalletBanner(rrn)), type: isEqual(to: MayaCreditAccountStatus.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccountMissing))
        }

        viewModel.getMayaCreditAccountStatusAction.apply(rrn).start()

        XCTAssertEqual(viewModel.creditBannerState.value, .apply)
        XCTAssertEqual(viewModel.creditAccountCanTransfer.value, false)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getWalletBanner(rrn)), type: isEqual(to: MayaCreditAccountStatus.self), decoder: any())
    }

    func testGetMayaCreditAccountStatusInvalid() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getWalletBanner(rrn)), type: isEqual(to: MayaCreditAccountStatus.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccountInvalid))
        }

        viewModel.getMayaCreditAccountStatusAction.apply(rrn).start()

        XCTAssertEqual(viewModel.creditBannerState.value, .hidden)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getWalletBanner(rrn)), type: isEqual(to: MayaCreditAccountStatus.self), decoder: any())
    }

    func testGetMayaCreditAccountStatusError() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getWalletBanner(rrn)), type: isEqual(to: MayaCreditAccountStatus.self), decoder: any())).thenReturn(getDatabaseError())
        }

        viewModel.getMayaCreditAccountStatusAction.apply(rrn).start()

        XCTAssertEqual(viewModel.creditBannerState.value, .hidden)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getWalletBanner(rrn)), type: isEqual(to: MayaCreditAccountStatus.self), decoder: any())
    }
}

fileprivate extension MayaDashboardViewModelTest {
    func getBalance() -> Balance {
        let balance = StubBalance(available: StubAvailableAmount(currency: "PHP", id: "1", value: NSNumber(value: 10000.00), balance: StubBalance()), current: StubCurrentAmount(currency: "PHP", id: "1", value: NSNumber(value: 10000.00), balance: StubBalance()), user: nil)
        return balance
    }

    func getUserWithPrefilledData() -> StubUser {
        let stubProfile = getProfileData()
        let stubMainIdentity = getStubIdentity()
        let stubFund = getStubFunds(stubProfile: stubProfile)
        return StubUser(id: "user_id", accountStatus: "ACTIVE", canBeReferred: 1, canLinkCard: 1, kycStatus: "kyc1", network: "Smart", physicalCardStatus: "activated", status: "ACTIVE", token: "1234567", hasGovID: 1, activities: nil, backupIdentity: nil, balance: nil, funds: [stubFund], identities: NSSet(array: [stubMainIdentity]), privacyPolicy: StubPrivacyPolicy(version: "v1"), profile: stubProfile, type: nil, reKycStatus: "tagged", reKycReason: "No_ID")
    }

    func getStubAddress() -> StubAddress {
        return StubAddress(city: "San Jose Del Monte", country: "PH", line1: "123", line2: nil, locality: "Highway Hills", state: "NCR", zipCode: "1501", profile: StubProfile())
    }

    func getProfileData() -> StubProfile {
        let stubUser = StubUser()
        let stubAddress = getStubAddress()
        let stubProfile = StubProfile(birthDate: nil, firstName: "John", lastName: "Doe", middleName: nil, address: stubAddress, user: stubUser)
        return stubProfile
    }

    func getStubFunds(stubProfile: StubProfile, cardProfile: StubCardProfile = StubCardProfile()) -> StubFund {
        StubFund(enabled: 1, id: "1234567-abcde", name: "virtual_card", status: FundSourceStatus.virtualCard.rawValue, type: FundSourceType.virtual.rawValue, cardProfile: cardProfile, purchases: nil, user: stubProfile.user)
    }

    func getStubIdentity() -> StubMainIdentity {
        StubMainIdentity(type: "msisdn", value: "+639123456789")
    }
}
