//
//  QRValidationUtilityTest.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON><PERSON><PERSON> on 6/3/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import MPQRCoreSDK
import XCTest

class QRValidationUtilityTest: XCTestCase {
    private let validQRP2PString = "00020101021227900012com.p2pqrpay0111AUBKPHMMXXX0208********0308id1234560412************0515+63-911-394900952046016530360854071253.255802PH5913Test Receiver6011Mandaluyong6104155062890011ph.ppmi.p2m0107bill1230203***0310storelabel0403***0506ref1230603***0707term1230903AME64340002PH0114Ibang Pangalan0206Syudad63047FC9"

    private let invalidQRPHP2PDynamicString = "00020201021227900012com.p2pqrpay0111AUBKPHMMXXX0208********0308id1234560412************0515+63-911-394900952046016530360854071253.255802PH5913Test Receiver6011Mandaluyong6104155062890011ph.ppmi.p2m0107bill1230203***0310storelabel0403***0506ref1230603***0707term1230903AME64340002PH0114Ibang Pangalan0206Syudad63057FC9"

    private let validP2MString = "00020101021128720011ph.ppmi.p2m0111MBTCPHMMXXX0312************0415***************05030005204601653036085802PH5920CAFE MARY GRACE INC.6011Mandaluyong6104100162650011ph.ppmi.p2m0203***0403********************0708********0803***64250002PH0115CAFE MARY GRACE88490012ph.ppmi.qrph0129006392094574580111MBTCPHMMXXX63040D2A"

    var qrDetailsP2P: QRDetails = QRDetails(
        acquirerRequiredInfo: nil,
        mobileNumber: nil,
        proxyNotifFlag: nil,
        creditAccountNumber: "***********",
        globalUniqueIdentifier: "com.p2pqrpay",
        acquirerId: "SRCPPHM2XXX",
        paymentType: "********",
        merchantCountryCode: "PH",
        merchantCity: "Laguna",
        merchantId: nil,
        merchantMobileNumber: nil,
        merchantName: "Miaaa",
        merchantPostalCode: "4028",
        payloadFormatIndicator: "01",
        pointOfInitiation: "11",
        transactionCurrencyCode: "608",
        merchantCategoryCode: "6016",
        merchantCrc: "87C8",
        merchantCreditAccountNumber: nil,
        transactionAmount: nil,
        additionalData: QRAdditionalData(
            globalUniqueIdentifier: "********",
            billNumber: nil,
            additionalMobileNumber: nil,
            storeName: nil,
            merchantPostalCode: "4028",
            loyaltyNumber: "***",
            referenceId: "********",
            customerLabel: nil,
            terminalId: "********",
            purpose: nil,
            additionalRequestData: nil
        ),
        additionalDataPayload: nil,
        p2pMerchantAccountInformation: nil,
        p2mMerchantAccountInformation: nil,
        payloadInformationIndicator: nil,
        languageData: nil,
        convenienceIndicator: nil,
        convenienceFeeFixed: nil,
        convenienceFeePercentage: nil
    )

    var qrDetailsP2M = QRDetails(
        acquirerRequiredInfo: nil,
        mobileNumber: nil,
        proxyNotifFlag: "000",
        creditAccountNumber: "***********",
        globalUniqueIdentifier: "ph.ppmi.p2m",
        acquirerId: "MBTCPHMMXXX",
        paymentType: nil,
        merchantCountryCode: "PH",
        merchantCity: "Mandaluyong",
        merchantId: "************",
        merchantMobileNumber: nil,
        merchantName: "CAFE MARY GRACE INC.",
        merchantPostalCode: "1001",
        payloadFormatIndicator: "01",
        pointOfInitiation: "11",
        transactionCurrencyCode: "608",
        merchantCategoryCode: "6016",
        merchantCrc: "0D2A",
        merchantCreditAccountNumber: "***************",
        transactionAmount: nil,
        additionalData: QRAdditionalData(
            globalUniqueIdentifier: "ph.ppmi.p2m",
            billNumber: nil,
            additionalMobileNumber: "***",
            storeName: nil,
            merchantPostalCode: nil,
            loyaltyNumber: "***",
            referenceId: "211000",
            customerLabel: "***",
            terminalId: "********",
            purpose: "***",
            additionalRequestData: nil
        ),
        additionalDataPayload: nil,
        p2pMerchantAccountInformation: nil,
        p2mMerchantAccountInformation: nil,
        payloadInformationIndicator: nil,
        languageData: nil,
        convenienceIndicator: nil,
        convenienceFeeFixed: nil,
        convenienceFeePercentage: nil
    )

    var qrValidationUtility: QRValidationUtility = QRValidationUtility()

    func testCheckQRPHFormatIndicator_WhenValidQR_ShouldReturnTrue() {
        let value = try? qrValidationUtility.checkQRPHFormatIndicator(qrCode: validQRP2PString)

       XCTAssertEqual(value, true)
    }

    func testCheckQRPHFormatIndicator_WhenEmptyString_ShouldReturnError() {
        XCTAssertThrowsError(try qrValidationUtility.checkQRPHFormatIndicator(qrCode: "")) { error in
            XCTAssertEqual(error as? QRError, QRError.parsingError(field: QRPHField.payloadFormatIndicator))
        }
    }

    func testCheckQRPHFormatIndicator_WhenInvalidQR_ShouldReturnError() {
        XCTAssertThrowsError(try qrValidationUtility.checkQRPHFormatIndicator(qrCode: invalidQRPHP2PDynamicString)) { error in
            XCTAssertEqual(error as? QRError, QRError.parsingError(field: QRPHField.payloadFormatIndicator))
        }
    }

    func testCheckQRPHCrc_WhenValidQR_ShouldReturnTrue() {
       let value = try? qrValidationUtility.checkQRPHCrc(qrCode: validQRP2PString)

       XCTAssertEqual(value, true)
    }

    func testCheckQRPHCrc_WhenEmptyString_ShouldReturnError() {
        XCTAssertThrowsError(try qrValidationUtility.checkQRPHCrc(qrCode: "")) { error in
            XCTAssertEqual(error as? QRError, QRError.parsingError(field: QRPHField.crc))
        }
    }

    func testCheckQRPHCrc_WhenInvalidQR_ShouldReturnError() {
        XCTAssertThrowsError(try qrValidationUtility.checkQRPHCrc(qrCode: invalidQRPHP2PDynamicString)) { error in
            XCTAssertEqual(error as? QRError, QRError.missingRequiredField(field: QRPHField.crc, bic: ""))
        }
    }

    func testValidateQRPHCrc_WhenValidQR_ShouldReturnTrue() {
       let value = try? qrValidationUtility.validateQRPHCrc(qrCode: validQRP2PString)

       XCTAssertEqual(value, true)
    }

    func testValidateQRPHCrc_WhenEmptyString_ShouldReturnError() {
        XCTAssertThrowsError(try qrValidationUtility.validateQRPHCrc(qrCode: "", bic: "")) { error in
            XCTAssertEqual(error as? QRError, QRError.tamperedQR(bic: ""))
        }
    }

    func testValidateQRPHCrc_WhenInvalidQR_ShouldReturnError() {
        XCTAssertThrowsError(try qrValidationUtility.validateQRPHCrc(qrCode: invalidQRPHP2PDynamicString, bic: "123")) { error in
            XCTAssertEqual(error as? QRError, QRError.tamperedQR(bic: "123"))
        }
    }

    func testParseQRPH_WhenValidP2PQR_ShouldReturnExpectedQRDetails() throws {
        let qrDetails = try qrValidationUtility.parseQRPH(qrCode: validQRP2PString)

        XCTAssertNil(qrDetails.p2mMerchantAccountInformation)
        XCTAssertNotNil(qrDetails.p2pMerchantAccountInformation)
        XCTAssertEqual(qrDetails.payloadFormatIndicator, "01")
        XCTAssertEqual(qrDetails.pointOfInitiation, "12")
        XCTAssertEqual(qrDetails.globalUniqueIdentifier, "com.p2pqrpay")
        XCTAssertEqual(qrDetails.acquirerId, "AUBKPHMMXXX")
        XCTAssertEqual(qrDetails.paymentType, "********")
        XCTAssertEqual(qrDetails.merchantId, "id123456")
        XCTAssertEqual(qrDetails.creditAccountNumber, "************")
        XCTAssertEqual(qrDetails.mobileNumber, "+63-911-3949009")
        XCTAssertEqual(qrDetails.merchantCreditAccountNumber, nil)
        XCTAssertEqual(qrDetails.proxyNotifFlag, nil)
        XCTAssertEqual(qrDetails.merchantCategoryCode, "6016")
        XCTAssertEqual(qrDetails.transactionAmount, "1253.25")
        XCTAssertEqual(qrDetails.transactionCurrencyCode, "608")
        XCTAssertEqual(qrDetails.convenienceIndicator, nil)
        XCTAssertEqual(qrDetails.convenienceFeeFixed, nil)
        XCTAssertEqual(qrDetails.convenienceFeePercentage, nil)
        XCTAssertEqual(qrDetails.merchantCountryCode, "PH")
        XCTAssertEqual(qrDetails.merchantName, "Test Receiver")
        XCTAssertEqual(qrDetails.merchantCity, "Mandaluyong")
        XCTAssertEqual(qrDetails.merchantPostalCode, "1550")
        XCTAssertEqual(qrDetails.additionalData?.billNumber, "bill123")
        XCTAssertEqual(qrDetails.additionalData?.additionalMobileNumber, "***")
        XCTAssertEqual(qrDetails.additionalData?.storeName, "storelabel")
        XCTAssertEqual(qrDetails.additionalData?.loyaltyNumber, "***")
        XCTAssertEqual(qrDetails.additionalData?.referenceId, "ref123")
        XCTAssertEqual(qrDetails.additionalData?.customerLabel, "***")
        XCTAssertEqual(qrDetails.additionalData?.terminalId, "term123")
        XCTAssertEqual(qrDetails.additionalData?.purpose, nil)
        XCTAssertEqual(qrDetails.additionalData?.additionalRequestData, "AME")
    }

    func testParseQRPH_WhenValidP2MQR_ShouldReturnExpectedQRDetails() throws {
        let qrDetails = try qrValidationUtility.parseQRPH(qrCode: validP2MString)

        XCTAssertNil(qrDetails.p2pMerchantAccountInformation)
        XCTAssertNotNil(qrDetails.p2mMerchantAccountInformation)
        XCTAssertEqual(qrDetails.payloadFormatIndicator, "01")
        XCTAssertEqual(qrDetails.pointOfInitiation, "11")
        XCTAssertEqual(qrDetails.globalUniqueIdentifier, "ph.ppmi.p2m")
        XCTAssertEqual(qrDetails.acquirerId, "MBTCPHMMXXX")
        XCTAssertEqual(qrDetails.paymentType, nil)
        XCTAssertEqual(qrDetails.merchantId, "************")
        XCTAssertEqual(qrDetails.mobileNumber, nil)
        XCTAssertEqual(qrDetails.merchantCreditAccountNumber, "***************")
        XCTAssertEqual(qrDetails.proxyNotifFlag, "000")
        XCTAssertEqual(qrDetails.merchantCategoryCode, "6016")
        XCTAssertEqual(qrDetails.transactionAmount, nil)
        XCTAssertEqual(qrDetails.transactionCurrencyCode, "608")
        XCTAssertEqual(qrDetails.convenienceIndicator, nil)
        XCTAssertEqual(qrDetails.convenienceFeeFixed, nil)
        XCTAssertEqual(qrDetails.convenienceFeePercentage, nil)
        XCTAssertEqual(qrDetails.merchantCountryCode, "PH")
        XCTAssertEqual(qrDetails.merchantName, "CAFE MARY GRACE INC.")
        XCTAssertEqual(qrDetails.merchantCity, "Mandaluyong")
        XCTAssertEqual(qrDetails.merchantPostalCode, "1001")
        XCTAssertEqual(qrDetails.additionalData?.billNumber, nil)
        XCTAssertEqual(qrDetails.additionalData?.additionalMobileNumber, "***")
        XCTAssertEqual(qrDetails.additionalData?.storeName, nil)
        XCTAssertEqual(qrDetails.additionalData?.loyaltyNumber, "***")
        XCTAssertEqual(qrDetails.additionalData?.referenceId, "211000")
        XCTAssertEqual(qrDetails.additionalData?.customerLabel, "***")
        XCTAssertEqual(qrDetails.additionalData?.terminalId, "********")
        XCTAssertEqual(qrDetails.additionalData?.purpose, "***")
        XCTAssertEqual(qrDetails.additionalData?.additionalRequestData, nil)
    }

    func testParseQRPH_WhenEmptyString_ShouldThrowParsingError() {
        XCTAssertThrowsError(try qrValidationUtility.parseQRPH(qrCode: "XXXXXXXXX")) { error in
            XCTAssertEqual(error as? QRError, QRError.parsingError(field: QRPHField.unknown))
        }
    }

    func testGetQRPHType_WhenP2PAndP2MAreNil_ShouldReturnInvalid() throws {
        let qrDetails = QRDetails()
        XCTAssertThrowsError(try qrValidationUtility.getQRPHType(qrDetails: qrDetails)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.qrType, bic: ""))
        }
    }

    func testGetQRPHType_WhenP2PAndP2MHaveValues_ShouldReturnInvalid() throws {
        var qrDetails = QRDetails()
        qrDetails.p2pMerchantAccountInformation = MAIData()
        qrDetails.p2mMerchantAccountInformation = MAIData()

        XCTAssertThrowsError(try qrValidationUtility.getQRPHType(qrDetails: qrDetails)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "", field: QRPHField.qrType, bic: ""))
        }
    }

    func testGetQRPHType_WhenP2pGloballyUniqueIdIsNull_ShouldThrowMissingField() {
        var qrDetails = QRDetails()
        qrDetails.p2pMerchantAccountInformation = MAIData()
        XCTAssertThrowsError(try qrValidationUtility.getQRPHType(qrDetails: qrDetails)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.p2pAcquirerID, bic: ""))
        }
    }

    func testGetQRPHType_WhenP2mGloballyUniqueIdIsNull_ShouldThrowMissingField() {
        var qrDetails = QRDetails()
        qrDetails.p2mMerchantAccountInformation = MAIData()
        XCTAssertThrowsError(try qrValidationUtility.getQRPHType(qrDetails: qrDetails)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.p2mAcquirerID, bic: ""))
        }
    }

    func testGetQRPHType_WhenP2pGloballyUniqueIdInvalid_ShouldThrowInvalidValue() {
        var qrDetails = QRDetails()
        let expectedGlobalUniqueIdentifier = "invalid.qr.pay"
        qrDetails.p2pMerchantAccountInformation = MAIData()
        qrDetails.globalUniqueIdentifier = expectedGlobalUniqueIdentifier
        qrDetails.acquirerId = "SAMPLE BANK"

        XCTAssertThrowsError(try qrValidationUtility.getQRPHType(qrDetails: qrDetails)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: expectedGlobalUniqueIdentifier, field: QRPHField.p2pGloballyUniqueId, bic: "SAMPLE BANK"))
        }
    }

    func testGetQRPHType_WhenValidGlobalUniqueIdentifierAndAcquirerID_ShouldReturnSendMoney() throws {
        var qrDetails = QRDetails()
        qrDetails.p2pMerchantAccountInformation = MAIData()
        qrDetails.globalUniqueIdentifier = QRPHConstants.p2pGloballyUniqueID
        qrDetails.acquirerId = QRPHConstants.mayaAcquirerID

        let result = try qrValidationUtility.getQRPHType(qrDetails: qrDetails)
        XCTAssertEqual(result, .p2pSendMoney)
    }

    func testGetQRPHType_WhenValidP2pGlobalUniqueIdentifierAndAcquirerIDIsNull_ShouldReturnMissingRequiredField() throws {
        var qrDetails = QRDetails()
        qrDetails.p2pMerchantAccountInformation = MAIData()
        qrDetails.globalUniqueIdentifier = QRPHConstants.p2pGloballyUniqueID
        qrDetails.acquirerId = nil

        XCTAssertThrowsError(try qrValidationUtility.getQRPHType(qrDetails: qrDetails)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.p2pAcquirerID, bic: ""))
        }
    }

    func testGetQRPHType_WhenValidP2mGlobalUniqueIdentifierAndAcquirerIDIsNull_ShouldReturnMissingRequiredField() throws {
       var qrDetails = QRDetails()
       qrDetails.p2mMerchantAccountInformation = MAIData()
       qrDetails.globalUniqueIdentifier = QRPHConstants.p2mGloballyUniqueID
       qrDetails.acquirerId = nil

       XCTAssertThrowsError(try qrValidationUtility.getQRPHType(qrDetails: qrDetails)) { error in
           XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.p2mAcquirerID, bic: ""))
       }
    }

    func testGetQRPHType_WhenP2PWithOtherBIC_ShouldReturnBankTransfer() throws {
        var qrDetails = QRDetails()
        qrDetails.p2pMerchantAccountInformation = MAIData()
        qrDetails.globalUniqueIdentifier = QRPHConstants.p2pGloballyUniqueID
        qrDetails.acquirerId = "SAMPLE BANK"
        let result = try qrValidationUtility.getQRPHType(qrDetails: qrDetails)
        XCTAssertEqual(result, .p2pBankTransfer)
    }

    func testGetQRPHType_WhenP2MWithBillersGUID_ShouldReturnP2B() throws {
        var qrDetails = QRDetails()
        qrDetails.p2mMerchantAccountInformation = MAIData()
        qrDetails.globalUniqueIdentifier = QRPHConstants.billersGloballyUniqueID
        qrDetails.acquirerId = "SAMPLE BANK"
        let result = try qrValidationUtility.getQRPHType(qrDetails: qrDetails)
        XCTAssertEqual(result, .p2b)
    }

    func testGetQRPHType_WhenP2MWithMicroGUIDAndMayaBIC_ShouldReturnOnUs() throws {
        var qrDetails = QRDetails()
        qrDetails.p2mMerchantAccountInformation = MAIData()
        qrDetails.globalUniqueIdentifier = QRPHConstants.p2mGloballyUniqueID
        qrDetails.acquirerId = QRPHConstants.mayaAcquirerID
        let result = try qrValidationUtility.getQRPHType(qrDetails: qrDetails)
        XCTAssertEqual(result, .p2mOnUs)
    }

    func testGetQRPHType_WhenP2MWithP2MGUIDAndNonMayaAcquirerID_ShouldReturnOffUs() throws {
        var qrDetails = QRDetails()
        qrDetails.p2mMerchantAccountInformation = MAIData()
        qrDetails.globalUniqueIdentifier = QRPHConstants.p2mGloballyUniqueID
        qrDetails.acquirerId = "SAMPLE MERCHANT"
        let result = try qrValidationUtility.getQRPHType(qrDetails: qrDetails)
        XCTAssertEqual(result, .p2mOffUs)
    }

    func testValidateQRPHP2p_ShouldNotThrowError() {
        XCTAssertNoThrow(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P))
    }

    func testValidateQRPHP2p_WhenAcquirerIDIsNull_ShouldThrowError() {
        qrDetailsP2P.acquirerId = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.p2pAcquirerID, bic: ""))
        }
    }

    func testValidateQRPHP2p_WhenPayloadFormatIndicatorIsNull_ShouldThrowError() {
        qrDetailsP2P.payloadFormatIndicator = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.payloadFormatIndicator, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenPointOfInitiationIsNull_ShouldThrowError() {
        qrDetailsP2P.pointOfInitiation = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.pointOfInitiation, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenInvalidPointOfInitiation_ShouldThrowError() {
        qrDetailsP2P.pointOfInitiation = "69"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "69", field: QRPHField.pointOfInitiation, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenGlobalUniqueIdentifierIsNull_ShouldThrowError() {
        qrDetailsP2P.globalUniqueIdentifier = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.p2pGloballyUniqueId, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenGlobalUniqueIdentifierIsInvalidLength_ShouldThrowError() {
        qrDetailsP2P.globalUniqueIdentifier = "ThisStringIsMoreThanTwentyCharactersLong"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidLength(value: "ThisStringIsMoreThanTwentyCharactersLong", field: QRPHField.p2pGloballyUniqueId, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenAcquirerIdIsNull_ShouldThrowError() {
        qrDetailsP2P.acquirerId = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.p2pAcquirerID, bic: ""))
        }
    }

    func testValidateQRPHP2p_WhenAcquirerIdIsInvalidLength_ShouldThrowError() {
       qrDetailsP2P.acquirerId = "Kendrick Lamar"

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "Kendrick Lamar", field: QRPHField.p2pAcquirerID, bic: ""))
       }
    }

    func testValidateQRPHP2p_WhenPaymentTypeIsNull_ShouldThrowError() {
       qrDetailsP2P.paymentType = nil

      XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
          XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.paymentType, bic: "SRCPPHM2XXX"))
      }
    }

    func testValidateQRPHP2p_WhenInvalidPaymentType_ShouldThrowError() {
        qrDetailsP2P.paymentType = "good kid, m.A.A.d city"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "good kid, m.A.A.d city", field: QRPHField.paymentType, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenMerchantIdIsInvalidLength_ShouldThrowError() {
        qrDetailsP2P.merchantId = "To Pimp a Butterfly"

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "To Pimp a Butterfly", field: QRPHField.p2pMerchantID, bic: "SRCPPHM2XXX"))
       }
    }

    func testValidateQRPHP2p_WhenCreditAccountNumberIsNull_ShouldThrowError() {
        qrDetailsP2P.creditAccountNumber = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
          XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.p2pMerchantCreditAccount, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenCreditAccountNumberIsInvalidLength_ShouldThrowError() {
        qrDetailsP2P.creditAccountNumber = "The mannerisms of Raphael, I can heal and give you art"

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "The mannerisms of Raphael, I can heal and give you art", field: QRPHField.p2pMerchantCreditAccount, bic: "SRCPPHM2XXX"))
       }
    }

    func testValidateQRPHP2p_WhenInvalidMobileNumber_ShouldThrowError() {
        qrDetailsP2P.mobileNumber = "+1234567"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "+1234567", field: QRPHField.merchantMobileNumber, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenMobileNumberIsInvalidLength_ShouldThrowError() {
        qrDetailsP2P.mobileNumber = "+639-**************"

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "+639-**************", field: QRPHField.merchantMobileNumber, bic: "SRCPPHM2XXX"))
       }
    }

    func testValidateQRPHP2p_WhenMobileNumberIsValid_ShouldNotThrowError() {
        qrDetailsP2P.mobileNumber = "+639-*********"
        XCTAssertNoThrow(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P))
    }

    func testValidateQRPHP2p_WhenMerchantCategoryCodeIsNull_ShouldThrowError() {
        qrDetailsP2P.merchantCategoryCode = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
          XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.merchantCategoryCode, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenInvalidMerchantCategoryCode_ShouldThrowError() {
        qrDetailsP2P.merchantCategoryCode = "DAMN."

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "DAMN.", field: QRPHField.merchantCategoryCode, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenTransactionCurrencyCodeIsNull_ShouldThrowError() {
        qrDetailsP2P.transactionCurrencyCode = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
          XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.transactionCurrencyCode, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenInvalidTransactionCurrencyCode_ShouldThrowError() {
        qrDetailsP2P.transactionCurrencyCode = "616"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "616", field: QRPHField.transactionCurrencyCode, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenStaticQR_WhenInvalidTransactionAmount_ShouldThrowError() {
        qrDetailsP2P.transactionAmount = "100.00"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "100.00", field: QRPHField.transactionAmount, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenDynamicQR_WhenTransactionAmountIsNull_ShouldThrowError() {
        qrDetailsP2P.pointOfInitiation = "12"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
          XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.transactionAmount, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenInvalidConvenienceIndicator_ShouldThrowError() {
        qrDetailsP2P.convenienceIndicator = "616"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "616", field: QRPHField.convenienceIndicator, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenConvenienceFeeFixedIndicatorIsNull_ShouldThrowError() {
        qrDetailsP2P.convenienceIndicator = "02"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.convenienceFeeFixed, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenConvenienceFeePercentageIndicatorIsNull_ShouldThrowError() {
        qrDetailsP2P.convenienceIndicator = "03"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.convenienceFeePercentage, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenMerchantCountryCodeIsNull_ShouldThrowError() {
        qrDetailsP2P.merchantCountryCode = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
          XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.countryCode, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenInvalidMerchantCountryCode_ShouldThrowError() {
        qrDetailsP2P.merchantCountryCode = "US"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "US", field: QRPHField.countryCode, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenMerchantNameIsNull_ShouldThrowError() {
        qrDetailsP2P.merchantName = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
          XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.merchantName, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenInvalidMerchantName_ShouldThrowError() {
        qrDetailsP2P.merchantName = "Café Später"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "Café Später", field: QRPHField.merchantName, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenMerchantCityIsNull_ShouldThrowError() {
        qrDetailsP2P.merchantCity = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
          XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.merchantCity, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenInvalidMerchantCity_ShouldThrowError() {
        qrDetailsP2P.merchantCity = "Österreich"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "Österreich", field: QRPHField.merchantCity, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenPostalCodeIsInvalidLength_ShouldThrowError() {
        qrDetailsP2P.merchantPostalCode = "69"

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "69", field: QRPHField.postalCode, bic: "SRCPPHM2XXX"))
       }
    }

    func testValidateQRPHP2p_WhenAdditionalDataGlobalUniqueIdentifierIsNull_ShouldThrowError() {
        qrDetailsP2P.additionalDataPayload = AdditionalData()
        qrDetailsP2P.additionalData?.globalUniqueIdentifier = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.p2pAdditionalDataGloballyUniqueId, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenAdditionalDataGlobalUniqueIdentifierIsInvalidLength_ShouldThrowError() {
        qrDetailsP2P.additionalDataPayload = AdditionalData()
        qrDetailsP2P.additionalData?.globalUniqueIdentifier = "untitled unmastered."

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "untitled unmastered.", field: QRPHField.p2pAdditionalDataGloballyUniqueId, bic: "SRCPPHM2XXX"))
       }
    }

    func testValidateQRPHP2p_WhenInvalidAdditionalDataMobileNumber_ShouldThrowError() {
        qrDetailsP2P.additionalDataPayload = AdditionalData()
        qrDetailsP2P.additionalData?.additionalMobileNumber = "+********"

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
           XCTAssertEqual(error as? QRError, .invalidValue(value: "+********", field: QRPHField.mobileNumber, bic: "SRCPPHM2XXX"))
       }
    }

    func testValidateQRPHP2p_WhenAdditionalDataMobileNumberIsValid_ShouldNotThrowError() {
        qrDetailsP2P.additionalDataPayload = AdditionalData()
        qrDetailsP2P.additionalData?.additionalMobileNumber = "+639-*********"
        XCTAssertNoThrow(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P))
    }

    func testValidateQRPHP2p_WhenAdditionalDataMobileNumberPlaceholderIsValid_ShouldNotThrowError() {
        qrDetailsP2P.additionalDataPayload = AdditionalData()
        qrDetailsP2P.additionalData?.additionalMobileNumber = "***"
        XCTAssertNoThrow(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P))
    }

    func testValidateQRPHP2p_WhenInvalidStoreName_ShouldThrowError() {
        qrDetailsP2P.additionalDataPayload = AdditionalData()
        qrDetailsP2P.additionalData?.storeName = "Café Später"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "Café Später", field: QRPHField.storeLabel, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenStoreNameIsInvalidLength_ShouldThrowError() {
        qrDetailsP2P.additionalDataPayload = AdditionalData()
        qrDetailsP2P.additionalData?.storeName = "Institutionalized"

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "Institutionalized", field: QRPHField.storeLabel, bic: "SRCPPHM2XXX"))
       }
    }

    func testValidateQRPHP2p_WhenInvalidReferenceId_ShouldThrowError() {
        qrDetailsP2P.additionalDataPayload = AdditionalData()
        qrDetailsP2P.additionalData?.referenceId = "123456ū"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "123456ū", field: QRPHField.referenceLabel, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenReferenceIdIsInvalidLength_ShouldThrowError() {
        qrDetailsP2P.additionalDataPayload = AdditionalData()
        qrDetailsP2P.additionalData?.referenceId = "Sing About Me, I'm Dying of Thirst"

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "Sing About Me, I'm Dying of Thirst", field: QRPHField.referenceLabel, bic: "SRCPPHM2XXX"))
       }
    }

    func testValidateQRPHP2p_WhenInvalidCustomerLabel_ShouldThrowError() {
        qrDetailsP2P.additionalDataPayload = AdditionalData()
        qrDetailsP2P.additionalData?.customerLabel = "Café"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "Café", field: QRPHField.customerLabel, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenCustomerLabelPlaceholderIsValid_ShouldNotThrowError() {
        qrDetailsP2P.additionalDataPayload = AdditionalData()
        qrDetailsP2P.additionalData?.customerLabel = "***"
        XCTAssertNoThrow(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P))
    }

    func testValidateQRPHP2p_WhenTerminalIdIsNull_ShouldThrowError() {
        qrDetailsP2P.additionalDataPayload = AdditionalData()
        qrDetailsP2P.additionalData?.terminalId = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.terminalLabel, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenInvalidTerminalId_ShouldThrowError() {
        qrDetailsP2P.additionalDataPayload = AdditionalData()
        qrDetailsP2P.additionalData?.terminalId = "Tèrmiń@l"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "Tèrmiń@l", field: QRPHField.terminalLabel, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenTerminalIdIsInvalidLength_ShouldThrowError() {
        qrDetailsP2P.additionalDataPayload = AdditionalData()
        qrDetailsP2P.additionalData?.terminalId = "DUCKWORTH."

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidLength(value: "DUCKWORTH.", field: QRPHField.terminalLabel, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenCRCIsNull_ShouldThrowError() {
        qrDetailsP2P.merchantCrc = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.crc, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenInvalidCRC_ShouldThrowError() {
        qrDetailsP2P.merchantCrc = "8aa0"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "8aa0", field: QRPHField.crc, bic: "SRCPPHM2XXX"))
        }
    }

    func testValidateQRPHP2p_WhenLanguagePreferenceIsInvalidLength_ShouldThrowError() {
        var languageData = LanguageData()
        languageData.languagePreference = "ESP"
        qrDetailsP2P.languageData = languageData

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "ESP", field: QRPHField.languagePreference, bic: "SRCPPHM2XXX"))
       }
    }

    func testValidateQRPHP2p_WhebMerchantNameAlternateLanguageIsInvalidLength_ShouldThrowError() {
        var languageData = LanguageData()
        languageData.alternateMerchantName = "Mr. Morale & the Big Steppers"
        qrDetailsP2P.languageData = languageData

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "Mr. Morale & the Big Steppers", field: QRPHField.merchantNameAlternateLanguage, bic: "SRCPPHM2XXX"))
       }
    }

    func testValidateQRPHP2p_WhenMerchantCityAlternateLanguageIsInvalidLength_ShouldThrowError() {
        var languageData = LanguageData()
        languageData.alternateMerchantCity = "Sing About Me, I'm Dying of Thirst"
        qrDetailsP2P.languageData = languageData

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2p(qrDetails: qrDetailsP2P)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "Sing About Me, I'm Dying of Thirst", field: QRPHField.merchantCityAlternateLanguage, bic: "SRCPPHM2XXX"))
       }
    }

    func testValidateQRPHP2m_ShouldNotThrowError() {
        XCTAssertNoThrow(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M))
    }

    func testValidateQRPHP2m_WhenAcquirerIdIsNull_ShouldThrowError() {
        qrDetailsP2M.acquirerId = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.p2mAcquirerID, bic: ""))
        }
    }

    func testValidateQRPHP2m_WhenAcquirerIdIsInvalidLength_ShouldThrowError() {
       qrDetailsP2M.acquirerId = "pgLang"

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "pgLang", field: QRPHField.p2mAcquirerID, bic: ""))
       }
    }

    func testValidateQRPHP2m_WhenPayloadFormatIndicatorIsNull_ShouldThrowError() {
        qrDetailsP2M.payloadFormatIndicator = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.payloadFormatIndicator, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenPointOfInitiationIsNull_ShouldThrowError() {
        qrDetailsP2M.pointOfInitiation = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.pointOfInitiation, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenInvalidPointOfInitiation_ShouldThrowError() {
        qrDetailsP2M.pointOfInitiation = "00"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "00", field: QRPHField.pointOfInitiation, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenGlobalUniqueIdentifierIsNull_ShouldThrowError() {
        qrDetailsP2M.globalUniqueIdentifier = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.p2mGloballyUniqueId, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenGlobalUniqueIdentifierIsInvalidLength_ShouldThrowError() {
        qrDetailsP2M.globalUniqueIdentifier = "I remember you was conflicted"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .invalidLength(value: "I remember you was conflicted", field: QRPHField.p2mGloballyUniqueId, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenMerchantIdIsInvalidLength_ShouldThrowError() {
        qrDetailsP2M.merchantId = "Misusing your influence. Sometimes I did the same"

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "Misusing your influence. Sometimes I did the same", field: QRPHField.p2mMerchantID, bic: "MBTCPHMMXXX"))
       }
    }

    func testValidateQRPHP2m_WhenMerchantIdIsNull_ShouldThrowError() {
        qrDetailsP2M.merchantId = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.p2mMerchantID, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenMerchantCreditAccountNumberIsInvalidLength_ShouldThrowError() {
        qrDetailsP2M.merchantCreditAccountNumber = "But I live in circadian rhythms of a shooting star"

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "But I live in circadian rhythms of a shooting star", field: QRPHField.p2mMerchantCreditAccount, bic: "MBTCPHMMXXX"))
       }
    }

    func testValidateQRPHP2m_WhenProxyNotifFlagIsNull_ShouldThrowError() {
        qrDetailsP2M.proxyNotifFlag = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.p2mProxyNotifFlag, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenProxyNotifFlagIsInvalidLength_ShouldThrowError() {
        qrDetailsP2M.proxyNotifFlag = "6969"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .invalidLength(value: "6969", field: QRPHField.p2mProxyNotifFlag, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenMerchantCategoryCodeIsNull_ShouldThrowError() {
        qrDetailsP2M.merchantCategoryCode = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
          XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.merchantCategoryCode, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenInvalidMerchantCategoryCode_ShouldThrowError() {
        qrDetailsP2M.merchantCategoryCode = "GNX"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .invalidLength(value: "GNX", field: QRPHField.merchantCategoryCode, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenTransactionCurrencyCodeIsNull_ShouldThrowError() {
        qrDetailsP2M.transactionCurrencyCode = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
          XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.transactionCurrencyCode, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenInvalidTransactionCurrencyCode_ShouldThrowError() {
        qrDetailsP2M.transactionCurrencyCode = "XXX."

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "XXX.", field: QRPHField.transactionCurrencyCode, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenStaticQR_WhenInvalidTransactionAmount_ShouldThrowError() {
        qrDetailsP2M.transactionAmount = "50.00"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "50.00", field: QRPHField.transactionAmount, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenDynamicQR_WhenTransactionAmountIsNull_ShouldThrowError() {
        qrDetailsP2M.pointOfInitiation = "12"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
          XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.transactionAmount, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenInvalidConvenienceIndicator_ShouldThrowError() {
        qrDetailsP2M.convenienceIndicator = "777"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "777", field: QRPHField.convenienceIndicator, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenConvenienceFeeFixedIndicatorIsNull_ShouldThrowError() {
        qrDetailsP2M.convenienceIndicator = "02"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.convenienceFeeFixed, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenConvenienceFeePercentageIndicatorIsNull_ShouldThrowError() {
        qrDetailsP2M.convenienceIndicator = "03"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.convenienceFeePercentage, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenMerchantCountryCodeIsNull_ShouldThrowError() {
        qrDetailsP2M.merchantCountryCode = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
          XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.countryCode, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenInvalidMerchantCountryCode_ShouldThrowError() {
        qrDetailsP2M.merchantCountryCode = "USA"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "USA", field: QRPHField.countryCode, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenMerchantNameIsNull_ShouldThrowError() {
        qrDetailsP2M.merchantName = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
          XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.merchantName, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenInvalidMerchantName_ShouldThrowError() {
        qrDetailsP2M.merchantName = "Café Kafee"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "Café Kafee", field: QRPHField.merchantName, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenMerchantCityIsNull_ShouldThrowError() {
        qrDetailsP2M.merchantCity = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
          XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.merchantCity, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenInvalidMerchantCity_ShouldThrowError() {
        qrDetailsP2M.merchantCity = "Ägypten"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "Ägypten", field: QRPHField.merchantCity, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenPostalCodeIsInvalidLength_ShouldThrowError() {
        qrDetailsP2M.merchantPostalCode = "69"

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "69", field: QRPHField.postalCode, bic: "MBTCPHMMXXX"))
       }
    }

    func testValidateQRPHP2m_WhenAdditionalDataGlobalUniqueIdentifierIsNull_ShouldThrowError() {
        qrDetailsP2M.additionalDataPayload = AdditionalData()
        qrDetailsP2M.additionalData?.globalUniqueIdentifier = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.p2mAdditionalDataGloballyUniqueId, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenAdditionalDataGlobalUniqueIdentifierIsInvalidLength_ShouldThrowError() {
        qrDetailsP2M.additionalDataPayload = AdditionalData()
        qrDetailsP2M.additionalData?.globalUniqueIdentifier = "We Cry Together"

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "We Cry Together", field: QRPHField.p2mAdditionalDataGloballyUniqueId, bic: "MBTCPHMMXXX"))
       }
    }

    func testValidateQRPHP2m_WhenInvalidAdditionalDataMobileNumber_ShouldThrowError() {
        qrDetailsP2M.additionalDataPayload = AdditionalData()
        qrDetailsP2M.additionalData?.additionalMobileNumber = "+********"

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
           XCTAssertEqual(error as? QRError, .invalidValue(value: "+********", field: QRPHField.mobileNumber, bic: "MBTCPHMMXXX"))
       }
    }

    func testValidateQRPHP2m_WhenAdditionalDataMobileNumberIsValid_ShouldNotThrowError() {
        qrDetailsP2M.additionalDataPayload = AdditionalData()
        qrDetailsP2M.additionalData?.additionalMobileNumber = "+639-*********"
        XCTAssertNoThrow(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M))
    }

    func testValidateQRPHP2m_WhenAdditionalDataMobileNumberPlaceholderIsValid_ShouldNotThrowError() {
        qrDetailsP2M.additionalDataPayload = AdditionalData()
        qrDetailsP2M.additionalData?.additionalMobileNumber = "***"
        XCTAssertNoThrow(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M))
    }

    func testValidateQRPHP2m_WhenInvalidStoreName_ShouldThrowError() {
        qrDetailsP2M.additionalDataPayload = AdditionalData()
        qrDetailsP2M.additionalData?.storeName = "Café"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "Café", field: QRPHField.storeLabel, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenStoreNameIsInvalidLength_ShouldThrowError() {
        qrDetailsP2M.additionalDataPayload = AdditionalData()
        qrDetailsP2M.additionalData?.storeName = "Wacced Out Murals"

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "Wacced Out Murals", field: QRPHField.storeLabel, bic: "MBTCPHMMXXX"))
       }
    }

    func testValidateQRPHP2m_WhenInvalidReferenceId_ShouldThrowError() {
        qrDetailsP2M.additionalDataPayload = AdditionalData()
        qrDetailsP2M.additionalData?.referenceId = "123456ū"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "123456ū", field: QRPHField.referenceLabel, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenReferenceIdIsInvalidLength_ShouldThrowError() {
        qrDetailsP2M.additionalDataPayload = AdditionalData()
        qrDetailsP2M.additionalData?.referenceId = "But don't tell no lie about me"

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "But don't tell no lie about me", field: QRPHField.referenceLabel, bic: "MBTCPHMMXXX"))
       }
    }

    func testValidateQRPHP2m_WhenInvalidTerminalId_ShouldThrowError() {
        qrDetailsP2M.additionalDataPayload = AdditionalData()
        qrDetailsP2M.additionalData?.terminalId = "Termińäl"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "Termińäl", field: QRPHField.terminalLabel, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenTerminalIdIsInvalidLength_ShouldThrowError() {
        qrDetailsP2M.additionalDataPayload = AdditionalData()
        qrDetailsP2M.additionalData?.terminalId = "BLOOD."

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .invalidLength(value: "BLOOD.", field: QRPHField.terminalLabel, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenPurposeIsInvalidLength_ShouldThrowError() {
        qrDetailsP2M.additionalDataPayload = AdditionalData()
        qrDetailsP2M.additionalData?.purpose = "HUMBLE."

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "HUMBLE.", field: QRPHField.purpose, bic: "MBTCPHMMXXX"))
       }
    }

    func testValidateQRPHP2m_WhenCRCIsNull_ShouldThrowError() {
        qrDetailsP2M.merchantCrc = nil

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .missingRequiredField(field: QRPHField.crc, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenInvalidCRC_ShouldThrowError() {
        qrDetailsP2M.merchantCrc = "8aaa"

        XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
            XCTAssertEqual(error as? QRError, .invalidValue(value: "8aaa", field: QRPHField.crc, bic: "MBTCPHMMXXX"))
        }
    }

    func testValidateQRPHP2m_WhenLanguagePreferenceIsInvalidLength_ShouldThrowError() {
        var languageData = LanguageData()
        languageData.languagePreference = "USA"
        qrDetailsP2M.languageData = languageData

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "USA", field: QRPHField.languagePreference, bic: "MBTCPHMMXXX"))
       }
    }

    func testValidateQRPHP2m_WhebMerchantNameAlternateLanguageIsInvalidLength_ShouldThrowError() {
        var languageData = LanguageData()
        languageData.languagePreference = "PH"
        languageData.alternateMerchantName = "Mr. Morale & the Big Steppers"
        qrDetailsP2M.languageData = languageData

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "Mr. Morale & the Big Steppers", field: QRPHField.merchantNameAlternateLanguage, bic: "MBTCPHMMXXX"))
       }
    }

    func testValidateQRPHP2m_WhenMerchantCityAlternateLanguageIsInvalidLength_ShouldThrowError() {
        var languageData = LanguageData()
        languageData.languagePreference = "PH"
        languageData.alternateMerchantName = "SAMPLE NAME"
        languageData.alternateMerchantCity = "Sing About Me, I'm Dying of Thirst"
        qrDetailsP2M.languageData = languageData

       XCTAssertThrowsError(try qrValidationUtility.validateQRPHP2m(qrDetails: qrDetailsP2M)) { error in
           XCTAssertEqual(error as? QRError, .invalidLength(value: "Sing About Me, I'm Dying of Thirst", field: QRPHField.merchantCityAlternateLanguage, bic: "MBTCPHMMXXX"))
       }
    }
}
