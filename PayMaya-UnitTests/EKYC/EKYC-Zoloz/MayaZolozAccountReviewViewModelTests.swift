//
//  MayaZolozAccountReviewViewModelTests.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON><PERSON> on 14/09/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import Cuckoo
import Foundation
import MayaCoreData
import ReactiveSwift
import StoreProvider
import XCTest

class MayaZolozAccountReviewViewModelTests: ViewModelTest {
    typealias Localizable = L10n.EkycZoloz.AccountReview.Sections

    func testCreatingViewModelSections() {
        stubData()
        let sut = MayaZolozAccountReviewViewModel()
        let expectation = expectation(description: "Data loaded")

        sut.loadData.completed.signal.observeValues {
            expectation.fulfill()
        }

        sut.loadData.apply().start()
        wait(for: [expectation], timeout: 1.0)

        XCTAssert(sut.sections.count == 4)
        XCTAssert(sut.sections[0].fields.count == 4)
        XCTAssertEqual(sut.sections[0].title, Localizable.PersonalInformation.title)
        XCTAssert(sut.sections[1].fields.count == 3)
        XCTAssertEqual(sut.sections[1].title, Localizable.WorkInformation.title)
        XCTAssert(sut.sections[2].fields.count == 2)
        XCTAssertEqual(sut.sections[2].title, Localizable.Address.title)
        XCTAssert(sut.sections[3].fields.count == 3)
        XCTAssertEqual(sut.sections[3].title, Localizable.Identification.title)
    }

    func testMappingCountryCodes() {
        stubData(countryCode: "TestCountry")
        let expectation = expectation(description: "Data loaded")

        let sut = MayaZolozAccountReviewViewModel()

        sut.loadData.completed.signal.observeValues {
            expectation.fulfill()
        }

        sut.loadData.apply().start()
        wait(for: [expectation], timeout: 1.0)

        XCTAssertEqual(sut.sections[0].fields.textValueForField(withLabel: Localizable.PersonalInformation.nationality), "SampleCountry")
    }

    func testMappingNonExistingCountryCodes() {
        stubData(countryCode: "NotMappedCountry")
        let expectation = expectation(description: "Data loaded")

        let sut = MayaZolozAccountReviewViewModel()

        sut.loadData.completed.signal.observeValues {
            expectation.fulfill()
        }

        sut.loadData.apply().start()
        wait(for: [expectation], timeout: 1.0)
        XCTAssertEqual(sut.sections[0].fields.textValueForField(withLabel: Localizable.PersonalInformation.nationality), "NotMappedCountry")
    }

    func testMappingDocumentTypes() {
        stubData(documentType: "DocumentType")
        let expectation = expectation(description: "Data loaded")
        let sut = MayaZolozAccountReviewViewModel()

        sut.loadData.completed.signal.observeValues {
            expectation.fulfill()
        }

        sut.loadData.apply().start()
        wait(for: [expectation], timeout: 1.0)

        XCTAssertEqual(sut.sections[3].fields.textValueForField(withLabel: Localizable.Identification.type), "Valid document type")
    }

    func testMappingInvalidDocumentType() {
        stubData(documentType: "Invalid document type")
        let expectation = expectation(description: "Data loaded")
        let sut = MayaZolozAccountReviewViewModel()

        sut.loadData.completed.signal.observeValues {
            expectation.fulfill()
        }

        sut.loadData.apply().start()
        wait(for: [expectation], timeout: 1.0)
        XCTAssertEqual(sut.sections[3].fields.textValueForField(withLabel: Localizable.Identification.type), "Invalid document type")
    }

    func testMappingRecommendedDocumentTypes() {
        stub(configurationService) { stub in
            when(stub.shortenedFlowUpgradeEnabled.get).thenReturn(trueConfig())
            when(stub.shortenedFlowReKYCEnabled.get).thenReturn(trueConfig())
            when(stub.secondaryIDFlowEnabled.get).thenReturn(trueConfig())
        }

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(getUser()))
        }

        stubDataShortenedFlow(documentType: "RecommendedDocumentType")
        let expectation = expectation(description: "Data loaded")
        let sut = MayaZolozAccountReviewViewModel()

        sut.loadData.completed.signal.observeValues {
            expectation.fulfill()
        }

        sut.loadData.apply().start()
        wait(for: [expectation], timeout: 1.0)

        XCTAssertEqual(sut.sections[3].fields.textValueForField(withLabel: Localizable.Identification.type), "Valid recommended document type")
    }

    func testMappingOtherPrimaryDocumentTypes() {
        stub(configurationService) { stub in
            when(stub.shortenedFlowUpgradeEnabled.get).thenReturn(trueConfig())
            when(stub.shortenedFlowReKYCEnabled.get).thenReturn(trueConfig())
            when(stub.secondaryIDFlowEnabled.get).thenReturn(trueConfig())
        }

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(getUser()))
        }

        stubDataShortenedFlow(documentType: "OtherPrimaryDocumentType")
        let expectation = expectation(description: "Data loaded")
        let sut = MayaZolozAccountReviewViewModel()

        sut.loadData.completed.signal.observeValues {
            expectation.fulfill()
        }

        sut.loadData.apply().start()
        wait(for: [expectation], timeout: 1.0)

        XCTAssertEqual(sut.sections[3].fields.textValueForField(withLabel: Localizable.Identification.type), "Valid other primary document type")
    }

    func testMappingSecondaryDocumentTypes() {
        stub(configurationService) { stub in
            when(stub.shortenedFlowUpgradeEnabled.get).thenReturn(trueConfig())
            when(stub.shortenedFlowReKYCEnabled.get).thenReturn(trueConfig())
            when(stub.secondaryIDFlowEnabled.get).thenReturn(trueConfig())
        }

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(getUser()))
        }

        stubDataShortenedFlow(documentType: "SecondaryDocumentType")
        let expectation = expectation(description: "Data loaded")
        let sut = MayaZolozAccountReviewViewModel()

        sut.loadData.completed.signal.observeValues {
            expectation.fulfill()
        }

        sut.loadData.apply().start()
        wait(for: [expectation], timeout: 1.0)

        XCTAssertEqual(sut.sections[3].fields.textValueForField(withLabel: Localizable.Identification.type), "Valid secondary document type")
    }

    func testMappingTextValues() {
        stubData(expiryDate: "2022-10-20")
        let expectation = expectation(description: "Data loaded")
        let sut = MayaZolozAccountReviewViewModel()

        sut.loadData.completed.signal.observeValues {
            expectation.fulfill()
        }

        sut.loadData.apply().start()

        wait(for: [expectation], timeout: 1.0)

        XCTAssertEqual(sut.sections[0].fields.textValueForField(withLabel: Localizable.PersonalInformation.fullName), "FirstName MiddleName LastName")
        XCTAssertEqual(sut.sections[2].fields.textValueForField(withLabel: Localizable.Address.present), "Line1, Line2, Locality, State, City, ZipCode, SampleCountry")
        XCTAssertEqual(sut.sections[1].fields.textValueForField(withLabel: Localizable.WorkInformation.nameOfCompany), "EmploymentDetail")
        XCTAssertEqual(sut.sections[3].fields.textValueForField(withLabel: Localizable.Identification.expirationDate), "10/20/2022")
    }

    func testMappingOptionalTextValues() {
        let expectation = expectation(description: "Data loaded")
        let sut = MayaZolozAccountReviewViewModel()
        stubData(middleName: nil, employmentDetail: nil, expiryDate: nil)

        sut.loadData.completed.signal.observeValues {
            expectation.fulfill()
        }

        sut.loadData.apply().start()

        wait(for: [expectation], timeout: 1.0)

        XCTAssertEqual(sut.sections[0].fields.textValueForField(withLabel: Localizable.PersonalInformation.fullName), "FirstName LastName")
        XCTAssertNil(sut.sections[1].fields.textValueForField(withLabel: Localizable.WorkInformation.nameOfCompany))
        XCTAssertNil(sut.sections[3].fields.textValueForField(withLabel: Localizable.Identification.expirationDate))
    }
}

private extension Array where Element == MayaZolozAccountReviewViewModel.Field {
    func textValueForField(withLabel label: String) -> String? {
        for field in self {
            if case .text(let title, let value) = field, title == label {
                return value
            }
        }

        return nil
    }
}

private extension MayaZolozAccountReviewViewModelTests {
    func stubData(middleName: String? = "MiddleName", countryCode: String = "TestCountry", employmentDetail: String? = "EmploymentDetail", expiryDate: String? = "2000-01-01", documentType: String = "DocumentType") {
        let kycData = KYCData(benefits: [],
                              documents: [.init(key: "DocumentType",
                                                description: "Valid document type",
                                                additionalDescription: nil,
                                                order: 0,
                                                hasExpiryDate: false,
                                                isShownOnList: false,
                                                category: nil,
                                                subHeader: nil)],
                              photoTips: [],
                              livenessTips: [],
                              submit: .init(info: "",
                                            header: "",
                                            highlightedPhrase: nil),
                              review: .init(info: "InfoText",
                                            customerServiceUrlString: "url"),
                              countries: [.init(id: 0,
                                                name: "SampleCountry",
                                                code: "TestCountry")],
                              nationalities: [],
                              workNatures: [],
                              incomeSources: [],
                              validationRules: [],
                              additionalDocuments: .init(consentURLString: ""),
                              nameRule: .init(id: 1,
                                              header: "",
                                              rules: ""),
                              recommendedDocuments: nil,
                              otherPrimaryDocuments: nil,
                              secondaryDocuments: nil,
                              captureGuides: nil,
                              livePhotoTips: nil,
                              imageQualityGuides: nil,
                              imageQualityMaxRetries: 2,
                              uploadGuides: nil,
                              intro: nil,
                              selfieTips: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.ShortenedKYC.kycCmsDataV6), type: isEqualIdentity(to: KYCDataWrapper.self))).thenReturn(getSuccessProducer(KYCDataWrapper(data: kycData)))
            when(stub.request(isEqual(to: API.ShortenedKYC.kycCmsDataV7), type: isEqualIdentity(to: KYCDataWrapper.self))).thenReturn(getSuccessProducer(KYCDataWrapper(data: kycData)))
        }

        let address = MayaZolozSubmissionResponse.Address(country: countryCode, city: "City", state: "State", locality: "Locality", line1: "Line1", line2: "Line2", zipCode: "ZipCode")
        let submissionData = MayaZolozSubmissionResponse(
            idNumber: "id",
            referenceId: "reference",
            birthPlace: .init(city: "TestCity", country: countryCode),
            nationality: countryCode,
            firstName: "FirstName",
            middleName: middleName,
            lastName: "LastName",
            birthDateString: "0001-01-01",
            presentAddress: address,
            permanentAddress: address,
            incomeSource: "IncomeSource",
            workNature: "WorkNature",
            employmentDetail: employmentDetail,
            liveness: .init(base64: nil),
            ids: [.init(type: documentType, file: .init(base64: nil), number: "Number", expiryDateString: expiryDate)],
            additionalDocs: nil
        )
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.ZolozKYC.submission), type: isEqualIdentity(to: MayaZolozSubmissionResponse.self))).thenReturn(getSuccessProducer(submissionData))
        }
    }

    func stubDataShortenedFlow(middleName: String? = "MiddleName", countryCode: String = "TestCountry", employmentDetail: String? = "EmploymentDetail", expiryDate: String? = "2000-01-01", documentType: String = "DocumentType") {
        stub(configurationService) { stub in
            when(stub.shortenedFlowUpgradeEnabled.get).thenReturn(trueConfig())
            when(stub.secondaryIDFlowEnabled.get).thenReturn(trueConfig())
        }

        let kycData = KYCData(benefits: [],
                              documents: nil,
                              photoTips: [],
                              livenessTips: [],
                              submit: .init(info: "",
                                            header: "",
                                            highlightedPhrase: nil),
                              review: .init(info: "InfoText",
                                            customerServiceUrlString: "url"),
                              countries: [.init(id: 0,
                                                name: "SampleCountry",
                                                code: "TestCountry")],
                              nationalities: [],
                              workNatures: [],
                              incomeSources: [],
                              validationRules: [],
                              additionalDocuments: .init(consentURLString: ""),
                              nameRule: .init(id: 1,
                                              header: "",
                                              rules: ""),
                              recommendedDocuments: [.init(key: "RecommendedDocumentType",
                                                description: "Valid recommended document type",
                                                additionalDescription: nil,
                                                order: 0,
                                                hasExpiryDate: false,
                                                isShownOnList: false,
                                                category: nil,
                                                subHeader: nil)],
                              otherPrimaryDocuments: [.init(key: "OtherPrimaryDocumentType",
                                                            description: "Valid other primary document type",
                                                            additionalDescription: nil,
                                                            order: 0,
                                                            hasExpiryDate: false,
                                                            isShownOnList: false,
                                                            category: nil,
                                                            subHeader: nil)],
                              secondaryDocuments: [.init(key: "SecondaryDocumentType",
                                                         description: "Valid secondary document type",
                                                         additionalDescription: nil,
                                                         order: 0,
                                                         hasExpiryDate: false,
                                                         isShownOnList: false,
                                                         category: nil,
                                                         subHeader: nil)],
                              captureGuides: nil,
                              livePhotoTips: nil,
                              imageQualityGuides: nil,
                              imageQualityMaxRetries: 2,
                              uploadGuides: nil,
                              intro: nil,
                              selfieTips: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.ShortenedKYC.kycCmsDataV6), type: isEqualIdentity(to: KYCDataWrapper.self))).thenReturn(getSuccessProducer(KYCDataWrapper(data: kycData)))
            when(stub.request(isEqual(to: API.ShortenedKYC.kycCmsDataV7), type: isEqualIdentity(to: KYCDataWrapper.self))).thenReturn(getSuccessProducer(KYCDataWrapper(data: kycData)))
        }

        let address = MayaZolozSubmissionResponse.Address(country: countryCode, city: "City", state: "State", locality: "Locality", line1: "Line1", line2: "Line2", zipCode: "ZipCode")
        let submissionData = MayaZolozSubmissionResponse(
            idNumber: "id",
            referenceId: "reference",
            birthPlace: .init(city: "TestCity", country: countryCode),
            nationality: countryCode,
            firstName: "FirstName",
            middleName: middleName,
            lastName: "LastName",
            birthDateString: "0001-01-01",
            presentAddress: address,
            permanentAddress: address,
            incomeSource: "IncomeSource",
            workNature: "WorkNature",
            employmentDetail: employmentDetail,
            liveness: .init(base64: nil),
            ids: [.init(type: documentType, file: .init(base64: nil), number: "Number", expiryDateString: expiryDate)],
            additionalDocs: nil
        )
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.ZolozKYC.submission), type: isEqualIdentity(to: MayaZolozSubmissionResponse.self))).thenReturn(getSuccessProducer(submissionData))
        }
    }

    func getUser() -> StubUser {
        let stubUser = StubUser()
        let stubProfile = StubProfile(birthDate: nil, firstName: "John", lastName: "Doe", middleName: nil, address: nil, user: stubUser)
        let stubMainIdentity = StubMainIdentity(type: "msisdn", value: "+************")
        let stubFund = StubFund(enabled: 1, id: "1234567-abcde", name: "virtual_card", status: FundSourceStatus.virtualCard.rawValue, type: FundSourceType.virtual.rawValue, cardProfile: StubCardProfile(), purchases: nil, user: stubProfile.user)
        return StubUser(id: "user_id", accountStatus: "ACTIVE", canBeReferred: 1, canLinkCard: 1, kycStatus: "kyc1", network: "Smart", physicalCardStatus: "activated", status: "ACTIVE", token: "1234567", hasGovID: 1, activities: nil, backupIdentity: nil, balance: nil, funds: [stubFund], identities: NSSet(array: [stubMainIdentity]), privacyPolicy: StubPrivacyPolicy(version: "v1"), profile: stubProfile, type: nil)
    }
}
