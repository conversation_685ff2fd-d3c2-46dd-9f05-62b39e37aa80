//
//  MayaPersonalInformationViewModelTests.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON> on 29/08/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Alamofire
import APIProvider
import AssetProvider
import Cuckoo
import Error
import MayaCoreData
import Moya
import ReactiveSwift
import StoreProvider
import XCTest

class MayaPersonalInformationViewModelTests: ViewModelTest {
    var user: StubUser!

    typealias ValidatingPropertyDecission = ValidatingProperty<String, FieldValidationError>.Decision
    typealias FieldValidator = (String) -> ValidatingPropertyDecission
    private var preSubmissionId = "5f8f8f8f-8f8f-8f8f-8f8f-8f8f8f8f8f8f"

    var kycAddressData: KYCAddressWrapper!

    override func setUp() {
        super.setUp()
        user = StubUser(kycLevel: "1")
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }

        kycAddressData = KYCAddressWrapper(data: [KYCAddress(id: 1,
                                                            name: "Metro Manila",
                                                            cities: [
                                                                KYCCities(id: 1,
                                                                          name: "Quezon City",
                                                                          localities: [
                                                                            KYCBarangays(id: 1,
                                                                                         name: "Commonwealth",
                                                                                         zipCodes: [
                                                                                            KYCZipCodes(id: 1,
                                                                                                        code: "1121")
                                                                                         ])
                                                                          ])
                                                            ])])
    }

    func test_formatDateMethod_returnsProperlyFormattedDate_whenUnformattedDateIsGiven() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: "5f8f8f8f-8f8f-8f8f-8f8f-8f8f8f8f8f8f",
                                                   selectedDocument: mockedSelectedDocument)

        let unformattedDateString = "04061999"
        let formattedString = sut.formatDate(text: unformattedDateString).0

        XCTAssertEqual(formattedString, "04/06/1999")
    }
}

// MARK: Tests for observing selectedNatureOfWork property
extension MayaPersonalInformationViewModelTests {
    func test_availableIncomeSources_doesNotContainProhibitedIncomeSources_whenProhibitedIncomeSourcesAreGiven() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        guard let selectedNatureOfWork = mockedWorkNatureData.first(where: { $0.id == 2 }) else { return }

        let prohibitedIncomeSources = selectedNatureOfWork.prohibitedIncomeSources
        sut.selectedNatureOfWork.value = selectedNatureOfWork

        guard let availableIncomeSources = sut.availableIncomeSources else { return }

        let containsProhibitedIncomeSources = prohibitedIncomeSources.allSatisfy({ availableIncomeSources.contains($0) })
        XCTAssertFalse(containsProhibitedIncomeSources)
    }

    func test_hasNoAvailableSourceOfIncome_isTrue_whenIncomeSourcesEqualProhibitedIncomeSources() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        guard let selectedNatureOfWork = mockedWorkNatureData.first(where: { $0.id == 2 }) else { return XCTFail("Unexpected Error") }

        sut.selectedNatureOfWork.value = selectedNatureOfWork

        let hasNoAvailableSourceOfIncome = sut.hasNoAvailableSourceOfIncome.value
        XCTAssertTrue(hasNoAvailableSourceOfIncome)
    }

    func test_hasNoAvailableSourceOfIncome_isFalse_whenIncomeSourcesNotEqualProhibitedIncomeSources() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        guard let selectedNatureOfWork = mockedWorkNatureData.first(where: { $0.id == 1 }) else { return XCTFail("Unexpected Error") }

        sut.selectedNatureOfWork.value = selectedNatureOfWork

        let hasNoAvailableSourceOfIncome = sut.hasNoAvailableSourceOfIncome.value
        XCTAssertFalse(hasNoAvailableSourceOfIncome)
    }

    func test_isNameOfCompanyHidden_isTrue_whenSelectedWorkNatureNotRequiresEmploymentDetails() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        guard let selectedNatureOfWork = mockedWorkNatureData.first(where: { $0.id == 2 }) else { return XCTFail("Unexpected Error") }

        sut.selectedNatureOfWork.value = selectedNatureOfWork

        let isNameOfCompanyHidden = sut.isNameOfCompanyHidden.value
        XCTAssertTrue(isNameOfCompanyHidden)
    }

    func test_isNameOfCompanyHidden_isFalse_whenSelectedWorkNatureRequiresEmploymentDetails() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        guard let selectedNatureOfWork = mockedWorkNatureData.first(where: { $0.id == 1 }) else { return XCTFail("Unexpected Error") }

        sut.selectedNatureOfWork.value = selectedNatureOfWork

        let isNameOfCompanyHidden = sut.isNameOfCompanyHidden.value
        XCTAssertFalse(isNameOfCompanyHidden)
    }

    // MARK: XDE
    func test_requiresEmploymentDetail_isTrue_whenSelectedWorkNatureRequiresEmploymentDetails() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        guard let selectedNatureOfWork = mockedWorkNatureData.first(where: { $0.id == 1 }) else { return XCTFail("Unexpected Error") }

        sut.selectedNatureOfWork.value = selectedNatureOfWork

        let requiresEmploymentDetail = sut.requiresEmploymentDetail.value
        XCTAssertTrue(requiresEmploymentDetail)
    }

    func test_requiresEmploymentDetail_isFalse_whenSelectedWorkNatureNotRequiresEmploymentDetails() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        guard let selectedNatureOfWork = mockedWorkNatureData.first(where: { $0.id == 2 }) else { return XCTFail("Unexpected Error") }

        sut.selectedNatureOfWork.value = selectedNatureOfWork

        let requiresEmploymentDetail = sut.requiresEmploymentDetail.value
        XCTAssertFalse(requiresEmploymentDetail)
    }
}

// MARK: Tests for getIsRequiredValidator method
extension MayaPersonalInformationViewModelTests {
    func test_getIsRequiredValidatorMethod_returnProperValidator_whenWrappeIsNotGiven() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        let isRequiredValidator = sut.exposedGetIsRequiredValidator(wrappe: nil)
        let resultForValidInput = isRequiredValidator("Not empty string")
        let resultForInvalidInput = isRequiredValidator("")

        let expectedResultForValidInput: ValidatingPropertyDecission = .valid
        let expectedResultForInvalidInput: ValidatingPropertyDecission = mockedInvalidDecisionData

        XCTAssertEqual(resultForInvalidInput, expectedResultForInvalidInput)
        XCTAssertEqual(resultForValidInput, expectedResultForValidInput)
    }

    func test_getIsRequiredValidatorMethod_returnProperValidator_whenValidDecissionWrappeIsGiven() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        let wrappeValidator: FieldValidator = { _ in .valid }
        let isRequiredValidator = sut.exposedGetIsRequiredValidator(wrappe: wrappeValidator)

        let resultForValidInput = isRequiredValidator("Not empty string")
        let resultForInvalidInput = isRequiredValidator("")

        let expectedResultForValidInput: ValidatingPropertyDecission = .valid
        let expectedResultForInvalidInput: ValidatingPropertyDecission = mockedInvalidDecisionData

        XCTAssertEqual(resultForInvalidInput, expectedResultForInvalidInput)
        XCTAssertEqual(resultForValidInput, expectedResultForValidInput)
    }

    func test_getIsRequiredValidatorMethod_returnProperValidator_whenInvalidDecissionWrappeIsGiven() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        let wrappeValidator: FieldValidator = { [weak self] _ in
            guard let self = self else { return .valid }
            return self.mockedInvalidDecisionData
        }
        let isRequiredValidator = sut.exposedGetIsRequiredValidator(wrappe: wrappeValidator)

        let resultForValidInput = isRequiredValidator("Not empty string")
        let resultForInvalidInput = isRequiredValidator("")

        let expectedResultForValidInput: ValidatingPropertyDecission = mockedInvalidDecisionData
        let expectedResultForInvalidInput: ValidatingPropertyDecission = mockedInvalidDecisionData

        XCTAssertEqual(resultForInvalidInput, expectedResultForInvalidInput)
        XCTAssertEqual(resultForValidInput, expectedResultForValidInput)
    }
}

// MARK: Tests for getLimitedCharactersValidator method
extension MayaPersonalInformationViewModelTests {
    func test_getLimitedCharactersValidatorMethod_returnProperValidator_whenWrappeIsNotGiven() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        let limitedCharacterValidator = sut.exposedGetLimitedCharactersValidator(characterLimit: 15)
        let resultForValidInput = limitedCharacterValidator("Valid input")
        let resultForInvalidInput = limitedCharacterValidator("To long invalid input text")

        let expectedResultForValidInput: ValidatingPropertyDecission = .valid
        let expectedResultForInvalidInput: ValidatingPropertyDecission = mockedInvalidDecisionData

        XCTAssertEqual(resultForValidInput, expectedResultForValidInput)
        XCTAssertEqual(resultForInvalidInput, expectedResultForInvalidInput)
    }

    func test_getLimitedCharactersValidatorMethod_returnProperValidator_whenValidDecissionWrappeIsGiven() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        let wrappeValidator: FieldValidator = { _ in .valid }
        let limitedCharacterValidator = sut.exposedGetLimitedCharactersValidator(characterLimit: 15, wrappe: wrappeValidator)
        let resultForValidInput = limitedCharacterValidator("Valid input")
        let resultForInvalidInput = limitedCharacterValidator("To long invalid input text")

        let expectedResultForValidInput: ValidatingPropertyDecission = .valid
        let expectedResultForInvalidInput: ValidatingPropertyDecission = mockedInvalidDecisionData

        XCTAssertEqual(resultForValidInput, expectedResultForValidInput)
        XCTAssertEqual(resultForInvalidInput, expectedResultForInvalidInput)
    }

    func test_getLimitedCharactersValidatorMethod_returnProperValidator_whenInvalidDecissionWrappeIsGiven() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        let wrappeValidator: FieldValidator = { _ in .valid }
        let limitedCharacterValidator = sut.exposedGetLimitedCharactersValidator(characterLimit: 15, wrappe: wrappeValidator)
        let resultForValidInput = limitedCharacterValidator("Valid input")
        let resultForInvalidInput = limitedCharacterValidator("To long invalid input text")

        let expectedResultForValidInput: ValidatingPropertyDecission = .valid
        let expectedResultForInvalidInput: ValidatingPropertyDecission = mockedInvalidDecisionData

        XCTAssertEqual(resultForValidInput, expectedResultForValidInput)
        XCTAssertEqual(resultForInvalidInput, expectedResultForInvalidInput)
    }
}

// MARK: Tests for getRegexValidator method with pattern
extension MayaPersonalInformationViewModelTests {
    func test_getRegexValidatorMethod_returnProperValidator_whenPatternIsGivenAndWrappeNot() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        let regexValidator = sut.exposedGetRegexValidator(pattern: "^[a-zA-Z ]*$", validationErrorMessage: "Invalid format")
        let resultForValidInput = regexValidator("Some latters")
        let resultForInvalidInput = regexValidator("Som3 l3tter5 and num8ers")

        let expectedResultForValidInput: ValidatingPropertyDecission = .valid
        let expectedResultForInvalidInput: ValidatingPropertyDecission = mockedInvalidDecisionData

        XCTAssertEqual(resultForValidInput, expectedResultForValidInput)
        XCTAssertEqual(resultForInvalidInput, expectedResultForInvalidInput)
    }

    func test_getRegexValidatorMethod_returnProperValidator_whenPatternAndValidDecissionWrappeAreGiven() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        let wrappeValidator: FieldValidator = { _ in .valid }
        let regexValidator = sut.exposedGetRegexValidator(pattern: "^[a-zA-Z ]*$", validationErrorMessage: "Invalid format", wrappe: wrappeValidator)
        let resultForValidInput = regexValidator("Some latters")
        let resultForInvalidInput = regexValidator("Som3 l3tter5 and num8ers")

        let expectedResultForValidInput: ValidatingPropertyDecission = .valid
        let expectedResultForInvalidInput: ValidatingPropertyDecission = mockedInvalidDecisionData

        XCTAssertEqual(resultForValidInput, expectedResultForValidInput)
        XCTAssertEqual(resultForInvalidInput, expectedResultForInvalidInput)
    }

    func test_getRegexValidatorMethod_returnProperValidator_whenPatternAndInvalidDecissionWrappeAreGiven() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        let wrappeValidator: FieldValidator = { [weak self] _ in
            guard let self = self else { return .valid }
            return self.mockedInvalidDecisionData
        }
        let regexValidator = sut.exposedGetRegexValidator(pattern: "^[a-zA-Z ]*$", validationErrorMessage: "Invalid format", wrappe: wrappeValidator)
        let resultForValidInput = regexValidator("Some latters")
        let resultForInvalidInput = regexValidator("Som3 l3tter5 and num8ers")

        let expectedResultForValidInput: ValidatingPropertyDecission = mockedInvalidDecisionData
        let expectedResultForInvalidInput: ValidatingPropertyDecission = mockedInvalidDecisionData

        XCTAssertEqual(resultForValidInput, expectedResultForValidInput)
        XCTAssertEqual(resultForInvalidInput, expectedResultForInvalidInput)
    }
}

// MARK: Tests for getRegexValidator method with fieldName
extension MayaPersonalInformationViewModelTests {
    func test_getRegexValidatorMethod_returnProperValidator_whenFiledNameIsGivenAndWrappeNot() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        let regexValidator = sut.exposedGetRegexValidator(fieldName: "firstName")
        let resultForValidInput = regexValidator("Gregory")
        let resultForInvalidInput = regexValidator("Brzęczy5zczyki3wicz")

        let expectedResultForValidInput: ValidatingPropertyDecission = .valid
        let expectedResultForInvalidInput: ValidatingPropertyDecission = mockedInvalidDecisionData

        XCTAssertEqual(resultForValidInput, expectedResultForValidInput)
        XCTAssertEqual(resultForInvalidInput, expectedResultForInvalidInput)
    }

    func test_getRegexValidatorMethod_returnProperValidator_whenFiledNameAndValidDecissionWrappeAreGiven() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        let wrappeValidator: FieldValidator = { _ in .valid }
        let regexValidator = sut.exposedGetRegexValidator(fieldName: "firstName", wrappe: wrappeValidator)
        let resultForValidInput = regexValidator("Gregory")
        let resultForInvalidInput = regexValidator("Brzęczy5zczyki3wicz")

        let expectedResultForValidInput: ValidatingPropertyDecission = .valid
        let expectedResultForInvalidInput: ValidatingPropertyDecission = mockedInvalidDecisionData

        XCTAssertEqual(resultForValidInput, expectedResultForValidInput)
        XCTAssertEqual(resultForInvalidInput, expectedResultForInvalidInput)
    }

    func test_getRegexValidatorMethod_returnProperValidator_whenFiledNameAndInvalidDecissionWrappeAreGiven() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        let wrappeValidator: FieldValidator = { _ in .valid }
        let regexValidator = sut.exposedGetRegexValidator(fieldName: "firstName", wrappe: wrappeValidator)
        let resultForValidInput = regexValidator("Gregory")
        let resultForInvalidInput = regexValidator("Brzęczy5zczyki3wicz")

        let expectedResultForValidInput: ValidatingPropertyDecission = .valid
        let expectedResultForInvalidInput: ValidatingPropertyDecission = mockedInvalidDecisionData

        XCTAssertEqual(resultForValidInput, expectedResultForValidInput)
        XCTAssertEqual(resultForInvalidInput, expectedResultForInvalidInput)
    }
}

// MARK: Tests for getThreeLettersValidator method
extension MayaPersonalInformationViewModelTests {
    func test_getThreeLettersValidatorMethod_returnProperValidator_whenWrappeIsNotGiven() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        let threeLettersValidator = sut.exposedGetThreeLettersValidator(wrappe: nil)
        let resultForValidInput = threeLettersValidator("Josephin")
        let resultForInvalidInput = threeLettersValidator("Jooosephin")

        let expectedResultForValidInput: ValidatingPropertyDecission = .valid
        let expectedResultForInvalidInput: ValidatingPropertyDecission = mockedInvalidDecisionData

        XCTAssertEqual(resultForInvalidInput, expectedResultForInvalidInput)
        XCTAssertEqual(resultForValidInput, expectedResultForValidInput)
    }

    func test_getThreeLettersValidatorMethod_returnProperValidator_whenValidDecissionWrappeIsGiven() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        let wrappeValidator: FieldValidator = { _ in .valid }
        let threeLettersValidator = sut.exposedGetThreeLettersValidator(wrappe: wrappeValidator)

        let resultForValidInput = threeLettersValidator("Josephin")
        let resultForInvalidInput = threeLettersValidator("Jooosephin")

        let expectedResultForValidInput: ValidatingPropertyDecission = .valid
        let expectedResultForInvalidInput: ValidatingPropertyDecission = mockedInvalidDecisionData

        XCTAssertEqual(resultForInvalidInput, expectedResultForInvalidInput)
        XCTAssertEqual(resultForValidInput, expectedResultForValidInput)
    }

    func test_getThreeLettersValidatorMethod_returnProperValidator_whenInvalidDecissionWrappeIsGiven() {
        let sut = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                   provinces: mockedProvienceData,
                                                   preSubmissionId: preSubmissionId,
                                                   selectedDocument: mockedSelectedDocument)

        let wrappeValidator: FieldValidator = { [weak self] _ in
            guard let self = self else { return .valid }
            return self.mockedInvalidDecisionData
        }
        let threeLettersValidator = sut.exposedGetThreeLettersValidator(wrappe: wrappeValidator)

        let resultForValidInput = threeLettersValidator("Josephin")
        let resultForInvalidInput = threeLettersValidator("Jooosephin")

        let expectedResultForValidInput: ValidatingPropertyDecission = mockedInvalidDecisionData
        let expectedResultForInvalidInput: ValidatingPropertyDecission = mockedInvalidDecisionData

        XCTAssertEqual(resultForInvalidInput, expectedResultForInvalidInput)
        XCTAssertEqual(resultForValidInput, expectedResultForValidInput)
    }
}

// MARK: Tests for idDocuments initialization
extension MayaPersonalInformationViewModelTests {
    func testIdDocumentsWhenShortenedFlowUpgradeIsEnabledForKYC0() {
        user.kycLevel = "0"
        XCTAssertEqual(user.kycLevel, KYCLevel.zero.rawValue)

        stub(configurationService) { stub in
            when(stub.shortenedFlowReKYCEnabled.get).thenReturn(falseConfig())
            when(stub.shortenedFlowUpgradeEnabled.get).thenReturn(trueConfig())
        }
        // Use mocked data for new CMS enpoint response
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCDataNewCms,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)

        XCTAssertEqual(viewModel.idDocuments.count, 6)
    }

    func testIdDocumentsWhenShortenedFlowReKYCIsEnabledForKYC0() {
        user.kycLevel = "0"
        XCTAssertEqual(user.kycLevel, KYCLevel.zero.rawValue)

        stub(configurationService) { stub in
            when(stub.shortenedFlowReKYCEnabled.get).thenReturn(trueConfig())
            when(stub.shortenedFlowUpgradeEnabled.get).thenReturn(falseConfig())
        }
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)

        XCTAssertEqual(viewModel.idDocuments.count, 2)
    }

    func testIdDocumentsWhenBothShortenedFlowAreEnabledForKYC0() {
        user.kycLevel = "0"
        XCTAssertEqual(user.kycLevel, KYCLevel.zero.rawValue)

        stub(configurationService) { stub in
            when(stub.shortenedFlowReKYCEnabled.get).thenReturn(trueConfig())
            when(stub.shortenedFlowUpgradeEnabled.get).thenReturn(trueConfig())
        }
        // Use mocked data for new CMS enpoint response
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCDataNewCms,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)

        XCTAssertEqual(viewModel.idDocuments.count, 6)
    }

    func testIdDocumentsWhenBothShortenedFlowAreDisabledForKYC0() {
        user.kycLevel = "0"
        XCTAssertEqual(user.kycLevel, KYCLevel.zero.rawValue)

        stub(configurationService) { stub in
            when(stub.shortenedFlowReKYCEnabled.get).thenReturn(falseConfig())
            when(stub.shortenedFlowUpgradeEnabled.get).thenReturn(falseConfig())
        }
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)

        XCTAssertEqual(viewModel.idDocuments.count, 2)
    }

    func testIdDocumentsWhenShortenedFlowUpgradeIsEnabledForKYC1() {
        user.kycLevel = "1"
        XCTAssertEqual(user.kycLevel, KYCLevel.one.rawValue)

        stub(configurationService) { stub in
            when(stub.shortenedFlowReKYCEnabled.get).thenReturn(falseConfig())
            when(stub.shortenedFlowUpgradeEnabled.get).thenReturn(trueConfig())
        }
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)

        XCTAssertEqual(viewModel.idDocuments.count, 2)
    }

    func testIdDocumentsWhenShortenedFlowReKYCIsEnabledForKYC1() {
        user.kycLevel = "1"
        XCTAssertEqual(user.kycLevel, KYCLevel.one.rawValue)

        stub(configurationService) { stub in
            when(stub.shortenedFlowReKYCEnabled.get).thenReturn(trueConfig())
            when(stub.shortenedFlowUpgradeEnabled.get).thenReturn(falseConfig())
        }
        // Use mocked data for new CMS enpoint response
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCDataNewCms,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)

        XCTAssertEqual(viewModel.idDocuments.count, 6)
    }

    func testIdDocumentsWhenBothShortenedFlowAreEnabledForKYC1() {
        user.kycLevel = "1"
        XCTAssertEqual(user.kycLevel, KYCLevel.one.rawValue)

        stub(configurationService) { stub in
            when(stub.shortenedFlowReKYCEnabled.get).thenReturn(trueConfig())
            when(stub.shortenedFlowUpgradeEnabled.get).thenReturn(trueConfig())
        }
        // Use mocked data for new CMS enpoint response
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCDataNewCms,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)

        XCTAssertEqual(viewModel.idDocuments.count, 6)
    }

    func testIdDocumentsWhenBothShortenedFlowAreDisabledForKYC1() {
        user.kycLevel = "1"
        XCTAssertEqual(user.kycLevel, KYCLevel.one.rawValue)

        stub(configurationService) { stub in
            when(stub.shortenedFlowReKYCEnabled.get).thenReturn(falseConfig())
            when(stub.shortenedFlowUpgradeEnabled.get).thenReturn(falseConfig())
        }
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)

        XCTAssertEqual(viewModel.idDocuments.count, 2)
    }
}

// MARK: Tests for Address Dropdown API
extension MayaPersonalInformationViewModelTests {
    func testPresentCitiesSuccess() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)
        viewModel.selectedCurrentAddressState.value = KYCAddress(id: 1, name: "Test Province", cities: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual( to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))).thenReturn(getSuccessProducer(kycAddressData))
        }

        viewModel.getPresentCities.errors.observeValues { error in
            XCTAssertNil(error, "Unexpected Error encountered")
        }

        viewModel.getPresentCities.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))
    }

    func testGetPresentCitiesError() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)
        viewModel.selectedCurrentAddressState.value = KYCAddress(id: 1, name: "Test Province", cities: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual( to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))).thenReturn(getBackendError())
        }

        viewModel.getPresentCities.errors.observeValues { error in
            XCTAssertEqual(error, self.getPayMayaError(CommonStrings.Maya.Error.Default.title))
        }

        viewModel.getPresentCities.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))
    }

    func testGetPresentCitiesUnknownError() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)
        viewModel.selectedCurrentAddressState.value = KYCAddress(id: 1, name: "Test Province", cities: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual( to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))).thenReturn(getUnknownError())
        }

        viewModel.getPresentCities.errors.observeValues { error in
            XCTAssertEqual(error, self.getPayMayaUnknownError(CommonStrings.Maya.Error.Default.title))
        }

        viewModel.getPresentCities.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))
    }

    func testPresentBarangayAndZipCodeSuccess() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)
        viewModel.selectedCurrentAddressState.value = KYCAddress(id: 1, name: "Test Province", cities: nil)
        viewModel.selectedCurrentAddressCity.value = KYCCities(id: 1, name: "Test City", localities: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual( to: API.ZolozKYC.getBarangayAndZipCode(province: "Test Province", city: "Test City")), type: isEqualIdentity(to: KYCAddressWrapper.self))).thenReturn(getSuccessProducer(kycAddressData))
        }

        viewModel.getPresentBarangayAndZipCode.errors.observeValues { error in
            XCTAssertNil(error, "Unexpected Error encountered")
        }

        viewModel.getPresentBarangayAndZipCode.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ZolozKYC.getBarangayAndZipCode(province: "Test Province", city: "Test City")), type: isEqualIdentity(to: KYCAddressWrapper.self))
    }

    func testPresentBarangayAndZipCodeError() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)
        viewModel.selectedCurrentAddressState.value = KYCAddress(id: 1, name: "Test Province", cities: nil)
        viewModel.selectedCurrentAddressCity.value = KYCCities(id: 1, name: "Test City", localities: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual( to: API.ZolozKYC.getBarangayAndZipCode(province: "Test Province", city: "Test City")), type: isEqualIdentity(to: KYCAddressWrapper.self))).thenReturn(getBackendError())
        }

        viewModel.getPresentBarangayAndZipCode.errors.observeValues { error in
            XCTAssertEqual(error, self.getPayMayaError(CommonStrings.Maya.Error.Default.title))
        }

        viewModel.getPresentBarangayAndZipCode.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ZolozKYC.getBarangayAndZipCode(province: "Test Province", city: "Test City")), type: isEqualIdentity(to: KYCAddressWrapper.self))
    }

    func testPresentBarangayAndZipCodeUnknownError() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)
        viewModel.selectedCurrentAddressState.value = KYCAddress(id: 1, name: "Test Province", cities: nil)
        viewModel.selectedCurrentAddressCity.value = KYCCities(id: 1, name: "Test City", localities: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual( to: API.ZolozKYC.getBarangayAndZipCode(province: "Test Province", city: "Test City")), type: isEqualIdentity(to: KYCAddressWrapper.self))).thenReturn(getUnknownError())
        }

        viewModel.getPresentBarangayAndZipCode.errors.observeValues { error in
            XCTAssertEqual(error, self.getPayMayaUnknownError(CommonStrings.Maya.Error.Default.title))
        }

        viewModel.getPresentBarangayAndZipCode.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ZolozKYC.getBarangayAndZipCode(province: "Test Province", city: "Test City")), type: isEqualIdentity(to: KYCAddressWrapper.self))
    }

    func testPermanentCitiesSuccess() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)
        viewModel.selectedPermanentAddressState.value = KYCAddress(id: 1, name: "Test Province", cities: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual( to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))).thenReturn(getSuccessProducer(kycAddressData))
        }

        viewModel.getPermanentCities.errors.observeValues { error in
            XCTAssertNil(error, "Unexpected Error encountered")
        }

        viewModel.getPermanentCities.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))
    }

    func testGetPermanentCitiesError() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)
        viewModel.selectedPermanentAddressState.value = KYCAddress(id: 1, name: "Test Province", cities: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual( to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))).thenReturn(getBackendError())
        }

        viewModel.getPermanentCities.errors.observeValues { error in
            XCTAssertEqual(error, self.getPayMayaError(CommonStrings.Maya.Error.Default.title))
        }

        viewModel.getPermanentCities.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))
    }

    func testGetPermanentCitiesUnknownError() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)
        viewModel.selectedPermanentAddressState.value = KYCAddress(id: 1, name: "Test Province", cities: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual( to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))).thenReturn(getUnknownError())
        }

        viewModel.getPermanentCities.errors.observeValues { error in
            XCTAssertEqual(error, self.getPayMayaUnknownError(CommonStrings.Maya.Error.Default.title))
        }

        viewModel.getPermanentCities.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))
    }

    func testPermanentBarangayAndZipCodeSuccess() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)
        viewModel.selectedPermanentAddressState.value = KYCAddress(id: 1, name: "Test Province", cities: nil)
        viewModel.selectedPermanentAddressCity.value = KYCCities(id: 1, name: "Test City", localities: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual( to: API.ZolozKYC.getBarangayAndZipCode(province: "Test Province", city: "Test City")), type: isEqualIdentity(to: KYCAddressWrapper.self))).thenReturn(getSuccessProducer(kycAddressData))
        }

        viewModel.getPermanentBarangayAndZipCode.errors.observeValues { error in
            XCTAssertNil(error, "Unexpected Error encountered")
        }

        viewModel.getPermanentBarangayAndZipCode.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ZolozKYC.getBarangayAndZipCode(province: "Test Province", city: "Test City")), type: isEqualIdentity(to: KYCAddressWrapper.self))
    }

    func testPermanentBarangayAndZipCodeError() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)
        viewModel.selectedPermanentAddressState.value = KYCAddress(id: 1, name: "Test Province", cities: nil)
        viewModel.selectedPermanentAddressCity.value = KYCCities(id: 1, name: "Test City", localities: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual( to: API.ZolozKYC.getBarangayAndZipCode(province: "Test Province", city: "Test City")), type: isEqualIdentity(to: KYCAddressWrapper.self))).thenReturn(getBackendError())
        }

        viewModel.getPermanentBarangayAndZipCode.errors.observeValues { error in
            XCTAssertEqual(error, self.getPayMayaError(CommonStrings.Maya.Error.Default.title))
        }

        viewModel.getPermanentBarangayAndZipCode.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ZolozKYC.getBarangayAndZipCode(province: "Test Province", city: "Test City")), type: isEqualIdentity(to: KYCAddressWrapper.self))
    }

    func testPermanentBarangayAndZipCodeUnknownError() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)
        viewModel.selectedPermanentAddressState.value = KYCAddress(id: 1, name: "Test Province", cities: nil)
        viewModel.selectedPermanentAddressCity.value = KYCCities(id: 1, name: "Test City", localities: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual( to: API.ZolozKYC.getBarangayAndZipCode(province: "Test Province", city: "Test City")), type: isEqualIdentity(to: KYCAddressWrapper.self))).thenReturn(getUnknownError())
        }

        viewModel.getPermanentBarangayAndZipCode.errors.observeValues { error in
            XCTAssertEqual(error, self.getPayMayaUnknownError(CommonStrings.Maya.Error.Default.title))
        }

        viewModel.getPermanentBarangayAndZipCode.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ZolozKYC.getBarangayAndZipCode(province: "Test Province", city: "Test City")), type: isEqualIdentity(to: KYCAddressWrapper.self))
    }

    func testBirthCitiesSuccess() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)
        viewModel.selectedBirthsState.value = KYCAddress(id: 1, name: "Test Province", cities: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual( to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))).thenReturn(getSuccessProducer(kycAddressData))
        }

        viewModel.getBirthCities.errors.observeValues { error in
            XCTAssertNil(error, "Unexpected Error encountered")
        }

        viewModel.getBirthCities.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))
    }

    func testBirthCitiesError() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)
        viewModel.selectedBirthsState.value = KYCAddress(id: 1, name: "Test Province", cities: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual( to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))).thenReturn(getBackendError())
        }

        viewModel.getBirthCities.errors.observeValues { error in
            XCTAssertEqual(error, self.getPayMayaError(CommonStrings.Maya.Error.Default.title))
        }

        viewModel.getBirthCities.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))
    }

    func testBirthCitiesUnknownError() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                          provinces: mockedProvienceData,
                                                          preSubmissionId: preSubmissionId,
                                                          selectedDocument: mockedSelectedDocument)
        viewModel.selectedBirthsState.value = KYCAddress(id: 1, name: "Test Province", cities: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual( to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))).thenReturn(getUnknownError())
        }

        viewModel.getBirthCities.errors.observeValues { error in
            XCTAssertEqual(error, self.getPayMayaUnknownError(CommonStrings.Maya.Error.Default.title))
        }

        viewModel.getBirthCities.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.ZolozKYC.getCities(province: "Test Province")), type: isEqualIdentity(to: KYCAddressWrapper.self))
    }

    func testCheckingForFlaggedFieldsTrue() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                         provinces: mockedProvienceData,
                                                         preSubmissionId: preSubmissionId,
                                                         selectedDocument: mockedSelectedDocument)
        viewModel.selectedBirthsCountry.value = KYCData.Country(id: 1, name: "United States", code: "US")
        XCTAssertEqual(viewModel.checkForFlaggedFields(fields: flagFields, value: "US"), true)
    }

    func testCheckingForFlaggedFieldsFalse() {
        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                         provinces: mockedProvienceData,
                                                         preSubmissionId: preSubmissionId,
                                                         selectedDocument: mockedSelectedDocument)
        viewModel.selectedBirthsCountry.value = KYCData.Country(id: 1, name: "Philippines", code: "PH")
        XCTAssertEqual(viewModel.checkForFlaggedFields(fields: flagFields, value: "US"), false)
    }

    func testAutoFlaggingFatcaRegulatoryStatusFalse() {
        stub(configurationService) { stub in
            when(stub.fatcaEnabled.get).thenReturn(trueConfig())
        }

        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                         provinces: mockedProvienceData,
                                                         preSubmissionId: preSubmissionId,
                                                         selectedDocument: mockedSelectedDocument)
        viewModel.selectedBirthsCountry.value = KYCData.Country(id: 1, name: "United States", code: "US")
        viewModel.regulatoryStatus = regulatoryStatusFalse
        XCTAssertEqual(viewModel.isFlaggedForFatca().isFlagged, false)
        XCTAssertEqual(viewModel.isFlaggedForFatca().reasons, nil)
    }

    func testAutoFlaggingFatcaFalse() {
        stub(configurationService) { stub in
            when(stub.fatcaEnabled.get).thenReturn(trueConfig())
        }

        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                         provinces: mockedProvienceData,
                                                         preSubmissionId: preSubmissionId,
                                                         selectedDocument: mockedSelectedDocument)
        viewModel.selectedBirthsCountry.value = KYCData.Country(id: 1, name: "Philippines", code: "PH")
        viewModel.regulatoryStatus = regulatoryStatusTrue
        viewModel.regulatoryCMS = regulatoryCMS
        XCTAssertEqual(viewModel.isFlaggedForFatca().isFlagged, false)
        XCTAssertEqual(viewModel.isFlaggedForFatca().reasons, [])
    }

    func testAutoFlaggingFatcaTrueCitizen() {
        stub(configurationService) { stub in
            when(stub.fatcaEnabled.get).thenReturn(trueConfig())
        }

        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                         provinces: mockedProvienceData,
                                                         preSubmissionId: preSubmissionId,
                                                         selectedDocument: mockedSelectedDocument)
        viewModel.selectedBirthsCountry.value = KYCData.Country(id: 1, name: "United States", code: "US")
        viewModel.regulatoryStatus = regulatoryStatusTrue
        viewModel.regulatoryCMS = regulatoryCMS
        XCTAssertEqual(viewModel.isFlaggedForFatca().isFlagged, true)
        XCTAssertEqual(viewModel.isFlaggedForFatca().reasons, ["CITIZEN"])
    }

    func testAutoFlaggingFatcaTruePostal() {
        stub(configurationService) { stub in
            when(stub.fatcaEnabled.get).thenReturn(trueConfig())
        }

        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                         provinces: mockedProvienceData,
                                                         preSubmissionId: preSubmissionId,
                                                         selectedDocument: mockedSelectedDocument)
        viewModel.selectedCurrentAddressCountry.value = KYCData.Country(id: 1, name: "United States", code: "US")
        viewModel.regulatoryStatus = regulatoryStatusTrue
        viewModel.regulatoryCMS = regulatoryCMS
        XCTAssertEqual(viewModel.isFlaggedForFatca().isFlagged, true)
        XCTAssertEqual(viewModel.isFlaggedForFatca().reasons, ["POSTAL_CODE"])
    }

    func testAutoFlaggingFatcaTrueBoth() {
        stub(configurationService) { stub in
            when(stub.fatcaEnabled.get).thenReturn(trueConfig())
        }

        let viewModel = MayaPersonalInformationViewModel(data: mockedKYCData,
                                                         provinces: mockedProvienceData,
                                                         preSubmissionId: preSubmissionId,
                                                         selectedDocument: mockedSelectedDocument)
        viewModel.selectedBirthsCountry.value = KYCData.Country(id: 1, name: "United States", code: "US")
        viewModel.selectedCurrentAddressCountry.value = KYCData.Country(id: 1, name: "United States", code: "US")
        viewModel.regulatoryStatus = regulatoryStatusTrue
        viewModel.regulatoryCMS = regulatoryCMS
        XCTAssertEqual(viewModel.isFlaggedForFatca().isFlagged, true)
        XCTAssertEqual(viewModel.isFlaggedForFatca().reasons, ["CITIZEN", "POSTAL_CODE"])
    }
}

// MARK: Mocked data variable extension
private extension MayaPersonalInformationViewModelTests {
    var regulatoryCMS: MayaRegulatoryCMSData {
        MayaRegulatoryCMSData(id: 1,
                              dosriRelatedToMaxCount: 3,
                              dosriGuides: MayaRegulatoryCMSData.DosriGuide(id: 1, header: "", body: ""),
                              dosriTypes: [
                                MayaRegulatoryCMSData.DosriType(
                                    id: 1,
                                    key: "",
                                    description: "",
                                    additionalDescription: "",
                                    requiresRelatedTo: false)
                              ],
                              dosriRelationships: [
                                MayaRegulatoryCMSData.DosriRelationship(id: 1, key: "", description: "")
                              ],
                              fatcaGuides: MayaRegulatoryCMSData.FatcaGuide(
                                id: 1,
                                header: "",
                                body: "",
                                iconURL: "",
                                autoFlagRules: [
                                    MayaRegulatoryCMSData.AutoFlagRule(
                                        id: 1,
                                        value: "US",
                                        fatcaReason: MayaRegulatoryCMSData.FatcaReason(id: 1, key: "CITIZEN"),
                                        flagFields: [
                                            MayaRegulatoryCMSData.FlagField(id: 1, name: "birthPlace.country"),
                                            MayaRegulatoryCMSData.FlagField(id: 1, name: "nationality")
                                        ]),
                                    MayaRegulatoryCMSData.AutoFlagRule(
                                        id: 2,
                                        value: "US",
                                        fatcaReason: MayaRegulatoryCMSData.FatcaReason(id: 2, key: "POSTAL_CODE"),
                                        flagFields: [
                                            MayaRegulatoryCMSData.FlagField(id: 3, name: "presentAddress.country"),
                                            MayaRegulatoryCMSData.FlagField(id: 4, name: "permanentAddress.country")
                                        ])
                                ]),
                              fatcaSelections: [
                                MayaRegulatoryCMSData.FatcaSelection(
                                    id: 1,
                                    isFatca: true,
                                    description: "",
                                    fatcaReason: MayaRegulatoryCMSData.FatcaReason(id: 1, key: ""))
                              ],
                              validationRules: [
                                MayaRegulatoryCMSData.ValidationRule(
                                    id: 1,
                                    regex: "",
                                    additionalRule: "",
                                    errorMessage: "",
                                    validationFields: [MayaRegulatoryCMSData.ValidationField(id: 1, name: "")])
                              ])
    }

    var regulatoryStatusTrue: MayaRegulatoryStatus {
        MayaRegulatoryStatus(fatca: MayaRegulatoryStatus.FatcaStatus(isRequired: true),
                             dosri: MayaRegulatoryStatus.DosriStatus(isRequired: true))
    }

    var regulatoryStatusFalse: MayaRegulatoryStatus {
        MayaRegulatoryStatus(fatca: MayaRegulatoryStatus.FatcaStatus(isRequired: false),
                             dosri: MayaRegulatoryStatus.DosriStatus(isRequired: false))
    }

    var flagFields: [MayaRegulatoryCMSData.FlagField] {
        [MayaRegulatoryCMSData.FlagField(id: 1, name: "birthPlace.country"),
         MayaRegulatoryCMSData.FlagField(id: 1, name: "nationality")]
    }

    var mockedSelectedDocument: MayaEKYCSelectedDocument {
        MayaEKYCSelectedDocument(document:
                                    KYCData.Document(
                                        key: "ePhil ID",
                                        description: "EPhil ID",
                                        additionalDescription: nil,
                                        order: nil,
                                        hasExpiryDate: true,
                                        isShownOnList: true,
                                        category: nil,
                                        subHeader: nil),
                                captureGuide: nil)
    }

    var mockedCountriesData: [KYCData.Country] {
        [KYCData.Country(id: 0, name: "Afghanistan", code: "AF"),
         KYCData.Country(id: 1, name: "Zambia", code: "ZM"),
         KYCData.Country(id: 2, name: "Philippines", code: "PH")]
    }

    var mockedNationalitiesData: [KYCData.Nationality] {
        [KYCData.Nationality(id: 0, name: "Afghanistan", code: "AF"),
         KYCData.Nationality(id: 1, name: "Zambia", code: "ZM"),
         KYCData.Nationality(id: 2, name: "Philippines", code: "PH")]
    }

    var mockedIncomeSourcesData: [KYCData.IncomeSource] {
        [KYCData.IncomeSource(id: 1, name: "Salary"),
         KYCData.IncomeSource(id: 2, name: "Business Proceeds"),
         KYCData.IncomeSource(id: 3, name: "Allowance")]
    }

    var mockedWorkNatureData: [KYCData.WorkNature] {
        [KYCData.WorkNature(id: 1,
                            name: "OFW",
                            requiresEmploymentDetails: true,
                            additionalInfoRequired: false,
                            prohibitedIncomeSources: []),
         KYCData.WorkNature(id: 2,
                            name: "Retiree",
                            requiresEmploymentDetails: false,
                            additionalInfoRequired: false,
                            prohibitedIncomeSources: mockedIncomeSourcesData),
         KYCData.WorkNature(id: 3,
                            name: "Banking",
                            requiresEmploymentDetails: true,
                            additionalInfoRequired: false,
                            prohibitedIncomeSources: [])]
    }

    var mockedValidationRulesData: [KYCData.ValidationRule] {
        [KYCData.ValidationRule(id: 1,
                                regex: "^[a-zA-ZñÑ'. -]*$",
                                additionalRule: nil,
                                errorMessage: "Only letters, dashes, and apostrophes are allowed",
                                validationFields: [KYCData.ValidationField(id: 1, name: "firstName")]),
         KYCData.ValidationRule(id: 2,
                                regex: "^[a-zA-Z0-9-]*$",
                                additionalRule: nil,
                                errorMessage: "Only letter, numbers, and dashes are allowed",
                                validationFields: [KYCData.ValidationField(id: 2, name: "zipCode")])]
    }

    var mockedKYCData: KYCData {
        KYCData(benefits: [],
                documents: [
                    KYCData.Document(key: "DRIVERS_LICENSE",
                                     description: "Philippine Driver's License",
                                     additionalDescription: nil,
                                     order: 1,
                                     hasExpiryDate: false,
                                     isShownOnList: true,
                                     category: nil,
                                     subHeader: nil),
                    KYCData.Document(key: "PASSPORT",
                                     description: "Philippine Passport",
                                     additionalDescription: nil,
                                     order: 10,
                                     hasExpiryDate: false,
                                     isShownOnList: true,
                                     category: nil,
                                     subHeader: nil)
                ],
                photoTips: [],
                livenessTips: [],
                submit: KYCData.Submit(info: "", header: "", highlightedPhrase: ""),
                review: KYCData.Review(info: "", customerServiceUrlString: ""),
                countries: mockedCountriesData,
                nationalities: mockedNationalitiesData,
                workNatures: mockedWorkNatureData,
                incomeSources: mockedIncomeSourcesData,
                validationRules: mockedValidationRulesData,
                additionalDocuments: KYCData.AdditionalDocuments(consentURLString: ""),
                nameRule: KYCData.NameRule(id: 0, header: "", rules: ""),
                recommendedDocuments: nil,
                otherPrimaryDocuments: nil,
                secondaryDocuments: nil,
                captureGuides: nil,
                livePhotoTips: nil,
                imageQualityGuides: nil,
                imageQualityMaxRetries: 2,
                uploadGuides: nil,
                intro: nil,
                selfieTips: nil)
    }

    var mockedProvienceData: [KYCAddress] {
        [
            KYCAddress(id: 1, name: "Abra", cities: nil),
            KYCAddress(id: 2, name: "Agusan del Norte", cities: nil),
            KYCAddress(id: 3, name: "Agusan del Sur", cities: nil),
            KYCAddress(id: 4, name: "Aklan", cities: nil),
            KYCAddress(id: 5, name: "Albay", cities: nil),
            KYCAddress(id: 6, name: "Antique", cities: nil),
            KYCAddress(id: 7, name: "Apayao", cities: nil),
            KYCAddress(id: 8, name: "Aurora", cities: nil),
            KYCAddress(id: 9, name: "Basilan", cities: nil),
            KYCAddress(id: 10, name: "Bataan", cities: nil)
        ]
    }

    var mockedRecommendedDocuments: [KYCData.Document] {
        [
            KYCData.Document(key: "DRIVERS_LICENSE",
                             description: "Philippine Driver's License",
                             additionalDescription: nil,
                             order: nil,
                             hasExpiryDate: false,
                             isShownOnList: nil,
                             category: nil,
                             subHeader: nil),
            KYCData.Document(key: "PASSPORT",
                             description: "Philippine Passport",
                             additionalDescription: nil,
                             order: nil,
                             hasExpiryDate: false,
                             isShownOnList: nil,
                             category: nil,
                             subHeader: nil)
        ]
    }

    var mockedOtherPrimaryDocuments: [KYCData.Document] {
        [
            KYCData.Document(key: "PRC_ID",
                             description: "PRC ID",
                             additionalDescription: nil,
                             order: nil,
                             hasExpiryDate: false,
                             isShownOnList: nil,
                             category: nil,
                             subHeader: nil),
            KYCData.Document(key: "SSS_ID",
                             description: "SSS ID",
                             additionalDescription: nil,
                             order: nil,
                             hasExpiryDate: false,
                             isShownOnList: nil,
                             category: nil,
                             subHeader: nil)
        ]
    }

    var mockedSecondaryDocuments: [KYCData.Document] {
        [
            KYCData.Document(key: "BIRTH_CERTIFICATE",
                             description: "Birth Certificate",
                             additionalDescription: nil,
                             order: nil,
                             hasExpiryDate: false,
                             isShownOnList: nil,
                             category: nil,
                             subHeader: nil),
            KYCData.Document(key: "PARENTAL_CONCENT",
                             description: "Parental Consent",
                             additionalDescription: nil,
                             order: nil,
                             hasExpiryDate: false,
                             isShownOnList: nil,
                             category: nil,
                             subHeader: nil)
        ]
    }

    var mockedCaptureGuides: [KYCData.CaptureGuide] {
        [
            KYCData.CaptureGuide(id: 1,
                                 document: KYCData.CaptureGuideDocument(id: 1, key: "DRIVERS_LICENSE"),
                                 pages: [KYCData.Page(id: 1, page: "FRONT")],
                                 captureMethods: [KYCData.CaptureMethod(id: 1, method: "ZOLOZ_ID_LIVENESS")],
                                 samples: nil,
                                 variations: nil,
                                 isAllowedToUpload: nil,
                                 header: nil,
                                 subHeader: nil,
                                 idGuides: nil),
            KYCData.CaptureGuide(id: 2,
                                 document: KYCData.CaptureGuideDocument(id: 1, key: "PASSPORT"),
                                 pages: [KYCData.Page(id: 1, page: "FRONT")],
                                 captureMethods: [KYCData.CaptureMethod(id: 1, method: "ZOLOZ_ID_LIVENESS")],
                                 samples: nil,
                                 variations: nil,
                                 isAllowedToUpload: nil,
                                 header: nil,
                                 subHeader: nil,
                                 idGuides: nil),
            KYCData.CaptureGuide(id: 3,
                                 document: KYCData.CaptureGuideDocument(id: 1, key: "PRC_ID"),
                                 pages: [KYCData.Page(id: 1, page: "FRONT"),
                                         KYCData.Page(id: 2, page: "BACK")],
                                 captureMethods: [KYCData.CaptureMethod(id: 1, method: "SIMPLE_ID_LIVENESS")],
                                 samples: nil,
                                 variations: nil,
                                 isAllowedToUpload: nil,
                                 header: nil,
                                 subHeader: nil,
                                 idGuides: nil),
            KYCData.CaptureGuide(id: 4,
                                 document: KYCData.CaptureGuideDocument(id: 1, key: "SSS_ID"),
                                 pages: [KYCData.Page(id: 1, page: "FRONT")],
                                 captureMethods: [KYCData.CaptureMethod(id: 1, method: "SIMPLE_ID_LIVENESS")],
                                 samples: nil,
                                 variations: nil,
                                 isAllowedToUpload: nil,
                                 header: nil,
                                 subHeader: nil,
                                 idGuides: nil),
            KYCData.CaptureGuide(id: 5,
                                 document: KYCData.CaptureGuideDocument(id: 1, key: "BIRTH_CERTIFICATE"),
                                 pages: [KYCData.Page(id: 1, page: "FRONT")],
                                 captureMethods: [KYCData.CaptureMethod(id: 1, method: "SIMPLE_ID_LIVENESS")],
                                 samples: nil,
                                 variations: nil,
                                 isAllowedToUpload: nil,
                                 header: nil,
                                 subHeader: nil,
                                 idGuides: nil),
            KYCData.CaptureGuide(id: 6,
                                 document: KYCData.CaptureGuideDocument(id: 1, key: "PARENTAL_CONCENT"),
                                 pages: [KYCData.Page(id: 1, page: "FRONT"),
                                         KYCData.Page(id: 2, page: "BACK")],
                                 captureMethods: [KYCData.CaptureMethod(id: 1, method: "SIMPLE_ID_LIVENESS")],
                                 samples: nil,
                                 variations: nil,
                                 isAllowedToUpload: nil,
                                 header: nil,
                                 subHeader: nil,
                                 idGuides: nil)
        ]
    }

    var mockedKYCDataNewCms: KYCData {
        KYCData(benefits: [],
                documents: nil,
                photoTips: [],
                livenessTips: [],
                submit: KYCData.Submit(info: "", header: "", highlightedPhrase: ""),
                review: KYCData.Review(info: "", customerServiceUrlString: ""),
                countries: mockedCountriesData,
                nationalities: mockedNationalitiesData,
                workNatures: mockedWorkNatureData,
                incomeSources: mockedIncomeSourcesData,
                validationRules: mockedValidationRulesData,
                additionalDocuments: KYCData.AdditionalDocuments(consentURLString: ""),
                nameRule: KYCData.NameRule(id: 0, header: "", rules: ""),
                recommendedDocuments: mockedRecommendedDocuments,
                otherPrimaryDocuments: mockedOtherPrimaryDocuments,
                secondaryDocuments: mockedSecondaryDocuments,
                captureGuides: mockedCaptureGuides,
                livePhotoTips: nil,
                imageQualityGuides: nil,
                imageQualityMaxRetries: 2,
                uploadGuides: nil,
                intro: nil,
                selfieTips: nil)
    }

    var mockedInvalidDecisionData: ValidatingProperty<String, FieldValidationError>.Decision {
        return .invalid(FieldValidationError(message: "Mocked falidation error message"))
    }
}

extension ValidatingProperty.Decision: Equatable {
    public static func == (lhs: ValidatingProperty<Value, ValidationError>.Decision, rhs: ValidatingProperty<Value, ValidationError>.Decision) -> Bool {
        switch (lhs, rhs) {
        case (.valid, .valid):
            return true
        case (.invalid, .invalid):
            return true
        case (.coerced, .coerced):
            return true
        case (_, _):
            return false
        }
    }
}
