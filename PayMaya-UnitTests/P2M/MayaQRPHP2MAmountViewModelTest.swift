//
//  MayaQRPHP2MAmountViewModelTest.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 4/27/23.
//  Copyright © 2023 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import ConfigurationService
import Cuckoo
import Error
import MayaCoreData
import Moya
import ReactiveSwift
import StoreProvider
import XCTest

class MayaQRPHP2MAmountViewModelTest: ViewModelTest {
    var viewModel: MayaP2MAmountViewModelProtocol?

    override func setUp() {
        super.setUp()
    }

    func testCreatePaymentV2ActionSuccess() {
        let createMerchantType: CreateMerchantType = .onUsStaticQrPh
        let qrPayload = QRScannerViewModelTest.validQrphStaticQRString
        let merchant = MayaQRPHP2MAmountViewModelTest.qrphMerchant(
            createMerchantType: createMerchantType,
            qrRawValue: qrPayload,
            isOnUsQr: true
        )

        viewModel = MayaQRPHP2MAmountViewModel(merchant: merchant, pwpMode: .external)

        let request = MayaQRPHP2MAmountViewModelTest.merchantPaymentQRPHRequest(
            createMerchantType: createMerchantType,
            qrPayload: qrPayload,
            merchant: merchant
        )
        let successResponse = SignalProducer<CreatedMerchantPaymentV2, Error>(
            value: MayaQRPHP2MAmountViewModelTest.createdMerchantPaymentV2(
                createMerchantType: createMerchantType, qrPayload: qrPayload
            )
        )
        stub(reactiveAPIProvider) { stub in
            when(stub.request(
                isEqual(to: API.MerchantPaymentV2.createQRPH(request)),
                type: isEqual(to: CreatedMerchantPaymentV2.self),
                decoder: any()
            )).thenReturn(successResponse)
        }

        viewModel?.createPaymentV2Action?.values.observeValues({ createdMerchantPayment in
            XCTAssertEqual(createdMerchantPayment.merchant.name, merchant.name)
            XCTAssertEqual(createdMerchantPayment.amount.value, 100.00)
            XCTAssertEqual(createdMerchantPayment.amount.currency, MayaQRPHP2MAmountViewModelTest.currency)
            XCTAssertEqual(createdMerchantPayment.paymentDetails.paymentId, MayaQRPHP2MAmountViewModelTest.tokenId)
            XCTAssertEqual(createdMerchantPayment.qrPayload, QRScannerViewModelTest.validQrphStaticQRString)
            XCTAssertEqual(createdMerchantPayment.type, createMerchantType)
        })?.addToDisposeBag(disposeBag)

        viewModel?.amountProperty.mutableProperty.value = "\(MayaQRPHP2MAmountViewModelTest.amount)"

        viewModel?.createPaymentV2Action?.apply().start()

        verify(reactiveAPIProvider, times(1)).request(
            isEqual(to: API.MerchantPaymentV2.createQRPH(request)),
            type: isEqual(to: CreatedMerchantPaymentV2.self),
            decoder: any()
        )
    }

    func testCreatePaymentV2ActionSessionTimeout() {
        let createMerchantType: CreateMerchantType = .onUsStaticQrPh
        let qrPayload = QRScannerViewModelTest.validQrphStaticQRString
        let merchant = MayaQRPHP2MAmountViewModelTest.qrphMerchant(
            createMerchantType: createMerchantType,
            qrRawValue: qrPayload,
            isOnUsQr: true
        )

        viewModel = MayaQRPHP2MAmountViewModel(merchant: merchant, pwpMode: .external)

        let request = MayaQRPHP2MAmountViewModelTest.merchantPaymentQRPHRequest(
            createMerchantType: createMerchantType,
            qrPayload: qrPayload,
            merchant: merchant
        )
        stub(reactiveAPIProvider) { stub in
            when(stub.request(
                isEqual(to: API.MerchantPaymentV2.createQRPH(request)),
                type: isEqual(to: CreatedMerchantPaymentV2.self),
                decoder: any()
            )).thenReturn(getSessionTimeoutError())
        }

        viewModel?.amountProperty.mutableProperty.value = "\(MayaQRPHP2MAmountViewModelTest.amount)"

        viewModel?.createPaymentV2Action?.completed.observeValues({ _ in
            XCTFail("Action was completed. Should return an error \(#function)")
        })?.addToDisposeBag(disposeBag)

        viewModel?.createPaymentV2Action?.errors.signal.observeValues { error in
            XCTAssertEqual(error.type, ErrorType.sessionTimeout(backendErrorSpiel))
        }?.addToDisposeBag(disposeBag)

        viewModel?.createPaymentV2Action?.apply().start()

        verify(reactiveAPIProvider, times(1)).request(
            isEqual(to: API.MerchantPaymentV2.createQRPH(request)),
            type: isEqual(to: CreatedMerchantPaymentV2.self),
            decoder: any()
        )
    }

    func testCreatePaymentV2ActionBackendError() {
        let createMerchantType: CreateMerchantType = .onUsStaticQrPh
        let qrPayload = QRScannerViewModelTest.validQrphStaticQRString
        let merchant = MayaQRPHP2MAmountViewModelTest.qrphMerchant(
            createMerchantType: createMerchantType,
            qrRawValue: qrPayload,
            isOnUsQr: true
        )

        viewModel = MayaQRPHP2MAmountViewModel(merchant: merchant, pwpMode: .external)

        let request = MayaQRPHP2MAmountViewModelTest.merchantPaymentQRPHRequest(
            createMerchantType: createMerchantType,
            qrPayload: qrPayload,
            merchant: merchant
        )
        stub(reactiveAPIProvider) { stub in
            when(stub.request(
                isEqual(to: API.MerchantPaymentV2.createQRPH(request)),
                type: isEqual(to: CreatedMerchantPaymentV2.self),
                decoder: any()
            )).thenReturn(getBackendError())
        }

        viewModel?.amountProperty.mutableProperty.value = "\(MayaQRPHP2MAmountViewModelTest.amount)"

        viewModel?.createPaymentV2Action?.completed.observeValues({ _ in
            XCTFail("Action was completed. Should return an error \(#function)")
        })?.addToDisposeBag(disposeBag)

        viewModel?.createPaymentV2Action?.errors.signal.observeValues { error in
            XCTAssertEqual(error, self.getMayaError())
        }?.addToDisposeBag(disposeBag)

        viewModel?.createPaymentV2Action?.apply().start()

        verify(reactiveAPIProvider, times(1)).request(
            isEqual(to: API.MerchantPaymentV2.createQRPH(request)),
            type: isEqual(to: CreatedMerchantPaymentV2.self), decoder: any()
        )
    }

    func testCreatePaymentV2ActionUnkownError() {
        let createMerchantType: CreateMerchantType = .onUsStaticQrPh
        let qrPayload = QRScannerViewModelTest.validQrphStaticQRString
        let merchant = MayaQRPHP2MAmountViewModelTest.qrphMerchant(
            createMerchantType: createMerchantType,
            qrRawValue: qrPayload,
            isOnUsQr: true
        )

        viewModel = MayaQRPHP2MAmountViewModel(merchant: merchant, pwpMode: .external)

        let request = MayaQRPHP2MAmountViewModelTest.merchantPaymentQRPHRequest(
            createMerchantType: createMerchantType,
            qrPayload: qrPayload,
            merchant: merchant
        )
        stub(reactiveAPIProvider) { stub in
            when(stub.request(
                isEqual(to: API.MerchantPaymentV2.createQRPH(request)),
                type: isEqual(to: CreatedMerchantPaymentV2.self),
                decoder: any()
            )).thenReturn(getUnknownError())
        }

        viewModel?.amountProperty.mutableProperty.value = "\(MayaQRPHP2MAmountViewModelTest.amount)"

        viewModel?.createPaymentV2Action?.completed.observeValues({ _ in
            XCTFail("Action was completed. Should return an error \(#function)")
        })?.addToDisposeBag(disposeBag)

        viewModel?.createPaymentV2Action?.errors.signal.observeValues { error in
            XCTAssertEqual(error, self.getMayaUnknownError())
        }?.addToDisposeBag(disposeBag)

        viewModel?.createPaymentV2Action?.apply().start()

        verify(reactiveAPIProvider, times(1)).request(
            isEqual(to: API.MerchantPaymentV2.createQRPH(request)),
            type: isEqual(to: CreatedMerchantPaymentV2.self),
            decoder: any()
        )
    }

    func testCreatePaymentV2ActionValidationError() {
        let createMerchantType: CreateMerchantType = .onUsStaticQrPh
        let qrPayload = QRScannerViewModelTest.validQrphStaticQRString
        let merchant = MayaQRPHP2MAmountViewModelTest.qrphMerchant(
            createMerchantType: createMerchantType,
            qrRawValue: qrPayload,
            isOnUsQr: true
        )
        let request = MayaQRPHP2MAmountViewModelTest.merchantPaymentQRPHRequest(
            createMerchantType: createMerchantType,
            qrPayload: qrPayload,
            merchant: merchant
        )

        viewModel = MayaQRPHP2MAmountViewModel(merchant: merchant, pwpMode: .external)

        viewModel?.amountProperty.mutableProperty.value = String.empty

        viewModel?.createPaymentV2Action?.values.observeValues({ _ in
            XCTFail("Action was completed. Should return an error \(#function)")
        })?.addToDisposeBag(disposeBag)

        viewModel?.createPaymentV2Action?.errors.observeValues({ error in
            guard let errorModel = error.viewModel else {
                XCTFail("No error view model found. \(#function)")
                return
            }
            XCTAssertEqual(errorModel.title, L10n.Error.Merchant.Payment.title)
            XCTAssertEqual(errorModel.message, L10n.Error.Spiel.Validation.message(L10n.Field.Name.amount))
        })?.addToDisposeBag(disposeBag)

        viewModel?.createPaymentV2Action?.apply().start()

        verify(reactiveAPIProvider, never()).request(
            isEqual(to: API.MerchantPaymentV2.createQRPH(request)),
            type: isEqual(to: CreatedMerchantPayment.self)
        )
    }

    func testAvailableBalance() {
        let merchant = MayaQRPHP2MAmountViewModelTest.qrphMerchant(
            createMerchantType: .onUsStaticQrPh,
            qrRawValue: QRScannerViewModelTest.validQrphStaticQRString,
            isOnUsQr: true
        )

        viewModel = MayaQRPHP2MAmountViewModel(merchant: merchant, pwpMode: .external)

        stub(databaseStore) { stub in
            when(
                stub.read(
                    isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.balance))
                )
            ).thenReturn(StoreResult.success(getBalanceData()))
        }

        XCTAssertEqual(viewModel?.availableBalance, "₱ 1,000.00")
    }

    func testIsAmountValidProperty() {
        let merchant = MayaQRPHP2MAmountViewModelTest.qrphMerchant(
            createMerchantType: .onUsStaticQrPh,
            qrRawValue: QRScannerViewModelTest.validQrphStaticQRString,
            isOnUsQr: true
        )

        viewModel = MayaQRPHP2MAmountViewModel(merchant: merchant, pwpMode: .external)

        var amountsArray: [(Bool, String?)] = []

        stub(databaseStore) { stub in
            when(stub.read(
                isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.balance))
            )).thenReturn(StoreResult.success(getBalanceData()))
        }

        viewModel?.isAmountValid.signal.observeValues { value in
            amountsArray.append(value)
        }?.addToDisposeBag(disposeBag)

        viewModel?.amountProperty.mutableProperty.value = "1001"
        viewModel?.amountProperty.mutableProperty.value = "1001.00"
        XCTAssertEqual(amountsArray.map { $0.0 }, [true, true])
        XCTAssertEqual(amountsArray.map { $0.1 }, ["Insufficient funds in your PayMaya account", "Insufficient funds in your PayMaya account"])
    }

    func testFormatAmountV2() {
        let merchant = MayaQRPHP2MAmountViewModelTest.qrphMerchant(
            createMerchantType: .onUsStaticQrPh,
            qrRawValue: QRScannerViewModelTest.validQrphStaticQRString,
            isOnUsQr: true
        )

        viewModel = MayaQRPHP2MAmountViewModel(merchant: merchant, pwpMode: .external)

        stub(configurationService) { stub in
            when(stub.amountFormatterV2Enabled.get).thenReturn(trueConfig())
        }

        let amountFormatterHelper = AmountFormatterViewModelTestHelper()
        amountFormatterHelper.testFormatAmount(for: viewModel!)
    }

    func testMerchantName() {
        let merchant = MayaQRPHP2MAmountViewModelTest.qrphMerchant(
            createMerchantType: .onUsStaticQrPh,
            qrRawValue: QRScannerViewModelTest.validQrphStaticQRString,
            isOnUsQr: true
        )

        viewModel = MayaQRPHP2MAmountViewModel(merchant: merchant, pwpMode: .external)

        XCTAssertEqual(viewModel?.merchantName, merchant.name)
    }
}

fileprivate extension MayaQRPHP2MAmountViewModelTest {
    static let currency = "₱"
    static let amount: Double = 100.00
    static let tokenId = "1234567890"
    static let merchantName = "CAFE MARY GRACE INC."
    static let merchantCategoryCode = ""
    static let merchantCountryCode = "PH"
    static let merchantCity = "Mandaluyong"
    static let id = "eac5f568-f2fd-4d5f-871a-9629c3ebbec5"
    static let customer = "2f32c243-a860-40e2-b553-b1c59c63ecd5"
    static let statusPendingPayment = "PENDING_PAYMENT"

    static func recipientType() -> RecipientType {
        return RecipientType(rawValue: "PAYMAYA")!
    }

    static func qrphMerchant(
        createMerchantType: CreateMerchantType,
        qrRawValue: String,
        isOnUsQr: Bool
    ) -> QRPHMerchant {
        return QRPHMerchant(
            qrDetails: getQRDetails(),
            pointOfInitiation: .static,
            qrRawValue: qrRawValue,
            createMerchantType: createMerchantType,
            isOnUsQr: isOnUsQr
        )!
    }

    static func merchantPaymentQRPHRequest(
        createMerchantType: CreateMerchantType,
        qrPayload: String,
        merchant: QRPHMerchant
    ) -> MerchantPaymentQRPHRequest {
        let amountRequest = AmountRequest(currency: currency, value: amount)
        let additionalInfo = MerchantPaymentQRPHRequest.Merchant.AdditionalInfo(
            systemId: merchant.globallyUniquePaymentSystemID,
            acquirerRequiredInfo: merchant.acquirerRequiredInformation,
            loyaltyNum: merchant.loyaltyNumber,
            proxyNotifyFlag: merchant.proxyNotifFlag,
            merchantCreditAccountNumber: merchant.merchantCreditAccountNumber,
            countryCode: merchant.countryCode,
            merchantPostalCode: merchant.merchantPostalCode
        )

        let merchant = MerchantPaymentQRPHRequest.Merchant(
            acquirerId: merchant.acquirerID,
            id: merchant.id,
            name: merchant.name,
            claimId: merchant.claimID,
            terminalId: merchant.terminalID,
            storeName: merchant.storeName,
            categoryCode: merchant.categoryCode,
            city: merchant.city,
            additionalInfo: additionalInfo
        )

        return MerchantPaymentQRPHRequest(
            type: createMerchantType.rawValue,
            amount: amountRequest,
            qrPayload: qrPayload,
            merchant: merchant
        )
    }

    static func createdMerchantPaymentV2(
        createMerchantType: CreateMerchantType,
        qrPayload: String
    ) -> CreatedMerchantPaymentV2 {
        return CreatedMerchantPaymentV2(
            id: id,
            customer: customer,
            type: createMerchantType,
            amount: CreatedMerchantPaymentV2.Amount(currency: currency, value: amount),
            qrPayload: qrPayload,
            merchant: CreatedMerchantPaymentV2.Merchant(
                id: nil,
                name: merchantName,
                claimId: nil,
                terminalId: nil,
                acquirerId: nil,
                storeName: nil,
                city: nil,
                traceReferenceNumber: nil,
                categoryCode: nil,
                additionalInfo: nil
            ),
            paymentDetails: CreatedMerchantPaymentV2.PaymentDetails(
                paymentId: tokenId,
                paymentMethodRedirectUrl: nil
            ),
            status: statusPendingPayment,
            createdDate: Date(),
            updatedDate: Date()
        )
    }

    func getBalanceData() -> StubBalance {
        let stubBalance = StubBalance()
        return StubBalance(
            available: StubAvailableAmount(
                currency: Constants.Defaults.Common.peso.rawValue, id: "1",
                value: 1000,
                balance: stubBalance
            ),
            current: StubCurrentAmount(
                currency: Constants.Defaults.Common.currency.rawValue,
                id: "2",
                value: 1000,
                balance: stubBalance
            ),
            user: StubUser()
        )
    }

    static func getQRDetails() -> QRDetails {
        var qrDetails = QRDetails()

        qrDetails.acquirerRequiredInfo = "006392094574580111MBTCPHMMXXX"
        qrDetails.proxyNotifFlag = "000"
        qrDetails.globalUniqueIdentifier = "ph.ppmi.p2m"
        qrDetails.acquirerId = "MBTCPHMMXXX"
        qrDetails.merchantCountryCode = merchantCountryCode
        qrDetails.merchantCity = merchantCity
        qrDetails.merchantId = "701000000202"
        qrDetails.merchantName = merchantName
        qrDetails.merchantCategoryCode = merchantCategoryCode
        qrDetails.pointOfInitiation = "11"
        qrDetails.additionalData = QRAdditionalData(storeName: nil, loyaltyNumber: nil, referenceId: tokenId, terminalId: "00000003", purpose: nil)

        return qrDetails
    }

    static func getMerchantRequestQRPHMerchant() -> MerchantPaymentQRPHRequest.Merchant {
        let qrDetails = MayaQRPHP2MAmountViewModelTest.getQRDetails()
        return MerchantPaymentQRPHRequest.Merchant(
            acquirerId: qrDetails.acquirerId!,
            id: qrDetails.merchantId!,
            name: qrDetails.merchantName!,
            claimId: qrDetails.additionalData?.referenceId,
            terminalId: qrDetails.additionalData?.terminalId,
            storeName: qrDetails.additionalData?.storeName,
            categoryCode: qrDetails.merchantCategoryCode,
            city: qrDetails.merchantCity!,
            additionalInfo: MerchantPaymentQRPHRequest.Merchant.AdditionalInfo(
                systemId: qrDetails.globalUniqueIdentifier!,
                acquirerRequiredInfo: qrDetails.acquirerRequiredInfo, loyaltyNum: qrDetails.additionalData?.loyaltyNumber,
                proxyNotifyFlag: qrDetails.proxyNotifFlag!,
                merchantCreditAccountNumber: qrDetails.merchantCreditAccountNumber,
                countryCode: qrDetails.merchantCountryCode,
                merchantPostalCode: qrDetails.merchantPostalCode
            )
        )
    }
}
