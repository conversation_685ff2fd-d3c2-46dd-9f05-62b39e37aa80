//
//  MayaOffUsP2MAmountViewModelTest.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 04/08/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import ConfigurationService
import Cuckoo
import Error
import MayaCoreData
import Moya
import ReactiveSwift
import StoreProvider
import XCTest

private let merchant = OffUSQRPHMerchant(qrDetails: MayaOffUsP2MAmountViewModelTest.getQRDetails(), pointOfInitiation: .static)

class MayaOffUsP2MAmountViewModelTest: ViewModelTest {
    var viewModel: MayaP2MAmountViewModelProtocol!

    override func setUp() {
        super.setUp()

        viewModel = MayaOffUsP2MAmountViewModel(merchant: merchant!, pwpMode: .external)
    }

    func testCreatePaymentActionSuccess() {
        let successResponse = SignalProducer<OffUSQRPHCreatedMerchantPayment, Error>(value: MayaOffUsP2MAmountViewModelTest.createdMerchantPayment())
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MerchantPayment.createQRPH(request: MayaOffUsP2MAmountViewModelTest.merchantPaymentRequest())), type: isEqual(to: OffUSQRPHCreatedMerchantPayment.self)))
            .thenReturn(successResponse)
        }

        viewModel.createPaymentAction?.values.observeValues({ model in
            guard let model = model else {
                XCTFail()
                return
            }
            XCTAssertEqual(model.merchantName, merchant!.name)
            XCTAssertEqual(model.amountValue, "100.00")
            XCTAssertFalse(model.isDynamicQr)
            XCTAssertEqual(model.amountCurrency, MayaOffUsP2MAmountViewModelTest.currency)
            })?.addToDisposeBag(disposeBag)

        viewModel.amountProperty.mutableProperty.value = "\(MayaOffUsP2MAmountViewModelTest.amount)"

        viewModel.createPaymentAction?.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MerchantPayment.createQRPH(request: MayaOffUsP2MAmountViewModelTest.merchantPaymentRequest())), type: isEqual(to: OffUSQRPHCreatedMerchantPayment.self))
    }

    func testCreatePaymentActionSessionTimeout() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MerchantPayment.createQRPH(request: MayaOffUsP2MAmountViewModelTest.merchantPaymentRequest())), type: isEqual(to: OffUSQRPHCreatedMerchantPayment.self)))
            .thenReturn(getSessionTimeoutError())
        }

        viewModel.amountProperty.mutableProperty.value = "\(MayaOffUsP2MAmountViewModelTest.amount)"

        viewModel.createPaymentAction?.completed.observeValues({ _ in
            XCTFail("Action was completed. Should return an error \(#function)")
            })?.addToDisposeBag(disposeBag)

        viewModel.createPaymentAction?.errors.signal.observeValues { error in
            XCTAssertEqual(error.type, ErrorType.sessionTimeout(backendErrorSpiel))
        }?.addToDisposeBag(disposeBag)

        viewModel.createPaymentAction?.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MerchantPayment.createQRPH(request: MayaOffUsP2MAmountViewModelTest.merchantPaymentRequest())), type: isEqual(to: OffUSQRPHCreatedMerchantPayment.self))
    }

    func testCreatePaymentActionBackendError() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MerchantPayment.createQRPH(request: MayaOffUsP2MAmountViewModelTest.merchantPaymentRequest())), type: isEqual(to: OffUSQRPHCreatedMerchantPayment.self)))
            .thenReturn(getBackendError())
        }

        viewModel.amountProperty.mutableProperty.value = "\(MayaOffUsP2MAmountViewModelTest.amount)"

        viewModel.createPaymentAction?.completed.observeValues({ _ in
            XCTFail("Action was completed. Should return an error \(#function)")
            })?.addToDisposeBag(disposeBag)

        viewModel.createPaymentAction?.errors.signal.observeValues { error in
            XCTAssertEqual(error, self.getPayMayaError(L10n.Error.Merchant.Payment.title))
        }?.addToDisposeBag(disposeBag)

        viewModel.createPaymentAction?.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MerchantPayment.createQRPH(request: MayaOffUsP2MAmountViewModelTest.merchantPaymentRequest())), type: isEqual(to: OffUSQRPHCreatedMerchantPayment.self))
    }

    func testCreatePaymentActionValidationError() {
        viewModel.amountProperty.mutableProperty.value = String.empty

        viewModel.createPaymentAction?.values.observeValues({ _ in
            XCTFail("Action was completed. Should return an error \(#function)")
        })?.addToDisposeBag(disposeBag)

        viewModel.createPaymentAction?.errors.observeValues({ error in
            guard let errorModel = error.viewModel else {
                XCTFail("No error view model found. \(#function)")
                return
            }

            XCTAssertEqual(errorModel.title, L10n.Error.Merchant.Payment.title)
            XCTAssertEqual(errorModel.message, L10n.Error.Spiel.Validation.message(L10n.Field.Name.amount))
            })?.addToDisposeBag(disposeBag)

        viewModel.createPaymentAction?.apply().start()

        verify(reactiveAPIProvider, never()).request(isEqual(to: API.MerchantPayment.createQRPH(request: MayaOffUsP2MAmountViewModelTest.merchantPaymentRequest())), type: isEqual(to: OffUSQRPHCreatedMerchantPayment.self))
    }

    func testCreatePaymentActionUnkownError() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MerchantPayment.createQRPH(request: MayaOffUsP2MAmountViewModelTest.merchantPaymentRequest())), type: isEqual(to: OffUSQRPHCreatedMerchantPayment.self))).thenReturn(getUnknownError())
        }

        viewModel.amountProperty.mutableProperty.value = "\(MayaOffUsP2MAmountViewModelTest.amount)"

        viewModel.createPaymentAction?.completed.observeValues({ _ in
            XCTFail("Action was completed. Should return an error \(#function)")
            })?.addToDisposeBag(disposeBag)

        viewModel.createPaymentAction?.errors.signal.observeValues { error in
            XCTAssertEqual(error, self.getPayMayaUnknownError(L10n.Error.Merchant.Payment.title))
        }?.addToDisposeBag(disposeBag)

        viewModel.createPaymentAction?.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MerchantPayment.createQRPH(request: MayaOffUsP2MAmountViewModelTest.merchantPaymentRequest())), type: isEqual(to: OffUSQRPHCreatedMerchantPayment.self))
    }

    func testAvailableBalance() {
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.balance)))).thenReturn(StoreResult.success(getBalanceData()))
        }

        XCTAssertEqual(viewModel.availableBalance, "PHP 1,000.00")
    }

    func testIsAmountValidProperty() {
        var amountsArray: [(Bool, String?)] = []

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.balance)))).thenReturn(StoreResult.success(getBalanceData()))
        }

        viewModel.isAmountValid.signal.observeValues { value in
            amountsArray.append(value)
        }?.addToDisposeBag(disposeBag)

        viewModel.amountProperty.mutableProperty.value = "100"
        viewModel.amountProperty.mutableProperty.value = "PHP 100.00"
        XCTAssertEqual(amountsArray.map { $0.0 }, [true, true])
        XCTAssertEqual(amountsArray.map { $0.1 }, ["Insufficient funds in your PayMaya account", "Insufficient funds in your PayMaya account"])
    }

    func testFormatAmountV2() {
        stub(configurationService) { stub in
            when(stub.amountFormatterV2Enabled.get).thenReturn(trueConfig())
        }

        let amountFormatterHelper = AmountFormatterViewModelTestHelper()
        amountFormatterHelper.testFormatAmount(for: viewModel)
    }

    func testMerchantName() {
        XCTAssertEqual(viewModel.merchantName, merchant!.name)
    }
}

fileprivate extension MayaOffUsP2MAmountViewModelTest {
    static let currency = "PHP"
    static let amount: Double = 100.00
    static let tokenId = "**********"

    static func recipientType() -> RecipientType {
        return RecipientType(rawValue: "PAYMAYA")!
    }

    static func merchantPaymentRequest() -> QRPHMerchantPaymentRequest {
        let amountRequest = AmountRequest(currency: currency, value: amount)
        return QRPHMerchantPaymentRequest(amount: amountRequest, merchant: MayaOffUsP2MAmountViewModelTest.getMerchantRequestMerchant())
    }

    static func createdMerchantPayment() -> OffUSQRPHCreatedMerchantPayment {
        return OffUSQRPHCreatedMerchantPayment(paymentToken: OffUSQRPHCreatedMerchantPayment.PaymentToken(id: tokenId), amount: AmountResponse(currency: currency, value: "\(amount)"), authChallengeId: nil, merchant: MayaOffUsP2MAmountViewModelTest.getMerchantResponse())
    }

    func getBalanceData() -> StubBalance {
        let stubBalance = StubBalance()
        return StubBalance(available: StubAvailableAmount(currency: Constants.Defaults.Common.currency.rawValue, id: "1", value: 1000, balance: stubBalance), current: StubCurrentAmount(currency: Constants.Defaults.Common.currency.rawValue, id: "2", value: 1000, balance: stubBalance), user: StubUser())
    }

    static func getQRDetails() -> QRDetails {
        var qrDetails = QRDetails()

        qrDetails.acquirerRequiredInfo = "006392094574580111MBTCPHMMXXX"
        qrDetails.proxyNotifFlag = "000"
        qrDetails.globalUniqueIdentifier = "ph.ppmi.p2m"
        qrDetails.acquirerId = "MBTCPHMMXXX"
        qrDetails.merchantCity = "Mandaluyong"
        qrDetails.merchantId = "701000000202"
        qrDetails.merchantName = "CAFE MARY GRACE INC."
        qrDetails.pointOfInitiation = "11"
        qrDetails.additionalData = QRAdditionalData(storeName: nil, loyaltyNumber: nil, referenceId: "211000", terminalId: "00000003", purpose: nil)

        return qrDetails
    }

    static func getMerchantRequestMerchant() -> QRPHMerchantPaymentRequest.Merchant {
        let qrDetails = MayaOffUsP2MAmountViewModelTest.getQRDetails()
        return QRPHMerchantPaymentRequest.Merchant(id: qrDetails.merchantId!, name: qrDetails.merchantName!, city: qrDetails.merchantCity!, claimId: qrDetails.additionalData?.referenceId, terminalId: qrDetails.additionalData?.terminalId, acquirerId: qrDetails.acquirerId!, storeName: qrDetails.additionalData?.storeName, additionalInfo: QRPHMerchantPaymentRequest.Merchant.AdditionalInfo(systemId: qrDetails.globalUniqueIdentifier!, loyaltyNum: qrDetails.additionalData?.loyaltyNumber, proxyNotifyFlag: qrDetails.proxyNotifFlag!, acquirerRequiredInfo: qrDetails.acquirerRequiredInfo, merchantCreditAccountNumber: qrDetails.merchantCreditAccountNumber))
    }

    static func getMerchantResponse() -> OffUsQRPHMerchantResponse {
        let qrDetails = MayaOffUsP2MAmountViewModelTest.getQRDetails()
        return OffUsQRPHMerchantResponse(id: qrDetails.merchantId!, name: qrDetails.merchantName!, city: qrDetails.merchantCity, claimId: qrDetails.additionalData?.referenceId, terminalId: qrDetails.additionalData?.terminalId, acquirerId: qrDetails.acquirerId!, storeName: qrDetails.additionalData?.storeName, additionalInfo: OffUsQRPHMerchantResponse.AdditionalInfo(globallyUniquePaymentSystemId: qrDetails.globalUniqueIdentifier, loyaltyNum: qrDetails.additionalData?.loyaltyNumber, proxyNotifFlag: qrDetails.proxyNotifFlag, acquirerRequiredInfo: qrDetails.acquirerRequiredInfo, merchantCreditAccountNumber: qrDetails.merchantCreditAccountNumber))
    }
}
