//
//  MayaRegistrationOTPViewModelTest.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON> on 3/3/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import Cuckoo
import Error
import Injector
import MayaCoreData
import Moya
import ReactiveSwift
import StoreProvider
import XCTest

private let errorTitle = L10n.Error.Verification.title
private let mobileNumber = "+************"
private let otpId = "otp_id"
private let registrationId = "registration_id"
private let validOtpCode = "123456"

class MayaRegistrationOTPViewModelTest: ViewModelTest {
    var viewModel: MayaOTPViewModelProtocol!

    private var flutterEngineManager: MockFlutterEngineManager!

    override func setUp() {
        super.setUp()
        flutterEngineManager = ContainerWrapper.shared.resolve(FlutterEngineManager.self) as? MockFlutterEngineManager
        let userRegistration = UserRegistration(registrationID: registrationId, identity: UserRegistration.Identity(value: mobileNumber, type: IdentityType.msisdn.rawValue))
        stub(encryptedDefaultsStore) { stub in
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.registration), type: isEqual(to: UserRegistration.self))).thenReturn(StoreResult.success(userRegistration))
            when(stub.remove(isEqual(to: EncryptedDefaultsStoreId.registration), completion: anyOptional())).thenDoNothing()
        }

        stub(userDefaultsStore) { stub in
            when(stub.write(any(), options: isEqual(to: UserDefaultsStoreId.shouldShowUpgradeAccountUponRegistration), completion: anyOptional())).thenDoNothing()
            when(stub.write(any(), options: isEqual(to: UserDefaultsStoreId.shouldShowInputInviteCode), completion: anyOptional())).thenDoNothing()
        }

        stub(configurationService) { stub in
            when(stub.registrationRestrictionsEnabled.get).thenReturn(falseConfig())
        }

        viewModel = MayaRegistrationOTPViewModel(otpType: .registration)
        viewModel.firstCode.mutableProperty.value = validOtpCode.stringCharacterAtIndex(0)
        viewModel.secondCode.mutableProperty.value = validOtpCode.stringCharacterAtIndex(1)
        viewModel.thirdCode.mutableProperty.value = validOtpCode.stringCharacterAtIndex(2)
        viewModel.fourthCode.mutableProperty.value = validOtpCode.stringCharacterAtIndex(3)
        viewModel.fifthCode.mutableProperty.value = validOtpCode.stringCharacterAtIndex(4)
        viewModel.sixthCode.mutableProperty.value = validOtpCode.stringCharacterAtIndex(5)
    }

    func testResendCountdown() {
        XCTAssertEqual(30, viewModel.resendCountdown)
    }

    func testDefaultErrorAuthenticateAction() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(any(APITargetType.self))).thenReturn(getUnknownError())
        }

        viewModel.authenticateAction.completed.observeValues { _ in
            XCTFail("Unexpected completion")
        }?.addToDisposeBag(disposeBag)

        viewModel.authenticateAction.errors.observeValues { [weak self] error in
            guard let self = self else { return }

            XCTAssertEqual(error, self.getMayaUnknownError())
        }?.addToDisposeBag(disposeBag)

        viewModel.authenticateAction.apply().start()
    }

    func testBackendErrorAuthenticateAction() {
        viewModel.authenticateAction.completed.observeValues { _ in
            XCTFail("Unexpected completion")
        }?.addToDisposeBag(disposeBag)

        viewModel.authenticateAction.errors.observeValues { [weak self] error in
            guard let self = self else { return }
            XCTAssertEqual(error, self.getMayaError())
        }?.addToDisposeBag(disposeBag)

        viewModel.authenticateAction.apply().start()
    }

    func testOTPMaxAttemptAuthenticateAction() {
        stub(reactiveAPIProvider) { stub in
             when(stub.request(any(APITargetType.self))).thenReturn(getVerifyBackendError(code: APIConstants.APIStatusCode.maxIncorrectVcode.rawValue))
        }

        viewModel.authenticateAction.completed.observeValues { _ in
            XCTFail("Unexpected completion")
        }?.addToDisposeBag(disposeBag)

        viewModel.authenticateAction.errors.observeValues {  error in
            XCTAssertEqual(error, self.getVerificationRedirectPayMayaError(errorTitle: defaultErrorTitle))
        }?.addToDisposeBag(disposeBag)

        viewModel.authenticateAction.apply().start()

        verify(encryptedDefaultsStore, times(1)).remove(isEqual(to: EncryptedDefaultsStoreId.registration), completion: anyOptional())    }

    func testMissingAuthenticateAction() {
        viewModel.sixthCode.mutableProperty.value = ""
        viewModel.authenticateAction.completed.observeValues { _ in
            XCTFail("Unexpected completion")
        }?.addToDisposeBag(disposeBag)

        viewModel.authenticateAction.errors.observeValues { [weak self] error in
            guard let self = self else { return }
            XCTAssertEqual(error, self.getValidationError(defaultErrorTitle, message: L10n.Error.Spiel.Otp.required))
        }?.addToDisposeBag(disposeBag)

        viewModel.authenticateAction.apply().start()
    }

    func testSuccessAuthenticateAction() {
        let verificationRequest = VerificationRequest(registrationId: registrationId, verificationCode: validOtpCode)
        let user = StubUser()
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Registration.verify(verificationRequest)))).thenReturn(getSuccessProducer())
        }
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }
        stub(flutterEngineManager) { stub in
            when(stub.initializeEngines(with: isEqual(to: FlutterModule.allCases))).thenDoNothing()
        }

        viewModel.authenticateAction.errors.observeValues { _ in
            XCTFail("Unexpected error")
        }?.addToDisposeBag(disposeBag)

        viewModel.authenticateAction.values.observeValues { [weak self] _ in
            guard let self = self else { return }
            XCTAssertEqual(self.viewModel.user.value, user)
        }?.addToDisposeBag(disposeBag)

        viewModel.authenticateAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Registration.verify(verificationRequest)))
        verify(flutterEngineManager, times(1)).initializeEngines(with: isEqual(to: FlutterModule.allCases))
    }

    func testSuccessAuthenticateActionRestrictionEnabled() {
        let verificationRequest = VerificationRequest(registrationId: registrationId, verificationCode: validOtpCode)
        let user = StubUser()
        self.stubClearRestrictions()
        stub(configurationService) { stub in
            when(stub.registrationRestrictionsEnabled.get).thenReturn(trueConfig())
        }

        let restrictionRequest = RestrictionRequest(accountStatus: "ACTIVE")
        let customerRestrictionRequest = API.Restrictions.getCustomerRestrictions(restrictionRequest)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Registration.verify(verificationRequest)))).thenReturn(getSuccessProducer())
            when(stub.request(isEqual(to: customerRestrictionRequest))).thenReturn(getSuccessProducer())
        }

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }
        stub(flutterEngineManager) { stub in
            when(stub.initializeEngines(with: isEqual(to: FlutterModule.allCases))).thenDoNothing()
        }

        viewModel.authenticateAction.errors.observeValues { _ in
            XCTFail("Unexpected error")
        }?.addToDisposeBag(disposeBag)

        viewModel.authenticateAction.values.observeValues { [weak self] _ in
            guard let self = self else { return }
            XCTAssertEqual(self.viewModel.user.value, user)
        }?.addToDisposeBag(disposeBag)

        viewModel.authenticateAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Registration.verify(verificationRequest)))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: customerRestrictionRequest), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))
        verify(flutterEngineManager, times(1)).initializeEngines(with: isEqual(to: FlutterModule.allCases))
    }

    func testResendAction() {
        let verificationRequest = VerificationRequest(registrationId: registrationId, verificationCode: nil)
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Registration.resendCode(verificationRequest)))).thenReturn(getSuccessProducer())
        }
        viewModel.resendOTPCodeAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Registration.resendCode(verificationRequest)))
    }

    func testMobileNumber() {
        guard let viewModel = viewModel as? MayaRegistrationOTPViewModel else {
            XCTFail("Unexpected error")
            return
        }

        XCTAssertEqual(viewModel.mobileNumber, mobileNumber)
    }

    func testSetUserDefaultShouldShowUpgradeAccount() {
        guard let viewModel = viewModel as? MayaRegistrationOTPViewModel else {
            XCTFail("Unexpected error")
        return
        }

        viewModel.setUserDefaultShouldShowUpgradeAccount()

        verify(userDefaultsStore, times(1)).write(any(), options: isEqual(to: UserDefaultsStoreId.shouldShowUpgradeAccountUponRegistration), completion: isNil())
    }

    func testSetUserDefaultShouldSubmitInviteCode() {
        guard let viewModel = viewModel as? MayaRegistrationOTPViewModel else {
            XCTFail("Unexpected error")
            return
        }

        viewModel.setUserDefaultShouldSubmitInviteCode()

        verify(userDefaultsStore, times(1)).write(any(), options: isEqual(to: UserDefaultsStoreId.shouldShowInputInviteCode), completion: isNil())
    }

    func testIsRegistrationMGMEnabled() {
        guard let viewModel = viewModel as? MayaRegistrationOTPViewModel else {
            XCTFail("Unexpected error")
            return
        }

        configurationService.stubRegistrationMGMEnabled(falseConfig())
        XCTAssertFalse(viewModel.isRegistrationMGMEnabled)

        configurationService.stubRegistrationMGMEnabled(trueConfig())
        XCTAssertTrue(viewModel.isRegistrationMGMEnabled)
    }

    func testCanBeReferred() {
        guard let viewModel = viewModel as? MayaRegistrationOTPViewModel else {
            XCTFail("Unexpected error")
            return
        }

        let user = StubUser()
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }
        XCTAssertTrue(viewModel.canBeReferred)

        let userWithPrefilledData = getUserWithPrefilledData()
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(userWithPrefilledData))
        }
        XCTAssertTrue(viewModel.canBeReferred)
    }

    func testOTPMessage() {
        XCTAssertEqual(viewModel.otpMessage, "Please enter the one-time PIN (OTP) that we sent to +63 912 345 6789")

        let userRegistration = UserRegistration(registrationID: registrationId, identity: UserRegistration.Identity(value: "09191111111", type: IdentityType.msisdn.rawValue))
        stub(encryptedDefaultsStore) { stub in
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.registration), type: isEqual(to: UserRegistration.self))).thenReturn(StoreResult.success(userRegistration))
            when(stub.remove(isEqual(to: EncryptedDefaultsStoreId.registration), completion: anyOptional())).thenDoNothing()
        }

        stub(userDefaultsStore) { stub in
            when(stub.write(any(), options: isEqual(to: UserDefaultsStoreId.shouldShowUpgradeAccountUponRegistration), completion: anyOptional())).thenDoNothing()
        }

        viewModel = MayaRegistrationOTPViewModel(otpType: .registration)
        viewModel.firstCode.mutableProperty.value = validOtpCode.stringCharacterAtIndex(0)
        viewModel.secondCode.mutableProperty.value = validOtpCode.stringCharacterAtIndex(1)
        viewModel.thirdCode.mutableProperty.value = validOtpCode.stringCharacterAtIndex(2)
        viewModel.fourthCode.mutableProperty.value = validOtpCode.stringCharacterAtIndex(3)
        viewModel.fifthCode.mutableProperty.value = validOtpCode.stringCharacterAtIndex(4)
        viewModel.sixthCode.mutableProperty.value = validOtpCode.stringCharacterAtIndex(5)

        XCTAssertEqual(viewModel.otpMessage, "Please enter the one-time PIN (OTP) that we sent to +63 919 111 1111")
    }

    func testSavingsAutoProvisionAction() {
        let request = [SavingsConsentData(type: Constants.Consent.Savings.Terms.type,
                                          version: Constants.Consent.Savings.Terms.version),
                       SavingsConsentData(type: Constants.Consent.Savings.Privacy.type,
                                          version: Constants.Consent.Savings.Privacy.version)]

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Consent.setSavingsConsent(request: request))))
                .thenReturn(getSuccessProducer())
        }

        stub(configurationService) { stub in
            when(stub.autoProvisioningEnabled.get).thenReturn(trueConfig())
        }

        viewModel.savingsAutoProvisionAction?.completed.observeValues({ _ in
            XCTAssertTrue(true, "Expected response")
        })?.addToDisposeBag(disposeBag)

        viewModel.savingsAutoProvisionAction?.errors.observeValues({ _ in
            XCTFail("Unexpected error Encountered")
        })?.addToDisposeBag(disposeBag)

        viewModel.savingsAutoProvisionAction!.apply().start()

        verify(reactiveAPIProvider).request(isEqual(to: API.Consent.setSavingsConsent(request: request)))
    }

    func testShouldLogRefereeMGMAnalyticsCanBeReferredTrueAndMGMFalse() {
        guard let viewModel = viewModel as? MayaRegistrationOTPViewModel else {
            XCTFail("Unexpected error")
            return
        }

        databaseStore.stubFetchUserResult(StoreResult.success(StubUser.stubWithPrefilledData()))
        configurationService.stubRegistrationMGMEnabled(falseConfig())
        XCTAssertFalse(viewModel.shouldLogRefereeMGMAnalytics)
    }

    func testShouldLogRefereeMGMAnalyticsCanBeReferredTrueAndMGMTrue() {
        guard let viewModel = viewModel as? MayaRegistrationOTPViewModel else {
            XCTFail("Unexpected error")
            return
        }

        databaseStore.stubFetchUserResult(StoreResult.success(StubUser.stubWithPrefilledData()))
        configurationService.stubRegistrationMGMEnabled(trueConfig())
        XCTAssertTrue(viewModel.shouldLogRefereeMGMAnalytics)
    }

    func testShouldLogRefereeMGMAnalyticsCanBeReferredFalseAndMGMTrue() {
        guard let viewModel = viewModel as? MayaRegistrationOTPViewModel else {
            XCTFail("Unexpected error")
            return
        }

        databaseStore.stubFetchUserResult(StoreResult.success(StubUser()))
        configurationService.stubRegistrationMGMEnabled(trueConfig())
        XCTAssertTrue(viewModel.shouldLogRefereeMGMAnalytics)
    }

    func testShouldLogRefereeMGMAnalyticsCanBeReferredFalseAndMGMFalse() {
        guard let viewModel = viewModel as? MayaRegistrationOTPViewModel else {
            XCTFail("Unexpected error")
            return
        }

        databaseStore.stubFetchUserResult(StoreResult.success(StubUser()))
        configurationService.stubRegistrationMGMEnabled(falseConfig())
        XCTAssertFalse(viewModel.shouldLogRefereeMGMAnalytics)
    }

    func testIsShortenedRegistrationEnabled() {
        guard let viewModel = viewModel as? MayaRegistrationOTPViewModel else {
            XCTFail("Unexpected error")
            return
        }
        stub(configurationServiceV2) { stub in
            when(stub.shortenedRegistrationEnabled.get).thenReturn(trueConfigV2())
        }
        XCTAssertTrue(viewModel.isShortenedRegistrationEnabled)

        stub(configurationServiceV2) { stub in
            when(stub.shortenedRegistrationEnabled.get).thenReturn(falseConfigV2())
        }
        XCTAssertFalse(viewModel.isShortenedRegistrationEnabled)
    }
}

extension MayaRegistrationOTPViewModelTest {
    func getVerifyBackendError<T>(code: Int = 0) -> SignalProducer<T, Error> {
        let errorResponse = ErrorResponse(code: code, msg: "", spiel: backendErrorSpiel, action: nil)
        let message = Message(error: errorResponse)
        return SignalProducer<T, Error>(error: Error.init(backend: .failed(message)))
    }

    func getVerificationRedirectPayMayaError(errorTitle: String = errorTitle) -> PayMayaError {
        var viewModel = ErrorAlertViewModel(title: errorTitle)
        viewModel.message = backendErrorSpiel
        return PayMayaError(type: .verificationRedirect, viewModel: viewModel)
    }

    func getUserWithPrefilledData(cardProfile: StubCardProfile = StubCardProfile()) -> StubUser {
        let stubProfile = getProfileData()
        let stubMainIdentity = StubMainIdentity(type: "msisdn", value: "+************")
        let stubFund = StubFund(enabled: 1, id: "1234567-abcde", name: "virtual_card", status: FundSourceStatus.virtualCard.rawValue, type: FundSourceType.virtual.rawValue, cardProfile: cardProfile, purchases: nil, user: stubProfile.user)
        return StubUser(id: "user_id", accountStatus: "ACTIVE", canBeReferred: 1, canLinkCard: 1, kycStatus: "kyc1", network: "Smart", physicalCardStatus: "activated", status: "ACTIVE", token: "1234567", hasGovID: 1, activities: nil, backupIdentity: nil, balance: nil, funds: [stubFund], identities: NSSet(array: [stubMainIdentity]), privacyPolicy: StubPrivacyPolicy(version: "v1"), profile: stubProfile, type: nil)
    }

    func getStubAddress() -> StubAddress {
        return StubAddress(city: "San Jose Del Monte", country: "PH", line1: "123", line2: nil, locality: "Highway Hills", state: "NCR", zipCode: "1501", profile: StubProfile())
    }

    func getProfileData() -> StubProfile {
        let stubUser = StubUser()
        let stubAddress = getStubAddress()
        let stubProfile = StubProfile(birthDate: nil, firstName: "John", lastName: "Doe", middleName: nil, address: stubAddress, user: stubUser)
        return stubProfile
    }

    private func stubClearRestrictions() {
        stub(databaseStore) { stub in
            when(stub.remove(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.restriction)), completion: anyOptional())).thenDoNothing()
        }

        stub(userDefaultsStore) { stub in
            when(stub.remove(isEqual(to: UserDefaultsStoreId.restriction), completion: isNil())).thenDoNothing()
        }
    }
}
