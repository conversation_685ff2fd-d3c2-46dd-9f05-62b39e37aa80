//
//  MayaLoginViewModelTest.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON><PERSON><PERSON> on 3/30/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import AssetProvider
import Cuckoo
import Error
import Injector
import MayaCoreData
import Moya
import ReactiveSwift
import StoreProvider
import XCTest

private let errorTitle = L10n.Error.Login.title
private let msisdn = IdentityType.msisdn.rawValue
private let source = Constants.Defaults.Common.source.rawValue
private let maxAttemptsErrorCode = APIConstants.APIStatusCode.loginMaxAttempts.rawValue

private let deviceToken = "Device Token"
private let formattedUsername = "+************"
private let validUsername = "09190000000"
private let maxAttemptsPassword = "MaxAttempts"
private let errorPassword = "asdaasdq"
private let validPassword = "Password1"
private let redirectPassword = "ajhsgdq"
private let transactionType = "login"
private let challengeId = "challengeId123"

class MayaLoginViewModelTest: ViewModelTest {
    private var viewModel: MayaLoginViewModelProtocol!

    private var authenticationProvider: MockAuthenticationProviderManager!
    private var signoutService: MockSignoutService!
    private var touchIDManager: MockTouchIDManager!
    private var locationAuthorizerService: MockLocationAuthorizerServiceProtocol!
    private var flutterEngineManager: MockFlutterEngineManager!

    override func setUp() {
        super.setUp()
        touchIDManager = ContainerWrapper.shared.resolve(TouchIDManager.self) as? MockTouchIDManager
        authenticationProvider = ContainerWrapper.shared.resolve(AuthenticationProviderManager.self) as? MockAuthenticationProviderManager
        signoutService = ContainerWrapper.shared.resolve(SignoutService.self) as? MockSignoutService
        locationAuthorizerService = ContainerWrapper.shared.resolve(
            LocationAuthorizerServiceProtocol.self) as? MockLocationAuthorizerServiceProtocol
        flutterEngineManager = ContainerWrapper.shared.resolve(FlutterEngineManager.self) as? MockFlutterEngineManager

        viewModel = MayaLoginViewModel()

        stub(encryptedDefaultsStore) { stub in
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.device), type: isEqual(to: Device.self) )).thenReturn(StoreResult.success(Device(token: deviceToken)))
        }

        stub(reactiveAPIProvider) { stub in
            let identityRequest = IdentityRequest(type: msisdn,
                                                  value: formattedUsername)
            let maxAttemptsRequest = SessionRequest(identity: identityRequest, password: maxAttemptsPassword, source: source, deviceToken: deviceToken)

            when(stub.request(any(APITargetType.self))).thenReturn(getBackendError())
            when(stub.request(isEqual(to: API.Session.sessions(maxAttemptsRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))).thenReturn(getBackendError(code: maxAttemptsErrorCode))
        }

        stub(signoutService) { stub in
            when(stub.signout(msisdn: anyOptional())).thenReturn(getSuccessProducer().map { _ in })
        }
    }

    func testMaxLoginAttemptsLoginAction() {
        viewModel.usernameField.mutableProperty.value = formattedUsername
        viewModel.passwordField.mutableProperty.value = maxAttemptsPassword

        let identityRequest = IdentityRequest(type: msisdn,
                                              value: formattedUsername)
        let maxAttemptsRequest = SessionRequest(identity: identityRequest, password: maxAttemptsPassword, source: source, deviceToken: deviceToken)

        viewModel.loginAction.completed.observeValues { _ in
            XCTFail("Must not send completed signal")
        }?.addToDisposeBag(disposeBag)

        var errorViewModel = ErrorAlertViewModel(title: CommonStrings.Login.Error.title, message: backendErrorSpiel, closeButtonTitle: CommonStrings.Common.close)
        errorViewModel.primaryActionURL = "paymaya://action"
        errorViewModel.primaryActionTitle = "Action"

        viewModel.loginAction.errors.observeValues { error in
            XCTAssertEqual(error, PayMayaError(type: .loginMaxAttempts, viewModel: errorViewModel))
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Session.sessions(maxAttemptsRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))
        verify(authenticationProvider, never()).saveCredential(credential: anyString())
    }

    func testTouchIDAuthenticateAction() {
        stub(authenticationProvider) { stub in
            when(stub.authenticateUsingTouchId()).thenReturn(getTouchIDResult(success: true))
        }

        viewModel.touchIdAuthenticationAction.values.observeValues { result in
            XCTAssertTrue(result.success)
        }?.addToDisposeBag(disposeBag)

        viewModel.touchIdAuthenticationAction.apply().start()
    }

    func testGetPreviousMobileNumber() {
        stub(encryptedDefaultsStore) { stub in
           let previousAccount = PreviousAccount(msisdn: validUsername)
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.previousAccount), type: isEqual(to: PreviousAccount.self))).thenReturn(StoreResult.success(previousAccount))
        }

        let previousMobileNumber = viewModel.getPreviousMobileNumber()

        XCTAssertEqual(previousMobileNumber, validUsername)
    }

    func testGetPreviousMobileNumberEmpty() {
        stub(encryptedDefaultsStore) { stub in
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.previousAccount), type: isEqual(to: PreviousAccount.self))).thenReturn(StoreResult.failure(Error(database: .readError(reason: "There is no previous mobile number"))))
        }

        let previousMobileNumber = viewModel.getPreviousMobileNumber()

        XCTAssertEqual(previousMobileNumber, nil)
    }

    func testGetSavedCredentials() {
        stub(authenticationProvider) { stub in
            when(stub.retrieveCredential()).thenReturn(deviceToken)
        }

        let credential = viewModel.getSavedCredential()

        XCTAssertNotNil(credential)
    }

    func testTouchIDAllowedForAuthentication() {
        stub(authenticationProvider) { stub in
            when(stub.isTouchIdAllowedForAuthentication()).thenReturn(true)
        }

        XCTAssertTrue(viewModel.isTouchIdAllowedForAuthentication())

        verify(authenticationProvider, times(1)).isTouchIdAllowedForAuthentication()
    }

    func testCanUseTouchID() {
       stub(authenticationProvider) { stub in
            when(stub.canUseTouchId()).thenReturn(true)
       }

        XCTAssertTrue(viewModel.canUseTouchId())

        verify(authenticationProvider, times(1)).canUseTouchId()
    }

    func testUpdateLocalAuthenticationPermission() {
        stub(authenticationProvider) { stub in
            when(stub.saveTouchIdStatus(status: any(Constants.Types.TouchIdStatus.self))).thenDoNothing()
        }

        viewModel.updateLocalAuthenticationPermission(didAllow: true)

        verify(authenticationProvider, times(1)).saveTouchIdStatus(status: isEqual(to: .touchIdAllowed))

        viewModel.updateLocalAuthenticationPermission(didAllow: false)

        verify(authenticationProvider, times(1)).saveTouchIdStatus(status: isEqual(to: .touchIdDisallowed))
    }

    func testGetUserDisplayName() {
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(getUserWithPrefilledData()))
        }

        XCTAssertNotNil(viewModel.getUserDisplayName())
    }

    func testGetEmptyUserDisplayName() {
        XCTAssertNil(viewModel.getUserDisplayName())
    }

    func testGetPrivacyPolicyVersion() {
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(getUserWithPrefilledData()))
        }

        XCTAssertEqual(viewModel.getPrivacyPolicyVersion(), "v1")
    }

    func testGetEmptyPrivacyPolicyVersion() {
        XCTAssertEqual(viewModel.getPrivacyPolicyVersion(), "")
    }

    func testSignoutAction() {
        viewModel.signoutAction.completed.observeValues {
            XCTAssert(true)
        }?.addToDisposeBag(disposeBag)

        viewModel.signoutAction.apply().start()
    }

    func testBiometricType() {
        stub(authenticationProvider) { stub in
            when(stub.touchIdManager.get).thenCallRealImplementation()
        }

        stub(touchIDManager) { stub in
            when(stub.biometricType.get).thenReturn(.touchID)
        }

        XCTAssertEqual(viewModel.biometricType.description, L10n.Settings.Biometrics.touchId)
    }

    func testInlineValidations() {
        viewModel.passwordField.mutableProperty.value = ""
        viewModel.usernameField.mutableProperty.value = ""

        viewModel.validateAction.apply().start()

        XCTAssertEqual("Password is required", viewModel.passwordField.validationMessage)
        XCTAssertEqual("Mobile Number is required", viewModel.usernameField.validationMessage)
    }

    func testClearSessionTokenWhenTokenIsNotNil() {
        let user = getUserWithPrefilledData()
        let userWithoutToken = getUserWithPrefilledData()
        userWithoutToken.token = nil

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
            when(stub.write(isEqual(to: userWithoutToken), options: isEqual(to: DatabaseStore.WritingOptions(DatabaseEntity.user)), completion: isNil())).thenDoNothing()
        }

        viewModel.clearSessionToken()

        verify(databaseStore, times(1)).read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))
        verify(databaseStore, times(1)).write(isEqual(to: userWithoutToken), options: isEqual(to: DatabaseStore.WritingOptions(DatabaseEntity.user)), completion: isNil())
    }

    func testClearSessionTokenWhenTokenIsNil() {
        let userWithoutToken = getUserWithPrefilledData()
        userWithoutToken.token = nil

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(userWithoutToken))
        }

        viewModel.clearSessionToken()

        verify(databaseStore, times(1)).read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))
        verify(databaseStore, never()).write(isEqual(to: userWithoutToken), options: isEqual(to: DatabaseStore.WritingOptions(DatabaseEntity.user)), completion: isNil())
    }

    func testObserveLocationAuthorizerServiceConfigurator() {
        stub(locationAuthorizerService) { stub in
            when(stub.setupService(delegate: anyOptional())).thenDoNothing()
        }

        viewModel.observeLocationAuthorization(delegate: FakeLocationAuthorizerServiceDelegate())

        verify(locationAuthorizerService, times(1)).setupService(delegate: anyOptional())
    }

    func testUnobserveLocationAuthorizerServiceConfigurator() {
        stub(locationAuthorizerService) { stub in
            when(stub.removeDelegate()).thenDoNothing()
        }

        viewModel.unobserveLocationAuthorization()

        verify(locationAuthorizerService, times(1)).removeDelegate()
    }

    func testDestroyLocationAuthorizerServiceConfigurator() {
        stub(locationAuthorizerService) { stub in
            when(stub.destroyService()).thenDoNothing()
        }

        viewModel.destroyLocationAuthorizerService()

        verify(locationAuthorizerService, times(1)).destroyService()
    }

    func testShowMayaIntroductionScreensWithEncryptedStore() {
        stub(encryptedDefaultsStore) { stub in
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.introductionScreensShown), type: isEqual(to: ShownConfig.self))).thenReturn(.success(ShownConfig(hasBeenShown: true)))
        }

        XCTAssertFalse(viewModel.shouldShowMayaIntroductionScreens)

        stub(encryptedDefaultsStore) { stub in
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.introductionScreensShown), type: isEqual(to: ShownConfig.self))).thenReturn(.success(ShownConfig(hasBeenShown: false)))
        }

        XCTAssertTrue(viewModel.shouldShowMayaIntroductionScreens)
    }

    func testShowMayaIntroductionScreensWithEmptyEncryptedStoreAndTrueUserDefaults() {
        stub(encryptedDefaultsStore) { stub in
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.introductionScreensShown), type: isEqual(to: ShownConfig.self))).thenReturn(.failure(.none))
        }
        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.mayaIntroductionScreensShown))).thenReturn(true)
        }
        stub(encryptedDefaultsStore) { stub in
            when(stub.write(isEqual(to: ShownConfig(hasBeenShown: true)), options: isEqual(to: EncryptedDefaultsStoreId.introductionScreensShown), completion: anyOptional())).thenDoNothing()
        }

        XCTAssertFalse(viewModel.shouldShowMayaIntroductionScreens)
    }

    func testShowMayaIntroductionScreensWithEmptyEncryptedStoreAndFalseUserDefaults() {
        stub(encryptedDefaultsStore) { stub in
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.introductionScreensShown), type: isEqual(to: ShownConfig.self))).thenReturn(.failure(.none))
        }
        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.mayaIntroductionScreensShown))).thenReturn(false)
        }
        stub(encryptedDefaultsStore) { stub in
            when(stub.write(isEqual(to: ShownConfig(hasBeenShown: false)), options: isEqual(to: EncryptedDefaultsStoreId.introductionScreensShown), completion: anyOptional())).thenDoNothing()
        }

        XCTAssertTrue(viewModel.shouldShowMayaIntroductionScreens)
    }

    func testShowMayaIntroductionScreensWithEmptyEncryptedStoreAndUserDefaults() {
        stub(encryptedDefaultsStore) { stub in
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.introductionScreensShown), type: isEqual(to: ShownConfig.self))).thenReturn(.failure(.none))
        }
        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.mayaIntroductionScreensShown))).thenReturn(.none)
        }

        XCTAssertTrue(viewModel.shouldShowMayaIntroductionScreens)
    }

    func testShowBiometryButton() {
        stub(authenticationProvider) { stub in
            when(stub.isTouchIdAllowedForAuthentication()).thenReturn(true)
        }
        XCTAssertTrue(viewModel.showBiometryButton)
    }

    func testNeedsLocationAuthorizationWithRefactoredLocationServiceEnabled() {
        stub(encryptedDefaultsStore) { stub in
            when(stub.read(
                isEqual(to: EncryptedDefaultsStoreId.locationPermissionSkipped),
                type: isEqual(to: ShownConfig.self)
            )).thenReturn(.success(.init(hasBeenShown: true)))
        }

        XCTAssertFalse(viewModel.needsLocationAuthorization)

        stub(encryptedDefaultsStore) { stub in
            when(stub.read(
                isEqual(to: EncryptedDefaultsStoreId.locationPermissionSkipped),
                type: isEqual(to: ShownConfig.self)
            )).thenReturn(.failure(Error()))
        }

        stub(locationAuthorizerService) { stub in
            when(stub.authorizationStatus.get).thenReturn(.notDetermined)
        }

        XCTAssertTrue(viewModel.needsLocationAuthorization)
    }

    func testGetUserFirstName() {
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(getUserWithPrefilledData()))
        }
        XCTAssertEqual(viewModel.getUserFirstName(), "John")
    }

    private func stubClearRestrictions() {
        stub(databaseStore) { stub in
            when(stub.remove(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.restriction)), completion: anyOptional())).thenDoNothing()
        }

        stub(userDefaultsStore) { stub in
            when(stub.remove(isEqual(to: UserDefaultsStoreId.restriction), completion: isNil())).thenDoNothing()
        }
    }

    func testLoginMFAChallengeRequired() {
        stub(authenticationProvider) { stub in
            when(stub.saveCredential(credential: anyString())).thenDoNothing()
        }

        self.stubClearRestrictions()

        stub(flutterEngineManager) { stub in
            when(stub.initializeEngines(with: isEqual(to: FlutterModule.allCases))).thenDoNothing()
        }

        viewModel.usernameField.mutableProperty.value = formattedUsername
        viewModel.passwordField.mutableProperty.value = validPassword

        let identityRequest = IdentityRequest(type: msisdn, value: viewModel.usernameField.mutableProperty.value)
        let sessionRequest = SessionRequest(identity: identityRequest, password: viewModel.passwordField.mutableProperty.value, source: source, deviceToken: deviceToken)
        let startChallengeRequest = MFAChallengeStartRequest(method: SessionMFAChallengeMethod.otp.rawValue, params: MFAChallengeStartRequest.Params(deviceData: nil, lifestyleId: nil, type: "otp", value: formattedUsername))

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Session.sessions(sessionRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))).thenReturn(getMFAChallengeRequiredError())
            when(stub.request(isEqual(to: API.MFAChallenge.startChallenge(transactionType: transactionType, challengeId: challengeId, request: startChallengeRequest)), useNoInternetConnectionType: isEqual(to: true)))
                .thenReturn(getSuccessProducer())
        }

        viewModel.loginAction.completed.observeValues { _ in
            let actualChallengeId = self.viewModel.mfaChallengeDetailsProperty.value?.challengeId
            let actualTransactionType = self.viewModel.mfaChallengeDetailsProperty.value?.transactionType
            XCTAssertEqual(actualChallengeId, challengeId)
            XCTAssertEqual(actualTransactionType, transactionType)
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.errors.observeValues { _ in
            XCTFail("UnexpectedFailure")
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Session.sessions(sessionRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MFAChallenge.startChallenge(transactionType: transactionType, challengeId: challengeId, request: startChallengeRequest)), useNoInternetConnectionType: isEqual(to: true))
    }

    func testSessionV5() {
        stub(authenticationProvider) { stub in
            when(stub.saveCredential(credential: anyString())).thenDoNothing()
        }

        self.stubClearRestrictions()

        stub(configurationService) { stub in
            when(stub.restrictedLoginFlowEnabled.get).thenReturn(falseConfig())
        }

        stub(flutterEngineManager) { stub in
            when(stub.initializeEngines(with: isEqual(to: FlutterModule.allCases))).thenDoNothing()
        }

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(getUserWithPrefilledData()))
            when(stub.write(any(), options: any(), completion: any())).thenDoNothing()
        }

        viewModel.usernameField.mutableProperty.value = formattedUsername
        viewModel.passwordField.mutableProperty.value = validPassword

        let identityRequest = IdentityRequest(type: msisdn, value: viewModel.usernameField.mutableProperty.value)
        let sessionRequest = SessionRequest(identity: identityRequest, password: viewModel.passwordField.mutableProperty.value, source: source, deviceToken: deviceToken)
        let startChallengeRequest = MFAChallengeStartRequest(method: SessionMFAChallengeMethod.otp.rawValue, params: MFAChallengeStartRequest.Params(deviceData: nil, lifestyleId: nil, type: "otp", value: formattedUsername))

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Session.sessions(sessionRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))).thenReturn(getSuccessProducer())
        }

        viewModel.loginAction.completed.observeValues { _ in
            let actualChallengeId = self.viewModel.mfaChallengeDetailsProperty.value?.challengeId
            let actualTransactionType = self.viewModel.mfaChallengeDetailsProperty.value?.transactionType
            XCTAssertNil(actualChallengeId)
            XCTAssertNil(actualTransactionType)
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.errors.observeValues { _ in
            XCTFail("UnexpectedFailure")
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.apply().start()

        verify(databaseStore, never()).write(any(), options: isEqual(to: DatabaseStore.WritingOptions(DatabaseEntity.restriction)), completion: any())
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Session.sessions(sessionRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))
        verify(reactiveAPIProvider, never()).request(isEqual(to: API.MFAChallenge.startChallenge(transactionType: transactionType, challengeId: challengeId, request: startChallengeRequest)), useNoInternetConnectionType: isEqual(to: true))
        verify(authenticationProvider).saveCredential(credential: viewModel.passwordField.mutableProperty.value)
        verify(flutterEngineManager, times(1)).initializeEngines(with: isEqual(to: FlutterModule.allCases))
    }

    func testSessionV5RestrictedLoginEnabledAndCustomerRestrictionsEnabled() {
        stub(authenticationProvider) { stub in
            when(stub.saveCredential(credential: anyString())).thenDoNothing()
        }

        self.stubClearRestrictions()

        stub(configurationService) { stub in
            when(stub.restrictedLoginFlowEnabled.get).thenReturn(trueConfig())
        }

        stub(configurationService) { stub in
            when(stub.customerRestrictionsEnabled.get).thenReturn(trueConfig())
        }

        stub(flutterEngineManager) { stub in
            when(stub.initializeEngines(with: isEqual(to: FlutterModule.allCases))).thenDoNothing()
        }

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(getUserWithPrefilledData()))
        }

        viewModel.usernameField.mutableProperty.value = formattedUsername
        viewModel.passwordField.mutableProperty.value = validPassword

        let identityRequest = IdentityRequest(type: msisdn, value: viewModel.usernameField.mutableProperty.value)
        let sessionRequest = SessionRequest(identity: identityRequest, password: viewModel.passwordField.mutableProperty.value, source: source, deviceToken: deviceToken)
        let startChallengeRequest = MFAChallengeStartRequest(method: SessionMFAChallengeMethod.otp.rawValue, params: MFAChallengeStartRequest.Params(deviceData: nil, lifestyleId: nil, type: "otp", value: formattedUsername))
        let restrictionRequest = RestrictionRequest(accountStatus: "ACTIVE")
        let customerRestrictionRequest = API.Restrictions.getCustomerRestrictions(restrictionRequest)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Session.sessions(sessionRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))).thenReturn(getSuccessProducer())
            when(stub.request(isEqual(to: customerRestrictionRequest), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))).thenReturn(getSuccessProducer())
        }

        viewModel.loginAction.completed.observeValues { _ in
            let actualChallengeId = self.viewModel.mfaChallengeDetailsProperty.value?.challengeId
            let actualTransactionType = self.viewModel.mfaChallengeDetailsProperty.value?.transactionType
            XCTAssertNil(actualChallengeId)
            XCTAssertNil(actualTransactionType)
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.errors.observeValues { _ in
            XCTFail("UnexpectedFailure")
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Session.sessions(sessionRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: customerRestrictionRequest), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))
        verify(reactiveAPIProvider, never()).request(isEqual(to: API.MFAChallenge.startChallenge(transactionType: transactionType, challengeId: challengeId, request: startChallengeRequest)), useNoInternetConnectionType: isEqual(to: true))
        verify(authenticationProvider).saveCredential(credential: viewModel.passwordField.mutableProperty.value)
        verify(flutterEngineManager, times(1)).initializeEngines(with: isEqual(to: FlutterModule.allCases))
    }

    func testSessionV5RestrictedLoginEnabledAndCustomerRestrictionsDisabled() {
        stub(authenticationProvider) { stub in
            when(stub.saveCredential(credential: anyString())).thenDoNothing()
        }

        self.stubClearRestrictions()

        stub(configurationService) { stub in
            when(stub.restrictedLoginFlowEnabled.get).thenReturn(trueConfig())
        }

        stub(configurationService) { stub in
            when(stub.customerRestrictionsEnabled.get).thenReturn(falseConfig())
        }

        stub(flutterEngineManager) { stub in
            when(stub.initializeEngines(with: isEqual(to: FlutterModule.allCases))).thenDoNothing()
        }

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(getUserWithPrefilledData()))
        }

        viewModel.usernameField.mutableProperty.value = formattedUsername
        viewModel.passwordField.mutableProperty.value = validPassword

        let identityRequest = IdentityRequest(type: msisdn, value: viewModel.usernameField.mutableProperty.value)
        let sessionRequest = SessionRequest(identity: identityRequest, password: viewModel.passwordField.mutableProperty.value, source: source, deviceToken: deviceToken)
        let startChallengeRequest = MFAChallengeStartRequest(method: SessionMFAChallengeMethod.otp.rawValue, params: MFAChallengeStartRequest.Params(deviceData: nil, lifestyleId: nil, type: "otp", value: formattedUsername))
        let accountRestrictionRequest = API.Restrictions.getAccountRestrictions

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Session.sessions(sessionRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))).thenReturn(getSuccessProducer())
            when(stub.request(isEqual(to: accountRestrictionRequest), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))).thenReturn(getSuccessProducer())
        }

        viewModel.loginAction.completed.observeValues { _ in
            let actualChallengeId = self.viewModel.mfaChallengeDetailsProperty.value?.challengeId
            let actualTransactionType = self.viewModel.mfaChallengeDetailsProperty.value?.transactionType
            XCTAssertNil(actualChallengeId)
            XCTAssertNil(actualTransactionType)
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.errors.observeValues { _ in
            XCTFail("UnexpectedFailure")
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Session.sessions(sessionRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: accountRestrictionRequest), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))
        verify(reactiveAPIProvider, never()).request(isEqual(to: API.MFAChallenge.startChallenge(transactionType: transactionType, challengeId: challengeId, request: startChallengeRequest)), useNoInternetConnectionType: isEqual(to: true))
        verify(authenticationProvider).saveCredential(credential: viewModel.passwordField.mutableProperty.value)
        verify(flutterEngineManager, times(1)).initializeEngines(with: isEqual(to: FlutterModule.allCases))
    }

    func testSessionV5Failed() {
        stub(authenticationProvider) { stub in
            when(stub.saveCredential(credential: anyString())).thenDoNothing()
        }

        stub(flutterEngineManager) { stub in
            when(stub.initializeEngines(with: isEqual(to: FlutterModule.allCases))).thenDoNothing()
        }

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(getUserWithPrefilledData()))
        }

        viewModel.usernameField.mutableProperty.value = formattedUsername
        viewModel.passwordField.mutableProperty.value = validPassword

        let identityRequest = IdentityRequest(type: msisdn, value: viewModel.usernameField.mutableProperty.value)
        let sessionRequest = SessionRequest(identity: identityRequest, password: viewModel.passwordField.mutableProperty.value, source: source, deviceToken: deviceToken)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Session.sessions(sessionRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))).thenReturn(getBackendError())
        }

        viewModel.loginAction.completed.observeValues { _ in
            XCTFail("UnexpectedFailure")
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.errors.observeValues { error in
            XCTAssertNotNil(error)
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Session.sessions(sessionRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))
    }

    func testLoginStartChallengeFailed() {
        stub(authenticationProvider) { stub in
            when(stub.saveCredential(credential: anyString())).thenDoNothing()
        }

        stub(flutterEngineManager) { stub in
            when(stub.initializeEngines(with: isEqual(to: FlutterModule.allCases))).thenDoNothing()
        }

        viewModel.usernameField.mutableProperty.value = formattedUsername
        viewModel.passwordField.mutableProperty.value = validPassword

        let identityRequest = IdentityRequest(type: msisdn, value: viewModel.usernameField.mutableProperty.value)
        let sessionRequest = SessionRequest(identity: identityRequest, password: viewModel.passwordField.mutableProperty.value, source: source, deviceToken: deviceToken)
        let startChallengeRequest = MFAChallengeStartRequest(method: SessionMFAChallengeMethod.otp.rawValue, params: MFAChallengeStartRequest.Params(deviceData: nil, lifestyleId: nil, type: "otp", value: formattedUsername))

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Session.sessions(sessionRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))).thenReturn(getMFAChallengeRequiredError())

            when(stub.request(isEqual(to: API.MFAChallenge.startChallenge(transactionType: transactionType, challengeId: challengeId, request: startChallengeRequest)), useNoInternetConnectionType: isEqual(to: true)))
                .thenReturn(getBackendError())
        }

        viewModel.loginAction.completed.observeValues { _ in
            XCTFail("UnexpectedFailure")
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.errors.observeValues { error in
            XCTAssertNotNil(error)
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Session.sessions(sessionRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MFAChallenge.startChallenge(transactionType: transactionType, challengeId: challengeId, request: startChallengeRequest)), useNoInternetConnectionType: isEqual(to: true))
    }

    func testLoginShieldSpecialErrorHandling() {
        stub(authenticationProvider) { stub in
            when(stub.saveCredential(credential: anyString())).thenDoNothing()
        }

        stub(flutterEngineManager) { stub in
            when(stub.initializeEngines(with: isEqual(to: FlutterModule.allCases))).thenDoNothing()
        }

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(getUserWithPrefilledData()))
        }

        viewModel.usernameField.mutableProperty.value = formattedUsername
        viewModel.passwordField.mutableProperty.value = validPassword

        let identityRequest = IdentityRequest(type: msisdn, value: viewModel.usernameField.mutableProperty.value)
        let sessionRequest = SessionRequest(identity: identityRequest, password: viewModel.passwordField.mutableProperty.value, source: source, deviceToken: deviceToken)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Session.sessions(sessionRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))).thenReturn(getBackendError(code: -395))
        }

        viewModel.loginAction.completed.observeValues { _ in
            XCTFail("UnexpectedFailure")
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.errors.observeValues { error in
            XCTAssertNotNil(error.viewModel)
            let errorViewModel = error.viewModel!
            XCTAssertEqual(errorViewModel.title, L10n.Error.Shield.Finger.Printing.title)
            XCTAssertEqual(errorViewModel.message, L10n.Error.Spiel.Shield.FingerPrinting.Special.message)
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Session.sessions(sessionRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))
        verify(authenticationProvider, never()).saveCredential(credential: anyString())
    }

    func testLoginShieldStandardErrorHandling() {
        viewModel.usernameField.mutableProperty.value = formattedUsername
        viewModel.passwordField.mutableProperty.value = errorPassword

        let identityRequest = IdentityRequest(type: msisdn, value: viewModel.usernameField.mutableProperty.value)
        let sessionRequest = SessionRequest(identity: identityRequest, password: viewModel.passwordField.mutableProperty.value, source: source, deviceToken: deviceToken)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Session.sessions(sessionRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))).thenReturn(getBackendError(code: -394))
        }

        viewModel.loginAction.completed.observeValues { _ in
            XCTFail("Must not send completed signal")
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.errors.observeValues { error in
            XCTAssertNotNil(error.viewModel)
            let errorViewModel = error.viewModel!
            XCTAssertEqual(errorViewModel.title, L10n.Error.Shield.Finger.Printing.title)
            XCTAssertEqual(errorViewModel.message, L10n.Error.Spiel.Shield.FingerPrinting.Standard.message)
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Session.sessions(sessionRequest)), ignoresQueue: isEqual(to: true), useNoInternetConnectionType: isEqual(to: true))
        verify(authenticationProvider, never()).saveCredential(credential: anyString())
    }
}

private class FakeLocationAuthorizerServiceDelegate: LocationAuthorizerServiceDelegate {
    func didDenyLocationAuthorization(didShowPrompt: Bool) {}
    func didAcceptLocationAuthorization(didShowPrompt: Bool) {}
    func didNotDetermineLocationAuthorization() {}
}

extension MayaLoginViewModelTest {
    func isEqual(to value: StubUser) -> ParameterMatcher<DatabaseStorable> {
        return ParameterMatcher<DatabaseStorable> {
            guard let user = $0 as? StubUser else { return false }
            return user.id == value.id && user.token == value.token
        }
    }

    func getUserWithPrefilledData(cardProfile: StubCardProfile = StubCardProfile()) -> StubUser {
        let stubProfile = getProfileData()
        let stubMainIdentity = StubMainIdentity(type: "msisdn", value: "+************")
        let stubFund = StubFund(enabled: 1, id: "1234567-abcde", name: "virtual_card", status: FundSourceStatus.virtualCard.rawValue, type: FundSourceType.virtual.rawValue, cardProfile: cardProfile, purchases: nil, user: stubProfile.user)
        return StubUser(id: "user_id", accountStatus: "ACTIVE", canBeReferred: 1, canLinkCard: 1, kycStatus: "kyc1", network: "Smart", physicalCardStatus: "activated", status: "ACTIVE", token: "1234567", hasGovID: 1, activities: nil, backupIdentity: nil, balance: nil, funds: [stubFund], identities: NSSet(array: [stubMainIdentity]), privacyPolicy: StubPrivacyPolicy(version: "v1"), profile: stubProfile, type: nil)
    }

    func getStubAddress() -> StubAddress {
        return StubAddress(city: "San Jose Del Monte", country: "PH", line1: "123", line2: nil, locality: "Highway Hills", state: "NCR", zipCode: "1501", profile: StubProfile())
    }

    func getProfileData() -> StubProfile {
        let stubUser = StubUser()
        let stubAddress = getStubAddress()
        let stubProfile = StubProfile(birthDate: nil, firstName: "John", lastName: "Doe", middleName: nil, address: stubAddress, user: stubUser)
        return stubProfile
    }

    func getMFAChallengeRequiredError<T>() -> SignalProducer<T, Error> {
        let errorResponse = ErrorResponse(code: -384, msg: "OTP required", spiel: backendErrorSpiel, action: ErrorAction(title: "Action", url: "paymaya://action"))
        let params = MFAChallengeRequiredResponse.Meta.Params(type: "otp", lifestyleId: nil)
        let meta = MFAChallengeRequiredResponse.Meta(method: "otp", transactionType: transactionType, challendId: challengeId, params: params, client: "client")
        return SignalProducer<T, Error>(error: Error.init(backend: .mfaChallengeRequired(MFAChallengeRequiredResponse(error: errorResponse, meta: meta))))
    }
}
