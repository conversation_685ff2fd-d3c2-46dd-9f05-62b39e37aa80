//
//  MayaLandingViewModelTest.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON><PERSON> on 11/20/20.
//  Copyright © 2020 PayMaya Philippines, Inc. All rights reserved.
//

import Cuckoo
import Injector
import StoreProvider
import XCTest

class MayaLandingViewModelTest: ViewModelTest {
    var viewModel: MayaLandingViewModelProtocol!
    private var locationAuthorizerService: MockLocationAuthorizerServiceProtocol!
    private var locationCheckerService: MockLocationCheckerServiceProtocol!

    override func setUp() {
        super.setUp()

        locationAuthorizerService = ContainerWrapper.shared.resolve(
            LocationAuthorizerServiceProtocol.self) as? MockLocationAuthorizerServiceProtocol
        locationCheckerService = ContainerWrapper.shared.resolve(
            LocationCheckerServiceProtocol.self) as? MockLocationCheckerServiceProtocol

        viewModel = MayaLandingViewModel()
    }

    func testInitial() {
        XCTAssert(viewModel != nil, "This view model is blank, no testing needed")
    }

    func testObserveLocationAuthorizerServiceConfigurator() {
        stub(locationAuthorizerService) { stub in
            when(stub.setupService(delegate: anyOptional())).thenDoNothing()
        }

        viewModel.observeLocationAuthorization(delegate: FakeLocationAuthorizerServiceDelegate())

        verify(locationAuthorizerService, times(1)).setupService(delegate: anyOptional())
    }

    func testUnobserveLocationAuthorizerServiceConfigurator() {
        stub(locationAuthorizerService) { stub in
            when(stub.removeDelegate()).thenDoNothing()
        }

        viewModel.unobserveLocationAuthorization()

        verify(locationAuthorizerService, times(1)).removeDelegate()
    }

    func testDestroyLocationAuthorizerServiceConfigurator() {
        stub(locationAuthorizerService) { stub in
            when(stub.destroyService()).thenDoNothing()
        }

        viewModel.destroyLocationAuthorizerService()

        verify(locationAuthorizerService, times(1)).destroyService()
    }

    func testObserveLocationCheckerServiceConfigurator() {
        stub(locationCheckerService) { stub in
            when(stub.setupService()).thenDoNothing()
        }

        viewModel.observeLocationChecking()

        verify(locationCheckerService, times(1)).setupService()
    }

    func testDestroyLocationCheckerServiceConfigurator() {
        stub(locationCheckerService) { stub in
            when(stub.destroyService()).thenDoNothing()
        }

        viewModel.destroyLocationCheckerService()

        verify(locationCheckerService, times(1)).destroyService()
    }

    func testShowMayaIntroductionScreensWithEncryptedStore() {
        stub(encryptedDefaultsStore) { stub in
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.introductionScreensShown), type: isEqual(to: ShownConfig.self))).thenReturn(.success(ShownConfig(hasBeenShown: true)))
        }

        XCTAssertFalse(viewModel.shouldShowMayaIntroductionScreens)

        stub(encryptedDefaultsStore) { stub in
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.introductionScreensShown), type: isEqual(to: ShownConfig.self))).thenReturn(.success(ShownConfig(hasBeenShown: false)))
        }

        XCTAssertTrue(viewModel.shouldShowMayaIntroductionScreens)
    }

    func testShowMayaIntroductionScreensWithEmptyEncryptedStoreAndTrueUserDefaults() {
        stub(encryptedDefaultsStore) { stub in
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.introductionScreensShown), type: isEqual(to: ShownConfig.self))).thenReturn(.failure(.none))
        }
        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.mayaIntroductionScreensShown))).thenReturn(true)
        }
        stub(encryptedDefaultsStore) { stub in
            when(stub.write(isEqual(to: ShownConfig(hasBeenShown: true)), options: isEqual(to: EncryptedDefaultsStoreId.introductionScreensShown), completion: anyOptional())).thenDoNothing()
        }

        XCTAssertFalse(viewModel.shouldShowMayaIntroductionScreens)
    }

    func testShowMayaIntroductionScreensWithEmptyEncryptedStoreAndFalseUserDefaults() {
        stub(encryptedDefaultsStore) { stub in
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.introductionScreensShown), type: isEqual(to: ShownConfig.self))).thenReturn(.failure(.none))
        }
        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.mayaIntroductionScreensShown))).thenReturn(false)
        }
        stub(encryptedDefaultsStore) { stub in
            when(stub.write(isEqual(to: ShownConfig(hasBeenShown: false)), options: isEqual(to: EncryptedDefaultsStoreId.introductionScreensShown), completion: anyOptional())).thenDoNothing()
        }

        XCTAssertTrue(viewModel.shouldShowMayaIntroductionScreens)
    }

    func testShowMayaIntroductionScreensWithEmptyEncryptedStoreAndUserDefaults() {
        stub(encryptedDefaultsStore) { stub in
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.introductionScreensShown), type: isEqual(to: ShownConfig.self))).thenReturn(.failure(.none))
        }
        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.mayaIntroductionScreensShown))).thenReturn(.none)
        }

        XCTAssertTrue(viewModel.shouldShowMayaIntroductionScreens)
    }

    func testIsShortenedRegistrationEnabled() {
        stub(configurationServiceV2) { stub in
            when(stub.shortenedRegistrationEnabled.get).thenReturn(trueConfigV2())
        }
        XCTAssertTrue(viewModel.isShortenedRegistrationEnabled)

        stub(configurationServiceV2) { stub in
            when(stub.shortenedRegistrationEnabled.get).thenReturn(falseConfigV2())
        }
        XCTAssertFalse(viewModel.isShortenedRegistrationEnabled)
    }

    func testIsLandingPageV2Enabled() {
        stub(configurationServiceV2) { stub in
            when(stub.landingPageV2Enabled.get).thenReturn(trueConfigV2())
        }
        XCTAssertTrue(viewModel.isLandingPageV2Enabled)

        stub(configurationServiceV2) { stub in
            when(stub.landingPageV2Enabled.get).thenReturn(falseConfigV2())
        }
        XCTAssertFalse(viewModel.isLandingPageV2Enabled)
    }
}

private class FakeLocationAuthorizerServiceDelegate: LocationAuthorizerServiceDelegate {
    func didDenyLocationAuthorization(didShowPrompt: Bool) {}
    func didAcceptLocationAuthorization(didShowPrompt: Bool) {}
    func didNotDetermineLocationAuthorization() {}
}
