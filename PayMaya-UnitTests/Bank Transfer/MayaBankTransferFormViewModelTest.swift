//
//  MayaBankTransferFormViewModelTest.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON> on 5/31/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import AssetProvider
import Cuckoo
import Error
import MayaCoreData
import Moya
import ReactiveSwift
import StoreProvider
import XCTest

private let accountName = "Fake User Number One"
private let accountNumber = "***********"
private let amount = "100.00"
private let bankDetails = MayaBankDetails(identifier: "bpi2", name: "BPI")
private let currency = "PHP"
private let instapay = "INSTAPAY"
private let purpose = "What is the meaning of life?"

private let qrDetails = QRDetails(
    creditAccountNumber: "*********",
    globalUniqueIdentifier: "bdo1",
    acquirerId: "123123",
    paymentType: "com.p2pqrpay",
    merchantCity: "Manila",
    merchantName: "Nice Name",
    merchantCategoryCode: "1234",
    additionalData: QRAdditionalData(
        billNumber: nil,
        additionalMobileNumber: nil,
        storeName: nil,
        loyaltyNumber: nil,
        referenceId: nil,
        customerLabel: nil,
        terminalId: "terminal",
        purpose: nil,
        additionalRequestData: nil
    )
)
private let bankTransferQR = BankTransferQR(details: qrDetails)!
private let bdoBank = StubBank(code: "bdo", name: "BDO", identifier: "bdo1", isActive: NSNumber(value: true))
private let bpiBank = StubBank(code: "bpi", name: "BPI", identifier: "bpi2", isActive: NSNumber(value: true))
private let createdTransfer = CreatedBankTransferV3(id: "1", status: "CREATED", fees: CreatedBankTransferV3.Amount(currency: currency, value: 15), authChallengeId: "fake-challenge-id")
private let expectedConfirmationViewItem = MayaConfirmationViewItem(accountName: accountName, accountNumber: accountNumber, bankName: bankDetails.name, amount: amount, currency: currency, purpose: purpose, createdBankTransfer: createdTransfer, bankIC: "bpi2")
private let expectedConfirmationViewItemFromQR = MayaConfirmationViewItem(accountName: bankTransferQR.accountName!, accountNumber: bankTransferQR.account, bankName: bdoBank.name, amount: amount, currency: currency, purpose: purpose, createdBankTransfer: createdTransfer, bankIC: "bdo1")
private let item1 = FavoriteItem(id: "1", alias: "Krischu deposit", type: "bank", contentPreview: FavoriteItem.ContentPreview(text1: "Krischu ipon", text2: "BDO", text3: "savings", media: nil, meta: nil), template: nil)

class MayaBankTransferFormViewModelTest: ViewModelTest {
    var viewModel: MayaBankTransferFormViewModelProtocol!

    override func setUp() {
        super.setUp()
        stub(databaseStore) { stub in
            let balance = StubBalance()
            balance.available = StubAvailableAmount(currency: currency, value: 10000.00)

            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.balance)))).thenReturn(StoreResult.success(balance))
            when(stub.fetch(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.bank, predicate: nil, sort: [NSSortDescriptor(key: "name", ascending: true)])), limit: any())).thenReturn(StoreResult.success([bdoBank, bpiBank]))
        }

        stub(configurationService) { stub in
            when(stub.bankTransferFee.get).thenReturn(10.0)
        }

        viewModel = MayaBankTransferFormViewModel(bankDetails: bankDetails, favoriteItemID: nil)
        viewModel.amountProperty.mutableProperty.value = amount
        viewModel.purposeProperty.mutableProperty.value = purpose
        viewModel.accountNameProperty.mutableProperty.value = accountName
        viewModel.accountNumberProperty.mutableProperty.value = accountNumber
    }

    func testCreateTransferActionSuccess() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.BankTransfer.createV3(getCreatedRequest())), type: isEqual(to: CreatedBankTransferV3.self))).thenReturn(getSuccessProducer(createdTransfer))
        }

        viewModel.createAction.errors.observeValues { _ in
            XCTFail("Unexpected error occured")
        }?.addToDisposeBag(disposeBag)

        viewModel.createAction.values.observeValues { item in
            XCTAssertEqual(item, expectedConfirmationViewItem)
        }?.addToDisposeBag(disposeBag)

        viewModel.createAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.BankTransfer.createV3(getCreatedRequest())), type: isEqual(to: CreatedBankTransferV3.self))
    }

    func testCreateTransferActionFailedAPI() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.BankTransfer.createV3(getCreatedRequest())), type: isEqual(to: CreatedBankTransferV3.self))).thenReturn(getBackendError())
        }

        viewModel.createAction.errors.observeValues { error in
            XCTAssertEqual(error, self.getPayMayaError(CommonStrings.Maya.Error.Default.title))
        }?.addToDisposeBag(disposeBag)

        viewModel.createAction.values.observeValues { _ in
            XCTFail("Unexpected completion occured")
        }?.addToDisposeBag(disposeBag)

        viewModel.createAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.BankTransfer.createV3(getCreatedRequest())), type: isEqual(to: CreatedBankTransferV3.self))
    }

    func testCreateTransferActionSessionTimeout() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.BankTransfer.createV3(getCreatedRequest())), type: isEqual(to: CreatedBankTransferV3.self))).thenReturn(getSessionTimeoutError())
        }

        viewModel.createAction.errors.observeValues { error in
            XCTAssertEqual(error.type, .sessionTimeout(backendErrorSpiel))
        }?.addToDisposeBag(disposeBag)

        viewModel.createAction.values.observeValues { _ in
            XCTFail("Unexpected completion occured")
        }?.addToDisposeBag(disposeBag)

        viewModel.createAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.BankTransfer.createV3(getCreatedRequest())), type: isEqual(to: CreatedBankTransferV3.self))
    }

    func testInlineValidations() {
        XCTAssertTrue(viewModel.validatingProperty.value)
        XCTAssertEqual(viewModel.purposeProperty.inlineValidationMessage, "")
        XCTAssertEqual(viewModel.accountNameProperty.inlineValidationMessage, "")
        XCTAssertEqual(viewModel.accountNumberProperty.inlineValidationMessage, "")
        XCTAssertEqual(viewModel.amountProperty.inlineValidationMessage, "")

        viewModel.purposeProperty.mutableProperty.value = ""
        XCTAssertFalse(viewModel.validatingProperty.value)
        XCTAssertEqual(viewModel.purposeProperty.inlineValidationMessage, "Purpose is required")
        viewModel.purposeProperty.mutableProperty.value = purpose

        viewModel.accountNameProperty.mutableProperty.value = ""
        XCTAssertFalse(viewModel.validatingProperty.value)
        XCTAssertEqual(viewModel.accountNameProperty.inlineValidationMessage, "Account name is required")
        viewModel.accountNameProperty.mutableProperty.value = accountName

        viewModel.accountNumberProperty.mutableProperty.value = ""
        XCTAssertFalse(viewModel.validatingProperty.value)
        XCTAssertEqual(viewModel.accountNumberProperty.inlineValidationMessage, "Account number is required")
        viewModel.accountNumberProperty.mutableProperty.value = accountNumber

        viewModel.amountProperty.mutableProperty.value = ""
        XCTAssertFalse(viewModel.validatingProperty.value)
        XCTAssertEqual(viewModel.amountProperty.inlineValidationMessage, "Amount is required. You have ₱10,000.00 in your wallet.")

        viewModel.amountProperty.mutableProperty.value = "1,000,000.00"
        XCTAssertFalse(viewModel.validatingProperty.value)
        let bankTransferFee = formatter.number.currencyStringValue(decimal: configurationService.bankTransferFee ?? Decimal.zero)
        XCTAssertEqual(viewModel.amountProperty.inlineValidationMessage, "You only have ₱10,000.00 in your wallet.\n₱\(bankTransferFee) transaction fee applies.")

        stub(configurationService) { stub in
                  when(stub.bankTransferFee.get).thenReturn(0.0)
        }
        viewModel.amountProperty.mutableProperty.value = "1,000,000.00"
        XCTAssertFalse(viewModel.validatingProperty.value)
        XCTAssertEqual(viewModel.amountProperty.inlineValidationMessage, "You only have ₱10,000.00 in your wallet.\nTransaction fee may apply.")
    }

    func testSetBankWithIdentifier() {
        viewModel.setBankWithIdentifier("bdo1")
        XCTAssertEqual(viewModel.bankNameProperty.value, bdoBank.name)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.BankTransfer.createV3(getCreatedRequest(bdoBank.identifier))), type: isEqual(to: CreatedBankTransferV3.self))).thenReturn(getBackendError())
        }

        viewModel.createAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.BankTransfer.createV3(getCreatedRequest(bdoBank.identifier))), type: isEqual(to: CreatedBankTransferV3.self))
    }

    func testSetBankWithCode() {
        viewModel.setBankWithCode("bpi")
        XCTAssertEqual(viewModel.bankNameProperty.value, bpiBank.name)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.BankTransfer.createV3(getCreatedRequest(bpiBank.identifier))), type: isEqual(to: CreatedBankTransferV3.self))).thenReturn(getBackendError())
        }

        viewModel.createAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.BankTransfer.createV3(getCreatedRequest(bpiBank.identifier))), type: isEqual(to: CreatedBankTransferV3.self))
    }

    func testBalanceAction() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Balance.current))).thenReturn(getSuccessProducer())
        }

        viewModel.balanceAction.errors.observeValues { _ in
            XCTFail("Unexpected error occured")
        }?.addToDisposeBag(disposeBag)

        viewModel.balanceAction.completed.observeValues { [weak self] in
            guard let self = self
            else {
                XCTFail("Unexpected Error")
                return
            }

            XCTAssertEqual(self.viewModel.availableBalanceText, "You have ₱10,000.00 in your wallet.")
        }?.addToDisposeBag(disposeBag)

        viewModel.balanceAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Balance.current))
    }

    func testAdditionalDataFromQR() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.BankTransfer.createV3(getCreatedRequestFromQR())), type: isEqual(to: CreatedBankTransferV3.self))).thenReturn(getSuccessProducer(createdTransfer))
        }

        viewModel.createAction.errors.observeValues { _ in
            XCTFail("Unexpected error occured")
        }?.addToDisposeBag(disposeBag)

        viewModel.createAction.values.observeValues { item in
            XCTAssertEqual(item, expectedConfirmationViewItemFromQR)
        }?.addToDisposeBag(disposeBag)

        viewModel.setAdditionalData(from: bankTransferQR)
        viewModel.setBankWithIdentifier("bdo1")

        viewModel.accountNameProperty.mutableProperty.value = bankTransferQR.accountName!
        viewModel.accountNumberProperty.mutableProperty.value = bankTransferQR.account

        viewModel.createAction.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.BankTransfer.createV3(getCreatedRequestFromQR())), type: isEqual(to: CreatedBankTransferV3.self))
    }

    func testGetFavoriteDetailsAction() {
        let favoriteItemId = "0001"
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Favorites.getDetails(favoriteItemId)), type: isEqualIdentity(to: FavoriteItem.self))).thenReturn(getSuccessProducer(item1))
        }

        viewModel = MayaBankTransferFormViewModel(bankDetails: bankDetails, favoriteItemID: favoriteItemId)

        viewModel.favoriteDetailsAction.errors.observeValues { error in
            XCTAssertNil(error, "Unexpected Error Signal received")
        }?.addToDisposeBag(disposeBag)

        viewModel.favoriteDetailsAction.values.observeValues { favoriteItem in
            XCTAssertEqual(favoriteItem, item1)
        }?.addToDisposeBag(disposeBag)

        viewModel.favoriteDetailsAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Favorites.getDetails(favoriteItemId)), type: isEqualIdentity(to: FavoriteItem.self))
    }

    func testGetFavoriteDetailsActionDefaultError() {
        let favoriteItemId = "0001"
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Favorites.getDetails(favoriteItemId)), type: isEqualIdentity(to: FavoriteItem.self))).thenReturn((getUnknownError()))
        }

        viewModel = MayaBankTransferFormViewModel(bankDetails: bankDetails, favoriteItemID: favoriteItemId)

        viewModel.favoriteDetailsAction.errors.observeValues { error in
            XCTAssertEqual(error, self.getPayMayaUnknownError(CommonStrings.Maya.Error.Default.title))
        }?.addToDisposeBag(disposeBag)

        viewModel.favoriteDetailsAction.completed.observeValues {
            XCTFail("Unexpected Completed Signal received")
        }?.addToDisposeBag(disposeBag)

        viewModel.favoriteDetailsAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Favorites.getDetails(favoriteItemId)), type: isEqualIdentity(to: FavoriteItem.self))
    }
}

private extension MayaBankTransferFormViewModelTest {
    func getCreatedRequest(_ identifier: String? = nil) -> CreateV3BankTransferRequest {
        let recipient = CreateV3BankTransferRequest.Recipient(type: instapay, account: accountNumber, accountName: accountName, institutionCode: identifier ?? bankDetails.identifier)
        let amountRequest = AmountRequest(currency: currency, value: Double(amount)!)
        return CreateV3BankTransferRequest(recipient: recipient, amount: amountRequest, purpose: purpose, additionalData: nil)
    }

    func getCreatedRequestFromQR() -> CreateV3BankTransferRequest {
        let recipient = CreateV3BankTransferRequest.Recipient(type: instapay, account: bankTransferQR.account, accountName: bankTransferQR.accountName!, institutionCode: bdoBank.identifier ?? "")
        let amountRequest = AmountRequest(currency: currency, value: Double(amount)!)
        let additionalData = CreateV3BankTransferRequest.AdditionalData(
            paymentType: bankTransferQR.paymentType,
            uniqueID: bankTransferQR.uniqueID,
            merchantMobileNumber: bankTransferQR.merchantMobileNumber,
            loyaltyNumber: bankTransferQR.loyaltyNumber,
            merchantCategoryCode: bankTransferQR.merchantCategoryCode,
            merchantCity: bankTransferQR.merchantCity,
            merchantPostalCode: bankTransferQR.merchantPostalCode,
            billNumber: bankTransferQR.billNumber,
            additionalMobileNumber: bankTransferQR.additionalMobileNumber,
            storeLabel: bankTransferQR.storeLabel,
            referenceLabel: bankTransferQR.referenceLabel,
            customerLabel: bankTransferQR.customerLabel,
            terminalLabel: bankTransferQR.terminalLabel,
            additionalRequestData: bankTransferQR.additionalRequestData
        )
        return CreateV3BankTransferRequest(recipient: recipient, amount: amountRequest, purpose: purpose, additionalData: additionalData)
    }
}
