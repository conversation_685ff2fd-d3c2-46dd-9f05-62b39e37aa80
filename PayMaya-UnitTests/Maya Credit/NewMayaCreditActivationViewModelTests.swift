//
//  NewMayaCreditActivationViewModel.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 27/04/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import AssetProvider
import ConfigurationService
import Cuckoo
import Foundation
import Injector
import MayaCoreData
@testable import RavenLocation
import ReactiveSwift
import StoreProvider
import XCTest

private let errorTitle = CommonStrings.Maya.Error.Default.title
private let agreement1 = MayaCreditAgreement(key: "key1", heading: "heading1", subheading: "subheading1", contents: "contents1", mandatory: true)
private let agreement2 = MayaCreditAgreement(key: "key2", heading: "heading2", subheading: "subheading2", contents: "contents2", mandatory: true)
private let agreement3 = MayaCreditAgreement(key: "key3", heading: "heading3", subheading: "subheading3", contents: "contents3", mandatory: false)

private let mayaCreditContactReference = MayaCreditContactReference(customerId: "934b58fa-1cbe-494c-aacb-5e7579d7aea1", firstName: "Ethel", lastName: "Velaryon", alternativeMobileNumber: nil, alternativeLandlineNumber: nil, gender: "MALE", maritalStatus: "SINGLE", contactReference: MayaCreditContactReference.ContactReference(firstName: "Arya", lastName: "Stark", relationship: "SIBLING", mobileNumber: "09000003542"))

private let contactReferenceRequest = MayaCreditSubmitContactReferenceRequest.ContactReference(firstName: "Arya", lastName: "Stark", relationship: "SIBLING", mobileNumber: "09000003542")
private let submitContactReferenceRequest = MayaCreditSubmitContactReferenceRequest(alternativeMobileNumber: nil, gender: "MALE", maritalStatus: "SINGLE", contactReference: contactReferenceRequest)
private let contactReferenceResponse = MayaCreditContactReferencePatchResponse.ContactReference(id: "04a3755e-6557-4f9f-aa2d-09809b05c3ef")
private let patchResponse = MayaCreditContactReferencePatchResponse(contactReference: contactReferenceResponse)

private let deviceId = "abb86e81d3194a54afd7c6ed6f0cb2d6"
private let customerId = "2fc10b52-f668-41d5-9363-91b3209b14ee"
private let osVersion = "16.2"
private let device = EligibilityRequest.Device(id: deviceId, channel: DeviceType.kind, version: osVersion)
private let initialEligibilityTerm = MayaCreditInitialEligibility.Term(scheduleSettings: MayaCreditScheduleSettings(gracePeriodSettings: MayaCreditScheduleSettings.GracePeriodSettings(type: "", unit: "", value: 15), billingSettings: MayaCreditScheduleSettings.BillingSettings(periodEndDayOfMonth: MayaCreditScheduleSettings.BillingSettings.PeriodEndDayOfMonth(minValue: 1, maxValue: 23, days: [1, 2, 3]))))
private let personDetails = MayaCreditPersonDetails(alternativeMobileNumber: "09000004088", gender: "FEMALE", maritalStatus: "MARRIED", contactReference: MayaCreditPersonDetails.ContactReference(firstName: "Arya", lastName: "Stark", relationship: "SIBLING", mobileNumber: "09000004099"), mothersMaidenName: MayaCreditPersonDetails.MothersMaidenName(firstName: "Catelyn", middleName: nil, lastName: "Last", hasNoMiddleName: true))
private let personDetailsPatchResponse = MayaCreditPersonDetailsPatchResponse(contactReference: nil, gender: nil, maritalStatus: nil, contact: nil, mothersMaidenName: nil)

private let creditEligibilityModel = MayaCreditEligibility(consents: [], term: nil, id: "3CF92742-FFBA-4978-989A-E17D2D4F8B91", product: "MAYA_CREDIT_CONSUMER_LOAN", status: "PARTIAL_UNDERWRITING", expiresOn: "2025-05-21T02:39:59.084820332Z", riskLevel: "HIGH")

class NewMayaCreditActivationViewModelTest: ViewModelTest {
    var viewModel: NewMayaCreditActivationViewModelProtocol!
    private var mockRavenLocation: RavenLocation.Location!

    override func setUp() {
        super.setUp()
        mockRavenLocation = RavenLocation.Location(latitude: 10.0, longitude: 10.0, isMock: true)

        stub(ravenActionProvider) { stub in
            when(stub.makeGetGeolocationAction()).then {
                return Action {
                    SignalProducer { [weak self] observer, _ in
                        guard let self else { return }
                        ravenWrapper.getCurrentLocation { loc, err in
                            if let loc {
                                observer.send(value: loc)
                                observer.sendCompleted()
                            } else if let err {
                                observer.send(error: err)
                            }
                        }
                    }
                }
            }
            when(stub.makeExtractAction()).then {
                return Action {
                    SignalProducer { [weak self] observer, _ in
                        guard let self else { return }
                        self.ravenWrapper.extract(customerID: customerId,
                                                  transactionID: nil,
                                                  triggeredBy: Constants.Trigger.lending,
                                                  eventTrigger: Constants.EventTrigger.credit) { result in
                            switch result {
                            case .success:
                                observer.sendCompleted()
                            case .failure:
                                observer.send(error: PayMayaError())
                            }
                        }
                    }
                }
            }
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(mayaCreditContactReference))
            when(stub.request(isEqual(to: API.MayaCredit.submitContactReference(submitContactReferenceRequest)), type: isEqual(to: MayaCreditContactReferencePatchResponse.self))).thenReturn(getSuccessProducer(patchResponse))
        }

        stub(configurationService) { stub in
            when(stub.mayaCreditIosDeviceScoringEnabled.get).thenReturn(trueConfig())
        }

        stub(configurationServiceV2) { stub in
            when(stub.mecDeviceScoringEnabled.get).thenReturn(trueConfigV2())
        }

        viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: getEligibilityTerm(), feeRate: 5, deviceId: deviceId)
        XCTAssertEqual("", viewModel.backupIdentity)
        XCTAssertEqual(.noEmail, viewModel.backupEmailStatus)
        XCTAssertEqual("", viewModel.billingEndDate.value)
        XCTAssertEqual(3, MayaCreditTermDetailsViewModel.dynamicInstanceCount)
        XCTAssertEqual("5", viewModel.feeRate)
    }

    func testOtherDetails() {
        XCTAssertEqual("₱1,500", viewModel.amountWithCurrency)
        XCTAssertEqual("August 25, 2021 at 6:42 AM", viewModel.expiry)
        XCTAssertEqual("5", viewModel.feeRate)
    }

    func testGetBillingEndDateSelection() {
        let billingEndDateSelection = viewModel.getBillingEndDateSelection()
        XCTAssertEqual(nil, billingEndDateSelection?.currentValue)
        XCTAssertEqual(1, billingEndDateSelection?.minValue)
        XCTAssertEqual(31, billingEndDateSelection?.maxValue)

        billingEndDateSelection?.onSelect(5)
        XCTAssertEqual(5, viewModel.getBillingEndDateSelection()?.currentValue)
        XCTAssertEqual("5th", viewModel.billingEndDate.value)

        billingEndDateSelection?.onSelect(1)
        XCTAssertEqual(1, viewModel.getBillingEndDateSelection()?.currentValue)
        XCTAssertEqual("1st", viewModel.billingEndDate.value)

        billingEndDateSelection?.onSelect(22)
        XCTAssertEqual(22, viewModel.getBillingEndDateSelection()?.currentValue)
        XCTAssertEqual("22nd", viewModel.billingEndDate.value)

        billingEndDateSelection?.onSelect(11)
        XCTAssertEqual(11, viewModel.getBillingEndDateSelection()?.currentValue)
        XCTAssertEqual("11th", viewModel.billingEndDate.value)

        billingEndDateSelection?.onSelect(13)
        XCTAssertEqual(13, viewModel.getBillingEndDateSelection()?.currentValue)
        XCTAssertEqual("13th", viewModel.billingEndDate.value)

        billingEndDateSelection?.onSelect(3)
        XCTAssertEqual(3, viewModel.getBillingEndDateSelection()?.currentValue)
        XCTAssertEqual("3rd", viewModel.billingEndDate.value)
    }

    func testBackupIdentityStatus() {
        let user = StubUser()
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }
        viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: getEligibilityTerm(), feeRate: nil, deviceId: deviceId)
        XCTAssertEqual(L10n.Maya.New.Credit.Activation.No.email, viewModel.backupIdentityInfo)
        XCTAssertEqual(.noEmail, viewModel.backupEmailStatus)

        let backUpIdentity = StubBackupIdentity(type: "msisdn", value: "09123456789", isVerified: 0)
        user.backupIdentity = backUpIdentity
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }
        viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: getEligibilityTerm(), feeRate: nil, deviceId: deviceId)
        XCTAssertEqual(.unverified, viewModel.backupEmailStatus)
        XCTAssertEqual(L10n.Maya.New.Credit.Activation.NotVerified.email, viewModel.backupIdentityInfo)
        XCTAssertEqual("09123456789", viewModel.backupIdentity)

        backUpIdentity.isVerified = 1
        user.backupIdentity = backUpIdentity
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }
        viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: getEligibilityTerm(), feeRate: nil, deviceId: deviceId)
        XCTAssertEqual(.verified, viewModel.backupEmailStatus)
        XCTAssertEqual(L10n.Maya.New.Credit.Activation.Verified.email, viewModel.backupIdentityInfo)
        XCTAssertEqual("09123456789", viewModel.backupIdentity)
    }

    func testInitialActivationTerm() {
        XCTAssertEqual(3, viewModel.activationTerms.value.count)
        XCTAssertEqual(agreement1, viewModel.activationTerms.value[0].term)
        XCTAssertEqual(agreement2, viewModel.activationTerms.value[1].term)
        XCTAssertEqual(agreement3, viewModel.activationTerms.value[2].term)
    }

    func testActivationButtonEnabled() {
        XCTAssertFalse(viewModel.isButtonEnabled.value)
        viewModel.getBillingEndDateSelection()?.onSelect(5)
        XCTAssertFalse(viewModel.isButtonEnabled.value)
        viewModel.activationTerms.value[0].viewModel.isConfirmed.value = true
        XCTAssertFalse(viewModel.isButtonEnabled.value)
        viewModel.activationTerms.value[1].viewModel.isConfirmed.value = true
        XCTAssertFalse(viewModel.isButtonEnabled.value)
        viewModel.activationTerms.value[2].viewModel.isConfirmed.value = true
        XCTAssertFalse(viewModel.isButtonEnabled.value)

        viewModel.activationTerms.value[2].viewModel.isConfirmed.value = false
        XCTAssertFalse(viewModel.isButtonEnabled.value)
    }

    func testSubmitLoanActionSuccess() {
        viewModel.getBillingEndDateSelection()?.onSelect(5)
        viewModel.activationTerms.value[0].viewModel.isConfirmed.value = true
        viewModel.activationTerms.value[1].viewModel.isConfirmed.value = true

        let term = MayaCreditSubmitLoanRequest.Term(id: "term_id", userPreference: MayaCreditSubmitLoanRequest.Term.UserPreference(periodEndDayOfMonth: 5, loanAmount: 1500))
        let request1 = MayaCreditSubmitLoanRequest(term: term, agreements: ["key1", "key2"])
        let request2 = MayaCreditSubmitLoanRequest(term: term, agreements: ["key2", "key1"])
        let otpResponse = OTPResponse(otpId: "otp_id")
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.submitLoanApplication(request1)), type: isEqual(to: OTPResponse.self))).thenReturn(getSuccessProducer(otpResponse))
            when(stub.request(isEqual(to: API.MayaCredit.submitLoanApplication(request2)), type: isEqual(to: OTPResponse.self))).thenReturn(getSuccessProducer(otpResponse))
        }

        viewModel.submitLoanAction.values.observeValues({ _ in
            // Do nothing
        })?.addToDisposeBag(disposeBag)

        viewModel.submitLoanAction.errors.observeValues({ error in
            XCTFail("Unexpected error encountered \(error)")
        })?.addToDisposeBag(disposeBag)

        viewModel.submitLoanAction.apply().start()
        verify(reactiveAPIProvider, atMost(1)).request(isEqual(to: API.MayaCredit.submitLoanApplication(request1)), type: isEqual(to: OTPResponse.self))
        verify(reactiveAPIProvider, atMost(1)).request(isEqual(to: API.MayaCredit.submitLoanApplication(request2)), type: isEqual(to: OTPResponse.self))
    }

    func testSubmitActionSessionTimoutError() {
        viewModel.getBillingEndDateSelection()?.onSelect(5)
        viewModel.activationTerms.value[0].viewModel.isConfirmed.value = true
        viewModel.activationTerms.value[1].viewModel.isConfirmed.value = true

        let term = MayaCreditSubmitLoanRequest.Term(id: "term_id", userPreference: MayaCreditSubmitLoanRequest.Term.UserPreference(periodEndDayOfMonth: 5, loanAmount: 1500))
        let request1 = MayaCreditSubmitLoanRequest(term: term, agreements: ["key1", "key2"])
        let request2 = MayaCreditSubmitLoanRequest(term: term, agreements: ["key2", "key1"])
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.submitLoanApplication(request1)), type: isEqual(to: OTPResponse.self))).thenReturn(getSessionTimeoutError())
            when(stub.request(isEqual(to: API.MayaCredit.submitLoanApplication(request2)), type: isEqual(to: OTPResponse.self))).thenReturn(getSessionTimeoutError())
        }

        viewModel.submitLoanAction.values.observeValues({ response in
            XCTFail("Unexpected value encountered \(response)")
        })?.addToDisposeBag(disposeBag)

        viewModel.submitLoanAction.errors.observeValues { error in
            XCTAssertEqual(.sessionTimeout(backendErrorSpiel), error.type)
        }?.addToDisposeBag(disposeBag)

        viewModel.submitLoanAction.apply().start()
        verify(reactiveAPIProvider, atMost(1)).request(isEqual(to: API.MayaCredit.submitLoanApplication(request1)), type: isEqual(to: OTPResponse.self))
        verify(reactiveAPIProvider, atMost(1)).request(isEqual(to: API.MayaCredit.submitLoanApplication(request2)), type: isEqual(to: OTPResponse.self))
    }

    func testSubmitActionBackendError() {
        viewModel.getBillingEndDateSelection()?.onSelect(5)
        viewModel.activationTerms.value[0].viewModel.isConfirmed.value = true
        viewModel.activationTerms.value[1].viewModel.isConfirmed.value = true

        let term = MayaCreditSubmitLoanRequest.Term(id: "term_id", userPreference: MayaCreditSubmitLoanRequest.Term.UserPreference(periodEndDayOfMonth: 5, loanAmount: 1500))
        let request1 = MayaCreditSubmitLoanRequest(term: term, agreements: ["key1", "key2"])
        let request2 = MayaCreditSubmitLoanRequest(term: term, agreements: ["key2", "key1"])
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.submitLoanApplication(request1)), type: isEqual(to: OTPResponse.self))).thenReturn(getBackendError())
            when(stub.request(isEqual(to: API.MayaCredit.submitLoanApplication(request2)), type: isEqual(to: OTPResponse.self))).thenReturn(getBackendError())
        }

        viewModel.submitLoanAction.completed.observeValues({ _ in
            XCTFail("Should encounter an error \(#function)")
        })?.addToDisposeBag(disposeBag)

        viewModel.submitLoanAction.errors.observeValues({ error in
            XCTAssertEqual(error, self.getPayMayaError(errorTitle))
        })?.addToDisposeBag(disposeBag)

        viewModel.submitLoanAction.apply().start()
        verify(reactiveAPIProvider, atMost(1)).request(isEqual(to: API.MayaCredit.submitLoanApplication(request1)), type: isEqual(to: OTPResponse.self))
        verify(reactiveAPIProvider, atMost(1)).request(isEqual(to: API.MayaCredit.submitLoanApplication(request2)), type: isEqual(to: OTPResponse.self))
    }

    func testSubmitActionUnknownError() {
        viewModel.getBillingEndDateSelection()?.onSelect(5)
        viewModel.activationTerms.value[0].viewModel.isConfirmed.value = true
        viewModel.activationTerms.value[1].viewModel.isConfirmed.value = true

        let term = MayaCreditSubmitLoanRequest.Term(id: "term_id", userPreference: MayaCreditSubmitLoanRequest.Term.UserPreference(periodEndDayOfMonth: 5, loanAmount: 1500))
        let request1 = MayaCreditSubmitLoanRequest(term: term, agreements: ["key1", "key2"])
        let request2 = MayaCreditSubmitLoanRequest(term: term, agreements: ["key2", "key1"])
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.submitLoanApplication(request1)), type: isEqual(to: OTPResponse.self))).thenReturn(getUnknownError())
            when(stub.request(isEqual(to: API.MayaCredit.submitLoanApplication(request2)), type: isEqual(to: OTPResponse.self))).thenReturn(getUnknownError())
        }

        viewModel.submitLoanAction.completed.observeValues({ _ in
            XCTFail("Should encounter an error \(#function)")
        })?.addToDisposeBag(disposeBag)

        viewModel.submitLoanAction.errors.observeValues({ error in
            XCTAssertEqual(error, self.getPayMayaUnknownError(errorTitle))
        })?.addToDisposeBag(disposeBag)

        viewModel.submitLoanAction.apply().start()
        verify(reactiveAPIProvider, atMost(1)).request(isEqual(to: API.MayaCredit.submitLoanApplication(request1)), type: isEqual(to: OTPResponse.self))
        verify(reactiveAPIProvider, atMost(1)).request(isEqual(to: API.MayaCredit.submitLoanApplication(request2)), type: isEqual(to: OTPResponse.self))
    }

    func testEmailIdentityStatusTitle() {
        XCTAssertEqual(EmailIdentityStatus.verified.title, L10n.Maya.New.Credit.Activation.IdentityStatus.verified)
        XCTAssertEqual(EmailIdentityStatus.unverified.title, L10n.Maya.New.Credit.Activation.IdentityStatus.unverified)
        XCTAssertEqual(EmailIdentityStatus.noEmail.title, L10n.Maya.New.Credit.Activation.IdentityStatus.noEmail)
    }

    func testEmailIdentityStatusBackgroundColor() {
        XCTAssertEqual(EmailIdentityStatus.verified.backgroundColor, CommonAsset.MayaColors.Primary.primaryGrownGreen.color)
        XCTAssertEqual(EmailIdentityStatus.unverified.backgroundColor, CommonAsset.MayaColors.Grey.grey4.color)
        XCTAssertEqual(EmailIdentityStatus.noEmail.backgroundColor, CommonAsset.MayaColors.Grey.grey4.color)
    }
}

extension NewMayaCreditActivationViewModelTest {
    // Contact referenct toggle ON

    func testInitialValues() {
        viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: getEligibilityTerm(), feeRate: 5, deviceId: deviceId)
        XCTAssertEqual("", viewModel.backupIdentity)
        XCTAssertEqual(.noEmail, viewModel.backupEmailStatus)
        XCTAssertNotNil(viewModel.billingEndDate.value)
        XCTAssertEqual(3, MayaCreditTermDetailsViewModel.dynamicInstanceCount)
        XCTAssertEqual("5", viewModel.feeRate)
    }

    func testInitialActivationTermContactReferenceOn() {
        XCTAssertEqual(3, viewModel.activationTerms.value.count)
        XCTAssertEqual(agreement1, viewModel.activationTerms.value[0].term)
        XCTAssertEqual(agreement2, viewModel.activationTerms.value[1].term)
        XCTAssertEqual(agreement3, viewModel.activationTerms.value[2].term)
    }

    func testGetPersonalDetailsSelection() {
        XCTAssertFalse(viewModel.isButtonEnabled.value)

        let personalDetailsSelection = viewModel.getPersonalDetailsSelection()
        personalDetailsSelection.onSelectGender(PersonalDetails.Gender.male)
        XCTAssertEqual(PersonalDetails.Gender.male, viewModel.selectedGender)
        personalDetailsSelection.onSelectMaritalStatus(PersonalDetails.MaritalStatus.single)
        XCTAssertEqual(PersonalDetails.MaritalStatus.single, viewModel.selectedMaritalStatus)
    }

    func testContinueButtonWithInvalidEmail() {
        XCTAssertFalse(viewModel.isButtonEnabled.value)

        let personalDetailsSelection = viewModel.getPersonalDetailsSelection()
        personalDetailsSelection.onSelectGender(PersonalDetails.Gender.male)
        XCTAssertEqual(PersonalDetails.Gender.male, viewModel.selectedGender)
        personalDetailsSelection.onSelectMaritalStatus(PersonalDetails.MaritalStatus.single)
        XCTAssertEqual(PersonalDetails.MaritalStatus.single, viewModel.selectedMaritalStatus)
        viewModel.emailField.mutableProperty.value = "InvalidEmail.com"

        XCTAssertFalse(viewModel.isButtonEnabled.value)
    }

    func testContinueButtonWithInvalidAlternativeMobileNumber() {
        XCTAssertFalse(viewModel.isButtonEnabled.value)

        let personalDetailsSelection = viewModel.getPersonalDetailsSelection()
        personalDetailsSelection.onSelectGender(PersonalDetails.Gender.male)
        XCTAssertEqual(PersonalDetails.Gender.male, viewModel.selectedGender)
        personalDetailsSelection.onSelectMaritalStatus(PersonalDetails.MaritalStatus.single)
        XCTAssertEqual(PersonalDetails.MaritalStatus.single, viewModel.selectedMaritalStatus)
        viewModel.emailField.mutableProperty.value = "<EMAIL>"
        viewModel.alternativeMobileNumberField.mutableProperty.value = "00071234567"

        XCTAssertFalse(viewModel.isButtonEnabled.value)
    }

    func testContinueButtonWithMissingAlternativeMobileNumber() {
        XCTAssertFalse(viewModel.isButtonEnabled.value)

        let personalDetailsSelection = viewModel.getPersonalDetailsSelection()
        personalDetailsSelection.onSelectGender(PersonalDetails.Gender.male)
        XCTAssertEqual(PersonalDetails.Gender.male, viewModel.selectedGender)
        personalDetailsSelection.onSelectMaritalStatus(PersonalDetails.MaritalStatus.single)
        XCTAssertEqual(PersonalDetails.MaritalStatus.single, viewModel.selectedMaritalStatus)
        viewModel.billingEndDate.value = "12th"
        viewModel.mothersMaidenFirstNameField.mutableProperty.value = "Test"
        viewModel.mothersMaidenLastNameField.mutableProperty.value = "Test"
        viewModel.mothersMaidenMiddleNameField.mutableProperty.value = "Test"
        viewModel.isNoMaidenMiddleNameChecked.value = false
        viewModel.emailField.mutableProperty.value = "<EMAIL>"

        XCTAssertFalse(viewModel.isButtonEnabled.value)
    }

    func testUpdateButtonWithInvalidAlternativeMobileNumber() {
        XCTAssertFalse(viewModel.isUpdateButtonEnabled.value)

        let personalDetailsSelection = viewModel.getPersonalDetailsSelection()
        personalDetailsSelection.onSelectGender(PersonalDetails.Gender.male)
        XCTAssertEqual(PersonalDetails.Gender.male, viewModel.selectedGender)
        personalDetailsSelection.onSelectMaritalStatus(PersonalDetails.MaritalStatus.single)
        XCTAssertEqual(PersonalDetails.MaritalStatus.single, viewModel.selectedMaritalStatus)
        viewModel.alternativeMobileNumberField.mutableProperty.value = "00071234567"

        XCTAssertFalse(viewModel.isUpdateButtonEnabled.value)
    }

    func testUpdateButtonWithValidAlternativeMobileNumber() {
        XCTAssertFalse(viewModel.isUpdateButtonEnabled.value)

        let personalDetailsSelection = viewModel.getPersonalDetailsSelection()
        personalDetailsSelection.onSelectGender(PersonalDetails.Gender.male)
        XCTAssertEqual(PersonalDetails.Gender.male, viewModel.selectedGender)
        personalDetailsSelection.onSelectMaritalStatus(PersonalDetails.MaritalStatus.single)
        XCTAssertEqual(PersonalDetails.MaritalStatus.single, viewModel.selectedMaritalStatus)
        viewModel.alternativeMobileNumberField.mutableProperty.value = "09071234567"

        XCTAssertTrue(viewModel.isUpdateButtonEnabled.value)
    }

    func testUpdateButtonWithMissingAlternativeMobileNumber() {
        XCTAssertFalse(viewModel.isUpdateButtonEnabled.value)

        let personalDetailsSelection = viewModel.getPersonalDetailsSelection()
        personalDetailsSelection.onSelectGender(PersonalDetails.Gender.male)
        XCTAssertEqual(PersonalDetails.Gender.male, viewModel.selectedGender)
        personalDetailsSelection.onSelectMaritalStatus(PersonalDetails.MaritalStatus.single)
        XCTAssertEqual(PersonalDetails.MaritalStatus.single, viewModel.selectedMaritalStatus)

        XCTAssertFalse(viewModel.isUpdateButtonEnabled.value)
    }

    func testValidBillingEmail() {
        viewModel.emailField.mutableProperty.value = "<EMAIL>"
        XCTAssertTrue(viewModel.emailField.isValid)
    }

    func testValidAlternativeNumber() {
        let mainIdentity = StubMainIdentity(type: "msisdn", value: "***********")
        let user = StubUser(identities: NSSet(array: [mainIdentity]))

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }

        viewModel.alternativeMobileNumberField.mutableProperty.value = "********567"
        XCTAssertTrue(viewModel.alternativeMobileNumberField.isValid)
        viewModel.alternativeMobileNumberField.mutableProperty.value = "" // optional
        XCTAssertTrue(viewModel.alternativeMobileNumberField.isValid)
    }

    func testInvalidAlternativeNumber() {
        let mainIdentity = StubMainIdentity(type: "msisdn", value: "***********")
        let user = StubUser(identities: NSSet(array: [mainIdentity]))

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }

        viewModel.alternativeMobileNumberField.mutableProperty.value = "***********" // similar to account mobile number
        XCTAssertFalse(viewModel.alternativeMobileNumberField.isValid)
        viewModel.alternativeMobileNumberField.mutableProperty.value = "***********" // should start with 09
        XCTAssertFalse(viewModel.alternativeMobileNumberField.isValid)
        viewModel.alternativeMobileNumberField.mutableProperty.value = "********" // less than 11 digit
        XCTAssertFalse(viewModel.alternativeMobileNumberField.isValid)
        viewModel.alternativeMobileNumberField.mutableProperty.value = "************" // more than 11 digit
        XCTAssertFalse(viewModel.alternativeMobileNumberField.isValid)
    }

    func testFourDigitsFormatAlternativeNumber() {
        let formattedNumber = viewModel.formatAlternativeNumber(with: "0989", and: "9")
        XCTAssertEqual("0989 ", formattedNumber)
    }

    func testEightDigitsFormatAlternativeNumber() {
        let formattedNumber = viewModel.formatAlternativeNumber(with: "0989 987", and: "8")
        XCTAssertEqual("0989 987 ", formattedNumber)
    }

    func testBackspaceFormatAlternativeNumber() {
        let formattedNumber = viewModel.formatAlternativeNumber(with: "0989", and: "")
        XCTAssertEqual("098", formattedNumber)
    }
}

extension NewMayaCreditActivationViewModelTest {
    // null eligibility

    func testOtherDetailsWithNilEligibility() {
        viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: nil, feeRate: 5, deviceId: deviceId)

        XCTAssertEqual("", viewModel.amountWithCurrency)
        XCTAssertEqual("", viewModel.expiry)
        XCTAssertEqual("", viewModel.feeRate)
    }

    func testGetBillingEndDateSelectionWithNilEligibility() {
        viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: nil, feeRate: 5, deviceId: deviceId)
        let billingEndDateSelection = viewModel.getBillingEndDateSelection()

        XCTAssertEqual(nil, billingEndDateSelection?.currentValue)
        XCTAssertEqual(nil, billingEndDateSelection?.minValue)
        XCTAssertEqual(nil, billingEndDateSelection?.maxValue)
        XCTAssertEqual("", viewModel.billingEndDate.value)
    }
}

extension NewMayaCreditActivationViewModelTest {
    func testGetContactReferenceSuccess() {
        viewModel.getContactReference.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testGetContactReferenceSuccessNilContactRef() {
        let contactReference = MayaCreditContactReference(customerId: "934b58fa-1cbe-494c-aacb-5e7579d7aea1", firstName: "Ethel", lastName: "Velaryon", alternativeMobileNumber: nil, alternativeLandlineNumber: nil, gender: "ABCD", maritalStatus: "ABCD", contactReference: nil)
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.getContactReference.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testSubmitContactReferenceSuccess() {
        viewModel.getContactReference.apply().start()
        viewModel.submitContactReference.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.submitContactReference(submitContactReferenceRequest)), type: isEqual(to: MayaCreditContactReferencePatchResponse.self))
    }

    func testSubmitContactReferenceIncompleValues() {
        let mayaCreditContactReference = MayaCreditContactReference(customerId: "934b58fa-1cbe-494c-aacb-5e7579d7aea1", firstName: "Ethel", lastName: "Velaryon", alternativeMobileNumber: nil, alternativeLandlineNumber: nil, gender: "ABCD", maritalStatus: "ABCD", contactReference: MayaCreditContactReference.ContactReference(firstName: "Arya", lastName: "Stark", relationship: "ABCD", mobileNumber: "09000003542"))

        let contactReferenceRequest = MayaCreditSubmitContactReferenceRequest.ContactReference(firstName: "Arya", lastName: "Stark", relationship: "SIBLING", mobileNumber: "09000003542")
        let submitContactReferenceRequest = MayaCreditSubmitContactReferenceRequest(alternativeMobileNumber: nil, gender: "ABCD", maritalStatus: "ABCD", contactReference: contactReferenceRequest)
        let contactReferenceResponse = MayaCreditContactReferencePatchResponse.ContactReference(id: "04a3755e-6557-4f9f-aa2d-09809b05c3ef")
        let patchResponse = MayaCreditContactReferencePatchResponse(contactReference: contactReferenceResponse)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(mayaCreditContactReference))
            when(stub.request(isEqual(to: API.MayaCredit.submitContactReference(submitContactReferenceRequest)), type: isEqual(to: MayaCreditContactReferencePatchResponse.self))).thenReturn(getSuccessProducer(patchResponse))
        }

        viewModel.getContactReference.apply().start()
        viewModel.submitContactReference.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
        verify(reactiveAPIProvider, times(0)).request(isEqual(to: API.MayaCredit.submitContactReference(submitContactReferenceRequest)), type: isEqual(to: MayaCreditContactReferencePatchResponse.self))
    }
}

// For Mother's Maiden Name
extension NewMayaCreditActivationViewModelTest {
    func testMothersMaidenFirstNameField() {
        viewModel.mothersMaidenFirstNameField.mutableProperty.value = "Valid Name"
        XCTAssertTrue(viewModel.mothersMaidenFirstNameField.isValid)

        viewModel.mothersMaidenFirstNameField.mutableProperty.value = "Invalid Name >.<"
        XCTAssertFalse(viewModel.mothersMaidenFirstNameField.isValid)

        viewModel.mothersMaidenFirstNameField.mutableProperty.value = "Invalid Name Invalid Name Invalid Name Invalid Name"
        XCTAssertFalse(viewModel.mothersMaidenFirstNameField.isValid)

        viewModel.mothersMaidenFirstNameField.mutableProperty.value = "Invalid Nameee"
        XCTAssertFalse(viewModel.mothersMaidenFirstNameField.isValid)

        viewModel.mothersMaidenFirstNameField.mutableProperty.value = "I"
        XCTAssertFalse(viewModel.mothersMaidenFirstNameField.isValid)
    }

    func testMothersMaidenMiddleNameField() {
        XCTAssertFalse(viewModel.isNoMaidenMiddleNameChecked.value)

        viewModel.mothersMaidenMiddleNameField.mutableProperty.value = "Valid Name"
        XCTAssertTrue(viewModel.mothersMaidenMiddleNameField.isValid)

        viewModel.mothersMaidenMiddleNameField.mutableProperty.value = "Invalid Name >.<"
        XCTAssertFalse(viewModel.mothersMaidenMiddleNameField.isValid)

        viewModel.mothersMaidenMiddleNameField.mutableProperty.value = "Invalid Name Invalid Name Invalid Name Invalid Name"
        XCTAssertFalse(viewModel.mothersMaidenMiddleNameField.isValid)

        viewModel.mothersMaidenMiddleNameField.mutableProperty.value = "Invalid Nameee"
        XCTAssertFalse(viewModel.mothersMaidenMiddleNameField.isValid)
    }

    func testMothersMaidenLastNameField() {
        viewModel.mothersMaidenLastNameField.mutableProperty.value = "Valid Name"
        XCTAssertTrue(viewModel.mothersMaidenLastNameField.isValid)

        viewModel.mothersMaidenLastNameField.mutableProperty.value = "Invalid Name >.<"
        XCTAssertFalse(viewModel.mothersMaidenLastNameField.isValid)

        viewModel.mothersMaidenLastNameField.mutableProperty.value = "Invalid Name Invalid Name Invalid Name Invalid Name"
        XCTAssertFalse(viewModel.mothersMaidenLastNameField.isValid)

        viewModel.mothersMaidenLastNameField.mutableProperty.value = "Invalid Nameee"
        XCTAssertFalse(viewModel.mothersMaidenLastNameField.isValid)

        viewModel.mothersMaidenLastNameField.mutableProperty.value = "I"
        XCTAssertFalse(viewModel.mothersMaidenLastNameField.isValid)
    }

    func testGetPersonDetailsNilValues() {
        let personDetails = MayaCreditPersonDetails(alternativeMobileNumber: nil, gender: nil, maritalStatus: nil,
            contactReference: MayaCreditPersonDetails.ContactReference(firstName: nil, lastName: nil, relationship: nil, mobileNumber: nil),
            mothersMaidenName: MayaCreditPersonDetails.MothersMaidenName(firstName: nil, middleName: nil, lastName: nil, hasNoMiddleName: nil))

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))).thenReturn(getSuccessProducer(personDetails))
        }

        viewModel.getPersonDetails.apply().start()

        XCTAssertEqual(viewModel.genderField.value, nil)
        XCTAssertEqual(viewModel.maritalStatusField.value, nil)
        XCTAssertEqual(viewModel.alternativeMobileNumberField.value, nil)
        XCTAssertEqual(viewModel.assignContactReferenceViewModel.selectedContactRelationship, nil)
        XCTAssertEqual(viewModel.assignContactReferenceViewModel.firstNameField.value, nil)
        XCTAssertEqual(viewModel.assignContactReferenceViewModel.lastNameField.value, nil)
        XCTAssertEqual(viewModel.assignContactReferenceViewModel.mobileNumberField.value, nil)
        XCTAssertEqual(viewModel.mothersMaidenFirstNameField.value, nil)
        XCTAssertEqual(viewModel.mothersMaidenMiddleNameField.value, nil)
        XCTAssertEqual(viewModel.mothersMaidenLastNameField.value, nil)
        XCTAssertFalse(viewModel.isNoMaidenMiddleNameChecked.value)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))
    }

    func testGetPersonDetailsWithValues() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))).thenReturn(getSuccessProducer(personDetails))
        }

        viewModel.getPersonDetails.apply().start()

        XCTAssertEqual(viewModel.genderField.value, "Female")
        XCTAssertEqual(viewModel.maritalStatusField.value, "Married")
        XCTAssertEqual(viewModel.alternativeMobileNumberField.value, "09000004088")
        XCTAssertEqual(viewModel.assignContactReferenceViewModel.selectedContactRelationship, PersonalDetails.ContactRelationship.sibling)
        XCTAssertEqual(viewModel.assignContactReferenceViewModel.firstNameField.value, "Arya")
        XCTAssertEqual(viewModel.assignContactReferenceViewModel.lastNameField.value, "Stark")
        XCTAssertEqual(viewModel.mothersMaidenFirstNameField.value, "Catelyn")
        XCTAssertEqual(viewModel.mothersMaidenMiddleNameField.value, nil)
        XCTAssertEqual(viewModel.mothersMaidenLastNameField.value, "Last")
        XCTAssertTrue(viewModel.isNoMaidenMiddleNameChecked.value)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))
    }

    func testGetPersonDetailsBackendError() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))).thenReturn(getBackendError())
        }

        viewModel.getPersonDetails.apply().start()
        verify(reactiveAPIProvider, atMost(1)).request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))
    }

    func testSubmitPersonDetailsSuccess() {
        let personDetailsPatchRequest = MayaCreditPersonDetails(alternativeMobileNumber: "09000004088", gender: "FEMALE", maritalStatus: "MARRIED", contactReference: MayaCreditPersonDetails.ContactReference(firstName: "Arya", lastName: "Stark", relationship: "SIBLING", mobileNumber: "09000004099"), mothersMaidenName: MayaCreditPersonDetails.MothersMaidenName(firstName: "Catelyn", middleName: "", lastName: "Last", hasNoMiddleName: true))

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))).thenReturn(getSuccessProducer(personDetails))
            when(stub.request(isEqual(to: API.MayaCredit.submitPersonDetails(personDetailsPatchRequest)), type: isEqual(to: MayaCreditPersonDetailsPatchResponse.self))).thenReturn(getSuccessProducer(personDetailsPatchResponse))
        }

        viewModel.getPersonDetails.apply().start()

        XCTAssertEqual(viewModel.genderField.value, "Female")
        XCTAssertEqual(viewModel.maritalStatusField.value, "Married")
        XCTAssertEqual(viewModel.alternativeMobileNumberField.value, "09000004088")
        XCTAssertEqual(viewModel.assignContactReferenceViewModel.selectedContactRelationship, PersonalDetails.ContactRelationship.sibling)
        XCTAssertEqual(viewModel.assignContactReferenceViewModel.firstNameField.value, "Arya")
        XCTAssertEqual(viewModel.assignContactReferenceViewModel.lastNameField.value, "Stark")
        XCTAssertEqual(viewModel.mothersMaidenFirstNameField.value, "Catelyn")
        XCTAssertEqual(viewModel.mothersMaidenMiddleNameField.value, nil)
        XCTAssertEqual(viewModel.mothersMaidenLastNameField.value, "Last")
        XCTAssertTrue(viewModel.isNoMaidenMiddleNameChecked.value)

        viewModel.submitPersonDetails.apply().start()
        verify(reactiveAPIProvider, atLeast(1)).request(isEqual(to: API.MayaCredit.submitPersonDetails(personDetailsPatchRequest)), type: isEqual(to: MayaCreditPersonDetailsPatchResponse.self))
    }

    func testSubmitPersonDetailsBackendError() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.submitPersonDetails(personDetails)), type: isEqual(to: MayaCreditPersonDetailsPatchResponse.self))).thenReturn(getBackendError())
        }

        viewModel.submitPersonDetails.apply().start()
        verify(reactiveAPIProvider, atMost(1)).request(isEqual(to: API.MayaCredit.submitPersonDetails(personDetails)), type: isEqual(to: MayaCreditPersonDetailsPatchResponse.self))
    }
}

private extension NewMayaCreditActivationViewModelTest {
    func getEligibilityTerm() -> MayaCreditEligibility.Term {
        let gracePeriodSettings = MayaCreditScheduleSettings.GracePeriodSettings(type: "grace_period_type", unit: "day", value: 15)
        let periodEndDayOfMonth = MayaCreditScheduleSettings.BillingSettings.PeriodEndDayOfMonth(minValue: 1, maxValue: 31, days: [5, 6, 7, 8, 9])
        let billingSettings = MayaCreditScheduleSettings.BillingSettings(periodEndDayOfMonth: periodEndDayOfMonth)
        let scheduleSettings = MayaCreditScheduleSettings(gracePeriodSettings: gracePeriodSettings, billingSettings: billingSettings)

        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.dateFormat = "YYYY-MM-dd'T'HH:mm:ss'Z'"
        let date = formatter.date(from: "2021-08-25T06:42:30Z")
        let eligibilityTerm = MayaCreditEligibility.Term(id: "term_id", expiresOn: date!, loanAmount: 1500, currency: "PHP", interestRate: 0.502, feeRate: 0.05, scheduleSettings: scheduleSettings, agreements: [agreement1, agreement2, agreement3])
        return eligibilityTerm
    }
}

// Test for credit scoring integration
extension NewMayaCreditActivationViewModelTest {
    func testDeviceScoringWhenRiskLevelIsHighAndIsValidIsFalse() {
        let initialEligibility = MayaCreditInitialEligibility(product: "MAYA_CREDIT_CONSUMER_LOAN", term: initialEligibilityTerm, riskLevel: Constants.RiskLevel.high.rawValue, isCreditScoringDataValid: false)
        viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: getEligibilityTerm(), initialEligibility: initialEligibility, feeRate: 5)

        XCTAssertEqual(true, viewModel.shouldInitializeDeviceScoring)
    }

    func testDeviceScoringWhenRiskLevelIsMediumAndIsValidIsFalse() {
        let initialEligibility = MayaCreditInitialEligibility(product: "MAYA_CREDIT_CONSUMER_LOAN", term: initialEligibilityTerm, riskLevel: Constants.RiskLevel.medium.rawValue, isCreditScoringDataValid: false)
        viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: getEligibilityTerm(), initialEligibility: initialEligibility, feeRate: 5)

        XCTAssertEqual(true, viewModel.shouldInitializeDeviceScoring)
    }

    func testDeviceScoringWhenRiskLevelIsLowAndIsValidIsFalse() {
        let initialEligibility = MayaCreditInitialEligibility(product: "MAYA_CREDIT_CONSUMER_LOAN", term: initialEligibilityTerm, riskLevel: Constants.RiskLevel.low.rawValue, isCreditScoringDataValid: false)
        viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: getEligibilityTerm(), initialEligibility: initialEligibility, feeRate: 5)

        XCTAssertEqual(false, viewModel.shouldInitializeDeviceScoring)
    }

    func testDeviceScoringWhenRiskLevelIsHighAndIsValidIsTrue() {
        let initialEligibility = MayaCreditInitialEligibility(product: "MAYA_CREDIT_CONSUMER_LOAN", term: initialEligibilityTerm, riskLevel: Constants.RiskLevel.high.rawValue, isCreditScoringDataValid: true)
        viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: getEligibilityTerm(), initialEligibility: initialEligibility, feeRate: 5)

        XCTAssertEqual(false, viewModel.shouldInitializeDeviceScoring)
    }

    func testDeviceScoringWhenRiskLevelIsMediumAndIsValidIsTrue() {
        let initialEligibility = MayaCreditInitialEligibility(product: "MAYA_CREDIT_CONSUMER_LOAN", term: initialEligibilityTerm, riskLevel: Constants.RiskLevel.medium.rawValue, isCreditScoringDataValid: true)
        viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: getEligibilityTerm(), initialEligibility: initialEligibility, feeRate: 5)

        XCTAssertEqual(false, viewModel.shouldInitializeDeviceScoring)
    }

    func testDeviceScoringWhenRiskLevelIsLowAndIsValidIsTrue() {
        let initialEligibility = MayaCreditInitialEligibility(product: "MAYA_CREDIT_CONSUMER_LOAN", term: initialEligibilityTerm, riskLevel: Constants.RiskLevel.low.rawValue, isCreditScoringDataValid: true)
        viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: getEligibilityTerm(), initialEligibility: initialEligibility, feeRate: 5)

        XCTAssertEqual(false, viewModel.shouldInitializeDeviceScoring)
    }

    func testGetMayaCreditFullEligibilityWithRefId() {
        let deviceId = "be22f727-8efc-4288-a6f1-522dab34d729"
        let rrn = "e7029316-168c-4b21-bf3b-665bd0f49089"
        let dataReferenceId = "61312b86-245e-49af-885b-90b0761cd4f5"
        let device = EligibilityRequest.Device(id: deviceId, channel: DeviceType.kind, version: osVersion, dataReferenceId: dataReferenceId, isCreditScoringEnabled: true)

        let eligibilityRequest = EligibilityRequest(requestReferenceNumber: rrn, productKey: Constants.Defaults.Common.mayaCreditProductKey.rawValue, device: device)
        let eligibility = MayaCreditEligibility(consents: [MayaCreditOptInConsent(id: 2198, type: "Date Privacy", version: "1.0", majorVersion: "1.0", value: "value")], term: nil, id: nil, product: nil, status: nil, expiresOn: nil, riskLevel: nil)

        viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: getEligibilityTerm(), feeRate: 5, deviceId: deviceId, osVersion: osVersion)

        stub(configurationServiceV2) { stub in
            when(stub.mecDeviceScoringEnabled.get).thenReturn(trueConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
        }

        viewModel.getMayaCreditFullEligibility.apply((rrn, dataReferenceId, nil)).start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())
    }

    func testGetMayaCreditFullEligibilityWithEligibilityModel() {
        let rrn = "e7029316-168c-4b21-bf3b-665bd0f49089"
        let dataReferenceId = "61312b86-245e-49af-885b-90b0761cd4f5"

        viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: getEligibilityTerm(), feeRate: 5, deviceId: deviceId, osVersion: osVersion)

        let expectation = expectation(description: "Credit elgibility loaded")

        viewModel.getMayaCreditFullEligibility.values
            .observe(on: UIScheduler())
            .observeValues { creditEligibility in
                expectation.fulfill()
                XCTAssertNotNil(creditEligibility)
            }?.addToDisposeBag(disposeBag)

        viewModel.getMayaCreditFullEligibility.apply((rrn, dataReferenceId, creditEligibilityModel)).start()

        wait(for: [expectation], timeout: 1.0)
    }
}

// Test geo location flow
extension NewMayaCreditActivationViewModelTest {
    func testGeoLocationNotDeterminedErrorThenShouldRequestLocationPermission() {
        stub(ravenWrapper) { stub in
            when(stub.getCurrentLocation(completion: any())).then { completion in
                completion(nil, .notDetermined)
            }
        }

        let expectation = XCTestExpectation(description: "Wait for error")

        viewModel.handleRavenGetGeolocation.errors
            .observe(on: UIScheduler())
            .observeValues { err in
                XCTAssertTrue(err == RavenLocation.Error.notDetermined)
                expectation.fulfill()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.apply().start()

        wait(for: [expectation], timeout: 1.0)
    }

    func testGeoLocationPermissionDenied() {
        stub(ravenWrapper) { stub in
            when(stub.getCurrentLocation(completion: any())).then { completion in
                completion(nil, .denied)
            }
        }

        let expectation = XCTestExpectation(description: "Wait for geolocation error")

        viewModel.handleRavenGetGeolocation.errors
            .observe(on: UIScheduler())
            .observeValues { err in
                XCTAssertTrue(err == RavenLocation.Error.denied)
                expectation.fulfill()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.apply().start()

        wait(for: [expectation], timeout: 1.0)
    }

    func testGeoLocationRestrictedError() {
        stub(ravenWrapper) { stub in
            when(stub.getCurrentLocation(completion: any())).then { completion in
                completion(nil, .restricted)
            }
        }

        let expectation = XCTestExpectation(description: "Wait for geolocation error")

        viewModel.handleRavenGetGeolocation.errors
            .observe(on: UIScheduler())
            .observeValues { err in
                XCTAssertTrue(err == RavenLocation.Error.restricted)
                expectation.fulfill()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.apply().start()

        wait(for: [expectation], timeout: 1.0)
    }

    func testGeoLocationUnknownError() {
        stub(ravenWrapper) { stub in
            when(stub.getCurrentLocation(completion: any())).then { completion in
                completion(nil, .unknown)
            }
        }

        let expectation = XCTestExpectation(description: "Wait for geolocation error")

        viewModel.handleRavenGetGeolocation.errors
            .observe(on: UIScheduler())
            .observeValues { err in
                XCTAssertTrue(err == RavenLocation.Error.unknown)
                expectation.fulfill()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.apply().start()

        wait(for: [expectation], timeout: 1.0)
    }

    func testGeoLocationAllowedPermisionThenShouldHandleExtract() {
        stub(configurationServiceV2) { stub in
            when(stub.lendingDataExtractionEnabled.get).thenReturn(trueConfigV2())
        }

        stub(ravenWrapper) { stub in
            when(stub.getCurrentLocation(completion: any())).then { [weak self] completion in
                guard let self else { return }
                completion(self.mockRavenLocation, nil)
            }
            when(stub.extract(customerID: customerId, transactionID: isNil(), triggeredBy: Constants.Trigger.lending, eventTrigger: Constants.EventTrigger.credit, completion: anyOptional())).then { _, _, _, _, completion in
                                                                                                                                                                                                   completion?(.success(()))
            }
        }

        let expectation = XCTestExpectation(description: "Wait for geolocation complete")

        viewModel.handleRavenGetGeolocation.errors
            .observe(on: UIScheduler())
            .observeValues { _ in
                XCTFail("Must not send failure signal")
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.completed
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self else { return }
                viewModel.handleRavenExtract.apply().start()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenExtract.completed
            .observe(on: UIScheduler())
            .observeValues { _ in
                expectation.fulfill()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.apply().start()

        wait(for: [expectation], timeout: 1.0)

        verify(ravenWrapper, times(1)).extract(customerID: customerId, transactionID: isNil(), triggeredBy: Constants.Trigger.lending, eventTrigger: Constants.EventTrigger.credit, completion: anyOptional())
    }

    func testHandleExtractSuccess() {
        stub(configurationServiceV2) { stub in
            when(stub.lendingDataExtractionEnabled.get).thenReturn(trueConfigV2())
            when(stub.mecGeolocationEnabled.get).thenReturn(trueConfigV2())
        }

        stub(ravenWrapper) { stub in
            when(stub.extract(customerID: customerId, transactionID: isNil(), triggeredBy: Constants.Trigger.lending, eventTrigger: Constants.EventTrigger.credit, completion: anyOptional())).then { _, _, _, _, completion in
                                                                                                                                                                                                   completion?(.success(()))
            }
        }

        let expectation = XCTestExpectation(description: "Wait for extract completion")

        viewModel.handleRavenExtract.completed
            .observe(on: UIScheduler())
            .observeValues { _ in
                XCTAssert(true)
                expectation.fulfill()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenExtract.apply().start()
        XCTAssertTrue(viewModel.isGeolocationEnabled == true)
        XCTAssertTrue(viewModel.isLendingDataExtractionEnabled == true)

        wait(for: [expectation], timeout: 1.0)

        verify(ravenWrapper, times(1)).extract(customerID: customerId, transactionID: isNil(), triggeredBy: Constants.Trigger.lending, eventTrigger: Constants.EventTrigger.credit, completion: anyOptional())
    }

    func testHandleExtractFailed() {
        stub(configurationServiceV2) { stub in
            when(stub.lendingDataExtractionEnabled.get).thenReturn(falseConfigV2())
        }

        stub(ravenWrapper) { stub in
            when(stub.extract(customerID: customerId, transactionID: isNil(), triggeredBy: Constants.Trigger.lending, eventTrigger: Constants.EventTrigger.credit, completion: anyOptional())).then { _, _, _, _, completion in
                completion?(.failure(PayMayaError()))
            }
        }

        let expectation = XCTestExpectation(description: "Wait for extract error")

        viewModel.handleRavenExtract.errors
            .observe(on: UIScheduler())
            .observeValues { error in
                XCTAssertNotNil(error, "Error must not be nil")
                expectation.fulfill()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenExtract.apply().start()

        wait(for: [expectation], timeout: 1.0)
    }

    func testHandleExtractSuccessThenShouldPerformFullEligibility() {
        let deviceId = "be22f727-8efc-4288-a6f1-522dab34d729"
        let rrn = "e7029316-168c-4b21-bf3b-665bd0f49089"
        let dataReferenceId = "61312b86-245e-49af-885b-90b0761cd4f5"
        let device = EligibilityRequest.Device(id: deviceId, channel: DeviceType.kind, version: osVersion, dataReferenceId: dataReferenceId, isCreditScoringEnabled: true)

        let eligibilityRequest = EligibilityRequest(requestReferenceNumber: rrn, productKey: Constants.Defaults.Common.mayaCreditProductKey.rawValue, device: device)
        let eligibility = MayaCreditEligibility(consents: [MayaCreditOptInConsent(id: 2198, type: "Date Privacy", version: "1.0", majorVersion: "1.0", value: "value")], term: nil, id: nil, product: nil, status: nil, expiresOn: nil, riskLevel: nil)

        viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: getEligibilityTerm(), feeRate: 5, deviceId: deviceId, osVersion: osVersion)

        stub(configurationServiceV2) { stub in
            when(stub.mecDeviceScoringEnabled.get).thenReturn(trueConfigV2())
            when(stub.lendingDataExtractionEnabled.get).thenReturn(trueConfigV2())
        }

        stub(ravenWrapper) { stub in
            when(stub.extract(customerID: customerId, transactionID: isNil(), triggeredBy: Constants.Trigger.lending, eventTrigger: Constants.EventTrigger.credit, completion: anyOptional())).then { _, _, _, _, completion in
                                                                                                                                                                                                   completion?(.success(()))
            }
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
        }

        let expectation = XCTestExpectation(description: "Wait for geolocation complete")

        viewModel.handleRavenGetGeolocation.errors
            .observe(on: UIScheduler())
            .observeValues { _ in
                XCTFail("Must not send failure signal")
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenExtract.completed
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self else { return }
                viewModel.getMayaCreditFullEligibility.apply((rrn, dataReferenceId, nil)).start()
                expectation.fulfill()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenExtract.apply().start()

        wait(for: [expectation], timeout: 1.0)

        verify(ravenWrapper, times(1)).extract(customerID: customerId, transactionID: isNil(), triggeredBy: Constants.Trigger.lending, eventTrigger: Constants.EventTrigger.credit, completion: anyOptional())

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())
    }
}
