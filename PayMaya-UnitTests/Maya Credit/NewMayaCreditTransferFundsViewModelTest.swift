//
//  NewMayaCreditTransferFundsViewModelTest.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 15/04/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import Cuckoo
import Foundation
import Injector
import Maya<PERSON>oreData
@testable import RavenLocation
import ReactiveSwift
import StoreProvider
import XCTest

private let creditAccount = MayaCreditAccount(id: "5e7b0b60-b2a2-406d-b4af-7ad56e8cc1e7", applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "ACTIVE", currencyCode: "₱", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: MayaCreditAccount.ScheduleSettings(gracePeriodSettings: MayaCreditAccount.ScheduleSettings.GracePeriodSettings(type: "INTEREST_FORGIVENESS", unit: "DAYS", value: 15), billingSettings: MayaCreditAccount.ScheduleSettings.BillingSettings(periodEndDayOfMonth: MayaCreditAccount.ScheduleSettings.BillingSettings.PeriodEndDayOfMonth(minValue: 1, maxValue: 27)), userPreference: MayaCreditAccount.ScheduleSettings.UserPreference(periodEndDayOfMonth: 2, periodStartDayOfMonth: 4)), repaymentSettings: nil, createdDate: Date(timeIntervalSince1970: **********), reasonCode: nil, daysInArrears: 0, daysLate: 0)

private let feesAndTaxes = MayaCreditFeesAndTaxes(loanAccountId: "", principal: 1000, currencyCode: "PHP", serviceFee: 50, feeRate: 0.05, dst: 10, total: 1060)
private let rrn = "20caa789-d73c-47ad-a07d-99ae33bea67b"
private let customerId = "2fc10b52-f668-41d5-9363-91b3209b14ee"
private let calculateDisbursementRequest = MayaCreditCalculateDisbursementRequest(customerId: customerId, loanAccountId: creditAccount.id, amount: AmountRequest(currency: "PHP", value: 1000))

class NewMayaCreditTransferFundsViewModelTest: ViewModelTest {
    var viewModel: NewMayaCreditTransferFundsViewModelProtocol!
    private var mockRavenLocation: RavenLocation.Location!

    override func setUp() {
        super.setUp()

        mockRavenLocation = RavenLocation.Location(latitude: 10.0, longitude: 10.0, isMock: true)

        stub(ravenActionProvider) { stub in
            when(stub.makeGetGeolocationAction()).then {
                return Action {
                    SignalProducer { [weak self] observer, _ in
                        guard let self else { return }
                        ravenWrapper.getCurrentLocation { loc, err in
                            if let loc {
                                observer.send(value: loc)
                                observer.sendCompleted()
                            } else if let err {
                                observer.send(error: err)
                            }
                        }
                    }
                }
            }
            when(stub.makeExtractAction()).then {
                return Action {
                    SignalProducer { [weak self] observer, _ in
                        guard let self else { return }
                        self.ravenWrapper.extract(customerID: customerId,
                                                  transactionID: nil,
                                                  triggeredBy: Constants.Trigger.lending,
                                                  eventTrigger: Constants.EventTrigger.credit) { result in
                            switch result {
                            case .success:
                                observer.sendCompleted()
                            case .failure:
                                observer.send(error: PayMayaError())
                            }
                        }
                    }
                }
            }
        }

        viewModel = NewMayaCreditTransferFundsViewModel(creditAccount: creditAccount)
        XCTAssertFalse(viewModel.isExceedsCashInLimit.value)
        XCTAssertFalse(viewModel.isExceedsAvailableCreditLimit.value)
        XCTAssertTrue(viewModel.isCashInLimitHidden.value)
        XCTAssertNil(viewModel.inlineValidationMessage.value)
        XCTAssertNil(viewModel.cashInLimitValue)
        XCTAssertEqual(creditAccount, viewModel.creditAccount)
    }

    func testAvailableCreditLimitValue() {
        XCTAssertEqual("₱15,000.00", viewModel.availableCreditLimitValue)

        let newCreditAccount = MayaCreditAccount(id: "5e7b0b60-b2a2-406d-b4af-7ad56e8cc1e7", applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "ACTIVE", currencyCode: "₱", loanAmount: 12000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 3000, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: MayaCreditAccount.ScheduleSettings(gracePeriodSettings: MayaCreditAccount.ScheduleSettings.GracePeriodSettings(type: "INTEREST_FORGIVENESS", unit: "DAYS", value: 15), billingSettings: MayaCreditAccount.ScheduleSettings.BillingSettings(periodEndDayOfMonth: MayaCreditAccount.ScheduleSettings.BillingSettings.PeriodEndDayOfMonth(minValue: 1, maxValue: 27)), userPreference: MayaCreditAccount.ScheduleSettings.UserPreference(periodEndDayOfMonth: 2, periodStartDayOfMonth: 4)), repaymentSettings: nil, createdDate: Date(timeIntervalSince1970: **********), reasonCode: nil, daysInArrears: 0, daysLate: 0)
        viewModel = NewMayaCreditTransferFundsViewModel(creditAccount: newCreditAccount)
        XCTAssertEqual("₱9,000.00", viewModel.availableCreditLimitValue)
    }

    func testAmountFieldValidation() {
        viewModel.createTransferAction.apply().start()
        XCTAssertFalse(viewModel.isExceedsCashInLimit.value)
        XCTAssertFalse(viewModel.isExceedsAvailableCreditLimit.value)
        XCTAssertEqual(MayaCreditTransferFundsAmountInputState.amountEmpty.validationMessage, viewModel.inlineValidationMessage.value)

        viewModel.amountProperty.mutableProperty.value = "1"
        XCTAssertFalse(viewModel.isExceedsCashInLimit.value)
        XCTAssertFalse(viewModel.isExceedsAvailableCreditLimit.value)
        XCTAssertNil(viewModel.inlineValidationMessage.value)

        viewModel.amountProperty.mutableProperty.value = ""
        XCTAssertFalse(viewModel.isExceedsCashInLimit.value)
        XCTAssertFalse(viewModel.isExceedsAvailableCreditLimit.value)
        XCTAssertEqual(MayaCreditTransferFundsAmountInputState.amountEmpty.validationMessage, viewModel.inlineValidationMessage.value)

        viewModel.amountProperty.mutableProperty.value = "20000"
        XCTAssertFalse(viewModel.isExceedsCashInLimit.value)
        XCTAssertTrue(viewModel.isExceedsAvailableCreditLimit.value)
        XCTAssertEqual(MayaCreditTransferFundsAmountInputState.availableCreditLimitExceeds.validationMessage, viewModel.inlineValidationMessage.value)

        let accountLimitList = MayaAccountLimitsViewModelTest.defaultAccountLimitList()
        stub(reactiveAPIProvider) { stub in
            when(stub.request(any(APITargetType.self))).thenReturn(getBackendError())
            when(stub.request(isEqual(to: API.AccountLimits.getAccountLimits), type: isEqual(to: AccountLimitList.self))).thenReturn(getSuccessProducer(accountLimitList))
        }
        viewModel.getAccountLimitsAction.apply().start()

        viewModel.amountProperty.mutableProperty.value = "11000"
        XCTAssertTrue(viewModel.isExceedsCashInLimit.value)
        XCTAssertFalse(viewModel.isExceedsAvailableCreditLimit.value)
        XCTAssertEqual(MayaCreditTransferFundsAmountInputState.cashInLimitExceeds.validationMessage, viewModel.inlineValidationMessage.value)

        viewModel.amountProperty.mutableProperty.value = "21000"
        XCTAssertTrue(viewModel.isExceedsCashInLimit.value)
        XCTAssertTrue(viewModel.isExceedsAvailableCreditLimit.value)
        XCTAssertEqual(MayaCreditTransferFundsAmountInputState.availableAndCashInLimitsExceed.validationMessage, viewModel.inlineValidationMessage.value)

        let newCreditAccount = MayaCreditAccount(id: "5e7b0b60-b2a2-406d-b4af-7ad56e8cc1e7", applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "ACTIVE", currencyCode: "₱", loanAmount: 12000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 12000, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: MayaCreditAccount.ScheduleSettings(gracePeriodSettings: MayaCreditAccount.ScheduleSettings.GracePeriodSettings(type: "INTEREST_FORGIVENESS", unit: "DAYS", value: 15), billingSettings: MayaCreditAccount.ScheduleSettings.BillingSettings(periodEndDayOfMonth: MayaCreditAccount.ScheduleSettings.BillingSettings.PeriodEndDayOfMonth(minValue: 1, maxValue: 27)), userPreference: MayaCreditAccount.ScheduleSettings.UserPreference(periodEndDayOfMonth: 2, periodStartDayOfMonth: 4)), repaymentSettings: nil, createdDate: Date(timeIntervalSince1970: **********), reasonCode: nil, daysInArrears: 0, daysLate: 0)
        viewModel = NewMayaCreditTransferFundsViewModel(creditAccount: newCreditAccount)
        XCTAssertFalse(viewModel.isExceedsCashInLimit.value)
        XCTAssertTrue(viewModel.isExceedsAvailableCreditLimit.value)
        XCTAssertEqual(NewMayaCreditTransferFundsAmountInputState.maximumCreditLimitReached.validationMessage, viewModel.inlineValidationMessage.value)

        viewModel.amountProperty.mutableProperty.value = "21000"
        XCTAssertFalse(viewModel.isExceedsCashInLimit.value)
        XCTAssertTrue(viewModel.isExceedsAvailableCreditLimit.value)
        XCTAssertEqual(NewMayaCreditTransferFundsAmountInputState.maximumCreditLimitReached.validationMessage, viewModel.inlineValidationMessage.value)
    }

    func testSuccessCreateTransferAction() {
        viewModel.amountProperty.mutableProperty.value = "1"
        viewModel.createTransferAction.completed.observeValues { response in
            XCTAssertNotNil(response)
        }?.addToDisposeBag(disposeBag)

        viewModel.createTransferAction.errors.observeValues { _ in
            XCTFail("Must not send failure signal")
        }?.addToDisposeBag(disposeBag)
        viewModel.createTransferAction.apply().start()
    }

    func testFailedCreateTransferAction() {
        viewModel.createTransferAction.completed.observeValues { _ in
            XCTFail("Must not send completed signal")
        }?.addToDisposeBag(disposeBag)

        viewModel.createTransferAction.errors.observeValues { error in
            XCTAssertEqual(error.type, .validation)
        }?.addToDisposeBag(disposeBag)
        viewModel.createTransferAction.apply().start()
    }

    func testSuccessAccountLimitsAction() {
        let accountLimitList = MayaAccountLimitsViewModelTest.defaultAccountLimitList()
        stub(reactiveAPIProvider) { stub in
            when(stub.request(any(APITargetType.self))).thenReturn(getBackendError())
            when(stub.request(isEqual(to: API.AccountLimits.getAccountLimits), type: isEqual(to: AccountLimitList.self))).thenReturn(getSuccessProducer(accountLimitList))
        }

        viewModel.getAccountLimitsAction.completed.observeValues { [weak self] in
            XCTAssertEqual("₱ 10,000.00", self?.viewModel.cashInLimitValue)
            XCTAssertFalse(self?.viewModel.isCashInLimitHidden.value ?? true)
        }?.addToDisposeBag(disposeBag)

        viewModel.getAccountLimitsAction.errors.observeValues { _ in
            XCTFail("Must not send failure signal")
        }?.addToDisposeBag(disposeBag)

        viewModel.getAccountLimitsAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.AccountLimits.getAccountLimits), type: isEqual(to: AccountLimitList.self))
    }

    func testErrorAccountLimitsAction() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(any(APITargetType.self))).thenReturn(getBackendError())
            when(stub.request(isEqual(to: API.AccountLimits.getAccountLimits), type: isEqual(to: AccountLimitList.self))).thenReturn(getBackendError())
        }

        viewModel.getAccountLimitsAction.completed.observeValues { _ in
            XCTFail("Must not send success signal")
        }?.addToDisposeBag(disposeBag)

        viewModel.getAccountLimitsAction.errors.observeValues { [weak self] _ in
            XCTAssertNil(self?.viewModel.cashInLimitValue ?? nil)
            XCTAssertTrue(self?.viewModel.isCashInLimitHidden.value ?? true)
        }?.addToDisposeBag(disposeBag)

        viewModel.getAccountLimitsAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.AccountLimits.getAccountLimits), type: isEqual(to: AccountLimitList.self))
    }
}

// Test transfer funds enhanced flow
extension NewMayaCreditTransferFundsViewModelTest {
    func testGetAccountNumberLastFourDigits() {
        XCTAssertEqual(viewModel.accountNumberLastFourDigits, "3519")
    }

    func testcalculateDisbursementActionSuccess() {
        let user = StubUser()
        user.walletId = customerId

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.calculateDisbursement(rrn, calculateDisbursementRequest)), type: isEqual(to: MayaCreditFeesAndTaxes.self)))
                .thenReturn(getSuccessProducer(feesAndTaxes))
        }
        viewModel = NewMayaCreditTransferFundsViewModel(creditAccount: creditAccount)

        viewModel.calculateDisbursementAction.apply((rrn, "1000")).start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.calculateDisbursement(rrn, calculateDisbursementRequest)), type: isEqual(to: MayaCreditFeesAndTaxes.self))
    }
}

// Test geo location flow
extension NewMayaCreditTransferFundsViewModelTest {
    func testGeoLocationNotDeterminedErrorThenShouldRequestLocationPermission() {
        stub(ravenWrapper) { stub in
            when(stub.getCurrentLocation(completion: any())).then { completion in
                completion(nil, .notDetermined)
            }
        }

        let expectation = XCTestExpectation(description: "Wait for error")

        viewModel.handleRavenGetGeolocation.errors
            .observe(on: UIScheduler())
            .observeValues { err in
                XCTAssertTrue(err == RavenLocation.Error.notDetermined)
                expectation.fulfill()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.apply().start()

        wait(for: [expectation], timeout: 1.0)
    }

    func testGeoLocationPermissionDeniedThenShouldContinueTransfer() {
        stub(ravenWrapper) { stub in
            when(stub.getCurrentLocation(completion: any())).then { completion in
                completion(nil, .denied)
            }
        }

        let expectation = XCTestExpectation(description: "Wait for geolocation error")

        viewModel.amountProperty.mutableProperty.value = "1"
        viewModel.createTransferAction.completed
            .observe(on: UIScheduler())
            .observeValues { response in
                XCTAssertNotNil(response)
            }?.addToDisposeBag(disposeBag)

        viewModel.createTransferAction.errors
            .observe(on: UIScheduler())
            .observeValues { _ in
                XCTFail("Must not send failure signal")
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] err in
                guard let self else { return }
                XCTAssertTrue(err == RavenLocation.Error.denied)
                viewModel.createTransferAction.apply().start()
                expectation.fulfill()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.apply().start()

        wait(for: [expectation], timeout: 1.0)
    }

    func testGeoLocationRestrictedErrorThenShouldContinueTransfer() {
        stub(ravenWrapper) { stub in
            when(stub.getCurrentLocation(completion: any())).then { completion in
                completion(nil, .restricted)
            }
        }

        let expectation = XCTestExpectation(description: "Wait for geolocation error")

        viewModel.amountProperty.mutableProperty.value = "1"
        viewModel.createTransferAction.completed
            .observe(on: UIScheduler())
            .observeValues { response in
                XCTAssertNotNil(response)
            }?.addToDisposeBag(disposeBag)

        viewModel.createTransferAction.errors
            .observe(on: UIScheduler())
            .observeValues { _ in
                XCTFail("Must not send failure signal")
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] err in
                guard let self else { return }
                XCTAssertTrue(err == RavenLocation.Error.restricted)
                viewModel.createTransferAction.apply().start()
                expectation.fulfill()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.apply().start()

        wait(for: [expectation], timeout: 1.0)
    }

    func testGeoLocationUnknownErrorThenShouldContinueTransfer() {
        stub(ravenWrapper) { stub in
            when(stub.getCurrentLocation(completion: any())).then { completion in
                completion(nil, .unknown)
            }
        }

        let expectation = XCTestExpectation(description: "Wait for geolocation error")

        viewModel.amountProperty.mutableProperty.value = "1"
        viewModel.createTransferAction.completed
            .observe(on: UIScheduler())
            .observeValues { response in
                XCTAssertNotNil(response)
            }?.addToDisposeBag(disposeBag)

        viewModel.createTransferAction.errors
            .observe(on: UIScheduler())
            .observeValues { _ in
                XCTFail("Must not send failure signal")
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] err in
                guard let self else { return }
                XCTAssertTrue(err == RavenLocation.Error.unknown)
                viewModel.createTransferAction.apply().start()
                expectation.fulfill()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.apply().start()

        wait(for: [expectation], timeout: 1.0)
    }

    func testGeoLocationAllowedPermisionThenShouldHandleExtract() {
        stub(configurationServiceV2) { stub in
            when(stub.lendingDataExtractionEnabled.get).thenReturn(trueConfigV2())
        }

        stub(ravenWrapper) { stub in
            when(stub.getCurrentLocation(completion: any())).then { [weak self] completion in
                guard let self else { return }
                completion(self.mockRavenLocation, nil)
            }
            when(stub.extract(customerID: customerId, transactionID: isNil(), triggeredBy: Constants.Trigger.lending, eventTrigger: Constants.EventTrigger.credit, completion: anyOptional())).then { _, _, _, _, completion in
                                                                                                                                                                                                   completion?(.success(()))
            }
        }

        let expectation = XCTestExpectation(description: "Wait for geolocation complete")

        viewModel.handleRavenGetGeolocation.errors
            .observe(on: UIScheduler())
            .observeValues { _ in
                XCTFail("Must not send failure signal")
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.completed
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self else { return }
                viewModel.handleRavenExtract.apply().start()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenExtract.completed
            .observe(on: UIScheduler())
            .observeValues { _ in
                expectation.fulfill()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.apply().start()

        wait(for: [expectation], timeout: 1.0)

        verify(ravenWrapper, times(1)).extract(customerID: customerId, transactionID: isNil(), triggeredBy: Constants.Trigger.lending, eventTrigger: Constants.EventTrigger.credit, completion: anyOptional())
    }

    func testHandleExtractSuccess() {
        stub(configurationServiceV2) { stub in
            when(stub.lendingDataExtractionEnabled.get).thenReturn(trueConfigV2())
            when(stub.mecGeolocationEnabled.get).thenReturn(trueConfigV2())
        }

        stub(ravenWrapper) { stub in
            when(stub.extract(customerID: customerId, transactionID: isNil(), triggeredBy: Constants.Trigger.lending, eventTrigger: Constants.EventTrigger.credit, completion: anyOptional())).then { _, _, _, _, completion in
                                                                                                                                                                                                   completion?(.success(()))
            }
        }

        let expectation = XCTestExpectation(description: "Wait for extract completion")

        viewModel.handleRavenExtract.completed
            .observe(on: UIScheduler())
            .observeValues { _ in
                XCTAssert(true)
                expectation.fulfill()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenExtract.apply().start()
        XCTAssertTrue(viewModel.isGeolocationEnabled == true)
        XCTAssertTrue(viewModel.isLendingDataExtractionEnabled == true)

        wait(for: [expectation], timeout: 1.0)

        verify(ravenWrapper, times(1)).extract(customerID: customerId, transactionID: isNil(), triggeredBy: Constants.Trigger.lending, eventTrigger: Constants.EventTrigger.credit, completion: anyOptional())
    }

    func testHandleExtractFailed() {
        stub(configurationServiceV2) { stub in
            when(stub.lendingDataExtractionEnabled.get).thenReturn(falseConfigV2())
        }

        stub(ravenWrapper) { stub in
            when(stub.extract(customerID: customerId, transactionID: isNil(), triggeredBy: Constants.Trigger.lending, eventTrigger: Constants.EventTrigger.credit, completion: anyOptional())).then { _, _, _, _, completion in
                completion?(.failure(PayMayaError()))
            }
        }

        let expectation = XCTestExpectation(description: "Wait for extract error")

        viewModel.handleRavenExtract.errors
            .observe(on: UIScheduler())
            .observeValues { error in
                XCTAssertNotNil(error, "Error must not be nil")
                expectation.fulfill()
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenExtract.apply().start()

        wait(for: [expectation], timeout: 1.0)
    }
}
