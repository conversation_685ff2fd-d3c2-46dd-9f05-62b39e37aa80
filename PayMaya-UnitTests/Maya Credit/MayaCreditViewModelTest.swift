//
//  MayaCreditViewModelTest.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON>az<PERSON> Paola Barroga on 3/6/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import AssetProvider
import ConfigurationService
import Cuckoo
import Error
import Foundation
import Injector
import MayaCoreData
import Moya
import ReactiveSwift
import StoreProvider
import XCTest

private let loanAccountID = "5e7b0b60-b2a2-406d-b4af-7ad56e8cc1e7"
private let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "ACTIVE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 0, daysLate: 0)
private let bookedLoanApplication = MayaCreditCurrentLoanApplication(id: "2416fc66-c11f-4c83-bf50-e4dc860fdc56", applicationNumber: "a844a45f-25eb-4ac3-a62d-ffc5b51418de", term: MayaCreditCurrentLoanApplication.Term(id: "3f3ead12-242a-4e8f-8de5-304648715ddb", loanAmount: 15000, currency: "PHP", feeRate: 0.1), loanAccountId: loanAccountID, status: "BOOKED")
private let statementId = "34hj24u-324h33-31i3p4"
private let billingStatementJSON = """
            {
                "statement_id": "34hj24u-324h33-31i3p4",
                "statement_reference": "003",
                "start_date_time": "2021-09-01T00:00:00.221722Z",
                "end_date_time": "2021-09-30T23:59:59.221722Z",
                "creation_date_time": "2021-10-01T00:00:00.221722Z",
                "statement_description": [ "September 2021 Statement"],
                "due_date": "2021-09-15",
                "total_amount_due": 423.50,
                "total_transaction_amount": 400.00,
                "total_penalties_due": 3.50,
                "total_service_fee": 20.50,
                "currency": "PHP"
            }
        """
private let errorTitleOptIn = L10n.Error.Maya.Credit.Optin.title
private let optInConsent = MayaCreditOptInConsent(id: 1, type: "type", version: "1", majorVersion: "1.0", value: "opt in consent")
private let optInConsentWithoutValue = MayaCreditOptInConsent(id: 1, type: "type", version: "1", majorVersion: "1.0", value: nil)
private let contactReference = MayaCreditContactReference(customerId: "77bd4f29-52c1-4627-b7ce-9227d079bc84", firstName: "Juan", lastName: "BNPL", alternativeMobileNumber: "+639672653929", alternativeLandlineNumber: nil, gender: "FEMALE", maritalStatus: "DIVORCED_OR_SEPARATED", contactReference: MayaCreditContactReference.ContactReference(firstName: "John", lastName: "Doe", relationship: "PARENT", mobileNumber: "09000001234"))
private let mayaCreditFeeRate = MayaCreditFeeRate(feeRate: "5.5")
private let term = MayaCreditEligibility.Term(id: "35a6d1d2-1955-4c03-9020-72374bcc7c57", expiresOn: Date(), loanAmount: 15000, currency: "PHP", interestRate: 0.502, feeRate: 0.05, scheduleSettings: MayaCreditScheduleSettings(gracePeriodSettings: MayaCreditScheduleSettings.GracePeriodSettings(type: "INTEREST_FORGIVENESS", unit: "DAYS", value: 15), billingSettings: MayaCreditScheduleSettings.BillingSettings(periodEndDayOfMonth: MayaCreditScheduleSettings.BillingSettings.PeriodEndDayOfMonth(minValue: 1, maxValue: 27, days: [5, 6, 7, 8, 9]))), agreements: [])
private let eligibility = MayaCreditEligibility(consents: [MayaCreditOptInConsent(id: 2198, type: "Date Privacy", version: "1.0", majorVersion: "1.0", value: "value")], term: term, id: nil, product: nil, status: nil, expiresOn: nil, riskLevel: nil)
private let creditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "ACTIVE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 12345.6, total: 350.56), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: MayaCreditAccount.RepaymentSettings(biller: "MAYACREDIT"), createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 0, daysLate: 0)
private let alertActiveLocked = MayaAlertViewModel(title: "Account under review", message: "Your account is currently being reviewed for unlocking. While waiting, you can still use your Maya wallet.", image: CommonAsset.Images.Alert.image3DPending.image)

// maya credit v3
private let uuid = "abb86e81-d319-4a54-afd7-c6ed6f0cb2d6"
private let deviceId = "abb86e81d3194a54afd7c6ed6f0cb2d6"
private let osVersion = "16.2"
private let device = EligibilityRequest.Device(id: deviceId, channel: DeviceType.kind, version: osVersion)
private let eligibilityRequest = EligibilityRequest(requestReferenceNumber: uuid, productKey: Constants.Defaults.Common.mayaCreditProductKey.rawValue, device: device)
private let feeRate = MayaCreditFeeRate(feeRate: "0.05")

class MayaCreditViewModelTest: ViewModelTest {
    private var viewModel: MayaCreditViewModelProtocol!
    private var toggleBalanceShownService: MockToggleBalanceShownService!

    private let currency = Constants.Defaults.Common.peso.rawValue
    private let isBalanceHiddenProperty = MutableProperty(false)

    private let getContactReferenceSuccessResponse = SignalProducer<MayaCreditContactReference, Error>(value: contactReference)
    private let getFeeRateSuccessResponse = SignalProducer<MayaCreditFeeRate, Error>(value: feeRate)

    override func setUp() {
        super.setUp()
        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.hideCreditBalanceOn))).thenReturn(nil)
        }
        toggleBalanceShownService = ContainerWrapper.shared.resolve(ToggleBalanceShownService.self) as? MockToggleBalanceShownService
        stub(toggleBalanceShownService) { stub in
            when(stub.getBalanceHiddenProperty(for: isEqual(to: .credit))).thenReturn(isBalanceHiddenProperty)
        }

        stub(configurationService) { stub in
            when(stub.mayaCreditPartnerMerchantEnabled.get).thenReturn(falseConfig())
        }
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self)))
                .thenReturn(getContactReferenceSuccessResponse)
            when(stub.request(isEqual(to: API.MayaCredit.getFeeRate), type: isEqual(to: MayaCreditFeeRate.self)))
                .thenReturn(getFeeRateSuccessResponse)
        }
        stub(databaseStore) { stub in
            let mayaCreditTransaction = StubMayaCreditTransaction()
            let sortDescriptor = [NSSortDescriptor(key: "dateTime", ascending: false), NSSortDescriptor(key: "transactionId", ascending: false, selector: #selector(NSString.localizedStandardCompare(_:)))]
            when(stub.fetch(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.creditTransaction, sort: sortDescriptor)), limit: any())).thenReturn(StoreResult.success([mayaCreditTransaction]))
        }
        viewModel = MayaCreditViewModel(deviceId: deviceId, osVersion: osVersion)
    }

    func getAlertOverdue(days: Int, amount: Float) -> MayaAlertViewModel {
        return MayaAlertViewModel(title: "Your account is about to be locked", message: "Your account will be locked in \(days) days unless your overdue credit bill of ₱\(String(format: "%.2f", amount)) is paid", image: CommonAsset.Images.Alert.image3DFailed.image)
    }

    func getAlertOverdueTomorrow(amount: Float) -> MayaAlertViewModel {
        return MayaAlertViewModel(title: "Your account is about to be locked", message: "Your account will be locked tomorrow unless your overdue credit bill of ₱\(String(format: "%.2f", amount)) is paid", image: CommonAsset.Images.Alert.image3DFailed.image)
    }

    func getAlertLocked(amount: Float) -> MayaAlertViewModel {
        return MayaAlertViewModel(title: "Your account is locked", message: "Pay your overdue bill of ₱\(String(format: "%.2f", amount)) to avoid further penalties. Once paid, we will review your account for unlocking.", image: CommonAsset.Images.Alert.image3DFailed.image)
    }

    func testApprovedMayaCreditLoanApplication() {
        viewModel.loanApplicationStatusProperty.value = .approved

        XCTAssertEqual(viewModel.items.value, [.approved, .helpCenter, .logo, .contactInformation, .spacer])
    }

    func testSubmittedMayaCreditLoanApplication() {
        viewModel.loanApplicationStatusProperty.value = .submitted

        XCTAssertEqual(viewModel.items.value, [.submitted, .helpCenter, .logo, .contactInformation, .spacer])
    }

    func testGetLoanApplicationContentNotFoundError() {
        let eligibility = MayaCreditEligibility(consents: [], term: term, id: nil, product: nil, status: nil, expiresOn: nil, riskLevel: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getBackendError(code: -6))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(LoanApplicationStatus.none, viewModel.loanApplicationStatusProperty.value)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
    }

    func testGetMayaCreditConsentsUnknownError() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getConsents), type: isEqual(to: [MayaCreditOptInConsent].self))).thenReturn(getUnknownError())
        }

        viewModel.getMayaCreditConsents.errors.observeValues { [weak self] error in
            XCTAssertEqual(error, self?.getMayaUnknownError())
        }?.addToDisposeBag(disposeBag)

        viewModel.getMayaCreditConsents.completed.observeValues {
            XCTFail("Must not send completed signal")
        }?.addToDisposeBag(disposeBag)

        viewModel.getMayaCreditConsents.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getConsents), type: isEqual(to: [MayaCreditOptInConsent].self))
    }

    func testGetMayaCreditConsentsBackendError() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getConsents), type: isEqual(to: [MayaCreditOptInConsent].self))).thenReturn(getBackendError())
        }

        viewModel.getMayaCreditConsents.errors.observeValues { [weak self] error in
            XCTAssertEqual(error, self?.getMayaError())
        }?.addToDisposeBag(disposeBag)

        viewModel.getMayaCreditConsents.completed.observeValues {
            XCTFail("Must not send completed signal")
        }?.addToDisposeBag(disposeBag)

        viewModel.getMayaCreditConsents.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getConsents), type: isEqual(to: [MayaCreditOptInConsent].self))
    }

    func testGetMayaCreditConsentsSessionTimeoutError() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getConsents), type: isEqual(to: [MayaCreditOptInConsent].self))).thenReturn(getSessionTimeoutError())
        }

        viewModel.getMayaCreditConsents.errors.observeValues { [weak self] error in
            XCTAssertEqual(error, self?.getMayaSessionTimeoutError())
        }?.addToDisposeBag(disposeBag)

        viewModel.getMayaCreditConsents.completed.observeValues {
            XCTFail("Must not send completed signal")
        }?.addToDisposeBag(disposeBag)

        viewModel.getMayaCreditConsents.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getConsents), type: isEqual(to: [MayaCreditOptInConsent].self))
    }

    func testIsAccountUpgraded() {
        let user = StubUser()
        user.kycStatus = KYCStatus.submitted.rawValue
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }
        XCTAssertFalse(viewModel.isAccountUpgraded)
    }

    func testGetDisplayBalanceWithBalanceHidden() {
        viewModel.creditAccount.value = mayaCreditAccount
        viewModel.isBalanceHiddenProperty.value = true
        XCTAssertEqual(viewModel.displayBalance, "\(currency)••••••••")
    }

    func testGetDisplayBalanceWithBalanceShown() {
        let creditLimit = mayaCreditAccount.loanAmount - mayaCreditAccount.balances.principal
        viewModel.creditAccount.value = mayaCreditAccount
        viewModel.isBalanceHiddenProperty.value = false
        XCTAssertEqual(viewModel.displayBalance, "\(currency)\(NumberFormatter().currencyStringValue(double: Double(creditLimit)))")
    }

    func testGetDisplayBalanceWithoutBalance() {
        viewModel.creditAccount.value = nil
        viewModel.isBalanceHiddenProperty.value = false
        XCTAssertEqual(viewModel.displayBalance, "\(currency)\(Constants.Defaults.Common.amount.rawValue)")
        viewModel.isBalanceHiddenProperty.value = true
        XCTAssertEqual(viewModel.displayBalance, "\(currency)••••••••")
    }

    func testIsBalanceHidden() {
        isBalanceHiddenProperty.value = false
        XCTAssertFalse(viewModel.isBalanceHiddenProperty.value)

        isBalanceHiddenProperty.value = true
        XCTAssertTrue(viewModel.isBalanceHiddenProperty.value)
    }

    func testToggleHideBalance() {
        stub(toggleBalanceShownService) { stub in
            when(stub.toggle(for: isEqual(to: .credit))).thenDoNothing()
        }
        viewModel.toggleHideBalance()
        verify(toggleBalanceShownService, times(1)).toggle(for: isEqual(to: .credit))
    }

    func testBuildPayEarlyCreditBillDeeplink() {
        let term = MayaCreditEligibility.Term(id: "35a6d1d2-1955-4c03-9020-72374bcc7c57", expiresOn: Date(), loanAmount: 15000, currency: "PHP", interestRate: 0.502, feeRate: 0.05, scheduleSettings: MayaCreditScheduleSettings(gracePeriodSettings: MayaCreditScheduleSettings.GracePeriodSettings(type: "INTEREST_FORGIVENESS", unit: "DAYS", value: 15), billingSettings: MayaCreditScheduleSettings.BillingSettings(periodEndDayOfMonth: MayaCreditScheduleSettings.BillingSettings.PeriodEndDayOfMonth(minValue: 1, maxValue: 27, days: [5, 6, 7, 8, 9]))), agreements: [])
        let eligibility = MayaCreditEligibility(consents: [MayaCreditOptInConsent(id: 2198, type: "Date Privacy", version: "1.0", majorVersion: "1.0", value: "value")], term: term, id: nil, product: nil, status: nil, expiresOn: nil, riskLevel: nil)
        let creditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "ACTIVE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 12345.6, total: 350.56), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: MayaCreditAccount.RepaymentSettings(biller: "MAYACREDIT"), createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 0, daysLate: 0)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(viewModel.buildPayEarlyCreditBillDeeplink(), URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=350.56")!)
    }

    func testBuildPayEarlyCreditBillDeeplinkMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        let term = MayaCreditEligibility.Term(id: "35a6d1d2-1955-4c03-9020-72374bcc7c57", expiresOn: Date(), loanAmount: 15000, currency: "PHP", interestRate: 0.502, feeRate: 0.05, scheduleSettings: MayaCreditScheduleSettings(gracePeriodSettings: MayaCreditScheduleSettings.GracePeriodSettings(type: "INTEREST_FORGIVENESS", unit: "DAYS", value: 15), billingSettings: MayaCreditScheduleSettings.BillingSettings(periodEndDayOfMonth: MayaCreditScheduleSettings.BillingSettings.PeriodEndDayOfMonth(minValue: 1, maxValue: 27, days: [5, 6, 7, 8, 9]))), agreements: [])
        let eligibility = MayaCreditEligibility(consents: [MayaCreditOptInConsent(id: 2198, type: "Date Privacy", version: "1.0", majorVersion: "1.0", value: "value")], term: term, id: nil, product: nil, status: nil, expiresOn: nil, riskLevel: nil)
        let creditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "ACTIVE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 12345.6, total: 350.56), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: MayaCreditAccount.RepaymentSettings(biller: "MAYACREDIT"), createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 0, daysLate: 0)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(viewModel.buildPayEarlyCreditBillDeeplink(), URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=350.56")!)
    }

    func testSubmitActionSuccess() {
        let request = MayaCreditSubmitConsentRequest(type: optInConsent.type, version: optInConsent.version, majorVersion: optInConsent.majorVersion)
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.submitConsent([request])))).thenReturn(getSuccessProducer())
        }

        viewModel.submitConsentAction.values.observeValues({ _ in
            // Do nothing
        })?.addToDisposeBag(disposeBag)

        viewModel.submitConsentAction.errors.observeValues({ error in
            XCTFail("Unexpected error encountered \(error)")
        })?.addToDisposeBag(disposeBag)

        viewModel.submitConsentAction.apply([optInConsent]).start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.submitConsent([request])))
    }

    func testSubmitActionSessionTimoutError() {
        let request = MayaCreditSubmitConsentRequest(type: optInConsent.type, version: optInConsent.version, majorVersion: optInConsent.majorVersion)
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.submitConsent([request])))).thenReturn(getSessionTimeoutError())
        }

        viewModel.submitConsentAction.values.observeValues({ response in
            XCTFail("Unexpected value encountered \(response)")
        })?.addToDisposeBag(disposeBag)

        viewModel.submitConsentAction.errors.observeValues { error in
            XCTAssertEqual(.sessionTimeout(backendErrorSpiel), error.type)
        }?.addToDisposeBag(disposeBag)

        viewModel.submitConsentAction.apply([optInConsent]).start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.submitConsent([request])))
    }

    func testSubmitActionBackendError() {
        let request = MayaCreditSubmitConsentRequest(type: optInConsent.type, version: optInConsent.version, majorVersion: optInConsent.majorVersion)
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.submitConsent([request])))).thenReturn(getBackendError())
        }

        viewModel.submitConsentAction.completed.observeValues({ _ in
            XCTFail("Should encounter an error \(#function)")
        })?.addToDisposeBag(disposeBag)

        viewModel.submitConsentAction.errors.observeValues({ error in
            XCTAssertEqual(error, self.getPayMayaError(errorTitleOptIn))
        })?.addToDisposeBag(disposeBag)

        viewModel.submitConsentAction.apply([optInConsent]).start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.submitConsent([request])))
    }

    func testSubmitActionUnknownError() {
        let request = MayaCreditSubmitConsentRequest(type: optInConsent.type, version: optInConsent.version, majorVersion: optInConsent.majorVersion)
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.submitConsent([request])))).thenReturn(getUnknownError())
        }

        viewModel.submitConsentAction.completed.observeValues({ _ in
            XCTFail("Should encounter an error \(#function)")
        })?.addToDisposeBag(disposeBag)

        viewModel.submitConsentAction.errors.observeValues({ error in
            XCTAssertEqual(error, self.getPayMayaUnknownError(errorTitleOptIn))
        })?.addToDisposeBag(disposeBag)

        viewModel.submitConsentAction.apply([optInConsent]).start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.submitConsent([request])))
    }

    func testBuildPayNowDeepLink() {
        let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "ACTIVE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 12345.6, total: 350.56), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 350.56), scheduleSettings: nil, repaymentSettings: MayaCreditAccount.RepaymentSettings(biller: "MAYACREDIT"), createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 0, daysLate: 0)

        viewModel.creditAccount.value = mayaCreditAccount
        let url = viewModel.buildPayNowDeeplink()

        XCTAssertEqual(url, URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=350.56"))
    }
}

extension MayaCreditViewModelTest { // Test for Enhanced Flow
    func testBookedMayaCreditLoanApplicationEnhancedFlow() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getFeeRate), type: isEqual(to: MayaCreditFeeRate.self))).thenReturn(getSuccessProducer(mayaCreditFeeRate))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.loanApplicationStatusProperty.value = .booked(loanAccountID)

        XCTAssertEqual(viewModel.items.value, [.availableCredit, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testBookedMayaCreditLoanApplicationEnhancedFlowMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getFeeRate), type: isEqual(to: MayaCreditFeeRate.self))).thenReturn(getSuccessProducer(mayaCreditFeeRate))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.loanApplicationStatusProperty.value = .booked(loanAccountID)

        XCTAssertEqual(viewModel.items.value, [.availableCredit, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testGetLoanApplicationUnknownErrorEnhancedFlow() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getUnknownError())
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
            when(stub.request(isEqual(to: API.MayaCredit.getFeeRate), type: isEqual(to: MayaCreditFeeRate.self))).thenReturn(getSuccessProducer(mayaCreditFeeRate))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.loanApplicationStatusProperty.value = .booked(loanAccountID)

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(viewModel.items.value, [.error])

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testGetLoanApplicationUnknownErrorEnhancedFlowMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getUnknownError())
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
            when(stub.request(isEqual(to: API.MayaCredit.getFeeRate), type: isEqual(to: MayaCreditFeeRate.self))).thenReturn(getSuccessProducer(mayaCreditFeeRate))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.loanApplicationStatusProperty.value = .booked(loanAccountID)

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(viewModel.items.value, [.error])

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testGetLoanApplicationSessionTimeoutErrorEnhancedFlow() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSessionTimeoutError())
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getFeeRate), type: isEqual(to: MayaCreditFeeRate.self))).thenReturn(getSuccessProducer(mayaCreditFeeRate))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.loanApplicationStatusProperty.value = .booked(loanAccountID)

        viewModel.getMayaCreditLoanApplication.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testGetLoanApplicationSessionTimeoutErrorEnhancedFlowMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSessionTimeoutError())
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getFeeRate), type: isEqual(to: MayaCreditFeeRate.self))).thenReturn(getSuccessProducer(mayaCreditFeeRate))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.loanApplicationStatusProperty.value = .booked(loanAccountID)

        viewModel.getMayaCreditLoanApplication.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testBuildPayBillsDeeplinkEnhancedFlow() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getFeeRate), type: isEqual(to: MayaCreditFeeRate.self))).thenReturn(getSuccessProducer(mayaCreditFeeRate))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(viewModel.buildPayCreditBillDeeplink(), URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=423.5")!)
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testBuildPayBillsDeeplinkEnhancedFlowMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getFeeRate), type: isEqual(to: MayaCreditFeeRate.self))).thenReturn(getSuccessProducer(mayaCreditFeeRate))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(viewModel.buildPayCreditBillDeeplink(), URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=423.5")!)
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testBuildPayEarlyCreditBillDeeplinkEnhancedFlow() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getFeeRate), type: isEqual(to: MayaCreditFeeRate.self))).thenReturn(getSuccessProducer(mayaCreditFeeRate))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(viewModel.buildPayEarlyCreditBillDeeplink(), URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=350.56")!)
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testBuildPayEarlyCreditBillDeeplinkEnhancedFlowMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getFeeRate), type: isEqual(to: MayaCreditFeeRate.self))).thenReturn(getSuccessProducer(mayaCreditFeeRate))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(viewModel.buildPayEarlyCreditBillDeeplink(), URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=350.56")!)
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }
}

extension MayaCreditViewModelTest { // Test for Contact Reference
    func testBookedMayaCreditLoanApplicationWithContactReference() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.loanApplicationStatusProperty.value = .booked(loanAccountID)

        XCTAssertEqual(viewModel.items.value, [.availableCredit, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testBookedMayaCreditLoanApplicationWithContactReferenceMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.loanApplicationStatusProperty.value = .booked(loanAccountID)

        XCTAssertEqual(viewModel.items.value, [.availableCredit, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testGetLoanApplicationUnknownErrorWithContactReference() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getUnknownError())
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.loanApplicationStatusProperty.value = .booked(loanAccountID)

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(viewModel.items.value, [.error])

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testGetLoanApplicationUnknownErrorWithContactReferenceMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getUnknownError())
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.loanApplicationStatusProperty.value = .booked(loanAccountID)

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(viewModel.items.value, [.error])

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testGetLoanApplicationSessionTimeoutErrorWithContactReference() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSessionTimeoutError())
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.loanApplicationStatusProperty.value = .booked(loanAccountID)

        viewModel.getMayaCreditLoanApplication.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testGetLoanApplicationSessionTimeoutErrorWithContactReferenceMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSessionTimeoutError())
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.loanApplicationStatusProperty.value = .booked(loanAccountID)

        viewModel.getMayaCreditLoanApplication.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testBuildPayBillsDeeplinkWithContactReference() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(viewModel.buildPayCreditBillDeeplink(), URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=423.5")!)
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testBuildPayBillsDeeplinkWithContactReferenceMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(viewModel.buildPayCreditBillDeeplink(), URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=423.5")!)
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testBuildPayEarlyCreditBillDeeplinkWithContactReference() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(viewModel.buildPayEarlyCreditBillDeeplink(), URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=350.56")!)
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testBuildPayEarlyCreditBillDeeplinkWithContactReferenceMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(viewModel.buildPayEarlyCreditBillDeeplink(), URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=350.56")!)
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
    }

    func testShowUpdateContactReference() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getBackendError(code: -516))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()
        XCTAssertEqual(viewModel.showContactReference.value, true)
    }

    func testShowUpdateContactReferenceMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getBackendError(code: -516))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()
        XCTAssertEqual(viewModel.showContactReference.value, true)
    }

    func testHideUpdateContactReference() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()
        XCTAssertEqual(viewModel.showContactReference.value, false)
    }

    func testHideUpdateContactReferenceMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()
        XCTAssertEqual(viewModel.showContactReference.value, false)
    }

    func testShowAndHideUpdateContactReference() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getBackendError(code: -516))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()
        XCTAssertEqual(viewModel.showContactReference.value, true)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()
        XCTAssertEqual(viewModel.showContactReference.value, false)
    }

    func testShowAndHideUpdateContactReferenceMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getBackendError(code: -516))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()
        XCTAssertEqual(viewModel.showContactReference.value, true)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()
        XCTAssertEqual(viewModel.showContactReference.value, false)
    }
}

extension MayaCreditViewModelTest { // Test for locked/unlocked account
    func testbuildTransferAlertViewModelPastDue() {
        let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "PAST_DUE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 500), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 10, daysLate: 0)
        let url = URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=350.56")!

        let alertViewModel = viewModel.buildTransferAlertViewModel(account: mayaCreditAccount, url: url)
        XCTAssertEqual(alertViewModel, getAlertOverdue(days: 20, amount: 500.00))
    }

    func testbuildTransferAlertViewModelPastDue1dayRemaining() {
        let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "PAST_DUE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 315), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 29, daysLate: 0)
        let url = URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=350.56")!

        let alertViewModel = viewModel.buildTransferAlertViewModel(account: mayaCreditAccount, url: url)
        XCTAssertEqual(alertViewModel, getAlertOverdueTomorrow(amount: 315.00))
    }

    func testbuildTransferAlertViewModelSuspended() {
        let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "SUSPENDED", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 100), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 20, daysLate: 0)
        let url = URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=350.56")!

        let alertViewModel = viewModel.buildTransferAlertViewModel(account: mayaCreditAccount, url: url)
        XCTAssertEqual(alertViewModel, getAlertLocked(amount: 100.00))
    }

    func testbuildTransferAlertViewModelActiveSuspended() {
        let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "SUSPENDED", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 20, daysLate: 0)
        let url = URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=350.56")!

        let alertViewModel = viewModel.buildTransferAlertViewModel(account: mayaCreditAccount, url: url)
        XCTAssertEqual(alertViewModel, alertActiveLocked)
    }

    func testbuildTransferAlertViewModelPastDueMoreThan30() {
        let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "PAST_DUE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 50.17), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 31, daysLate: 0)
        let url = URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=350.56")!

        let alertViewModel = viewModel.buildTransferAlertViewModel(account: mayaCreditAccount, url: url)
        XCTAssertEqual(alertViewModel, getAlertLocked(amount: 50.17))
    }

    func testbuildTransferAlertViewModelPastDueEqualTo30() {
        let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "PAST_DUE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 620.15), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 620.15), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 30, daysLate: 0)
        let url = URL(string: "paymaya://paybills?account_number=************&biller=MAYACREDIT&amount=350.56")!

        let alertViewModel = viewModel.buildTransferAlertViewModel(account: mayaCreditAccount, url: url)
        XCTAssertEqual(alertViewModel, getAlertLocked(amount: 620.15))
    }

    func testAccountLockedPastDue() {
        let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "PAST_DUE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 10, daysLate: 0)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
        }

        viewModel.getMayaCreditLoanAccount.apply(loanAccountID).start()

        XCTAssertEqual(viewModel.isAccountLocked.value, false)
    }

    func testAccountLockedPastDueMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "PAST_DUE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 10, daysLate: 0)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
        }

        viewModel.getMayaCreditLoanAccount.apply(loanAccountID).start()

        XCTAssertEqual(viewModel.isAccountLocked.value, false)
    }

    func testAccountLockedSuspended() {
        let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "SUSPENDED", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 20, daysLate: 0)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
        }

        viewModel.getMayaCreditLoanAccount.apply(loanAccountID).start()

        XCTAssertEqual(viewModel.isAccountLocked.value, true)
    }

    func testAccountLockedSuspendedMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "SUSPENDED", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 20, daysLate: 0)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
        }

        viewModel.getMayaCreditLoanAccount.apply(loanAccountID).start()

        XCTAssertEqual(viewModel.isAccountLocked.value, true)
    }

    func testAccountLockedPastDueMoreThan30() {
        let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "PAST_DUE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 31, daysLate: 0)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
        }

        viewModel.getMayaCreditLoanAccount.apply(loanAccountID).start()

        XCTAssertEqual(viewModel.isAccountLocked.value, true)
    }

    func testAccountLockedPastDueMoreThan30MECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "PAST_DUE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 31, daysLate: 0)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
        }

        viewModel.getMayaCreditLoanAccount.apply(loanAccountID).start()

        XCTAssertEqual(viewModel.isAccountLocked.value, true)
    }

    func testAccountLockedPastEqualTo30() {
        let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "PAST_DUE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 30, daysLate: 0)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
        }

        viewModel.getMayaCreditLoanAccount.apply(loanAccountID).start()

        XCTAssertEqual(viewModel.isAccountLocked.value, true)
    }

    func testAccountLockedPastEqualTo30MECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        let mayaCreditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "PAST_DUE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: nil, createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 30, daysLate: 0)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
        }

        viewModel.getMayaCreditLoanAccount.apply(loanAccountID).start()

        XCTAssertEqual(viewModel.isAccountLocked.value, true)
    }
}

extension MayaCreditViewModelTest { // Test for toggling all financial values using eye icon
    func testGetDisplayBalanceWithEyeOn() {
        let creditLimit = mayaCreditAccount.loanAmount - mayaCreditAccount.balances.principal
        viewModel.creditAccount.value = mayaCreditAccount
        viewModel.isBalanceHiddenProperty.value = false
        XCTAssertEqual(viewModel.displayBalance, "\(currency)\(NumberFormatter().currencyStringValue(double: Double(creditLimit)))")
    }

    func testGetDisplayBalanceWithEyeOff() {
        viewModel.creditAccount.value = mayaCreditAccount
        viewModel.isBalanceHiddenProperty.value = true
        XCTAssertEqual(viewModel.displayBalance, "\(currency)\(Constants.Defaults.Common.hidenAmount.rawValue)")
    }

    func testGetUsedBalanceWithEyeOn() {
        viewModel.creditAccount.value = creditAccount
        let usedBalance = formatter.number.currencyStringValue(double: Double(creditAccount.balances.principal))
        viewModel.isBalanceHiddenProperty.value = false

        XCTAssertEqual(viewModel.usedBalance, L10n.Maya.Credit.Dashboard.Balance.used(String(format: "%@%@", currency, usedBalance)))
    }

    func testGetUsedBalanceWithEyeOff() {
        viewModel.creditAccount.value = mayaCreditAccount
        viewModel.isBalanceHiddenProperty.value = true

        XCTAssertEqual(viewModel.usedBalance, "\(currency)\(Constants.Defaults.Common.hidenAmount.rawValue) used")
    }

    func testGetCreditLimitWithEyeOn() {
        viewModel.creditAccount.value = creditAccount
        let creditLimit = formatter.number.currencyStringValue(double: Double(creditAccount.loanAmount))
        viewModel.isBalanceHiddenProperty.value = false

        XCTAssertEqual(viewModel.creditLimit, L10n.Maya.Credit.Dashboard.Balance.limit(String(format: "%@%@", currency, creditLimit)))
    }

    func testGetCreditLimitWithEyeOff() {
        viewModel.creditAccount.value = mayaCreditAccount
        viewModel.isBalanceHiddenProperty.value = true

        XCTAssertEqual(viewModel.creditLimit, "\(currency)\(Constants.Defaults.Common.hidenAmount.rawValue) limit")
    }

    func testGetAmountDueWithEyeOn() {
        viewModel.creditAccount.value = creditAccount
        viewModel.isBalanceHiddenProperty.value = false

        XCTAssertEqual(viewModel.amountDue, "\(currency)0.00")
    }

    func testGetAmountDueWithEyeOff() {
        viewModel.creditAccount.value = mayaCreditAccount
        viewModel.isBalanceHiddenProperty.value = true

        XCTAssertEqual(viewModel.amountDue, "\(currency)\(Constants.Defaults.Common.hidenAmount.rawValue)")
    }

    func testGetCreditBalanceThisPeriodWithEyeOn() {
        viewModel.creditAccount.value = creditAccount
        viewModel.isBalanceHiddenProperty.value = false

        XCTAssertEqual(viewModel.totalCreditBalance, "\(currency)\(creditAccount.balances.total)")
    }

    func testGetCreditBalanceThisPeriodWithEyeOff() {
        viewModel.creditAccount.value = mayaCreditAccount
        viewModel.isBalanceHiddenProperty.value = true

        XCTAssertEqual(viewModel.totalCreditBalance, "\(currency)\(Constants.Defaults.Common.hidenAmount.rawValue)")
    }
}

extension MayaCreditViewModelTest { // Test mayaCreditV3LoanApplication toggle enabled
    func testBookedMayaCreditLoanApplicationV3() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getFeeRate), type: isEqual(to: MayaCreditFeeRate.self))).thenReturn(getSuccessProducer(mayaCreditFeeRate))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.loanApplicationStatusProperty.value = .booked(loanAccountID)

        XCTAssertEqual(viewModel.items.value, [.availableCredit, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
        verify(reactiveAPIProvider, times(0)).request(isEqual(to: API.MayaCredit.getFeeRate), type: isEqual(to: MayaCreditFeeRate.self))
    }

    func testBookedMayaCreditLoanApplicationV3MECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
            when(stub.request(isEqual(to: API.MayaCredit.getFeeRate), type: isEqual(to: MayaCreditFeeRate.self))).thenReturn(getSuccessProducer(mayaCreditFeeRate))
            when(stub.request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))).thenReturn(getSuccessProducer(contactReference))
        }

        viewModel.loanApplicationStatusProperty.value = .booked(loanAccountID)

        XCTAssertEqual(viewModel.items.value, [.availableCredit, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getContactReference), type: isEqual(to: MayaCreditContactReference.self))
        verify(reactiveAPIProvider, times(0)).request(isEqual(to: API.MayaCredit.getFeeRate), type: isEqual(to: MayaCreditFeeRate.self))
    }

    func testGetLoanApplicationSuccessV3() {
        let term = MayaCreditEligibility.Term(id: "35a6d1d2-1955-4c03-9020-72374bcc7c57", expiresOn: Date(), loanAmount: 15000, currency: "PHP", interestRate: 0.502, feeRate: 0.05, scheduleSettings: MayaCreditScheduleSettings(gracePeriodSettings: MayaCreditScheduleSettings.GracePeriodSettings(type: "INTEREST_FORGIVENESS", unit: "DAYS", value: 15), billingSettings: MayaCreditScheduleSettings.BillingSettings(periodEndDayOfMonth: MayaCreditScheduleSettings.BillingSettings.PeriodEndDayOfMonth(minValue: 1, maxValue: 27, days: [5, 6, 7, 8, 9]))), agreements: [])
        let eligibility = MayaCreditEligibility(consents: [MayaCreditOptInConsent(id: 2198, type: "Date Privacy", version: "1.0", majorVersion: "1.0", value: "value")], term: term, id: nil, product: nil, status: nil, expiresOn: nil, riskLevel: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(.booked(loanAccountID), viewModel.loanApplicationStatusProperty.value)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
    }

    func testGetLoanApplicationSuccessV3MECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        let term = MayaCreditEligibility.Term(id: "35a6d1d2-1955-4c03-9020-72374bcc7c57", expiresOn: Date(), loanAmount: 15000, currency: "PHP", interestRate: 0.502, feeRate: 0.05, scheduleSettings: MayaCreditScheduleSettings(gracePeriodSettings: MayaCreditScheduleSettings.GracePeriodSettings(type: "INTEREST_FORGIVENESS", unit: "DAYS", value: 15), billingSettings: MayaCreditScheduleSettings.BillingSettings(periodEndDayOfMonth: MayaCreditScheduleSettings.BillingSettings.PeriodEndDayOfMonth(minValue: 1, maxValue: 27, days: [5, 6, 7, 8, 9]))), agreements: [])
        let eligibility = MayaCreditEligibility(consents: [MayaCreditOptInConsent(id: 2198, type: "Date Privacy", version: "1.0", majorVersion: "1.0", value: "value")], term: term, id: nil, product: nil, status: nil, expiresOn: nil, riskLevel: nil)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))).thenReturn(getSuccessProducer(bookedLoanApplication))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
        }

        viewModel.getMayaCreditLoanApplication.apply().start()

        XCTAssertEqual(.booked(loanAccountID), viewModel.loanApplicationStatusProperty.value)

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanApplication), type: isEqual(to: MayaCreditCurrentLoanApplication.self))
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
    }

    func testNoLoanApplication() {
        let user = StubUser()
        user.kycStatus = KYCStatus.approved.rawValue

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }

        viewModel.loanApplicationStatusProperty.value = LoanApplicationStatus.none

        XCTAssertEqual(viewModel.items.value, [.initialEligibility, .helpCenter, .logo, .contactInformation, .spacer])
    }
}

extension MayaCreditViewModelTest { // Test mayaCreditHomePageRevampEnabled toggle enabled
    func setUpHomePageRevampTest(mayaCreditAccount: MayaCreditAccount = creditAccount) {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(mayaCreditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
        }

        viewModel.loanApplicationStatusProperty.value = .booked(loanAccountID)
    }

    func testBookedMayaCreditLoanApplicationRevamp() {
        setUpHomePageRevampTest()

        XCTAssertEqual(viewModel.items.value, [.availableCredit, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
    }

    func testBookedMayaCreditLoanApplicationRevampMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        setUpHomePageRevampTest()

        XCTAssertEqual(viewModel.items.value, [.availableCredit, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())
    }

    func testOutstandingBalance() {
        setUpHomePageRevampTest()

        XCTAssertEqual(viewModel.outstandingBalance, "₱350.56")
        XCTAssertTrue(viewModel.isPayNowEnabled)
    }

    func testOutstandingBalanceMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        setUpHomePageRevampTest()

        XCTAssertEqual(viewModel.outstandingBalance, "₱350.56")
        XCTAssertTrue(viewModel.isPayNowEnabled)
    }

    func testLatestBill() {
        let creditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "ACTIVE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 12345.6, total: 350.56), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 120.00), scheduleSettings: nil, repaymentSettings: MayaCreditAccount.RepaymentSettings(biller: "MAYACREDIT"), createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 0, daysLate: 0)
        setUpHomePageRevampTest(mayaCreditAccount: creditAccount)

        XCTAssertEqual(viewModel.latestBill, "₱120.00")
        XCTAssertTrue(viewModel.isPayThisBillEnabled)
    }

    func testStatementMonth() {
        setUpHomePageRevampTest()

        XCTAssertEqual(viewModel.statementMonth, "Sep 2021")
    }

    func testDueDate() {
        setUpHomePageRevampTest()

        XCTAssertEqual(viewModel.dueDate, "September 15, 2021")
    }

    func testPayNowButtonDisabled() {
        let creditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: "ACTIVE", currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: MayaCreditAccount.RepaymentSettings(biller: "MAYACREDIT"), createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 0, daysLate: 0)
        setUpHomePageRevampTest(mayaCreditAccount: creditAccount)

        XCTAssertFalse(viewModel.isPayNowEnabled)
        XCTAssertEqual(viewModel.outstandingBalance, "₱0.00")
    }

    func testPayThisBillDisabled() {
        setUpHomePageRevampTest(mayaCreditAccount: creditAccount)

        XCTAssertFalse(viewModel.isPayThisBillEnabled)
        XCTAssertEqual(viewModel.latestBill, "₱0.00")
    }
}

// Mother's maiden name
extension MayaCreditViewModelTest {
    func testGetPersonDetails() {
        let personDetails = MayaCreditPersonDetails(alternativeMobileNumber: "***********", gender: "FEMALE", maritalStatus: "MARRIED", contactReference: MayaCreditPersonDetails.ContactReference(firstName: "Arya", lastName: "Stark", relationship: "SIBLING", mobileNumber: "***********"), mothersMaidenName: MayaCreditPersonDetails.MothersMaidenName(firstName: "Catelyn", middleName: nil, lastName: "Last", hasNoMiddleName: true))

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))).thenReturn(getSuccessProducer(personDetails))
        }

        viewModel.getPersonDetails.apply().start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MayaCredit.getPersonDetails), type: isEqual(to: MayaCreditPersonDetails.self))
    }
}

// Write off
extension MayaCreditViewModelTest {
    func setUpWriteOffTest(status: String, totalBalance: Float = 0) {
        let creditAccount = MayaCreditAccount(id: loanAccountID, applicationNumber: "bc378ba3-0527-46c1-8557-10f4eefc5505", promissoryNoteNumber: "************", status: status, currencyCode: "PHP", loanAmount: 15000, balances: MayaCreditAccount.Balances(fee: 0, interest: 0, penalty: 0, principal: 0, total: totalBalance), dues: MayaCreditAccount.Dues(fee: 0, interest: 0, penalty: 0, principal: 0, total: 0), scheduleSettings: nil, repaymentSettings: MayaCreditAccount.RepaymentSettings(biller: "MAYACREDIT"), createdDate: Date.distantPast, reasonCode: nil, daysInArrears: 0, daysLate: 0)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccount(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.getLoanAccountV2(loanAccountID)), type: isEqual(to: MayaCreditAccount.self), decoder: any())).thenReturn(getSuccessProducer(creditAccount))
            when(stub.request(isEqual(to: API.MayaCredit.loanApplicationEligibility(eligibilityRequest)), type: isEqual(to: MayaCreditEligibility.self), decoder: any())).thenReturn(getSuccessProducer(eligibility))
            when(stub.request(isEqual(to: API.MayaCredit.getBillingStatement(loanAccountID)))).thenReturn(SignalProducer<Response, Error>(value: Response(statusCode: 200, data: billingStatementJSON.data(using: .utf8)!)))
        }

        viewModel.loanApplicationStatusProperty.value = .booked(loanAccountID)
    }

    func testWriteOffToggle() {
        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(falseConfigV2())
        }
        XCTAssertFalse(viewModel.isWriteOffEnabled)

        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(trueConfigV2())
        }
        XCTAssertTrue(viewModel.isWriteOffEnabled)
    }

    func testWriteOffStatusToggleOn() {
        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(trueConfigV2())
        }
        setUpWriteOffTest(status: "WRITE_OFF")

        XCTAssertEqual(viewModel.items.value, [.writeOff, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
    }

    func testWriteOffStatusToggleOnMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(trueConfigV2())
        }
        setUpWriteOffTest(status: "WRITE_OFF")

        XCTAssertEqual(viewModel.items.value, [.writeOff, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
    }

    func testWriteOffStatusToggleOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(falseConfigV2())
        }
        setUpWriteOffTest(status: "WRITE_OFF")

        XCTAssertEqual(viewModel.items.value, [.availableCredit, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
    }

    func testWriteOffStatusToggleOffMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(falseConfigV2())
        }
        setUpWriteOffTest(status: "WRITE_OFF")

        XCTAssertEqual(viewModel.items.value, [.availableCredit, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
    }

    func testWrittenOffStatusToggleOn() {
        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(trueConfigV2())
        }
        setUpWriteOffTest(status: "WRITTEN_OFF")

        XCTAssertEqual(viewModel.items.value, [.writtenOff, .transactions, .manageMyCredit, .contactInformation, .spacer])
    }

    func testWrittenOffStatusToggleOnMECAPIGatewayToggledOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(trueConfigV2())
        }
        setUpWriteOffTest(status: "WRITTEN_OFF")

        XCTAssertEqual(viewModel.items.value, [.writtenOff, .transactions, .manageMyCredit, .contactInformation, .spacer])
    }

    func testWrittenOffStatusToggleOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(falseConfigV2())
        }
        setUpWriteOffTest(status: "WRITTEN_OFF")

        XCTAssertEqual(viewModel.items.value, [.availableCredit, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
    }

    func testWrittenOffStatusToggleOffMECAPIGatewayToggledOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(falseConfigV2())
        }
        setUpWriteOffTest(status: "WRITTEN_OFF")

        XCTAssertEqual(viewModel.items.value, [.availableCredit, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
    }

    func testPendingWrittenOffStatusToggleOnWithBalance() {
        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(trueConfigV2())
        }
        setUpWriteOffTest(status: "PENDING_WRITTEN_OFF", totalBalance: 100)

        XCTAssertEqual(viewModel.items.value, [.writeOff, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
        XCTAssertFalse(viewModel.isPayNowEnabled)
    }

    func testPendingWrittenOffStatusToggleOnWithBalanceMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(trueConfigV2())
        }
        setUpWriteOffTest(status: "PENDING_WRITTEN_OFF", totalBalance: 100)

        XCTAssertEqual(viewModel.items.value, [.writeOff, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
        XCTAssertFalse(viewModel.isPayNowEnabled)
    }

    func testPendingWrittenOffStatusToggleOffWithBalance() {
        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(falseConfigV2())
        }
        setUpWriteOffTest(status: "PENDING_WRITTEN_OFF", totalBalance: 100)

        XCTAssertEqual(viewModel.items.value, [.availableCredit, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
        XCTAssertTrue(viewModel.isPayNowEnabled)
    }

    func testPendingWrittenOffStatusToggleOffWithBalanceMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(falseConfigV2())
        }
        setUpWriteOffTest(status: "PENDING_WRITTEN_OFF", totalBalance: 100)

        XCTAssertEqual(viewModel.items.value, [.availableCredit, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
        XCTAssertTrue(viewModel.isPayNowEnabled)
    }

    func testPendingWrittenOffStatusToggleOnWithoutBalance() {
        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(trueConfigV2())
        }
        setUpWriteOffTest(status: "PENDING_WRITTEN_OFF")

        XCTAssertEqual(viewModel.items.value, [.writeOff, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
        XCTAssertFalse(viewModel.isPayNowEnabled)
    }

    func testPendingWrittenOffStatusToggleOnWithoutBalanceMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(trueConfigV2())
        }
        setUpWriteOffTest(status: "PENDING_WRITTEN_OFF")

        XCTAssertEqual(viewModel.items.value, [.writeOff, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
        XCTAssertFalse(viewModel.isPayNowEnabled)
    }

    func testPendingWrittenOffStatusToggleOffWithoutBalance() {
        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(falseConfigV2())
        }
        setUpWriteOffTest(status: "PENDING_WRITTEN_OFF")

        XCTAssertEqual(viewModel.items.value, [.availableCredit, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
        XCTAssertFalse(viewModel.isPayNowEnabled)
    }

    func testPendingWrittenOffStatusToggleOffWithoutBalanceMECAPIGatewayOff() {
        stub(configurationServiceV2) { stub in
            when(stub.mecAPIGatewayEnabled.get).thenReturn(falseConfigV2())
        }

        stub(configurationServiceV2) { stub in
            when(stub.mecWriteOffEnabled.get).thenReturn(falseConfigV2())
        }
        setUpWriteOffTest(status: "PENDING_WRITTEN_OFF")

        XCTAssertEqual(viewModel.items.value, [.availableCredit, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer])
        XCTAssertFalse(viewModel.isPayNowEnabled)
    }
}
