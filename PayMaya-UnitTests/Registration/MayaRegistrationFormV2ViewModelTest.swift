//
//  MayaRegistrationFormV2ViewModelTest.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON><PERSON> on 5/16/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import Cuckoo
import Injector
import MayaCoreData
import ReactiveSwift
import StoreProvider
import XCTest

private let validFirstName = "Juan"
private let validMiddleName = "Rodrigo"
private let validLastName = "Dela Cruz III"
private let validEmail = "<EMAIL>"
private let validPassword = "Password@1"
private let blacklistedPassword = "Password123"
private let validMobileNumber = "**********"

private let firstConsent = SecondaryConsent(name: "firstConsent", identifier: "firstIdentifier", consentDescription: "firstDesc", enabled: true)
private let secondConsent = SecondaryConsent(name: "secondConsent", identifier: "secondIdentifier", consentDescription: "secondDesc", enabled: false)
private var thirdConsent = SecondaryConsent(name: "thirdConsent", identifier: "thirdIdentifier", consentDescription: "thirdDesc", enabled: nil)
private let secondaryConsents: [SecondaryConsent] = [firstConsent, secondConsent, thirdConsent]

class MayaRegistrationFormV2ViewModelTest: ViewModelTest {
    private var viewModel: MayaRegistrationFormV2ViewModelProtocol!
    private var locationCheckerService: MockLocationCheckerServiceProtocol!

    override func setUp() {
        super.setUp()

        locationCheckerService = ContainerWrapper.shared.resolve(LocationCheckerServiceProtocol.self) as? MockLocationCheckerServiceProtocol

        stub(reactiveAPIProvider) { stub in
            let validPasswordRequest = ValidatePasswordRequest(password: validPassword)
            when(stub.request(isEqual(to: API.Registration.validatePassword(validPasswordRequest)))).thenReturn(getSuccessProducer())

            let blacklistedPasswordRequest = ValidatePasswordRequest(password: blacklistedPassword)
            when(stub.request(isEqual(to: API.Registration.validatePassword(blacklistedPasswordRequest)))).thenReturn(getBackendError())
        }

        let dpOptIns = DataPrivacyOptIns(consents: secondaryConsents)
        stub(encryptedDefaultsStore) { stub in
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.secondaryConsents), type: isEqual(to: DataPrivacyOptIns.self))).thenReturn(StoreResult.success(dpOptIns))
        }

        viewModel = MayaRegistrationFormV2ViewModel()
    }

    func testSuccessValidateAndFetchConsentsAction() {
        viewModel.validateAndFetchConsentsAction.completed.observeValues {
            XCTAssertTrue(true)
        }
        viewModel.passwordField.mutableProperty.value = validPassword
        viewModel.validateAndFetchConsentsAction.apply().start()
    }

    func testBackendErrorValidateAndFetchConsentsAction() {
        viewModel.validateAndFetchConsentsAction.errors.observeValues { error in
            XCTAssertNotNil(error)
        }?.addToDisposeBag(disposeBag)

        viewModel.validateAndFetchConsentsAction.completed.observeValues {
            XCTFail("Unexpected Completed signal received")
        }?.addToDisposeBag(disposeBag)
        viewModel.validateAndFetchConsentsAction.apply().start()
    }

    func testDefaultErrorValidateAndFetchConsentsAction() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(any(APITargetType.self))).thenReturn(getUnknownError())
        }
        viewModel.validateAndFetchConsentsAction.errors.observeValues { error in
            XCTAssertNotNil(error)
        }?.addToDisposeBag(disposeBag)

        viewModel.validateAndFetchConsentsAction.completed.observeValues {
            XCTFail("Unexpected Completed signal received")
        }?.addToDisposeBag(disposeBag)

        viewModel.validateAndFetchConsentsAction.apply().start()
    }

    func testToggleEnableConsent() {
        viewModel.secondaryConsentsProperty.value = [firstConsent, secondConsent, thirdConsent]
        viewModel.toggleEnableConsent(index: 0)
        XCTAssertEqual(viewModel.secondaryConsentsProperty.value[0].enabled, !(firstConsent.enabled ?? false))
        viewModel.toggleEnableConsent(index: 2)
        XCTAssertEqual(viewModel.secondaryConsentsProperty.value[2].enabled, !(thirdConsent.enabled ?? false))
    }

    func testEnableAllConsents() {
        viewModel.enableAllConsents(true)
        viewModel.secondaryConsentsProperty.value.forEach { consent in
            XCTAssertTrue(consent.accepted)
        }
        viewModel.enableAllConsents(false)
        viewModel.secondaryConsentsProperty.value.forEach { consent in
            XCTAssertTrue(!consent.accepted)
        }
    }

    func testToggleConsentDescription() {
        viewModel.secondaryConsentsProperty.value = [firstConsent, secondConsent, thirdConsent]
        viewModel.toggleShowConsentDescription(at: 0)
        XCTAssertTrue(viewModel.secondaryConsentsProperty.value[0].expanded!)
    }

    func testGetSecondaryConsentGroup() {
        let secondaryConsentDictionary = getConsentGroup()
        XCTAssertEqual(viewModel.getConsentGroup(), secondaryConsentDictionary)
    }

    func testSuccessRegisterAction() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Registration.registerV2(getReigstrationRequest())))).thenReturn(getSuccessProducer())
        }

        viewModel.firstNameField.mutableProperty.value = validFirstName
        viewModel.hasNoLegalMiddleName.value = false
        viewModel.middleNameField.mutableProperty.value = validMiddleName
        viewModel.lastNameField.mutableProperty.value = validLastName
        viewModel.emailAddressField.mutableProperty.value = validEmail
        viewModel.mobileNumberField.mutableProperty.value = validMobileNumber
        viewModel.passwordField.mutableProperty.value = validPassword

        viewModel.registerAction.errors.observeValues { _ in
            XCTFail("Unexpected Error signal received")
        }?.addToDisposeBag(disposeBag)

        viewModel.registerAction.completed.observeValues {
            XCTAssertTrue(true)
        }?.addToDisposeBag(disposeBag)

        viewModel.registerAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Registration.registerV2(getReigstrationRequest())))
    }

    func testDefaultErrorRegisterAction() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Registration.registerV2(getReigstrationRequest())))).thenReturn(getUnknownError())
        }

        viewModel.firstNameField.mutableProperty.value = validFirstName
        viewModel.hasNoLegalMiddleName.value = false
        viewModel.middleNameField.mutableProperty.value = validMiddleName
        viewModel.lastNameField.mutableProperty.value = validLastName
        viewModel.emailAddressField.mutableProperty.value = validEmail
        viewModel.mobileNumberField.mutableProperty.value = validMobileNumber
        viewModel.passwordField.mutableProperty.value = validPassword

        viewModel.registerAction.errors.observeValues { error in
            XCTAssertNotNil(error)
        }?.addToDisposeBag(disposeBag)

        viewModel.registerAction.completed.observeValues {
            XCTFail("Unexpected completion")
        }?.addToDisposeBag(disposeBag)

        viewModel.registerAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Registration.registerV2(getReigstrationRequest())))
    }

    func testBackendErrorRegisterAction() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Registration.registerV2(getReigstrationRequest())))).thenReturn(getBackendError())
        }

        viewModel.firstNameField.mutableProperty.value = validFirstName
        viewModel.hasNoLegalMiddleName.value = false
        viewModel.middleNameField.mutableProperty.value = validMiddleName
        viewModel.lastNameField.mutableProperty.value = validLastName
        viewModel.emailAddressField.mutableProperty.value = validEmail
        viewModel.mobileNumberField.mutableProperty.value = validMobileNumber
        viewModel.passwordField.mutableProperty.value = validPassword

        viewModel.registerAction.errors.observeValues { error in
            XCTAssertNotNil(error)
        }?.addToDisposeBag(disposeBag)

        viewModel.registerAction.completed.observeValues {
            XCTFail("Unexpected completion")
        }?.addToDisposeBag(disposeBag)

        viewModel.registerAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Registration.registerV2(getReigstrationRequest())))
    }

    func testSessionTimeOutErrorRegisterAction() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Registration.registerV2(getReigstrationRequest())))).thenReturn(getSessionTimeoutError())
        }

        viewModel.firstNameField.mutableProperty.value = validFirstName
        viewModel.hasNoLegalMiddleName.value = false
        viewModel.middleNameField.mutableProperty.value = validMiddleName
        viewModel.lastNameField.mutableProperty.value = validLastName
        viewModel.emailAddressField.mutableProperty.value = validEmail
        viewModel.mobileNumberField.mutableProperty.value = validMobileNumber
        viewModel.passwordField.mutableProperty.value = validPassword

        viewModel.registerAction.errors.observeValues { error in
            XCTAssertEqual(error.type, .sessionTimeout(backendErrorSpiel))
        }?.addToDisposeBag(disposeBag)

        viewModel.registerAction.completed.observeValues {
            XCTFail("Unexpected completion")
        }?.addToDisposeBag(disposeBag)

        viewModel.registerAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Registration.registerV2(getReigstrationRequest())))
    }
}

fileprivate extension MayaRegistrationFormV2ViewModelTest {
       func getReigstrationRequest() -> RegistrationRequest {
        // Unit test can't see main bundle, so countries.plist is not loaded. +63 will not be present
        let identityRequest = IdentityRequest(type: IdentityType.msisdn.rawValue, value: validMobileNumber)
        let backupIdentityRequest = IdentityRequest(type: IdentityType.email.rawValue, value: validEmail)

        return RegistrationRequest(firstName: validFirstName,
                                   middleName: validMiddleName,
                                   lastName: validLastName,
                                   noMiddleNameFlag: false,
                                   identity: identityRequest,
                                   password: validPassword,
                                   source: Constants.Defaults.Common.source.rawValue,
                                   birthDate: nil,
                                   consent: getConsentGroup(),
                                   backupIdentity: backupIdentityRequest)
    }

    func getConsentGroup() -> ConsentGroup {
        var secondaryConsentDictionary = ConsentGroup()
        viewModel.setConsents()
        secondaryConsents.forEach { consent in
            secondaryConsentDictionary[consent.identifier] = consent.accepted
        }
        return secondaryConsentDictionary
    }
}
