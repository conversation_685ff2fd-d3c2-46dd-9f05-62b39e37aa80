//
//  MayaInterstitialViewModelTest.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON> on 4/13/23.
//  Copyright © 2023 PayMaya Philippines, Inc. All rights reserved.
//

import Cuckoo
import StoreProvider
import XCTest

final class MayaInterstitialViewModelTest: ViewModelTest {
    private var viewModel: MayaInterstitialViewModelProtocol!

    override func setUp() {
        super.setUp()
        stub(propertyListStore) { stub in
            when(stub.read(isEqual(to: PropertyListStoreId.webBaseUrl))).thenReturn(StoreResult.success("https://www.paymaya.com/"))
        }
    }

    override func tearDown() {
        super.tearDown()
        viewModel = nil
    }

    func testInvalidCreatorStoreInterstitial() {
        viewModel = MayaInterstitialViewModel(model: CreatorStore.food)
        XCTAssertNil(viewModel)
    }

    func testStocksInterstitial() {
        viewModel = MayaInterstitialViewModel(model: CreatorStore.stocks)
        XCTAssertEqual(viewModel.navigationTitle, "Stocks")
        XCTAssertEqual(viewModel.iconImage.name, Asset.MayaImages.CreatorStore.iconStocksCreatorStoreInterstitial.name)
        XCTAssertEqual(viewModel.title, "Step into the exciting world of stock trading")
        XCTAssertEqual(viewModel.buttonText, "Get started")

        testInterstitialMessages(messages: [
            "Trade stocks with SEC licensed brokers",
            "Easily manage your stock portfolio",
            "Use your Maya Wallet to fund your trades with each partner"
        ])
    }

    func testGlobalStocksInterstitial() {
        viewModel = MayaInterstitialViewModel(model: CreatorStore.globalStocks)
        XCTAssertEqual(viewModel.navigationTitle, "Global Stocks")
        XCTAssertEqual(viewModel.iconImage.name, Asset.MayaImages.CreatorStore.iconGlobalStocksCreatorStoreInterstitial.name)
        XCTAssertEqual(viewModel.title, "Unlock the world of global stock trading")
        XCTAssertEqual(viewModel.buttonText, "Get started")

        testInterstitialMessages(messages: [
            "Invest in the world’s largest companies like Google, Facebook, Tesla",
            "Trade global stocks seamlessly with Maya's premier trading providers",
            "Use your Maya Wallet to fund your USD wallet with each partner"
        ])
    }

    func testGlobalRemittanceInterstitial() {
        viewModel = MayaInterstitialViewModel(model: CreatorStore.globalRemittance)
        XCTAssertEqual(viewModel.navigationTitle, "Global remittance")
        XCTAssertEqual(viewModel.iconImage.name, Asset.MayaImages.CreatorStore.iconGlobalRemittanceCreatorStoreInterstitial.name)
        XCTAssertEqual(viewModel.title, "Global remittance at your fingertips")
        XCTAssertEqual(viewModel.buttonText, "Get started")

        testInterstitialMessages(messages: [
            "Conveniently claim your money from around the world using Maya",
            "Select from a range of partners like Western Union and more",
            "Receive the money in your Maya Wallet"
        ])
    }

    func testInsuranceInterstitial() {
        viewModel = MayaInterstitialViewModel(model: CreatorStore.insurance)
        XCTAssertEqual(viewModel.navigationTitle, "Insurance")
        XCTAssertEqual(viewModel.iconImage.name, Asset.MayaImages.CreatorStore.iconInsuranceCreatorStoreInterstitial.name)
        XCTAssertEqual(viewModel.title, "Stay covered in times of need")
        XCTAssertEqual(viewModel.buttonText, "Get started")

        testInterstitialMessages(messages: [
            "Choose among leading insurance providers in the Philippines",
            "Explore insurance products from travel to health – and more",
            "Use your Maya Wallet to pay insurance premiums from each partner"
        ])
    }

    func testStocksGameInterstitial() {
        viewModel = MayaInterstitialViewModel(model: CreatorStore.stocksGame)
        XCTAssertEqual(viewModel.navigationTitle, "Stocks game")
        XCTAssertEqual(viewModel.iconImage.name, Asset.MayaImages.CreatorStore.iconStocksGameCreatorStoreInterstitial.name)
        XCTAssertEqual(viewModel.title, "Learn stock trading securely with Ztock")
        XCTAssertEqual(viewModel.buttonText, "Get started")

        testInterstitialMessages(messages: [
            "Discover what it’s like to trade stocks, ETFs, and more",
            "Learn how to trade stocks without using any real money",
            "Simulate trading with world’s best companies from Apple to Zoom"
        ])
    }

    func testLuckyGamesInterstitial() {
        viewModel = MayaInterstitialViewModel(model: CreatorStore.luckyGames)
        XCTAssertEqual(viewModel.navigationTitle, "Games")
        XCTAssertEqual(viewModel.iconImage.name, Asset.MayaImages.CreatorStore.iconLuckyGamesCreatorStoreInterstitial.name)
        XCTAssertEqual(viewModel.title, "Lucky games await!")
        XCTAssertEqual(viewModel.buttonText, "Continue")

        testInterstitialMessages(messages: [
            "Choose a PAGCOR-authorized game of your choice to be redirected to a third-party platform",
            "Available to upgraded Maya users who are at least 21 years old and are not government employees",
            "Maya is responsible for cash in and cash out services only. We are not liable for any losses you may experience in your transactions.",
            "By continuing, you fully understand the Terms and Conditions on the use of this service, accept all the risks involved, and acknowledge that profits and returns are not guaranteed."
        ])
    }

    func testStreamInterstitial() {
        viewModel = MayaInterstitialViewModel(model: CreatorStore.stream)
        XCTAssertEqual(viewModel.navigationTitle, "Stream")
        XCTAssertEqual(viewModel.iconImage.name, Asset.MayaImages.CreatorStore.iconStreamCreatorStoreInterstitial.name)
        XCTAssertEqual(viewModel.title, "Your one-stop streaming hub")
        XCTAssertEqual(viewModel.buttonText, "Get started")

        testInterstitialMessages(messages: [
            "Access a curated collection of streaming providers",
            "Stream movies, TV shows, and more from your favorite platforms",
            "Use your Maya Wallet for hassle-free and secure payments"
        ])
    }

    func testSparkHackathonInterstitial() {
        viewModel = MayaInterstitialViewModel(model: CreatorStore.sparkHackathon)
        XCTAssertEqual(viewModel.navigationTitle, "Spark Hackathon")
        XCTAssertEqual(viewModel.iconImage.name, Asset.MayaImages.CreatorStore.iconSparkHackathonCreatorStoreInterstitial.name)
        XCTAssertEqual(viewModel.title, "Powering the Future of Maya")
        XCTAssertEqual(viewModel.buttonText, "Get started")

        testInterstitialMessages(messages: [
            "Discover innovative solutions developed by Maya’s talented teams",
            "Be the first to try new features and help shape the future of Maya products",
            "Network with fellow innovators and learn about the latest trends in tech"
        ])
    }

    func testPhilippineAirlinesInterstitial() {
        viewModel = MayaInterstitialViewModel(model: MMA.philippineAirlines)
        XCTAssertNil(viewModel.navigationTitle)
        XCTAssertEqual(viewModel.iconImage.name, Asset.MayaImages.Mma.iconPhilippineAirlinesMMAInterstitial.name)
        XCTAssertEqual(viewModel.title, "Fly with PAL, right here in Maya")
        XCTAssertEqual(viewModel.buttonText, "Continue")

        testInterstitialMessages(messages: [
            "Seamlessly link your Mabuhay Miles account in Maya",
            "Instantly check your current Mabuhay Miles balance",
            "Stay tuned for more features coming soon"
        ])
    }

    private func testInterstitialMessages(messages testMessages: [String]) {
        let messages = viewModel.messages.compactMap { $0.text.message ?? $0.text.attributedMessage?.string }
        for (message1, message2) in zip(messages, testMessages) {
            XCTAssertEqual(message1, message2)
        }
    }
}
