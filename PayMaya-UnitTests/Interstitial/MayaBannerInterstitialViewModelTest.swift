//
//  MayaBannerInterstitialViewModelTest.swift
//  PayMaya
//
//  Created by <PERSON> on 6/5/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Cuckoo
import Injector
import ReactiveSwift
import StoreProvider
import XCTest

final class MayaBannerInterstitialViewModelTest: ViewModelTest {
    private var viewModel: MayaBannerInterstitialViewModelProtocol!

    override func setUp() {
        super.setUp()

        let adBanner = AdBanner(imageURLString: "", text: "Banner", message: "Banner Message", actionURLString: nil, tag: 1, screen: "CREATOR_STORE_INTERSTITIAL_APPEAR")
        let adBannerProperty = MutableProperty([adBanner])
        let adBannerService = ContainerWrapper.shared.resolve(AdBannerServiceProtocol.self) as! MockAdBannerServiceProtocol

        stub(adBannerService) { stub in
            when(stub.adBannersProperty.get).thenReturn(adBannerProperty)
            when(stub.rotationIntervalProperty.get).thenReturn(MutableProperty(Constants.CleverTapRotationIntervalDefault))
        }

        stub(propertyListStore) { stub in
            when(stub.read(isEqual(to: PropertyListStoreId.webBaseUrl))).thenReturn(StoreResult.success("https://www.paymaya.com/"))
        }
    }

    override func tearDown() {
        super.tearDown()
        viewModel = nil
    }

    func testInvalidCreatorStoreInterstitial() {
        viewModel = MayaBannerInterstitialViewModel(model: CreatorStore.food)
        XCTAssertNil(viewModel)
    }

    func testLuckyGamesInterstitial() {
        viewModel = MayaBannerInterstitialViewModel(model: CreatorStore.luckyGames)
        XCTAssertEqual(viewModel.navigationTitle, "Games")
        XCTAssertEqual(viewModel.iconImage.name, Asset.MayaImages.CreatorStore.iconLuckyGamesCreatorStoreInterstitial.name)
        XCTAssertEqual(viewModel.title, "Before you start")
        XCTAssertEqual(viewModel.buttonText, "Continue")

        testInterstitialMessages(messages: [
            "Available to upgraded Maya users who are at least 21 years old and are not government employees",
            "All games are PAGCOR-authorized and will redirect you to a third-party platform",
            "Maya is responsible for cash in and cash out services only. We are not liable for any losses you may experience in your transactions."
        ])
    }

    func testAdCarouselViewModel() {
        viewModel = MayaBannerInterstitialViewModel(model: CreatorStore.luckyGames)
        let adCarouselViewModel = viewModel.adCarouselViewModel

        XCTAssertTrue(adCarouselViewModel.isAutoscrollEnabled)
        XCTAssertEqual(adCarouselViewModel.adWidth, AdBannerProperties.dynamicWidth)
        XCTAssertEqual(adCarouselViewModel.adHeight, AdBannerProperties.dynamicHeight(desiredWidth: 327, desiredHeight: 245))
        XCTAssertEqual(adCarouselViewModel.module, .creatorStoreInterstitial)
    }

    private func testInterstitialMessages(messages testMessages: [String]) {
        let messages = viewModel.messages.compactMap { $0.text.message ?? $0.text.attributedMessage?.string }
        for (message1, message2) in zip(messages, testMessages) {
            XCTAssertEqual(message1, message2)
        }
    }
}
