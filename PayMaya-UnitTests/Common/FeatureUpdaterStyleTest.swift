//
//  FeatureUpdaterStyleTest.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 7/6/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//
import XCTest

class FeatureUpdaterStyleTest: XCTestCase {
    var style: FeatureUpdaterStyle!

    func testDefaultValues() {
        style = FeatureUpdaterStyle()
        XCTAssertNil(style.leftBarButtonTintColor)
        XCTAssertNil(style.navigationBarColor)
        XCTAssertNil(style.backgroundColor)
        XCTAssertNil(style.subtitleStyle)
        XCTAssertNil(style.titleStyle)
    }

    func testCorrectAssignments() {
        let expectedBarTintColor = UIColor.red
        let expectedBarColor = UIColor.orange
        let expectedBGColor = UIColor.yellow
        let expectedTitleStyle = LabelStyle(textColor: .green)
        let expectedSubtitleStyle = LabelStyle(textColor: .blue)

        style = FeatureUpdaterStyle(leftBarButtonTintColor: expectedBarTintColor,
                                    navigationBarColor: expectedBarColor,
                                    backgroundColor: expectedBGColor,
                                    titleStyle: expectedTitleStyle,
                                    subtitleStyle: expectedSubtitleStyle)

        XCTAssertEqual(style.leftBarButtonTintColor, expectedBarTintColor)
        XCTAssertEqual(style.navigationBarColor, expectedBarColor)
        XCTAssertEqual(style.backgroundColor, expectedBGColor)
        XCTAssertEqual(style.titleStyle, expectedTitleStyle)
        XCTAssertEqual(style.subtitleStyle, expectedSubtitleStyle)
    }
}
