//
//  MayaFeatureUpdateViewModelTest.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 7/6/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//
import XCTest

class MayaFeatureUpdateViewModelTest: ViewModelTest {
    var viewModel: MayaFeatureUpdaterViewModelProtocol!

    override func setUp() {
        super.setUp()

        viewModel = MayaFeatureUpdaterViewModel()
    }

    func testConfiguration() {
        let expectedConfig = FeatureUpdaterContentConfiguration()
        XCTAssertEqual(viewModel.configuration, expectedConfig)
    }

    func testStyle() {
        let expectedStyle = FeatureUpdaterStyle()
        XCTAssertEqual(viewModel.style, expectedStyle)
    }

    func testAppStoreLink() {
        let expectedUrl = Constants.AppStoreLinks.store.url
        XCTAssertNotNil(viewModel.appStoreLink)
        XCTAssertEqual(viewModel.appStoreLink, expectedUrl)
    }
}
