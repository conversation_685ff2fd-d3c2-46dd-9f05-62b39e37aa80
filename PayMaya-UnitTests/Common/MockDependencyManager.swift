//
//  MockDependencyManager.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON><PERSON> on 1/19/21.
//  Copyright © 2021 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
@testable import ConfigurationService
import ConsentOptOutService
import CoreData
import CoreDataStack
import Cuckoo
import Error
import Injector
import MayaCoreData
import MayaFormatterManager
import ReactiveSwift
import StoreProvider
import XCTest

struct MockDependencyManager {
    let userDefaultsStore: MockUserDefaultsStore
    let encryptedDefaultsStore: MockEncryptedDefaultsStore
    let propertyListStore: MockPropertyListStore
    let databaseStore: MockDatabaseStore
    let fileStore: MockFileStore
    let storeProvider: MockStoreProvider

    let apiProvider: MockStubAPIProvider
    let reactiveAPIProvider: MockReactiveMoyaAPIProviderExtension

    var configurationService: MockConfigurationService!
    var configurationServiceV2: MockConfigurationServiceV2!
    var notificationService: MockNotificationService!
    var appThemeService: MockAppThemeService!
    var formatter: MockMayaFormatterManager!
    var deeplinkRouter: MockPaymayaDeepLinkRouter!
    var ravenWrapper: MockRavenWrapper!
    var ravenActionProvider: MockRavenActionProvider!

    init(_ test: ViewModelTest) {
        ContainerWrapper.initialize()
        ContainerWrapper.shared.register(type: ErrorLogger.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return ErrorLogger(with: { _ in })
        })

        userDefaultsStore = createMock(MockUserDefaultsStore.self) { _, stub in
            let migrator = createMock(MockUserDefaultsStoreMigrator.self) { _, stub  in
                when(stub.performMigration(storage: any(), previousBuildNumber: anyInt())).thenDoNothing()
                return MockUserDefaultsStoreMigrator()
            }
            return MockUserDefaultsStore(configuration: StoreConfiguration(previousBuildNumber: 999, migrator: migrator))
        }

        encryptedDefaultsStore = createMock(MockEncryptedDefaultsStore.self) { _, stub in
            let migrator = createMock(MockEncryptedDefaultsStoreMigrator.self) { _, stub  in
                when(stub.performMigration(storage: any(), previousBuildNumber: anyInt())).thenDoNothing()
                return MockEncryptedDefaultsStoreMigrator()
            }
            return MockEncryptedDefaultsStore(configuration: StoreConfiguration( previousBuildNumber: 999, migrator: migrator), storeName: "Paymaya-Defaults")
        }

        propertyListStore = createMock(MockPropertyListStore.self) { _, stub in
            let migrator = createMock(MockPropertyListStoreMigrator.self) { _, stub  in
                when(stub.performMigration(storage: any(), previousBuildNumber: anyInt())).thenDoNothing()
                return MockPropertyListStoreMigrator()
            }
            return MockPropertyListStore(configuration: StoreConfiguration(previousBuildNumber: 999, migrator: migrator))
        }

        databaseStore = createMock(MockDatabaseStore.self) { _, stub in
            let migrator = createMock(MockDatabaseStoreMigrator.self) { _, stub  in
                when(stub.performMigration(storage: any(), previousBuildNumber: anyInt())).thenDoNothing()
                return MockDatabaseStoreMigrator()
            }
            let dataStack = CoreDataStack(storeType: NSInMemoryStoreType, modelName: "PayMaya", transientStore: "PayMaya-Transient-v2", persistentStore: "PayMaya-Persistent-v2", transientStoreConfigName: "TransientStore", persistentStoreConfigName: "PersistentStore", bundle: .mayaCoreDataBundle)
            return MockDatabaseStore(configuration: StoreConfiguration(previousBuildNumber: 999, migrator: migrator), dataStack: dataStack)
        }

        fileStore = createMock(MockFileStore.self) { _, stub in
            let migrator = createMock(MockFileStoreMigrator.self) { _, stub  in
                when(stub.performMigration(storage: any(), previousBuildNumber: anyInt())).thenDoNothing()
                return MockFileStoreMigrator()
            }
            return MockFileStore(configuration: StoreConfiguration(previousBuildNumber: 999, migrator: migrator))
        }

        storeProvider = MockStoreProvider([userDefaultsStore, encryptedDefaultsStore, propertyListStore, databaseStore, fileStore])

        apiProvider = MockStubAPIProvider([])
        reactiveAPIProvider = MockReactiveMoyaAPIProviderExtension(apiProvider)

        registerDependencies()

        notificationService = ContainerWrapper.shared.resolve(NotificationService.self) as? MockNotificationService
        configurationService = ContainerWrapper.shared.resolve(ConfigurationService.self) as? MockConfigurationService
        appThemeService = ContainerWrapper.shared.resolve(AppThemeService.self) as? MockAppThemeService
        formatter = ContainerWrapper.shared.resolve(MayaFormatterManager.self) as? MockMayaFormatterManager
        deeplinkRouter = ContainerWrapper.shared.resolve(PaymayaDeepLinkRouter.self) as? MockPaymayaDeepLinkRouter
        ravenWrapper = ContainerWrapper.shared.resolve(RavenWrapper.self) as? MockRavenWrapper
        ravenActionProvider = ContainerWrapper.shared.resolve(RavenActionProvider.self) as? MockRavenActionProvider

        stubDependencies(test)

        configurationServiceV2 = ContainerWrapper.shared.resolve(ConfigurationServiceV2.self) as? MockConfigurationServiceV2
    }

    func dispose() {
        ContainerWrapper.shared.dispose()
    }

    private func registerDependencies() {
        ContainerWrapper.shared.register(type: StoreProvider.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return self.storeProvider
        })

        ContainerWrapper.shared.register(type: APIProvider.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return self.apiProvider
        })

        ContainerWrapper.shared.register(type: ConfigurationService.self, scope: ContainerWrapper.getContainerScope(), { _ in
            let identifier = ConfigurationServiceStorableIdentifier(appConfig: EncryptedDefaultsStoreId.appConfig.rawValue,
                                                                    remoteConfig: EncryptedDefaultsStoreId.remoteConfig.rawValue,
                                                                    appConfigurationSettingsLastUpdatedOn:
                                                                        UserDefaultsStoreId.appConfigurationSettingsLastUpdatedOn.rawValue)
            return MockConfigurationService(identifier: identifier,
                                            metadataDateFormatter: DateFormatters.getDateFormatter(for: .lastModifiedHeader),
                                            completion: nil)
        })

        ContainerWrapper.shared.register(type: ConfigurationServiceV2.self, scope: ContainerWrapper.getContainerScope()) { _ in
            let configuration = ConfigurationServiceV2.Configuration(sdkKey: "")
            return MockConfigurationServiceV2(configuration: configuration)
        }

        ContainerWrapper.shared.register(type: NotificationService.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockNotificationService()
        })

        ContainerWrapper.shared.register(type: AppThemeService.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockAppThemeService()
        })

        ContainerWrapper.shared.register(type: TouchIDManager.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockTouchIDManager()
        })

        ContainerWrapper.shared.register(type: ConsentOptOutService.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockConsentOptOutService()
        })

        ContainerWrapper.shared.register(type: ShieldDataCollectorProtocol.self) { _ in
            return MockShieldDataCollectorProtocol()
        }

        ContainerWrapper.shared.register(type: CredentialsManager.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return CredentialsManager() // No need to mock
        })

        ContainerWrapper.shared.register(type: AuthenticationProviderManager.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockAuthenticationProviderManager()
        })

        ContainerWrapper.shared.register(type: SignoutService.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockSignoutService()
        })

        ContainerWrapper.shared.register(type: ContactsImporter.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockContactsImporter()
        })

        ContainerWrapper.shared.register(type: ResumeSessionService.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockResumeSessionService()
        })

        ContainerWrapper.shared.register(type: BackgroundSessionManager.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockBackgroundSessionManager()
        })

        ContainerWrapper.shared.register(type: FlutterSessionManager.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockFlutterSessionManager()
        })

        ContainerWrapper.shared.register(type: KountDataCollectorProtocol.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockKountDataCollectorProtocol()
        })

        ContainerWrapper.shared.register(type: AdBannerServiceProtocol.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockAdBannerServiceProtocol()
        })

        ContainerWrapper.shared.register(type: QRGeneratorUtilityProtocol.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockQRGeneratorUtilityProtocol()
        })

        ContainerWrapper.shared.register(type: QRValidationUtilityProtocol.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockQRValidationUtilityProtocol()
        })

        ContainerWrapper.shared.register(
            type: LocationAuthorizerServiceProtocol.self,
            scope: ContainerWrapper.getContainerScope()) { _ in
            return MockLocationAuthorizerServiceProtocol()
        }

        ContainerWrapper.shared.register(
            type: LocationCheckerServiceProtocol.self,
            scope: ContainerWrapper.getContainerScope()
        ) { _ in
            return MockLocationCheckerServiceProtocol()
        }

        ContainerWrapper.shared.register(type: DataLoaderProtocol.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockDataLoaderProtocol()
        })

        ContainerWrapper.shared.register(type: InboxManagerProtocol.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockInboxManagerProtocol()
        })

        ContainerWrapper.shared.register(type: ToggleBalanceShownService.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockToggleBalanceShownService()
        })

        ContainerWrapper.shared.register(type: FlutterEngineManager.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockFlutterEngineManager()
        })

        ContainerWrapper.shared.register(type: MayaFormatterManager.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockMayaFormatterManager()
        })

        ContainerWrapper.shared.register(type: DeepLinkHandler.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockDeepLinkHandler()
        })

        ContainerWrapper.shared.register(type: PaymayaDeepLinkRouter.self, scope: ContainerWrapper.getContainerScope(), { _ in
            let handler = ContainerWrapper.shared.resolve(DeepLinkHandler.self)
            return MockPaymayaDeepLinkRouter(rootDeepLinkHandler: handler)
        })

        ContainerWrapper.shared.register(type: PushApprovalProvider.self) { _ in
            return PushApprovalProvider()
        }

        let dependencySetupHelper = createMock(MockDependencySetupHelper.self) { _, stub in
            when(stub.setupFirebaseDebugView()).thenDoNothing()
            when(stub.setupFirebase()).thenDoNothing()
            when(stub.setupAppsFlyer()).thenDoNothing()
            when(stub.setupGlimpse()).thenDoNothing()
            when(stub.setupCleverTap()).thenDoNothing()
            when(stub.setupShield()).thenDoNothing()
            when(stub.setupVoyagerUpdater()).thenDoNothing()
            when(stub.setupCertTrustKitPinning()).thenDoNothing()
            when(stub.setupLifeTimeTracker()).thenDoNothing()
            return MockDependencySetupHelper(window: UIWindow())
        }

        ContainerWrapper.shared.register(type: DependencySetupHelper.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return dependencySetupHelper
        })

        ContainerWrapper.shared.register(type: AnalyticsService.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockAnalyticsService(providers: [AnalyticsProviderProtocol]())
        })

        ContainerWrapper.shared.register(type: ProfileTrackingManager.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockProfileTrackingManager()
        })

        ContainerWrapper.shared.register(type: RavenWrapper.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockRavenWrapper()
        })

        ContainerWrapper.shared.register(type: RavenActionProvider.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockRavenActionProvider()
        })

        ContainerWrapper.shared.register(type: PerformanceMonitoringServiceProtocol.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MockPerformanceMonitoringServiceProtocol()
        })
    }

    private func stubDependencies(_ test: ViewModelTest) {
        stub(databaseStore) { stub in
            when(stub.read(any(DatabaseStore.ReadingOptions.self))).thenReturn(StoreResult.failure(.none))
            when(stub.fetch(any(DatabaseStore.ReadingOptions.self), limit: isNil())).thenReturn(StoreResult.failure(.none))
            when(stub.write(any(DatabaseStore.Storable.self), options: any(DatabaseStore.WritingOptions.self), completion: anyClosure())).thenDoNothing()
        }

        stub(storeProvider) { stub in
            when(stub.target(test.isEqualIdentity(to: UserDefaultsStore.self))).thenReturn(userDefaultsStore)
            when(stub.target(test.isEqualIdentity(to: EncryptedDefaultsStore.self))).thenReturn(encryptedDefaultsStore)
            when(stub.target(test.isEqualIdentity(to: PropertyListStore.self))).thenReturn(propertyListStore)
            when(stub.target(test.isEqualIdentity(to: FileStore.self))).thenReturn(fileStore)
            when(stub.target(test.isEqualIdentity(to: DatabaseStore.self))).thenReturn(databaseStore)
        }

        stub(apiProvider) { stub in
            when(stub.reactiveProvider.get).thenReturn(reactiveAPIProvider)
            when(stub.reactiveProvider.set(any())).thenDoNothing()
        }

        stub(propertyListStore) { stub in
            when(stub.read(any(ConfigPropertyListStoreId.self))).thenReturn(StoreResult.failure(.none))
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(any(APITargetType.self))).thenReturn(test.getBackendError())
            when(stub.request(any(APITargetType.self), ignoresQueue: any(Bool.self))).thenReturn(test.getBackendError())
            when(stub.request(any(APITargetType.self), ignoresQueue: any(Bool.self), useNoInternetConnectionType: any(Bool.self))).thenReturn(test.getBackendError())
            when(stub.request(any(APITargetType.self), lastUpdated: test.anyOptional(), dateFormatter: any(DateFormatter.self), updateRequired: any())).thenReturn(SignalProducer(result: .success(.updateAvailable)))
            when(stub.request(any(APITargetType.self), lastUpdated: test.anyOptional(), dateFormatter: any(DateFormatter.self), updateRequired: test.isEqual(to: true))).thenReturn(SignalProducer(result: .success(.updateRequired)))
        }

        stub(notificationService) { stub in
            let plainAction: Action<Void, Void, Error> = Action { _ in
                return SignalProducer<Void, Error>(result: Result.success(()))
            }
            when(stub.sendPushTokenAction.get).thenReturn(plainAction)
            when(stub.deletePushTokenAction.get).thenReturn(plainAction)
        }

        stub(formatter) { stub in
            when(stub.number.get).thenCallRealImplementation()
            when(stub.date.get).thenCallRealImplementation()
        }

        stub(userDefaultsStore) { stub in
            when(stub.readRaw(any(UserDefaultsStoreId.self))).thenReturn(nil)
        }

        let consentOptOutService = ContainerWrapper.shared.resolve(ConsentOptOutService.self) as! MockConsentOptOutService
        stub(consentOptOutService) { stub in
            let consentAction: Action<Void, Bool, Error> = Action { _ in
                return SignalProducer(value: true)
            }
            when(stub.getUserDataSendingConsentOptOutAction.get).thenReturn(consentAction)
        }
    }
}
