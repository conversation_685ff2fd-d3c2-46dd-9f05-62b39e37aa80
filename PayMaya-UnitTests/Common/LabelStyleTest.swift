//
//  LabelStyleTest.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 7/6/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//
import XCTest

class LabelStyleTest: XCTestCase {
    var labelStyle: LabelStyle!

    func testDefautValues() {
        labelStyle = LabelStyle()
        XCTAssertNil(labelStyle.font)
        XCTAssertNil(labelStyle.textColor)
    }

    func testCorrectAssignments() {
        let expectedFont = UIFont.systemFont(ofSize: 12)
        let expectedColor = UIColor.red

        labelStyle = LabelStyle(font: expectedFont, textColor: expectedColor)

        XCTAssertEqual(labelStyle.font, expectedFont)
        XCTAssertEqual(labelStyle.textColor, expectedColor)
    }
}
