//
//  ViewModelTest.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON><PERSON> on 9/4/19.
//  Copyright © 2019 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import AssetProvider
import Combine
import ConfigurationService
import Cuckoo
import Error
import Moya
import ReactiveSwift
import XCTest

let backendErrorSpiel = "Error Spiel"
let databaseErrorSpiel = "Database Error Spiel"

class ViewModelTest: XCTestCase {
    enum Times {
        case times(Int)
        case never
    }

    let disposeBag = CompositeDisposable()
    var dependencyManager: MockDependencyManager!

    var signalProducerExpectationArray = [XCTestExpectation]()
    var publisherExpectationArray = [XCTestExpectation]()

    var storeProvider: MockStoreProvider {
        return dependencyManager.storeProvider
    }

    var userDefaultsStore: MockUserDefaultsStore {
        return dependencyManager.userDefaultsStore
    }

    var encryptedDefaultsStore: MockEncryptedDefaultsStore {
        return dependencyManager.encryptedDefaultsStore
    }

    var propertyListStore: MockPropertyListStore {
        return dependencyManager.propertyListStore
    }

    var databaseStore: MockDatabaseStore {
        return dependencyManager.databaseStore
    }

    var fileStore: MockFileStore {
        return dependencyManager.fileStore
    }

    var apiProvider: MockStubAPIProvider {
        return dependencyManager.apiProvider
    }

    var reactiveAPIProvider: MockReactiveMoyaAPIProviderExtension {
        return dependencyManager.reactiveAPIProvider
    }

    var configurationService: MockConfigurationService {
        return dependencyManager.configurationService
    }

    var configurationServiceV2: MockConfigurationServiceV2 {
        return dependencyManager.configurationServiceV2
    }

    var notificationService: MockNotificationService {
        return dependencyManager.notificationService
    }

    var appThemeService: MockAppThemeService {
        return dependencyManager.appThemeService
    }

    var formatter: MockMayaFormatterManager {
        return dependencyManager.formatter
    }

    var deeplinkRouter: MockPaymayaDeepLinkRouter {
        return dependencyManager.deeplinkRouter
    }

    var ravenWrapper: MockRavenWrapper {
        return dependencyManager.ravenWrapper
    }

    var ravenActionProvider: MockRavenActionProvider {
        return dependencyManager.ravenActionProvider
    }

    override func setUp() {
        super.setUp()
        dependencyManager = MockDependencyManager(self)
        stubConfigurationService()
        stubConfigurationServiceV2()
    }

    override func tearDown() {
        dependencyManager.dispose()
        disposeBag.dispose()
        signalProducerExpectationArray = []
        super.tearDown()
    }
}

extension ViewModelTest {
    func isEqual<S: Equatable>(to value: S) -> ParameterMatcher<S> {
        return ParameterMatcher<S> { $0 == value }
    }

    func isEqual<S: Codable & Equatable>(to value: S.Type) -> ParameterMatcher<S.Type> {
        return ParameterMatcher<S.Type> { $0 == value }
    }

    func isEqualIdentity<S: Any>(to value: S.Type) -> ParameterMatcher<S.Type> {
        return ParameterMatcher<S.Type> { $0 == value }
    }

    func isEqual<S: APITargetType & Equatable>(to value: S) -> ParameterMatcher<APITargetType> {
        return ParameterMatcher<APITargetType> { return $0 as? S == value }
    }

    func anyOptional<T>() -> ParameterMatcher<T?> {
        return ParameterMatcher<T?> { _ in return true }
    }
}

extension ViewModelTest {
    func getBackendError<T>(code: Int = 0) -> SignalProducer<T, Error> {
        let errorResponse = ErrorResponse(code: code, msg: "", spiel: backendErrorSpiel, action: ErrorAction(title: "Action", url: "paymaya://action"))
        let message = Message(error: errorResponse)
        return SignalProducer<T, Error>(error: Error.init(backend: .failed(message)))
    }

    func getDatabaseError<T>() -> SignalProducer<T, Error> {
        return .init(error: .database(.writeError(reason: databaseErrorSpiel)))
    }

    func getPayMayaError(_ title: String = "Error Title", message: String? = nil) -> PayMayaError {
        var viewModel = ErrorAlertViewModel(title: title, message: message)
        viewModel.primaryActionURL = "paymaya://action"
        viewModel.primaryActionTitle = "Action"
        viewModel.message = backendErrorSpiel
        return PayMayaError(viewModel: viewModel)
    }

    func getMayaError(_ title: String = CommonStrings.Maya.Error.Default.title, message: String? = nil, closeButtonTitle: String = CommonStrings.Common.Modal.Got.it) -> PayMayaError {
        var viewModel = ErrorAlertViewModel(title: title, message: message, closeButtonTitle: closeButtonTitle)
        viewModel.primaryActionURL = "paymaya://action"
        viewModel.primaryActionTitle = "Action"
        viewModel.message = backendErrorSpiel
        return PayMayaError(viewModel: viewModel)
    }

    func getSessionTimeoutError<T>() -> SignalProducer<T, Error> {
        let errorResponse = ErrorResponse(code: 0, msg: "", spiel: backendErrorSpiel)
        let message = Message(error: errorResponse)
        return SignalProducer<T, Error>(error: Error.init(backend: .sessionTimeout(message)))
    }

    func getPayMayaSessionTimeoutError(_ title: String = CommonStrings.Error.Default.title, message: String? = nil) -> PayMayaError {
        return PayMayaError(type: .sessionTimeout(backendErrorSpiel), viewModel: ErrorAlertViewModel(title: title, message: message))
    }

    func getMayaSessionTimeoutError(_ title: String = CommonStrings.Maya.Error.Default.title, message: String? = nil) -> PayMayaError {
        return PayMayaError(type: .sessionTimeout(backendErrorSpiel), viewModel: ErrorAlertViewModel(title: title, message: message))
    }

    func getRedirectError<T>() -> SignalProducer<T, Error> {
        let redirectResponse = RedirectResponse(otpId: "123456")
        return SignalProducer<T, Error>(error: Error(backend: .redirectResponse(redirectResponse)))
    }

    func getUnknownError<T>() -> SignalProducer<T, Error> {
        return SignalProducer<T, Error>(error: Error.init())
    }

    func getPayMayaUnknownError(_ title: String = "Error Title", message: String? = nil) -> PayMayaError {
        return PayMayaError(viewModel: ErrorAlertViewModel(title: title, message: message))
    }

    func getMayaUnknownError(_ title: String = CommonStrings.Maya.Error.Default.title, message: String? = nil) -> PayMayaError {
        return PayMayaError(viewModel: ErrorAlertViewModel(title: title, message: message))
    }

    func getValidationError(_ title: String, message: String) -> PayMayaError {
        return PayMayaError(type: .validation, viewModel: ErrorAlertViewModel(title: title, message: message))
    }

    func getSuccessProducer(_ statusCode: Int = 200) -> SignalProducer<Response, Error> {
        return SignalProducer<Response, Error>(value: Response(statusCode: statusCode, data: Data()))
    }

    func getSuccessProducer<S: Codable>(_ data: S) -> SignalProducer<S, Error> {
        return SignalProducer<S, Error>(value: data)
    }

    func getMetadataProducer(_ state: APIMetadataState) -> SignalProducer<APIMetadataState, Error> {
        // This mimics the behavior of the signal producer at request(for:target:lastUpdated:dateFormatter:updateRequired:) at MoyaAPIProvider+Reactive.swift
        switch state {
        case .updateAvailable:
            return SignalProducer(value: .updateAvailable)
        case .updateRequired:
            return SignalProducer(value: .updateRequired)
        case .updateNone:
            return SignalProducer<APIMetadataState, Error> { observer, _ in
                observer.send(value: .updateNone)
                observer.send(error: .metadata)
            }
        }
    }

    func trueConfig() -> CSConfig<Bool> {
        return CSConfig<Bool>(defaultValue: true)
    }

    func falseConfig() -> CSConfig<Bool> {
        return CSConfig<Bool>(defaultValue: false)
    }

    func trueConfigV2() -> CSConfigV2<Bool> {
        return CSConfigV2<Bool>(defaultConfig: { true }, analyticsCompletion: { _, _, _, _ in })
    }

    func falseConfigV2() -> CSConfigV2<Bool> {
        return CSConfigV2<Bool>(defaultConfig: { false }, analyticsCompletion: { _, _, _, _ in })
    }

    func getTouchIDResult(success: Bool) -> SignalProducer<TouchIdResult, Error> {
        return SignalProducer<TouchIdResult, Error>(value: .init(success: success, error: nil))
    }

    // MARK: - Signal Producer Expectation
    ///
    func expect<T>(_ signalProducer: SignalProducer<T, Error>, fired times: ViewModelTest.Times, line: Int = #line) -> SignalProducer<T, Error> {
        let expectation = expectation(description: "SignalProducer at line \(line) is")

        let description: String
        switch times {
        case .times(let count):
            expectation.expectedFulfillmentCount = count
            description = " expected to fire \(count) time(s)."
        case .never:
            expectation.isInverted = true
            description = " not expected to fire."
        }

        expectation.expectationDescription.append(description)

        signalProducerExpectationArray.append(expectation)

        return signalProducer.on(started: {
            expectation.fulfill()
        })
    }

    func verifyExpectationsForSignalProducers() {
        // Timeout needed due to how inverted expectations work.
        // Since signal producers in our unit tests return immediately, use a very short time in order to not affect overall testing duration.
        wait(for: signalProducerExpectationArray, timeout: 0.000000001) // 1ns
    }
}

// MARK: - Publishers
extension ViewModelTest {
    func getSuccessPublisher(statusCode: Int = 200) -> AnyPublisher<Response, Error> {
        return Just(Response(statusCode: statusCode, data: Data()))
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }

    func getSuccessPublisher<Output: Codable>(data: Output) -> AnyPublisher<Output, Error> {
        return Just(data)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }

    func getBackendErrorPublisher<Output>(code: Int = 0) -> AnyPublisher<Output, Error> {
        let errorResponse = ErrorResponse(
            code: code,
            spiel: backendErrorSpiel,
            action: ErrorAction(title: "Action", url: "paymaya://action"))
        let message = Message(error: errorResponse)
        return Fail(error: .backend(.failed(message))).eraseToAnyPublisher()
    }

    func getDatabaseErrorPublisher<Output>() -> AnyPublisher<Output, Error> {
        return Fail(error: .database(.writeError(reason: databaseErrorSpiel))).eraseToAnyPublisher()
    }

    func getSessionTimeoutErrorPublisher<Output>() -> AnyPublisher<Output, Error> {
        let errorResponse = ErrorResponse(code: 0, spiel: backendErrorSpiel)
        let message = Message(error: errorResponse)
        return Fail(error: .backend(.sessionTimeout(message))).eraseToAnyPublisher()
    }

    func getRedirectErrorPublisher<Output>() -> AnyPublisher<Output, Error> {
        let redirectResponse = RedirectResponse(otpId: "123456")
        return Fail(error: .backend(.redirectResponse(redirectResponse))).eraseToAnyPublisher()
    }

    func getUnknownErrorPublisher<Output>() -> AnyPublisher<Output, Error> {
        return Fail(error: Error()).eraseToAnyPublisher()
    }

    func getMetadataPublisher(_ state: APIMetadataState) -> AnyPublisher<APIMetadataState, Error> {
        switch state {
        case .updateAvailable:
            return Just(.updateAvailable)
                .setFailureType(to: Error.self)
                .eraseToAnyPublisher()
        case .updateRequired:
            return Just(.updateRequired)
                .setFailureType(to: Error.self)
                .eraseToAnyPublisher()
        case .updateNone:
            let subject = PassthroughSubject<APIMetadataState, Error>()
            subject.send(.updateNone)
            subject.send(completion: .failure(.metadata))
            return subject.eraseToAnyPublisher()
        }
    }

    func expectPublisher<Output>(
        _ publisher: AnyPublisher<Output, Error>,
        fired times: Times,
        line: Int = #line
    ) -> AnyPublisher<Output, Error> {
        let expectation = expectation(description: "Publisher at line \(line) is")

        let description: String
        switch times {
        case .times(let count):
            expectation.expectedFulfillmentCount = count
            description = " expected to fire \(count) time(s)."
        case .never:
            expectation.isInverted = true
            description = " not expected to fire."
        }

        expectation.expectationDescription.append(description)

        publisherExpectationArray.append(expectation)

        return publisher.handleEvents(receiveSubscription: { _ in
            expectation.fulfill()
        }).eraseToAnyPublisher()
    }

    func verifyExpectationsForPublishers() {
        // Timeout needed due to how inverted expectations work.
        // Since publishers in our unit tests return immediately,
        // use a very short time in order to not affect overall testing duration.
        wait(for: publisherExpectationArray, timeout: 0.000000001) // 1ns
    }
}
