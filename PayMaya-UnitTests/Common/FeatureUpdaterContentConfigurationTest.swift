//
//  FeatureUpdaterContentConfigurationTest.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 7/6/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//
import AssetProvider
import XCTest

class FeatureUpdaterContentConfigurationTest: XCTestCase {
    var configuration: FeatureUpdaterContentConfiguration!

    func testDefaultValues() {
        configuration = FeatureUpdaterContentConfiguration()
        XCTAssertEqual(configuration.subtitle, "Update your app to enjoy the newest features and fixes.")
        XCTAssertEqual(configuration.centerImage, CommonAsset.Images.Alert.imageMaintenance.image)
        XCTAssertEqual(configuration.leftBarButtonImage, CommonAsset.Images.Icons.iconBack.image)
        XCTAssertEqual(configuration.buttonTitle, "Update now")
        XCTAssertEqual(configuration.title, "Update required")
        XCTAssertEqual(configuration.navigationBarTitle, "")
    }

    func testCorrectAssignments() {
        let expectedSubtitle = "a"
        let expectedCenterImage = CommonAsset.Images.Icons.iconBack.image
        let expectedLeftBarButtonImage = CommonAsset.Images.Icons.iconInfo.image
        let expectedButtonTitle = "b"
        let expectedTitle = "c"
        let expectedNavBarTitle = "d"

        configuration = FeatureUpdaterContentConfiguration(leftBarButtonImage: expectedLeftBarButtonImage,
                                                           navigationBarTitle: expectedNavBarTitle,
                                                           centerImage: expectedCenterImage,
                                                           buttonTitle: expectedButtonTitle,
                                                           subtitle: expectedSubtitle,
                                                           title: expectedTitle)

        XCTAssertEqual(configuration.subtitle, expectedSubtitle)
        XCTAssertEqual(configuration.centerImage, expectedCenterImage)
        XCTAssertEqual(configuration.leftBarButtonImage, expectedLeftBarButtonImage)
        XCTAssertEqual(configuration.buttonTitle, expectedButtonTitle)
        XCTAssertEqual(configuration.title, expectedTitle)
        XCTAssertEqual(configuration.navigationBarTitle, expectedNavBarTitle)
    }
}
