//
//  QRScannerViewModelTest.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 10/6/18.
//  Copyright © 2018 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import ConfigurationService
import Cuckoo
import Error
import Injector
import ReactiveSwift
import StoreProvider
import XCTest

class QRScannerViewModelTest: ViewModelTest {
    private let arrayJsonString = "[]"
    private let invalidJsonString = "{"
    private let invalidQRString = "{}"
    private let validP2MQRString = """
    {
        "m":{
            "v":"24176fc8-dac5-4902-bca0-e016690acf43",
            "n":"Mars, Incorporated and its Affiliates",
            "t":"PAYMAYA"
        },
        "a":{
            "v":"10000",
            "c":"PHP"
        }
    }
    """
    static let validStaticLegacyQRString = """
    {
        "m":{
            "v":"24176fc8-dac5-4902-bca0-e016690acf43",
            "n":"Mars, Incorporated and its Affiliates",
            "t":"PAYMAYA"
        }
    }
    """
    private let validSendMoneyQRString = """
    {
        "r": {
            "v": "+639983311428",
            "t": "PAYMAYA"
        }
    }
    """
    static let validDynamicQRString = """
    {
        "m":{
            "n":"Mars, Incorporated and its Affiliates"
        },
        "t":{
            "p":"ABCDEF",
            "a":"10000"
        }
    }
    """
    private let merchantAqcuirerId = "PAPHPHM1XXX"
    private let validInstapayQRString = "00020101021127740012com.p2pqrpay0111PAPHPHM1XXX0208********041092283499450513+6392283499455204601653036085802PH5911Karen Aclan6001A63046173"
    static let validQrphStaticQRString = "00020101021128430011ph.ppmi.p2m0111PAPHPHM1XXX03091004031315204601653036085802PH5910MYFOODHALL6011MANDALUYONG62290525360aca02a1b742cfb1fd21fef6304684A"
    static let validQrphDynamicQRString = "00020101021228430011ph.ppmi.p2m0111PAPHPHM1XXX03091004031315204601653036085406100.005802PH5910MYFOODHALL6011MANDALUYONG62290525360aca02a1b742cfb1fd21fef6304684A"
    static let validOffUsQRPHMerchantString = "00020101021128720011ph.ppmi.p2m0111MBTCPHMMXXX0312************0415***************05030005204601653036085802PH5920CAFE MARY GRACE INC.6011Mandaluyong62440011ph.ppmi.p2m0506211000*******************88490012ph.ppmi.qrph0129006392094574580111MBTCPHMMXXX63045945"
    static let validOffUsQRPHDynamicMerchantString = "00020101021128720011ph.ppmi.p2m0111MBTCPHMMXXX0312************0415***************05030005204601653036085802PH5920CAFE MARY GRACE INC.6011Mandaluyong62440011ph.ppmi.p2m0506211000*******************88490012ph.ppmi.qrph0129006392094574580111MBTCPHMMXXX63045945"
    private let validQRPHP2PStaticString = "00020101021127900012com.p2pqrpay0111AUBKPHMMXXX0208********0308id12345604126391139490090515+63-911-39490095204601653036085802PH5913Test Receiver6011Mandaluyong6104155062890011ph.ppmi.p2m0107bill1230203***0310storelabel0403***0506ref1230603***0707term1230903AME64340002PH0114Ibang Pangalan0206Syudad630470A1"
    private let validQRPHP2PDynamicString = "00020101021227900012com.p2pqrpay0111AUBKPHMMXXX0208********0308id12345604126391139490090515+63-911-394900952046016530360854071253.255802PH5913Test Receiver6011Mandaluyong6104155062890011ph.ppmi.p2m0107bill1230203***0310storelabel0403***0506ref1230603***0707term1230903AME64340002PH0114Ibang Pangalan0206Syudad63047FC9"

    private let validP2PSendMoneyString = "00020101021127780012com.p2pqrpay0111PAPHPHM1XXX0208********0412************0515+63-927-15289455204601653036085802PH5912Allan Barola6009Parañaque63047926"

    private let validP2PBankTransferString = "00020101021127580012com.p2pqrpay0111SRCPPHM2XXX0208********0411***********5204601653036085802PH5905Miaaa6006Laguna6104402862640008********0203***0403*****************************************630487C8"

    private let validP2MString = "00020101021128720011ph.ppmi.p2m0111MBTCPHMMXXX0312************0415***************05030005204601653036085802PH5920CAFE MARY GRACE INC.6011Mandaluyong6104100162650011ph.ppmi.p2m0203***0403***************************************64250002PH0115CAFE MARY GRACE88490012ph.ppmi.qrph0129006392094574580111MBTCPHMMXXX63040D2A"

    private var validP2PSendMoneyQRDetails = QRDetails(
                                                creditAccountNumber: "************",
                                                globalUniqueIdentifier: "com.p2pqrpay",
                                                acquirerId: "PAPHPHM1XXX",
                                                paymentType: "********",
                                                merchantCountryCode: "PH",
                                                merchantCity: "Parañaque",
                                                merchantMobileNumber: "+63-927-1528945",
                                                merchantName: "Allan Barola",
                                                payloadFormatIndicator: "01",
                                                pointOfInitiation: "11",
                                                transactionCurrencyCode: "608",
                                                merchantCategoryCode: "6016",
                                                merchantCrc: "7926"
                                            )

    private var validP2PBankTransferQRDetails = QRDetails(
                                                    creditAccountNumber: "***********",
                                                    globalUniqueIdentifier: "********",
                                                    acquirerId: "SRCPPHM2XXX",
                                                    paymentType: "********",
                                                    merchantCountryCode: "PH",
                                                    merchantCity: "Laguna",
                                                    merchantName: "Miaaa",
                                                    merchantPostalCode: "4028",
                                                    payloadFormatIndicator: "01",
                                                    pointOfInitiation: "11",
                                                    transactionCurrencyCode: "608",
                                                    merchantCategoryCode: "6016",
                                                    merchantCrc: "87C8",
                                                    additionalData: QRAdditionalData(
                                                        globalUniqueIdentifier: "********",
                                                        loyaltyNumber: "***",
                                                        referenceId: "********",
                                                        terminalId: "12345678"
                                                    )
                                                )

    private var validP2MQRDetails = QRDetails(
        acquirerRequiredInfo: "006392094574580111MBTCPHMMXXX",
        mobileNumber: "***",
        proxyNotifFlag: "000",
        globalUniqueIdentifier: "ph.ppmi.p2m",
        acquirerId: "PAPHPHM1XXX",
        merchantCountryCode: "PH",
        merchantCity: "Mandaluyong",
        merchantId: "************",
        merchantName: "CAFE MARY GRACE INC.",
        merchantPostalCode: "1001",
        payloadFormatIndicator: "01",
        pointOfInitiation: "11",
        transactionCurrencyCode: "608",
        merchantCategoryCode: "6016",
        merchantCrc: "0D2A",
        merchantCreditAccountNumber: "***************",
        additionalData: QRAdditionalData(
            globalUniqueIdentifier: "ph.ppmi.p2m",
            additionalMobileNumber: "***",
            loyaltyNumber: "***",
            referenceId: "211000",
            customerLabel: "***",
            terminalId: "********",
            purpose: "***"
        )
    )

    var viewModel: QRScannerViewModelProtocol!
    var qrValidationUtility: MockQRValidationUtilityProtocol!

    override func setUp() {
        super.setUp()

        qrValidationUtility = ContainerWrapper.shared.resolve(QRValidationUtilityProtocol.self) as? MockQRValidationUtilityProtocol

        stub(configurationService) { stub in
            when(stub.maintenance.get).thenReturn(nil)
        }

        stub(qrValidationUtility) { stub in
            when(stub.checkQRPHFormatIndicator(qrCode: any())).thenReturn(true)
            when(stub.checkQRPHCrc(qrCode: any())).thenReturn(true)
            when(stub.validateQRPHCrc(qrCode: any(), bic: any())).thenReturn(true)
            when(stub.parseQRPH(qrCode: any())).thenReturn(QRDetails())
            when(stub.validateQRPHP2p(qrDetails: any())).thenDoNothing()
            when(stub.validateQRPHP2m(qrDetails: any())).thenDoNothing()
        }

        viewModel = QRScannerViewModel(scanType: .any)
    }

    func testCreateQRPHDynamicMerchantPaymentActionSuccess() {
        stub(configurationService) { stub in
            when(stub.qrWithCheckoutFlowEnabled.get).thenReturn(trueConfig())
        }

        let successResponse = SignalProducer<CreatedMerchantPaymentV2, Error>(value: QRScannerViewModelTest.createdMerchantPaymentV2())
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MerchantPaymentV2.createQRPH(QRScannerViewModelTest.dynamicMerchantPaymentQRPHRequest())), type: isEqual(to: CreatedMerchantPaymentV2.self), decoder: any()))
                .thenReturn(successResponse)
        }

        viewModel?.createQRPHDynamicMerchantPaymentAction?.values.observeValues({ offUSQRPHCreatedMerchantPayment in
            XCTAssertEqual(offUSQRPHCreatedMerchantPayment.merchant.name, QRScannerViewModelTest.merchantName)
            XCTAssertEqual(offUSQRPHCreatedMerchantPayment.amount.value, 100.0)
            XCTAssertEqual(offUSQRPHCreatedMerchantPayment.paymentDetails.paymentId, QRScannerViewModelTest.tokenId)
            XCTAssertEqual(offUSQRPHCreatedMerchantPayment.qrPayload, QRScannerViewModelTest.validQrphDynamicQRString)
            XCTAssertEqual(offUSQRPHCreatedMerchantPayment.type, .onUsDynamicQrPh)
            XCTAssertEqual(offUSQRPHCreatedMerchantPayment.amount.currency, QRScannerViewModelTest.currency)
        })?.addToDisposeBag(disposeBag)

        viewModel?.createQRPHDynamicMerchantPaymentAction?.apply(QRScannerViewModelTest.createQRPHMerchant(isOnUsQr: true)).start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MerchantPaymentV2.createQRPH(QRScannerViewModelTest.dynamicMerchantPaymentQRPHRequest())), type: isEqual(to: CreatedMerchantPaymentV2.self), decoder: any())
    }

    func testCreateQRPHDynamicMerchantPaymentActionToggleDisabled() {
        stub(configurationService) { stub in
            when(stub.qrWithCheckoutFlowEnabled.get).thenReturn(falseConfig())
        }

        viewModel?.createQRPHDynamicMerchantPaymentAction?.values.observeValues({ _ in
            XCTFail("Action was completed. Should return an error \(#function)")
        })?.addToDisposeBag(disposeBag)

        viewModel?.createQRPHDynamicMerchantPaymentAction?.errors.observeValues({ error in
            guard let errorModel = error.viewModel else {
                XCTFail("No error view model found. \(#function)")
                return
            }

            XCTAssertEqual(errorModel.title, L10n.Error.Merchant.Payment.title)
        })?.addToDisposeBag(disposeBag)

        viewModel?.createQRPHDynamicMerchantPaymentAction?.apply(QRScannerViewModelTest.createQRPHMerchant(isOnUsQr: true)).start()

        verify(reactiveAPIProvider, never()).request(isEqual(to: API.MerchantPaymentV2.createQRPH(QRScannerViewModelTest.dynamicMerchantPaymentQRPHRequest())), type: isEqual(to: CreatedMerchantPaymentV2.self))
    }

    func testCreateQRPHDynamicMerchantPaymentActionTimeout() {
        stub(configurationService) { stub in
            when(stub.qrWithCheckoutFlowEnabled.get).thenReturn(trueConfig())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MerchantPaymentV2.createQRPH(QRScannerViewModelTest.dynamicMerchantPaymentQRPHRequest())), type: isEqual(to: CreatedMerchantPaymentV2.self), decoder: any()))
                .thenReturn(getSessionTimeoutError())
        }

        viewModel?.createQRPHDynamicMerchantPaymentAction?.completed.observeValues({ _ in
            XCTFail("Action was completed. Should return an error \(#function)")
        })?.addToDisposeBag(disposeBag)

        viewModel?.createQRPHDynamicMerchantPaymentAction?.errors.signal.observeValues { error in
            XCTAssertEqual(error.type, ErrorType.sessionTimeout(backendErrorSpiel))
        }?.addToDisposeBag(disposeBag)

        viewModel?.createQRPHDynamicMerchantPaymentAction?.apply(QRScannerViewModelTest.createQRPHMerchant(isOnUsQr: true)).start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MerchantPaymentV2.createQRPH(QRScannerViewModelTest.dynamicMerchantPaymentQRPHRequest())), type: isEqual(to: CreatedMerchantPaymentV2.self), decoder: any())
    }

    func testCreateQRPHDynamicMerchantPaymentActionBackendError() {
        stub(configurationService) { stub in
            when(stub.qrWithCheckoutFlowEnabled.get).thenReturn(trueConfig())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.MerchantPaymentV2.createQRPH(QRScannerViewModelTest.dynamicMerchantPaymentQRPHRequest())), type: isEqual(to: CreatedMerchantPaymentV2.self), decoder: any()))
                .thenReturn(getBackendError())
        }

        viewModel?.createQRPHDynamicMerchantPaymentAction?.completed.observeValues({ _ in
            XCTFail("Action was completed. Should return an error \(#function)")
        })?.addToDisposeBag(disposeBag)

        viewModel?.createQRPHDynamicMerchantPaymentAction?.errors.signal.observeValues { error in
            XCTAssertEqual(error, self.getPayMayaError(L10n.Error.Merchant.Payment.title))
        }?.addToDisposeBag(disposeBag)

        viewModel?.createQRPHDynamicMerchantPaymentAction?.apply(QRScannerViewModelTest.createQRPHMerchant(isOnUsQr: true)).start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MerchantPaymentV2.createQRPH(QRScannerViewModelTest.dynamicMerchantPaymentQRPHRequest())), type: isEqual(to: CreatedMerchantPaymentV2.self), decoder: any())
    }

    func testCreateQRPHDynamicMerchantPaymentActionUnkownError() {
        stub(configurationService) { stub in
            when(stub.qrWithCheckoutFlowEnabled.get).thenReturn(trueConfig())
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(any(), type: isEqual(to: CreatedMerchantPaymentV2.self), decoder: any())).thenReturn(getUnknownError())
        }

        viewModel?.createQRPHDynamicMerchantPaymentAction?.completed.observeValues({ _ in
            XCTFail("Action was completed. Should return an error \(#function)")
        })?.addToDisposeBag(disposeBag)

        viewModel?.createQRPHDynamicMerchantPaymentAction?.errors.signal.observeValues { error in
            XCTAssertEqual(error, self.getPayMayaUnknownError(L10n.Error.Merchant.Payment.title))
        }?.addToDisposeBag(disposeBag)

        viewModel?.createQRPHDynamicMerchantPaymentAction?.apply(QRScannerViewModelTest.createQRPHMerchant(isOnUsQr: true)).start()

        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.MerchantPaymentV2.createQRPH(QRScannerViewModelTest.dynamicMerchantPaymentQRPHRequest())), type: isEqual(to: CreatedMerchantPaymentV2.self), decoder: any())
    }

    func testIsEcommAppEventV2QrEnabledAndIsQROffUsAvailable_WhenTrueReturnsTrue() {
        XCTAssertTrue(viewModel.isEcommAppEventV2QrEnabled)
        XCTAssertTrue(viewModel.isQROffUsAvailable)
    }

    func testIsEcommAppEventV2QrEnabledAndIsQROffUsAvailable_WhenFalseReturnsFalse() {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.dateFormat = "yyyy-MM-dd'T'HH-mm-ss.SSS'Z'"
        formatter.timeZone = TimeZone(secondsFromGMT: 0)
        if let startDate = formatter.date(from: "2019-02-01T16:00:00.000Z"), let endDate = formatter.date(from: "2099-06-01T16:00:00.000Z") {
            let serviceAvailability = MaintenanceViewModelTest.createServiceAvailability(qrOffUs: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance(start: startDate, end: endDate, message: ""))
            let maintenance = MaintenanceViewModelTest.createAppConfigMaintenance(serviceAvailability: serviceAvailability)
            stub(configurationService) { stub in
                when(stub.maintenance.get).thenReturn(maintenance)
                when(stub.ecommAppEventV2QrEnabled.get).thenReturn(CSConfig<Bool>(defaultValue: false))
            }

            XCTAssertFalse(viewModel.isEcommAppEventV2QrEnabled)
            XCTAssertFalse(viewModel.isQROffUsAvailable)
        } else {
            XCTFail("Unexpected Date Formatting Error")
        }
    }

    func testQRScan_WhenP2PSendMoneyValid() {
        guard let viewModel = viewModel else {
            XCTFail("Unexpected error")
            return
        }

        stub(qrValidationUtility) { stub in
            when(stub.getQRPHType(qrDetails: any())).thenReturn(.p2pSendMoney)
            when(stub.parseQRPH(qrCode: any())).thenReturn(validP2PSendMoneyQRDetails)
        }

        let codeInput = CodeInput(text: validP2PSendMoneyString, source: .qrScan)

        viewModel.classifyCodeAction.values.observe(on: UIScheduler()).observeValues { codeOutput in
            XCTAssertTrue(codeOutput.detectedCode is SendMoneyQR)
        }

        viewModel.classifyCodeAction.errors.observe(on: UIScheduler()).observeValues { error in
            XCTAssertNil(error, "Unexpected error signal received")
        }

        viewModel.classifyCodeAction.apply(codeInput).start()
    }

    func testQRScan_WhenP2PSendMoneyInvalid() {
        guard let viewModel = viewModel else {
            XCTFail("Unexpected error")
            return
        }

        validP2PSendMoneyQRDetails.creditAccountNumber = nil

        stub(qrValidationUtility) { stub in
            when(stub.getQRPHType(qrDetails: any())).thenReturn(.p2pSendMoney)
            when(stub.parseQRPH(qrCode: any())).thenReturn(validP2PSendMoneyQRDetails)
        }

        let codeInput = CodeInput(text: validP2PSendMoneyString, source: .qrScan)

        viewModel.classifyCodeAction.values.observe(on: UIScheduler()).observeValues { codeOutput in
            XCTAssertFalse(codeOutput.detectedCode is SendMoneyQR)
        }

        viewModel.classifyCodeAction.errors.observe(on: UIScheduler()).observeValues { error in
            XCTAssertTrue(error is PayMayaError)
        }

        viewModel.classifyCodeAction.apply(codeInput).start()
    }

    func testQRScan_WhenP2PBankTransferValid() {
        guard let viewModel = viewModel else {
            XCTFail("Unexpected error")
            return
        }

        stub(qrValidationUtility) { stub in
            when(stub.getQRPHType(qrDetails: any())).thenReturn(.p2pBankTransfer)
            when(stub.parseQRPH(qrCode: any())).thenReturn(validP2PBankTransferQRDetails)
            when(stub.validateQRPHP2p(qrDetails: any())).thenDoNothing()
        }

        let codeInput = CodeInput(text: validP2PBankTransferString, source: .qrScan)

        viewModel.classifyCodeAction.values.observe(on: UIScheduler()).observeValues { codeOutput in
            XCTAssertTrue(codeOutput.detectedCode is BankTransferQR)
        }

        viewModel.classifyCodeAction.errors.observe(on: UIScheduler()).observeValues { error in
            XCTAssertNil(error, "Unexpected error signal received")
        }

        viewModel.classifyCodeAction.apply(codeInput).start()
    }

    func testQRScan_WhenP2MOnUsStaticValid() {
        guard let viewModel = viewModel else {
            XCTFail("Unexpected error")
            return
        }

        stub(qrValidationUtility) { stub in
            when(stub.getQRPHType(qrDetails: any())).thenReturn(.p2mOnUs)
            when(stub.parseQRPH(qrCode: any())).thenReturn(validP2MQRDetails)
            when(stub.validateQRPHP2m(qrDetails: any())).thenDoNothing()
        }

        let codeInput = CodeInput(text: validP2MString, source: .qrScan)

        viewModel.classifyCodeAction.values.observe(on: UIScheduler()).observeValues { codeOutput in
            XCTAssertTrue(codeOutput.detectedCode is QRPHMerchant)
            XCTAssertEqual(false, codeOutput.isDynamicQr)
        }

        viewModel.classifyCodeAction.errors.observe(on: UIScheduler()).observeValues { error in
            XCTAssertNil(error, "Unexpected error signal received")
        }

        viewModel.classifyCodeAction.apply(codeInput).start()
    }

    func testQRScan_WhenP2MOnUsDynamicValid() {
        guard let viewModel = viewModel else {
            XCTFail("Unexpected error")
            return
        }

        validP2MQRDetails.pointOfInitiation = "12"
        validP2MQRDetails.transactionAmount = "13.00"

        stub(qrValidationUtility) { stub in
            when(stub.getQRPHType(qrDetails: any())).thenReturn(.p2mOnUs)
            when(stub.parseQRPH(qrCode: any())).thenReturn(validP2MQRDetails)
            when(stub.validateQRPHP2m(qrDetails: any())).thenDoNothing()
        }

        let codeInput = CodeInput(text: validP2MString, source: .qrScan)

        viewModel.classifyCodeAction.values.observe(on: UIScheduler()).observeValues { codeOutput in
            XCTAssertTrue(codeOutput.detectedCode is QRPHMerchant)
            XCTAssertEqual(true, codeOutput.isDynamicQr)
        }

        viewModel.classifyCodeAction.errors.observe(on: UIScheduler()).observeValues { error in
            XCTAssertNil(error, "Unexpected error signal received")
        }

        viewModel.classifyCodeAction.apply(codeInput).start()
    }

    func testQRScan_WhenP2MOffUsStaticValid() {
        guard let viewModel = viewModel else {
            XCTFail("Unexpected error")
            return
        }

        // Non-maya merchant
        validP2MQRDetails.acquirerId = "MBTCPHMMXXX"

        stub(qrValidationUtility) { stub in
            when(stub.getQRPHType(qrDetails: any())).thenReturn(.p2mOnUs)
            when(stub.parseQRPH(qrCode: any())).thenReturn(validP2MQRDetails)
            when(stub.validateQRPHP2m(qrDetails: any())).thenDoNothing()
        }

        let codeInput = CodeInput(text: validP2MString, source: .qrScan)

        viewModel.classifyCodeAction.values.observe(on: UIScheduler()).observeValues { codeOutput in
            XCTAssertTrue(codeOutput.detectedCode is QRPHMerchant)
            XCTAssertEqual(false, codeOutput.isDynamicQr)
        }

        viewModel.classifyCodeAction.errors.observe(on: UIScheduler()).observeValues { error in
            XCTAssertNil(error, "Unexpected error signal received")
        }

        viewModel.classifyCodeAction.apply(codeInput).start()
    }

    func testQRScan_WhenP2MOffUsDynamicValid() {
        guard let viewModel = viewModel else {
            XCTFail("Unexpected error")
            return
        }

        // Non-maya merchant
        validP2MQRDetails.acquirerId = "MBTCPHMMXXX"
        validP2MQRDetails.pointOfInitiation = "12"
        validP2MQRDetails.transactionAmount = "13.00"

        stub(qrValidationUtility) { stub in
            when(stub.getQRPHType(qrDetails: any())).thenReturn(.p2mOnUs)
            when(stub.parseQRPH(qrCode: any())).thenReturn(validP2MQRDetails)
            when(stub.validateQRPHP2m(qrDetails: any())).thenDoNothing()
        }

        let codeInput = CodeInput(text: validP2MString, source: .qrScan)

        viewModel.classifyCodeAction.values.observe(on: UIScheduler()).observeValues { codeOutput in
            XCTAssertTrue(codeOutput.detectedCode is QRPHMerchant)
            XCTAssertEqual(true, codeOutput.isDynamicQr)
        }

        viewModel.classifyCodeAction.errors.observe(on: UIScheduler()).observeValues { error in
            XCTAssertNil(error, "Unexpected error signal received")
        }

        viewModel.classifyCodeAction.apply(codeInput).start()
    }

    func testQRScan_WhenP2BInvalid() {
        guard let viewModel = viewModel else {
            XCTFail("Unexpected error")
            return
        }

        stub(qrValidationUtility) { stub in
            when(stub.getQRPHType(qrDetails: any())).thenReturn(.p2b)
            when(stub.parseQRPH(qrCode: any())).thenReturn(validP2MQRDetails)
        }

        let codeInput = CodeInput(text: validP2MString, source: .qrScan)

        viewModel.classifyCodeAction.errors.observe(on: UIScheduler()).observeValues { error in
            XCTAssertTrue(error is PayMayaError)
        }

        viewModel.classifyCodeAction.apply(codeInput).start()
    }
}

fileprivate extension QRScannerViewModelTest {
    static let merchantId = "734fb064-e790-4ffd-877a-03768060747d"
    static let claimId = "b0c7e7bb-c726-42b4-9fc7-570cdd6741d3"
    static let currency = "PHP"
    static let amount: Double = 100.00
    static let tokenId = "1234567890"
    static let fee: Double = 20.00
    static let merchantName = "Favorite Store"
    static let merchantCategoryCode = "7001"
    static let merchantCountryCode = "PH"
    static let merchantCity = "Mandaluyong"
    static let amountString = "100"

    // V2
    static let id = "eac5f568-f2fd-4d5f-871a-9629c3ebbec5"
    static let customer = "2f32c243-a860-40e2-b553-b1c59c63ecd5"
    static let statusPendingPayment = "PENDING_PAYMENT"

    static func createQRPHMerchant(isOnUsQr: Bool) -> QRPHMerchant {
        return QRPHMerchant(
            qrDetails: getQRDetails(),
            pointOfInitiation: .dynamic,
            qrRawValue: QRScannerViewModelTest.validQrphDynamicQRString,
            createMerchantType: .onUsDynamicQrPh,
            isOnUsQr: isOnUsQr
        )!
    }

    static func dynamicMerchantPaymentQRPHRequest() -> MerchantPaymentQRPHRequest {
        let qrDetails = getQRDetails()
        let amountRequest = AmountRequest(currency: currency, value: amount)
        return MerchantPaymentQRPHRequest(
            type: CreateMerchantType.onUsDynamicQrPh.rawValue,
            amount: amountRequest, qrPayload: QRScannerViewModelTest.validQrphDynamicQRString,
            merchant: MerchantPaymentQRPHRequest.Merchant(
                acquirerId: qrDetails.acquirerId!,
                id: qrDetails.merchantId!,
                name: qrDetails.merchantName!,
                claimId: qrDetails.additionalData?.referenceId,
                terminalId: qrDetails.additionalData?.terminalId,
                storeName: qrDetails.additionalData?.storeName,
                categoryCode: qrDetails.merchantCategoryCode,
                city: qrDetails.merchantCity,
                additionalInfo: MerchantPaymentQRPHRequest.Merchant.AdditionalInfo(
                    systemId: qrDetails.globalUniqueIdentifier!,
                    acquirerRequiredInfo: qrDetails.acquirerRequiredInfo,
                    loyaltyNum: qrDetails.additionalData?.loyaltyNumber,
                    proxyNotifyFlag: qrDetails.proxyNotifFlag!,
                    merchantCreditAccountNumber: qrDetails.merchantCreditAccountNumber,
                    countryCode: qrDetails.merchantCountryCode,
                    merchantPostalCode: qrDetails.merchantPostalCode
                )
            )
        )
    }

    static func createdMerchantPaymentV2() -> CreatedMerchantPaymentV2 {
        return CreatedMerchantPaymentV2(
            id: id,
            customer: customer,
            type: .onUsDynamicQrPh,
            amount: CreatedMerchantPaymentV2.Amount(currency: currency, value: amount),
            qrPayload: QRScannerViewModelTest.validQrphDynamicQRString,
            merchant: CreatedMerchantPaymentV2.Merchant(
                id: nil,
                name: merchantName,
                claimId: nil,
                terminalId: nil,
                acquirerId: nil,
                storeName: nil,
                city: nil,
                traceReferenceNumber: nil,
                categoryCode: nil,
                additionalInfo: nil
            ),
            paymentDetails: CreatedMerchantPaymentV2.PaymentDetails(
                paymentId: tokenId,
                paymentMethodRedirectUrl: nil
            ),
            status: statusPendingPayment,
            createdDate: Date(),
            updatedDate: Date()
        )
    }

    static func getQRDetails() -> QRDetails {
        var qrDetails = QRDetails()

        qrDetails.acquirerRequiredInfo = "006392094574580111MBTCPHMMXXX"
        qrDetails.proxyNotifFlag = "000"
        qrDetails.globalUniqueIdentifier = "ph.ppmi.p2m"
        qrDetails.acquirerId = "MBTCPHMMXXX"
        qrDetails.merchantCountryCode = merchantCountryCode
        qrDetails.merchantCity = merchantCity
        qrDetails.merchantId = "************"
        qrDetails.merchantName = merchantName
        qrDetails.merchantCategoryCode = merchantCategoryCode
        qrDetails.pointOfInitiation = "12"
        qrDetails.transactionAmount = "\(amount)"
        qrDetails.additionalData = QRAdditionalData(
            storeName: nil,
            merchantPostalCode: nil,
            loyaltyNumber: nil,
            referenceId: claimId,
            terminalId: "********",
            purpose: nil
        )

        return qrDetails
    }
}
