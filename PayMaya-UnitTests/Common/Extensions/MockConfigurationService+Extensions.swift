//
//  MockConfigurationService.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON><PERSON><PERSON> on 12/9/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import ConfigurationService
import Cuckoo
import Foundation

extension MockConfigurationService {
    func stubRegistrationMGMEnabled(_ stubbedResult: CSConfig<Bool>) {
        stub(self) { stub in
            when(stub.registrationMGMEnabled.get).thenReturn(stubbedResult)
        }
    }

    func stubMGMConfig(enabled: Bool) {
        stub(self) { stub in
            when(stub.mgm.get).thenReturn(AppConfig.MGM(enabled: enabled))
        }
    }
}
