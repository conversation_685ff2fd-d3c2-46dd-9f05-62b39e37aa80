//
//  MockConfigurationServiceV2+Extensions.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 7/6/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//
import ConfigurationService
import Cuckoo

extension MockConfigurationServiceV2 {
    func stubSecurityCenterConfig(enabled: Bool) {
        stub(self) { stub in
            when(stub.securityCenterEnabled.get).thenReturn(enabled ? .trueConfig() : .falseConfig())
        }
    }

    func stubMGMV2Config(enabled: Bool) {
        stub(self) { stub in
            when(stub.mgmV2Enabled.get).thenReturn(enabled ? .trueConfig() : .falseConfig())
        }
    }
}

extension CSConfigV2 {
    static func trueConfig() -> CSConfigV2<Bool> {
        return CSConfigV2<Bool>(defaultConfig: { true }) { _, _, _, _ in }
    }

    static func falseConfig() -> CSConfigV2<Bool> {
        return CSConfigV2<Bool>(defaultConfig: { false }) { _, _, _, _ in }
    }
}
