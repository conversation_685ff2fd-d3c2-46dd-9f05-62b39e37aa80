//
//  MayaServicesViewModelTest.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON> on 3/11/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import ConfigurationService
import Cuckoo
import Injector
import MayaCoreData
import ReactiveSwift
import StoreProvider
import XCTest

private let helpURL = "https://online.maya.ph/"

final class MayaServicesViewModelTest: ViewModelTest {
    private var viewModel: MayaServicesViewModelProtocol!

    private let coreServiceTypes: [ServiceType] = [.cashIn, .sendMoney, .bankTransfer]
    private var coreServiceIDs: [String] { coreServiceTypes.map { $0.rawValue } }

    private let investServiceTypes: [ServiceType] = [.cryptoV2, .funds, .stocks, .stocksGame]
    private var investServiceIDs: [String] { investServiceTypes.map { $0.rawValue } }
    private var investServices: [Service] {
        investServiceTypes.map { Service(type: $0) }
    }
    private var investCategory: MayaServiceCategory {
        MayaServiceCategory(id: "grow_my_money", name: "Grow my money", serviceIDs: investServiceIDs, backgroundColorString: "ECEDEF")
    }

    private let rewardsServiceTypes: [ServiceType] = [.missions, .voucher]
    private var rewardsServiceIDs: [String] { rewardsServiceTypes.map { $0.rawValue } }
    private var rewardsServices: [Service] {
        rewardsServiceTypes.map { Service(type: $0) }
    }
    private var rewardsCategory: MayaServiceCategory {
        MayaServiceCategory(id: "earn_rewards", name: "Earn rewards", serviceIDs: rewardsServiceIDs, backgroundColorString: "ECEDEF")
    }

    private var serviceTypes: [ServiceType] {
        let serviceTypes = coreServiceTypes + investServiceTypes + rewardsServiceTypes
        return serviceTypes.shuffled()
    }
    private var services: [Service] {
        serviceTypes.map { Service(type: $0) }
    }
    private var categories: [MayaServiceCategory] { [investCategory, rewardsCategory] }

    override func setUp() {
        super.setUp()

        let adBanner = AdBanner(imageURLString: nil, text: "Banner", message: "Banner Message", actionURLString: nil, tag: 1, screen: "CREATOR_STORE_BANNER")
        let adBannerProperty: MutableProperty<[AdBanner]> = MutableProperty([adBanner])
        let adBannerService = ContainerWrapper.shared.resolve(AdBannerServiceProtocol.self) as! MockAdBannerServiceProtocol

        stub(adBannerService) { stub in
            when(stub.adBannersProperty.get).thenReturn(adBannerProperty)
            when(stub.rotationIntervalProperty.get).thenReturn(MutableProperty(Constants.CleverTapRotationIntervalDefault))
        }

        stub(configurationService) { stub in
            when(stub.mayaServicesCategory.get).thenReturn(MayaServiceCategoryResponse(coreServiceIDs: coreServiceIDs, categories: categories))
            when(stub.mayaServices.get).thenReturn(services)
        }

        stub(configurationServiceV2) { stub in
            when(stub.servicesChatWithMayaEnabled.get).thenReturn(falseConfigV2())
            when(stub.servicesClevertapUnliBannerEnabled.get).thenReturn(falseConfigV2())
        }

        stub(configurationServiceV2) { stub in
            when(stub.securityCenterEnabled.get).thenReturn(falseConfigV2())
        }

        viewModel = MayaServicesViewModel()
    }

    override func tearDown() {
        super.tearDown()
        viewModel = nil
    }

    func testHelpUrl() {
        stub(propertyListStore) { stub in
            when(stub.read(isEqual(to: PropertyListStoreId.helpBaseUrl))).thenReturn(StoreResult.success(helpURL))
        }

        let stubUser = getUserWithPrefilledData()
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user))))
                .thenReturn(StoreResult.success(stubUser))
        }

        XCTAssertNotNil(viewModel.helpUrl)
    }

    func testNumberOfCoreServices() {
        XCTAssertEqual(viewModel.numberOfCoreServices, 3)
    }

    func testNumberOfCategories() {
        XCTAssertEqual(viewModel.numberOfCategories, 2)
    }

    func testNumberOfActions() {
        XCTAssertEqual(viewModel.numberOfActions, 4)

        stub(configurationServiceV2) { stub in
            when(stub.servicesChatWithMayaEnabled.get).thenReturn(trueConfigV2())
        }

        stub(configurationServiceV2) { stub in
            when(stub.securityCenterEnabled.get).thenReturn(trueConfigV2())
        }

        viewModel = MayaServicesViewModel()
        XCTAssertEqual(viewModel.numberOfActions, 6)
    }

    func testCoreServiceForRowAtIndexPath() {
        XCTAssertEqual(viewModel.coreService(forItemAt: IndexPath(row: 0, section: 0)), Service(type: .cashIn))
        XCTAssertEqual(viewModel.coreService(forItemAt: IndexPath(row: 1, section: 0)), Service(type: .sendMoney))
        XCTAssertEqual(viewModel.coreService(forItemAt: IndexPath(row: 2, section: 0)), Service(type: .bankTransfer))
        XCTAssertNil(viewModel.coreService(forItemAt: IndexPath(row: 3, section: 0)))
    }

    func testCategoryRowForID() {
        XCTAssertEqual(viewModel.categoryRow(forID: "grow_my_money"), 0)
        XCTAssertEqual(viewModel.categoryRow(forID: "earn_rewards"), 1)
        XCTAssertNil(viewModel.categoryRow(forID: "do_more"))
    }

    func testCategoryViewModelForRowAtIndexPath() {
        let annotatedInvestServices = investServiceTypes.map { Service(type: $0) }
        let annotatedRewardsServices = rewardsServiceTypes.map { Service(type: $0) }

        let investCategoryViewModel = MayaServiceCategoryViewModel(category: investCategory, services: annotatedInvestServices, categoryRow: 0)
        let rewardsCategoryViewModel = MayaServiceCategoryViewModel(category: rewardsCategory, services: annotatedRewardsServices, categoryRow: 1)

        XCTAssertEqual(viewModel.categoryViewModel(forRowAt: IndexPath(row: 0, section: 0)), investCategoryViewModel)
        XCTAssertEqual(viewModel.categoryViewModel(forRowAt: IndexPath(row: 1, section: 0)), rewardsCategoryViewModel)
        XCTAssertNil(viewModel.categoryViewModel(forRowAt: IndexPath(row: 2, section: 0)))
    }

    func testDefaultCategories() {
        stub(configurationService) { stub in
            when(stub.mayaServicesCategory.get).thenReturn(nil)
            when(stub.mayaServices.get).thenReturn(nil)
        }

        viewModel = MayaServicesViewModel()

        let defaultCategoryViewModels = MayaServiceCategory.defaultCategories.enumerated().map { index, category in
            let serviceIDs = ServiceCategory.allCases[index].defaultServiceIds
            let serviceTypes = serviceIDs.compactMap { ServiceType(rawValue: $0) }
            let services = serviceTypes.map { Service(type: $0) }.filter { Service.defaultServices.contains($0) }
            return MayaServiceCategoryViewModel(category: category, services: services, categoryRow: index)
        }

        XCTAssertEqual(viewModel.categoryViewModel(forRowAt: IndexPath(row: 0, section: 0)), defaultCategoryViewModels[0])
        XCTAssertEqual(viewModel.categoryViewModel(forRowAt: IndexPath(row: 1, section: 0)), defaultCategoryViewModels[1])
        XCTAssertEqual(viewModel.categoryViewModel(forRowAt: IndexPath(row: 2, section: 0)), defaultCategoryViewModels[2])
        XCTAssertNil(viewModel.categoryViewModel(forRowAt: IndexPath(row: 6, section: 0)))
    }

    func testActionForRowAtIndexPath() {
        XCTAssertEqual(viewModel.action(forRowAt: IndexPath(row: 0, section: 0)), .help)
        XCTAssertEqual(viewModel.action(forRowAt: IndexPath(row: 1, section: 0)), .profile)
        XCTAssertEqual(viewModel.action(forRowAt: IndexPath(row: 2, section: 0)), .settings)
        XCTAssertEqual(viewModel.action(forRowAt: IndexPath(row: 3, section: 0)), .about)
        XCTAssertEqual(viewModel.action(forRowAt: IndexPath(row: 4, section: 0)), nil)

        stub(configurationServiceV2) { stub in
            when(stub.servicesChatWithMayaEnabled.get).thenReturn(trueConfigV2())
        }

        stub(configurationServiceV2) { stub in
            when(stub.securityCenterEnabled.get).thenReturn(trueConfigV2())
        }

        viewModel = MayaServicesViewModel()
        XCTAssertEqual(viewModel.action(forRowAt: IndexPath(row: 0, section: 0)), .securityCenter)
        XCTAssertEqual(viewModel.action(forRowAt: IndexPath(row: 1, section: 0)), .chat)
        XCTAssertEqual(viewModel.action(forRowAt: IndexPath(row: 2, section: 0)), .help)
        XCTAssertEqual(viewModel.action(forRowAt: IndexPath(row: 3, section: 0)), .profile)
        XCTAssertEqual(viewModel.action(forRowAt: IndexPath(row: 4, section: 0)), .settings)
        XCTAssertEqual(viewModel.action(forRowAt: IndexPath(row: 5, section: 0)), .about)
        XCTAssertEqual(viewModel.action(forRowAt: IndexPath(row: 6, section: 0)), nil)
    }

    func testUpdateSourceScreen() {
        XCTAssertNil(viewModel.sourceScreen)

        viewModel.updateSourceScreen(.dashboard)
        XCTAssertEqual(viewModel.sourceScreen, .dashboard)

        viewModel.updateSourceScreen(.floatingTab)
        XCTAssertEqual(viewModel.sourceScreen, .floatingTab)
    }

    func testNoBannerRemoveClevertapBannerCategory() {
        XCTAssertEqual(viewModel.categoriesProperty.value.count, 2)
        viewModel.removeClevertapBannerCategory()
        XCTAssertEqual(viewModel.categoriesProperty.value.count, 2)
        XCTAssertEqual(viewModel.bannerLocation, .none)
    }

    func testWithBannerAddRemoveClevertapBannerCategory() {
        stub(configurationServiceV2) { stub in
            when(stub.servicesClevertapUnliBannerEnabled.get).thenReturn(trueConfigV2())
        }
        stub(configurationService) { stub in
            when(stub.mayaServicesCategory.get).thenReturn(MayaServiceCategoryResponse(coreServiceIDs: coreServiceIDs, categories: categories, bannerIndex: 1))
        }

        viewModel = MayaServicesViewModel()
        XCTAssertEqual(viewModel.categoriesProperty.value.count, 2)
        viewModel.addClevertapBannerCategory()
        XCTAssertEqual(viewModel.categoriesProperty.value.count, 3)
        viewModel.removeClevertapBannerCategory()
        XCTAssertEqual(viewModel.categoriesProperty.value.count, 2)
    }

    func testBannerIsInCategoriesSection() {
        stub(configurationServiceV2) { stub in
            when(stub.servicesClevertapUnliBannerEnabled.get).thenReturn(trueConfigV2())
        }
        stub(configurationService) { stub in
            when(stub.mayaServicesCategory.get).thenReturn(MayaServiceCategoryResponse(coreServiceIDs: coreServiceIDs, categories: categories, bannerIndex: 1))
        }

        viewModel = MayaServicesViewModel()
        viewModel.addClevertapBannerCategory()
        XCTAssertTrue(viewModel.isCategoryRowBanner(forRowAt: IndexPath(row: 1, section: 0)))
        XCTAssertEqual(viewModel.bannerLocation, .categoriesSection)
    }

    func testBannerIsInCoreServicesSection() {
        stub(configurationServiceV2) { stub in
            when(stub.servicesClevertapUnliBannerEnabled.get).thenReturn(trueConfigV2())
        }
        stub(configurationService) { stub in
            when(stub.mayaServicesCategory.get).thenReturn(MayaServiceCategoryResponse(coreServiceIDs: coreServiceIDs, categories: categories, bannerIndex: 0))
        }

        viewModel = MayaServicesViewModel()
        XCTAssertFalse(viewModel.isCategoryRowBanner(forRowAt: IndexPath(row: 0, section: 0)))
        XCTAssertEqual(viewModel.bannerLocation, .categoriesSection)
    }

    func testBannerIndexIsOutOfRange() {
        stub(configurationServiceV2) { stub in
            when(stub.servicesClevertapUnliBannerEnabled.get).thenReturn(trueConfigV2())
        }
        stub(configurationService) { stub in
            when(stub.mayaServicesCategory.get).thenReturn(MayaServiceCategoryResponse(coreServiceIDs: coreServiceIDs, categories: categories, bannerIndex: 10))
        }

        viewModel = MayaServicesViewModel()
        XCTAssertFalse(viewModel.isCategoryRowBanner(forRowAt: IndexPath(row: 0, section: 0)))
        XCTAssertEqual(viewModel.bannerLocation, .none)
    }
}

private func getUserWithPrefilledData(cardProfile: StubCardProfile = StubCardProfile()) -> StubUser {
    let stubProfile = getProfileData()
    let stubMainIdentity = StubMainIdentity(type: "msisdn", value: "+************")
    let stubFund = StubFund(enabled: 1, id: "1234567-abcde", name: "virtual_card", status: FundSourceStatus.virtualCard.rawValue, type: FundSourceType.virtual.rawValue, cardProfile: cardProfile, purchases: nil, user: stubProfile.user)
    return StubUser(id: "user_id", accountStatus: "ACTIVE", canBeReferred: 1, canLinkCard: 1, kycStatus: "kyc1", network: "Smart", physicalCardStatus: "activated", status: "ACTIVE", token: "1234567", hasGovID: 1, activities: nil, backupIdentity: nil, balance: nil, funds: [stubFund], identities: NSSet(array: [stubMainIdentity]), privacyPolicy: StubPrivacyPolicy(version: "v1"), profile: stubProfile, type: nil)
}

private func getStubAddress() -> StubAddress {
    return StubAddress(city: "San Jose Del Monte", country: "PH", line1: "123", line2: nil, locality: "Highway Hills", state: "NCR", zipCode: "1501", profile: StubProfile())
}

private func getProfileData() -> StubProfile {
    let stubUser = StubUser()
    let stubAddress = getStubAddress()
    let stubProfile = StubProfile(birthDate: nil, firstName: "John", lastName: "Doe", middleName: nil, address: stubAddress, user: stubUser)
    return stubProfile
}
