//
//  MayaProfileViewModelTest.swift
//  PayMaya-UnitTests
//
//  Created by <PERSON> on 4/7/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import ConfigurationService
import Cuckoo
import Injector
import MayaCoreData
import Moya
import ReactiveSwift
import StoreProvider
import XCTest

private let helpUrl = "cares.paymaya.com"
private let chatbotUrl = "https://cares.paymaya.com/s/in-app-chat"
private let bannerInviteFriends = "Get P50 when you invite your friends!"

class MayaProfileViewModelTest: ViewModelTest {
    var viewModel: MayaProfileViewModelProtocol!
    var signoutService: MockSignoutService!

    override func setUp() {
        super.setUp()
        stub(configurationService) { stub in
            when(stub.mgm.get).thenReturn(nil)
            when(stub.verifiedSellerLevels.get).thenReturn([])
            // TODO: Miquido <PERSON> Auth - remove when ended dev cell
            when(stub.pushApprovalEnabled.get).thenReturn(falseConfig())
        }

        stub(configurationServiceV2) { stub in
            when(stub.securityCenterEnabled.get).thenReturn(falseConfigV2())
            when(stub.profileCleverTapUnliBannersEnabled.get).thenReturn(falseConfigV2())
        }

        configurationServiceV2.stubMGMV2Config(enabled: false)

        let user = getUserWithPrefilledData()
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }

        let adBanner = AdBanner(imageURLString: nil, text: "Banner", message: "Banner Message", actionURLString: nil, tag: 1, screen: "CREATOR_STORE_BANNER")
        let adBannerProperty: MutableProperty<[AdBanner]> = MutableProperty([adBanner])
        let adBannerService = ContainerWrapper.shared.resolve(AdBannerServiceProtocol.self) as! MockAdBannerServiceProtocol

        stub(adBannerService) { stub in
            when(stub.adBannersProperty.get).thenReturn(adBannerProperty)
            when(stub.rotationIntervalProperty.get).thenReturn(MutableProperty(Constants.CleverTapRotationIntervalDefault))
        }

        signoutService = ContainerWrapper.shared.resolve(SignoutService.self) as? MockSignoutService
        stubEligibility()
        viewModel = MayaProfileViewModel()
    }

    func testSignoutSession() {
        stub(signoutService) { stub in
            when(stub.signout(msisdn: anyOptional())).thenReturn(SignalProducer.empty)
        }

        viewModel.signoutAction.apply().start()
        verify(signoutService, times(1)).signout(msisdn: anyOptional())
    }

    func testHelpUrlWithCaresParameter() {
        stub(propertyListStore) { stub in
            when(stub.read(isEqual(to: PropertyListStoreId.helpBaseUrl))).thenReturn(StoreResult.success(helpUrl))
        }

        stub(configurationService) { stub in
            when(stub.mgm.get).thenReturn(nil)
        }

        guard let caresUrl = viewModel.helpUrl else {
            XCTFail("Unexpected error")
            return
        }

        XCTAssertTrue(caresUrl.absoluteString.contains(Constants.WebView.salesForceQuery.rawValue))
    }

    func testProfileProperties() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Profile.fetch))).thenReturn(getSuccessProducer())
        }

        viewModel.userProfileAction.completed.observeValues { [weak self] in
            guard let self = self else {
                XCTFail("Unexpected error")
                return
            }
            XCTAssertEqual(self.viewModel.mobileNumber.value, "+************")
            XCTAssertEqual(self.viewModel.name.value, "John Doe")
            XCTAssertTrue(self.viewModel.username.value.isEmpty)
            XCTAssertFalse(self.viewModel.isAccountLimited.value)
            XCTAssertEqual(self.viewModel.kycClientStatusProperty.value, .one)
            XCTAssertEqual(self.viewModel.reKYCStatusProperty.value, .tagged)
        }?.addToDisposeBag(disposeBag)

        viewModel.userProfileAction.errors.observeValues { _ in
            XCTFail("Error in User Profile Action. \(#function)")
        }?.addToDisposeBag(disposeBag)

        viewModel.userProfileAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Profile.fetch))
    }

    func testVerifiedSeller() {
        let user = getUserWithPrefilledData()
        let stubCustomerAccount = StubCustomerAccount(level: "CASUAL_SELLER_L1_CONSUMER_WALLET", user: user, walletId: "68a578a9-356a-4189-bac3-cb9c9f2ff3f5")
        user.customerAccount = stubCustomerAccount

        stub(configurationService) { stub in
            when(stub.mgm.get).thenReturn(nil)
            when(stub.verifiedSellerLevels.get).thenReturn(["CASUAL_SELLER_L1_CONSUMER_WALLET"])
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Profile.customerAccount))).thenReturn(getSuccessProducer())
        }

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }

        signoutService = ContainerWrapper.shared.resolve(SignoutService.self) as? MockSignoutService
        viewModel = MayaProfileViewModel()

        viewModel.customerAccountAction.completed.observeValues { [weak self] in
            guard let self = self else {
                XCTFail("Unexpected error")
                return
            }
            XCTAssertEqual(self.viewModel.kycClientStatusProperty.value, .verifiedSeller)
        }?.addToDisposeBag(disposeBag)

        viewModel.customerAccountAction.errors.observeValues { _ in
            XCTFail("Error in User Profile Action. \(#function)")
        }?.addToDisposeBag(disposeBag)

        viewModel.customerAccountAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Profile.customerAccount))
    }

    func testUserProfileActionSuccess() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Profile.fetch))).thenReturn(getSuccessProducer())
        }

        viewModel.userProfileAction.completed.observeValues { _ in
            XCTAssertTrue(true, "Result is expected")
        }?.addToDisposeBag(disposeBag)

        viewModel.userProfileAction.errors.observeValues { _ in
            XCTFail("Error in User Profile Action. \(#function)")
        }?.addToDisposeBag(disposeBag)

        viewModel.userProfileAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Profile.fetch))
    }

    func testUserProfileActionFailed() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Profile.fetch))).thenReturn(getBackendError())
        }

        viewModel.userProfileAction.completed.observeValues { _ in
            XCTFail("Must not send success signal")
        }?.addToDisposeBag(disposeBag)

        viewModel.userProfileAction.errors.observeValues { error in
            XCTAssertNotNil(error)
        }?.addToDisposeBag(disposeBag)

        viewModel.userProfileAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Profile.fetch))
    }

    func testCustomerAccountActionSuccess() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Profile.customerAccount))).thenReturn(getSuccessProducer())
        }

        viewModel.customerAccountAction.completed.observeValues { _ in
            XCTAssertTrue(true, "Result is expected")
        }?.addToDisposeBag(disposeBag)

        viewModel.customerAccountAction.errors.observeValues { _ in
            XCTFail("Error in User Profile Action. \(#function)")
        }?.addToDisposeBag(disposeBag)

        viewModel.customerAccountAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Profile.customerAccount))
    }

    func testCustomerAccountActionFailed() {
        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Profile.customerAccount))).thenReturn(getBackendError())
        }

        viewModel.customerAccountAction.completed.observeValues { _ in
            XCTFail("Must not send success signal")
        }?.addToDisposeBag(disposeBag)

        viewModel.customerAccountAction.errors.observeValues { error in
            XCTAssertNotNil(error)
        }?.addToDisposeBag(disposeBag)

        viewModel.customerAccountAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Profile.customerAccount))
    }
    func testProfilePropertiesWithUsername() {
        let user = getUserWithUsername()
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))).thenReturn(StoreResult.success(user))
        }

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Profile.fetch))).thenReturn(getSuccessProducer())
        }

        viewModel.userProfileAction.completed.observeValues { [weak self] in
            guard let self = self else {
                XCTFail("Unexpected error")
                return
            }
            XCTAssertEqual(self.viewModel.mobileNumber.value, "+************")
            XCTAssertEqual(self.viewModel.name.value, "John Doe")
            XCTAssertEqual(self.viewModel.username.value, "@stefan.kuhn")
            XCTAssertFalse(self.viewModel.isAccountLimited.value)
            XCTAssertEqual(self.viewModel.kycClientStatusProperty.value, .one)
            XCTAssertEqual(self.viewModel.reKYCStatusProperty.value, .submitted)
        }?.addToDisposeBag(disposeBag)

        viewModel.userProfileAction.errors.observeValues { _ in
            XCTFail("Error in User Profile Action. \(#function)")
        }?.addToDisposeBag(disposeBag)

        viewModel.userProfileAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Profile.fetch))
    }

    func testHelpUrl() {
        stub(propertyListStore) { stub in
            when(stub.read(isEqual(to: PropertyListStoreId.helpBaseUrl))).thenReturn(StoreResult.success(helpUrl))
        }

        let stubUser = getUserWithPrefilledData()
        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user))))
                .thenReturn(StoreResult.success(stubUser))
        }

        XCTAssertNotNil(viewModel.helpUrl)
        verify(databaseStore, times(2)).read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))
        XCTAssertNotNil(viewModel.helpUrl)
    }

    func testNilHelpUrl() {
        stub(propertyListStore) { stub in
            when(stub.read(isEqual(to: PropertyListStoreId.helpBaseUrl))).thenReturn(StoreResult.failure(.none))
        }
        XCTAssertNil(viewModel.helpUrl)
    }

    func testHelpUrlWithoutProfile() {
        stub(propertyListStore) { stub in
            when(stub.read(isEqual(to: PropertyListStoreId.helpBaseUrl))).thenReturn(StoreResult.success(helpUrl))
        }

        stub(databaseStore) { stub in
            when(stub.read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user))))
                .thenReturn(StoreResult.failure(.none))
        }

        XCTAssertEqual(viewModel.helpUrl, URL(string: helpUrl))
        verify(databaseStore, times(2)).read(isEqual(to: DatabaseStore.ReadingOptions(DatabaseEntity.user)))
    }

    func testReferralNotEligible() {
        viewModel = MayaProfileViewModel()

        configurationService.stubMGMConfig(enabled: true)
        stubEligibility(canRefer: false, canBeReferred: false, hasReachedQuota: false)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Referral.eligibility))).thenReturn(getSuccessProducer())
        }

        viewModel.referralEligibilityAction.completed.observeValues { [weak self] in
            guard
                let self = self,
                let profileMenu = self.viewModel.profileMenuProperty.value
            else {
                XCTFail("Unexpected error")
                return
            }
            let canInviteFriends = self.viewModel.canInviteFriendsProperty.value

            XCTAssertEqual([.quickGuide, .favorites, .missions, .vouchers, .accountLimits, .settings, .getHelp, .rateThisApp, .signout], profileMenu)
            XCTAssertFalse(canInviteFriends)
        }?.addToDisposeBag(disposeBag)

        viewModel.referralEligibilityAction.errors.observeValues { _ in
            XCTFail("Unexpected error signal received")
        }?.addToDisposeBag(disposeBag)

        viewModel.referralEligibilityAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Referral.eligibility))
    }

    func testReferralEligibilityAction() {
        configurationService.stubMGMConfig(enabled: true)
        stubEligibility(canRefer: true, canBeReferred: true, hasReachedQuota: false)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Referral.eligibility))).thenReturn(getSuccessProducer())
        }

        viewModel.referralEligibilityAction.completed.observeValues { [weak self] in
            guard let canInviteFriends = self?.viewModel.canInviteFriendsProperty.value
            else {
                XCTFail("Unexpected error")
                return
            }
            XCTAssertTrue(canInviteFriends)
        }?.addToDisposeBag(disposeBag)

        viewModel.referralEligibilityAction.errors.observeValues { _ in
            XCTFail("Unexpected error signal received")
        }?.addToDisposeBag(disposeBag)

        viewModel.referralEligibilityAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Referral.eligibility))
    }

    func testDefaultProfileMenuItems() {
        configurationService.stubMGMConfig(enabled: true)
        stubEligibility(canBeReferred: true)
        viewModel = MayaProfileViewModel()

        guard let profileMenu = self.viewModel.profileMenuProperty.value else {
            XCTFail("Unexpected error")
            return
        }
        XCTAssertEqual([.quickGuide, .favorites, .missions, .vouchers, .submitInviteCode, .accountLimits, .settings, .getHelp, .rateThisApp, .signout], profileMenu)
    }

    func testProfileMenuItemsWithoutReferral() {
        viewModel = MayaProfileViewModel()

        configurationService.stubMGMConfig(enabled: true)
        stubEligibility(canRefer: true, canBeReferred: false, hasReachedQuota: false)

        stub(reactiveAPIProvider) { stub in
            when(stub.request(isEqual(to: API.Referral.eligibility))).thenReturn(getSuccessProducer())
        }

        viewModel.referralEligibilityAction.completed.observeValues { [weak self] in
            guard
                let self = self,
                let profileMenu = self.viewModel.profileMenuProperty.value else {
                XCTFail("Unexpected error")
                return
            }
            XCTAssertEqual([.quickGuide, .favorites, .missions, .vouchers, .accountLimits, .settings, .getHelp, .rateThisApp, .signout], profileMenu)
        }?.addToDisposeBag(disposeBag)

        viewModel.referralEligibilityAction.errors.observeValues { _ in
            XCTFail("Unexpected error signal received")
        }?.addToDisposeBag(disposeBag)

        viewModel.referralEligibilityAction.apply().start()
        verify(reactiveAPIProvider, times(1)).request(isEqual(to: API.Referral.eligibility))
    }

    func testRateThisAppMenuItem() {
        guard let profileMenu = self.viewModel.profileMenuProperty.value else {
            XCTFail("Unexpected error")
            return
        }

        XCTAssertTrue(profileMenu.contains(.rateThisApp))

        stub(configurationServiceV2) { stub in
            when(stub.rateThisAppButtonEnabled.get).thenReturn(falseConfigV2())
        }

        viewModel = MayaProfileViewModel()

        guard let newProfileMenu = self.viewModel.profileMenuProperty.value else {
            XCTFail("Unexpected error")
            return
        }

        XCTAssertFalse(newProfileMenu.contains(.rateThisApp))
    }

    func testIsEddEnabled() {
        XCTAssertTrue(viewModel.isEddEnabled)
        stub(configurationService) { stub in
            when(stub.eddEnabled.get).thenReturn(falseConfig())
        }
        XCTAssertFalse(viewModel.isEddEnabled)
    }

    func testIsSetUsernameEnabled() {
        XCTAssertTrue(viewModel.isSetUsernameEnabled)
        stub(configurationService) { stub in
            when(stub.mayaSetUsernameEnabled.get).thenReturn(falseConfig())
        }
        XCTAssertFalse(viewModel.isSetUsernameEnabled)
    }

    func testExpandedReKYCAction() {
        stub(userDefaultsStore) { stub in
            when(stub.readRaw(isEqual(to: UserDefaultsStoreId.expandedReKYCAction)))
                .thenReturn("UI_BLOCK_FOR_SUBMISSION")
        }
        XCTAssertEqual(viewModel.expandedReKYCAction, "UI_BLOCK_FOR_SUBMISSION")
    }
}

// MARK: - Security Center Menu Item
extension MayaProfileViewModelTest {
    func testSecurityCenterTileDisabled() {
        configurationServiceV2.stubSecurityCenterConfig(enabled: false)
        configurationService.stubMGMConfig(enabled: true)
        stubEligibility(canBeReferred: true)

        viewModel = MayaProfileViewModel()
        guard let profileMenu = self.viewModel.profileMenuProperty.value else {
            XCTFail("Unexpected error")
            return
        }

        let expectedTiles: [MayaProfileMenuItem] = [.quickGuide,
                                                    .favorites,
                                                    .missions,
                                                    .vouchers,
                                                    .submitInviteCode,
                                                    .accountLimits,
                                                    .settings,
                                                    .getHelp,
                                                    .rateThisApp,
                                                    .signout]
        XCTAssertEqual(profileMenu, expectedTiles)
    }

    func testSecurityCenterTileEnabled() {
        configurationServiceV2.stubSecurityCenterConfig(enabled: true)
        configurationService.stubMGMConfig(enabled: true)
        stubEligibility(canBeReferred: true)

        viewModel = MayaProfileViewModel()
        guard let profileMenu = viewModel.profileMenuProperty.value else {
            XCTFail("Unexpected error")
            return
        }

        let expectedTiles: [MayaProfileMenuItem] = [.securityCenter,
                                                    .quickGuide,
                                                    .favorites,
                                                    .missions,
                                                    .vouchers,
                                                    .submitInviteCode,
                                                    .accountLimits,
                                                    .settings,
                                                    .getHelp,
                                                    .rateThisApp,
                                                    .signout]
        XCTAssertEqual(profileMenu, expectedTiles)
    }
}

// MARK: - MGM 2.0 Tiles Test
extension MayaProfileViewModelTest {
    func testMGMV2EnabledCase1() {
        // Security Center = Disabled
        // MGM = Disabled
        // canBeReferred = Disabled
        configurationServiceV2.stubSecurityCenterConfig(enabled: false)
        configurationServiceV2.stubMGMV2Config(enabled: true)
        configurationService.stubMGMConfig(enabled: false)
        stubEligibility(canBeReferred: false)

        viewModel = MayaProfileViewModel()
        guard let profileMenu = viewModel.profileMenuProperty.value else {
            XCTFail("Unexpected error")
            return
        }

        let expectedTiles: [MayaProfileMenuItem] = [.inviteAFriend,
                                                    .quickGuide,
                                                    .favorites,
                                                    .missions,
                                                    .vouchers,
                                                    .accountLimits,
                                                    .settings,
                                                    .getHelp,
                                                    .rateThisApp,
                                                    .signout]
        XCTAssertEqual(profileMenu, expectedTiles)
    }

    func testMGMV2EnabledCase2() {
        // Security Center = Disabled
        // MGM = Disabled
        // canBeReferred = Enabled
        configurationServiceV2.stubSecurityCenterConfig(enabled: false)
        configurationServiceV2.stubMGMV2Config(enabled: true)
        configurationService.stubMGMConfig(enabled: true)
        stubEligibility(canBeReferred: true)

        viewModel = MayaProfileViewModel()
        guard let profileMenu = viewModel.profileMenuProperty.value else {
            XCTFail("Unexpected error")
            return
        }

        let expectedTiles: [MayaProfileMenuItem] = [.submitInviteCode,
                                                    .inviteAFriend,
                                                    .quickGuide,
                                                    .favorites,
                                                    .missions,
                                                    .vouchers,
                                                    .accountLimits,
                                                    .settings,
                                                    .getHelp,
                                                    .rateThisApp,
                                                    .signout]
        XCTAssertEqual(profileMenu, expectedTiles)
    }

    func testMGMV2EnabledCase3() {
        // Security Center = Enabled
        // MGM = Disabled
        // canBeReferred = Disabled
        configurationServiceV2.stubSecurityCenterConfig(enabled: true)
        configurationServiceV2.stubMGMV2Config(enabled: true)
        configurationService.stubMGMConfig(enabled: false)
        stubEligibility(canBeReferred: false)

        viewModel = MayaProfileViewModel()
        guard let profileMenu = viewModel.profileMenuProperty.value else {
            XCTFail("Unexpected error")
            return
        }

        let expectedTiles: [MayaProfileMenuItem] = [.inviteAFriend,
                                                    .securityCenter,
                                                    .quickGuide,
                                                    .favorites,
                                                    .missions,
                                                    .vouchers,
                                                    .accountLimits,
                                                    .settings,
                                                    .getHelp,
                                                    .rateThisApp,
                                                    .signout]
        XCTAssertEqual(profileMenu, expectedTiles)
    }

    func testMGMV2EnabledCase4() {
        // Security Center = Enabled
        // MGM = Enabled
        // canBeReferred = Enabled
        configurationServiceV2.stubSecurityCenterConfig(enabled: true)
        configurationServiceV2.stubMGMV2Config(enabled: true)
        configurationService.stubMGMConfig(enabled: true)
        stubEligibility(canBeReferred: true)

        viewModel = MayaProfileViewModel()
        guard let profileMenu = viewModel.profileMenuProperty.value else {
            XCTFail("Unexpected error")
            return
        }

        let expectedTiles: [MayaProfileMenuItem] = [.submitInviteCode,
                                                    .inviteAFriend,
                                                    .securityCenter,
                                                    .quickGuide,
                                                    .favorites,
                                                    .missions,
                                                    .vouchers,
                                                    .accountLimits,
                                                    .settings,
                                                    .getHelp,
                                                    .rateThisApp,
                                                    .signout]
        XCTAssertEqual(profileMenu, expectedTiles)
    }
}

// MARK: - Helper Methods
extension MayaProfileViewModelTest {
    func stubEligibility(canRefer: Bool = false,
                         canBeReferred: Bool = false,
                         hasReachedQuota: Bool = false) {
        stub(encryptedDefaultsStore) { stub in
            let eligibility = Eligibility(canRefer: canRefer, canBeReferred: canBeReferred, hasReachedQuota: hasReachedQuota)
            when(stub.read(isEqual(to: EncryptedDefaultsStoreId.eligibility), type: isEqual(to: Eligibility.self))).thenReturn(StoreResult.success(eligibility))
        }
    }
}

extension MayaProfileViewModelTest {
    func getUserWithPrefilledData(cardProfile: StubCardProfile = StubCardProfile()) -> StubUser {
        let stubProfile = getProfileData()
        let stubMainIdentity = StubMainIdentity(type: "msisdn", value: "+************")
        let stubFund = StubFund(enabled: 1, id: "1234567-abcde", name: "virtual_card", status: FundSourceStatus.virtualCard.rawValue, type: FundSourceType.virtual.rawValue, cardProfile: cardProfile, purchases: nil, user: stubProfile.user)
        return StubUser(id: "user_id", accountStatus: "ACTIVE", canBeReferred: 1, canLinkCard: 1, kycStatus: "kyc1", network: "Smart", physicalCardStatus: "activated", status: "ACTIVE", token: "1234567", hasGovID: 1, activities: nil, backupIdentity: nil, balance: nil, funds: [stubFund], identities: NSSet(array: [stubMainIdentity]), privacyPolicy: StubPrivacyPolicy(version: "v1"), profile: stubProfile, type: nil, reKycStatus: "tagged")
    }

    func getUserWithUsername(cardProfile: StubCardProfile = StubCardProfile()) -> StubUser {
        let stubProfile = getProfileData()
        let stubMainIdentity = StubMainIdentity(type: "msisdn", value: "+************")
        let stubUsernameIdentity = StubMainIdentity(type: "username", value: "@stefan.kuhn")
        let stubFund = StubFund(enabled: 1, id: "1234567-abcde", name: "virtual_card", status: FundSourceStatus.virtualCard.rawValue, type: FundSourceType.virtual.rawValue, cardProfile: cardProfile, purchases: nil, user: stubProfile.user)
        return StubUser(id: "user_id", accountStatus: "ACTIVE", canBeReferred: 1, canLinkCard: 1, kycStatus: "kyc1", network: "Smart", physicalCardStatus: "activated", status: "ACTIVE", token: "1234567", hasGovID: 1, activities: nil, backupIdentity: nil, balance: nil, funds: [stubFund], identities: NSSet(array: [stubMainIdentity, stubUsernameIdentity]), privacyPolicy: StubPrivacyPolicy(version: "v1"), profile: stubProfile, type: nil, reKycStatus: "submitted")
    }

    func getStubAddress() -> StubAddress {
        return StubAddress(city: "San Jose Del Monte", country: "PH", line1: "123", line2: nil, locality: "Highway Hills", state: "NCR", zipCode: "1501", profile: StubProfile())
    }

    func getProfileData() -> StubProfile {
        let stubUser = StubUser()
        let stubAddress = getStubAddress()
        let stubProfile = StubProfile(birthDate: nil, firstName: "John", lastName: "Doe", middleName: nil, address: stubAddress, user: stubUser)
        return stubProfile
    }

    func testInviteCodeBannerSpiel() {
        let referral = AppConfig.Referral(spielEnterInviteCode: "", spielInviteFriends: "", spielSuccessInviteCode: "", spielBannerInviteFriends: bannerInviteFriends, spielShareMessageInviteFriends: "")
        stub(configurationService) { stub in
            when(stub.referral.get).thenReturn(referral)
        }
        XCTAssertEqual(viewModel.inviteCodeBannerSpiel, bannerInviteFriends)

        stub(configurationService) { stub in
            when(stub.referral.get).thenReturn(nil)
        }
        XCTAssertEqual(viewModel.inviteCodeBannerSpiel, String.empty)
    }
}
