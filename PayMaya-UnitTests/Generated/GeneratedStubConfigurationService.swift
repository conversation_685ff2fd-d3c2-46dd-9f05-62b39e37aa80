// This is only generated. Do not edit.

import Cuckoo

import ConfigurationService


extension ViewModelTest {
	func stubConfigurationService() {
		stub(configurationService) { stub in
			when(stub.isAppMaintenanceEnabled.get).thenReturn(trueConfig())
			when(stub.glimpseEnabled.get).thenReturn(trueConfig())
			when(stub.instapayQREnabled.get).thenReturn(trueConfig())
			when(stub.bankPullFundsEnabled.get).thenReturn(trueConfig())
			when(stub.changeMinEnabled.get).thenReturn(trueConfig())
			when(stub.whitelistedAnalyticsEventsV2Enabled.get).thenReturn(trueConfig())
			when(stub.topUpServiceEnabled.get).thenReturn(trueConfig())
			when(stub.whiteThemeEnabled.get).thenReturn(trueConfig())
			when(stub.digitalOREnabled.get).thenReturn(trueConfig())
			when(stub.amvcFingerprintingEnabled.get).thenReturn(trueConfig())
			when(stub.eddEnabled.get).thenReturn(trueConfig())
			when(stub.idClassificationEnabled.get).thenReturn(trueConfig())
			when(stub.mandatoryIdMatchingEnabled.get).thenReturn(trueConfig())
			when(stub.billsPayCheckoutFlowV3Enabled.get).thenReturn(trueConfig())
			when(stub.billsPayInitialReceiptEntrypointEnabled.get).thenReturn(trueConfig())
			when(stub.billsPayHandleOtherFieldsFromDeeplinkEnabled.get).thenReturn(trueConfig())
			when(stub.billsPayDisableAccountNumberField.get).thenReturn(trueConfig())
			when(stub.billsPaySkipWalletValidationEnabled.get).thenReturn(trueConfig())
			when(stub.billsPayFavoritesWithCreatedPayment.get).thenReturn(trueConfig())
			when(stub.cashInWithAccountLimitEnabled.get).thenReturn(trueConfig())
			when(stub.amountFormatterV2Enabled.get).thenReturn(trueConfig())
			when(stub.cryptoEnabled.get).thenReturn(trueConfig())
			when(stub.mayaLoansSimplifiedCheckoutEnabled.get).thenReturn(trueConfig())
			when(stub.loansCreditCardDetailsV2Enabled.get).thenReturn(trueConfig())
			when(stub.mayaLoansV3Enabled.get).thenReturn(trueConfig())
			when(stub.mayaLoansICLEnabled.get).thenReturn(trueConfig())
			when(stub.unifiedLendingExperienceReskin.get).thenReturn(trueConfig())
			when(stub.mayaCreditAccountSummaryAddEnabled.get).thenReturn(trueConfig())
			when(stub.mayaCreditPartnerMerchantEnabled.get).thenReturn(trueConfig())
			when(stub.iclMothersMaidenNameV3Enabled.get).thenReturn(trueConfig())
			when(stub.mayaLoansEnabled.get).thenReturn(trueConfig())
			when(stub.iclInitialEligibilityCheckEnabled.get).thenReturn(trueConfig())
			when(stub.iclDropdownV2Enabled.get).thenReturn(trueConfig())
			when(stub.iclPhaseTwoV1Enabled.get).thenReturn(trueConfig())
			when(stub.iclPhaseTwoV2Enabled.get).thenReturn(trueConfig())
			when(stub.iclToMambuEnabled.get).thenReturn(trueConfig())
			when(stub.iclDblEnabled.get).thenReturn(trueConfig())
			when(stub.loansCreditCardEnabled.get).thenReturn(trueConfig())
			when(stub.loansCreditCardMayaBlackEnabled.get).thenReturn(trueConfig())
			when(stub.iclDeviceScoringDataReferenceIdEnabled.get).thenReturn(trueConfig())
			when(stub.mayaCreditIosDeviceScoringEnabled.get).thenReturn(trueConfig())
			when(stub.loansICLDeviceScoringEnabled.get).thenReturn(trueConfig())
			when(stub.ccDeviceScoringEnabled.get).thenReturn(trueConfig())
			when(stub.bnplDeepFreeze.get).thenReturn(trueConfig())
			when(stub.showOtherIDsEnabled.get).thenReturn(trueConfig())
			when(stub.reKYCEnabled.get).thenReturn(trueConfig())
			when(stub.reKYC10MinutesTestEnabled.get).thenReturn(trueConfig())
			when(stub.shortenedFlowUpgradeEnabled.get).thenReturn(trueConfig())
			when(stub.shortenedFlowReKYCEnabled.get).thenReturn(trueConfig())
			when(stub.shortenedFlowShowOtherIdEnabled.get).thenReturn(trueConfig())
			when(stub.shortenedFlowIdCaptureV2Enabled.get).thenReturn(trueConfig())
			when(stub.backIDCaptureEnabled.get).thenReturn(trueConfig())
			when(stub.updateProfileDeeplinkEnabled.get).thenReturn(trueConfig())
			when(stub.secondaryIDFlowEnabled.get).thenReturn(trueConfig())
			when(stub.expandedReKYCEnabled.get).thenReturn(trueConfig())
			when(stub.idBlurDetectionEnabled.get).thenReturn(trueConfig())
			when(stub.skipZolozFlowEnabled.get).thenReturn(trueConfig())
			when(stub.showIDValScoreEnabled.get).thenReturn(trueConfig())
			when(stub.showIDQualityScoreEnabled.get).thenReturn(trueConfig())
			when(stub.kycPersonalInfoV2Enabled.get).thenReturn(trueConfig())
			when(stub.shortenedFlowAddresssDropdownEnabled.get).thenReturn(trueConfig())
			when(stub.shortenedFlowIdCaptureV3Enabled.get).thenReturn(trueConfig())
			when(stub.onboardingAndKYCFlowV2Enabled.get).thenReturn(trueConfig())
			when(stub.onboardingAndKYCFlowEnabled.get).thenReturn(trueConfig())
			when(stub.shortenedFlowForeignAddressComplianceEnabled.get).thenReturn(trueConfig())
			when(stub.dosriEnabled.get).thenReturn(trueConfig())
			when(stub.fatcaEnabled.get).thenReturn(trueConfig())
			when(stub.philsysEnabled.get).thenReturn(trueConfig())
			when(stub.forcePHNationalIDEnabled.get).thenReturn(trueConfig())
			when(stub.kycIDImageQualityEnabled.get).thenReturn(trueConfig())
			when(stub.onboardingRoutingEnabled.get).thenReturn(trueConfig())
			when(stub.kycMaintenanceEnabled.get).thenReturn(trueConfig())
			when(stub.registrationMiddleNameEnabled.get).thenReturn(trueConfig())
			when(stub.consentCheckAllEnabled.get).thenReturn(trueConfig())
			when(stub.autoProvisioningEnabled.get).thenReturn(trueConfig())
			when(stub.mfaFrameworkV1Enabled.get).thenReturn(trueConfig())
			when(stub.mfaFaceAuthFormEnabled.get).thenReturn(trueConfig())
			when(stub.forgotPasswordV2Enabled.get).thenReturn(trueConfig())
			when(stub.mockedPreSubmissionDataEnabled.get).thenReturn(trueConfig())
			when(stub.savingsEnabled.get).thenReturn(trueConfig())
			when(stub.restrictedLoginFlowEnabled.get).thenReturn(trueConfig())
			when(stub.customerRestrictionsEnabled.get).thenReturn(trueConfig())
			when(stub.kycCompletedCallbackEnabled.get).thenReturn(trueConfig())
			when(stub.registrationRestrictionsEnabled.get).thenReturn(trueConfig())
			when(stub.eddRestrictionsEnabled.get).thenReturn(trueConfig())
			when(stub.mayaSetUsernameEnabled.get).thenReturn(trueConfig())
			when(stub.mayaSavingsButtonEnabled.get).thenReturn(trueConfig())
			when(stub.mayaWalletSettingsButtonEnabled.get).thenReturn(trueConfig())
			when(stub.mayaTransactionLimitWalletSettingsOptionEnabled.get).thenReturn(trueConfig())
			when(stub.mayaAutoCashinWalletSettingsOptionEnabled.get).thenReturn(trueConfig())
			when(stub.mayaInstaFillWalletSettingsOptionEnabled.get).thenReturn(trueConfig())
			when(stub.mayaInboxFlutterEnabled.get).thenReturn(trueConfig())
			when(stub.mayaInboxManageNotificationsEnabled.get).thenReturn(trueConfig())
			when(stub.inboxSupportEnabled.get).thenReturn(trueConfig())
			when(stub.mayaQRPHP2PAdjustmentsEnabled.get).thenReturn(trueConfig())
			when(stub.pushApprovalEnabled.get).thenReturn(trueConfig())
			when(stub.registrationMGMEnabled.get).thenReturn(trueConfig())
			when(stub.fundsEnabled.get).thenReturn(trueConfig())
			when(stub.qrWithCheckoutFlowEnabled.get).thenReturn(trueConfig())
			when(stub.mayaCreditPrePaymentBannerEnabled.get).thenReturn(trueConfig())
			when(stub.mayaCreditPrePaymentSoonEnabled.get).thenReturn(trueConfig())
			when(stub.mayaPayBillsPrePaymentBannerNewTagEnabled.get).thenReturn(trueConfig())
			when(stub.billsPayV3EndpointEnabled.get).thenReturn(trueConfig())
			when(stub.billerReminderEnabled.get).thenReturn(trueConfig())
			when(stub.cashInBankPullV3Enabled.get).thenReturn(trueConfig())
			when(stub.bdoCashInEnabled.get).thenReturn(trueConfig())
			when(stub.mayaShopProviderSubcatPillsEnabled.get).thenReturn(trueConfig())
			when(stub.virtualNumberRegistrationEnabled.get).thenReturn(trueConfig())
			when(stub.locationBasedVirtualNumberRegistrationEnabled.get).thenReturn(trueConfig())
			when(stub.inAppReviewEnabled.get).thenReturn(trueConfig())
			when(stub.inAppReviewSendMoneyEnabled.get).thenReturn(trueConfig())
			when(stub.fundsCreatorStoreEnabled.get).thenReturn(trueConfig())
			when(stub.loadV2Enabled.get).thenReturn(trueConfig())
			when(stub.loadV2FavoritesEnabled.get).thenReturn(trueConfig())
			when(stub.loadV2RecommenderEnabled.get).thenReturn(trueConfig())
			when(stub.requestMoneyTileUpdatesEnabled.get).thenReturn(trueConfig())
			when(stub.cardPullV2Enabled.get).thenReturn(trueConfig())
			when(stub.bannerCorrelationHeaderEnabled.get).thenReturn(trueConfig())
			when(stub.creditCardCashbackTileEnabled.get).thenReturn(trueConfig())
			when(stub.voucherDetailsV2Enabled.get).thenReturn(trueConfig())
			when(stub.ecommAppEventV2QrEnabled.get).thenReturn(trueConfig())
			when(stub.ecommAppEventV2ShopEnabled.get).thenReturn(trueConfig())
			when(stub.ecommAppEventV2BillsPayEnabled.get).thenReturn(trueConfig())
			when(stub.appEventV2BankTransferEnabled.get).thenReturn(trueConfig())
			when(stub.appEventV2SendMoneyEnabled.get).thenReturn(trueConfig())
			when(stub.loyaltyForceUpdateEnabled.get).thenReturn(trueConfig())
			when(stub.freelancerHubEnabled.get).thenReturn(trueConfig())
			when(stub.appEventV2CashInEnabled.get).thenReturn(trueConfig())
			when(stub.landersCreditCardEnabled.get).thenReturn(trueConfig())
			when(stub.cardDetailsProtectionEnabled.get).thenReturn(trueConfig())
		}
	}
}
