// This is only generated. Do not edit.

import Cuckoo

import ConfigurationService


extension ViewModelTest {
	func stubConfigurationServiceV2() {
		stub(configurationServiceV2) { stub in
			when(stub.actionCardsEnabled.get).thenReturn(trueConfigV2())
			when(stub.bannerCorrelationHeaderEnabled.get).thenReturn(trueConfigV2())
			when(stub.changePasswordSwiftUIEnabled.get).thenReturn(trueConfigV2())
			when(stub.creatorStoreBannersEnabled.get).thenReturn(trueConfigV2())
			when(stub.creatorStoreInterstitialBannersEnabled.get).thenReturn(trueConfigV2())
			when(stub.dcrnSessionHeaderEnabled.get).thenReturn(trueConfigV2())
			when(stub.dcrnVerifyHeaderEnabled.get).thenReturn(trueConfigV2())
			when(stub.ddpLauncherEnabled.get).thenReturn(trueConfigV2())
			when(stub.deviceManagementEnabled.get).thenReturn(trueConfigV2())
			when(stub.easyCreditBannerEnabled.get).thenReturn(trueConfigV2())
			when(stub.externalLinkPromptEnabled.get).thenReturn(trueConfigV2())
			when(stub.maintenanceBypassEnabled.get).thenReturn(trueConfigV2())
			when(stub.profileCleverTapUnliBannersEnabled.get).thenReturn(trueConfigV2())
			when(stub.rateThisAppButtonEnabled.get).thenReturn(trueConfigV2())
			when(stub.servicesChatWithMayaEnabled.get).thenReturn(trueConfigV2())
			when(stub.servicesClevertapUnliBannerEnabled.get).thenReturn(trueConfigV2())
			when(stub.dashboardNavigationRefactorEnabled.get).thenReturn(trueConfigV2())
			when(stub.cleverTapUnliBannersEnabled.get).thenReturn(trueConfigV2())
			when(stub.cryptoV2Enabled.get).thenReturn(trueConfigV2())
			when(stub.ccCreditScoringEnabled.get).thenReturn(trueConfigV2())
			when(stub.ccSkipCreditScoringEnabled.get).thenReturn(trueConfigV2())
			when(stub.ccDBLEnabled.get).thenReturn(trueConfigV2())
			when(stub.iclDeviceScoringEnabled.get).thenReturn(trueConfigV2())
			when(stub.iclMfaEnabled.get).thenReturn(trueConfigV2())
			when(stub.iclGeolocationEnabled.get).thenReturn(trueConfigV2())
			when(stub.lendingDataExtractionEnabled.get).thenReturn(trueConfigV2())
			when(stub.mecAPIGatewayEnabled.get).thenReturn(trueConfigV2())
			when(stub.mecBannerV2Enabled.get).thenReturn(trueConfigV2())
			when(stub.mecDeviceScoringEnabled.get).thenReturn(trueConfigV2())
			when(stub.mecGeolocationEnabled.get).thenReturn(trueConfigV2())
			when(stub.mecMfaEnabled.get).thenReturn(trueConfigV2())
			when(stub.mecWriteOffEnabled.get).thenReturn(trueConfigV2())
			when(stub.pfMvpEnabled.get).thenReturn(trueConfigV2())
			when(stub.mayaBlackCreditCardEnabled.get).thenReturn(trueConfigV2())
			when(stub.mayaBlackIntroEnabled.get).thenReturn(trueConfigV2())
			when(stub.convertToInstallmentEnabled.get).thenReturn(trueConfigV2())
			when(stub.landersCreditCardEnabled.get).thenReturn(trueConfigV2())
			when(stub.cardDetailsProtectionEnabled.get).thenReturn(trueConfigV2())
			when(stub.securityCenterEnabled.get).thenReturn(trueConfigV2())
			when(stub.accountFreezeStatusEnabled.get).thenReturn(trueConfigV2())
			when(stub.shortenedRegistrationEnabled.get).thenReturn(trueConfigV2())
			when(stub.landingPageV2Enabled.get).thenReturn(trueConfigV2())
			when(stub.changeWelcomeBackButtonEnabled.get).thenReturn(trueConfigV2())
			when(stub.showWelcomeDoItLaterButtonEnabled.get).thenReturn(trueConfigV2())
			when(stub.kycTofuUXRedesignEnabled.get).thenReturn(trueConfigV2())
			when(stub.kycCMSv8MockEnabled.get).thenReturn(trueConfigV2())
			when(stub.regToKYCFlowEnabled.get).thenReturn(trueConfigV2())
			when(stub.billsCategoryAdBannerEnabled.get).thenReturn(trueConfigV2())
			when(stub.autoDebitArrangementSdkEnabled.get).thenReturn(trueConfigV2())
			when(stub.loadMinDetectionEnabled.get).thenReturn(trueConfigV2())
			when(stub.interstitialManualConfigServiceEnabled.get).thenReturn(trueConfigV2())
			when(stub.blackpinkGiveawayDeeplinkEnabled.get).thenReturn(trueConfigV2())
			when(stub.mgmV2ForceUpdateEnabled.get).thenReturn(trueConfigV2())
			when(stub.mgmV2Enabled.get).thenReturn(trueConfigV2())
			when(stub.inboxTicketingEnabled.get).thenReturn(trueConfigV2())
			when(stub.sendbirdCachingCollectionEnabled.get).thenReturn(trueConfigV2())
		}
	}
}
