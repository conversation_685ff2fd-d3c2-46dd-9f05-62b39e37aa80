$(PROJECT_DIR)/$(PROJECT_NAME)/Helpers/Inbox/InboxManager.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Helpers/Inbox/Protocols/InboxProviderProtocol.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Helpers/TouchIDManager.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Helpers/ContactsImporter.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Helpers/Fingerprinting/Protocol/KountDataCollectorProtocol.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Helpers/QR/Protocols/QRGeneratorUtilityProtocol.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Helpers/QR/Protocols/QRParserUtilityProtocol.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Helpers/QR/Protocols/QRValidationUtilityProtocol.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Helpers/EKYC/Protocols/DataLoaderProtocol.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Helpers/Fingerprinting/Protocol/ShieldDataCollectorProtocol.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Services/SignoutService.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Services/ToggleBalanceShownService.swift
$(PROJECT_DIR)/ConfigurationService/Sources/ConfigurationService/ConfigurationService.swift
$(PROJECT_DIR)/ConfigurationService/Sources/ConfigurationService/ConfigurationProvider/LocalConfigurationProvider.swift
$(PROJECT_DIR)/ConfigurationService/Sources/ConfigurationService/ConfigurationProvider/SplitConfigurationProvider.swift
$(PROJECT_DIR)/ConfigurationService/Sources/ConfigurationService/ConfigurationServiceV2.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Services/NotificationService.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Services/AppThemeService.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Services/Protocols/AdBannerServiceProtocol.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Services/Protocols/PerformanceMonitoringServiceProtocol.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Services/ResumeSessionService.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Services/BackgroundSessionManager.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Services/FlutterEngineManager.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Services/FlutterSessionManager.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Services/Protocols/LocationServiceProtocol.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Services/Protocols/LocationAuthorizerServiceProtocol.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Services/Protocols/LocationCheckerServiceProtocol.swift
$(PROJECT_DIR)/APIProvider/Sources/APIProviderTest/TestMoyaAPIProvider.swift
$(PROJECT_DIR)/APIProvider/Sources/APIProviderTest/TestMoyaAPIProvider+Reactive.swift
$(PROJECT_DIR)/StoreProvider/Sources/StoreProvider/StoreProvider.swift
$(PROJECT_DIR)/StoreProvider/Sources/StoreProvider/Stores/FileStore.swift
$(PROJECT_DIR)/StoreProvider/Sources/StoreProvider/Stores/PropertyListStore.swift
$(PROJECT_DIR)/StoreProvider/Sources/StoreProvider/Stores/UserDefaultsStore.swift
$(PROJECT_DIR)/StoreProvider/Sources/StoreProvider/Stores/DatabaseStore.swift
$(PROJECT_DIR)/StoreProvider/Sources/StoreProvider/Stores/EncryptedDefaultsStore.swift
$(PROJECT_DIR)/ConsentOptOutService/Sources/ConsentOptOutService/ConsentOptOutService.swift
$(PROJECT_DIR)/MayaCoreData/Sources/MayaCoreData/MayaCoreData+DataStack.swift
$(PROJECT_DIR)/CoreDataStack/Sources/CoreDataStack/CoreDataStack.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Stores/Migrators/DatabaseStoreMigrator.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Stores/Migrators/UserDefaultsStoreMigrator.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Stores/Migrators/EncryptedDefaultsStoreMigrator.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Stores/Migrators/FileStoreMigrator.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Stores/Migrators/PropertyListStoreMigrator.swift
$(PROJECT_DIR)/MayaFormatterManager/Sources/MayaFormatterManager/MayaFormatterManager.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Coordinators/Common/DeepLinkHandler.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Coordinators/Common/PaymayaDeepLinkRouter.swift
$(PROJECT_DIR)/Analytics/Sources/Analytics/AnalyticsService.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Helpers/Vouchers/VoucherLocalDataSourceProtocol.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Helpers/RavenWrapper.swift
$(PROJECT_DIR)/$(PROJECT_NAME)/Helpers/RavenActionProvider.swift
