.gitlab_project_variables: &gitlab_project_variables
  GITLAB_BOT_EMAIL: <EMAIL>
  GITLAB_BOT_NAME: issuing-base-app-bot
  GITLAB_TOKEN: $ISSUING_BASEAPP_BOT_TOKEN

.sast_runner: &sast_runner
  tags:
    - build

.sca_variables: &sca_variables
  SCA_MONITOR_OPTIONS: -d --all-projects --exclude=.out,.git,.gitmodules,.gitignore,.DS_Store,derived_data,.swiftpm,.build,APIProvider,ConfigurationService,ConsentOptOutService,CoreDataStack,Logger,MayaCoreData,StoreProvider
  SCA_TEST_OPTIONS: -d --all-projects --exclude=.out,.git,.gitmodules,.gitignore,.DS_Store,derived_data,.swiftpm,.build,APIProvider,ConfigurationService,ConsentOptOutService,CoreDataStack,<PERSON><PERSON>,MayaCoreData,StoreProvider --json-file-output=snyk_sca_results.json

.sca_runner: &sca_runner
  tags:
    - build
  image:
    name: registry.gitlab.corp.paymaya.com/isse/service-engineering/containers/snyk-container/ios-snyk:1.9.0

.mast_runner: &mast_runner
  tags:
    - build

variables:
  # AppDome
  APPDOME_API_KEY: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiM2M0MTZjYjAtZjlhNy0xMWVmLTkxZjEtNTUzN2JmNDg0NWI3Iiwic2FsdCI6Ijc0ODM3NDYxLTFiNDgtNDljMC04NjYyLWJiMTE5ZWRmMTdkMyJ9.efx8y7yZpY44Fl0cLnwiGTBLe29bB24rhqmUTudI9Rg"
  APPDOME_NON_PROD_TEAM_ID: "fccb4fb0-6802-11ee-af10-014570f1acc5"
  APPDOME_PROD_TEAM_ID: "6bb80c80-6e5a-11ee-8a6a-35f6d845273a"
  # APPDOME_NON_PROD_FS_ID: "b344af20-c4e2-11ee-aec7-81d6d6255429" # rules enabled
  APPDOME_NON_PROD_FS_ID: "25e29350-313c-11f0-9b0d-bd50aad720b3" # monitoring only
  APPDOME_PROD_FS_ID: "86b9aef0-3161-11f0-8a4b-3faa8e18b927" 
  APPDOME_SCRIPT: "Scripts/get_appdome_file_names.sh"

  # SonarQube (SAST)
  SAST_JAVA_HOME: /usr/lib/jvm/java-17-openjdk
  SAST_JAVA_VERSION: 17

  # Snyk (SCA)
  SCA_JAVA_HOME: /usr/lib/jvm/java-17-openjdk
  SCA_JAVA_VERSION: 17

  # iOS
  XCODE_PATH: "/Applications/Xcode_16.2.app"

  GOOGLE_FLUTTER_VERSION:
    value: ""
    description: |
                 Specify version of Google Flutter SDK to build (e.g. 2.5.300).
                 Leave blank if you want to follow version declared in Podfile.
                 Applicable only to test and staging environments, this is ignored in production and sandbox.
  PAYMAYA_FLUTTER_SDK_VERSION:
    value: ""
    description: |
                 Specify version of PayMaya Flutter SDK to build (e.g. 0.0.400).
                 Input '> 0.0.0' to pull the latest version or input exact version to be descriptive in the release notes.
                 Leave blank if you want to follow version declared in Podfile.
                 Applicable only to test and staging environments, this is ignored in in production and sandbox.
  IS_RC_BUILD:
    value: "false"
    options:
      - "true"
      - "false"
    description: |
                 Choose 'true' if this build will be used as a release candidate build.
                 This doesn't do anything aside from creating the build (for now), but it will eventually be used to run AppDome and PRFC jobs.

  # speed up clone and submodule
  GIT_DEPTH: 10
  GIT_SUBMODULE_DEPTH: 1

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      variables:
        PROJECT_PIPELINE_NAME: "MR Verifier"
    - if: $CI_PIPELINE_SOURCE == "schedule"
      variables:
        PROJECT_PIPELINE_NAME: 'Nightly Unit Test'
    - if: $CI_PIPELINE_SOURCE == "web" && $IS_RC_BUILD == "false" && $IS_FOR_SCANS == "false"
      variables:
        PROJECT_PIPELINE_NAME: "${DEPLOY_ENVIRONMENT} Job"
    - if: $CI_PIPELINE_SOURCE == "web" && $IS_RC_BUILD == "true"
      variables:
        PROJECT_PIPELINE_NAME: "${DEPLOY_ENVIRONMENT} RC Job"
    - if: $CI_PIPELINE_SOURCE == "web" && $IS_FOR_SCANS == "true"
      variables:
        PROJECT_PIPELINE_NAME: "${DEPLOY_ENVIRONMENT} Scan Job"

.appdome_non_prod_variables: &appdome_non_prod_variables
  APPDOME_TEAM_ID: $APPDOME_NON_PROD_TEAM_ID
  APPDOME_IOS_FS_ID: $APPDOME_NON_PROD_FS_ID

.appdome_prod_variables: &appdome_prod_variables
  APPDOME_TEAM_ID: $APPDOME_PROD_TEAM_ID
  APPDOME_IOS_FS_ID: $APPDOME_PROD_FS_ID

.test_appdome_rules: &test_appdome_rules
  rules:
    - if: $DEPLOY_ENVIRONMENT == "Test" && $IS_APPDOME_ENABLED == "true"
      variables: *appdome_non_prod_variables

.staging_appdome_rules: &staging_appdome_rules
  rules:
    - if: $DEPLOY_ENVIRONMENT == "Staging" && $IS_FOR_SCANS == "false" && $IS_APPDOME_ENABLED == "true" # Non-RC build
      variables: *appdome_non_prod_variables
    - if: $DEPLOY_ENVIRONMENT == "Staging" && $IS_RC_BUILD == "true" && $IS_FOR_SCANS == "true" && $IS_APPDOME_ENABLED == "true" # RC build
      variables: *appdome_non_prod_variables

.production_adhoc_appdome_rules: &production_adhoc_appdome_rules
  rules:
    - if: $DEPLOY_ENVIRONMENT == "Production Adhoc" && $IS_APPDOME_ENABLED == "true"
      variables: *appdome_prod_variables

.production_store_appdome_rules: &production_store_appdome_rules
  rules:
    - if: $DEPLOY_ENVIRONMENT == "Production Store" && $IS_APPDOME_ENABLED == "true"
      variables: *appdome_prod_variables

.sandbox_appdome_rules: &sandbox_appdome_rules
  rules:
    - if: $DEPLOY_ENVIRONMENT == "Sandbox" && $IS_APPDOME_ENABLED == "true"
      variables: *appdome_prod_variables

.appdome-generate:lane:
  variables:
    <<: *gitlab_project_variables
    APP_LOCATION: ".out/*.ipa"
    # BUILD_WITH_LOGS: "True"
  script:
    - chmod +x $APPDOME_SCRIPT
    - source $APPDOME_SCRIPT
    - appdome_ios_auto_dev_signing
  artifacts:
    paths:
      - "appdome_outputs/" # Path generated by AppDome, cannot be changed
    expire_in: 3 mos
  image: ${CI_DEPENDENCY_PROXY_DIRECT_GROUP_IMAGE_PREFIX}/appdome/gitlab_build-2secure
  tags:
    - build

.appdome-upload:lane:
  extends: .base-upload:lane
  variables:
    <<: *gitlab_project_variables
    GIT_STRATEGY: fetch
    FASTLANE_LANE_CLI_OPTS: "upload_ipa"
  script:
    - chmod +x $CI_PROJECT_DIR/appdome_outputs/app.sh
    - $CI_PROJECT_DIR/appdome_outputs/app.sh --signer "${CERT_FINGERPRINT}" --output "${APPDOME_IPA_PATH}"
    - ${BUNDLE_EXEC_COMMAND} ${DEFAULT_FASTLANE_COMMAND} ${FASTLANE_LANE_CLI_OPTS} environment:"${DEPLOY_ENVIRONMENT}" mesh_team:"${MESH_TEAM}" release_notes:"${RELEASE_NOTES}" branch:"${CI_COMMIT_BRANCH}" patch_type:"${BUMP_TYPE}"
  artifacts:
    paths:
      - "appdome_outputs/*.ipa"
    expire_in: 3 mos

.ipa-upload:lane:
  variables:
    <<: *gitlab_project_variables

unit-test:
  variables:
    <<: *gitlab_project_variables

verify-build:
  variables:
    <<: *gitlab_project_variables

sca:
  variables:
    <<: *gitlab_project_variables
    <<: *sca_variables
  <<: *sca_runner

sast-build:
  variables:
    <<: *gitlab_project_variables

sast:
  variables:
    <<: *gitlab_project_variables
    SONAR_PROJECT_KEY: issuing_consumer-app-and-features_mobile_paymaya-app-ios_AYhrnjY8ZK1VkovIgKsr
  <<: *sast_runner

test:lane:
  variables:
    <<: *gitlab_project_variables
    FLUTTER_VERSION: $GOOGLE_FLUTTER_VERSION
    FLUTTER_DEV_VERSION: $PAYMAYA_FLUTTER_SDK_VERSION

test:appdome-generate:lane:
  stage: post:test
  extends: .appdome-generate:lane
  needs: ['test:lane']
  <<: *test_appdome_rules

test:appdome-upload:lane:
  stage: post:test
  extends: .appdome-upload:lane
  variables:
    APPDOME_IPA_PATH: "appdome_outputs/PayMaya-Test.ipa"
  needs: ['test:appdome-generate:lane']
  <<: *test_appdome_rules

staging:lane:
  variables:
    <<: *gitlab_project_variables
    FLUTTER_VERSION: $GOOGLE_FLUTTER_VERSION
    FLUTTER_STAGE_VERSION: $PAYMAYA_FLUTTER_SDK_VERSION

staging:appdome-generate:lane:
  stage: post:staging
  extends: .appdome-generate:lane
  needs: ['staging:lane']
  <<: *staging_appdome_rules

staging:appdome-upload:lane:
  stage: post:staging
  extends: .appdome-upload:lane
  variables:
    APPDOME_IPA_PATH: "appdome_outputs/PayMaya-Staging.ipa"
  needs: ['staging:appdome-generate:lane']
  <<: *staging_appdome_rules

staging:sast-build:
  variables:
    <<: *gitlab_project_variables

staging:sast:
  variables:
    <<: *gitlab_project_variables
  <<: *sast_runner

staging:sca:
  variables:
    <<: *gitlab_project_variables
    <<: *sca_variables
  <<: *sca_runner

staging:mast:
  variables:
    <<: *gitlab_project_variables
  <<: *mast_runner

production:store:lane:
  variables:
    <<: *gitlab_project_variables

production:store:appdome-generate:lane:
  stage: post:production
  extends: .appdome-generate:lane
  needs: ['production:store:lane']
  <<: *production_store_appdome_rules

production:store:appdome-upload:lane:
  stage: post:production
  extends: .appdome-upload:lane
  variables:
    APPDOME_IPA_PATH: "appdome_outputs/PayMaya.ipa"
  needs: ['production:store:appdome-generate:lane']
  <<: *production_store_appdome_rules

production:adhoc:lane:
  variables:
    <<: *gitlab_project_variables

production:adhoc:appdome-generate:lane:
  stage: post:production
  extends: .appdome-generate:lane
  needs: ['production:adhoc:lane']
  <<: *production_adhoc_appdome_rules

production:adhoc:appdome-upload:lane:
  stage: post:production
  extends: .appdome-upload:lane
  variables:
    APPDOME_IPA_PATH: "appdome_outputs/PayMaya.ipa"

  needs: ['production:adhoc:appdome-generate:lane']
  <<: *production_adhoc_appdome_rules

sandbox:lane:
  variables:
    <<: *gitlab_project_variables

sandbox:appdome-generate:lane:
  stage: post:sandbox
  extends: .appdome-generate:lane
  needs: ['sandbox:lane']
  <<: *sandbox_appdome_rules

sandbox:appdome-upload:lane:
  stage: post:sandbox
  extends: .appdome-upload:lane
  variables:
    APPDOME_IPA_PATH: "appdome_outputs/PayMaya-Sandbox.ipa"
  needs: ['sandbox:appdome-generate:lane']
  <<: *sandbox_appdome_rules