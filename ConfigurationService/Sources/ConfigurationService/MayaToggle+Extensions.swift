//
//  MayaToggle+Extensions.swift
//
//
//  Created by <PERSON> on 7/2/24.
//

import Foundation

extension MayaToggle {
    /*
     For multiple cases, please group them into five per line
     (even if they still return the same value).
     e.g.
        // Base App
        case .toggleOne, .toggleTwo, .toggleThree, .toggleFour, .toggleFive: .foo
        case .toggleSix, .toggleSeven, .toggleEight, .toggleNine, .toggleTen: .foo
        case .toggleEleven, .toggleTwelve: .bar
    */
    var trafficType: MayaToggle.TrafficType {
        switch self {
        // Base App
        case .actionCards, .changePasswordSwiftUI, .creatorStoreBanners, .dashboardNavigationRefactor, .deviceManagement: .user
        case .easyCreditBanner, .externalLinkPrompt, .rateThisAppButton, .servicesChatWithMaya, .walletRewardsReorder: .user
        case .bannerCorrelationHeader, .dcrnSessionHeader, .dcrnVerifyHeader, .ddpLauncher, .maintenanceBypass: .anonymous
        case .splitBlockingTimeout: .anonymous
        case .clevertapUnliBanners, .creatorStoreInterstitialBanners, .servicesClevertapUnliBanners, .profileClevertapUnliBanners:  .user
        // Invest
        case .cryptoV2: .user
        // Lending
        case .ccCreditScoring: .user
        case .ccDBL: .user
        case .ccSkipCreditScoring: .user
        case .iclDeviceScoring: .user
        case .iclGeolocation: .user
        case .iclMfa: .user
        case .lendingDataExtraction: .user
        case .mecAPIGateway: .user
        case .mecBannerV2: .user
        case .mecDeviceScoring: .user
        case .mecGeolocation: .user
        case .mecMfa: .user
        case .mecWriteOff: .user
        case .pfMvp: .user
        // CMS LYNX
        case .mayaBlackCreditCard, .mayaBlackIntro, .convertToInstallment, .landersCreditCard, .cardDetailsProtection: .user
        // Mewtwo
        case .securityCenter, .accountFreezeStatus: .user
        // Onyx
        case .registerNowLabel, .shortenedRegistration, .landingPageV2: .anonymous
        // Helix
        case .welcomeButtonLabel, .showWelcomeDoItLaterButton, .changeWelcomeBackButton, .kycTofuUXRedesign, .mockCMSV8: .user
        case .upgradeNowLabel, .regToKYCFlow: .anonymous
        // Anbu
        case .billsCategoryAdBanner: .user
        case .autoDebitArrangementSdk: .user
        case .loadMinDetection: .user
        // Rubeus
        case .interstitialManualConfigService: .user
        case .blackpinkGiveawayDeepLinkGuard: .user
        case .mgmV2ForceUpdate: .user
        case .mgmV2: .user
        // Inbox
        case .inboxTicketing: .user
        case .sendBirdCachingCollection: .user
        }
    }

    var dataType: MayaToggle.DataType {
        switch self {
        // Base App
        case .actionCards, .bannerCorrelationHeader, .changePasswordSwiftUI, .creatorStoreBanners, .dashboardNavigationRefactor: .bool
        case .dcrnSessionHeader, .dcrnVerifyHeader, .ddpLauncher, .deviceManagement, .easyCreditBanner: .bool
        case .externalLinkPrompt, .rateThisAppButton, .servicesChatWithMaya, .maintenanceBypass, .clevertapUnliBanners: .bool
        case .creatorStoreInterstitialBanners, .servicesClevertapUnliBanners, .profileClevertapUnliBanners: .bool
        case .splitBlockingTimeout: .integer
        case .walletRewardsReorder: .string
        // Invest
        case .cryptoV2: .bool
        // Lending
        case .ccCreditScoring: .bool
        case .ccDBL: .bool
        case .ccSkipCreditScoring: .bool
        case .iclDeviceScoring: .bool
        case .iclGeolocation: .bool
        case .iclMfa: .bool
        case .lendingDataExtraction: .bool
        case .mecAPIGateway: .bool
        case .mecBannerV2: .bool
        case .mecDeviceScoring: .bool
        case .mecGeolocation: .bool
        case .mecMfa: .bool
        case .mecWriteOff: .bool
        case .pfMvp: .bool
        // CMS LYNX
        case .mayaBlackCreditCard, .mayaBlackIntro, .convertToInstallment, .landersCreditCard, .cardDetailsProtection: .bool
        // Mewtwo
        case .securityCenter, .accountFreezeStatus: .bool
        // Onyx
        case .registerNowLabel, .shortenedRegistration, .landingPageV2: .bool
        // Helix
        case .showWelcomeDoItLaterButton, .changeWelcomeBackButton, .kycTofuUXRedesign, .mockCMSV8, .regToKYCFlow: .bool
        case .upgradeNowLabel, .welcomeButtonLabel: .string
        // Anbu
        case .billsCategoryAdBanner: .bool
        case .autoDebitArrangementSdk: .bool
        case .loadMinDetection: .bool
        // Rubeus
        case .interstitialManualConfigService: .bool
        case .blackpinkGiveawayDeepLinkGuard: .bool
        case .mgmV2ForceUpdate: .bool
        case .mgmV2: .bool
        // Inbox
        case .inboxTicketing, .sendBirdCachingCollection: .bool
        }
    }

    var owner: MayaToggle.Owner {
        switch self {
        // Base App
        case .actionCards, .bannerCorrelationHeader, .changePasswordSwiftUI, .creatorStoreBanners, .dashboardNavigationRefactor: .baseApp
        case .dcrnSessionHeader, .dcrnVerifyHeader, .ddpLauncher, .deviceManagement, .easyCreditBanner: .baseApp
        case .externalLinkPrompt, .rateThisAppButton, .servicesChatWithMaya, .walletRewardsReorder, .maintenanceBypass: .baseApp
        case .clevertapUnliBanners, .creatorStoreInterstitialBanners, .servicesClevertapUnliBanners, .profileClevertapUnliBanners: .baseApp
        case .splitBlockingTimeout: .baseApp
        // Invest
        case .cryptoV2: .invest
        // Lending
        case .ccCreditScoring: .lending
        case .ccDBL: .lending
        case .ccSkipCreditScoring: .lending
        case .iclDeviceScoring: .lending
        case .iclGeolocation: .lending
        case .iclMfa: .lending
        case .lendingDataExtraction: .lending
        case .mecAPIGateway: .lending
        case .mecBannerV2: .lending
        case .mecDeviceScoring: .lending
        case .mecGeolocation: .lending
        case .mecMfa: .lending
        case .mecWriteOff: .lending
        case .pfMvp: .lending
        // CMS LYNX
        case .mayaBlackCreditCard, .mayaBlackIntro, .convertToInstallment, .landersCreditCard, .cardDetailsProtection: .cmsLynx
        // Mewtwo
        case .securityCenter, .accountFreezeStatus: .mewtwo
        case .registerNowLabel, .shortenedRegistration, .landingPageV2: .onyx
        // Helix
        case .upgradeNowLabel, .welcomeButtonLabel, .showWelcomeDoItLaterButton, .changeWelcomeBackButton, .kycTofuUXRedesign: .helix
        case .mockCMSV8, .regToKYCFlow: .helix
        // Anbu
        case .billsCategoryAdBanner: .anbu
        case .autoDebitArrangementSdk: .anbu
        case .loadMinDetection: .anbu
        // Rubeus
        case .interstitialManualConfigService: .rubeus
        case .blackpinkGiveawayDeepLinkGuard: .rubeus
        case .mgmV2ForceUpdate: .rubeus
        case .mgmV2: .rubeus
        // Inbox
        case .inboxTicketing, .sendBirdCachingCollection: .inbox
        }
    }

    var title: String {
        switch self {
        // Base App
        case .actionCards: "Action Cards"
        case .bannerCorrelationHeader: "Banner Correlation Header"
        case .changePasswordSwiftUI: "Change Password SwiftUI"
        case .creatorStoreBanners: "Creator Store Banners"
        case .creatorStoreInterstitialBanners: "Creator Store Interstitial Banners"
        case .dcrnSessionHeader: "DCRN Session Header"
        case .dcrnVerifyHeader: "DCRN Verify Header"
        case .ddpLauncher: "DDP Launcher"
        case .deviceManagement: "Device Management"
        case .easyCreditBanner: "Easy Credit Banner"
        case .externalLinkPrompt: "External Link Prompt"
        case .maintenanceBypass: "Maintenance Bypass"
        case .rateThisAppButton: "Rate this App Button"
        case .servicesChatWithMaya: "Services Chat with Maya"
        case .servicesClevertapUnliBanners: "Services Clevertap Unli Banners"
        case .dashboardNavigationRefactor: "Dashboard Navigation Refactor"
        case .walletRewardsReorder: "Wallet Rewards Dashboard Reorder"
        case .splitBlockingTimeout: "Split Blocking Timeout"
        // Invest
        case .cryptoV2: "Crypto V2"
        // Lending
        case .ccCreditScoring: "Credit Scoring for Credit Card"
        case .ccSkipCreditScoring: "Skip Credit Scoring for Credit Card"
        case .ccDBL: "Document Based Lending for Credit Card"
        case .iclDeviceScoring: "ICL Device Scoring"
        case .iclMfa: "ICL MFA"
        case .iclGeolocation: "ICL Geolocation"
        case .lendingDataExtraction: "Lending Device Data Extraction"
        case .mecAPIGateway: "MEC API Gateway"
        case .mecBannerV2: "MEC on Wallet with Rejected"
        case .mecDeviceScoring: "MEC Device Scoring"
        case .mecGeolocation: "MEC Geolocation"
        case .mecMfa: "MEC Transfer to Wallet MFA"
        case .mecWriteOff: "MEC Write Off"
        case .pfMvp: "Purchase Financing"
        // CMS LYNX
        case .mayaBlackCreditCard: "Maya Black Credit Card"
        case .mayaBlackIntro: "Maya Black Intro"
        case .convertToInstallment: "Convert to Installment"
        case .landersCreditCard: "Lander Credit Card"
        case .cardDetailsProtection: "Card Details Protection"
        // Mewtwo
        case .securityCenter: "Security Center"
        case .accountFreezeStatus: "Account Freeze Status"
        // Onyx
        case .registerNowLabel: "Register Now Label"
        case .shortenedRegistration: "Shortened Registration"
        case .landingPageV2: "Landing Page V2"
        // Helix
        case .upgradeNowLabel: "Upgrade now Label"
        case .welcomeButtonLabel: "Welcome button Label"
        case .changeWelcomeBackButton: "Change KYC welcome page back button"
        case .showWelcomeDoItLaterButton: "Show I'll do it Later button KYC welcome page"
        case .kycTofuUXRedesign: "KYC Tofu UX Redesign"
        case .mockCMSV8: "KYC CMS v8 mock"
        case .regToKYCFlow: "Registration to KYC flow"
        // Anbu
        case .billsCategoryAdBanner: "Bills Pay Category Ad Banner"
        case .autoDebitArrangementSdk: "Auto Debit Arrangement SDK"
        case .loadMinDetection: "Load SDK MIN Detection"
        // Rubeus
        case .interstitialManualConfigService: "CleverTap Interstitial Manual Config Service"
        case .blackpinkGiveawayDeepLinkGuard: "Enable Blackpink Giveaway Deep link"
        case .mgmV2ForceUpdate: "Enable MGM 2.0 Force Update"
        case .mgmV2: "MGM 2.0"
        // Inbox
        case .inboxTicketing: "Inbox Ticketing"
        case .sendBirdCachingCollection: "Sendbird Caching Collection"
        // Default
        default: rawValue
        }
    }

    var localKey: String { "\(Constants.togglePrefix)\(rawValue)" }

    var splitKey: String? {
        switch self {
        // Base App
        case .actionCards: "release-action-cards"
        case .bannerCorrelationHeader: "release-banner-correlation-header"
        case .changePasswordSwiftUI: "release-change-password-swiftui"
        case .dcrnSessionHeader: "release-dcrn-session-header"
        case .dcrnVerifyHeader: "release-dcrn-verify-header"
        case .ddpLauncher: "release-ddp-app-launch"
        case .deviceManagement: "release-login-history"
        case .easyCreditBanner: "release-maya-easy-credit-banner"
        case .externalLinkPrompt: "release-external-link-prompt"
        case .maintenanceBypass: "segmentation-app-maintenance-bypass"
        case .rateThisAppButton: "release-rate-this-app"
        case .servicesChatWithMaya: "release-services-chat-maya"
        case .creatorStoreBanners: "release-creator-store-banners"
        case .walletRewardsReorder: MayaExperiment.walletRewardsReorder.splitKey
        case .clevertapUnliBanners: "release-creator-store-banner-v2"
        case .creatorStoreInterstitialBanners: "release-creator-store-interstitial-banner"
        case .servicesClevertapUnliBanners: "release-services-banner"
        // Invest
        case .cryptoV2: "segmentation-crypto-v2"
        // Mewtwo
        case .securityCenter: "release-security-center-v2"
        case .accountFreezeStatus: "permanent-freeze-status-on-dashboard"
        // Lending
        case .ccCreditScoring: "release-cc-credit-scoring"
        case .ccDBL: "release-cc-dbl"
        case .ccSkipCreditScoring: "release-cc-credolab-skip-permission"
        case .iclDeviceScoring: "release-icl-device-scoring"
        case .lendingDataExtraction: "release-lending-data-extraction"
        case .mecAPIGateway: "release-mec-api-gateway"
        case .mecBannerV2: "release-maya-easy-credit-banner-v2"
        case .mecGeolocation: "release-mec-geolocation"
        case .mecDeviceScoring: "release-mec-device-scoring"
        case .mecWriteOff: "release-mec-write-off"
        // CMS LYNX
        case .mayaBlackCreditCard: "release-maya-black-cc"
        case .landersCreditCard: "release-landers-credit-card"
        case .cardDetailsProtection: "release-cards-details-protection"
        // Onyx
        case .registerNowLabel: MayaExperiment.registerNowLabel.splitKey
        case .shortenedRegistration: "release-shortened-registration"
        case .landingPageV2: "release-landing-page-v2"
        // Helix
        case .upgradeNowLabel: "experiment-user-profiles-bottomsheet-button-label-reg"
        case .welcomeButtonLabel: "experiment-user-profiles-welcome-screen-button-label"
        case .changeWelcomeBackButton: "experiment-user-profiles-back-button-icon"
        case .showWelcomeDoItLaterButton: "experiment-user-profiles-show-later-button"
        case .kycTofuUXRedesign: "release-kyc-ux-tofu-redesign"
        case .regToKYCFlow: "release-reg-to-kyc-flow"
        // Rubeus
        case .interstitialManualConfigService: "release-interstitial-flow-guard"
        case .blackpinkGiveawayDeepLinkGuard: "release-giveaway-deeplink"
        case .mgmV2ForceUpdate: "release-referral-force-update"
        // Anbu
        case .billsCategoryAdBanner: "release-bills-category-banner"
        // Inbox
        case .sendBirdCachingCollection: "release-inbox-caching-collection"
        // Default
        default: nil
        }
    }

    /*
     Our process requires that both local and Split toggles
     cannot co-exist to help test properly getting the correct values.
     Adjust this to true only for cases that would need a special buid.
    */
    var generateBothLocalAndSplitToggle: Bool { false }
}

public extension MayaToggle {
    var experiment: MayaExperiment? {
        switch self {
        case .walletRewardsReorder: return .walletRewardsReorder
        case .registerNowLabel: return .registerNowLabel
        default: return nil
        }
    }
}
