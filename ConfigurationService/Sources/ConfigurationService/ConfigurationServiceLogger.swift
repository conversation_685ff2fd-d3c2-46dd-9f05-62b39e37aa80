//
//  ConfigurationServiceLogger.swift
//  ConfigurationService
//
//  Created by <PERSON> on 9/24/24.
//

import Foundation
import os.log

public class ConfigurationServiceLogger {
    private enum Constants {
        static let subsystem = Bundle.main.bundleIdentifier!
        static let category = "ConfigurationService"
    }

    public static var isEnabled = false

    private static let logger = os.Logger(subsystem: Constants.subsystem, category: Constants.category)

    public static func log(message: String) {
        guard isEnabled else { return }
        #if !PROD_TARGET && !SANDBOX_TARGET
        logger.notice("\(message, privacy: .public)")
        #endif
    }
}
