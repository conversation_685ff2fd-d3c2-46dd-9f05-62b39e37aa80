//
//  LocalConfigurationProvider.swift
//  
//
//  Created by <PERSON> on 7/3/24.
//

import Foundation

public class LocalConfigurationProvider: ConfigurationProvider {
    private let userDefaults = UserDefaults.standard

    public init() { }

    func getStringValue(for toggle: MayaToggle) -> String? {
        return getValue(for: toggle) { $0 as? String }
    }

    func getBoolValue(for toggle: MayaToggle) -> Bool? {
        return getValue(for: toggle) { $0 as? Bool }
    }

    func getIntValue(for toggle: MayaToggle) -> Int? {
        return getValue(for: toggle) { value in
            guard let stringValue = value as? String else { return nil }
            return Int(stringValue)
        }
    }

    func getDoubleValue(for toggle: MayaToggle) -> Double? {
        return getValue(for: toggle) { value in
            guard let stringValue = value as? String else { return nil }
            return Double(stringValue)
        }
    }

    func getDictionaryValue(for toggle: MayaToggle) -> [String: Any]? {
        let key = toggle.localKey
        Logger.log(message: "Fetching Split dict for \(key)")

        guard let data = getDataValue(for: toggle),
            let dictionary = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            Logger.log(message: "Failed to fetch Split dict for \(key) - cannot deserialize data")
            return nil
        }

        Logger.log(message: "Fetched Split dict for \(key)")
        for (dictKey, value) in dictionary {
            Logger.log(message: "\(dictKey): \(value)")
        }
        return dictionary
    }

    func getObjectValue<T: Codable>(for toggle: MayaToggle, as type: T.Type) -> T? {
        let key = toggle.localKey
        Logger.log(message: "Fetching Split \(type) for \(key)")

        guard let data = getDataValue(for: toggle),
            let object = try? JSONDecoder().decode(type, from: data) else {
            Logger.log(message: "Failed to fetch lo \(type) for \(key) - cannot decode data from dictionary")
            return nil
        }

        Logger.log(message: "Fetched Split \(type) for \(key)")
        Logger.log(message: "\(object)")
        return object
    }

    private func getDataValue(for toggle: MayaToggle) -> Data? {
        return getValue(for: toggle) { value in
            guard let stringValue = value as? String,
                let data = stringValue.data(using: .utf8) else {
                return nil
            }
            return data
        }
    }

    private func getValue<T>(for toggle: MayaToggle, handler: (Any) -> T?) -> T? {
        let key = toggle.localKey
        let type = T.Type.self
        Logger.log(message: "Fetching local \(type) value for \(key)")

        guard let value = userDefaults.object(forKey: key) else {
            Logger.log(message: "Failed to fetch local \(type) value for \(key)")
            return nil
        }

        guard let actualValue = handler(value) else {
            Logger.log(message: "Failed to cast \"\(value)\" to local \(type) for \(key)")
            return nil
        }

        Logger.log(message: "Fetched \(actualValue) local \(type) value for \(key)")
        return actualValue
    }
}
