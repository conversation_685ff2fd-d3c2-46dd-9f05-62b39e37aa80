//
//  SplitConfigurationProvider.swift
//
//
//  Created by <PERSON> on 7/3/24.
//

import Analytics
import Foundation
import Injector
import ReactiveSwift
import Split
import StoreProvider
import UIKit

private typealias Constants = SplitConstants

public class SplitConfigurationProvider: ConfigurationProvider {
    @Inject private var analyticsService: AnalyticsService
    @Inject private var storeProvider: StoreProvider

    private var anonymousClient: SplitClient? {
        didSet { setAnonymousClientAttributes() }
    }

    private var userClient: SplitClient? {
        didSet { setUserClientAttributes() }
    }
    private var userKey: String?

    private var clientAttributes: [String: Any]? {
        didSet { setClientAttributes() }
    }

    private let sdkKey: String
    private var userContent: UserConsent = .unknown

    private let anonymousClientEventProperty = MutableProperty<SplitClientEvent>(.initial)
    private let userClientEventProperty = MutableProperty<SplitClientEvent>(.initial)

    public init(sdkKey: String) {
        self.sdkKey = sdkKey
        createAnonymousClient()
    }

    // MARK: ConfigurationProvider methods
    func getStringValue(for toggle: MayaToggle) -> String? {
        getStringValue(for: toggle, attributes: nil)
    }

    func getBoolValue(for toggle: MayaToggle) -> Bool? {
        getBoolValue(for: toggle, attributes: nil)
    }

    func getIntValue(for toggle: MayaToggle) -> Int? {
        getIntValue(for: toggle, attributes: nil)
    }

    func getDoubleValue(for toggle: MayaToggle) -> Double? {
        getDoubleValue(for: toggle, attributes: nil)
    }

    func getDictionaryValue(for toggle: MayaToggle) -> [String: Any]? {
        getDictionaryValue(for: toggle, attributes: nil)
    }

    func getObjectValue<T: Codable>(for toggle: MayaToggle, as type: T.Type) -> T? {
        getObjectValue(for: toggle, as: type, attributes: nil)
    }

    // MARK: ConfigurationProvider methods (with attributes)
    func getStringValue(for toggle: MayaToggle, attributes: [String: Any]?) -> String? {
        getValue(for: toggle, attributes: attributes) { $0 }
    }

    func getBoolValue(for toggle: MayaToggle, attributes: [String: Any]?) -> Bool? {
        getValue(for: toggle, attributes: attributes) { $0 == Constants.Reserved.on }
    }

    func getIntValue(for toggle: MayaToggle, attributes: [String: Any]?) -> Int? {
        getValue(for: toggle, attributes: attributes) { Int($0) }
    }

    func getDoubleValue(for toggle: MayaToggle, attributes: [String: Any]?) -> Double? {
         getValue(for: toggle, attributes: attributes) { Double($0) }
    }

    func getDictionaryValue(for toggle: MayaToggle, attributes: [String: Any]?) -> [String: Any]? {
        guard let key = toggle.splitKey else {
            Logger.log(message: "Failed to fetch Split dict for \(toggle.title) - no Split key")
            return nil
        }

        Logger.log(message: "Fetching Split dict for \(key)")
        let failedLogMessage = "Failed to fetch Split dict for \(key)"

        guard let client = getClient(trafficType: toggle.trafficType) else {
            Logger.log(message: "\(failedLogMessage) - no \(toggle.trafficType.rawValue) client")
            return nil
        }

        guard let stringData = client.getTreatmentWithConfig(key, attributes: attributes).config,
              let data = stringData.data(using: .utf8),
              let dictValue = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            Logger.log(message: "\(failedLogMessage) - cannot serialize data")
            return nil
        }

        Logger.log(message: "Fetched Split dict for \(key)")
        for (dictKey, value) in dictValue {
            Logger.log(message: "\(dictKey): \(value)")
        }
        return dictValue
    }

    private func getObjectValue<T: Decodable>(for toggle: MayaToggle, as type: T.Type, attributes: [String: Any]?) -> T? {
        guard let key = toggle.splitKey else {
            Logger.log(message: "Failed to fetch Split \(type) for \(toggle.title) - no Split key")
            return nil
        }

        guard let dict = getDictionaryValue(for: toggle, attributes: attributes) else {
            return nil
        }

        Logger.log(message: "Fetching Split \(type) for \(key)")
        let failedLogMessage = "Failed to fetch Split \(type) for \(key)"

        guard let data = try? JSONSerialization.data(withJSONObject: dict),
            let object = try? JSONDecoder().decode(type, from: data) else {
            Logger.log(message: "\(failedLogMessage) - cannot decode data from dictionary")
            return nil
        }

        Logger.log(message: "Fetched Split \(type) for \(key)")
        Logger.log(message: "\(object)")
        return object
    }

    private func getValue<T>(for toggle: MayaToggle, attributes: [String: Any]?, handler: (String) -> T?) -> T? {
        let type = T.self
        guard let key = toggle.splitKey else {
            Logger.log(message: "Failed to fetch Split \(type) for \(toggle.title) - no Split key")
            return nil
        }

        Logger.log(message: "Fetching Split \(type) for \(key)")
        let failedLogMessage = "Failed to fetch Split \(type) for \(key)"

        guard let client = getClient(trafficType: toggle.trafficType) else {
            Logger.log(message: "\(failedLogMessage) - no \(toggle.trafficType.rawValue) client")
            return nil
        }

        let value = client.getTreatment(key, attributes: attributes)

        // "control" is a reserved word returned when the SDK cannot fetch the value (e.g. key doesn't exist)
        // https://help.split.io/hc/en-us/articles/360020528072-Control-treatment
        guard value != Constants.Reserved.control else {
            Logger.log(message: "\(failedLogMessage) - \"control\" value")
            return nil
        }

        guard let actualValue = handler(value) else {
            Logger.log(message: "Failed to cast \"\(value)\" to Split \(type) for \(key)")
            return nil
        }

        Logger.log(message: "Fetched \"\(value)\" Split \(type) for \(key)")
        return actualValue
    }
}

// MARK: Analytics methods
extension SplitConfigurationProvider {
    @discardableResult
    func track(for toggle: MayaToggle, eventName: String, attributes: [String: Any]?) -> Bool {
        guard let client = getClient(trafficType: toggle.trafficType) else {
            return false
        }
        return client.track(eventType: eventName, properties: attributes)
    }
}

// MARK: Helper get property methods
extension SplitConfigurationProvider {
    func getEventProperty(trafficType: MayaToggle.TrafficType) -> MutableProperty<SplitClientEvent> {
        switch trafficType {
        case .anonymous: anonymousClientEventProperty
        case .user: userClientEventProperty
        }
    }
}

// MARK: Attribute methods
extension SplitConfigurationProvider {
    func setAttributes(_ values: [String: Any]) {
        clientAttributes = values
    }

    func clearAttributes() {
        clientAttributes = nil
    }
}

// MARK: Client lifecycle methods
extension SplitConfigurationProvider {
    func createUserClient(key: String) {
        func createUserClient() {
            guard let (factory, client) = createClient(trafficType: .user, key: key) else { return }
            userClient = client
            userKey = key
            observeClient(client, factory: factory, trafficType: .user)
        }

        if userKey == nil {
            createUserClient()
        } else if let userKey, userKey != key {
            createUserClient()
        }
    }

    func createAnonymousClient() {
        guard let key = UIDevice.current.identifierForVendor?.uuidString,
            let (factory, client) = createClient(trafficType: .anonymous, key: key) else {
            return
        }
        anonymousClient = client
        observeClient(client, factory: factory, trafficType: .anonymous)
    }

    func destroyUserClient() {
        userClient?.destroy()
        userClient = nil
        userKey = nil
        userClientEventProperty.value = .destroyed
        Logger.log(message: "\(MayaToggle.TrafficType.user) destroyed")
    }
}

// MARK: Consent methods
extension SplitConfigurationProvider {
    func setUserConsent(enabled: Bool) {
        userContent = enabled ? .granted : .declined
    }
}

// MARK: Private methods
private extension SplitConfigurationProvider {
    private func getClient(trafficType: MayaToggle.TrafficType) -> SplitClient? {
         switch trafficType {
         case .anonymous: anonymousClient
         case .user: userClient
         }
     }

    func createClient(trafficType: MayaToggle.TrafficType, key: String) -> (factory: SplitFactory, client: SplitClient)? {
        let eventProperty = getEventProperty(trafficType: trafficType)
        eventProperty.value = .preparing

        let splitKey = Key(matchingKey: Constants.Config.initialKey)

        let config = SplitClientConfig()
        config.streamingEnabled = false
        config.encryptionEnabled = true
        config.persistentAttributesEnabled = true
        config.prefix = Constants.Config.prefix
        config.trafficType = trafficType.rawValue

        var sdkReadyTimeOut = 300
        if let timeoutString = UserDefaults.standard.object(forKey: MayaToggle.splitBlockingTimeout.localKey) as? String,
           let timeout = Int(timeoutString) {
            sdkReadyTimeOut = timeout
        }
        config.sdkReadyTimeOut = sdkReadyTimeOut

        config.sync = SyncConfig.builder()
            .addSplitFilter(SplitFilter.bySet([Constants.Config.mobileSet, trafficType.flagSet]))
            .build()

        #if DEBUG
        config.logLevel = .debug
        #else
        config.logLevel = .none
        #endif

        /*
         // See https://help.split.io/hc/en-us/articles/360020401491-iOS-SDK#configuration for other parameters
         config.persistentAttributesEnabled = false
         config.featuresRefreshRate = 3600
         config.segmentsRefreshRate = 1800
         config.impressionRefreshRate = 1800
         config.impressionsQueueSize = 30000
         config.userConsent = userContent
         */

        let builder = DefaultSplitFactoryBuilder()

        let factory = builder
            .setApiKey(sdkKey)
            .setKey(splitKey)
            .setConfig(config)
            .build()

        guard let factory else {
            Logger.log(message: "\(trafficType) failed to be created - factory error")
            eventProperty.value = .unknown
            return nil
        }

        let client = factory.client(matchingKey: key)
        Logger.log(message: "\(trafficType) created - key \(key)")

        eventProperty.value = .created
        return (factory, client)
    }

    func setClientAttributes() {
        setAnonymousClientAttributes()
        setUserClientAttributes()
    }

    func setAnonymousClientAttributes() {
        setClientAttributes(trafficType: .anonymous)
    }

    func setUserClientAttributes() {
        setClientAttributes(trafficType: .user)
    }

    func setClientAttributes(trafficType: MayaToggle.TrafficType) {
        guard let client = getClient(trafficType: trafficType) else { return }
        if let clientAttributes {
            _ = client.setAttributes(clientAttributes)

            Logger.log(message: "\(trafficType) attributes saved")
            for (key, value) in clientAttributes {
                Logger.log(message: "\(key): \(value)")
            }
        } else {
            _ = client.clearAttributes()
            Logger.log(message: "\(trafficType) attributes cleared")
        }
    }

    func observeClient(_ client: SplitClient, factory: SplitFactory, trafficType: MayaToggle.TrafficType) {
        func handleEvent(_ event: SplitEvent) {
            getEventProperty(trafficType: trafficType).value = SplitClientEvent(event: event)

            let attributes = [AnalyticsAttributeKey.trafficType.rawValue: trafficType.analyticsValue]
            switch event {
            case .sdkReadyFromCache:
                analyticsService.logMayaEvents(action: .success(Analytics.Split.Client.cached), attributes: attributes)
            case .sdkReady:
                analyticsService.logMayaEvents(action: .success(Analytics.Split.Client.ready), attributes: attributes)
            default:
                break
            }
        }

        client.on(event: .sdkReadyFromCache) {
            Logger.log(message: "😬 \(trafficType) ready from cache 😬")
            handleEvent(.sdkReadyFromCache)
        }

        client.on(event: .sdkReady) {
            Logger.log(message: "✨ \(trafficType) ready ✨")

            let splits = factory.manager.splits
            let splitNames = splits.compactMap { $0.name }.joined(separator: ", ")

            Logger.log(message: "🕺🏻💃🏻 Split feature flag names: \(splitNames) 💃🏻🕺🏻")
            handleEvent(.sdkReady)
        }

        client.on(event: .sdkReadyTimedOut) {
            Logger.log(message: "😵‍💫 \(trafficType) timed out 😵‍💫")
            handleEvent(.sdkReadyTimedOut)
        }

        client.on(event: .sdkUpdated) {
            Logger.log(message: "🫨 \(trafficType) updated 🫨")
            handleEvent(.sdkUpdated)
        }
    }
}
