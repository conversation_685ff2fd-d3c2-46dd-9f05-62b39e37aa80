//
//  ConfigurationProvider.swift
//  
//
//  Created by <PERSON> on 7/2/24.
//

import Foundation

protocol ConfigurationProvider {
    func getStringValue(for toggle: MayaToggle) -> String?
    func getBoolValue(for toggle: <PERSON>Toggle) -> Bool?
    func getIntValue(for toggle: <PERSON>Toggle) -> Int?
    func getDoubleValue(for toggle: <PERSON>Toggle) -> Double?
    func getDictionaryValue(for toggle: <PERSON>Toggle) -> [String: Any]?
    func getObjectValue<T: Codable>(for toggle: MayaToggle, as type: T.Type) -> T?
}
