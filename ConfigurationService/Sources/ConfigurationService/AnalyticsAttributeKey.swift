//
//  AnalyticsAttributeKey.swift
//  ConfigurationService
//
//  Created by <PERSON> on 6/4/25.
//

enum AnalyticsAttributeKey: String {
    case trafficType = "traffic_type"
    case clientIsReady = "client_is_ready"
    case key
    case localValue = "local_value"
    case finalValue = "final_value"
    case remoteValue = "remote_value"
    case defaultValue = "default_value"
    case dataType = "data_type"
}
