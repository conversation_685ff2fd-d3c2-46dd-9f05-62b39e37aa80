//
//  MayaToggle.swift
//
//
//  Created by <PERSON> on 7/2/24.
//

import Foundation

/**
    Toggles can be used for non-production targets only. Values declared here are generated in SplitToggles.plist.
    Feature toggle do not need to be removed once feature is ready for production release; it can be done with actual cleanup.
 */
public enum MayaToggle: String, CaseIterable {
    enum Constants {
        static let togglePrefix = "non_prod_"
    }

    // MARK: Base App
    case actionCards
    case bannerCorrelationHeader
    case changePasswordSwiftUI
    case clevertapUnliBanners
    case creatorStoreBanners
    case creatorStoreInterstitialBanners
    case dashboardNavigationRefactor
    case dcrnSessionHeader
    case dcrnVerifyHeader
    case ddpLauncher
    case deviceManagement
    case easyCreditBanner
    case externalLinkPrompt
    case maintenanceBypass
    case profileClevertapUnliBanners
    case rateThisAppButton
    case servicesChatWithMaya
    case servicesClevertapUnliBanners
    case walletRewardsReorder
    case splitBlockingTimeout

    // MARK: Crypto
    case cryptoV2

    // MARK: Lending
    case ccCreditScoring
    case ccDBL
    case ccSkipCreditScoring
    case iclDeviceScoring
    case iclMfa
    case iclGeolocation
    case lendingDataExtraction
    case mecAPIGateway
    case mecBannerV2
    case mecDeviceScoring
    case mecGeolocation
    case mecMfa
    case mecWriteOff
    case pfMvp

    // MARK: CMS LYNX
    case mayaBlackCreditCard
    case mayaBlackIntro
    case convertToInstallment
    case landersCreditCard
    case cardDetailsProtection

    // MARK: Mewtwo
    case securityCenter
    case accountFreezeStatus

    // MARK: - Onyx
    case registerNowLabel
    case shortenedRegistration
    case landingPageV2

    // MARK: - Helix
    case upgradeNowLabel
    case welcomeButtonLabel
    case showWelcomeDoItLaterButton
    case changeWelcomeBackButton
    case kycTofuUXRedesign
    case mockCMSV8
    case regToKYCFlow

    // MARK: - Anbu
    case billsCategoryAdBanner
    case autoDebitArrangementSdk
    case loadMinDetection

    // MARK: - Rubeus
    case interstitialManualConfigService
    case blackpinkGiveawayDeepLinkGuard
    case mgmV2ForceUpdate
    case mgmV2

    // MARK: INBOX
    case inboxTicketing
    case sendBirdCachingCollection
}

public extension MayaToggle {
    enum Owner {
        case baseApp
        case invest
        case lending
        case cmsLynx
        case mewtwo
        case onyx
        case helix
        case anbu
        case rubeus
        case inbox
    }

    enum DataType: String {
        case bool
        case string
        case integer
        case double
        case dictionary
        case object
    }

    enum TrafficType: String {
        case anonymous
        case user
    }
}

extension MayaToggle.Owner {
    var title: String {
        switch self {
        case .baseApp: "Base App"
        case .invest: "Invest"
        case .lending: "Lending"
        case .cmsLynx: "CMS Lynx"
        case .mewtwo: "Mewtwo"
        case .onyx: "Onyx"
        case .helix: "Helix"
        case .anbu: "Anbu"
        case .rubeus: "Rubeus"
        case .inbox: "Inbox"
        }
    }
}

extension MayaToggle.DataType {
    var analyticsValue: String { rawValue }
}

extension MayaToggle.TrafficType {
    var analyticsValue: String { rawValue }

    var flagSet: String {
        switch self {
        case .anonymous: "mobile_anonymous_traffic"
        case .user: "mobile_users_traffic"
        }
    }
}

extension MayaToggle.TrafficType: CustomDebugStringConvertible {
    public var debugDescription: String { "\(rawValue.capitalized) Split client" }
}
