//
//  CSConfigV2.swift
//  
//
//  Created by <PERSON> on 7/2/24.
//

import Foundation

public struct CSConfigV2<T: CSConfigType> {
    public typealias LocalConfigClosure = () -> T?
    public typealias SplitConfigClosure = (_ attributes: [String: Any]?) -> T?
    public typealias DefaultConfigClosure = () -> T
    public typealias AnalyticsClosure = (_ finalValue: T, _ localValue: T?, _ splitValue: T?, _ defaultValue: T) -> Void

    private var localConfig: LocalConfigClosure?
    private var splitConfig: SplitConfigClosure?
    private let defaultConfig: DefaultConfigClosure
    private let analyticsCompletion: AnalyticsClosure

    public init(localConfig: LocalConfigClosure? = nil, splitConfig: SplitConfigClosure? = nil, defaultConfig: @escaping DefaultConfigClosure, analyticsCompletion: @escaping AnalyticsClosure) {
        self.localConfig = localConfig
        self.splitConfig = splitConfig
        self.defaultConfig = defaultConfig
        self.analyticsCompletion = analyticsCompletion
    }
}

extension CSConfigV2 {
    public var value: T {
        value(withAttributes: nil)
    }

    public func value(withAttributes attributes: [String: Any]?) -> T {
        let localValue = localConfig?()
        let splitValue = splitConfig?(attributes)
        let defaultValue = defaultConfig()
        let finalValue: T

        #if AUTOMATION_TARGET
        if let localValue {
            finalValue = localValue
        } else {
            finalValue = defaultValue
        }
        #else
        if let localValue {
            finalValue = localValue
        } else if let splitValue {
            finalValue = splitValue
        } else {
            finalValue = defaultValue
        }
        #endif

        analyticsCompletion(finalValue, localValue, splitValue, defaultValue)
        return finalValue
    }
}
