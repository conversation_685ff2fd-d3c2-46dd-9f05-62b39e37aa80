//
//  PayMayaRemoteConfig.swift
//  PayMaya
//
//  Created by <PERSON> on 20/09/2019.
//  Copyright © 2019 PayMaya Philippines, Inc. All rights reserved.
//

import FirebaseRemoteConfig
import Foundation
import StoreProvider

/// Fetched values from Firebase remote config for a more controlled roll-out
struct PayMayaRemoteConfig: Codable {
    var appMaintenance: Bool?
    var glimpseEnabled: Bool?
    var instapayQREnabled: Bool?
    var bankPullFundsEnabled: Bool?
    var changeMinEnabled: Bool?
    var whitelistedAnalyticsEventsV2Enabled: Bool?
    var topUpServiceEnabled: Bool?
    var whiteThemeEnabled: Bool?
    var amvcFingerprintingEnabled: Bool?
    var digitalOREnabled: Bool?
    var eddEnabled: Bool?
    var restrictedLoginEnabled: Bool?
    var customerRestrictionsEnabled: Bool?
    var kycCompletedCallbackEnabled: Bool?
    var registrationRestrictionsEnabled: Bool?
    var eddRestrictionsEnabled: Bool?
    var idClassificationEnabled: Bool?
    var mandatoryIdClassificationEnabled: Bool?
    var amountFormatterV2Enabled: Bool?
    var cashInWithAccountLimitEnabled: Bool?
    var cryptoEnabled: Bool?
    var savingsEnabled: Bool?
    var chatbotHelpEnabled: Bool?
    var qrP2PAdjustmentEnabled: Bool?
    var registrationMGMEnabled: Bool?
    var mayaUsernameEnabled: Bool?
    var mayaPushApprovalEnabled: Bool?
    var mayaShowOtherIDsEnabled: Bool?
    var mayaReKYCEnabled: Bool?
    var mayaCashinWalletSettingsEnabled: Bool?
    var mayaCashinWalletSettingsAutoCashInOptionEnabled: Bool?
    var mayaCashinMayaSavingsButtonEnabled: Bool?
    var mayaBnplLoansTabEnabled: Bool?
    var qrCheckoutEnabled: Bool?
    var mayaLoansV3Enabled: Bool?
    var mayaLoansSimplifiedCheckoutEnabled: Bool?
    var mayaBillerReminderEnabled: Bool?
    var payBillsV3EndpointEnabled: Bool?
    var cashInBankPullV3Enabled: Bool?
    var fundsEnabled: Bool?
    var mayaLoansCashInstallmentEnabled: Bool?
    var bdoCashInEnabled: Bool?
    var inboxFlutterEnabled: Bool?
    var inboxManageNotificationsEnabled: Bool?
    var inboxSupportEnabled: Bool?
    var virtualNumberRegistrationEnabled: Bool?
    var locationBasedVirtualNumberRegistrationEnabled: Bool?
    var unifiedLendingExperienceReskinEnabled: Bool?
    var mayaEKYCV6ShortenedFlowUpgradeEnabled: Bool?
    var mayaEKYCV6ShortenedFlowReKYCEnabled: Bool?
    var mayaEKYCV6ShortenedFlowOtherIDEnabled: Bool?
    var mayaEKYCV6ShortenedFlowIdCaptureV2Enabled: Bool?
    var mayaEKYCV6ShortenedFlowIdCaptureV3Enabled: Bool?
    var mayaEKYCV6ShortenedFlowExpandedReKYCEnabled: Bool?
    var mayaEKYCV6ShortenedFlowBlurDetectionEnabled: Bool?
    var mayaEKYCV6ShortenedFlowBackIDCaptureEnabled: Bool?
    var mayaEKYCV6ShortenedFlowSecondaryIDCaptureEnabled: Bool?
    var mayaEKYCV6ShortenedFlowAddressDropdownEnabled: Bool?
    var mayaEKYCV6ShortenedFlowForeignAddressComplianceEnabled: Bool?
    var mayaEKYCV6ShortenedFlowPhilsysEnabled: Bool?
    var mayaEKYCV6KycIdImageQualityEnabled: Bool?
    var mayaEKYCV6PartnerOnboardingEnabled: Bool?
    var mayaEKYCV6OnboardingAndKYCFlow: Bool?
    var mayaEKYCV6OnboardingAndKYCFlowV2: Bool?
    var mayaEKYCV6KycMaintenance: Bool?
    var mayaEKYCV6ForcePHNationalID: Bool?
    var mayaComplianceFatca: Bool?
    var mayaComplianceDosri: Bool?
    var mayaRegistrationAutoProvisionEnabled: Bool?
    var mayaRegistrationConsentCheckAll: Bool?
    var mayaRegistrationRequireMiddleName: Bool?
    var mayaMFAFrameworkV1Enabled: Bool?
    var mayaMFAFaceAuthFormEnabled: Bool?
    var mayaForgotPasswordFrameworkV2Enabled: Bool?
    var mayaShopProviderSubcatPillsEnabled: Bool?
    var initialEligibilityCheckEnabled: Bool?
    var mayaPayBillsPayWithMayaCreditEnabled: Bool?
    var mayaPayBillsPayWithMayaCreditNewTagEnabled: Bool?
    var mayaPayBillsPayWithMayaCreditSoonEnabled: Bool?
    var mayaPayBillsClientApiCheckoutFlowV3Enabled: Bool?
    var mayaBillsPaySkipWalletValidationEnabled: Bool?
    var billsPayHandleOtherFieldsFromDeepLinkEnabled: Bool?
    var billsPayDisableAccountNumberFieldEnabled: Bool?
    var billsPayFavoritesWithCreatedPaymentEnabled: Bool?
    var iclDropdownV2Enabled: Bool?
    var iclPhaseTwoV1Enabled: Bool?
    var iclPhaseTwoV2Enabled: Bool?
    var bnplDeepFreezeEnabled: Bool?
    var inAppReviewEnabled: Bool?
    var inAppReviewSendMoneyEnabled: Bool?
    var fundsCreatorStoreEnabled: Bool?
    var requestMoneyTileUpdateEnabled: Bool?
    var userProfileUpdateV1Enabled: Bool?
    var iclMothersMaidenNameEnabled: Bool?
    var loadV2Enabled: Bool?
    var loadV2FavoritesEnabled: Bool?
    var loadV2RecommenderEnabled: Bool?
    var iclMothersMaidenNameV2Enabled: Bool?
    var iclMothersMaidenNameV3Enabled: Bool?
    var loansCreditCardEnabled: Bool?
    var iclDeviceScoringDataReferenceIdEnabled: Bool?
    var iclToMambuEnabled: Bool?
    var iclDblEnabled: Bool?
    var loansCreditCardDetailsV2Enabled: Bool?
    var ccMayaBlackEnabled: Bool?
    var bannerCorrelationHeaderEnabled: Bool?
    var voucherDetailsV2Enabled: Bool?
    var creditCardCashbackTileEnabled: Bool?
    var ecommAppEventV2QrEnabled: Bool?
    var ecommAppEventV2ShopEnabled: Bool?
    var ecommAppEventV2BillsPayEnabled: Bool?
    var loyaltyForceUpdateEnabled: Bool?
    var loginHistoryTTL: String?
    var appEventsV2CashInEnabled: Bool?
    var freezeCardSpiel: String?
    var unfreezeCardSpiel: String?
    var freezeConfirmationSpiel: String?
    var unfreezeConfirmationSpiel: String?
    var freezeSuccessSpiel: String?
    var unfreezeSuccessSpiel: String?
    var cardPullV2Enabled: Bool?
    var loansICLDeviceScoringEnabled: Bool?
    var mayaCreditIosDeviceScoringEnabled: Bool?
    var ccDeviceScoringEnabled: Bool?
    var landersCreditCardEnabled: Bool?
    var cardDetailsProtectionEnabled: Bool?

    enum CodingKeys: String, CodingKey {
        case appMaintenance = "app_maintenance"
        case glimpseEnabled = "glimpse_enabled"
        case instapayQREnabled = "instapay_qr_enabled"
        case bankPullFundsEnabled = "pull_funds_bpi_enabled"
        case changeMinEnabled = "change_min_enabled"
        case whitelistedAnalyticsEventsV2Enabled = "whitelisted_analytics_events_v2_enabled"
        case topUpServiceEnabled = "top_up_service_enabled"
        case whiteThemeEnabled = "white_navigation_bar_enabled"
        case amvcFingerprintingEnabled = "amvc_fingerprinting_enabled"
        case digitalOREnabled = "shop_purchase_digital_or_enabled"
        case eddEnabled = "edd_enabled"
        case restrictedLoginEnabled = "restricted_login_flow_enabled"
        case customerRestrictionsEnabled = "customer_restrictions_enabled"
        case kycCompletedCallbackEnabled = "kyc_completed_callback_enabled"
        case registrationRestrictionsEnabled = "registration_restrictions_enabled"
        case eddRestrictionsEnabled = "edd_restrictions_enabled"
        case idClassificationEnabled = "id_classification_enabled"
        case mandatoryIdClassificationEnabled = "mandatory_id_classification_enabled"
        case amountFormatterV2Enabled = "amount_formatter_v2_enabled"
        case cashInWithAccountLimitEnabled = "cash_in_with_account_limit_enabled"
        case cryptoEnabled = "crypto_enabled"
        case savingsEnabled = "maya_enabled_savings"
        case chatbotHelpEnabled = "chat_bot_help_enabled"
        case qrP2PAdjustmentEnabled = "maya_enabled_qrp2p_adjustment"
        case registrationMGMEnabled = "registration_submit_invite_code_enabled"
        case mayaUsernameEnabled = "maya_enabled_username"
        case mayaPushApprovalEnabled = "maya_enabled_push_approval_2"
        case mayaShowOtherIDsEnabled = "show_other_ids_zoloz"
        case mayaReKYCEnabled = "re_kyc"
        case mayaCashinWalletSettingsEnabled = "maya_enabled_cashin_wallet_settings"
        case mayaCashinWalletSettingsAutoCashInOptionEnabled = "maya_enabled_cashin_wallet_settings_auto_cash_in_option"
        case mayaCashinMayaSavingsButtonEnabled = "maya_enabled_cashin_maya_savings_button"
        case mayaBnplLoansTabEnabled = "maya_enabled_loans"
        case qrCheckoutEnabled = "maya_enabled_checkout_on_qr"
        case mayaLoansV3Enabled = "loans_v3_enabled"
        case mayaLoansSimplifiedCheckoutEnabled = "loans_simplified_checkout_enabled"
        case mayaBillerReminderEnabled = "maya_biller_reminder_enabled"
        case payBillsV3EndpointEnabled = "pay_bills_v3_endpoint_enabled"
        case cashInBankPullV3Enabled = "cashin_bank_pull_v3_enabled"
        case fundsEnabled = "funds_enabled"
        case mayaLoansCashInstallmentEnabled = "installment_cash_loans_enabled"
        case bdoCashInEnabled = "bank_pull_settings_v2_enabled"
        case inboxFlutterEnabled = "inbox_v4_enabled"
        case inboxManageNotificationsEnabled = "inbox_v4_manage_notifs_enabled"
        case inboxSupportEnabled = "inbox_v4_maya_support_enabled"
        case virtualNumberRegistrationEnabled = "virtual_number_registration_enabled"
        case locationBasedVirtualNumberRegistrationEnabled = "location_based_virtual_number_registration_v2_enabled"
        case unifiedLendingExperienceReskinEnabled = "unified_lending_experience_reskin_enabled"
        case mayaEKYCV6ShortenedFlowUpgradeEnabled = "maya_ekyc_v6_shortened_flow_upgrade"
        case mayaEKYCV6ShortenedFlowReKYCEnabled = "maya_ekyc_v6_shortened_flow_rekyc"
        case mayaEKYCV6ShortenedFlowOtherIDEnabled = "maya_ekyc_v6_shortened_flow_show_other_id"
        case mayaEKYCV6ShortenedFlowIdCaptureV2Enabled = "maya_ekyc_v6_shortened_flow_id_capture_v2"
        case mayaEKYCV6ShortenedFlowIdCaptureV3Enabled = "maya_ekyc_v6_shortened_flow_id_capture_v3"
        case mayaEKYCV6ShortenedFlowExpandedReKYCEnabled = "maya_ekyc_v6_shortened_flow_expanded_rekyc"
        case mayaEKYCV6ShortenedFlowBlurDetectionEnabled = "maya_ekyc_v6_shortened_flow_blur_detection"
        case mayaEKYCV6ShortenedFlowBackIDCaptureEnabled = "maya_ekyc_v6_shortened_flow_back_id_capture"
        case mayaEKYCV6ShortenedFlowSecondaryIDCaptureEnabled = "maya_ekyc_v6_shortened_flow_secondary_id_capture"
        case mayaEKYCV6ShortenedFlowAddressDropdownEnabled = "maya_ekyc_v6_shortened_flow_address_dropdown"
        case mayaEKYCV6ShortenedFlowForeignAddressComplianceEnabled = "maya_ekyc_v6_shortened_flow_foreign_address_compliance"
        case mayaEKYCV6ShortenedFlowPhilsysEnabled = "maya_ekyc_v6_shortened_flow_philsys_v2"
        case mayaEKYCV6KycIdImageQualityEnabled = "maya_ekyc_v6_kyc_id_image_quality_v4"
        case mayaEKYCV6PartnerOnboardingEnabled = "maya_ekyc_v6_partner_onboarding"
        case mayaEKYCV6OnboardingAndKYCFlow = "maya_ekyc_v6_onboarding_and_kyc"
        case mayaEKYCV6OnboardingAndKYCFlowV2 = "maya_ekyc_v6_onboarding_and_kyc_flow_v2"
        case mayaEKYCV6KycMaintenance = "maya_ekyc_v6_kyc_maintenance"
        case mayaEKYCV6ForcePHNationalID = "maya_ekyc_v6_force_ph_national_id_v2"
        case mayaComplianceFatca = "maya_compliance_fatca"
        case mayaComplianceDosri = "maya_compliance_dosri"
        case mayaRegistrationAutoProvisionEnabled = "maya_registration_auto_provision"
        case mayaRegistrationConsentCheckAll = "maya_registration_consent_check_all"
        case mayaRegistrationRequireMiddleName = "maya_registration_require_middle_name"
        case mayaMFAFrameworkV1Enabled = "maya_mfa_framework_v1"
        case mayaMFAFaceAuthFormEnabled = "maya_mfa_face_auth_form"
        case mayaForgotPasswordFrameworkV2Enabled = "maya_forgot_password_framework_v2"
        case mayaShopProviderSubcatPillsEnabled = "maya_shop_provider_subcategories"
        case initialEligibilityCheckEnabled = "initial_eligibility_check_enabled"
        case mayaPayBillsPayWithMayaCreditEnabled = "maya_paybills_pay_with_maya_credit_enabled"
        case mayaPayBillsPayWithMayaCreditNewTagEnabled = "maya_paybills_pay_with_maya_credit_new_tag_enabled"
        case mayaPayBillsPayWithMayaCreditSoonEnabled = "maya_paybills_pay_with_maya_credit_soon_enabled"
        case mayaPayBillsClientApiCheckoutFlowV3Enabled = "maya_billspay_client_api_checkout_flow_v3_enabled"
        case mayaBillsPaySkipWalletValidationEnabled = "maya_bills_pay_skip_wallet_validation_enabled"
        case billsPayHandleOtherFieldsFromDeepLinkEnabled = "bills_pay_handle_other_fields_from_deep_link_enabled"
        case billsPayDisableAccountNumberFieldEnabled = "bills_pay_disable_account_number_field_enabled"
        case billsPayFavoritesWithCreatedPaymentEnabled = "bills_pay_favorites_with_created_payment_enabled"
        case iclDropdownV2Enabled = "icl_drop_down_v2_enabled"
        case iclPhaseTwoV1Enabled = "icl_phase_two_v1_enabled"
        case iclPhaseTwoV2Enabled = "icl_phase_two_v2_enabled"
        case bnplDeepFreezeEnabled = "bnpl_deep_freeze_enabled"
        case inAppReviewEnabled = "in_app_review_enabled_default"
        case inAppReviewSendMoneyEnabled = "in_app_review_enabled_send_money"
        case fundsCreatorStoreEnabled = "funds_creator_store_enabled"
        case requestMoneyTileUpdateEnabled = "request_money_tile_update_enabled"
        case userProfileUpdateV1Enabled = "user_profile_update_v1"
        case iclMothersMaidenNameEnabled = "icl_mothers_maiden_name_enabled"
        case loadV2Enabled = "load_v2_enabled"
        case loadV2FavoritesEnabled = "load_v2_favorites_enabled"
        case loadV2RecommenderEnabled = "load_v2_recommender_enabled"
        case iclMothersMaidenNameV2Enabled = "icl_mothers_maiden_name_v2_enabled"
        case iclMothersMaidenNameV3Enabled = "icl_mothers_maiden_name_v3_enabled"
        case loansCreditCardEnabled = "loans_credit_card_enabled"
        case iclDeviceScoringDataReferenceIdEnabled = "icl_device_scoring_data_reference_id_enabled"
        case iclToMambuEnabled = "icl_to_mambu_enabled"
        case iclDblEnabled = "icl_dbl_enabled"
        case loansCreditCardDetailsV2Enabled = "loans_credit_card_details_v2_enabled"
        case ccMayaBlackEnabled = "loans_credit_card_maya_black_enabled"
        case bannerCorrelationHeaderEnabled = "banner_correlation_header_enabled"
        case voucherDetailsV2Enabled = "voucher_details_v2_enabled"
        case creditCardCashbackTileEnabled = "landers_credit_card_cashback_tile_enabled"
        case ecommAppEventV2QrEnabled = "ecomm_app_event_v2_qr_enabled"
        case ecommAppEventV2ShopEnabled = "ecomm_app_event_v2_shop_enabled"
        case ecommAppEventV2BillsPayEnabled = "ecomm_app_event_v2_bills_pay_enabled"
        case loyaltyForceUpdateEnabled = "loyalty_force_update_enabled"
        case loginHistoryTTL = "login_history_ttl"
        case appEventsV2CashInEnabled = "app_events_v2_cash_in_enabled"
        case freezeCardSpiel = "freeze_card_spiel"
        case unfreezeCardSpiel = "unfreeze_card_spiel"
        case freezeConfirmationSpiel = "freeze_confirmation_spiel"
        case unfreezeConfirmationSpiel = "unfreeze_confirmation_spiel"
        case freezeSuccessSpiel = "freeze_success_spiel"
        case unfreezeSuccessSpiel = "unfreeze_success_spiel"
        case cardPullV2Enabled = "card_pull_v2_enabled"
        case loansICLDeviceScoringEnabled = "icl_device_scoring_enabled"
        case mayaCreditIosDeviceScoringEnabled = "easy_credit_ios_device_scoring_enabled"
        case ccDeviceScoringEnabled = "cc_device_scoring_enabled"
        case landersCreditCardEnabled = "landers_credit_card_enabled"
        case cardDetailsProtectionEnabled = "card_details_protection_enabled"
    }
}

extension PayMayaRemoteConfig {
    init(config: RemoteConfig) {
        appMaintenance = config.value(for: CodingKeys.appMaintenance.rawValue)?.boolValue
        glimpseEnabled = config.value(for: CodingKeys.glimpseEnabled.rawValue)?.boolValue
        instapayQREnabled = config.value(for: CodingKeys.instapayQREnabled.rawValue)?.boolValue
        bankPullFundsEnabled = config.value(for: CodingKeys.bankPullFundsEnabled.rawValue)?.boolValue
        changeMinEnabled = config.value(for: CodingKeys.changeMinEnabled.rawValue)?.boolValue
        whitelistedAnalyticsEventsV2Enabled = config.value(for: CodingKeys.whitelistedAnalyticsEventsV2Enabled.rawValue)?.boolValue
        topUpServiceEnabled = config.value(for: CodingKeys.topUpServiceEnabled.rawValue)?.boolValue
        whiteThemeEnabled = config.value(for: CodingKeys.whiteThemeEnabled.rawValue)?.boolValue
        amvcFingerprintingEnabled = config.value(for: CodingKeys.amvcFingerprintingEnabled.rawValue)?.boolValue
        digitalOREnabled = config.value(for: CodingKeys.digitalOREnabled.rawValue)?.boolValue
        eddEnabled = config.value(for: CodingKeys.eddEnabled.rawValue)?.boolValue
        restrictedLoginEnabled = config.value(for: CodingKeys.eddEnabled.rawValue)?.boolValue
        customerRestrictionsEnabled = config.value(for: CodingKeys.customerRestrictionsEnabled.rawValue)?.boolValue
        kycCompletedCallbackEnabled = config.value(for: CodingKeys.kycCompletedCallbackEnabled.rawValue)?.boolValue
        registrationRestrictionsEnabled = config.value(for: CodingKeys.registrationRestrictionsEnabled.rawValue)?.boolValue
        eddRestrictionsEnabled = config.value(for: CodingKeys.eddRestrictionsEnabled.rawValue)?.boolValue
        idClassificationEnabled = config.value(for: CodingKeys.idClassificationEnabled.rawValue)?.boolValue
        mandatoryIdClassificationEnabled = config.value(for: CodingKeys.mandatoryIdClassificationEnabled.rawValue)?.boolValue
        amountFormatterV2Enabled = config.value(for: CodingKeys.amountFormatterV2Enabled.rawValue)?.boolValue
        cashInWithAccountLimitEnabled = config.value(for: CodingKeys.cashInWithAccountLimitEnabled.rawValue)?.boolValue
        cryptoEnabled = config.value(for: CodingKeys.cryptoEnabled.rawValue)?.boolValue
        savingsEnabled = config.value(for: CodingKeys.savingsEnabled.rawValue)?.boolValue
        chatbotHelpEnabled = config.value(for: CodingKeys.chatbotHelpEnabled.rawValue)?.boolValue
        qrP2PAdjustmentEnabled = config.value(for: CodingKeys.qrP2PAdjustmentEnabled.rawValue)?.boolValue
        registrationMGMEnabled = config.value(for: CodingKeys.registrationMGMEnabled.rawValue)?.boolValue
        mayaUsernameEnabled = config.value(for: CodingKeys.mayaUsernameEnabled.rawValue)?.boolValue
        mayaPushApprovalEnabled = config.value(for: CodingKeys.mayaPushApprovalEnabled.rawValue)?.boolValue
        mayaShowOtherIDsEnabled = config.value(for: CodingKeys.mayaShowOtherIDsEnabled.rawValue)?.boolValue
        mayaReKYCEnabled = config.value(for: CodingKeys.mayaReKYCEnabled.rawValue)?.boolValue
        mayaCashinWalletSettingsEnabled = config.value(for: CodingKeys.mayaCashinWalletSettingsEnabled.rawValue)?.boolValue
        mayaCashinWalletSettingsAutoCashInOptionEnabled = config.value(for: CodingKeys.mayaCashinWalletSettingsAutoCashInOptionEnabled.rawValue)?.boolValue
        mayaCashinMayaSavingsButtonEnabled = config.value(for: CodingKeys.mayaCashinMayaSavingsButtonEnabled.rawValue)?.boolValue
        mayaBnplLoansTabEnabled = config.value(for: CodingKeys.mayaBnplLoansTabEnabled.rawValue)?.boolValue
        qrCheckoutEnabled = config.value(for: CodingKeys.qrCheckoutEnabled.rawValue)?.boolValue
        mayaLoansV3Enabled = config.value(for: CodingKeys.mayaLoansV3Enabled.rawValue)?.boolValue
        mayaLoansSimplifiedCheckoutEnabled = config.value(for: CodingKeys.mayaLoansSimplifiedCheckoutEnabled.rawValue)?.boolValue
        mayaBillerReminderEnabled = config.value(for: CodingKeys.mayaBillerReminderEnabled.rawValue)?.boolValue
        payBillsV3EndpointEnabled = config.value(for: CodingKeys.payBillsV3EndpointEnabled.rawValue)?.boolValue
        cashInBankPullV3Enabled = config.value(for: CodingKeys.cashInBankPullV3Enabled.rawValue)?.boolValue
        fundsEnabled = config.value(for: CodingKeys.fundsEnabled.rawValue)?.boolValue
        mayaLoansCashInstallmentEnabled = config.value(for: CodingKeys.mayaLoansCashInstallmentEnabled.rawValue)?.boolValue
        bdoCashInEnabled = config.value(for: CodingKeys.bdoCashInEnabled.rawValue)?.boolValue
        inboxFlutterEnabled = config.value(for: CodingKeys.inboxFlutterEnabled.rawValue)?.boolValue
        inboxManageNotificationsEnabled = config.value(for: CodingKeys.inboxManageNotificationsEnabled.rawValue)?.boolValue
        inboxSupportEnabled = config.value(for: CodingKeys.inboxSupportEnabled.rawValue)?.boolValue
        virtualNumberRegistrationEnabled = config.value(for: CodingKeys.virtualNumberRegistrationEnabled.rawValue)?.boolValue
        locationBasedVirtualNumberRegistrationEnabled = config.value(for: CodingKeys.locationBasedVirtualNumberRegistrationEnabled.rawValue)?.boolValue
        unifiedLendingExperienceReskinEnabled = config.value(for: CodingKeys.unifiedLendingExperienceReskinEnabled.rawValue)?.boolValue
        mayaEKYCV6ShortenedFlowUpgradeEnabled = config.value(for: CodingKeys.mayaEKYCV6ShortenedFlowUpgradeEnabled.rawValue)?.boolValue
        mayaEKYCV6ShortenedFlowReKYCEnabled = config.value(for: CodingKeys.mayaEKYCV6ShortenedFlowReKYCEnabled.rawValue)?.boolValue
        mayaEKYCV6ShortenedFlowOtherIDEnabled = config.value(for: CodingKeys.mayaEKYCV6ShortenedFlowOtherIDEnabled.rawValue)?.boolValue
        mayaEKYCV6ShortenedFlowIdCaptureV2Enabled = config.value(for: CodingKeys.mayaEKYCV6ShortenedFlowIdCaptureV2Enabled.rawValue)?.boolValue
        mayaEKYCV6ShortenedFlowIdCaptureV3Enabled = config.value(for: CodingKeys.mayaEKYCV6ShortenedFlowIdCaptureV3Enabled.rawValue)?.boolValue
        mayaEKYCV6ShortenedFlowExpandedReKYCEnabled = config.value(for: CodingKeys.mayaEKYCV6ShortenedFlowExpandedReKYCEnabled.rawValue)?.boolValue
        mayaEKYCV6ShortenedFlowBlurDetectionEnabled = config.value(for: CodingKeys.mayaEKYCV6ShortenedFlowBlurDetectionEnabled.rawValue)?.boolValue
        mayaEKYCV6ShortenedFlowAddressDropdownEnabled = config.value(for: CodingKeys.mayaEKYCV6ShortenedFlowAddressDropdownEnabled.rawValue)?.boolValue
        mayaEKYCV6ShortenedFlowForeignAddressComplianceEnabled = config.value(for: CodingKeys.mayaEKYCV6ShortenedFlowForeignAddressComplianceEnabled.rawValue)?.boolValue
        mayaEKYCV6ShortenedFlowPhilsysEnabled = config.value(for: CodingKeys.mayaEKYCV6ShortenedFlowPhilsysEnabled.rawValue)?.boolValue
        mayaEKYCV6KycIdImageQualityEnabled = config.value(for: CodingKeys.mayaEKYCV6KycIdImageQualityEnabled.rawValue)?.boolValue
        mayaRegistrationAutoProvisionEnabled = config.value(for: CodingKeys.mayaRegistrationAutoProvisionEnabled.rawValue)?.boolValue
        mayaEKYCV6PartnerOnboardingEnabled = config.value(for: CodingKeys.mayaEKYCV6PartnerOnboardingEnabled.rawValue)?.boolValue
        mayaEKYCV6OnboardingAndKYCFlow = config.value(for: CodingKeys.mayaEKYCV6OnboardingAndKYCFlow.rawValue)?.boolValue
        mayaEKYCV6OnboardingAndKYCFlowV2 = config.value(for: CodingKeys.mayaEKYCV6OnboardingAndKYCFlowV2.rawValue)?.boolValue
        mayaEKYCV6ForcePHNationalID = config.value(for: CodingKeys.mayaEKYCV6ForcePHNationalID.rawValue)?.boolValue
        mayaComplianceFatca = config.value(for: CodingKeys.mayaComplianceFatca.rawValue)?.boolValue
        mayaComplianceDosri = config.value(for: CodingKeys.mayaComplianceDosri.rawValue)?.boolValue
        mayaRegistrationRequireMiddleName = config.value(for: CodingKeys.mayaRegistrationRequireMiddleName.rawValue)?.boolValue
        mayaMFAFrameworkV1Enabled = config.value(for: CodingKeys.mayaMFAFrameworkV1Enabled.rawValue)?.boolValue
        mayaMFAFaceAuthFormEnabled = config.value(for: CodingKeys.mayaMFAFaceAuthFormEnabled.rawValue)?.boolValue
        mayaForgotPasswordFrameworkV2Enabled = config.value(for: CodingKeys.mayaForgotPasswordFrameworkV2Enabled.rawValue)?.boolValue
        mayaShopProviderSubcatPillsEnabled = config.value(for: CodingKeys.mayaShopProviderSubcatPillsEnabled.rawValue)?.boolValue
        initialEligibilityCheckEnabled = config.value(for: CodingKeys.initialEligibilityCheckEnabled.rawValue)?.boolValue
        mayaPayBillsPayWithMayaCreditEnabled = config.value(for: CodingKeys.mayaPayBillsPayWithMayaCreditEnabled.rawValue)?.boolValue
        mayaPayBillsPayWithMayaCreditNewTagEnabled = config.value(for: CodingKeys.mayaPayBillsPayWithMayaCreditNewTagEnabled.rawValue)?.boolValue
        mayaPayBillsPayWithMayaCreditSoonEnabled = config.value(for: CodingKeys.mayaPayBillsPayWithMayaCreditSoonEnabled.rawValue)?.boolValue
        mayaPayBillsClientApiCheckoutFlowV3Enabled = config.value(for: CodingKeys.mayaPayBillsClientApiCheckoutFlowV3Enabled.rawValue)?.boolValue
        mayaBillsPaySkipWalletValidationEnabled = config.value(for: CodingKeys.mayaBillsPaySkipWalletValidationEnabled.rawValue)?.boolValue
        billsPayHandleOtherFieldsFromDeepLinkEnabled = config.value(for: CodingKeys.billsPayHandleOtherFieldsFromDeepLinkEnabled.rawValue)?.boolValue
        billsPayDisableAccountNumberFieldEnabled = config.value(for: CodingKeys.billsPayDisableAccountNumberFieldEnabled.rawValue)?.boolValue
        billsPayFavoritesWithCreatedPaymentEnabled = config.value(for: CodingKeys.billsPayFavoritesWithCreatedPaymentEnabled.rawValue)?.boolValue
        iclDropdownV2Enabled = config.value(for: CodingKeys.iclDropdownV2Enabled.rawValue)?.boolValue
        iclPhaseTwoV1Enabled = config.value(for: CodingKeys.iclPhaseTwoV1Enabled.rawValue)?.boolValue
        iclPhaseTwoV2Enabled = config.value(for: CodingKeys.iclPhaseTwoV2Enabled.rawValue)?.boolValue
        bnplDeepFreezeEnabled = config.value(for: CodingKeys.bnplDeepFreezeEnabled.rawValue)?.boolValue
        inAppReviewEnabled = config.value(for: CodingKeys.inAppReviewEnabled.rawValue)?.boolValue
        inAppReviewSendMoneyEnabled = config.value(for: CodingKeys.inAppReviewSendMoneyEnabled.rawValue)?.boolValue
        fundsCreatorStoreEnabled = config.value(for: CodingKeys.fundsCreatorStoreEnabled.rawValue)?.boolValue
        requestMoneyTileUpdateEnabled = config.value(for: CodingKeys.requestMoneyTileUpdateEnabled.rawValue)?.boolValue
        userProfileUpdateV1Enabled = config.value(for: CodingKeys.userProfileUpdateV1Enabled.rawValue)?.boolValue
        iclMothersMaidenNameEnabled = config.value(for: CodingKeys.iclMothersMaidenNameEnabled.rawValue)?.boolValue
        loadV2Enabled = config.value(for: CodingKeys.loadV2Enabled.rawValue)?.boolValue
        loadV2FavoritesEnabled = config.value(for: CodingKeys.loadV2FavoritesEnabled.rawValue)?.boolValue
        loadV2RecommenderEnabled = config.value(for: CodingKeys.loadV2RecommenderEnabled.rawValue)?.boolValue
        iclMothersMaidenNameV2Enabled = config.value(for: CodingKeys.iclMothersMaidenNameV2Enabled.rawValue)?.boolValue
        iclMothersMaidenNameV3Enabled = config.value(for: CodingKeys.iclMothersMaidenNameV3Enabled.rawValue)?.boolValue
        loansCreditCardEnabled = config.value(for: CodingKeys.loansCreditCardEnabled.rawValue)?.boolValue
        iclDeviceScoringDataReferenceIdEnabled = config.value(for: CodingKeys.iclDeviceScoringDataReferenceIdEnabled.rawValue)?.boolValue
        iclToMambuEnabled = config.value(for: CodingKeys.iclToMambuEnabled.rawValue)?.boolValue
        iclDblEnabled = config.value(for: CodingKeys.iclDblEnabled.rawValue)?.boolValue
        loansCreditCardDetailsV2Enabled = config.value(for: CodingKeys.loansCreditCardDetailsV2Enabled.rawValue)?.boolValue
        ccMayaBlackEnabled = config.value(for: CodingKeys.ccMayaBlackEnabled.rawValue)?.boolValue
        bannerCorrelationHeaderEnabled = config.value(for: CodingKeys.bannerCorrelationHeaderEnabled.rawValue)?.boolValue
        voucherDetailsV2Enabled = config.value(for: CodingKeys.voucherDetailsV2Enabled.rawValue)?.boolValue
        creditCardCashbackTileEnabled = config.value(for: CodingKeys.creditCardCashbackTileEnabled.rawValue)?.boolValue
        ecommAppEventV2QrEnabled = config.value(for: CodingKeys.ecommAppEventV2QrEnabled.rawValue)?.boolValue
        ecommAppEventV2ShopEnabled = config.value(for: CodingKeys.ecommAppEventV2ShopEnabled.rawValue)?.boolValue
        ecommAppEventV2BillsPayEnabled = config.value(for: CodingKeys.ecommAppEventV2BillsPayEnabled.rawValue)?.boolValue
        loyaltyForceUpdateEnabled = config.value(for: CodingKeys.loyaltyForceUpdateEnabled.rawValue)?.boolValue
        loginHistoryTTL = config.value(for: CodingKeys.loginHistoryTTL.rawValue)?.stringValue
        appEventsV2CashInEnabled = config.value(for: CodingKeys.appEventsV2CashInEnabled.rawValue)?.boolValue
        freezeCardSpiel = config.value(for: CodingKeys.freezeCardSpiel.rawValue)?.stringValue
        unfreezeCardSpiel = config.value(for: CodingKeys.unfreezeCardSpiel.rawValue)?.stringValue
        freezeConfirmationSpiel = config.value(for: CodingKeys.freezeConfirmationSpiel.rawValue)?.stringValue
        unfreezeConfirmationSpiel = config.value(for: CodingKeys.unfreezeConfirmationSpiel.rawValue)?.stringValue
        freezeSuccessSpiel = config.value(for: CodingKeys.freezeSuccessSpiel.rawValue)?.stringValue
        unfreezeSuccessSpiel = config.value(for: CodingKeys.unfreezeSuccessSpiel.rawValue)?.stringValue
        cardPullV2Enabled = config.value(for: CodingKeys.cardPullV2Enabled.rawValue)?.boolValue
        loansICLDeviceScoringEnabled = config.value(for: CodingKeys.loansICLDeviceScoringEnabled.rawValue)?.boolValue
        mayaCreditIosDeviceScoringEnabled = config.value(for: CodingKeys.mayaCreditIosDeviceScoringEnabled.rawValue)?.boolValue
        ccDeviceScoringEnabled = config.value(for: CodingKeys.ccDeviceScoringEnabled.rawValue)?.boolValue
        landersCreditCardEnabled = config.value(for: CodingKeys.landersCreditCardEnabled.rawValue)?.boolValue
        cardDetailsProtectionEnabled = config.value(for: CodingKeys.cardDetailsProtectionEnabled.rawValue)?.boolValue
    }
}

extension PayMayaRemoteConfig: EncryptedDefaultsStorable {}
