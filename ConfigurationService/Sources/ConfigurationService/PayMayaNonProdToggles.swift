//
//  PayMayaNonProdToggles.swift
//  PayMaya
//
//  Created by <PERSON> on 12/12/19.
//  Copyright © 2019 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation

/**
    Toggles are exclusively for development only. Values declared here are generated in Toggles.plist.
    Once feature listed here is ready for production release, the corresponding toggle for that feature should be removed.
 */
public enum PayMayaNonProdToggles: String {
    // MARK: Yasha
    case kycPhase1

    // MARK: Helix
    case reKYC10MinutesTest
    case showIDValScore
    case showIDQualityScore
    case kycPersonalInfoV2

    // MARK: EKYC-Zoloz
    case mockedPreSubmissionResuestData
    case skipZolozFlow

    // MARK: Velaryon
    case mayaCreditAccountSummaryAdd
    case mayaCreditPartnerMerchant

    // MARK: CHARMANDER
    case walletTransactionLimitWalletSettingsOption
    case instaFillWalletSettingsOption

    // MARK: BFEAT
    case mayaPayBillsParsePaymentMethods
    case billsPayInitialReceiptEntryPoint

    // MARK: ECOMM
    case freelancerHub
}
