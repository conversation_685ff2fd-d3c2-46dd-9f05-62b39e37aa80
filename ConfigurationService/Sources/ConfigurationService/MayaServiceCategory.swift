//
//  MayaServiceCategory.swift
//  PayMaya
//
//  Created by <PERSON> on 3/8/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.

import Foundation

public struct MayaServiceCategory: Codable, Equatable {
    public let id: String
    public let name: String
    public let serviceIDs: [String]
    public let backgroundColorString: String

    enum CodingKeys: String, CodingKey {
        case id
        case name
        case serviceIDs = "service_ids"
        case backgroundColorString = "bg_color"
    }

    public init(
        id: String,
        name: String,
        serviceIDs: [String],
        backgroundColorString: String
    ) {
        self.id = id
        self.name = name
        self.serviceIDs = serviceIDs
        self.backgroundColorString = backgroundColorString
    }
}

public struct MayaServiceCategoryResponse: Codable, Equatable {
    public let coreServiceIDs: [String]
    public let categories: [MayaServiceCategory]
    public let bannerIndex: Int?

    enum CodingKeys: String, CodingKey {
        case coreServiceIDs = "core_tiles"
        case categories
        case bannerIndex = "banner"
    }

    public init(coreServiceIDs: [String], categories: [MayaServiceCategory], bannerIndex: Int? = nil) {
        self.coreServiceIDs = coreServiceIDs
        self.categories = categories
        self.bannerIndex = bannerIndex
    }
}
