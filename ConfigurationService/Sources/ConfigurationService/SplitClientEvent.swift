//
//  SplitClientEvent.swift
//
//
//  Created by <PERSON> on 11/11/24.
//

import Split

public enum SplitClientEvent: Equatable {
    /// The inital uninitialized state
    case initial

    /// Client being created
    case preparing

    /// Client created, but not yet read
    case created

    /// C<PERSON> only has cached values, but these may not be updated
    case readyFromCache

    /// Client has retrieved latest values. This may still be called after readyTimedOut.
    case ready

    /// Client timeout reached before being ready
    case readyTimedOut

    /// Client values (segments, target rules) updated in real-time
    case updated

    /// Client destroyed, only applicable for user client
    case destroyed

    /// Client status unknown
    case unknown

    init(event: SplitEvent) {
        switch event {
        case .sdkReadyFromCache: self = .readyFromCache
        case .sdkReady: self = .ready
        case .sdkReadyTimedOut: self = .readyTimedOut
        case .sdkUpdated: self = .updated
        }
    }
}

public extension SplitClientEvent {
    var traceName: String {
        switch self {
        case .ready: "split_sdk_ready"
        case .readyFromCache: "split_sdk_ready_cache"
        default: ""
        }
    }
}

extension SplitClientEvent {
    var isReady: Bool {
        switch self {
        case .ready, .updated: true
        default: false
        }
    }
}
