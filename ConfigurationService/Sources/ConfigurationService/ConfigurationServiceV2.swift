//
//  ConfigurationServiceV2.swift
//  
//
//  Created by <PERSON> on 6/3/24.
//

import Analytics
import AssetProvider
import Foundation
import Injector
import ReactiveSwift
import Split

typealias Logger = ConfigurationServiceLogger

public class ConfigurationServiceV2 {
    @Inject private var analyticsService: AnalyticsService

    private let splitProvider: SplitConfigurationProvider
    private let localProvider: LocalConfigurationProvider
    private let disposeBag = CompositeDisposable()

    // MARK: Base App
    public lazy var actionCardsEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .actionCards, defaultValue: false)
    public lazy var bannerCorrelationHeaderEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .bannerCorrelationHeader, defaultValue: false)
    public lazy var changePasswordSwiftUIEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .changePasswordSwiftUI, defaultValue: false)
    public lazy var creatorStoreBannersEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .creatorStoreBanners, defaultValue: false)
    public lazy var creatorStoreInterstitialBannersEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .creatorStoreInterstitialBanners, defaultValue: false)
    public lazy var dcrnSessionHeaderEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .dcrnSessionHeader, defaultValue: false)
    public lazy var dcrnVerifyHeaderEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .dcrnVerifyHeader, defaultValue: false)
    public lazy var ddpLauncherEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .ddpLauncher, defaultValue: false)
    public lazy var deviceManagementEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .deviceManagement, defaultValue: false)
    public lazy var easyCreditBannerEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .easyCreditBanner, defaultValue: false)
    public lazy var externalLinkPromptEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .externalLinkPrompt, defaultValue: false)
    public lazy var maintenanceBypassEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .maintenanceBypass, defaultValue: false)
    public lazy var profileCleverTapUnliBannersEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .profileClevertapUnliBanners, defaultValue: false)
    public lazy var rateThisAppButtonEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .rateThisAppButton, defaultValue: false)
    public lazy var servicesChatWithMayaEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .servicesChatWithMaya, defaultValue: false)
    public lazy var servicesClevertapUnliBannerEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .servicesClevertapUnliBanners, defaultValue: false)
    public lazy var dashboardNavigationRefactorEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .dashboardNavigationRefactor, defaultValue: false)
    public lazy var walletRewardsReorderExperimentation: CSConfigV2<String> = createStringConfig(forToggle: .walletRewardsReorder, defaultValue: MayaToggle.walletRewardsReorder.experiment?.controlKey ?? "")
    public lazy var cleverTapUnliBannersEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .clevertapUnliBanners, defaultValue: false)
    public lazy var splitBlockingTimeout: CSConfigV2<Int> = createIntConfig(forToggle: .splitBlockingTimeout, defaultValue: 300)

    // MARK: Crypto SDK
    public lazy var cryptoV2Enabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .cryptoV2, defaultValue: false)

    // MARK: Lending
    public lazy var ccCreditScoringEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .ccCreditScoring, defaultValue: false)
    public lazy var ccSkipCreditScoringEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .ccSkipCreditScoring, defaultValue: false)
    public lazy var ccDBLEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .ccDBL, defaultValue: false)
    public lazy var iclDeviceScoringEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .iclDeviceScoring, defaultValue: false)
    public lazy var iclMfaEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .iclMfa, defaultValue: false)
    public lazy var iclGeolocationEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .iclGeolocation, defaultValue: false)
    public lazy var lendingDataExtractionEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .lendingDataExtraction, defaultValue: false)
    public lazy var mecAPIGatewayEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .mecAPIGateway, defaultValue: false)
    public lazy var mecBannerV2Enabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .mecBannerV2, defaultValue: false)
    public lazy var mecDeviceScoringEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .mecDeviceScoring, defaultValue: false)
    public lazy var mecGeolocationEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .mecGeolocation, defaultValue: false)
    public lazy var mecMfaEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .mecMfa, defaultValue: false)
    public lazy var mecWriteOffEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .mecWriteOff, defaultValue: false)
    public lazy var pfMvpEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .pfMvp, defaultValue: false)

    // MARK: CMS LYNX
    public lazy var mayaBlackCreditCardEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .mayaBlackCreditCard, defaultValue: false)
    public lazy var mayaBlackIntroEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .mayaBlackIntro, defaultValue: false)
    public lazy var convertToInstallmentEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .convertToInstallment, defaultValue: false)
    public lazy var landersCreditCardEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .landersCreditCard, defaultValue: false)
    public lazy var cardDetailsProtectionEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .cardDetailsProtection, defaultValue: false)

    // MARK: Mewtwo
    public lazy var securityCenterEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .securityCenter, defaultValue: false)
    public lazy var accountFreezeStatusEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .accountFreezeStatus, defaultValue: false)
    public lazy var shortenedRegistrationEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .shortenedRegistration, defaultValue: false)
    public lazy var landingPageV2Enabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .landingPageV2, defaultValue: false)

    // MARK: Helix
    public lazy var upgradeNowLabel: CSConfigV2<String> = createStringConfig(forToggle: .upgradeNowLabel, defaultValue: CommonStrings.Common.Upgrade.now)
    public lazy var welcomeButtonLabel: CSConfigV2<String> = createStringConfig(forToggle: .welcomeButtonLabel, defaultValue: "")
    public lazy var changeWelcomeBackButtonEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .changeWelcomeBackButton, defaultValue: false)
    public lazy var showWelcomeDoItLaterButtonEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .showWelcomeDoItLaterButton, defaultValue: false)
    public lazy var kycTofuUXRedesignEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .kycTofuUXRedesign, defaultValue: false)
    public lazy var kycCMSv8MockEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .mockCMSV8, defaultValue: false)
    public lazy var regToKYCFlowEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .regToKYCFlow, defaultValue: false)

    // MARK: Anbu
    public lazy var billsCategoryAdBannerEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .billsCategoryAdBanner, defaultValue: false)
    public lazy var autoDebitArrangementSdkEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .autoDebitArrangementSdk, defaultValue: false)
    public lazy var loadMinDetectionEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .loadMinDetection, defaultValue: false)

    // MARK: Rubeus
    public lazy var interstitialManualConfigServiceEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .interstitialManualConfigService, defaultValue: false)
    public lazy var blackpinkGiveawayDeeplinkEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .blackpinkGiveawayDeepLinkGuard, defaultValue: false)
    public lazy var mgmV2ForceUpdateEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .mgmV2ForceUpdate, defaultValue: false)
    public lazy var mgmV2Enabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .mgmV2, defaultValue: false)

    // MARK: Inbox
    public lazy var inboxTicketingEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .inboxTicketing, defaultValue: false)
    public lazy var sendbirdCachingCollectionEnabled: CSConfigV2<Bool> = createBoolConfig(forToggle: .sendBirdCachingCollection, defaultValue: false)

    // MARK: Initializer
    public init(configuration: Configuration) {
        self.splitProvider = .init(sdkKey: configuration.sdkKey)
        self.localProvider = .init()
    }

    // MARK: Client helper methods
    public func createUserClient(key: String) {
        splitProvider.createUserClient(key: key)
    }

    public func destroyUserClient() {
        splitProvider.destroyUserClient()
    }

    // MARK: Attribute methods
    public func setClientAttributes(_ values: [String: Any]) {
        splitProvider.setAttributes(values)
    }

    public func clearClientAttributes() {
        splitProvider.clearAttributes()
    }

    // MARK: Analytics methods
    @discardableResult
    public func track(for toggle: MayaToggle, eventName: String, attributes: [String: Any]? = nil) -> Bool {
        splitProvider.track(for: toggle, eventName: eventName, attributes: attributes)
    }

    // MARK: Event helper methods
    /// Returns the property of the current client event for a particular traffic type.
    public func getEventProperty(trafficType: MayaToggle.TrafficType) -> MutableProperty<SplitClientEvent> {
        splitProvider.getEventProperty(trafficType: trafficType)
    }

    // MARK: Private helper methods
    private func createBoolConfig(forToggle toggle: MayaToggle, defaultValue: Bool) -> CSConfigV2<Bool> {
        return createConfig(
            forToggle: toggle,
            localConfig: { [weak self] in self?.localProvider.getBoolValue(for: toggle) },
            splitConfig: { [weak self] attributes in
                self?.splitProvider.getBoolValue(for: toggle, attributes: attributes)
            },
            defaultConfig: {
                Logger.log(message: "Fetched \(defaultValue) default bool value for \(toggle.title)")
                return defaultValue
            }
        )
    }

    private func createStringConfig(forToggle toggle: MayaToggle, defaultValue: String) -> CSConfigV2<String> {
        return createConfig(
            forToggle: toggle,
            localConfig: { [weak self] in self?.localProvider.getStringValue(for: toggle) },
            splitConfig: { [weak self] attributes in
                self?.splitProvider.getStringValue(for: toggle, attributes: attributes)
            },
            defaultConfig: {
                Logger.log(message: "Fetched \(defaultValue) default string value for \(toggle.title)")
                return defaultValue
            }
        )
    }

    private func createIntConfig(forToggle toggle: MayaToggle, defaultValue: Int) -> CSConfigV2<Int> {
        return createConfig(
            forToggle: toggle,
            localConfig: { [weak self] in self?.localProvider.getIntValue(for: toggle) },
            splitConfig: { [weak self] attributes in
                self?.splitProvider.getIntValue(for: toggle, attributes: attributes)
            },
            defaultConfig: {
                Logger.log(message: "Fetched \(defaultValue) default int value for \(toggle.title)")
                return defaultValue
            }
        )
    }

    private func createDoubleConfig(forToggle toggle: MayaToggle, defaultValue: Double) -> CSConfigV2<Double> {
        return createConfig(
            forToggle: toggle,
            localConfig: { [weak self] in self?.localProvider.getDoubleValue(for: toggle) },
            splitConfig: { [weak self] attributes in
                self?.splitProvider.getDoubleValue(for: toggle, attributes: attributes)
            },
            defaultConfig: {
                Logger.log(message: "Fetched \(defaultValue) default double value for \(toggle.title)")
                return defaultValue
            }
        )
    }

    private func createDictionaryConfig(forToggle toggle: MayaToggle, defaultValue: [String: Any]) -> CSConfigV2<[String: Any]> {
        return createConfig(
            forToggle: toggle,
            localConfig: { [weak self] in self?.localProvider.getDictionaryValue(for: toggle) },
            splitConfig: { [weak self] attributes in
                self?.splitProvider.getDictionaryValue(for: toggle, attributes: attributes)
            },
            defaultConfig: {
                Logger.log(message: "Fetched \(defaultValue) default dict value for \(toggle.title)")
                return defaultValue
            }
        )
    }

    private func createConfig<T>(
        forToggle toggle: MayaToggle,
        localConfig: @escaping CSConfigV2<T>.LocalConfigClosure,
        splitConfig: @escaping CSConfigV2<T>.SplitConfigClosure,
        defaultConfig: @escaping CSConfigV2<T>.DefaultConfigClosure
    ) -> CSConfigV2<T> {
        var localConfig: CSConfigV2<T>.LocalConfigClosure? = {
            if toggle.generateBothLocalAndSplitToggle {
                localConfig
            } else if toggle.splitKey == nil {
                localConfig
            } else {
                nil
            }
        }()
        #if PROD_TARGET
        localConfig = nil
        #endif

        let splitConfig = toggle.splitKey != nil ? splitConfig : nil

        return CSConfigV2<T>(localConfig: localConfig, splitConfig: splitConfig, defaultConfig: defaultConfig) { [weak self] finalValue, localValue, splitValue, defaultValue in
            guard let self, let splitKey = toggle.splitKey else { return }

            let clientIsReady = splitProvider.getEventProperty(trafficType: toggle.trafficType).value.isReady

            let getAttributeValue: (T?) -> Any? = { value in
                guard let value else { return nil }
                if value is Bool || value is Int || value is Double || value is String {
                    return value
                } else {
                    return "\(value)"
                }
            }

            let allAttributes: [String: Any?] = [
                AnalyticsAttributeKey.clientIsReady.rawValue: clientIsReady,
                AnalyticsAttributeKey.dataType.rawValue: toggle.dataType.analyticsValue,
                AnalyticsAttributeKey.defaultValue.rawValue: getAttributeValue(defaultValue),
                AnalyticsAttributeKey.localValue.rawValue: getAttributeValue(localValue),
                AnalyticsAttributeKey.finalValue.rawValue: getAttributeValue(finalValue),
                AnalyticsAttributeKey.key.rawValue: splitKey,
                AnalyticsAttributeKey.remoteValue.rawValue: getAttributeValue(splitValue) ?? "control",
                AnalyticsAttributeKey.trafficType.rawValue: toggle.trafficType.analyticsValue
            ]
            let attributes = allAttributes.compactMapValues { $0 }

            analyticsService.logMayaEvents(action: .success(Analytics.Split.Flag.fetch), attributes: attributes)
        }
    }
}
