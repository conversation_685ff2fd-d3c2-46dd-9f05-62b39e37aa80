//
//  AppConfig.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 12/16/2021.
//  Copyright © 2018 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation
import StoreProvider
import UIKit

/// Fetched values from s3 config containing toggles, maintenance, and service availability
public struct AppConfig: Codable {
    let maintenance: Maintenance
    let shop: Shop?
    let referral: Referral?
    let mgm: MGM?
    let kycVideo: KYCVideo?
    let mayaServices: [Service]?
    let mayaServicesCategory: MayaServiceCategoryResponse?
    let dashboardSettings: DashboardSettings?

    let bankPullFunds: BankPullFunds?
    let refreshSessionDuration: TimeInterval?
    let appLocalTimeoutDuration: TimeInterval?
    let profileBadges: ProfileBadges?
    let loginHistoryTTL: String?

    let glimpse: EnabledConfig?
    let instapayQR: EnabledConfig?
    let changeMin: EnabledConfig?
    let whitelistedAnalyticsEventsV2: EnabledConfig?
    let topUpService: EnabledConfig?
    let yearInReview: EnabledConfig?
    let whiteTheme: EnabledConfig?
    let amvcFingerprinting: EnabledConfig?
    let digitalOR: EnabledConfig?
    let edd: EnabledConfig?
    let idClassification: EnabledConfig?
    let mandatoryIdClassification: EnabledConfig?
    let cashInV2: EnabledConfig?
    let amountFormatterV2: EnabledConfig?
    let bankPullFundsV2: EnabledConfig?
    let cashInWithAccountLimit: EnabledConfig?
    let crypto: EnabledConfig?
    let maya: Maya?
    let reKYC: EnabledConfig?
    let customerRestrictions: EnabledConfig?
    let kycCompletedCallback: EnabledConfig?
    let registrationRestrictions: EnabledConfig?
    let eddRestrictions: EnabledConfig?
    let showOtherIDs: EnabledConfig?
    let registrationMGM: EnabledConfig?
    let mayaLoansV3: EnabledConfig?
    let mayaLoansSimplifiedCheckout: EnabledConfig?
    let cashInBankPullFundsV3: EnabledConfig?
    let funds: EnabledConfig?
    let mayaLoansCashInstallment: EnabledConfig?
    let payBillsV3Endpoint: EnabledConfig?
    let mayaBillerReminder: EnabledConfig?
    let bdoCashIn: EnabledConfig?
    let inboxFlutter: EnabledConfig?
    let inboxManageNotifications: EnabledConfig?
    let inboxSupport: EnabledConfig?
    let virtualNumberRegistration: EnabledConfig?
    let locationBasedVirtualNumberRegistration: EnabledConfig?
    let unifiedLendingExperienceReskin: EnabledConfig?
    let mayaEKYCV6: MayaEKYCV6?
    let mayaCompliance: MayaCompliance?
    let userProfile: UserProfile?
    let mayaRegistration: MayaRegistration?
    let mayaMFA: MayaMFA?
    let mayaForgotPassword: MayaForgotPassword?
    let mayaShopProviderSubcatPills: EnabledConfig?
    let iclDropdownV2: EnabledConfig?
    let initialEligibilityCheck: EnabledConfig?
    let mayaPayBillsPayWithMayaCredit: EnabledConfig?
    let mayaPayBillsPayWithMayaCreditNewTag: EnabledConfig?
    let mayaPayBillsPayWithMayaCreditSoon: EnabledConfig?
    let mayaPayBillsClientApiCheckoutFlowV3: EnabledConfig?
    let billsPayHandleOtherFieldsFromDeepLink: EnabledConfig?
    let billsPayDisableAccountNumberField: EnabledConfig?
    let billsPayFavoritesWithCreatedPayment: EnabledConfig?
    let mayaBillsPaySkipWalletValidation: EnabledConfig?
    let iclPhaseTwoV1: EnabledConfig?
    let iclPhaseTwoV2: EnabledConfig?
    let bnplDeepFreeze: EnabledConfig?
    let inAppReview: InAppReview?
    let fundsCreatorStore: EnabledConfig?
    let requestMoneyTileUpdate: EnabledConfig?
    let savings: Savings?
    let iclMothersMaidenName: EnabledConfig?
    let loadV2: EnabledConfig?
    let loadV2Config: LoadV2Config?
    let iclMothersMaidenNameV2: EnabledConfig?
    let iclMothersMaidenNameV3: EnabledConfig?
    let loansCreditCard: EnabledConfig?
    let iclDeviceScoringDataReferenceId: EnabledConfig?
    let iclToMambu: EnabledConfig?
    let iclDbl: EnabledConfig?
    let loansCreditCardDetailsV2: EnabledConfig?
    let ccMayaBlack: EnabledConfig?
    let bannerCorrelationHeader: EnabledConfig?
    let voucherDetailsV2: EnabledConfig?
    let creditCardCashbackTile: EnabledConfig?
    let ecommAppEventV2Config: EcommAppEventV2Config?
    let bankTransferFee: Decimal?
    let appEventsV2: appEventsV2?
    let loyaltyForceUpdate: EnabledConfig?
    let securityCenter: SecurityCenter?
    let cardPullV2: EnabledConfig?
    let loansICLDeviceScoring: EnabledConfig?
    let mayaCreditIosDeviceScoring: EnabledConfig?
    let ccDeviceScoring: EnabledConfig?
    let landersCreditCard: EnabledConfig?
    let cardDetailsProtection: EnabledConfig?

    enum CodingKeys: String, CodingKey {
        case maintenance
        case shop = "shop"
        case referral = "referral"
        case mgm = "mgm"
        case kycVideo = "kyc_video"
        case mayaServices = "maya_services_master_list"
        case mayaServicesCategory = "maya_services_category_v2"
        case dashboardSettings = "dashboard_settings"

        case bankPullFunds = "pull_funds"
        case refreshSessionDuration = "refresh_session_duration"
        case appLocalTimeoutDuration = "app_local_timeout"
        case profileBadges = "profile_badges"
        case loginHistoryTTL = "login_history_ttl"

        case glimpse
        case instapayQR = "instapay_qr"
        case changeMin = "change_min"
        case whitelistedAnalyticsEventsV2 = "whitelisted_analytics_events_v2"
        case topUpService = "top_up_service"
        case yearInReview = "year_in_review"
        case whiteTheme = "white_navigation_bar"
        case amvcFingerprinting = "amvc_fingerprinting"
        case digitalOR = "shop_purchase_digital_or"
        case edd = "edd"
        case idClassification = "id_classification"
        case mandatoryIdClassification = "mandatory_id_classification"
        case cashInV2 = "cash_in_v2"
        case amountFormatterV2 = "amount_formatter_v2"
        case bankPullFundsV2 = "bank_pull_v2"
        case cashInWithAccountLimit = "cash_in_with_account_limit"
        case crypto = "crypto"
        case maya = "maya_enabled"
        case reKYC = "re_kyc"
        case customerRestrictions = "customer_restrictions"
        case kycCompletedCallback = "kyc_completed_callback"
        case registrationRestrictions = "registration_restrictions"
        case eddRestrictions = "edd_restrictions"
        case showOtherIDs = "show_other_ids_zoloz"
        case registrationMGM = "registration_submit_invite_code"
        case mayaLoansV3 = "loans_v3"
        case mayaLoansSimplifiedCheckout = "loans_simplified_checkout"
        case cashInBankPullFundsV3 = "cashin_bank_pull_v3"
        case funds = "funds"
        case mayaLoansCashInstallment = "installment_cash_loans"
        case payBillsV3Endpoint = "pay_bills_v3_endpoint"
        case mayaBillerReminder = "maya_biller_reminder"
        case bdoCashIn = "bank_pull_settings_v2"
        case inboxFlutter = "inbox_v4"
        case inboxManageNotifications = "inbox_v4_manage_notifs"
        case inboxSupport = "inbox_v4_maya_support"
        case virtualNumberRegistration = "virtual_number_registration"
        case locationBasedVirtualNumberRegistration = "location_based_virtual_number_registration_v2"
        case unifiedLendingExperienceReskin = "unified_lending_experience_reskin"
        case mayaEKYCV6 = "maya_ekyc_v6"
        case mayaCompliance = "maya_compliance"
        case mayaRegistration = "maya_registration"
        case mayaMFA = "maya_mfa"
        case mayaForgotPassword = "maya_forgot_password"
        case mayaShopProviderSubcatPills = "maya_shop_provider_subcategories"
        case iclDropdownV2 = "icl_drop_down_v2"
        case initialEligibilityCheck = "initial_eligibility_check"
        case mayaPayBillsPayWithMayaCredit = "maya_paybills_pay_with_maya_credit"
        case mayaPayBillsPayWithMayaCreditNewTag = "maya_paybills_pay_with_maya_credit_new_tag"
        case mayaPayBillsPayWithMayaCreditSoon = "maya_paybills_pay_with_maya_credit_soon"
        case mayaPayBillsClientApiCheckoutFlowV3 = "maya_billspay_client_api_checkout_flow_v3"
        case mayaBillsPaySkipWalletValidation = "maya_bills_pay_skip_wallet_validation"
        case billsPayHandleOtherFieldsFromDeepLink = "bills_pay_handle_other_fields_from_deep_link"
        case billsPayDisableAccountNumberField = "bills_pay_disable_account_number_field"
        case billsPayFavoritesWithCreatedPayment = "bills_pay_favorites_with_created_payment"
        case iclPhaseTwoV1 = "icl_phase_two_v1"
        case iclPhaseTwoV2 = "icl_phase_two_v2"
        case bnplDeepFreeze = "bnpl_deep_freeze"
        case inAppReview = "in_app_review_enabled"
        case fundsCreatorStore = "funds_creator_store"
        case requestMoneyTileUpdate = "request_money_tile_update"
        case userProfile = "user_profile"
        case savings = "savings"
        case iclMothersMaidenName = "icl_mothers_maiden_name"
        case loadV2 = "load_v2"
        case loadV2Config = "load_v2_config"
        case iclMothersMaidenNameV2 = "icl_mothers_maiden_name_v2"
        case iclMothersMaidenNameV3 = "icl_mothers_maiden_name_v3"
        case loansCreditCard = "loans_credit_card"
        case iclDeviceScoringDataReferenceId = "icl_device_scoring_data_reference_id"
        case iclToMambu = "icl_to_mambu"
        case iclDbl = "icl_dbl"
        case loansCreditCardDetailsV2 = "loans_credit_card_details_v2"
        case ccMayaBlack = "loans_credit_card_maya_black"
        case bannerCorrelationHeader = "banner_correlation_header"
        case voucherDetailsV2 = "voucher_details_v2"
        case creditCardCashbackTile = "landers_credit_card_cashback_tile"
        case ecommAppEventV2Config = "ecomm_app_event_v2_config"
        case bankTransferFee = "bank_transfer_fee"
        case appEventsV2 = "app_events_v2"
        case loyaltyForceUpdate = "loyalty_force_update"
        case securityCenter = "security_center"
        case cardPullV2 = "card_pull_v2"
        case loansICLDeviceScoring = "icl_device_scoring"
        case mayaCreditIosDeviceScoring = "easy_credit_ios_device_scoring"
        case ccDeviceScoring = "cc_device_scoring"
        case landersCreditCard = "landers_credit_card"
        case cardDetailsProtection = "card_details_protection"
    }

    public struct Maintenance: Codable, MaintenanceProtocol {
        public let start: Date
        public let end: Date
        public let message: String
        public let messageV2: String?
        public let serviceAvailability: ServiceAvailability

        public init(start: Date, end: Date, message: String, messageV2: String? = nil, serviceAvailability: AppConfig.Maintenance.ServiceAvailability) {
            self.start = start
            self.end = end
            self.message = message
            self.messageV2 = messageV2
            self.serviceAvailability = serviceAvailability
        }

        public var isUnderMaintenance: Bool {
            let currentDate = Date()
            return (start.compare(currentDate) == .orderedAscending &&
                end.compare(currentDate) == .orderedDescending)
        }

        enum CodingKeys: String, CodingKey {
            case start
            case end
            case message
            case messageV2 = "message_v2"
            case serviceAvailability = "service_availability"
        }

        public struct ServiceAvailability: Codable {
            public let accountLimits: ServiceMaintenance?
            public let addMoneyViaCard: ServiceMaintenance?
            public let bankCashIn: ServiceMaintenance?
            public let bankTransfer: ServiceMaintenance?
            public let cards: ServiceMaintenance?
            public let funds: ServiceMaintenance?
            public let crypto: ServiceMaintenance?
            public let cryptoV2: ServiceMaintenance?
            public let inbox: ServiceMaintenance?
            public let load: ServiceMaintenance?
            public let luckyGames: ServiceMaintenance?
            public let mayaCredit: ServiceMaintenance?
            public let missions: ServiceMaintenance?
            public let myFavorites: ServiceMaintenance?
            public let payBills: ServiceMaintenance?
            public let purchase: ServiceMaintenance?
            public let sendMoney: ServiceMaintenance?
            public let shop: ServiceMaintenance?
            public let shopPurchaseHistory: ServiceMaintenance?
            public let transport: ServiceMaintenance?
            public let vouchers: ServiceMaintenance?
            public let qrOffUs: ServiceMaintenance?

            public init(
                accountLimits: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                addMoneyViaCard: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                bankCashIn: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                bankTransfer: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                cards: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                funds: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                crypto: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                cryptoV2: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                inbox: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                load: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                luckyGames: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                mayaCredit: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                missions: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                myFavorites: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                payBills: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                purchase: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                sendMoney: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                shop: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                shopPurchaseHistory: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                transport: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                vouchers: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?,
                qrOffUs: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance?
            ) {
                self.accountLimits = accountLimits
                self.addMoneyViaCard = addMoneyViaCard
                self.bankCashIn = bankCashIn
                self.bankTransfer = bankTransfer
                self.cards = cards
                self.funds = funds
                self.crypto = crypto
                self.cryptoV2 = cryptoV2
                self.inbox = inbox
                self.load = load
                self.luckyGames = luckyGames
                self.mayaCredit = mayaCredit
                self.missions = missions
                self.myFavorites = myFavorites
                self.payBills = payBills
                self.purchase = purchase
                self.sendMoney = sendMoney
                self.shop = shop
                self.shopPurchaseHistory = shopPurchaseHistory
                self.transport = transport
                self.vouchers = vouchers
                self.qrOffUs = qrOffUs
            }

            // When you add a new case to this, update Module+Maintenance and ServiceAvailability+Module as well.
            public enum CodingKeys: String, CodingKey {
                case accountLimits = "account_limits"
                case addMoneyViaCard = "add_money_via_card_service"
                case bankCashIn = "cash_in_via_bank_service"
                case bankTransfer = "send_money_via_bank"
                case cards
                case funds
                case crypto
                case cryptoV2 = "crypto_v2"
                case inbox = "inbox"
                case load
                case luckyGames = "lucky_games"
                case mayaCredit = "maya_credit"
                case missions = "missions"
                case myFavorites = "my_favorites"
                case payBills = "pay_bills"
                case purchase = "purchase_service"
                case sendMoney = "send_money"
                case shop = "shop_service"
                case shopPurchaseHistory = "shop_purchase_history"
                case transport = "transport"
                case vouchers = "vouchers_service"
                case qrOffUs = "qr_off_us"
            }

            public struct ServiceMaintenance: Codable, MaintenanceProtocol {
                public let start: Date
                public let end: Date
                public let message: String

                public init(start: Date, end: Date, message: String) {
                    self.start = start
                    self.end = end
                    self.message = message
                }

                public var isUnderMaintenance: Bool {
                    let currentDate = Date()
                    return (start.compare(currentDate) == .orderedAscending &&
                        end.compare(currentDate) == .orderedDescending)
                }
            }
        }
    }

    public struct DashboardSettings: Codable {
        public let tabs: [DashboardTab]
        public let walletTiles: [String]

        public init(tabs: [DashboardTab], walletTiles: [String]) {
            self.tabs = tabs
            self.walletTiles = walletTiles
        }

        enum CodingKeys: String, CodingKey {
            case tabs
            case walletTiles = "wallet_tiles"
        }

        public struct DashboardTab: Codable {
            public let name: String
            public let id: String

            public init(name: String, id: String) {
                self.name = name
                self.id = id
            }

            enum CodingKeys: String, CodingKey {
                case name
                case id
            }
        }
    }

    public struct Shop: Codable {
        public let defaultCategory: String
        public let defaultProductTypes: [String]
        public let home: Home?

        public init(defaultCategory: String, defaultProductTypes: [String], home: AppConfig.Shop.Home?) {
            self.defaultCategory = defaultCategory
            self.defaultProductTypes = defaultProductTypes
            self.home = home
        }

        enum CodingKeys: String, CodingKey {
            case defaultCategory = "ios_default_category"
            case defaultProductTypes = "ios_default_product_types"
            case home
        }

        public struct Home: Codable {
            public let sections: [Section]?

            public init(sections: [AppConfig.Shop.Home.Section]?) {
                self.sections = sections
            }

            enum CodingKeys: String, CodingKey {
                case sections
            }

            public struct Section: Codable, Comparable {
                public let name: String?
                public let position: Int?
                public let listType: String?

                public init(name: String?, position: Int?, listType: String?) {
                    self.name = name
                    self.position = position
                    self.listType = listType
                }

                enum CodingKeys: String, CodingKey {
                    case name
                    case position
                    case listType = "list_type"
                }

                public static func < (lhs: Section, rhs: Section) -> Bool {
                    return lhs.position ?? 0 < rhs.position ?? 0
                }
            }
        }
    }

    public struct Referral: Codable {
        public let spielEnterInviteCode: String
        public let spielInviteFriends: String
        public let spielSuccessInviteCode: String
        public let spielBannerInviteFriends: String
        public let spielShareMessageInviteFriends: String

        public init(spielEnterInviteCode: String, spielInviteFriends: String, spielSuccessInviteCode: String, spielBannerInviteFriends: String, spielShareMessageInviteFriends: String) {
            self.spielEnterInviteCode = spielEnterInviteCode
            self.spielInviteFriends = spielInviteFriends
            self.spielSuccessInviteCode = spielSuccessInviteCode
            self.spielBannerInviteFriends = spielBannerInviteFriends
            self.spielShareMessageInviteFriends = spielShareMessageInviteFriends
        }

        enum CodingKeys: String, CodingKey {
            case spielEnterInviteCode = "enter_invite_code"
            case spielInviteFriends = "invite_friends"
            case spielSuccessInviteCode = "success_invite_code"
            case spielBannerInviteFriends = "banner_invite_friends"
            case spielShareMessageInviteFriends = "share_message_invite_friends"
        }
    }

    public struct MGM: Codable {
        public let enabled: Bool

        public init(enabled: Bool) {
            self.enabled = enabled
        }

        enum CodingKeys: String, CodingKey {
            case enabled = "ios_enabled"
        }
    }

    public struct KYCVideo: Codable {
        public let enabled: Bool
        public let androidUnsupportedVersions: [String]
        public let iosUnsupportedVersions: [String]
        public let agentAvailability: String

        public init(enabled: Bool, androidUnsupportedVersions: [String], iosUnsupportedVersions: [String], agentAvailability: String) {
            self.enabled = enabled
            self.androidUnsupportedVersions = androidUnsupportedVersions
            self.iosUnsupportedVersions = iosUnsupportedVersions
            self.agentAvailability = agentAvailability
        }

        enum CodingKeys: String, CodingKey {
            case enabled
            case androidUnsupportedVersions = "android_unsupported"
            case iosUnsupportedVersions = "ios_unsupported"
            case agentAvailability = "agent_availability"
        }

        public var isVideoEnabled: Bool {
            guard
                enabled,
                let currentOS = UIDevice.current.systemVersion.split(separator: ".").first
            else {
                return false
            }

            return !iosUnsupportedVersions.contains(String(currentOS))
        }
    }

    public struct EnabledConfig: Codable {
        public let enabled: Bool

        public init(enabled: Bool) {
            self.enabled = enabled
        }
    }

    public struct BankPullFunds: Codable {
        public let bpi: BankPullFundsPartner?

        public init(bpi: AppConfig.BankPullFunds.BankPullFundsPartner?) {
            self.bpi = bpi
        }

        enum CodingKeys: String, CodingKey {
            case bpi
        }

        public struct BankPullFundsPartner: Codable {
            public let enabled: Bool
            public let currency: String
            public let limitMin: Double
            public let limitMax: Double

            public init(enabled: Bool, currency: String, limitMin: Double, limitMax: Double) {
                self.enabled = enabled
                self.currency = currency
                self.limitMin = limitMin
                self.limitMax = limitMax
            }

            enum CodingKeys: String, CodingKey {
                case enabled
                case currency
                case limitMin = "limit_min"
                case limitMax = "limit_max"
            }
        }
    }

    struct ProfileBadges: Codable {
        let verifiedSeller: [String]

        enum CodingKeys: String, CodingKey {
            case verifiedSeller = "verified_seller"
        }
    }

    struct Maya: Codable {
        let savings: Bool?
        let cards: Bool?
        let qrP2PAdjustment: Bool?
        let funds: Bool?
        let crypto: Bool?
        let cryptoV2: Bool?
        let username: Bool?
        let pushApproval: Bool?
        let cashInWalletSettingsButton: Bool?
        let cashInWalletSettingsAutoCashInOption: Bool?
        let cashinMayaSavingsButton: Bool?
        let loans: Bool?
        let qrCheckout: Bool?
        let restrictedLogin: Bool?

        enum CodingKeys: String, CodingKey {
            case savings
            case cards
            case qrP2PAdjustment = "qrp2p_adjustment"
            case funds
            case crypto
            case cryptoV2 = "crypto_v2"
            case username
            case pushApproval = "push_approval_2"
            case cashInWalletSettingsButton = "cashin_wallet_settings"
            case cashInWalletSettingsAutoCashInOption = "cashin_wallet_settings_auto_cash_in_option"
            case cashinMayaSavingsButton = "cashin_maya_savings_button"
            case loans
            case qrCheckout = "checkout_on_qr"
            case restrictedLogin = "restricted_login_flow"
        }
    }

    struct MayaEKYCV6: Codable {
        let shortenedFlowUpgrade: Bool?
        let shortenedFlowReKYC: Bool?
        let shortenedFlowShowOtherID: Bool?
        let shortenedFlowIdCaptureV2: Bool?
        let shortenedFlowIdCaptureV3: Bool?
        let shortenedFlowExpandedReKYC: Bool?
        let shortenedFlowBlurDetection: Bool?
        let shortenedFlowBackIDCapture: Bool?
        let shortenedFlowSecondaryIDCapture: Bool?
        let shortenedFlowAddressDropdown: Bool?
        let shortenedFlowForeignAddressCompliance: Bool?
        let kycIdImageQuality: Bool?
        let partnerOnboarding: Bool?
        let onboardingAndKYCFlow: Bool?
        let onboardingAndKYCFlowV2: Bool?
        let kycMaintenance: Bool?
        let shortenedFlowPhilsys: Bool?
        let forcePHNationalID: Bool?
        enum CodingKeys: String, CodingKey {
            case shortenedFlowUpgrade = "shortened_flow_upgrade_ios"
            case shortenedFlowReKYC = "shortened_flow_rekyc_ios"
            case shortenedFlowShowOtherID = "shortened_flow_show_other_id_ios"
            case shortenedFlowIdCaptureV2 = "shortened_flow_id_capture_v2_ios"
            case shortenedFlowIdCaptureV3 = "shortened_flow_id_capture_v3_ios"
            case shortenedFlowExpandedReKYC = "shortened_flow_expanded_rekyc_ios"
            case shortenedFlowBlurDetection = "shortened_flow_blur_detection_ios"
            case shortenedFlowBackIDCapture = "shortened_flow_back_id_capture_ios"
            case shortenedFlowSecondaryIDCapture = "shortened_flow_secondary_id_capture_ios"
            case shortenedFlowForeignAddressCompliance = "shortened_flow_foreign_address_compliance_ios"
            case kycIdImageQuality = "kyc_id_image_quality_v4_ios"
            case partnerOnboarding = "partner_onboarding_ios"
            case onboardingAndKYCFlow = "onboarding_and_kyc_flow_ios"
            case onboardingAndKYCFlowV2 = "onboarding_and_kyc_flow_v2_ios"
            case kycMaintenance = "kyc_maintenance_ios"
            case shortenedFlowAddressDropdown = "shortened_flow_address_dropdown_ios"
            case shortenedFlowPhilsys = "shortened_flow_philsys_v2_ios"
            case forcePHNationalID = "force_ph_national_id_v2_ios"
        }
    }

    struct MayaCompliance: Codable {
        let fatca: Bool?
        let dosri: Bool?

        enum CodingKeys: String, CodingKey {
            case fatca = "fatca_ios"
            case dosri = "dosri_ios"
        }
    }

    struct MayaRegistration: Codable {
        let autoProvision: Bool?
        let consentCheckAll: Bool?
        let requireMiddleName: Bool?

        enum CodingKeys: String, CodingKey {
            case autoProvision = "auto_provision_ios"
            case consentCheckAll = "consent_check_all"
            case requireMiddleName = "require_middle_name_ios"
        }
    }

    struct MayaMFA: Codable {
        let frameworkV1: Bool?
        let faceAuthForm: Bool?

        enum CodingKeys: String, CodingKey {
            case frameworkV1 = "framework_v1_ios"
            case faceAuthForm = "face_auth_form_ios"
        }
    }

    struct MayaForgotPassword: Codable {
        let frameworkV2: Bool?

        enum CodingKeys: String, CodingKey {
            case frameworkV2 = "framework_v2_ios"
        }
    }

    struct InAppReview: Codable {
        let `default`: Bool?
        let sendMoney: Bool?

        enum CodingKeys: String, CodingKey {
            case `default` = "default"
            case sendMoney = "send_money"
        }
    }

    struct UserProfile: Codable {
        let updateV1: Bool?

        enum CodingKeys: String, CodingKey {
            case updateV1 = "update_v1_ios"
        }
    }

    struct Savings: Codable {
        let savingsDormancy: Bool?

        enum CodingKeys: String, CodingKey {
            case savingsDormancy = "savings_dormancy"
        }
    }

    struct LoadV2Config: Codable {
        let favorites: Bool?
        let recommender: Bool?

        enum CodingKeys: String, CodingKey {
            case favorites = "favorites"
            case recommender = "recommender"
        }
    }

    struct EcommAppEventV2Config: Codable {
        let qr: Bool?
        let shop: Bool?
        let billsPay: Bool?

        enum CodingKeys: String, CodingKey {
            case qr = "qr"
            case shop = "shop"
            case billsPay = "bills_pay"
        }
    }

    struct appEventsV2: Codable {
        let bankTransfer: EnabledConfig?
        let sendMoney: EnabledConfig?
        let cashIn: EnabledConfig?

        enum CodingKeys: String, CodingKey {
            case bankTransfer = "bank_transfer"
            case sendMoney = "send_money"
            case cashIn = "cash_in"
        }
    }

    public struct SecurityCenter: Codable {
        let freezeCardSpiel: String?
        let unfreezeCardSpiel: String?
        let freezeConfirmationSpiel: String?
        let unfreezeConfirmationSpiel: String?
        let freezeSuccessSpiel: String?
        let unfreezeSuccessSpiel: String?

        enum CodingKeys: String, CodingKey {
            case freezeCardSpiel = "freeze_card_spiel"
            case unfreezeCardSpiel = "unfreeze_card_spiel"
            case freezeConfirmationSpiel = "freeze_confirmation_spiel"
            case unfreezeConfirmationSpiel = "unfreeze_confirmation_spiel"
            case freezeSuccessSpiel = "freeze_success_spiel"
            case unfreezeSuccessSpiel = "unfreeze_success_spiel"
        }
    }
}

extension AppConfig {
    static let decoder: JSONDecoder = {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.dateFormat = "yyyy-MM-dd'T'HH-mm-ss.SSS'Z'"
        formatter.timeZone = TimeZone(secondsFromGMT: 0)
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .formatted(formatter)
        return decoder
    }()
}

extension AppConfig: EncryptedDefaultsStorable {}
