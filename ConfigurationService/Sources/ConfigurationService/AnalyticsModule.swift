//
//  AnalyticsModule.swift
//  ConfigurationService
//
//  Created by <PERSON> on 6/4/25.
//

import Analytics

enum Analytics {
    enum Split {
        enum Client: String, AnalyticsModule {
            case ready = "READY"
            case cached = "CACHED"

            var name: String { "SPLIT_CLIENT" }
        }

        enum Flag: String, AnalyticsModule {
            case fetch = "FETCH"

            var name: String { "SPLIT_FLAG" }
        }
    }
}
