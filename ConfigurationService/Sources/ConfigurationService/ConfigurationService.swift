//
//
//  ConfigurationService.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 8/28/18.
//  Copyright © 2018 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import APIProvider_Reactive
import CoreLocation
import Error
import FirebaseRemoteConfig
import Injector
import ReactiveSwift
import StoreProvider

/// A service used by the app to fetch and set configuration values from different sources.
///
/// - note:
///     - Toggling rules are in [this confluence page.](https://confluence.paymaya.com/display/ISS/Toggling+Rules)
///     - Any addition to this class should reflect on GeneratedStubConfigurationService.swift.
///
/// Toggle Types
/// ------------
/// **Non-prod:**
/// - PayMayaNonProdToggles - toggles that can be managed via PayMaya app settings
///
/// **Production:**
/// - AppConfig - S3 json containing toggles, maintenance, and service availability
/// - PayMayaRemoteConfig - Firebase cloud service that contains toggles for a more controlled roll-out
///
public class ConfigurationService {
    private enum Constants {
        static let togglePrefix = "non_prod_"
        static let maintenancePrefix = "non_prod_maintenance_"
        static let mockLocationPrefix = "non_prod_mock_location_"
        static let app = "app"
        static let override = "override"
        static let longitude = "longitude"
        static let latitude = "latitude"
        static let officeLocation = CLLocation(latitude: 14.573978602443939, longitude: 121.05408489217993)
    }

    @Inject private var apiProvider: APIProvider
    @Inject private var storeProvider: StoreProvider

    public var getConfigurationAction: Action<Void, AppConfig?, Error>!
    public var getRemoteConfigAction: Action<Void, Void, Swift.Error>!

    private var identifier: ConfigurationServiceStorableIdentifier {
        return ContainerWrapper.shared.resolve(ConfigurationServiceStorableIdentifier.self)
    }

    private var s3Config: AppConfig? {
        guard cachedS3Config == nil else { return cachedS3Config }

        guard let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
              let config = encryptedStore.read(identifier.appConfig, type: AppConfig.self).value
            else { return nil }

        cachedS3Config = config
        return config
    }

    private var remoteConfig: PayMayaRemoteConfig? {
        guard cachedRemoteConfig == nil else { return cachedRemoteConfig }

        guard let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
              let config = encryptedStore.read(identifier.remoteConfig, type: PayMayaRemoteConfig.self).value
            else { return nil }

        cachedRemoteConfig = config
        return config
    }

    private let disposeBag = CompositeDisposable()

    /// Cached config vars to minimize reading from the EncryptedDefaultsStore
    private var cachedS3Config: AppConfig?
    private var cachedRemoteConfig: PayMayaRemoteConfig?

    public var appLocalTimeoutDuration: TimeInterval? {
        return s3Config?.appLocalTimeoutDuration
    }

    public var bankTransferFee: Decimal? {
        return s3Config?.bankTransferFee
    }

    public lazy var isAppMaintenanceEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(nonProdConfig: {
            return self?.isMaintenanceToggled(key: Constants.app)
        }, remoteConfig: {
            return self?.remoteConfig?.appMaintenance
        }, s3Config: {
            return self?.s3Config?.maintenance.isUnderMaintenance
        }, defaultValue: false)
    }()

    public lazy var glimpseEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.glimpseEnabled
        }, s3Config: {
            return self?.s3Config?.glimpse?.enabled
        }, defaultValue: false)
    }()

    public lazy var instapayQREnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.instapayQREnabled
        }, s3Config: {
            return self?.s3Config?.instapayQR?.enabled
        }, defaultValue: false)
    }()

    public lazy var bankPullFundsEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.bankPullFundsEnabled
        }, s3Config: {
            return self?.s3Config?.bankPullFunds?.bpi?.enabled
        }, defaultValue: false)
    }()

    public lazy var changeMinEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.changeMinEnabled
        }, s3Config: {
            return self?.s3Config?.changeMin?.enabled
        }, defaultValue: false)
    }()

    public lazy var whitelistedAnalyticsEventsV2Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.whitelistedAnalyticsEventsV2Enabled
        }, s3Config: {
            return self?.s3Config?.whitelistedAnalyticsEventsV2?.enabled
        }, defaultValue: false)
    }()

    public lazy var topUpServiceEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.topUpServiceEnabled
        }, s3Config: {
            return self?.s3Config?.topUpService?.enabled
        }, defaultValue: false)
    }()

    public lazy var whiteThemeEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.whiteThemeEnabled
        }, s3Config: {
            return self?.s3Config?.whiteTheme?.enabled
        }, defaultValue: false)
    }()

    public lazy var digitalOREnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.digitalOREnabled
        }, s3Config: {
            return self?.s3Config?.digitalOR?.enabled
        }, defaultValue: false)
    }()

    public lazy var amvcFingerprintingEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.amvcFingerprintingEnabled
        }, s3Config: {
            return self?.s3Config?.amvcFingerprinting?.enabled
       }, defaultValue: false)
    }()

    public lazy var eddEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.eddEnabled
        }, s3Config: {
            return self?.s3Config?.edd?.enabled
       }, defaultValue: false)
    }()

    public lazy var idClassificationEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.idClassificationEnabled
        }, s3Config: {
            return self?.s3Config?.idClassification?.enabled
        }, defaultValue: false)
    }()

    public lazy var mandatoryIdMatchingEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mandatoryIdClassificationEnabled
        }, s3Config: {
            return self?.s3Config?.mandatoryIdClassification?.enabled
        }, defaultValue: false)
    }()

    public lazy var billsPayCheckoutFlowV3Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaPayBillsClientApiCheckoutFlowV3Enabled
        }, s3Config: {
            return self?.s3Config?.mayaPayBillsClientApiCheckoutFlowV3?.enabled
        }, defaultValue: false)
    }()

    public lazy var billsPayInitialReceiptEntrypointEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(nonProdConfig: {
            return self?.isEnabled(.billsPayInitialReceiptEntryPoint)
        }, defaultValue: false)
    }()

    public lazy var billsPayHandleOtherFieldsFromDeeplinkEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.billsPayHandleOtherFieldsFromDeepLinkEnabled
        }, s3Config: {
            return self?.s3Config?.billsPayHandleOtherFieldsFromDeepLink?.enabled
        }, defaultValue: false)
    }()

    public lazy var billsPayDisableAccountNumberField: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.billsPayDisableAccountNumberFieldEnabled
        }, s3Config: {
            return self?.s3Config?.billsPayDisableAccountNumberField?.enabled
        }, defaultValue: false)
    }()

    public lazy var billsPaySkipWalletValidationEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaBillsPaySkipWalletValidationEnabled
        }, s3Config: {
            return self?.s3Config?.mayaBillsPaySkipWalletValidation?.enabled
        }, defaultValue: false)
    }()

    public lazy var billsPayFavoritesWithCreatedPayment: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.billsPayFavoritesWithCreatedPaymentEnabled
        }, s3Config: {
            return self?.s3Config?.billsPayFavoritesWithCreatedPayment?.enabled
        }, defaultValue: false)
    }()

    public lazy var cashInWithAccountLimitEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.cashInWithAccountLimitEnabled
        }, s3Config: {
            return self?.s3Config?.cashInWithAccountLimit?.enabled
        }, defaultValue: false)
    }()

    public lazy var amountFormatterV2Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.amountFormatterV2Enabled
        }, s3Config: {
            return self?.s3Config?.amountFormatterV2?.enabled
        }, defaultValue: false)
    }()

    public lazy var cryptoEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.cryptoEnabled
        }, s3Config: {
            return self?.s3Config?.crypto?.enabled
        }, defaultValue: false)
    }()

    public lazy var mayaLoansSimplifiedCheckoutEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaLoansSimplifiedCheckoutEnabled
        }, s3Config: {
            return self?.s3Config?.mayaLoansSimplifiedCheckout?.enabled
        }, defaultValue: false)
    }()

    public lazy var loansCreditCardDetailsV2Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.loansCreditCardDetailsV2Enabled
        }, s3Config: {
            return self?.s3Config?.loansCreditCardDetailsV2?.enabled
        }, defaultValue: false)
    }()

    public lazy var mayaLoansV3Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaLoansV3Enabled
        }, s3Config: {
            return self?.s3Config?.mayaLoansV3?.enabled
        }, defaultValue: false)
    }()

    public lazy var mayaLoansICLEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaLoansCashInstallmentEnabled
        }, s3Config: {
            return self?.s3Config?.mayaLoansCashInstallment?.enabled
        }, defaultValue: false)
    }()

    public lazy var unifiedLendingExperienceReskin: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.unifiedLendingExperienceReskinEnabled
        }, s3Config: {
            return self?.s3Config?.unifiedLendingExperienceReskin?.enabled
        }, defaultValue: false)
    }()

    public lazy var mayaCreditAccountSummaryAddEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(nonProdConfig: {
            return self?.isEnabled(.mayaCreditAccountSummaryAdd)
        }, defaultValue: false)
    }()

    public lazy var mayaCreditPartnerMerchantEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(nonProdConfig: {
            return self?.isEnabled(.mayaCreditPartnerMerchant)
        }, defaultValue: false)
    }()

    public lazy var iclMothersMaidenNameV3Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.iclMothersMaidenNameV3Enabled
        }, s3Config: {
            return self?.s3Config?.iclMothersMaidenNameV3?.enabled
        }, defaultValue: false)
    }()

    public lazy var mayaLoansEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaBnplLoansTabEnabled
        }, s3Config: {
            return self?.s3Config?.maya?.loans
        }, defaultValue: false)
    }()

    public lazy var iclInitialEligibilityCheckEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.initialEligibilityCheckEnabled
        }, s3Config: {
            return self?.s3Config?.initialEligibilityCheck?.enabled
        }, defaultValue: false)
    }()

    public lazy var iclDropdownV2Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.iclDropdownV2Enabled
        }, s3Config: {
            return self?.s3Config?.iclDropdownV2?.enabled
        }, defaultValue: false)
    }()

    public lazy var iclPhaseTwoV1Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.iclPhaseTwoV1Enabled
        }, s3Config: {
            return self?.s3Config?.iclDropdownV2?.enabled
        }, defaultValue: false)
    }()

    public lazy var iclPhaseTwoV2Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.iclPhaseTwoV2Enabled
        }, s3Config: {
            return self?.s3Config?.iclPhaseTwoV2?.enabled
        }, defaultValue: false)
    }()

    public lazy var iclToMambuEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.iclToMambuEnabled
        }, s3Config: {
            return self?.s3Config?.iclToMambu?.enabled
        }, defaultValue: false)
    }()

    public lazy var iclDblEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.iclDblEnabled
        }, s3Config: {
            return self?.s3Config?.iclDbl?.enabled
        }, defaultValue: false)
    }()

    public lazy var loansCreditCardEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.loansCreditCardEnabled
        }, s3Config: {
            return self?.s3Config?.loansCreditCard?.enabled
        }, defaultValue: false)
    }()

    public lazy var loansCreditCardMayaBlackEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.ccMayaBlackEnabled
        }, s3Config: {
            return self?.s3Config?.ccMayaBlack?.enabled
        }, defaultValue: false)
    }()

    public lazy var iclDeviceScoringDataReferenceIdEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.iclDeviceScoringDataReferenceIdEnabled
        }, s3Config: {
            return self?.s3Config?.iclDeviceScoringDataReferenceId?.enabled
        }, defaultValue: false)
    }()

    public lazy var mayaCreditIosDeviceScoringEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaCreditIosDeviceScoringEnabled
        }, s3Config: {
            return self?.s3Config?.mayaCreditIosDeviceScoring?.enabled
        }, defaultValue: false)
    }()

    public lazy var loansICLDeviceScoringEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.loansICLDeviceScoringEnabled
        }, s3Config: {
            return self?.s3Config?.loansICLDeviceScoring?.enabled
        }, defaultValue: false)
    }()

    public lazy var ccDeviceScoringEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.ccDeviceScoringEnabled
        }, s3Config: {
            return self?.s3Config?.ccDeviceScoring?.enabled
        }, defaultValue: false)
    }()

    public lazy var bnplDeepFreeze: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.bnplDeepFreezeEnabled
        }, s3Config: {
            return self?.s3Config?.bnplDeepFreeze?.enabled
        }, defaultValue: false)
    }()

    public lazy var showOtherIDsEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaShowOtherIDsEnabled
        }, s3Config: {
            return self?.s3Config?.showOtherIDs?.enabled
        }, defaultValue: false)
    }()

    public lazy var reKYCEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaReKYCEnabled
        }, s3Config: {
            return self?.s3Config?.reKYC?.enabled
        }, defaultValue: false)
    }()

    public lazy var reKYC10MinutesTestEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(nonProdConfig: {
            return self?.isEnabled(.reKYC10MinutesTest)
        }, defaultValue: false)
    }()

    public lazy var shortenedFlowUpgradeEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6ShortenedFlowUpgradeEnabled
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.shortenedFlowUpgrade
        }, defaultValue: false)
    }()

    public lazy var shortenedFlowReKYCEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6ShortenedFlowReKYCEnabled
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.shortenedFlowReKYC
        }, defaultValue: false)
    }()

    public lazy var shortenedFlowShowOtherIdEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6ShortenedFlowOtherIDEnabled
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.shortenedFlowShowOtherID
        }, defaultValue: false)
    }()

    public lazy var shortenedFlowIdCaptureV2Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6ShortenedFlowIdCaptureV2Enabled
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.shortenedFlowIdCaptureV2
        }, defaultValue: false)
    }()

    public lazy var backIDCaptureEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6ShortenedFlowBackIDCaptureEnabled
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.shortenedFlowBackIDCapture
        }, defaultValue: false)
    }()

    public lazy var updateProfileDeeplinkEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.userProfileUpdateV1Enabled
        }, s3Config: {
            return self?.s3Config?.userProfile?.updateV1
        }, defaultValue: false)
    }()

    public lazy var secondaryIDFlowEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6ShortenedFlowSecondaryIDCaptureEnabled
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.shortenedFlowSecondaryIDCapture
        }, defaultValue: false)
    }()

    public lazy var expandedReKYCEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6ShortenedFlowExpandedReKYCEnabled
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.shortenedFlowExpandedReKYC
        }, defaultValue: false)
    }()

    public lazy var idBlurDetectionEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6ShortenedFlowBlurDetectionEnabled
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.shortenedFlowBlurDetection
        }, defaultValue: false)
    }()

    public lazy var skipZolozFlowEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(nonProdConfig: {
            return self?.isEnabled(.skipZolozFlow)
        }, defaultValue: false)
    }()

    public lazy var showIDValScoreEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(nonProdConfig: {
            return self?.isEnabled(.showIDValScore)
        }, defaultValue: false)
    }()

    public lazy var showIDQualityScoreEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(nonProdConfig: {
            return self?.isEnabled(.showIDQualityScore)
        }, defaultValue: false)
    }()

    public lazy var kycPersonalInfoV2Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(nonProdConfig: {
            return self?.isEnabled(.kycPersonalInfoV2)
        }, defaultValue: false)
    }()

    public lazy var shortenedFlowAddresssDropdownEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6ShortenedFlowAddressDropdownEnabled
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.shortenedFlowAddressDropdown
        }, defaultValue: false)
    }()

    public lazy var shortenedFlowIdCaptureV3Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6ShortenedFlowIdCaptureV3Enabled
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.shortenedFlowIdCaptureV3
        }, defaultValue: false)
    }()

    public lazy var onboardingAndKYCFlowV2Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6OnboardingAndKYCFlowV2
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.onboardingAndKYCFlowV2
        }, defaultValue: false)
    }()

    public lazy var onboardingAndKYCFlowEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6OnboardingAndKYCFlow
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.onboardingAndKYCFlow
        }, defaultValue: false)
    }()

    public lazy var shortenedFlowForeignAddressComplianceEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6ShortenedFlowForeignAddressComplianceEnabled
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.shortenedFlowForeignAddressCompliance
        }, defaultValue: false)
    }()

    public lazy var dosriEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaComplianceDosri
        }, s3Config: {
            return self?.s3Config?.mayaCompliance?.dosri
        }, defaultValue: false)
    }()

    public lazy var fatcaEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaComplianceFatca
        }, s3Config: {
            return self?.s3Config?.mayaCompliance?.fatca
        }, defaultValue: false)
    }()

    public lazy var philsysEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6ShortenedFlowPhilsysEnabled
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.shortenedFlowPhilsys
        }, defaultValue: false)
    }()

    public lazy var forcePHNationalIDEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6ForcePHNationalID
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.forcePHNationalID
        }, defaultValue: false)
    }()

    public lazy var kycIDImageQualityEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6KycIdImageQualityEnabled
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.kycIdImageQuality
        }, defaultValue: false)
    }()

    public lazy var onboardingRoutingEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6PartnerOnboardingEnabled
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.partnerOnboarding
        }, defaultValue: false)
    }()

    public lazy var kycMaintenanceEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaEKYCV6KycMaintenance
        }, s3Config: {
            return self?.s3Config?.mayaEKYCV6?.kycMaintenance
        }, defaultValue: false)
    }()

    public lazy var registrationMiddleNameEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaRegistrationRequireMiddleName
        }, s3Config: {
            return self?.s3Config?.mayaRegistration?.requireMiddleName
        }, defaultValue: false)
    }()

    public lazy var consentCheckAllEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaRegistrationConsentCheckAll
        }, s3Config: {
            return self?.s3Config?.mayaRegistration?.consentCheckAll
        }, defaultValue: false)
    }()

    public lazy var autoProvisioningEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaRegistrationAutoProvisionEnabled
        }, s3Config: {
            return self?.s3Config?.mayaRegistration?.autoProvision
        }, defaultValue: false)
    }()

    public lazy var mfaFrameworkV1Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaMFAFrameworkV1Enabled
        }, s3Config: {
            return self?.s3Config?.mayaMFA?.frameworkV1
        }, defaultValue: false)
    }()

    public lazy var mfaFaceAuthFormEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaMFAFaceAuthFormEnabled
        }, s3Config: {
            return self?.s3Config?.mayaMFA?.faceAuthForm
        }, defaultValue: false)
    }()

    public lazy var forgotPasswordV2Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaForgotPasswordFrameworkV2Enabled
        }, s3Config: {
            return self?.s3Config?.mayaForgotPassword?.frameworkV2
        }, defaultValue: false)
    }()

    public lazy var mockedPreSubmissionDataEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(nonProdConfig: {
            return self?.isEnabled(.mockedPreSubmissionResuestData)
        }, defaultValue: false)
    }()

    public lazy var savingsEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.savingsEnabled
        }, s3Config: {
            return self?.s3Config?.maya?.savings
        }, defaultValue: true)
    }()

    public lazy var restrictedLoginFlowEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.restrictedLoginEnabled
        }, s3Config: {
            return self?.s3Config?.maya?.restrictedLogin
        }, defaultValue: false)
    }()

    public lazy var customerRestrictionsEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.customerRestrictionsEnabled
        }, s3Config: {
            return self?.s3Config?.customerRestrictions?.enabled
        }, defaultValue: false)
    }()

    public lazy var kycCompletedCallbackEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.kycCompletedCallbackEnabled
        }, s3Config: {
            return self?.s3Config?.kycCompletedCallback?.enabled
        }, defaultValue: false)
    }()

    public lazy var registrationRestrictionsEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.registrationRestrictionsEnabled
        }, s3Config: {
            return self?.s3Config?.registrationRestrictions?.enabled
        }, defaultValue: false)
    }()

    public lazy var eddRestrictionsEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.eddRestrictionsEnabled
        }, s3Config: {
            return self?.s3Config?.eddRestrictions?.enabled
        }, defaultValue: false)
    }()

    public lazy var mayaSetUsernameEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaUsernameEnabled
        }, s3Config: {
            return self?.s3Config?.maya?.username
        }, defaultValue: false)
    }()

    public lazy var mayaSavingsButtonEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaCashinMayaSavingsButtonEnabled
        }, s3Config: {
            return self?.s3Config?.maya?.cashinMayaSavingsButton
        }, defaultValue: false)
    }()

    public lazy var mayaWalletSettingsButtonEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaCashinWalletSettingsEnabled
        }, s3Config: {
            return self?.s3Config?.maya?.cashInWalletSettingsButton
        }, defaultValue: false)
    }()

    public lazy var mayaTransactionLimitWalletSettingsOptionEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(nonProdConfig: {
            return self?.isEnabled(.walletTransactionLimitWalletSettingsOption)
        }, defaultValue: false)
    }()

    public lazy var mayaAutoCashinWalletSettingsOptionEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaCashinWalletSettingsAutoCashInOptionEnabled
        }, s3Config: {
            return self?.s3Config?.maya?.cashInWalletSettingsAutoCashInOption
        }, defaultValue: false)
    }()

    public lazy var mayaInstaFillWalletSettingsOptionEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(nonProdConfig: {
            return self?.isEnabled(.instaFillWalletSettingsOption)
        }, defaultValue: false)
    }()

    public lazy var mayaInboxFlutterEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.inboxFlutterEnabled
        }, s3Config: {
            return self?.s3Config?.inboxFlutter?.enabled
        }, defaultValue: false)
    }()

    public lazy var mayaInboxManageNotificationsEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.inboxManageNotificationsEnabled
        }, s3Config: {
            return self?.s3Config?.inboxManageNotifications?.enabled
        }, defaultValue: false)
    }()

    public lazy var inboxSupportEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.inboxSupportEnabled
        }, s3Config: {
            return self?.s3Config?.inboxSupport?.enabled
        }, defaultValue: false)
    }()

    public lazy var mayaQRPHP2PAdjustmentsEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.qrP2PAdjustmentEnabled
        }, s3Config: {
            return self?.s3Config?.maya?.qrP2PAdjustment
        }, defaultValue: true)
    }()

    public lazy var pushApprovalEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaPushApprovalEnabled
        }, s3Config: {
            return self?.s3Config?.maya?.pushApproval
        }, defaultValue: false)
    }()

    public lazy var registrationMGMEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.registrationMGMEnabled
        }, s3Config: {
            return self?.s3Config?.registrationMGM?.enabled
        }, defaultValue: false)
    }()

    public lazy var fundsEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.fundsEnabled
        }, s3Config: {
            return self?.s3Config?.funds?.enabled
        }, defaultValue: false)
    }()

    public lazy var qrWithCheckoutFlowEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.qrCheckoutEnabled
        }, s3Config: {
            return self?.s3Config?.maya?.qrCheckout
        }, defaultValue: false)
    }()

    public lazy var mayaCreditPrePaymentBannerEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaPayBillsPayWithMayaCreditEnabled
        }, s3Config: {
            return self?.s3Config?.mayaPayBillsPayWithMayaCredit?.enabled
        }, defaultValue: false)
    }()

    public lazy var mayaCreditPrePaymentSoonEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaPayBillsPayWithMayaCreditSoonEnabled
        }, s3Config: {
            return self?.s3Config?.mayaPayBillsPayWithMayaCreditSoon?.enabled
        }, defaultValue: false)
    }()

    public lazy var mayaPayBillsPrePaymentBannerNewTagEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaPayBillsPayWithMayaCreditNewTagEnabled
        }, s3Config: {
            return self?.s3Config?.mayaPayBillsPayWithMayaCreditNewTag?.enabled
        }, defaultValue: false)
    }()

    public lazy var billsPayV3EndpointEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.payBillsV3EndpointEnabled
        }, s3Config: {
            return self?.s3Config?.payBillsV3Endpoint?.enabled
        }, defaultValue: false)
    }()

    public lazy var billerReminderEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaBillerReminderEnabled
        }, s3Config: {
            return self?.s3Config?.mayaBillerReminder?.enabled
        }, defaultValue: false)
    }()

    public lazy var cashInBankPullV3Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.cashInBankPullV3Enabled
        }, s3Config: {
            return self?.s3Config?.cashInBankPullFundsV3?.enabled
        }, defaultValue: false)
    }()

    public lazy var bdoCashInEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.bdoCashInEnabled
        }, s3Config: {
            return self?.s3Config?.bdoCashIn?.enabled
        }, defaultValue: false)
    }()

    public lazy var mayaShopProviderSubcatPillsEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.mayaShopProviderSubcatPillsEnabled
        }, s3Config: {
            return self?.s3Config?.mayaShopProviderSubcatPills?.enabled
        }, defaultValue: false)
    }()

    public lazy var virtualNumberRegistrationEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.virtualNumberRegistrationEnabled
        }, s3Config: {
            return self?.s3Config?.virtualNumberRegistration?.enabled
        }, defaultValue: false)
    }()

    public lazy var locationBasedVirtualNumberRegistrationEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.locationBasedVirtualNumberRegistrationEnabled
        }, s3Config: {
            return self?.s3Config?.locationBasedVirtualNumberRegistration?.enabled
        }, defaultValue: false)
    }()

    public lazy var inAppReviewEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.inAppReviewEnabled
        }, s3Config: {
            return self?.s3Config?.inAppReview?.default
        }, defaultValue: false)
    }()

    public lazy var inAppReviewSendMoneyEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.inAppReviewSendMoneyEnabled
        }, s3Config: {
            return self?.s3Config?.inAppReview?.sendMoney
        }, defaultValue: false)
    }()

    public lazy var fundsCreatorStoreEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.fundsCreatorStoreEnabled
        }, s3Config: {
            return self?.s3Config?.fundsCreatorStore?.enabled
        }, defaultValue: false)
    }()

    public lazy var loadV2Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.loadV2Enabled
        }, s3Config: {
            return self?.s3Config?.loadV2?.enabled
        }, defaultValue: false)
    }()

    public lazy var loadV2FavoritesEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.loadV2FavoritesEnabled
        }, s3Config: {
            return self?.s3Config?.loadV2Config?.favorites
        }, defaultValue: false)
    }()

    public lazy var loadV2RecommenderEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.loadV2RecommenderEnabled
        }, s3Config: {
            return self?.s3Config?.loadV2Config?.recommender
        }, defaultValue: false)
    }()

    public lazy var requestMoneyTileUpdatesEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.requestMoneyTileUpdateEnabled
        }, s3Config: {
            return self?.s3Config?.requestMoneyTileUpdate?.enabled
        }, defaultValue: false)
    }()

    public lazy var cardPullV2Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.cardPullV2Enabled
        }, s3Config: {
            return self?.s3Config?.cardPullV2?.enabled
        }, defaultValue: false)
    }()

    public lazy var bannerCorrelationHeaderEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.bannerCorrelationHeaderEnabled
        }, s3Config: {
            return self?.s3Config?.bannerCorrelationHeader?.enabled
        }, defaultValue: true)
    }()

    public lazy var creditCardCashbackTileEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.creditCardCashbackTileEnabled
        }, s3Config: {
            return self?.s3Config?.creditCardCashbackTile?.enabled
        }, defaultValue: false)
    }()

    public lazy var voucherDetailsV2Enabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.voucherDetailsV2Enabled
        }, s3Config: {
            return self?.s3Config?.voucherDetailsV2?.enabled
        }, defaultValue: false)
    }()

    public lazy var ecommAppEventV2QrEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.ecommAppEventV2QrEnabled
        }, s3Config: {
            return self?.s3Config?.ecommAppEventV2Config?.qr
        }, defaultValue: false)
    }()

    public lazy var ecommAppEventV2ShopEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.ecommAppEventV2ShopEnabled
        }, s3Config: {
            return self?.s3Config?.ecommAppEventV2Config?.shop
        }, defaultValue: false)
    }()

    public lazy var ecommAppEventV2BillsPayEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.ecommAppEventV2BillsPayEnabled
        }, s3Config: {
            return self?.s3Config?.ecommAppEventV2Config?.billsPay
        }, defaultValue: false)
    }()

    public lazy var appEventV2BankTransferEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(s3Config: {
            return self?.s3Config?.appEventsV2?.bankTransfer?.enabled
        }, defaultValue: false)
    }()

    public lazy var appEventV2SendMoneyEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(s3Config: {
            return self?.s3Config?.appEventsV2?.sendMoney?.enabled
        }, defaultValue: false)
    }()

    public lazy var loyaltyForceUpdateEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.loyaltyForceUpdateEnabled
        }, s3Config: {
            return self?.s3Config?.loyaltyForceUpdate?.enabled
        }, defaultValue: false)
    }()

    public lazy var loginHistoryTTL: CSConfig<String> = { [weak self] in
        return CSConfig<String>(remoteConfig: {
            return self?.remoteConfig?.loginHistoryTTL
        }, s3Config: {
            return self?.s3Config?.loginHistoryTTL
        }, defaultValue: "90")
    }()

    public lazy var freelancerHubEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(nonProdConfig: {
            return self?.isEnabled(.freelancerHub)
        }, defaultValue: false)
    }()

    public lazy var appEventV2CashInEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.appEventsV2CashInEnabled
        }, s3Config: {
            return self?.s3Config?.appEventsV2?.cashIn?.enabled
        }, defaultValue: false)
    }()

    public lazy var freezeCardSpiel: CSConfig<String> = { [weak self] in
        return CSConfig<String>(remoteConfig: {
            return self?.remoteConfig?.freezeCardSpiel
        }, s3Config: {
            return self?.s3Config?.securityCenter?.freezeCardSpiel
        }, defaultValue: "")
    }()

    public lazy var unfreezeCardSpiel: CSConfig<String> = { [weak self] in
        return CSConfig<String>(remoteConfig: {
            return self?.remoteConfig?.unfreezeCardSpiel
        }, s3Config: {
            return self?.s3Config?.securityCenter?.unfreezeCardSpiel
        }, defaultValue: "")
    }()

    public lazy var freezeConfirmationSpiel: CSConfig<String> = { [weak self] in
        return CSConfig<String>(remoteConfig: {
            return self?.remoteConfig?.freezeConfirmationSpiel
        }, s3Config: {
            return self?.s3Config?.securityCenter?.freezeConfirmationSpiel
        }, defaultValue: "")
    }()

    public lazy var unfreezeConfirmationSpiel: CSConfig<String> = { [weak self] in
        return CSConfig<String>(remoteConfig: {
            return self?.remoteConfig?.unfreezeConfirmationSpiel
        }, s3Config: {
            return self?.s3Config?.securityCenter?.unfreezeConfirmationSpiel
        }, defaultValue: "")
    }()

    public lazy var freezeSuccessSpiel: CSConfig<String> = { [weak self] in
        return CSConfig<String>(remoteConfig: {
            return self?.remoteConfig?.freezeSuccessSpiel
        }, s3Config: {
            return self?.s3Config?.securityCenter?.freezeSuccessSpiel
        }, defaultValue: "")
    }()

    public lazy var unfreezeSuccessSpiel: CSConfig<String> = { [weak self] in
        return CSConfig<String>(remoteConfig: {
            return self?.remoteConfig?.unfreezeSuccessSpiel
        }, s3Config: {
            return self?.s3Config?.securityCenter?.unfreezeSuccessSpiel
        }, defaultValue: "")
    }()

    public lazy var landersCreditCardEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.landersCreditCardEnabled
        }, s3Config: {
            return self?.s3Config?.landersCreditCard?.enabled
        }, defaultValue: false)
    }()

    public lazy var cardDetailsProtectionEnabled: CSConfig<Bool> = { [weak self] in
        return CSConfig<Bool>(remoteConfig: {
            return self?.remoteConfig?.cardDetailsProtectionEnabled
        }, s3Config: {
            return self?.s3Config?.cardDetailsProtection?.enabled
        }, defaultValue: false)
    }()

    public init(identifier: ConfigurationServiceStorableIdentifier, metadataDateFormatter: DateFormatter, completion: ((ConfigurationService) -> Void)?) {
        ContainerWrapper.shared.register(type: ConfigurationServiceStorableIdentifier.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return identifier
        })
        ContainerWrapper.shared.synchronize()

        let settings = RemoteConfigSettings()
        #if !PROD_TARGET && !SANDBOX_TARGET
        settings.minimumFetchInterval = 0
        #else
        settings.minimumFetchInterval = 43200
        #endif

        getConfigurationAction = Action { [weak self] in
            guard let self = self else { return SignalProducer<AppConfig?, Error>(error: Error()) }

            let userDefaultsStore = self.storeProvider.target(UserDefaultsStore.self)
            let configurationSettingsLastUpdate = userDefaultsStore?.readRaw(identifier.appConfigurationSettingsLastUpdatedOn) as? Date
            let configurationSettingsMetadataSignalProducer =
                self.apiProvider.reactive.request(API.Configuration.settingsMetadata, lastUpdated: configurationSettingsLastUpdate, dateFormatter: metadataDateFormatter)
                .map({ _ -> AppConfig? in
                    return nil
                })

            let configurationSettingsSignalProducer = self.apiProvider.reactive.request(API.Configuration.settings, type: AppConfig.self, decoder: AppConfig.decoder)
                .on(value: { [weak self] appConfig in
                    guard let self else { return }
                    self.cachedS3Config = appConfig

                    if appConfig.maintenance.isUnderMaintenance {
                        NotificationCenter.default.post(name: Notification.Name.appLaunchMaintenance, object: nil)
                    }

                    userDefaultsStore?.write(Date(), options: identifier.appConfigurationSettingsLastUpdatedOn)
                    completion?(self)
                })
                .map({ appConfig -> AppConfig? in
                    return appConfig
                })

            return configurationSettingsMetadataSignalProducer.concat(configurationSettingsSignalProducer)
        }

        getRemoteConfigAction = Action { [weak self] in
            guard let self = self else { return SignalProducer<Void, Swift.Error>(error: Error()) }
            return SignalProducer<Void, Swift.Error> { observer, _ in
                let remoteConfig = RemoteConfig.remoteConfig()
                remoteConfig.configSettings = settings

                remoteConfig.fetchAndActivate { [weak self] status, error in
                    guard let self else { return }
                    if let error = error {
                        observer.send(error: error)
                    } else {
                        switch status {
                        case .error:
                            observer.send(error: Error())
                        case .successFetchedFromRemote:
                            guard let encryptedStore = self.storeProvider.target(EncryptedDefaultsStore.self) else {
                                observer.send(error: Error())
                                return
                            }
                            let config = PayMayaRemoteConfig(config: remoteConfig)
                            encryptedStore.write(config, options: identifier.remoteConfig)
                            self.cachedRemoteConfig = config
                            fallthrough
                        case .successUsingPreFetchedData:
                            if let cachedRemoteConfig, cachedRemoteConfig.appMaintenance == true {
                                NotificationCenter.default.post(name: Notification.Name.appLaunchMaintenance, object: nil)
                            }

                            observer.sendCompleted()
                        @unknown default:
                            observer.send(error: Error())
                        }
                    }
                }
            }
        }
    }

    public func startRealtimeRemoteConfigListener() {
        let remoteConfig = RemoteConfig.remoteConfig()
        remoteConfig.addOnConfigUpdateListener { [weak self] update, error in
            guard error == nil,
                let update
            else { return }

            remoteConfig.activate { changed, error in
                guard changed && error == nil,
                      let self = self,
                      let encryptedStore = self.storeProvider.target(EncryptedDefaultsStore.self)
                else { return }

                let config = PayMayaRemoteConfig(config: remoteConfig)
                encryptedStore.write(config, options: self.identifier.remoteConfig)
                self.cachedRemoteConfig = config

                if update.updatedKeys.contains(PayMayaRemoteConfig.CodingKeys.appMaintenance.rawValue) {
                    NotificationCenter.default.post(name: Notification.Name.realTimeMaintenance, object: nil)
                }
            }
        }
    }

    /// S3 Related Configs only for centralized access to ConfigurationService
    public var maintenance: AppConfig.Maintenance? {
        return s3Config?.maintenance
    }

    public var shop: AppConfig.Shop? {
        return s3Config?.shop
    }

    public var referral: AppConfig.Referral? {
        return s3Config?.referral
    }

    public var mgm: AppConfig.MGM? {
        return s3Config?.mgm
    }

    public var kycVideo: AppConfig.KYCVideo? {
        return s3Config?.kycVideo
    }

    public var bankPullFunds: AppConfig.BankPullFunds? {
        return s3Config?.bankPullFunds
    }

    public var refreshSessionDuration: TimeInterval {
        if let duration = s3Config?.refreshSessionDuration {
            return duration * 60.0
        }
        return 10.0 * 60.0
    }

    public var mayaServices: [Service]? {
        return s3Config?.mayaServices
    }

    public var mayaServicesCategory: MayaServiceCategoryResponse? {
        return s3Config?.mayaServicesCategory
    }

    public var dashboardSettings: AppConfig.DashboardSettings? {
        return s3Config?.dashboardSettings
    }

    public var verifiedSellerLevels: [String]? {
        return s3Config?.profileBadges?.verifiedSeller
    }

    public var mockLocation: CLLocation? {
        let overrideKey = "\(Constants.mockLocationPrefix)\(Constants.override)"
        let latitudeKey = "\(Constants.mockLocationPrefix)\(Constants.latitude)"
        let longitudeKey = "\(Constants.mockLocationPrefix)\(Constants.longitude)"

        guard isEnabled(forKey: overrideKey) == true else {
            return nil
        }

        let defaults = UserDefaults.standard
        guard let latitudeString = defaults.string(forKey: latitudeKey),
            let longitudeString = defaults.string(forKey: longitudeKey) else {
            return Constants.officeLocation
        }

        guard let latitude = CLLocationDegrees(latitudeString), let longitude = CLLocationDegrees(longitudeString) else {
            return Constants.officeLocation
        }

        return CLLocation(latitude: latitude, longitude: longitude)
    }

    public func isMaintenanceToggled(key: String) -> Bool? {
        let overrideKey = "\(Constants.maintenancePrefix)\(Constants.override)"
        let maintenanceKey = "\(Constants.maintenancePrefix)\(key)"

        guard isEnabled(forKey: overrideKey) == true else {
            return nil
        }
        return isEnabled(forKey: maintenanceKey)
    }

    /// A function that gets the corresponding value of a nonProdConfig toggle
    /// - parameter toggle: a value in PayMayaNonProdToggles
    /// - returns: boolean value of the toggle. nil if toggle doesn't exist.
    private func isEnabled(_ toggle: PayMayaNonProdToggles) -> Bool? {
        let key = "\(Constants.togglePrefix)\(toggle.rawValue)"
        return isEnabled(forKey: key)
    }

    private func isEnabled(forKey key: String) -> Bool? {
        let defaults = UserDefaults.standard
        guard defaults.object(forKey: key) != nil else {
            return nil
        }
        return defaults.bool(forKey: key)
    }
}
