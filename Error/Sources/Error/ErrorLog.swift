//
//  ErrorLog.swift
//  
//
//  Created by <PERSON><PERSON> on 4/27/23.
//

import Foundation

public enum ErrorLog: LocalizedError, CustomStringConvertible {
    case moyaError(code: Int, url: String)
    case databaseError(underlying: Swift.Error, message: String, customKeys: [String: Any]? = nil)
    case `default`(underlying: Swift.Error)

    public var description: String {
        switch self {
        case .moyaError(let code, let urlString):
            return "Status Code=\(code) URL=\(urlString)"
        case .databaseError(let underlying, let message, _):
            return "\(underlying.localizedDescription) \n\nMessage: \(message)"
        case .default(let underlying):
            return underlying.localizedDescription
        }
    }
}

public extension LocalizedError where Self: CustomStringConvertible {
    var errorDescription: String? {
        return description
    }
}
