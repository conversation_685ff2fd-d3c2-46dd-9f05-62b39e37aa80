// swiftlint:disable all
// Generated using SwiftGen — https://github.com/SwiftGen/SwiftGen

#if os(macOS)
  import AppKit
#elseif os(iOS)
  import UIKit
#elseif os(tvOS) || os(watchOS)
  import UIKit
#endif

// Deprecated typealiases
@available(*, deprecated, renamed: "ColorAsset.Color", message: "This typealias will be removed in SwiftGen 7.0")
public typealias CommonAssetColorTypeAlias = ColorAsset.Color
@available(*, deprecated, renamed: "ImageAsset.Image", message: "This typealias will be removed in SwiftGen 7.0")
public typealias CommonAssetImageTypeAlias = ImageAsset.Image

// swiftlint:disable superfluous_disable_command file_length implicit_return

// MARK: - Asset Catalogs

// swiftlint:disable identifier_name line_length nesting type_body_length type_name
public enum CommonAsset {
  public enum Colors {
    public enum BrandIndigo {
      public static let brandIndigo100Base = ColorAsset(name: "BrandIndigo100Base")
      public static let brandIndigo120S = ColorAsset(name: "BrandIndigo120S")
      public static let brandIndigo140S = ColorAsset(name: "BrandIndigo140S")
      public static let brandIndigo160S = ColorAsset(name: "BrandIndigo160S")
      public static let brandIndigo20T = ColorAsset(name: "BrandIndigo20T")
      public static let brandIndigo40T = ColorAsset(name: "BrandIndigo40T")
      public static let brandIndigo60T = ColorAsset(name: "BrandIndigo60T")
      public static let brandIndigo80T = ColorAsset(name: "BrandIndigo80T")
    }
    public enum BrandPurple {
      public static let brandPurple100Base = ColorAsset(name: "BrandPurple100Base")
      public static let brandPurple120S = ColorAsset(name: "BrandPurple120S")
      public static let brandPurple140S = ColorAsset(name: "BrandPurple140S")
      public static let brandPurple20T = ColorAsset(name: "BrandPurple20T")
      public static let brandPurple40T = ColorAsset(name: "BrandPurple40T")
      public static let brandPurple60T = ColorAsset(name: "BrandPurple60T")
      public static let brandPurple80T = ColorAsset(name: "BrandPurple80T")
    }
    public enum BrandViolet {
      public static let brandViolet100Base = ColorAsset(name: "BrandViolet100Base")
      public static let brandViolet120S = ColorAsset(name: "BrandViolet120S")
      public static let brandViolet140S = ColorAsset(name: "BrandViolet140S")
      public static let brandViolet160S = ColorAsset(name: "BrandViolet160S")
      public static let brandViolet180S = ColorAsset(name: "BrandViolet180S")
      public static let brandViolet20T = ColorAsset(name: "BrandViolet20T")
      public static let brandViolet40T = ColorAsset(name: "BrandViolet40T")
      public static let brandViolet600T = ColorAsset(name: "BrandViolet600T")
      public static let brandViolet60T = ColorAsset(name: "BrandViolet60T")
    }
    public enum Constants {
      public static let _00A651 = ColorAsset(name: "00A651")
      public static let _00B463 = ColorAsset(name: "00B463")
      public static let _99E1C1 = ColorAsset(name: "99E1C1")
      public static let appleGreen = ColorAsset(name: "AppleGreen")
      public static let azure = ColorAsset(name: "Azure")
      public static let bfcdb4 = ColorAsset(name: "BFCDB4")
      public static let blackish = ColorAsset(name: "Blackish")
      public static let bluishGray = ColorAsset(name: "BluishGray")
      public static let booger = ColorAsset(name: "Booger")
      public static let c4C6Ca = ColorAsset(name: "C4C6CA")
      public static let c73Beb = ColorAsset(name: "C73BEB")
      public static let cf2B19 = ColorAsset(name: "CF2B19")
      public static let cloudyBlue = ColorAsset(name: "CloudyBlue")
      public static let dde199 = ColorAsset(name: "DDE199")
      public static let dark = ColorAsset(name: "Dark")
      public static let darkBlueTint = ColorAsset(name: "DarkBlueTint")
      public static let darkGrey = ColorAsset(name: "DarkGrey")
      public static let darkRed = ColorAsset(name: "DarkRed")
      public static let darkSkyBlue = ColorAsset(name: "DarkSkyBlue")
      public static let darkerGray = ColorAsset(name: "DarkerGray")
      public static let disabled = ColorAsset(name: "Disabled")
      public static let e0E0E0 = ColorAsset(name: "E0E0E0")
      public static let e8Fff4 = ColorAsset(name: "E8FFF4")
      public static let ec6C6C = ColorAsset(name: "EC6C6C")
      public static let errorRed = ColorAsset(name: "ErrorRed")
      public static let faf2F2 = ColorAsset(name: "FAF2F2")
      public static let ffffff = ColorAsset(name: "FFFFFF")
      public static let grayDisabled = ColorAsset(name: "GrayDisabled")
      public static let greyish = ColorAsset(name: "Greyish")
      public static let gunMetal = ColorAsset(name: "GunMetal")
      public static let lightBlue = ColorAsset(name: "LightBlue")
      public static let lightDark = ColorAsset(name: "LightDark")
      public static let lightGray = ColorAsset(name: "LightGray")
      public static let lightGreen = ColorAsset(name: "LightGreen")
      public static let lighterGray = ColorAsset(name: "LighterGray")
      public static let linkBlue = ColorAsset(name: "LinkBlue")
      public static let newBlack = ColorAsset(name: "NewBlack")
      public static let newGray = ColorAsset(name: "NewGray")
      public static let newGreen = ColorAsset(name: "NewGreen")
      public static let newLightGray = ColorAsset(name: "NewLightGray")
      public static let newRed = ColorAsset(name: "NewRed")
      public static let normalRed = ColorAsset(name: "NormalRed")
      public static let offWhite = ColorAsset(name: "OffWhite")
      public static let orangeLimit = ColorAsset(name: "OrangeLimit")
      public static let primaryDark = ColorAsset(name: "PrimaryDark")
      public static let rosyPink = ColorAsset(name: "RosyPink")
      public static let searchBackground = ColorAsset(name: "SearchBackground")
      public static let selectionColor = ColorAsset(name: "SelectionColor")
      public static let textFieldBorder = ColorAsset(name: "TextFieldBorder")
      public static let webviewTint = ColorAsset(name: "WebviewTint")
    }
    public enum Error {
      public static let error100Base = ColorAsset(name: "Error100Base")
      public static let error120S = ColorAsset(name: "Error120S")
      public static let error140S = ColorAsset(name: "Error140S")
      public static let error160S = ColorAsset(name: "Error160S")
      public static let error180S = ColorAsset(name: "Error180S")
      public static let error20T = ColorAsset(name: "Error20T")
      public static let error40T = ColorAsset(name: "Error40T")
      public static let error60T = ColorAsset(name: "Error60T")
      public static let error80T = ColorAsset(name: "Error80T")
    }
    public enum Gray {
      public static let gray00T50Alpha = ColorAsset(name: "Gray00T-50Alpha")
      public static let gray00T = ColorAsset(name: "Gray00T")
      public static let gray100Base = ColorAsset(name: "Gray100Base")
      public static let gray120S = ColorAsset(name: "Gray120S")
      public static let gray140S = ColorAsset(name: "Gray140S")
      public static let gray160S = ColorAsset(name: "Gray160S")
      public static let gray180S = ColorAsset(name: "Gray180S")
      public static let gray200S = ColorAsset(name: "Gray200S")
      public static let gray20T = ColorAsset(name: "Gray20T")
      public static let gray220S = ColorAsset(name: "Gray220S")
      public static let gray40T = ColorAsset(name: "Gray40T")
      public static let gray60T = ColorAsset(name: "Gray60T")
      public static let gray80T = ColorAsset(name: "Gray80T")
    }
    public enum Inform {
      public static let inform100Base = ColorAsset(name: "Inform100Base")
      public static let inform120S = ColorAsset(name: "Inform120S")
      public static let inform140S = ColorAsset(name: "Inform140S")
      public static let inform160S = ColorAsset(name: "Inform160S")
      public static let inform180S = ColorAsset(name: "Inform180S")
      public static let inform20T = ColorAsset(name: "Inform20T")
      public static let inform40T = ColorAsset(name: "Inform40T")
      public static let inform60T = ColorAsset(name: "Inform60T")
      public static let inform80T = ColorAsset(name: "Inform80T")
    }
    public enum Literals {
      public static let amountSelectorAccent = ColorAsset(name: "AmountSelectorAccent")
      public static let amountSelectorBorder = ColorAsset(name: "AmountSelectorBorder")
      public static let cardViewErrorBackground = ColorAsset(name: "CardViewErrorBackground")
      public static let cardViewErrorBorder = ColorAsset(name: "CardViewErrorBorder")
      public static let cardViewLoadingBackground = ColorAsset(name: "CardViewLoadingBackground")
      public static let deleteActionBackground = ColorAsset(name: "DeleteActionBackground")
      public static let dimmerView = ColorAsset(name: "DimmerView")
      public static let errorViewBackground = ColorAsset(name: "ErrorViewBackground")
      public static let menuBarAccent = ColorAsset(name: "MenuBarAccent")
      public static let menuBarNormal = ColorAsset(name: "MenuBarNormal")
      public static let pickerBackground = ColorAsset(name: "PickerBackground")
      public static let searchBackground = ColorAsset(name: "SearchBackground")
      public static let searchBarBackground = ColorAsset(name: "SearchBarBackground")
      public static let searchController = ColorAsset(name: "SearchController")
      public static let snackbarBackground = ColorAsset(name: "SnackbarBackground")
    }
    public enum Others {
      public static let opacityBlack = ColorAsset(name: "OpacityBlack")
      public static let outline = ColorAsset(name: "Outline")
    }
    public enum Primary {
      public static let primary100Base = ColorAsset(name: "Primary100Base")
      public static let primary120S = ColorAsset(name: "Primary120S")
      public static let primary140S = ColorAsset(name: "Primary140S")
      public static let primary160S = ColorAsset(name: "Primary160S")
      public static let primary180S = ColorAsset(name: "Primary180S")
      public static let primary20T = ColorAsset(name: "Primary20T")
      public static let primary40T = ColorAsset(name: "Primary40T")
      public static let primary60T = ColorAsset(name: "Primary60T")
      public static let primary80T = ColorAsset(name: "Primary80T")
    }
    public enum Secondary {
      public static let secondary100Base = ColorAsset(name: "Secondary100Base")
      public static let secondary120S = ColorAsset(name: "Secondary120S")
      public static let secondary140S = ColorAsset(name: "Secondary140S")
      public static let secondary160S = ColorAsset(name: "Secondary160S")
      public static let secondary180S = ColorAsset(name: "Secondary180S")
      public static let secondary20T = ColorAsset(name: "Secondary20T")
      public static let secondary40T = ColorAsset(name: "Secondary40T")
      public static let secondary60T = ColorAsset(name: "Secondary60T")
      public static let secondary80T = ColorAsset(name: "Secondary80T")
    }
    public enum Status {
      public static let statusError = ColorAsset(name: "StatusError")
      public static let statusProcessing = ColorAsset(name: "StatusProcessing")
      public static let statusSuccess = ColorAsset(name: "StatusSuccess")
    }
    public enum Success {
      public static let success100Base = ColorAsset(name: "Success100Base")
      public static let success120S = ColorAsset(name: "Success120S")
      public static let success140S = ColorAsset(name: "Success140S")
      public static let success160S = ColorAsset(name: "Success160S")
      public static let success180S = ColorAsset(name: "Success180S")
      public static let success20T = ColorAsset(name: "Success20T")
      public static let success40T = ColorAsset(name: "Success40T")
      public static let success60T = ColorAsset(name: "Success60T")
      public static let success80T = ColorAsset(name: "Success80T")
    }
    public enum Tertiary {
      public static let tertiary100Base = ColorAsset(name: "Tertiary100Base")
      public static let tertiary120S = ColorAsset(name: "Tertiary120S")
      public static let tertiary140S = ColorAsset(name: "Tertiary140S")
      public static let tertiary160S = ColorAsset(name: "Tertiary160S")
      public static let tertiary180S = ColorAsset(name: "Tertiary180S")
      public static let tertiary20T = ColorAsset(name: "Tertiary20T")
      public static let tertiary40T = ColorAsset(name: "Tertiary40T")
      public static let tertiary60T = ColorAsset(name: "Tertiary60T")
      public static let tertiary80T = ColorAsset(name: "Tertiary80T")
    }
    public enum Warning {
      public static let warning100Base = ColorAsset(name: "Warning100Base")
      public static let warning120S = ColorAsset(name: "Warning120S")
      public static let warning140S = ColorAsset(name: "Warning140S")
      public static let warning160S = ColorAsset(name: "Warning160S")
      public static let warning180S = ColorAsset(name: "Warning180S")
      public static let warning20T = ColorAsset(name: "Warning20T")
      public static let warning40T = ColorAsset(name: "Warning40T")
      public static let warning60T = ColorAsset(name: "Warning60T")
      public static let warning80T = ColorAsset(name: "Warning80T")
    }
  }
  public enum Images {
    public enum Alert {
      public static let image3DFailed = ImageAsset(name: "image3DFailed")
      public static let image3DMaintenance = ImageAsset(name: "image3DMaintenance")
      public static let image3DPending = ImageAsset(name: "image3DPending")
      public static let image3DSuccess = ImageAsset(name: "image3DSuccess")
      public static let image3DWarning = ImageAsset(name: "image3DWarning")
      public static let imageAccountError = ImageAsset(name: "imageAccountError")
      public static let imageCamera = ImageAsset(name: "imageCamera")
      public static let imageConnectionError = ImageAsset(name: "imageConnectionError")
      public static let imageDefaultError = ImageAsset(name: "imageDefaultError")
      public static let imageDefaultSuccess = ImageAsset(name: "imageDefaultSuccess")
      public static let imageFailedOctagon = ImageAsset(name: "imageFailedOctagon")
      public static let imageID = ImageAsset(name: "imageID")
      public static let imageInfoTriangle = ImageAsset(name: "imageInfoTriangle")
      public static let imageLarger3DWarning = ImageAsset(name: "imageLarger3DWarning")
      public static let imageMCCLPreview = ImageAsset(name: "imageMCCLPreview")
      public static let imageMaintenance = ImageAsset(name: "imageMaintenance")
      public static let imageMissedTarget = ImageAsset(name: "imageMissedTarget")
      public static let imageSuccessGear = ImageAsset(name: "imageSuccessGear")
      public static let imageYellowClock = ImageAsset(name: "imageYellowClock")
    }
    public enum Common {
      public static let imageBinoculars = ImageAsset(name: "imageBinoculars")
      public static let imageScrollBar = ImageAsset(name: "imageScrollBar")
      public static let imageStarSuccess = ImageAsset(name: "imageStarSuccess")
      public static let ineligibleMissedTarget = ImageAsset(name: "ineligibleMissedTarget")
    }
    public enum Icons {
      public static let iconBack = ImageAsset(name: "iconBack")
      public static let iconBackWhite = ImageAsset(name: "iconBackWhite")
      public static let iconCheck = ImageAsset(name: "iconCheck")
      public static let iconCheckErrorStatus = ImageAsset(name: "iconCheckErrorStatus")
      public static let iconCheckGreen = ImageAsset(name: "iconCheckGreen")
      public static let iconChevron = ImageAsset(name: "iconChevron")
      public static let iconChevronGray = ImageAsset(name: "iconChevronGray")
      public static let iconChevronGreenDown = ImageAsset(name: "iconChevronGreenDown")
      public static let iconChevronGreenUp = ImageAsset(name: "iconChevronGreenUp")
      public static let iconChevronRightGray = ImageAsset(name: "iconChevronRightGray")
      public static let iconChevronRightLight = ImageAsset(name: "iconChevronRightLight")
      public static let iconCompleted = ImageAsset(name: "iconCompleted")
      public static let iconContact = ImageAsset(name: "iconContact")
      public static let iconCopyGreen = ImageAsset(name: "iconCopyGreen")
      public static let iconDate = ImageAsset(name: "iconDate")
      public static let iconDotsHorizontal = ImageAsset(name: "iconDotsHorizontal")
      public static let iconEyeClosed = ImageAsset(name: "iconEyeClosed")
      public static let iconEyeOpen = ImageAsset(name: "iconEyeOpen")
      public static let iconFailed = ImageAsset(name: "iconFailed")
      public static let iconGreenDot = ImageAsset(name: "iconGreenDot")
      public static let iconGreenHeart = ImageAsset(name: "iconGreenHeart")
      public static let iconGreenRightChevron = ImageAsset(name: "iconGreenRightChevron")
      public static let iconInbox = ImageAsset(name: "iconInbox")
      public static let iconInfo = ImageAsset(name: "iconInfo")
      public static let iconInfoBlack = ImageAsset(name: "iconInfoBlack")
      public static let iconInfoError = ImageAsset(name: "iconInfoError")
      public static let iconInfoFilled = ImageAsset(name: "iconInfoFilled")
      public static let iconInfoNote = ImageAsset(name: "iconInfoNote")
      public static let iconLocked = ImageAsset(name: "iconLocked")
      public static let iconMagnifier = ImageAsset(name: "iconMagnifier")
      public static let iconMaya3DTargetAndArrow = ImageAsset(name: "iconMaya3DTargetAndArrow")
      public static let iconMenuDots = ImageAsset(name: "iconMenuDots")
      public static let iconMerchantPlaceholderGreen = ImageAsset(name: "iconMerchantPlaceholderGreen")
      public static let iconMinus = ImageAsset(name: "iconMinus")
      public static let iconNotificationDisabled = ImageAsset(name: "iconNotificationDisabled")
      public static let iconNotificationDisabledClear = ImageAsset(name: "iconNotificationDisabledClear")
      public static let iconNotificationEnabled = ImageAsset(name: "iconNotificationEnabled")
      public static let iconOther = ImageAsset(name: "iconOther")
      public static let iconOtherCamera = ImageAsset(name: "iconOtherCamera")
      public static let iconOtherReload = ImageAsset(name: "iconOtherReload")
      public static let iconPHFlag = ImageAsset(name: "iconPHFlag")
      public static let iconPlus = ImageAsset(name: "iconPlus")
      public static let iconPlusSign = ImageAsset(name: "iconPlusSign")
      public static let iconProcessing = ImageAsset(name: "iconProcessing")
      public static let iconProfilePlaceholder = ImageAsset(name: "iconProfilePlaceholder")
      public static let iconRadioButtonSelected = ImageAsset(name: "iconRadioButtonSelected")
      public static let iconRadioButtonUnselected = ImageAsset(name: "iconRadioButtonUnselected")
      public static let iconRightArrow = ImageAsset(name: "iconRightArrow")
      public static let iconRightChevron = ImageAsset(name: "iconRightChevron")
      public static let iconSelectedCheckbox = ImageAsset(name: "iconSelectedCheckbox")
      public static let iconShareOrSave = ImageAsset(name: "iconShareOrSave")
      public static let iconSuccess = ImageAsset(name: "iconSuccess")
      public static let iconSuccess3d = ImageAsset(name: "iconSuccess3d")
      public static let iconSystemChevronLeft = ImageAsset(name: "iconSystemChevronLeft")
      public static let iconSystemChevronRight = ImageAsset(name: "iconSystemChevronRight")
      public static let iconSystemCross = ImageAsset(name: "iconSystemCross")
      public static let iconSystemReject = ImageAsset(name: "iconSystemReject")
      public static let iconSystemRejectWhite = ImageAsset(name: "iconSystemRejectWhite")
      public static let iconUnselectedCheckbox = ImageAsset(name: "iconUnselectedCheckbox")
      public static let iconYellowClockExtraSmall = ImageAsset(name: "iconYellowClockExtraSmall")
      public static let otherPublicName = ImageAsset(name: "otherPublicName")
    }
    public enum MayaLogos {
      public static let iconGrayRoundMaya = ImageAsset(name: "iconGrayRoundMaya")
      public static let iconMayaLogoExtraSmall = ImageAsset(name: "iconMayaLogoExtraSmall")
      public static let iconMayaLogoPlainGreen = ImageAsset(name: "iconMayaLogoPlainGreen")
      public static let iconMayaLogoPlainM = ImageAsset(name: "iconMayaLogoPlainM")
      public static let iconRoundedGreenMaya = ImageAsset(name: "iconRoundedGreenMaya")
      public static let iconRoundedMaya = ImageAsset(name: "iconRoundedMaya")
      public static let imageMayaLogo = ImageAsset(name: "imageMayaLogo")
      public static let imageMayaLogoAllWhite = ImageAsset(name: "imageMayaLogoAllWhite")
      public static let imageMayaLogoExtraSmall = ImageAsset(name: "imageMayaLogoExtraSmall")
      public static let imageMayaLogoMedium = ImageAsset(name: "imageMayaLogoMedium")
      public static let imageMayaLogoPlain = ImageAsset(name: "imageMayaLogoPlain")
      public static let imageMayaLogoSmall = ImageAsset(name: "imageMayaLogoSmall")
      public static let imageMayaLogoTopBlackDescription = ImageAsset(name: "imageMayaLogoTopBlackDescription")
      public static let imageMayaLogoTopBoldWhiteDescription = ImageAsset(name: "imageMayaLogoTopBoldWhiteDescription")
      public static let imageMayaLogoTopGrayDescription = ImageAsset(name: "imageMayaLogoTopGrayDescription")
      public static let imageMayaLogoTopWhiteDescription = ImageAsset(name: "imageMayaLogoTopWhiteDescription")
    }
    public enum PayMaya {
      public enum Common {
        public static let backgroundGradient = ImageAsset(name: "backgroundGradient")
        public static let backgroundGradientGreenNavigationBar = ImageAsset(name: "backgroundGradientGreenNavigationBar")
        public static let backgroundNavigationBar = ImageAsset(name: "backgroundNavigationBar")
        public static let backgroundSelectedSegmentedControl = ImageAsset(name: "backgroundSelectedSegmentedControl")
        public static let backgroundSolidGreen = ImageAsset(name: "backgroundSolidGreen")
        public static let bannerBackgroundGradient = ImageAsset(name: "bannerBackgroundGradient")
        public static let iconArrowLeftBlack = ImageAsset(name: "iconArrowLeftBlack")
        public static let iconArrowRightBlack = ImageAsset(name: "iconArrowRightBlack")
        public static let iconCheckBlue = ImageAsset(name: "iconCheckBlue")
        public static let iconCheckSuccess = ImageAsset(name: "iconCheckSuccess")
        public static let iconCheckWhite = ImageAsset(name: "iconCheckWhite")
        public static let iconCheckboxActive = ImageAsset(name: "iconCheckboxActive")
        public static let iconCheckboxInactive = ImageAsset(name: "iconCheckboxInactive")
        public static let iconCheckedStatus = ImageAsset(name: "iconCheckedStatus")
        public static let iconChevronDown = ImageAsset(name: "iconChevronDown")
        public static let iconChevronRight = ImageAsset(name: "iconChevronRight")
        public static let iconClearSearch = ImageAsset(name: "iconClearSearch")
        public static let iconClose = ImageAsset(name: "iconClose")
        public static let iconCloseBlue = ImageAsset(name: "iconCloseBlue")
        public static let iconCloseDark = ImageAsset(name: "iconCloseDark")
        public static let iconCloseGrayBackground = ImageAsset(name: "iconCloseGrayBackground")
        public static let iconCloseOrange = ImageAsset(name: "iconCloseOrange")
        public static let iconCloseWhite = ImageAsset(name: "iconCloseWhite")
        public static let iconCloseWhiteWithShadow = ImageAsset(name: "iconCloseWhiteWithShadow")
        public static let iconCollapseDown = ImageAsset(name: "iconCollapseDown")
        public static let iconCollapseUp = ImageAsset(name: "iconCollapseUp")
        public static let iconContacts = ImageAsset(name: "iconContacts")
        public static let iconFailedBig = ImageAsset(name: "iconFailedBig")
        public static let iconHome = ImageAsset(name: "iconHome")
        public static let iconInformation = ImageAsset(name: "iconInformation")
        public static let iconLock = ImageAsset(name: "iconLock")
        public static let iconMerchantPlaceholder = ImageAsset(name: "iconMerchantPlaceholder")
        public static let iconNewCheckSuccess = ImageAsset(name: "iconNewCheckSuccess")
        public static let iconNewCheckedStatus = ImageAsset(name: "iconNewCheckedStatus")
        public static let iconNewInactiveCheck = ImageAsset(name: "iconNewInactiveCheck")
        public static let iconOptionsBlue = ImageAsset(name: "iconOptionsBlue")
        public static let iconProcessingBig = ImageAsset(name: "iconProcessingBig")
        public static let iconProcessingStatus = ImageAsset(name: "iconProcessingStatus")
        public static let iconRedExclamation = ImageAsset(name: "iconRedExclamation")
        public static let iconShare = ImageAsset(name: "iconShare")
        public static let iconShareLight = ImageAsset(name: "iconShareLight")
        public static let iconSuccessBig = ImageAsset(name: "iconSuccessBig")
        public static let iconWhiteExclamation = ImageAsset(name: "iconWhiteExclamation")
        public static let imagePaymayaSplash = ImageAsset(name: "imagePaymayaSplash")
        public static let logoCircle = ImageAsset(name: "logoCircle")
        public static let logoNewGreenPayMaya = ImageAsset(name: "logoNewGreenPayMaya")
        public static let logoNewGreenPayMayaSmall = ImageAsset(name: "logoNewGreenPayMayaSmall")
        public static let logoPaymaya = ImageAsset(name: "logoPaymaya")
        public static let logoPaymayaTextWhite = ImageAsset(name: "logoPaymayaTextWhite")
        public static let logoSmartPadala = ImageAsset(name: "logoSmartPadala")
        public static let newIconArrowRightBlack = ImageAsset(name: "newIconArrowRightBlack")
        public static let newLogoPayMaya = ImageAsset(name: "newLogoPayMaya")
        public static let webviewBack = ImageAsset(name: "webviewBack")
        public static let webviewForward = ImageAsset(name: "webviewForward")
        public static let webviewRefresh = ImageAsset(name: "webviewRefresh")
        public static let whiteSegmentDivider = ImageAsset(name: "whiteSegmentDivider")
        public static let whiteSegmentDividerOlderOS = ImageAsset(name: "whiteSegmentDividerOlderOS")
      }
      public enum MayaBird {
        public static let imageMaya = ImageAsset(name: "imageMaya")
        public static let imageMayaGenericError = ImageAsset(name: "imageMayaGenericError")
        public static let mayaBirdPlug = ImageAsset(name: "mayaBirdPlug")
        public static let mayaClockPending = ImageAsset(name: "mayaClockPending")
        public static let mayaFavorite = ImageAsset(name: "mayaFavorite")
        public static let mayaShop = ImageAsset(name: "mayaShop")
        public static let mayaSleeping = ImageAsset(name: "mayaSleeping")
        public static let mayaTelescope = ImageAsset(name: "mayaTelescope")
      }
    }
  }
  public enum MayaColors {
    public enum Additional {
      public static let additionalBlue = ColorAsset(name: "AdditionalBlue")
      public static let additionalCyan = ColorAsset(name: "AdditionalCyan")
      public static let additionalLightRed = ColorAsset(name: "AdditionalLightRed")
      public static let additionalOrange = ColorAsset(name: "AdditionalOrange")
      public static let additionalPink = ColorAsset(name: "AdditionalPink")
      public static let additionalPurple = ColorAsset(name: "AdditionalPurple")
    }
    public enum Background {
      public static let backgroundBlue2 = ColorAsset(name: "BackgroundBlue2")
      public static let backgroundGrey1 = ColorAsset(name: "BackgroundGrey1")
      public static let backgroundGrey11 = ColorAsset(name: "BackgroundGrey11")
      public static let backgroundPrimaryBlack = ColorAsset(name: "BackgroundPrimaryBlack")
      public static let backgroundPrimaryGreen = ColorAsset(name: "BackgroundPrimaryGreen")
      public static let backgroundPrimaryWhite = ColorAsset(name: "BackgroundPrimaryWhite")
      public static let backgroundSoftBlue = ColorAsset(name: "BackgroundSoftBlue")
    }
    public enum Blur {
      public static let blurDark = ColorAsset(name: "BlurDark")
      public static let blurLight = ColorAsset(name: "BlurLight")
    }
    public enum Button {
      public static let buttonDisabledBlack = ColorAsset(name: "ButtonDisabledBlack")
      public static let buttonDisabledGrey3 = ColorAsset(name: "ButtonDisabledGrey3")
      public static let buttonDisabledPrimaryGreen = ColorAsset(name: "ButtonDisabledPrimaryGreen")
      public static let buttonDisabledSecondaryLightGreen = ColorAsset(name: "ButtonDisabledSecondaryLightGreen")
      public static let buttonFocusedBlack = ColorAsset(name: "ButtonFocusedBlack")
      public static let buttonFocusedGrey = ColorAsset(name: "ButtonFocusedGrey")
      public static let buttonFocusedPrimaryGreen = ColorAsset(name: "ButtonFocusedPrimaryGreen")
      public static let buttonFocusedSecondaryLightGreen = ColorAsset(name: "ButtonFocusedSecondaryLightGreen")
      public static let buttonGrey3 = ColorAsset(name: "ButtonGrey3")
      public static let buttonPrimaryBlack = ColorAsset(name: "ButtonPrimaryBlack")
      public static let buttonPrimaryCharcoalBlack = ColorAsset(name: "ButtonPrimaryCharcoalBlack")
      public static let buttonPrimaryGreen = ColorAsset(name: "ButtonPrimaryGreen")
      public static let buttonPrimaryLightGreen = ColorAsset(name: "ButtonPrimaryLightGreen")
      public static let buttonPrimaryMintGreen = ColorAsset(name: "ButtonPrimaryMintGreen")
      public static let buttonPrimaryWhite = ColorAsset(name: "ButtonPrimaryWhite")
      public static let buttonSecondaryLightGreen = ColorAsset(name: "ButtonSecondaryLightGreen")
    }
    public enum Content {
      public static let contentButtonDisabled = ColorAsset(name: "ContentButtonDisabled")
      public static let contentGrey4 = ColorAsset(name: "ContentGrey4")
      public static let contentGrey5 = ColorAsset(name: "ContentGrey5")
      public static let contentGrey6 = ColorAsset(name: "ContentGrey6")
      public static let contentGrey9 = ColorAsset(name: "ContentGrey9")
      public static let contentPrimaryBlack = ColorAsset(name: "ContentPrimaryBlack")
      public static let contentPrimaryGreen = ColorAsset(name: "ContentPrimaryGreen")
      public static let contentPrimaryWhite = ColorAsset(name: "ContentPrimaryWhite")
      public static let contentSystemError = ColorAsset(name: "ContentSystemError")
    }
    public enum Focus {
      public static let focusLightGreen = ColorAsset(name: "FocusLightGreen")
    }
    public enum Grey {
      public static let grey1 = ColorAsset(name: "Grey1")
      public static let grey10 = ColorAsset(name: "Grey10")
      public static let grey11 = ColorAsset(name: "Grey11")
      public static let grey12 = ColorAsset(name: "Grey12")
      public static let grey2 = ColorAsset(name: "Grey2")
      public static let grey3 = ColorAsset(name: "Grey3")
      public static let grey4 = ColorAsset(name: "Grey4")
      public static let grey5 = ColorAsset(name: "Grey5")
      public static let grey6 = ColorAsset(name: "Grey6")
      public static let grey7 = ColorAsset(name: "Grey7")
      public static let grey8 = ColorAsset(name: "Grey8")
      public static let grey9 = ColorAsset(name: "Grey9")
    }
    public enum InitialScreen {
      public static let launchScreenGreen = ColorAsset(name: "LaunchScreenGreen")
    }
    public enum Input {
      public static let inputGrey1 = ColorAsset(name: "InputGrey1")
      public static let inputGrey4 = ColorAsset(name: "InputGrey4")
      public static let inputGrey6 = ColorAsset(name: "InputGrey6")
      public static let inputPrimaryBlack = ColorAsset(name: "InputPrimaryBlack")
      public static let inputPrimaryGreen = ColorAsset(name: "InputPrimaryGreen")
      public static let inputSystemError = ColorAsset(name: "InputSystemError")
    }
    public enum Menu {
      public static let menuGrey1 = ColorAsset(name: "MenuGrey1")
      public static let menuGrey3 = ColorAsset(name: "MenuGrey3")
      public static let menuPrimaryBlack = ColorAsset(name: "MenuPrimaryBlack")
      public static let menuPrimaryWhite = ColorAsset(name: "MenuPrimaryWhite")
    }
    public enum Modal {
      public static let modalGrey3 = ColorAsset(name: "ModalGrey3")
      public static let modalGrey4 = ColorAsset(name: "ModalGrey4")
      public static let modalPrimaryGreen = ColorAsset(name: "ModalPrimaryGreen")
      public static let modalPrimaryWhite = ColorAsset(name: "ModalPrimaryWhite")
      public static let modalSecondaryLightGreen = ColorAsset(name: "ModalSecondaryLightGreen")
      public static let modalSystemAttention = ColorAsset(name: "ModalSystemAttention")
    }
    public enum Opacity {
      public static let opacityBlue25 = ColorAsset(name: "OpacityBlue25")
      public static let opacityBlue40 = ColorAsset(name: "OpacityBlue40")
      public static let opacityGreen25 = ColorAsset(name: "OpacityGreen25")
      public static let opacityModal70 = ColorAsset(name: "OpacityModal70")
      public static let opacityPink25 = ColorAsset(name: "OpacityPink25")
      public static let opacityPurple25 = ColorAsset(name: "OpacityPurple25")
      public static let opacityWhite60 = ColorAsset(name: "OpacityWhite60")
      public static let opacityWhite90 = ColorAsset(name: "OpacityWhite90")
      public static let opacityYellow25 = ColorAsset(name: "OpacityYellow25")
    }
    public enum Primary {
      public static let primaryBlack = ColorAsset(name: "PrimaryBlack")
      public static let primaryGrownGreen = ColorAsset(name: "PrimaryGrownGreen")
      public static let primaryWhite = ColorAsset(name: "PrimaryWhite")
    }
    public enum Search {
      public static let searchGrey1 = ColorAsset(name: "SearchGrey1")
      public static let searchGrey4 = ColorAsset(name: "SearchGrey4")
      public static let searchPrimaryBlack = ColorAsset(name: "SearchPrimaryBlack")
      public static let searchPrimaryGreen = ColorAsset(name: "SearchPrimaryGreen")
    }
    public enum Secondary {
      public static let secondaryDarkGreen = ColorAsset(name: "SecondaryDarkGreen")
      public static let secondaryLightGreen = ColorAsset(name: "SecondaryLightGreen")
    }
    public enum Segment {
      public static let segmentGrey1 = ColorAsset(name: "SegmentGrey1")
      public static let segmentPrimaryWhite = ColorAsset(name: "SegmentPrimaryWhite")
    }
    public enum SnackBar {
      public static let snackBarPrimaryGreen = ColorAsset(name: "SnackBarPrimaryGreen")
      public static let snackBarSecondaryFilledLightGreen = ColorAsset(name: "SnackBarSecondaryFilledLightGreen")
      public static let snackBarSecondaryLightGreen = ColorAsset(name: "SnackBarSecondaryLightGreen")
      public static let snackBarSystemError = ColorAsset(name: "SnackBarSystemError")
      public static let snackBarSystemErrorLight = ColorAsset(name: "SnackBarSystemErrorLight")
    }
    public enum System {
      public static let systemAttention = ColorAsset(name: "SystemAttention")
      public static let systemAttention20Opacity = ColorAsset(name: "SystemAttention20Opacity")
      public static let systemError = ColorAsset(name: "SystemError")
      public static let systemError40Opacity = ColorAsset(name: "SystemError40Opacity")
      public static let systemErrorShadow = ColorAsset(name: "SystemErrorShadow")
      public static let systemSuccessErrorLight = ColorAsset(name: "SystemSuccessErrorLight")
      public static let systemSuccessLightGreen = ColorAsset(name: "SystemSuccessLightGreen")
      public static let systemWarning = ColorAsset(name: "SystemWarning")
    }
    public enum Tabs {
      public static let tabsGrey1 = ColorAsset(name: "TabsGrey1")
      public static let tabsGrey3 = ColorAsset(name: "TabsGrey3")
      public static let tabsPrimaryBlack = ColorAsset(name: "TabsPrimaryBlack")
    }
    public enum Toast {
      public static let toastColor = ColorAsset(name: "ToastColor")
      public static let toastColorLight = ColorAsset(name: "ToastColorLight")
    }
    public enum TopBar {
      public static let topBarGrey4 = ColorAsset(name: "TopBarGrey4")
      public static let topBarPrimaryBlack = ColorAsset(name: "TopBarPrimaryBlack")
      public static let topBarPrimaryGreen = ColorAsset(name: "TopBarPrimaryGreen")
      public static let topBarPrimaryWhite = ColorAsset(name: "TopBarPrimaryWhite")
      public static let topBarSecondaryLightGreen = ColorAsset(name: "TopBarSecondaryLightGreen")
    }
    public enum Updater {
      public static let updaterBackgroundColor = ColorAsset(name: "UpdaterBackgroundColor")
      public static let updaterDescriptionTextColor = ColorAsset(name: "updaterDescriptionTextColor")
      public static let updaterTitleTextColor = ColorAsset(name: "updaterTitleTextColor")
    }
  }
}
// swiftlint:enable identifier_name line_length nesting type_body_length type_name

// MARK: - Implementation Details

public final class ColorAsset {
  public fileprivate(set) var name: String

  #if os(macOS)
  public typealias Color = NSColor
  #elseif os(iOS) || os(tvOS) || os(watchOS)
  public typealias Color = UIColor
  #endif

  @available(iOS 11.0, tvOS 11.0, watchOS 4.0, macOS 10.13, *)
  public private(set) lazy var color: Color = Color(asset: self)

  #if os(iOS) || os(tvOS)
  @available(iOS 11.0, tvOS 11.0, *)
  public func color(compatibleWith traitCollection: UITraitCollection) -> Color {
    let bundle = BundleToken.bundle
    guard let color = Color(named: name, in: bundle, compatibleWith: traitCollection) else {
      fatalError("Unable to load color asset named \(name).")
    }
    return color
  }
  #endif

  fileprivate init(name: String) {
    self.name = name
  }
}

public extension ColorAsset.Color {
  @available(iOS 11.0, tvOS 11.0, watchOS 4.0, macOS 10.13, *)
  convenience init!(asset: ColorAsset) {
    let bundle = BundleToken.bundle
    #if os(iOS) || os(tvOS)
    self.init(named: asset.name, in: bundle, compatibleWith: nil)
    #elseif os(macOS)
    self.init(named: NSColor.Name(asset.name), bundle: bundle)
    #elseif os(watchOS)
    self.init(named: asset.name)
    #endif
  }
}

public struct ImageAsset {
  public fileprivate(set) var name: String

  #if os(macOS)
  public typealias Image = NSImage
  #elseif os(iOS) || os(tvOS) || os(watchOS)
  public typealias Image = UIImage
  #endif

  @available(iOS 8.0, tvOS 9.0, watchOS 2.0, macOS 10.7, *)
  public var image: Image {
    let bundle = BundleToken.bundle
    #if os(iOS) || os(tvOS)
    let image = Image(named: name, in: bundle, compatibleWith: nil)
    #elseif os(macOS)
    let name = NSImage.Name(self.name)
    let image = (bundle == .main) ? NSImage(named: name) : bundle.image(forResource: name)
    #elseif os(watchOS)
    let image = Image(named: name)
    #endif
    guard let result = image else {
      fatalError("Unable to load image asset named \(name).")
    }
    return result
  }

  #if os(iOS) || os(tvOS)
  @available(iOS 8.0, tvOS 9.0, *)
  public func image(compatibleWith traitCollection: UITraitCollection) -> Image {
    let bundle = BundleToken.bundle
    guard let result = Image(named: name, in: bundle, compatibleWith: traitCollection) else {
      fatalError("Unable to load image asset named \(name).")
    }
    return result
  }
  #endif
}

public extension ImageAsset.Image {
  @available(iOS 8.0, tvOS 9.0, watchOS 2.0, *)
  @available(macOS, deprecated,
    message: "This initializer is unsafe on macOS, please use the ImageAsset.image property")
  convenience init!(asset: ImageAsset) {
    #if os(iOS) || os(tvOS)
    let bundle = BundleToken.bundle
    self.init(named: asset.name, in: bundle, compatibleWith: nil)
    #elseif os(macOS)
    self.init(named: NSImage.Name(asset.name))
    #elseif os(watchOS)
    self.init(named: asset.name)
    #endif
  }
}

// swiftlint:disable convenience_type
private final class BundleToken {
  static let bundle: Bundle = {
    #if SWIFT_PACKAGE
    return Bundle.module
    #else
    return Bundle(for: BundleToken.self)
    #endif
  }()
}
// swiftlint:enable convenience_type
