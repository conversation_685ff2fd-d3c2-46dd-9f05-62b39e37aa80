{
  "object": {
    "pins": [
      {
        "package": "abseil",
        "repositoryURL": "https://github.com/google/abseil-cpp-binary.git",
        "state": {
          "branch": null,
          "revision": "bbe8b69694d7873315fd3a4ad41efe043e1c07c5",
          "version": "1.2024072200.0"
        }
      },
      {
        "package": "Alamofire",
        "repositoryURL": "https://github.com/Alamofire/Alamofire.git",
        "state": {
          "branch": null,
          "revision": "513364f870f6bfc468f9d2ff0a95caccc10044c5",
          "version": "5.10.2"
        }
      },
      {
        "package": "AppCheck",
        "repositoryURL": "https://github.com/google/app-check.git",
        "state": {
          "branch": null,
          "revision": "61b85103a1aeed8218f17c794687781505fbbef5",
          "version": "11.2.0"
        }
      },
      {
        "package": "CleverTapSDK",
        "repositoryURL": "https://github.com/CleverTap/clevertap-ios-sdk",
        "state": {
          "branch": null,
          "revision": "465d0ce457481d1a566dddeb26a3cb5a2796ff71",
          "version": "5.2.0"
        }
      },
      {
        "package": "CryptoSwift",
        "repositoryURL": "https://github.com/krzyzanowskim/CryptoSwift.git",
        "state": {
          "branch": null,
          "revision": "678d442c6f7828def400a70ae15968aef67ef52d",
          "version": "1.8.3"
        }
      },
      {
        "package": "Cuckoo",
        "repositoryURL": "https://github.com/Brightify/Cuckoo.git",
        "state": {
          "branch": null,
          "revision": "69c1c9aee64ec08a14461deb20878bd7ebeb9459",
          "version": "1.7.1"
        }
      },
      {
        "package": "DeviceKit",
        "repositoryURL": "https://github.com/devicekit/DeviceKit",
        "state": {
          "branch": null,
          "revision": "513b9d7e7a1bd46504a1009bbab943b75ce2f195",
          "version": "5.6.0"
        }
      },
      {
        "package": "Firebase",
        "repositoryURL": "https://github.com/firebase/firebase-ios-sdk",
        "state": {
          "branch": null,
          "revision": "d1f7c7e8eaa74d7e44467184dc5f592268247d33",
          "version": "11.11.0"
        }
      },
      {
        "package": "Glimpse",
        "repositoryURL": "https://gitlab.corp.paymaya.com/issuing/consumer-app-and-features/mobile/ios/glimpse-ios.git",
        "state": {
          "branch": null,
          "revision": "d07c23b607fb4c326dbcdf5f4c15fe6b0d7cb525",
          "version": "0.7.1"
        }
      },
      {
        "package": "GoogleAppMeasurement",
        "repositoryURL": "https://github.com/google/GoogleAppMeasurement.git",
        "state": {
          "branch": null,
          "revision": "dd89fc79a77183830742a16866d87e4e54785734",
          "version": "11.11.0"
        }
      },
      {
        "package": "GoogleDataTransport",
        "repositoryURL": "https://github.com/google/GoogleDataTransport.git",
        "state": {
          "branch": null,
          "revision": "617af071af9aa1d6a091d59a202910ac482128f9",
          "version": "10.1.0"
        }
      },
      {
        "package": "GoogleUtilities",
        "repositoryURL": "https://github.com/google/GoogleUtilities.git",
        "state": {
          "branch": null,
          "revision": "60da361632d0de02786f709bdc0c4df340f7613e",
          "version": "8.1.0"
        }
      },
      {
        "package": "gRPC",
        "repositoryURL": "https://github.com/google/grpc-binary.git",
        "state": {
          "branch": null,
          "revision": "cc0001a0cf963aa40501d9c2b181e7fc9fd8ec71",
          "version": "1.69.0"
        }
      },
      {
        "package": "GTMSessionFetcher",
        "repositoryURL": "https://github.com/google/gtm-session-fetcher.git",
        "state": {
          "branch": null,
          "revision": "c756a29784521063b6a1202907e2cc47f41b667c",
          "version": "4.5.0"
        }
      },
      {
        "package": "InteropForGoogle",
        "repositoryURL": "https://github.com/google/interop-ios-for-google-sdks.git",
        "state": {
          "branch": null,
          "revision": "040d087ac2267d2ddd4cca36c757d1c6a05fdbfe",
          "version": "101.0.0"
        }
      },
      {
        "package": "Split",
        "repositoryURL": "https://github.com/splitio/ios-client",
        "state": {
          "branch": null,
          "revision": "8fbf8d6bb162135d028b84f7b2075fcc1800a772",
          "version": "2.25.0"
        }
      },
      {
        "package": "Kanna",
        "repositoryURL": "https://github.com/tid-kijyun/Kanna.git",
        "state": {
          "branch": null,
          "revision": "41c3d28ea0eac07e4551b28def9de1ede702e739",
          "version": "5.3.0"
        }
      },
      {
        "package": "Komondor",
        "repositoryURL": "https://github.com/shibapm/Komondor.git",
        "state": {
          "branch": null,
          "revision": "90b087b1e39069684b1ff4bf915c2aae594f2d60",
          "version": "1.1.3"
        }
      },
      {
        "package": "leveldb",
        "repositoryURL": "https://github.com/firebase/leveldb.git",
        "state": {
          "branch": null,
          "revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1",
          "version": "1.22.5"
        }
      },
      {
        "package": "LifetimeTracker",
        "repositoryURL": "https://github.com/krzysztofzablocki/LifetimeTracker",
        "state": {
          "branch": null,
          "revision": "fccc98aae719f6615d1f0cfd8f5e60f3888bdf62",
          "version": "1.8.4"
        }
      },
      {
        "package": "Lottie",
        "repositoryURL": "https://github.com/airbnb/lottie-ios",
        "state": {
          "branch": null,
          "revision": "fe4c6fe3a0aa66cdeb51d549623c82ca9704b9a5",
          "version": "4.5.0"
        }
      },
      {
        "package": "Moya",
        "repositoryURL": "https://github.com/Moya/Moya.git",
        "state": {
          "branch": null,
          "revision": "b3e5a233e0d85fd4d69f561c80988590859c7dee",
          "version": "14.0.0"
        }
      },
      {
        "package": "nanopb",
        "repositoryURL": "https://github.com/firebase/nanopb.git",
        "state": {
          "branch": null,
          "revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1",
          "version": "2.30910.0"
        }
      },
      {
        "package": "PackageConfig",
        "repositoryURL": "https://github.com/shibapm/PackageConfig.git",
        "state": {
          "branch": null,
          "revision": "58523193c26fb821ed1720dcd8a21009055c7cdb",
          "version": "1.1.3"
        }
      },
      {
        "package": "PathKit",
        "repositoryURL": "https://github.com/kylef/PathKit.git",
        "state": {
          "branch": null,4Oo
          "revision": "3bfd2737b700b9a36565a8c94f4ad2b050a5e574",
          "version": "1.0.1"
        }
      },
      {
        "package": "PhoneNumberKit",
        "repositoryURL": "https://github.com/marmelroy/PhoneNumberKit",
        "state": {
          "branch": null,
          "revision": "0a025521711539e3a6e0e725ac868ac84a4983e1",
          "version": "3.7.11"
        }
      },
      {
        "package": "Promises",
        "repositoryURL": "https://github.com/google/promises.git",
        "state": {
          "branch": null,
          "revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac",
          "version": "2.4.0"
        }
      },
      {
        "package": "Raven",
        "repositoryURL": "https://gitlab.corp.paymaya.com/issuing/consumer-app-and-features/mobile/ios/raven-ios",
        "state": {
          "branch": null,
          "revision": "95485e87eea25c1e6e26b8b9843f75d970dfdf9e",
          "version": "0.12.0"
        }
      },
      {
        "package": "ReactiveCocoa",
        "repositoryURL": "https://github.com/ReactiveCocoa/ReactiveCocoa",
        "state": {
          "branch": null,
          "revision": "579364393ad587352bcce133b7b3f73392cb585b",
          "version": "11.2.2"
        }
      },
      {
        "package": "ReactiveSwift",
        "repositoryURL": "https://github.com/ReactiveCocoa/ReactiveSwift",
        "state": {
          "branch": null,
          "revision": "c43bae3dac73fdd3cb906bd5a1914686ca71ed3c",
          "version": "6.7.0"
        }
      },
      {
        "package": "RxSwift",
        "repositoryURL": "https://github.com/ReactiveX/RxSwift.git",
        "state": {
          "branch": null,
          "revision": "cec68169a048a079f461ba203fe85636548d7a89",
          "version": "5.1.3"
        }
      },
      {
        "package": "SDWebImage",
        "repositoryURL": "https://github.com/SDWebImage/SDWebImage",
        "state": {
          "branch": null,
          "revision": "8a1be70a625683bc04d6903e2935bf23f3c6d609",
          "version": "5.19.7"
        }
      },
      {
        "package": "ShellOut",
        "repositoryURL": "https://github.com/JohnSundell/ShellOut.git",
        "state": {
          "branch": null,
          "revision": "e1577acf2b6e90086d01a6d5e2b8efdaae033568",
          "version": "2.3.0"
        }
      },
      {
        "package": "SkeletonView",
        "repositoryURL": "https://github.com/Juanpe/SkeletonView",
        "state": {
          "branch": null,
          "revision": "2f5274827d310e32c09325dd3e0007120940988e",
          "version": "1.31.0"
        }
      },
      {
        "package": "Spectre",
        "repositoryURL": "https://github.com/kylef/Spectre.git",
        "state": {
          "branch": null,
          "revision": "26cc5e9ae0947092c7139ef7ba612e34646086c7",
          "version": "0.10.1"
        }
      },
      {
        "package": "Stencil",
        "repositoryURL": "https://github.com/stencilproject/Stencil.git",
        "state": {
          "branch": null,
          "revision": "4f222ac85d673f35df29962fc4c36ccfdaf9da5b",
          "version": "0.15.1"
        }
      },
      {
        "package": "StencilSwiftKit",
        "repositoryURL": "https://github.com/SwiftGen/StencilSwiftKit.git",
        "state": {
          "branch": null,
          "revision": "20e2de5322c83df005939d9d9300fab130b49f97",
          "version": "2.10.1"
        }
      },
      {
        "package": "Strongbox",
        "repositoryURL": "https://github.com/granoff/Strongbox.git",
        "state": {
          "branch": null,
          "revision": "455e1d9a2775f2265c99bb611c7fa0e092734a04",
          "version": "0.6.1"
        }
      },
      {
        "package": "swift-argument-parser",
        "repositoryURL": "https://github.com/apple/swift-argument-parser.git",
        "state": {
          "branch": null,
          "revision": "309a47b2b1d9b5e991f36961c983ecec72275be3",
          "version": "1.6.1"
        }
      },
      {
        "package": "GoogleMobileAds",
        "repositoryURL": "https://github.com/googleads/swift-package-manager-google-mobile-ads.git",
        "state": {
          "branch": null,
          "revision": "d24a9f5ad472610598449dc669befa5b7ed50658",
          "version": "12.7.0"
        }
      },
      {
        "package": "GoogleUserMessagingPlatform",
        "repositoryURL": "https://github.com/googleads/swift-package-manager-google-user-messaging-platform.git",
        "state": {
          "branch": null,
          "revision": "69b53394c5258b3fe688e625a998047d1f393497",
          "version": "3.0.0"
        }
      },
      {
        "package": "SwiftProtobuf",
        "repositoryURL": "https://github.com/apple/swift-protobuf.git",
        "state": {
          "branch": null,
          "revision": "9f0c76544701845ad98716f3f6a774a892152bcb",
          "version": "1.26.0"
        }
      },
      {
        "package": "SwiftGen",
        "repositoryURL": "https://github.com/SwiftGen/SwiftGen.git",
        "state": {
          "branch": null,
          "revision": "f7c23b63053e5a8aab4a4dbb633b24920bbb9436",
          "version": "6.6.3"
        }
      },
      {
        "package": "Swinject",
        "repositoryURL": "https://github.com/Swinject/Swinject.git",
        "state": {
          "branch": null,
          "revision": "be9dbcc7b86811bc131539a20c6f9c2d3e56919f",
          "version": "2.9.1"
        }
      },
      {
        "package": "Sync",
        "repositoryURL": "https://gitlab.corp.paymaya.com/issuing/consumer-app-and-features/mobile/ios/sync.git",
        "state": {
          "branch": null,
          "revision": "0b88a7a60e9b151c61616a95f461d34155e416b1",
          "version": "6.5.5"
        }
      },
      {
        "package": "TrustKit",
        "repositoryURL": "https://github.com/datatheorem/TrustKit",
        "state": {
          "branch": null,
          "revision": "2fb6ce731fd51f745f6cc3393462865b95c3df3c",
          "version": "3.0.4"
        }
      },
      {
        "package": "VoyagerGenericEncryption",
        "repositoryURL": "***************************:issuing/consumer-app-and-features/mobile/ios/voyager-generic-encryption.git",
        "state": {
          "branch": null,
          "revision": "6373e57e6b4e09c7197762972da903b0b3f32e3f",
          "version": "1.1.9"
        }
      },
      {
        "package": "Yams",
        "repositoryURL": "https://github.com/jpsim/Yams.git",
        "state": {
          "branch": null,
          "revision": "3d6871d5b4a5cd519adf233fbb576e0a2af71c17",
          "version": "5.4.0"
        }
      }
    ]
  },
  "version": 1
}
