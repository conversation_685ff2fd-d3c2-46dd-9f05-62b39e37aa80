PODS:
  - AppsFlyerFramework (6.10.0):
    - AppsFlyerFramework/Main (= 6.10.0)
  - AppsFlyerFramework/Main (6.10.0)
  - CloudHuiYanSDK_FW (1.0.9.23)
  - CredoAppCalendarEvents (3.2.0)
  - CredoAppContacts (3.1.0)
  - CredoAppCore (3.4.0)
  - CredoAppMedia (3.2.0)
  - CTNotificationContent (0.1.4)
  - CTNotificationService (0.1.2)
  - DebugSwift (0.3.6)
  - Flutter (3.22.300)
  - Flutter-Debug (3.22.300)
  - LicensePlist (3.25.1)
  - MBProgressHUD (1.2.0)
  - Paymaya-Flutter-Dev (0.0.6089)
  - Paymaya-Flutter-Dev-Debug (0.0.6089)
  - Paymaya-Flutter-Prod (0.0.6089)
  - Paymaya-Flutter-Stage (0.0.6089)
  - Paymaya-Flutter-Stage-Debug (0.0.6089)
  - ShieldFraud (1.5.49)
  - SwiftGen (6.6.3)
  - SwiftLint (0.56.2)
  - SZTextView (1.3.0)
  - TensorFlowLiteC (2.4.0):
    - TensorFlowLiteC/Core (= 2.4.0)
  - TensorFlowLiteC/Core (2.4.0)
  - TensorFlowLiteSwift (2.4.0):
    - TensorFlowLiteSwift/Core (= 2.4.0)
  - TensorFlowLiteSwift/Core (2.4.0):
    - TensorFlowLiteC (= 2.4.0)
  - TTTAttributedLabel (2.0.0)
  - UIImageViewAlignedSwift (0.8.1)
  - VoyagerUpdaterSDK (0.0.9)

DEPENDENCIES:
  - AppsFlyerFramework (= 6.10.0)
  - CloudHuiYanSDK_FW (from `./PayMaya-Frameworks/CloudHuiYanSDK_FW`)
  - CredoAppCalendarEvents (= 3.2.0)
  - CredoAppContacts (= 3.1.0)
  - CredoAppCore (= 3.4.0)
  - CredoAppMedia (= 3.2.0)
  - CTNotificationContent (= 0.1.4)
  - CTNotificationService (= 0.1.2)
  - DebugSwift (= 0.3.6)
  - Flutter (= 3.22.300)
  - Flutter-Debug (= 3.22.300)
  - LicensePlist (= 3.25.1)
  - MBProgressHUD (= 1.2.0)
  - Paymaya-Flutter-Dev (= 0.0.6089)
  - Paymaya-Flutter-Dev-Debug (= 0.0.6089)
  - Paymaya-Flutter-Prod (= 0.0.6089)
  - Paymaya-Flutter-Stage (= 0.0.6089)
  - Paymaya-Flutter-Stage-Debug (= 0.0.6089)
  - ShieldFraud (= 1.5.49)
  - SwiftGen (= 6.6.3)
  - SwiftLint (= 0.56.2)
  - SZTextView (= 1.3.0)
  - TensorFlowLiteSwift (= 2.4.0)
  - TTTAttributedLabel (= 2.0.0)
  - UIImageViewAlignedSwift (= 0.8.1)
  - VoyagerUpdaterSDK (= 0.0.9)

SPEC REPOS:
  "***************************:finserv/mayabank/consumer-savings/flutter-podspec-repository.git":
    - Flutter
    - Flutter-Debug
    - Paymaya-Flutter-Dev
    - Paymaya-Flutter-Dev-Debug
    - Paymaya-Flutter-Prod
    - Paymaya-Flutter-Stage
    - Paymaya-Flutter-Stage-Debug
  "***************************:issuing/consumer-app-and-features/mobile/ios/cocoapods-specs.git":
    - VoyagerUpdaterSDK
  "https://token:<EMAIL>/basic/credolab/proxy-sdk/cocoapods/index.git":
    - CredoAppCalendarEvents
    - CredoAppContacts
    - CredoAppCore
    - CredoAppMedia
  trunk:
    - AppsFlyerFramework
    - CTNotificationContent
    - CTNotificationService
    - DebugSwift
    - LicensePlist
    - MBProgressHUD
    - ShieldFraud
    - SwiftGen
    - SwiftLint
    - SZTextView
    - TensorFlowLiteC
    - TensorFlowLiteSwift
    - TTTAttributedLabel
    - UIImageViewAlignedSwift

EXTERNAL SOURCES:
  CloudHuiYanSDK_FW:
    :path: "./PayMaya-Frameworks/CloudHuiYanSDK_FW"

SPEC CHECKSUMS:
  AppsFlyerFramework: dc8599db42e1669388287d47a1f0e6b8aa35639e
  CloudHuiYanSDK_FW: 109ee1f9fb16228e3c9704ed1df6d5b1b5d1e220
  CredoAppCalendarEvents: fd1d5a689fa724b106c3a3813ea81695d3000ec3
  CredoAppContacts: 1f22a365a82e35769bbe4f07e32defdb6e9a6675
  CredoAppCore: 22e6ea6fa73aecb9c81b3645317d9dbe510d5cf0
  CredoAppMedia: f52b12ef4d08f8f4850716f6d826feb8aa184266
  CTNotificationContent: 412cc1e94ade3519b2e8742b4bacc63b7c6bb48f
  CTNotificationService: 6f60b2d2c966eb4627b691900f09eba3a0ffce63
  DebugSwift: f766d934affddea9ffe36b0cf2631cd28311481f
  Flutter: f18901a63a2021bb865374a01fa8c30510e1a431
  Flutter-Debug: 6912501566dfa9f0fca430dd09ac1a4b82ea313f
  LicensePlist: 8a2019452d6a6376d2b5a40e4170af6b62f51ee5
  MBProgressHUD: 3ee5efcc380f6a79a7cc9b363dd669c5e1ae7406
  Paymaya-Flutter-Dev: 2634077b63e775c7fc1a6620e1ee4253fe2d66b4
  Paymaya-Flutter-Dev-Debug: 44883182c3246b2992f37f8bf81cbacd2d69c624
  Paymaya-Flutter-Prod: 7a2748225b14574316447a77ce4d5576804acccc
  Paymaya-Flutter-Stage: 4cffd2a85da4c2e176eae79ddf8f9b4fbf2f3785
  Paymaya-Flutter-Stage-Debug: becd51e1b9b3e472531c6b89c4e67e588433232d
  ShieldFraud: b8d1f4926b7f109e9678e4e48c1feb4572e0d674
  SwiftGen: 4993cbf71cbc4886f775e26f8d5c3a1188ec9f99
  SwiftLint: bd7cfb914762ab5f0cbb632964849571db075706
  SZTextView: 094dc6acc9beec537685c545d6e3e0d4975174e1
  TensorFlowLiteC: 09f8ac75a76caeadb19bcfa694e97454cc1ecf87
  TensorFlowLiteSwift: f062dc1178120100d825d7799fd9f115b4a37fee
  TTTAttributedLabel: 8cffe8e127e4e82ff3af1e5386d4cd0ad000b656
  UIImageViewAlignedSwift: e4f83530f90860aa31b6840bf570034a681bd2c4
  VoyagerUpdaterSDK: 5d393e4d7c744765e07b5cb44d3d47793163bdfb

PODFILE CHECKSUM: 3e0c59d75dd50feb26e7f332a4619f41cd0fe2f1

COCOAPODS: 1.15.2
