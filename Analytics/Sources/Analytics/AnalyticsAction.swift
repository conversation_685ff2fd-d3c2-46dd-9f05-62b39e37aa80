//
//  AnalyticsAction.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 6/5/19.
//  Copyright © 2019 PayMaya Philippines, Inc. All rights reserved.
//

/// Enum that represents all possible actions Analytics module supports
/// In case of additional action, add here
public enum AnalyticsAction {
    case tap(AnalyticsModule)
    case slide(AnalyticsModule)
    case success(AnalyticsModule)
    case start(AnalyticsModule)
    case failure(AnalyticsModule)
    case duration(of: AnalyticsModule)
    case ended(AnalyticsModule)
    case appear(AnalyticsModule)
    case confirmation(AnalyticsModule)
    case swipe(AnalyticsModule)
    case sheet(of: AnalyticsModule)
    case `continue`(AnalyticsModule)
    case pass(AnalyticsModule)
    case fail(AnalyticsModule)
    case takePhoto(AnalyticsModule)
    case invalid(AnalyticsModule)
    case appeared(AnalyticsModule)
    case tapped(AnalyticsModule)
    case failed(AnalyticsModule)
    case requested(AnalyticsModule)
    case allowed(AnalyticsModule)
    case denied(AnalyticsModule)
    case opened(AnalyticsModule)
    case error(AnalyticsModule)

    public var name: String {
        let event = AnalyticsEvent(self)
        return event.getName() ?? ""
    }

    public var module: AnalyticsModule {
        switch self {
        case .tap(let module):
            return module
        case .slide(let module):
            return module
        case .success(let module):
            return module
        case .start(let module):
            return module
        case .failure(let module):
            return module
        case .duration(let module):
            return module
        case .ended(let module):
            return module
        case .appear(let module):
            return module
        case .confirmation(let module):
            return module
        case .swipe(let module):
            return module
        case .sheet(let module):
            return module
        case .continue(let module):
            return module
        case .pass(let module):
            return module
        case .fail(let module):
            return module
        case .takePhoto(let module):
            return module
        case .invalid(let module):
            return module
        case .appeared(let module):
            return module
        case .tapped(let module):
            return module
        case .failed(let module):
            return module
        case .requested(let module):
            return module
        case .allowed(let module):
            return module
        case .denied(let module):
            return module
        case .opened(let module):
            return module
        case .error(let module):
            return module
        }
    }
}

extension AnalyticsAction: Equatable {
    public static func == (lhs: AnalyticsAction, rhs: AnalyticsAction) -> Bool {
        return lhs.name == rhs.name
    }
}

extension AnalyticsAction: Hashable {
    public func hash(into hasher: inout Hasher) {
        switch self {
        case .tap(let module):
            return hasher.combine(module.object + module.name)
        case .slide(let module):
            return hasher.combine(module.object + module.name)
        case .success(let module):
            return hasher.combine(module.object + module.name)
        case .start(let module):
            return hasher.combine(module.object + module.name)
        case .failure(let module):
            return hasher.combine(module.object + module.name)
        case .duration(let module):
            return hasher.combine(module.object + module.name)
        case .ended(let module):
            return hasher.combine(module.object + module.name)
        case .appear(let module):
            return hasher.combine(module.object + module.name)
        case .confirmation(let module):
            return hasher.combine(module.object + module.name)
        case .swipe(let module):
            return hasher.combine(module.object + module.name)
        case .sheet(let module):
            return hasher.combine(module.object + module.name)
        case .continue(let module):
            return hasher.combine(module.object + module.name)
        case .pass(let module):
            return hasher.combine(module.object + module.name)
        case .fail(let module):
            return hasher.combine(module.object + module.name)
        case .takePhoto(let module):
            return hasher.combine(module.object + module.name)
        case .invalid(let module):
            return hasher.combine(module.object + module.name)
        case .appeared(let module):
            return hasher.combine(module.object + module.name)
        case .tapped(let module):
            return hasher.combine(module.object + module.name)
        case .failed(let module):
            return hasher.combine(module.object + module.name)
        case .requested(let module):
            return hasher.combine(module.object + module.name)
        case .allowed(let module):
            return hasher.combine(module.object + module.name)
        case .denied(let module):
            return hasher.combine(module.object + module.name)
        case .opened(let module):
            return hasher.combine(module.object + module.name)
        case .error(let module):
            return hasher.combine(module.object + module.name)
        }
    }
}
