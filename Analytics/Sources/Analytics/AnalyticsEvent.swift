//
//  AnalyticsEvent.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 6/4/19.
//  Copyright © 2019 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation
import UtilityExtension

private let durationEventKey = "duration"
public typealias EventAttributes = [String: Any]

public class AnalyticsEvent {
    private var action: AnalyticsAction?
    private var attributes: EventAttributes?
    private var inHouseAttributes: EventAttributes?
    private var startDate: Date?

    /// An analytic event desribes an action that can be accompanied by attributes that will be logged and sent to analytics platforms
    /// - Parameter action: descibes the user's action that triggered an event
    /// - Parameter attributes: a dictionary containing additional details about the event
    /// - Parameter glimpseAttributes: attributes that are only being sent to glimpse aside from the normal attributes
    public init(_ action: AnalyticsAction? = nil, withAttributes attributes: EventAttributes? = nil, inHouseAttributes: EventAttributes? = nil) {
        self.action = action
        self.attributes = attributes
        self.inHouseAttributes = inHouseAttributes
        if let action = action, case .duration  = action {
            startDate = Date()
        }
    }

    public func getName() -> String? {
        if let action = action {
            switch action {
            case .start(let module):
                return "\(module.name)\(getObjectOfAction(module))_START"
            case .success(let module):
                return "\(module.name)\(getObjectOfAction(module))_SUCCESS"
            case .failure(let module):
                return "\(module.name)\(getObjectOfAction(module))_FAILURE"
            case .duration(of: let module):
                return "\(module.name)\(getObjectOfAction(module))_SCREEN"
            case .tap(let module):
                return "\(module.name)_TAP\(getObjectOfAction(module))"
            case .slide(let module):
                return "\(module.name)_SLIDE\(getObjectOfAction(module))"
            case .ended(let module):
                return "\(module.name)\(getObjectOfAction(module))_ENDED"
            case .appear(let module):
                return "\(module.name)\(getObjectOfAction(module))_APPEAR"
            case .confirmation(let module):
                return "\(module.name)\(getObjectOfAction(module))_CONFIRMATION"
            case .swipe(let module):
                return "\(module.name)_SWIPE\(getObjectOfAction(module))"
            case .sheet(of: let module):
                return "\(module.name)\(getObjectOfAction(module))_SHEET"
            case .continue(let module):
                return "\(module.name)\(getObjectOfAction(module))_CONTINUE"
            case .pass(let module):
                return "\(module.name)\(getObjectOfAction(module))_PASS"
            case .fail(let module):
                return "\(module.name)\(getObjectOfAction(module))_FAIL"
            case .takePhoto(let module):
                return "\(module.name)\(getObjectOfAction(module))_TAKE_PHOTO"
            case .invalid(let module):
                return "\(module.name)\(getObjectOfAction(module))_INVALID"
            case .appeared(let module):
                return "\(module.name)\(getObjectOfAction(module))_APPEARED"
            case .tapped(let module):
                return "\(module.name)\(getObjectOfAction(module))_TAPPED"
            case .failed(let module):
                return "\(module.name)\(getObjectOfAction(module))_FAILED"
            case .requested(let module):
                return "\(module.name)\(getObjectOfAction(module))_REQUESTED"
            case .allowed(let module):
                return "\(module.name)\(getObjectOfAction(module))_ALLOWED"
            case .denied(let module):
                return "\(module.name)\(getObjectOfAction(module))_DENIED"
            case .opened(let module):
                return "\(module.name)\(getObjectOfAction(module))_OPENED"
            case .error(let module):
                return "\(module.name)\(getObjectOfAction(module))_ERROR"
            }
        }
        return nil
    }

    public func getModule() -> AnalyticsModule? {
        if let action = action {
            switch action {
            case .start(let module):
                return module
            case .success(let module):
                return module
            case .failure(let module):
                return module
            case .duration(of: let module):
                return module
            case .tap(let module):
                return module
            case .slide(let module):
                return module
            case .ended(let module):
                return module
            case .appear(let module):
                return module
            case .confirmation(let module):
                return module
            case .swipe(let module):
                return module
            case .sheet(let module):
                return module
            case .continue(let module):
                return module
            case .pass(let module):
                return module
            case .fail(let module):
                return module
            case .takePhoto(let module):
                return module
            case .invalid(let module):
                return module
            case .appeared(let module):
                return module
            case .tapped(let module):
                return module
            case .failed(let module):
                return module
            case .requested(let module):
                return module
            case .allowed(let module):
                return module
            case .denied(let module):
                return module
            case .opened(let module):
                return module
            case .error(let module):
                return module
            }
        }
        return nil
    }

    public func setAction(_ action: AnalyticsAction) {
        self.action = action
        switch action {
        case .duration, .sheet:
            startDate = Date()
        case _:
            break
        }
    }

    public func setAttributes(_ attributes: EventAttributes) {
        self.attributes = attributes
    }

    public func addAttributes(_ attributes: EventAttributes) {
        var updatedAttributes = self.attributes ?? EventAttributes()
        attributes.forEach { attribute in
            updatedAttributes.updateValue(attribute.value, forKey: attribute.key)
        }
        setAttributes(updatedAttributes)
    }

    public func getAttributes() -> EventAttributes? {
        var tupleArray = attributes?.map { (key: String, value: Any) in
            return (key, value)
        }

        if case .duration = action, let startDate = startDate {
            if tupleArray == nil {
                tupleArray = [(String, Any)]()
            }
            let elapsedTime = Int(ceil(abs(startDate.timeIntervalSinceNow)))
            tupleArray?.append((durationEventKey, "\(elapsedTime)"))
        } else if case .sheet = action, let startDate = startDate {
            if tupleArray == nil {
                tupleArray = [(String, Any)]()
            }
            let elapsedTime = Int(ceil(abs(startDate.timeIntervalSinceNow)))
            tupleArray?.append((durationEventKey, "\(elapsedTime)"))
        }

        if let tupleArray = tupleArray {
            return Dictionary(uniqueKeysWithValues: tupleArray)
        }
        return nil
    }

    public func getInHouseAttributes() -> EventAttributes? {
        var completeAttributes = EventAttributes()
        if let normalAttributes = getAttributes() {
            completeAttributes.merge(with: normalAttributes)
        }

        let tupleArray = inHouseAttributes?.map { (key: String, value: Any) in
            return (key, value)
        }
        if let tupleArray = tupleArray {
            let tempInHouseAttributes = Dictionary(uniqueKeysWithValues: tupleArray)
            completeAttributes.merge(with: tempInHouseAttributes)
        }

        if completeAttributes.count == 0 {
            return nil
        }
        return completeAttributes
    }

    private func getObjectOfAction(_ module: AnalyticsModule) -> String {
        if module.object == "" {
            return ""
        }
        return "_\(module.object)"
    }
}
