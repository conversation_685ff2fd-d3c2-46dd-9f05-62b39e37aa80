//
//  MayaChangePasswordScreen.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> Ra<PERSON> on 9/12/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Injector
import Swift<PERSON>

struct MayaChangePasswordScreen<
    ViewModel: MayaChangePasswordScreenViewModelProtocol
>: SwiftUIView, MayaViewModelBindable, MayaHostEmbeddable, AnalyticsServiceProtocol {
    @EnvironmentObject var hostProvider: MayaHostProvider<MayaChangePasswordHostingController>
    @EnvironmentObject var viewModel: ViewModel
    @Inject var analyticsService: AnalyticsService

    @State private var focusedIndex: Int?
    @State private var error: PayMayaError?

    var body: some SwiftUIView {
        VStack(spacing: 12) {
            formView
            Spacer()
            saveButton
        }
        .padding(24)
        .navigationBarBackButtonHidden()
        .mayaLoading($viewModel.isChangingPassword, on: hostingController)
        .endEditingOnTap()
        .onChange(of: viewModel.changePasswordResult) { result in
            switch result {
            case .success:
                handleChangePasswordSuccess()
            case .failure(let error):
                handleChangePasswordFailure(error: error)
            default: break
            }

            /// Reset result after appropriate handling
            viewModel.changePasswordResult = nil
        }
        .onChange(of: viewModel.didSignout) { didSignout in
            guard didSignout, let error else { return }
            hostingController?.didSignout(error: error)
        }
    }
}

// MARK: - Child views
private extension MayaChangePasswordScreen {
    var formView: some SwiftUIView {
        MayaFormView(
            fields: [currentPasswordField, newPasswordField, confirmNewPasswordField],
            focusedIndex: $focusedIndex
        ) {
            guard !saveButtonDisabled else { return }
            viewModel.changePassword()
        }
    }

    var currentPasswordField: MayaFormField {
        MayaFormField(
            title: L10n.Maya.Settings.ChangePassword.Title.currentPassword,
            placeholder: L10n.Maya.Settings.ChangePassword.Placeholder.currentPassword,
            text: $viewModel.currentPassword,
            accessoryButtons: [.secureTextEntry])
    }

    var newPasswordField: MayaFormField {
        MayaFormField(
            title: L10n.Maya.Settings.ChangePassword.Title.newPassword,
            placeholder: L10n.Maya.Settings.ChangePassword.Placeholder.newPassword,
            text: $viewModel.newPassword,
            accessoryButtons: [infoAccessoryButton, .secureTextEntry],
            error: $viewModel.newPasswordError
        )
        .validate(with: viewModel.newPasswordValidator, performedActionOnce: $viewModel.performedActionOnce)
    }

    var infoAccessoryButton: MayaTextField.AccessoryButton {
        return .custom(Image(.passwordInfo), hideOnError: true) {
            analyticsService.logMayaEvents(.tap(Analytics.ChangePassword.whatsThis))
            hostingController?.didTapChangePasswordTips()
        }
    }

    var confirmNewPasswordField: MayaFormField {
        MayaFormField(
            title: L10n.Maya.Settings.ChangePassword.Title.confirmNewPassword,
            placeholder: L10n.Maya.Settings.ChangePassword.Placeholder.confirmNewPassword,
            text: $viewModel.retypedNewPassword,
            accessoryButtons: [.secureTextEntry],
            error: $viewModel.retypedNewPasswordError
        )
        .validate(with: viewModel.retypedNewPasswordValidator, performedActionOnce: $viewModel.performedActionOnce)
    }

    var saveButton: some SwiftUIView {
        SwiftUIButton(CommonStrings.Common.save) {
            focusedIndex = nil
            viewModel.changePassword()
        }
        .mayaButtonStyle(disabled: saveButtonDisabled)
    }

    var saveButtonDisabled: Bool {
        return !viewModel.allFieldsValid || viewModel.isChangingPassword
    }
}

// MARK: - Private methods
private extension MayaChangePasswordScreen {
    func handleChangePasswordSuccess() {
        let viewModel = MayaAlertViewModel(
            title: L10n.Maya.Settings.Password.Alert.passwordChanged,
            message: L10n.Maya.Settings.Password.Alert.description,
            image: .image3DSuccess,
            closeButtonTitle: CommonStrings.Common.Modal.Got.it,
            actionsAxis: .horizontal)

        hostingController?.didTapBack()
        hostingController?.showBottomAlert(
            viewModel: viewModel,
            analyticsModuleType: .screen(Analytics.ChangePassword()))

        analyticsService.logMayaEvents(action: .success(Analytics.ChangePassword()))
    }

    func handleChangePasswordFailure(error: PayMayaError) {
        guard let errorViewModel = error.viewModel else { return }
        switch error.type {
        case .sessionTimeout(let message):
            analyticsService.logMayaEvents(
                .failure(Analytics.ChangePassword()),
                attributes: [.reason: message])
        case .validation:
            /// Do nothing
            break
        case .loginMaxAttempts:
            self.error = error
            viewModel.signout()
            analyticsService.logMayaEvents(
                .failure(Analytics.ChangePassword()),
                attributes: [.reason: errorViewModel.message])
        default:
            hostingController?.showMayaErrorAlert(viewModel: errorViewModel)
            if error.type != .validation {
                analyticsService.logMayaEvents(
                    .failure(Analytics.ChangePassword()),
                    attributes: [.reason: errorViewModel.message])
            }
        }
    }
}

// MARK: - Preview
#if DEBUG
#Preview {
    PreviewDependencyManager.initialize()

    class MockViewModel: MayaChangePasswordScreenViewModelProtocol {
        var currentPassword: String = ""
        var newPassword: String = ""
        var retypedNewPassword: String = ""
        var allFieldsValid: Bool { true }

        var newPasswordValidator: MayaTextFieldValidator = { _ in return nil }
        var retypedNewPasswordValidator: MayaTextFieldValidator = { _ in return nil }
        var newPasswordError: MayaTextFieldValidationError?
        var retypedNewPasswordError: MayaTextFieldValidationError?
        var performedActionOnce: Bool = false

        var isChangingPassword: Bool = false
        var changePasswordResult: Result<VoidSuccess, PayMayaError>?
        var didSignout: Bool = false

        func changePassword() {}
        func signout() {}
    }

    return NavigationView {
        MayaChangePasswordScreen<MockViewModel>()
            .environmentObject(MayaHostProvider<MayaChangePasswordHostingController>())
            .environmentObject(MockViewModel())
            .navigationBarTitle(L10n.Maya.Settings.Menu.Title.changePassword, displayMode: .inline)
    }
}
#endif
