//
//  MayaHostingController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> Rabago on 9/23/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Injector
import Logger
import SwiftUI
import UIKit

/// The base class for hosting controllers.
///
/// `MayaHostingController` ensures that all hosting controllers behave and look the same as existing ``ViewController`` instances.
class MayaHostingController: UIHostingController<AnyView>, MayaViewController {
    // MARK: - Dependencies
    @Inject var crashlyticsLogger: CrashlyticsLogger
    @Inject var performanceMonitoringService: PerformanceMonitoringServiceProtocol

    // MARK: - MayaNavigationBarProtocol
    var shouldUseMayaNavigationBar: Bool { false }
    var shouldUseMayaArrowBackButton: Bool { true }
    var titleFont: UIFont { CommonFontFamily.Jeko.bold.font(size: 16) }
    var titleOffset: UIOffset { UIOffset(horizontal: 0, vertical: 0) }
    var leftBarButtonInset: UIEdgeInsets { UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0) }
    var mayaNavigationBarScrollView: UIScrollView? { nil }
    var titleContentOffset: CGFloat { 0 }
    var navigationBarBackgroundColor: UIColor { .topBarPrimaryWhite }

    weak var navigationBarTitleView: MayaNavigationBarTitleViewProtocol?

    // MARK: - PerformanceMonitoringProtocol
    var shouldMonitorScreenPerformance: Bool { false }
    var shouldOverrideTTFDMetric: Bool { false }

    // MARK: - Delegates
    weak var backActionDelegate: ViewControllerBackActionDelegate?
    weak var routeActionDelegate: ViewControllerRouteActionDelegate?

    // MARK: - Navigation bar
    var navigationBarHidden: Bool = false {
        didSet { setupHiddenNavigationBar(navigationBarHidden, animated: false) }
    }

    // MARK: - Initializers
    required init(screen: AnyView) { super.init(rootView: screen) }

    dynamic required init?(coder aDecoder: NSCoder) { super.init(coder: aDecoder) }

    // MARK: - View controller lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        logViewControllerForCrashlytics()
        setupDefaultNavigationBarHidden()
        setupBackButton()
    }

    override func viewWillLayoutSubviews() {
        super.viewWillLayoutSubviews()
        logViewControllerForCrashlytics()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        logViewControllerForCrashlytics()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        logViewControllerForCrashlytics()

        setupMayaNavigationBar()
        setupHiddenNavigationBar(navigationBarHidden, animated: animated)
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        logViewControllerForCrashlytics()
        // Revisit implementation for SwiftUI
        // start methods are in bind(_:)
        stopTTDandTTFDIfNeeded()
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        logViewControllerForCrashlytics()
        setupHiddenNavigationBar(navigationBarHidden, animated: animated)
        logDurationAnalytics()
    }

    // MARK: - Actions
    func didTapBack() {
        backActionDelegate?.didTapBack(self)
    }

    func dismissKeyboard() {
        view.resignFirstResponder()
        view.endEditing(true)
    }
}

private extension MayaHostingController {
    func setupBackButton() {
        navigationItem.leftBarButtonItem = .init(
            title: CommonStrings.Common.back,
            style: .plain,
            target: self,
            action: #selector(didTapBackButton))
    }

    @objc func didTapBackButton(_ sender: Any) {
        didTapBack()
    }
}

// MARK: - Analytics methods
extension MayaHostingController {
    func logDurationAnalytics() {
        if let self = self as? MayaAnalyticsDurationManagerProtocol {
            self.logMayaDuration()
        }
    }
}

// MARK: - MayaNavigationBarProtocol methods
extension MayaHostingController {
    func setupNavigationForScrolling(_ navigationBar: UINavigationBar) {
        // Do nothing for now
    }
}
