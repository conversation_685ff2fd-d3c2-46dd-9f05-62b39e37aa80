//
//  MayaTextField.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 9/13/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import SwiftUI

/// A custom view representing the UX-defined text field.
struct MayaTextField: SwiftUIView {
    // MARK: - Title
    /// The title displayed above the text field.
    private let title: String

    // MARK: - Text field
    /// The placeholder text displayed inside the text field.
    private let placeholder: String
    /// The text entered in the text field.
    @Binding private var text: String

    // MARK: - Accessory buttons
    /// Enumeration of accessory buttons displayed alongside the text field.
    enum AccessoryButton: Equatable {
        /// A button that toggles secure text entry.
        case secureTextEntry
        /// A custom button with an icon and an action.
        ///
        /// - Parameters:
        ///   - icon: The icon displayed on the button.
        ///   - hideOnError: A flag indicating whether the button should be hidden when there is an error.
        ///   - action: The action to perform when the button is tapped.
        case custom(_ icon: Image, hideOnError: Bool = false, action: () -> Void)

        static func == (lhs: Self, rhs: Self) -> <PERSON><PERSON> {
            switch (lhs, rhs) {
            case (.secureTextEntry, .secureTextEntry): return true
            case (.custom, .custom): return true
            default: return false
            }
        }
    }
    /// The accessory buttons displayed alongside the text field.
    private let accessoryButtons: [AccessoryButton]

    // MARK: - Error
    /// The error displayed below the text field.
    @Binding private var error: MayaTextFieldValidationError?

    // MARK: - Event handlers
    /// The action to perform when the text field's commit event is triggered.
    private let onCommit: () -> Void
    /// The action to perform when the text field's editing state changes.
    private let onEditingChanged: (Bool) -> Void

    /// Initializes a new instance of `MayaTextField`.
    ///
    /// - Parameters:
    ///   - title: The title displayed above the text field.
    ///   - placeholder: The placeholder text displayed inside the text field.
    ///   - text: The text entered in the text field.
    ///   - accessoryButtons: The accessory buttons displayed alongside the text field.
    ///   - error: The error displayed below the text field.
    ///   - onCommit: The action to perform when the text field's commit event is triggered.
    ///   - onEditingChanged: The action to perform when the text field's editing state changes.
    init(
        title: String,
        placeholder: String,
        text: Binding<String>,
        accessoryButtons: [AccessoryButton] = [],
        error: Binding<MayaTextFieldValidationError?> = .constant(nil),
        onCommit: @escaping () -> Void,
        onEditingChanged: @escaping (Bool) -> Void
    ) {
        self.title = title
        self.placeholder = placeholder
        self._text = text
        self.accessoryButtons = accessoryButtons
        self._error = error
        self.onCommit = onCommit
        self.onEditingChanged = onEditingChanged
    }

    // MARK: - Editing state
    /// A flag indicating whether the text field is being edited.
    @State private var editing = false
    /// A flag indicating whether a border should be displayed around the text field.
    private var showBorder: Bool {
        return error != nil || editing
    }

    // MARK: - Secure text entry
    /// A flag indicating whether the text field should switch between secure and plain text.
    @State private var secureTextEntry: Bool = true
    /// A flag indicating whether the text field should have secure text entry enabled.
    private var shouldSecureTextEntry: Bool {
        return accessoryButtons.contains(.secureTextEntry)
    }

    // MARK: - Focus state
    /// A flag indicating whether the text field should be focused.
    @Environment(\.mayaTextFieldFocus) private var focused

    // MARK: - Text content type
    /// The text content type for the text field.
    private var textContentType: UITextContentType?

    var body: some SwiftUIView {
        VStack(alignment: .leading, spacing: 8) {
            textFieldContentView
            if let error = error {
                errorHelperView(error: error)
            }
        }
        .onChange(of: editing) { editing in
            onEditingChanged(editing)
            if !editing {
                focused?.wrappedValue = false
            }
        }
    }
}

// MARK: - Child views
private extension MayaTextField {
    var textFieldContentView: some SwiftUIView {
        HStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 0) {
                titleView
                textFieldView
            }
            if error != nil {
                errorIconView
            }
            accessoryButtonsView
        }
        .padding(.vertical, 10)
        .padding(.horizontal, 24)
        .frame(height: 56)
        .background(backgroundView)
        .onTapGesture {
            /// Expand hit area of text field
            focused?.wrappedValue = true
        }
    }

    var titleView: some SwiftUIView {
        Text(title)
            .frame(height: 16)
            .font(CommonFontFamily.Jeko.semiBold.swiftUIFont(size: 12))
            .foregroundColor(titleForegroundColor)
    }

    var titleForegroundColor: Color {
        if error == nil {
            return .primaryGrownGreen
        } else {
            return .systemError
        }
    }

    var textFieldView: some SwiftUIView {
        UITextFieldRepresentable(placeholder, text: $text, onCommit: onCommit) { editing in
            self.editing = editing
        }
        .font(CommonFontFamily.Jeko.semiBold.font(size: 16))
        .textContentType(textContentType)
        .secureTextEntry($secureTextEntry)
        .enforceSecureRules(shouldSecureTextEntry)
        .focused(focused)
        .frame(height: 20)
        .accentColor(.primaryGrownGreen)
    }

    var errorIconView: some SwiftUIView {
        Image(.iconInfoError)
            .resizeToFit(width: 24, height: 24)
            .foregroundColor(.systemError)
    }

    var accessoryButtonsView: some SwiftUIView {
        ForEach(accessoryButtons.indices, id: \.self) { index in
            switch accessoryButtons[index] {
            case .secureTextEntry:
                if shouldSecureTextEntry {
                    SwiftUIButton {
                        secureTextEntry.toggle()
                    } label: {
                        secureTextEntryImage
                    }
                }
            case .custom(let icon, let hideOnError, let action):
                if error != nil, hideOnError {
                    /// Hide custom button so return nothing
                } else {
                    SwiftUIButton {
                        action()
                    } label: {
                        icon.resizeToFit(width: 24, height: 24)
                    }
                }
            }
        }
    }

    var secureTextEntryImage: some SwiftUIView {
        Image(secureTextEntry ? .iconEyeClosed : .iconEyeOpen)
            .resizeToFit(width: 24, height: 24)
            .animation(nil, value: secureTextEntry)
    }

    var backgroundView: some SwiftUIView {
        RoundedRectangle(cornerRadius: 16)
            .fill(.inputGrey1)
            .overlay(border)
    }

    var border: some SwiftUIView {
        RoundedRectangle(cornerRadius: 16)
            .stroke(borderColor, lineWidth: 1)
            .opacity(showBorder ? 1 : 0)
            .animation(.default, value: showBorder)
    }

    var borderColor: Color {
        error == nil ? .grey4 : .systemError
    }

    func errorHelperView(error: MayaTextFieldValidationError) -> some SwiftUIView {
        Text(error.message)
            .padding(.horizontal, 24)
            .frame(height: 16)
            .font(CommonFontFamily.Jeko.semiBold.swiftUIFont(size: 12))
            .foregroundColor(.systemError)
    }
}

// MARK: - Pseudo modifiers
extension MayaTextField {
    /// Sets the text content type for the text field.
    /// - Parameter type: The text content type to set.
    /// - Returns: A new `MayaTextField` instance with the specified text content type.
    func textContentType(_ type: UITextContentType?) -> MayaTextField {
        var textField = self
        textField.textContentType = type
        return textField
    }
}

// MARK: - Focus management
extension EnvironmentValues {
    @Entry var mayaTextFieldFocus: Binding<Bool?>?
}

// MARK: - Previews
#if DEBUG
#Preview("Plain") {
    MayaTextField(
        title: L10n.Registration.Firstname.Field.name,
        placeholder: L10n.Registration.Firstname.Field.placeholder,
        text: .constant(""),
        onCommit: {},
        onEditingChanged: { _ in }
    )
    .padding()
}

#Preview("Secure text entry") {
    MayaTextField(
        title: L10n.Maya.Settings.ChangePassword.Title.currentPassword,
        placeholder: L10n.Maya.Settings.ChangePassword.Placeholder.currentPassword,
        text: .constant(""),
        accessoryButtons: [.secureTextEntry],
        onCommit: {},
        onEditingChanged: { _ in }
    )
    .padding()
}

#Preview("With custom accessory button") {
    MayaTextField(
        title: L10n.Maya.Settings.ChangePassword.Title.currentPassword,
        placeholder: L10n.Maya.Settings.ChangePassword.Placeholder.currentPassword,
        text: .constant(""),
        accessoryButtons: [
            .custom(Image(.passwordInfo)) {},
            .secureTextEntry
        ],
        onCommit: {},
        onEditingChanged: { _ in }
    )
    .padding()
}

#Preview("With error") {
    MayaTextField(
        title: L10n.Maya.Settings.ChangePassword.Title.newPassword,
        placeholder: L10n.Maya.Settings.ChangePassword.Placeholder.newPassword,
        text: .constant(""),
        accessoryButtons: [
            .custom(Image(.passwordInfo)) {},
            .secureTextEntry
        ],
        error: .constant(.init(message: L10n.Maya.Settings.Password.Validation.Not.match)),
        onCommit: {},
        onEditingChanged: { _ in }
    )
    .padding()
}
#endif
