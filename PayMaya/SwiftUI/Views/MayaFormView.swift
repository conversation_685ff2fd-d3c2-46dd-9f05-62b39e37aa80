//
//  MayaFormView.swift
//  PayMaya
//
//  Created by <PERSON> on 3/13/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import SwiftUI

/// A reusable form view for managing and displaying multiple text fields.
///
/// This view provides a structured way to handle focus state management, validation, and custom behavior for a series of text fields.
struct MayaFormView: SwiftUIView {
    /// An array of `MayaFormField` objects, representing the text fields in the form.
    let fields: [MayaForm<PERSON>ield]
    /// A binding variable that tracks the index of the currently focused text field.
    @Binding var focusedIndex: Int?
    /// An optional closure that is executed when the user finishes editing the last text field in the form.
    let onLastFieldCommit: (() -> Void)?

    /// Initializes a new instance of `<PERSON><PERSON>ormView`.
    ///
    /// - Parameters:
    ///   - fields: An array of `MayaForm<PERSON>ield` objects, representing the text fields in the form.
    ///   - focusedIndex: A binding variable that tracks the index of the currently focused text field.
    ///   - onLastFieldCommit: An optional closure that is executed when the user finishes editing the last text field in the form.
    init(fields: [<PERSON><PERSON><PERSON><PERSON>ield], focusedIndex: Binding<Int?>, onLastFieldCommit: (() -> Void)? = nil) {
        self.fields = fields
        self._focusedIndex = focusedIndex
        self.onLastFieldCommit = onLastFieldCommit
    }

    var body: some SwiftUIView {
        VStack {
            ForEach(Array(fields.enumerated()), id: \.offset) { index, field in
                MayaTextField(
                    title: field.title,
                    placeholder: field.placeholder,
                    text: field.$text,
                    accessoryButtons: field.accessoryButtons,
                    error: field.$error,
                    onCommit: {
                        if index == (fields.endIndex - 1) {
                            focusedIndex = nil
                            onLastFieldCommit?()
                        } else {
                            focusedIndex = index + 1
                        }
                    },
                    onEditingChanged: { editing in
                        guard editing else { return }
                        focusedIndex = index
                    }
                )
                .textContentType(field.textContentType)
                .focused($focusedIndex, equals: index)
                .validateFormField(
                    text: field.$text,
                    with: field.validator,
                    error: field.$error,
                    performedActionOnce: field.performedActionOnce)
            }
        }
    }
}

// MARK: - View modifiers
private extension SwiftUIView {
    @ViewBuilder
    func validateFormField(
        text: Binding<String>,
        with validator: MayaTextFieldValidator?,
        error: Binding<MayaTextFieldValidationError?>,
        performedActionOnce: Binding<Bool>?
    ) -> some SwiftUIView {
        if let validator, let performedActionOnce {
            validate(text: text, with: validator, error: error, performedActionOnce: performedActionOnce)
        } else {
            self
        }
    }
}

#if DEBUG
#Preview {
    struct PreviewWrapper: SwiftUIView {
        @State private var focusedIndex: Int?

        var body: some SwiftUIView {
            MayaFormView(
                fields: [
                    MayaFormField(
                        title: L10n.Registration.Firstname.Field.name,
                        placeholder: L10n.Registration.Firstname.Field.placeholder,
                        text: .constant("")),
                    MayaFormField(
                        title: L10n.Maya.Settings.ChangePassword.Title.currentPassword,
                        placeholder: L10n.Maya.Settings.ChangePassword.Placeholder.currentPassword,
                        text: .constant(""),
                        accessoryButtons: [.secureTextEntry])
                ],
                focusedIndex: $focusedIndex
            )
            .padding()
        }
    }

    return PreviewWrapper()
}
#endif
