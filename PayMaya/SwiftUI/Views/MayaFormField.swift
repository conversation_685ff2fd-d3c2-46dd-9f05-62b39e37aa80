//
//  MayaFormField.swift
//  PayMaya
//
//  Created by <PERSON> on 3/14/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import SwiftUI
import UIKit

/// A data model that holds necessary form field information.
///
/// This model encapsulates the properties required to configure a `MayaTextField` and any additional data provided by the view model.
struct MayaFormField {
    // MARK: - Basic components
    let title: String
    let placeholder: String
    @Binding var text: String
    let accessoryButtons: [MayaTextField.AccessoryButton]
    @Binding var error: MayaTextFieldValidationError?

    // MARK: - Validation
    var validator: MayaTextFieldValidator?
    var performedActionOnce: Binding<Bool>?

    // MARK: - Content type
    var textContentType: UITextContentType?

    /// Initializes a new `MayaFormField` instance.
    ///
    /// - Parameters:
    ///   - title: The title displayed for the form field.
    ///   - placeholder: The placeholder text shown when the field is empty.
    ///   - text: The current text value of the form field.
    ///   - accessoryButtons: An array of accessory buttons associated with the form field, if any.
    ///   - error: A binding to the validation error associated with the form field, if any.
    ///   - validator: An optional validator used to validate the form field's input.
    ///   - performedActionOnce: A binding that tracks whether an action has been performed on the field.
    init(
        title: String,
        placeholder: String,
        text: Binding<String>,
        accessoryButtons: [MayaTextField.AccessoryButton] = [],
        error: Binding<MayaTextFieldValidationError?> = .constant(nil)
    ) {
        self.title = title
        self.placeholder = placeholder
        self._text = text
        self.accessoryButtons = accessoryButtons
        self._error = error
    }
}

// MARK: - Pseudo modifiers
extension MayaFormField {
    /// Validates the text entered in the text field.
    ///
    /// - Parameters:
    ///   - validator: A closure that validates the text.
    ///   - performedActionOnce: A flag that indicates whether the validation action has been performed once.
    /// - Returns: A new `MayaFormField` instance with validation configured.
    func validate(
        with validator: @escaping MayaTextFieldValidator,
        performedActionOnce: Binding<Bool>
    ) -> MayaFormField {
        var field = self
        field.validator = validator
        field.performedActionOnce = performedActionOnce
        return field
    }

    /// Sets the text content type for the text field.
    ///
    /// - Parameter contentType: The `UITextContentType` to be set.
    /// - Returns: A new `MayaFormField` instance with the specified content type.
    func textContentType(_ contentType: UITextContentType) -> MayaFormField {
        var field = self
        field.textContentType = contentType
        return field
    }
}
