//
//  UITextFieldRepresentable.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 6/16/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import SwiftUI
import UIKit

/// A `UIViewRepresentable` implementation that wraps a `UITextField` for use in SwiftUI.
struct UITextFieldRepresentable: UIViewRepresentable {
    // MARK: - Basic components
    /// The placeholder text displayed inside the text field.
    private let placeholder: String
    /// The text entered in the text field.
    @Binding private var text: String

    // MARK: - Event handlers
    /// The action to perform when the text field's commit event is triggered.
    private let onCommit: () -> Void
    /// The action to perform when the text field's editing state changes.
    private let onEditingChanged: (Bool) -> Void

    // MARK: - Customizations
    /// The font used for the text field's content.
    private var font: UIFont?
    /// The text content type for the text field.
    private var textContentType: UITextContentType?
    /// A flag indicating whether the text field should switch between secure and plain text.
    private var secureTextEntry: Binding<Bool>?
    /// Enforces secure rules for the text field.
    private var enforceSecureRules: Bool?
    /// A flag indicating whether the text field should be focused.
    private var focused: Binding<Bool?>?

    /// Initializes a `UITextFieldRepresentable` instance.
    ///
    /// - Parameters:
    ///   - placeholder: The placeholder text displayed inside the text field.
    ///   - text: The text entered in the text field.
    ///   - onCommit: The action to perform when the text field's commit event is triggered.
    ///   - onEditingChanged: The action to perform when the text field's editing state changes.
    init(
        _ placeholder: String,
        text: Binding<String>,
        onCommit: @escaping () -> Void,
        onEditingChanged: @escaping (Bool) -> Void
    ) {
        self.placeholder = placeholder
        self._text = text
        self.onCommit = onCommit
        self.onEditingChanged = onEditingChanged
    }

    func makeUIView(context: Context) -> UITextField {
        let textField = UITextField(frame: .zero)
        textField.borderStyle = .none
        textField.placeholder = placeholder
        textField.font = font
        textField.textContentType = textContentType
        textField.delegate = context.coordinator
        textField.setContentCompressionResistancePriority(.defaultLow, for: .horizontal)
        return textField
    }

    func updateUIView(_ uiView: UITextField, context: Context) {
        if uiView.text != text {
            uiView.text = text
        }

        if let textContentType, uiView.textContentType != textContentType {
            uiView.textContentType = textContentType
        }

        if let secureTextEntry = secureTextEntry?.wrappedValue,
           uiView.isSecureTextEntry != secureTextEntry {
            uiView.isSecureTextEntry = secureTextEntry
        }

        if let enforceSecureRules, enforceSecureRules {
            uiView.autocapitalizationType = .none
            uiView.autocorrectionType = .no
            uiView.keyboardType = .asciiCapable /// Disable emojis
        }

        if let focused = focused?.wrappedValue {
            if focused && !uiView.isFirstResponder {
                /// Automatically resigns the current first responder
                uiView.becomeFirstResponder()
            }
        } else if uiView.isFirstResponder {
            /// Explicitly resign first responder only if there should be no active text field
            uiView.resignFirstResponder()
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
}

// MARK: - Coordinator
extension UITextFieldRepresentable {
    class Coordinator: NSObject, UITextFieldDelegate {
        var parent: UITextFieldRepresentable

        init(_ parent: UITextFieldRepresentable) {
            self.parent = parent
        }

        func textFieldDidChangeSelection(_ textField: UITextField) {
            var text = textField.text ?? ""

            if let enforceSecureRules = parent.enforceSecureRules, enforceSecureRules {
                /// Disable whitespaces
                text = text.replacingOccurrences(of: " ", with: "")
            }

            DispatchQueue.main.async {
                self.parent.text = text
            }
        }

        func textFieldDidBeginEditing(_ textField: UITextField) {
            DispatchQueue.main.async {
                self.parent.onEditingChanged(true)
                self.parent.focused?.wrappedValue = true
            }
        }

        func textFieldDidEndEditing(_ textField: UITextField) {
            DispatchQueue.main.async {
                self.parent.onEditingChanged(false)
                self.parent.focused?.wrappedValue = false
            }
        }

        func textFieldShouldReturn(_ textField: UITextField) -> Bool {
            DispatchQueue.main.async {
                self.parent.onCommit()
            }
            return true
        }
    }
}

// MARK: - Pseudo view modifiers
extension UITextFieldRepresentable {
    /// Sets the font for the text field.
    ///
    /// - Parameter font: The font to use for the text field's content.
    /// - Returns: A modified `UITextFieldRepresentable` with the font set.
    func font(_ font: UIFont?) -> UITextFieldRepresentable {
        var view = self
        view.font = font
        return view
    }

    /// Sets the text content type for the text field.
    ///
    /// - Parameter textContentType: The text content type to use for the text field.
    /// - Returns: A modified `UITextFieldRepresentable` instance with the text content type configured.
    func textContentType(_ textContentType: UITextContentType?) -> UITextFieldRepresentable {
        var view = self
        view.textContentType = textContentType
        return view
    }

    /// Configures whether the text field should obscure its text or not.
    ///
    /// - Parameter secureTextEntry: A flag indicating whether the text field should switch between secure and plain text.
    /// - Returns: A modified `UITextFieldRepresentable` instance with secure text entry configured.
    func secureTextEntry(_ secureTextEntry: Binding<Bool>) -> UITextFieldRepresentable {
        var view = self
        view.secureTextEntry = secureTextEntry
        return view
    }

    /// Enforces secure rules for the text field.
    ///
    /// This modifier configures the text field to:
    /// - Disable autocapitalization
    /// - Disable autocorrection
    /// - Use an ASCII-capable keyboard (disabling emojis)
    /// - Remove any whitespace characters from the input
    ///
    /// - Parameter enforceSecureRules: A flag indicating whether to enforce secure rules.
    /// - Returns: A modified `UITextFieldRepresentable` instance with secure rules enforced.
    func enforceSecureRules(_ enforceSecureRules: Bool) -> UITextFieldRepresentable {
        var view = self
        view.enforceSecureRules = enforceSecureRules
        return view
    }

    /// Configures whether the text field is focused or not.
    ///
    /// - Parameter focused: A flag indicating whether the text field should be focused.
    /// - Returns: A modified `UITextFieldRepresentable` instance with focus management configured.
    func focused(_ focused: Binding<Bool?>? = nil) -> UITextFieldRepresentable {
        var view = self
        view.focused = focused
        return view
    }
}
