//
//  FocusModifier.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 3/11/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import SwiftUI

/// A view modifier that sets the focus state of a MayaTextField.
struct FocusModifier<Value: Hashable>: ViewModifier {
    @Binding var focus: Value?
    let value: Value
    @State private var internalFocus: Bool?

    func body(content: Content) -> some SwiftUIView {
        content
            .environment(\.mayaTextFieldFocus, $internalFocus)
            .onChange(of: focus) { focus in
                if let focus {
                    let shouldFocus = focus == value
                    internalFocus = shouldFocus
                } else {
                    internalFocus = nil
                }
            }
    }
}
