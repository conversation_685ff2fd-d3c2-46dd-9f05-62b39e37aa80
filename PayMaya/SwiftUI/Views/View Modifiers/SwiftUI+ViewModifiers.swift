//
//  SwiftUI+ViewModifiers.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 9/16/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import SwiftUI

// MARK: - Common view modifiers
extension SwiftUIView {
    /// Displays the UX-defined loading indicator on top of the view.
    ///
    /// - Parameters:
    ///   - loading: A flag that determines whether the loading indicator should be visible.
    ///   - hostingController: The hosting controller of the view.
    /// - Returns: The modified view that displays the loading indicator when `loading` is true.
    func mayaLoading(
        _ loading: Binding<Bool>,
        on hostingController: MayaHostingController?
    ) -> some SwiftUIView {
        modifier(MayaLoaderModifier(loading: loading, hostingController: hostingController))
    }

    /// Removes focus from any text field when tapped.
    ///
    /// This view modifier should ideally be applied to the outermost view.
    ///
    /// - Returns: The modified view that removes focus from any text field when tapped.
    func endEditingOnTap() -> some SwiftUIView {
        self.contentShape(Rectangle())
            .onTapGesture(perform: endEditing)
    }

    /// Ends editing for the current view, removing focus from any text field.
    func endEditing() {
        UIApplication.shared.sendAction(
            #selector(UIResponder.resignFirstResponder),
            to: nil, from: nil, for: nil)
    }
}

// MARK: - Image modifiers
extension Image {
    /// Resizes the image to fit within the specified dimensions.
    ///
    /// - Parameters:
    ///   - width: The width to fit the image within.
    ///   - height: The height to fit the image within.
    /// - Returns: The modified image that is resized to fit within the specified dimensions.
    func resizeToFit(width: CGFloat, height: CGFloat) -> some SwiftUIView {
        self.resizable()
            .scaledToFit()
            .frame(width: width, height: height)
    }
}

// MARK: - Button modifiers
extension SwiftUIView {
    /// Applies the UX-defined style to the button.
    ///
    /// - Parameter disabled: A flag that determines whether the button is disabled.
    /// - Returns: The modified button with the UX-defined style applied.
    func mayaButtonStyle(disabled: Bool) -> some SwiftUIView {
        self.buttonStyle(MayaButtonStyle(disabled: disabled))
            .disabled(disabled)
    }
}

// MARK: - Common TextField modifiers
extension SwiftUIView {
    /// Enforces secure rules for the text field.
    ///
    /// This view modifier configures the text field to:
    /// - Disable autocapitalization
    /// - Disable autocorrection
    /// - Use an ASCII-capable keyboard (disabling emojis)
    /// - Remove any whitespace characters from the input
    ///
    /// - Parameter text: The text entered in the text field.
    /// - Returns: The modified text field with secure rules enforced.
    func enforceSecureRules(text: Binding<String>) -> some SwiftUIView {
        modifier(EnforceSecureRulesModifier(text: text))
    }

    /// Validates the text entered in the text field.
    ///
    /// - Parameters:
    ///   - text: The text entered in the text field.
    ///   - validator: A closure that validates the text.
    ///   - error: The error returned by a failed validation.
    ///   - performedActionOnce: A flag that indicates whether the validation action has been performed once.
    /// - Returns: The modified text field with the validation applied.
    func validate(
        text: Binding<String>,
        with validator: @escaping MayaTextFieldValidator,
        error: Binding<MayaTextFieldValidationError?>,
        performedActionOnce: Binding<Bool>
    ) -> some SwiftUIView {
        modifier(MayaTextFieldValidationModifier(
            text: text,
            validator: validator,
            error: error,
            performedActionOnce: performedActionOnce))
    }
}

// MARK: - MayaTextField modifiers
extension MayaTextField {
    /// Sets the focus state of the text field.
    ///
    /// This view modifier can be used this way:
    /// ```swift
    /// struct MyScreen: SwiftUIView {
    ///    enum FocusedField: Hashable {
    ///        case username
    ///        case password
    ///    }
    ///    @State private var focusedField: FocusedField?
    ///
    ///    var body: some SwiftUIView {
    ///        VStack {
    ///            TextField("Username", text: $username)
    ///                .focused($focusedField, equals: .username)
    ///            TextField("Password", text: $password)
    ///                .focused($focusedField, equals: .password)
    ///        }
    ///    }
    /// }
    /// ```
    ///
    /// - Parameters:
    ///   - focus: The currently focused value.
    ///   - value: The focus value of the text field.
    /// - Returns: The modified text field with its focus state set.
    func focused<Value: Hashable>(_ focus: Binding<Value?>, equals value: Value) -> some SwiftUIView {
        modifier(FocusModifier(focus: focus, value: value))
    }

    /// Sets the focus state of the text field.
    ///
    /// This view modifier can be used this way:
    /// ```swift
    /// struct MyScreen: SwiftUIView {
    ///    @State private var isUsernameFocused: Bool = true
    ///
    ///    var body: some SwiftUIView {
    ///        TextField("Username", text: $username)
    ///            .focused($isUsernameFocused)
    ///    }
    /// }
    /// ```
    ///
    /// - Parameter focus: A flag that determines whether the text field should be focused.
    /// - Returns: The modified text field with its focus state set.
    func focused(_ focus: Binding<Bool?>) -> some SwiftUIView {
        modifier(FocusModifier(focus: focus, value: true))
    }
}
