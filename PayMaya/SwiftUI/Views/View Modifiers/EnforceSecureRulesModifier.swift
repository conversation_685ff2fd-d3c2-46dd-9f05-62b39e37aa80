//
//  EnforceSecureRulesModifier.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 3/11/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import SwiftUI

/// A view modifier that enforces secure rules for a text field.
///
/// This view modifier configures the text field to:
/// - Disable autocapitalization
/// - Disable autocorrection
/// - Use an ASCII-capable keyboard (disabling emojis)
/// - Remove any whitespace characters from the input
struct EnforceSecureRulesModifier: ViewModifier {
    @Binding var text: String

    func body(content: Content) -> some SwiftUIView {
        content
            .autocapitalization(.none)
            .autocorrectionDisabled()
            .keyboardType(.asciiCapable) /// Disable emojis
            .onChange(of: text) { newText in
                /// Disable whitespaces
                text = newText.replacingOccurrences(of: " ", with: "")
            }
    }
}
