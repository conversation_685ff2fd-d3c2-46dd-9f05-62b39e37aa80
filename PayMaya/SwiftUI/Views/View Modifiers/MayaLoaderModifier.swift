//
//  MayaLoaderModifier.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> Ra<PERSON> on 3/11/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import SwiftUI

/// A view modifier that displays the UX-defined loading indicator on top of a view.
struct MayaLoaderModifier: ViewModifier {
    @Binding var loading: Bo<PERSON>
    weak var hostingController: MayaHostingController?

    func body(content: Content) -> some SwiftUIView {
        content.onChange(of: loading) { loading in
            if loading {
                hostingController?.dismissKeyboard()
                hostingController?.showMayaLoader()
            } else {
                hostingController?.hideMayaLoader()
            }
        }
    }
}
