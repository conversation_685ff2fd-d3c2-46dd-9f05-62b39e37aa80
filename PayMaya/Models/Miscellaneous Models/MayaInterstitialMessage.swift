//
//  MayaInterstitialMessage.swift
//  PayMaya
//
//  Created by <PERSON> on 6/5/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import UIKit

struct MayaInterstitialMessage {
    let text: Text
    let config: Config
}

extension MayaInterstitialMessage {
    init(emoji: String, message: String, config: Config = Config()) {
        self.text = Text(emoji: emoji, message: message)
        self.config = config
    }

    init(emoji: String, attributedMessage: NSAttributedString, config: Config = Config()) {
        self.text = Text(emoji: emoji, attributedMessage: attributedMessage)
        self.config = config
    }
}

extension MayaInterstitialMessage {
    struct Text {
        let emoji: String
        let message: String?
        let attributedMessage: NSAttributedString?

        init(emoji: String, message: String) {
            self.emoji = emoji
            self.message = message
            self.attributedMessage = nil
        }

        init(emoji: String, attributedMessage: NSAttributedString) {
            self.emoji = emoji
            self.attributedMessage = attributedMessage
            self.message = nil
        }
    }

    struct Config {
        let textSize: CGFloat
        let lineHeightMultiple: CGFloat
        let spacing: CGFloat

        init(textSize: CGFloat = 16, lineHeightMultiple: CGFloat = 1.24, spacing: CGFloat = 16) {
            self.textSize = textSize
            self.lineHeightMultiple = lineHeightMultiple
            self.spacing = spacing
        }
    }
}
