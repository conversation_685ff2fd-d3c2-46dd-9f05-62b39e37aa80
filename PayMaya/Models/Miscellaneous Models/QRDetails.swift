//
//  QRDetails.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> Barroga on 7/15/21.
//  Copyright © 2021 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation
import MPQRCoreSDK

struct QRDetails: Equatable {
    var acquirerRequiredInfo: String?
    var mobileNumber: String?
    var proxyNotifFlag: String?
    var creditAccountNumber: String?
    var globalUniqueIdentifier: String?
    var acquirerId: String?
    var paymentType: String?
    var merchantCountryCode: String?
    var merchantCity: String?
    var merchantId: String?
    var merchantMobileNumber: String?
    var merchantName: String?
    var merchantPostalCode: String?
    var payloadFormatIndicator: String?
    var pointOfInitiation: String?
    var transactionCurrencyCode: String?
    var merchantCategoryCode: String?
    var merchantCrc: String?
    var merchantCreditAccountNumber: String?
    var transactionAmount: String?
    var additionalData: QRAdditionalData?
    var additionalDataPayload: AdditionalData?
    var p2pMerchantAccountInformation: MAIData?
    var p2mMerchantAccountInformation: MAIData?
    var payloadInformationIndicator: String?
    var languageData: LanguageData?
    var convenienceIndicator: String?
    var convenienceFeeFixed: String?
    var convenienceFeePercentage: String?
}

struct QRAdditionalData: Equatable {
    var globalUniqueIdentifier: String?
    var billNumber: String?
    var additionalMobileNumber: String?
    var storeName: String?
    var merchantPostalCode: String?
    var loyaltyNumber: String?
    var referenceId: String?
    var customerLabel: String?
    var terminalId: String?
    var purpose: String?
    var additionalRequestData: String?
}

extension QRDetails {
    var maskedPhMobileNumber: String? {
        guard let mobileNumber = mobileNumber else { return nil }
        let formattedNumber = String.formatToPhilippinesMSISDN(mobileNumber)
        return "+63 *** *** \(formattedNumber.suffix(4))"
    }
}
