//
//  KYCDataRequest.swift
//  PayMaya
//
//  Created by <PERSON> on 01/08/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation
import ReactiveSwift

struct KYCDataWrapper: Codable {
    let data: KYCData?

    init(data: KYCData? = nil) {
        self.data = data
    }
}

// MARK: KYCData structure
struct KYCData: Codable {
    let benefits: [Benefit]
    let documents: [Document]?
    let photoTips: [MediaTip]
    let livenessTips: [MediaTip]
    let submit: Submit
    let review: Review
    let countries: [Country]
    let nationalities: [Nationality]
    let workNatures: [WorkNature]
    let incomeSources: [IncomeSource]
    let validationRules: [ValidationRule]
    let additionalDocuments: AdditionalDocuments
    let nameRule: NameRule
    let recommendedDocuments: [Document]?
    let otherPrimaryDocuments: [Document]?
    let secondaryDocuments: [Document]?
    let captureGuides: [CaptureGuide]?
    let livePhotoTips: [MediaTip]?
    let imageQ<PERSON>uides: [ImageQualityGuide]?
    let imageQualityMaxRetries: Int?
    let uploadGuides: UploadGuide?
    let intro: Intro?
    let selfieTips: SelfieTips?

    enum CodingKeys: String, CodingKey {
        case benefits, documents, photoTips = "photo_tips", livenessTips = "video_tips", submit, review, countries, nationalities, workNatures = "work_natures", incomeSources = "income_sources", validationRules = "validation_rules", additionalDocuments = "additional_document", nameRule = "rule", recommendedDocuments = "recommended_documents", otherPrimaryDocuments = "other_primary_documents", secondaryDocuments = "secondary_documents", captureGuides = "capture_guides",
            livePhotoTips = "live_photo_tips", imageQualityGuides = "image_quality_guides", imageQualityMaxRetries = "image_quality_max_retries", uploadGuides = "upload_guides", intro = "intro", selfieTips = "selfie_tips"
    }

    func getIDLevel(_ key: String) -> String {
        if (recommendedDocuments?.first(where: { $0.key == key })) != nil {
            return KYCData.IDLevel.recommendedIDs.rawValue
        } else if (otherPrimaryDocuments?.first(where: { $0.key == key })) != nil {
            return KYCData.IDLevel.otherPrimaryIDs.rawValue
        } else if (secondaryDocuments?.first(where: { $0.key == key })) != nil {
            return KYCData.IDLevel.secondaryIDs.rawValue
        } else {
            return ""
        }
    }

    func getCountryCodeFromName(_ name: String?) -> String? {
        guard let name = name else { return nil }
        let country = countries.first(where: { $0.name == name })
        return country?.code
    }

    func getNationalityCodeFromName(_ name: String?) -> String? {
        guard let name = name else { return nil }
        let nationality = nationalities.first(where: { $0.name == name })
        return nationality?.code
    }

    func isSecondaryID(_ key: String) -> Bool {
        let secondaryDocument = secondaryDocuments?.first(where: { $0.key == key })
        return secondaryDocument != nil
    }

    func orderedVisibleDocuments() -> [Document]? {
        documents?.filter { $0.isShownOnList ?? true }
            .sorted {
                guard let order0 = $0.order, let order1 = $1.order else { return false }
                return order0 < order1
            } ?? recommendedDocuments
    }

    var orderedBenefits: [Benefit] {
        benefits.sorted {
            guard let order0 = $0.order, let order1 = $1.order else { return false }
            return order0 < order1
        }
    }

    var orderedPhotoTips: [MediaTip] {
        photoTips.sorted {
            guard let order0 = $0.order, let order1 = $1.order else { return false }
            return order0 < order1
        }
    }

    var orderedLivenessTips: [MediaTip] {
        livenessTips.sorted {
            guard let order0 = $0.order, let order1 = $1.order else { return false }
            return order0 < order1
        }
    }

    var livePhotoTipsString: String {
        guard let livePhotoTipsArray = livePhotoTips else { return "" }
        let livePhotoTipsDescriptionsArray = livePhotoTipsArray.map { L10n.Maya.Ekyc.Capture.Photo.livetip($0.description) }
        return livePhotoTipsDescriptionsArray.joined(separator: "\n")
    }

    var livePhotoNumberedTipsString: String {
        guard let livePhotoTipsArray = livePhotoTips else { return "" }
            // Map the array to a numbered bullet string
            let livePhotoTipsDescriptionsArray = livePhotoTipsArray.enumerated().map { index, tip in
                return "\(index + 1)  \(tip.description)"
            }
            return livePhotoTipsDescriptionsArray.joined(separator: "\n")
    }
}

// MARK: ID level values for Analytics
extension KYCData {
    public enum IDLevel: String {
        case recommendedIDs = "Recommended"
        case otherPrimaryIDs = "Other"
        case secondaryIDs = "Secondary"
    }
}

// MARK: KYCData ImageQualityGuide structure
extension KYCData {
    struct ImageQualityGuide: Codable, Equatable {
        let id: Int
        let feedback: ImageQFeedback
        let confidenceThresholds: [ImageQThreshold]
        let allowedIDs: [ImageQAllowedID]

        var key: String {
            return feedback.key
        }

        var threshold: Int {
            if let passedThreshold = confidenceThresholds.first(where: { $0.decision == KYCData.ImageQualityGuide.ImageQGuideDecision.passed.rawValue }) {
                return passedThreshold.threshold
            } else {
                return 0
            }
        }

        func isAllowed(_ key: String) -> Bool {
            if allowedIDs.first?.key == "*" {
                return true
            }
            let allowedID = allowedIDs.first(where: { $0.key.lowercased() == key })
            return allowedID != nil
        }

        func passesThreshold(confidenceScore: Int, decision: ImageQualityModelDataHandler.ImageQualityLabels) -> Bool {
            return confidenceScore >= threshold
        }

        enum CodingKeys: String, CodingKey {
            case id, feedback, confidenceThresholds = "confidence_thresholds", allowedIDs = "allowed_ids"
        }

        public enum ImageQGuideDecision: String {
            case passed = "PASSED"
        }
    }
}

// MARK: KYCData Intro structure
extension KYCData {
    struct Intro: Codable, Equatable {
        let newRegHeader: String
        let newRegIntroDescription: String
        let kyc0Header: String
        let kyc0Description: String
        let kyc1Header: String
        let kyc1Description: String
        let inviteCodeDescription: String
        let upgradeNowHeaderKyc0: String
        let upgradeNowHeaderKyc1: String
        let upgradeNowDescription: String
        let upgradeStepsGuide: UpgradeStepsGuide
        let unlockEverythingHeaderKyc0: String
        let unlockEverythingHeaderKyc1: String
        let unlockEverythingDetails: Features
        let safeTransactionHeader: String
        let safeTransactionDescription: String
        let upgradeButtonTitleKyc0: String
        let upgradeButtonTitleKyc1: String
        let doItLaterButtonTitle: String

        enum CodingKeys: String, CodingKey {
            case newRegHeader = "new_registration_upgrade_intro_header", newRegIntroDescription = "new_registration_upgrade_intro_description", kyc0Header = "kyc0_upgrade_intro_header", kyc0Description = "kyc0_upgrade_intro_description", kyc1Header = "kyc1_upgrade_intro_header", kyc1Description = "kyc1_upgrade_intro_description", inviteCodeDescription = "invite_code_description", upgradeNowHeaderKyc0 = "kyc0_upgrade_now_header", upgradeNowHeaderKyc1 = "kyc1_upgrade_now_header", upgradeNowDescription = "upgrade_now_description", upgradeStepsGuide = "upgrade_steps_guide", unlockEverythingHeaderKyc0 = "kyc0_unlock_everything_header", unlockEverythingHeaderKyc1 = "kyc1_unlock_everything_header", unlockEverythingDetails = "unlock_everything_details", safeTransactionHeader = "safe_transaction_header", safeTransactionDescription = "safe_transaction_description", upgradeButtonTitleKyc0 = "upgrade_btn_title_kyc0", upgradeButtonTitleKyc1 = "upgrade_btn_title_kyc1", doItLaterButtonTitle = "do_it_later_btn_title"
        }
    }
}

// MARK: KYCData Upgrade Steps Guide structure
extension KYCData {
    struct UpgradeStepsGuide: Codable, Equatable {
        let images: [SampleImage]

        enum CodingKeys: String, CodingKey {
            case images
        }
    }
}

// MARK: KYCData Features structure
extension KYCData {
    struct Features: Codable, Equatable {
        let features: [SampleImage]
        let featureIconURL: String

        enum CodingKeys: String, CodingKey {
            case features, featureIconURL = "feature_icon_url"
        }
    }
}

// MARK: KYCData ImageQFeedback structure
extension KYCData {
    struct ImageQFeedback: Codable, Equatable {
        let id: Int
        let key: String

        enum CodingKeys: String, CodingKey {
            case id, key
        }
    }
}

// MARK: KYCData ImageQThreshold structure
extension KYCData {
    struct ImageQThreshold: Codable, Equatable {
        let id: Int
        let threshold: Int
        let decision: String

        enum CodingKeys: String, CodingKey {
            case id, threshold, decision
        }
    }
}

// MARK: KYCData ImageQAllowedID structure
extension KYCData {
    struct ImageQAllowedID: Codable, Equatable {
        let id: Int
        let key: String
        let action: String

        enum CodingKeys: String, CodingKey {
            case id, key, action
        }
    }
}

// MARK: KYCData Benefit structure
extension KYCData {
    struct Benefit: Codable, Equatable {
        let id: Int
        let order: Int?
        let key: String
        let description: String
        let iconUrl: String

        enum CodingKeys: String, CodingKey {
            case id, order, key, description, iconUrl
        }
    }
}

// MARK: KYCData Document structure
extension KYCData {
    struct Document: Codable, Equatable {
        let key: String
        let description: String
        let additionalDescription: String?
        let order: Int?
        let hasExpiryDate: Bool
        let isShownOnList: Bool?
        let category: String?
        let subHeader: String?

        enum CodingKeys: String, CodingKey {
            case key, description, additionalDescription, order, hasExpiryDate = "expiryDate", isShownOnList = "showOnList", category, subHeader = "subDescription"
        }
    }
}

// MARK: KYCData Media Type enum
extension KYCData {
    public enum MediaType: String {
        case photo = "PHOTO"
        case liveness = "LIVENESS"
    }
}

// MARK: KYCData Media Tip structure
extension KYCData {
    struct MediaTip: Codable, Equatable {
        let order: Int?
        let key: String
        let description: String
        let iconUrl: String?

        enum CodingKeys: String, CodingKey {
            case key, order, description, iconUrl
        }
    }
}

// MARK: KYCData Submit structure
extension KYCData {
    struct Submit: Codable {
        let info: String
        let header: String
        let highlightedPhrase: String?
    }
}

// MARK: KYCData Review structure 
extension KYCData {
    struct Review: Codable {
        let info: String
        let customerServiceUrlString: String

        enum CodingKeys: String, CodingKey {
            case info, customerServiceUrlString = "customerServiceUrl"
        }
    }
}

// MARK: KYCData Country structure
extension KYCData {
    struct Country: Codable {
        let id: Int
        let name: String
        let code: String

        enum CodingKeys: String, CodingKey {
            case id, name, code
        }
    }
}

// MARK: KYCData State structure
extension KYCData {
    struct State: Codable {
        let id: Int
        let name: String
        let code: String

        enum CodingKeys: String, CodingKey {
            case id, name, code
        }
    }
}

// MARK: KYCData City structure
extension KYCData {
    struct City: Codable {
        let id: Int
        let name: String
        let code: String

        enum CodingKeys: String, CodingKey {
            case id, name, code
        }
    }
}

// MARK: KYCData Barangay structure
extension KYCData {
    struct Barangay: Codable {
        let id: Int
        let name: String
        let code: String

        enum CodingKeys: String, CodingKey {
            case id, name, code
        }
    }
}

// MARK: KYCData Barangay structure
extension KYCData {
    struct ZipCode: Codable {
        let id: Int
        let code: String

        enum CodingKeys: String, CodingKey {
            case id, code
        }
    }
}

// MARK: KYCData Nationality structure
extension KYCData {
    struct Nationality: Codable {
        let id: Int
        let name: String
        let code: String

        enum CodingKeys: String, CodingKey {
            case id, name, code
        }
    }
}

// MARK: KYCData Work Nature structure
extension KYCData {
    struct WorkNature: Codable {
        let id: Int
        let name: String
        let requiresEmploymentDetails: Bool
        let additionalInfoRequired: Bool
        var description: String?
        let prohibitedIncomeSources: [IncomeSource]

        enum CodingKeys: String, CodingKey {
            case id, name, requiresEmploymentDetails, additionalInfoRequired, description, prohibitedIncomeSources
        }
    }
}

// MARK: KYCData Income Source structure
extension KYCData {
    struct IncomeSource: Codable, Equatable {
        let id: Int
        let name: String
        var description: String?

        enum CodingKeys: String, CodingKey {
            case id, name, description
        }
    }
}

// MARK: KYCData Validation Rules structure
extension KYCData {
    struct ValidationRule: Codable {
        let id: Int
        let regex: String?
        let additionalRule: String?
        let errorMessage: String
        let validationFields: [ValidationField]

        enum CodingKeys: String, CodingKey {
            case id, regex, additionalRule, errorMessage = "message", validationFields = "validation_fields"
        }
    }
}

// MARK: KYCData Validation Field structure
extension KYCData {
    struct ValidationField: Codable {
        let id: Int
        let name: String

        enum CodingKeys: String, CodingKey {
            case id, name
        }
    }
}

// MARK: KYCData Additional Documents structure
extension KYCData {
    struct AdditionalDocuments: Codable {
        let consentURLString: String

        var consentURL: URL? {
            URL(string: consentURLString)
        }

        enum CodingKeys: String, CodingKey {
            case consentURLString = "consentUrl"
        }
    }
}

// MARK: KYCData Name Rule structure
extension KYCData {
    struct NameRule: Codable {
        let id: Int
        let header: String
        let rules: String

        enum CodingKeys: String, CodingKey {
            case id, header, rules = "value"
        }
    }
}

// MARK: KYC Capture Guide Structure
extension KYCData {
    struct CaptureGuide: Codable, Equatable {
        static func == (lhs: KYCData.CaptureGuide, rhs: KYCData.CaptureGuide) -> Bool {
            return lhs.document.key == rhs.document.key
        }

        let id: Int
        let document: CaptureGuideDocument
        let pages: [Page]
        let captureMethods: [CaptureMethod]
        let samples: [Sample]?
        let variations: [Variation]?
        let isAllowedToUpload: Bool?
        let header: String?
        let subHeader: String?
        let idGuides: [IDGuide]?

        var hasZeroPassingThreshold: Bool {
            let zeroThresholdVariation = variations?.first(where: { $0.passedThreshold == 0 })
            return zeroThresholdVariation != nil
        }

        enum CodingKeys: String, CodingKey {
            case id, document, pages, samples, captureMethods = "capture_methods", variations, isAllowedToUpload = "is_allowed_to_upload", header, subHeader = "sub_header", idGuides = "id_guides"
        }

        public enum ScanType: String {
            case front = "FRONT"
            case back = "BACK"
            case tencentFace = "FACE"
            case tencentMeta = "META"
        }

        public enum MethodType: String {
            case simpleLiveness = "SIMPLE_ID_TENCENT_LIVENESS"
        }

        public enum Decision: String {
            case passed = "PASSED"
            case failed = "FAILED"
            case mismatch = "MISMATCH"
            case blurry = "BLURRY"
            case philsysMismatch = "PHILSYS_MISMATCH"
        }
    }

    struct IDGuide: Codable {
        let id: Int?
        let front: String?
        let back: String?

        enum CodingKeys: String, CodingKey {
            case id, front, back
        }
    }

    struct CaptureGuideDocument: Codable {
        let id: Int
        let key: String

        enum CodingKeys: String, CodingKey {
            case id, key
        }
    }

    struct Page: Codable {
        let id: Int
        let page: String

        enum CodingKeys: String, CodingKey {
            case id, page = "page"
        }

        var scanType: CaptureGuide.ScanType? {
            return CaptureGuide.ScanType(rawValue: page)
        }
    }

    struct CaptureMethod: Codable {
        let id: Int
        let method: String

        enum CodingKeys: String, CodingKey {
            case id, method = "method"
        }

        var methodType: CaptureGuide.MethodType? {
            return CaptureGuide.MethodType(rawValue: method)
        }
    }

    struct Sample: Codable {
        let id: Int?
        let imageUrl: String?
        let variant: String?
        let description: String?
        let images: [SampleImage]?
        let additionalDescription: String?

        enum CodingKeys: String, CodingKey {
            case id, imageUrl = "image_url", variant, description, images, additionalDescription
        }
    }

    struct SampleImage: Codable, Equatable {
        let id: Int?
        let instruction: String?
        let url: String?

        enum CodingKeys: String, CodingKey {
            case id, instruction, url
        }
    }

    struct Variation: Codable {
        let id: Int
        let key: String
        let confidenceThresholds: [Threshold]?

        enum CodingKeys: String, CodingKey {
            case id, key, confidenceThresholds = "confidence_thresholds"
        }

        var passedThreshold: Float {
            return self.confidenceThresholds?.first(where: { $0.decisionType == KYCData.CaptureGuide.Decision.passed })?.threshold ?? 0
        }

        struct Threshold: Codable {
            let id: Int
            let threshold: Float
            let decision: String

            var decisionType: CaptureGuide.Decision? {
                return CaptureGuide.Decision(rawValue: decision)
            }
        }
    }
}

// MARK: KYC Upload Guide Structure
extension KYCData {
    struct UploadGuide: Codable {
        let id: Int?
        let header: String?
        let subHeader: String?
        let maxByteSize: Int?
        let maxByteSizeAllowed: Int?
        let osTypes: [UploadOSType]?

        enum CodingKeys: String, CodingKey {
            case id, header, subHeader = "sub_header", maxByteSize = "maximum_byte_size", maxByteSizeAllowed = "maximum_byte_size_allowed", osTypes = "os_types"
        }
    }

    struct UploadOSType: Codable {
        let id: Int?
        let type: String?
        let uploadDescription: String?
        let mimeTypes: [String]?
        let errorSpiels: [UploadErrorSpiel]?

        enum CodingKeys: String, CodingKey {
            case id, type, uploadDescription = "upload_description", mimeTypes = "mime_types", errorSpiels = "error_spiels"
        }
    }

    struct UploadErrorSpiel: Codable {
        let id: Int?
        let code: String?
        let message: String?

        enum CodingKeys: String, CodingKey {
            case id, code, message
        }
    }
}

// MARK: KYCData SelfieTips structure
extension KYCData {
    struct SelfieTips: Codable, Equatable {
        let iconUrl: String
        let header: String
        let description: String

        enum CodingKeys: String, CodingKey {
            case iconUrl, header, description
        }
    }
}

// MARK: - Mocked data for testing purpose. Should be deleted after integrating with BE.
extension KYCDataWrapper {
  /// Mocked data that matched new CMS endpoint response
    static var mockedResponse: KYCDataWrapper? {
        guard let path = Bundle.main.path(forResource: "cms_response", ofType: "json") else {
            return nil
        }
        let url = URL(fileURLWithPath: path)
        do {
            let data = try Data(contentsOf: url)
            let response = try JSONDecoder().decode(KYCDataWrapper.self, from: data)
            return response
        } catch {
            return nil
        }
    }
}
