//
//  AdCarouselViewModel.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 05/05/2019.
//  Copyright © 2019 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import ConfigurationService
import Error
import Foundation
import Injector
import ReactiveSwift
import UIKit

struct AdBannerProperties {
    static let defaultSpacing: CGFloat = 8.0
    /// Padding for left and right edge
    static let defaultHorizontalPadding: CGFloat = 24.0
    static let defaultWidth: CGFloat = 327.0
    static let defaultHeight: CGFloat = 148.0
    static let dashboardHeight: CGFloat = 100

    /// The width of the AdBanner relative to the screen size
    static var dynamicWidth: CGFloat {
        return UIScreen.main.bounds.width - (defaultHorizontalPadding * 2)
    }

    /// The height of the AdBanner scaled proportionally with the dynamic width using the default aspect ratio
    static var dynamicHeight: CGFloat {
        let scale = dynamicWidth / defaultWidth
        return defaultHeight * scale
    }

    /// The computed height of the AdBanner relative to `dynamicWidth` scaled proportionally with a custom aspect ratio.
    static func dynamicHeight(desiredWidth: CGFloat = AdBannerProperties.defaultWidth, desiredHeight: CGFloat = AdBannerProperties.defaultHeight) -> CGFloat {
        return desiredHeight / desiredWidth * dynamicWidth
    }
}

struct AdBanner: Equatable {
    var imageURLString: String?
    var text: String?
    var message: String?
    var actionURLString: String?
    var tag: Int
    var screen: String?

    // Newly added properties. Used for displaying unlimited banners (Custom Key Value Pair inside the Clevertap Dashboard's Campaign)
    var name: String?
    var active: Bool?
    var priority: Int?
    var bannerKey: String?

    init(imageURLString: String? = nil, text: String? = nil, message: String? = nil, actionURLString: String? = nil, tag: Int, screen: String? = nil, priority: Int? = nil, name: String? = nil, active: Bool? = nil, bannerKey: String? = nil) {
        self.imageURLString = imageURLString
        self.text = text
        self.message = message
        self.actionURLString = actionURLString
        self.tag = tag
        self.screen = screen
        self.priority = priority
        self.name = name
        self.active = active
        self.bannerKey = bannerKey
    }
}

class AdCarouselViewModel: ViewModel, AdCarouselViewModelProtocol {
    @Inject private var adBannerService: AdBannerServiceProtocol
    @Inject private var configurationService: ConfigurationServiceV2

    var adBannersProperty = MutableProperty([AdBanner]())

    var rotationIntervalProperty = MutableProperty(Constants.CleverTapRotationIntervalDefault)

    private let disposeBag = CompositeDisposable()

    var isAutoscrollEnabled: Bool
    var adWidth: Double
    var adHeight: Double
    let module: AdModule

    var isCleverTapUnliBannersEnabled: Bool {
        configurationService.cleverTapUnliBannersEnabled.value
    }

    var additionalTapAnalyticsAttributes: [AnalyticsAttributeKey: Any]? {
        return additionalTap
    }

    private var additionalTap: [AnalyticsAttributeKey: Any]?

    public static let baseAdModuleCount = AdModule.allCases.count
    // hardcoded value for number of shop categories that can have banners

    // In order to:
    // 1.) Handle dynamic instances of Shop Categories
    // 2.) Work around the limitation that instanceCount cannot be dynamic
    /// A hardcoded max number of instance counts for Shop Categories is used,
    ///  increase/decrease when necessary
    public static let shopCategoriesInstanceCount = 8

    override class var instanceCount: Int {
        return AdCarouselViewModel.baseAdModuleCount + AdCarouselViewModel.shopCategoriesInstanceCount
    }

    var shouldShowNewDashboard: Bool {
        return module == .dashboard
    }

    init(isAutoscrollEnabled: Bool = true, adWidth: CGFloat = AdBannerProperties.defaultWidth, adHeight: CGFloat = AdBannerProperties.defaultHeight, module: AdModule = .dashboard, tapAnalyticsAttributes: [AnalyticsAttributeKey: Any]? = nil ) {
        self.isAutoscrollEnabled = isAutoscrollEnabled
        self.adHeight = Double(module.adBannerSize?.height ?? adHeight)
        self.adWidth = Double(module.adBannerSize?.width ?? adWidth)
        self.module = module
        self.additionalTap = tapAnalyticsAttributes
        super.init()

        adBannerService.adBannersProperty.signal
            .observeValues { [weak self] adBanners in
                self?.adBannersProperty.value = adBanners.filter { $0.screen == module.screen }
            }?.addToDisposeBag(disposeBag)

        rotationIntervalProperty <~ adBannerService.rotationIntervalProperty.signal.skipRepeats()
    }

    var numberOfItems: Int {
        return adBannersProperty.value.count
    }

    func initializeAdBannerService() {
        adBannerService.setDisplayUnitDelegate()
    }

    func sendAnalyticsForClickedEvent() {
        adBannerService.sendAnalyticsForClickedEvent()
    }
}
