import GoogleMobileAds
import Injector
import ReactiveSwift
import UIKit

/// Protocol for Google Banner Ads Carousel View Model
protocol GoogleBannerAdsCarouselViewModelProtocol: ViewModelProtocol {
    var adUnitIds: [String] { get }
    var adSize: AdSize { get }
    var autoScrollDelay: TimeInterval { get }
}

/// View model for Google Banner Ads Carousel
class GoogleBannerAdsCarouselViewModel: ViewModel, GoogleBannerAdsCarouselViewModelProtocol {
    // MARK: - Properties
    let adUnitIds: [String]
    let adSize: AdSize
    let autoScrollDelay: TimeInterval

    // MARK: - Initialization
    init(
        adUnitIds: [String],
        adSize: AdSize,
        autoScrollDelay: TimeInterval
    ) {
        self.adUnitIds = adUnitIds
        self.adSize = adSize
        self.autoScrollDelay = autoScrollDelay

        super.init()
    }
}

// MARK: - Factory Methods
extension GoogleBannerAdsCarouselViewModel {
    /// Create a view model configured for dashboard usage
    static func forDashboard() -> GoogleBannerAdsCarouselViewModel {
        return GoogleBannerAdsCarouselViewModel(
            adUnitIds: [
                "/23303781892/App_Display_Home_Homepage_iOS_320x100",
                "/23303781892/App_Display_Home_Homepage_2_iOS_320x100",
                "/23303781892/App_Display_Home_Homepage_3_iOS_320x100"
            ],
            adSize: adSizeFor(cgSize: CGSize(width: 320, height: 100)),
            autoScrollDelay: 3.0
        )
    }

    /// Create a view model with custom configuration
    static func custom(
        adUnitIds: [String],
        adSize: AdSize,
        autoScrollDelay: TimeInterval
    ) -> GoogleBannerAdsCarouselViewModel {
        return GoogleBannerAdsCarouselViewModel(
            adUnitIds: adUnitIds,
            adSize: adSize,
            autoScrollDelay: autoScrollDelay
        )
    }
}
