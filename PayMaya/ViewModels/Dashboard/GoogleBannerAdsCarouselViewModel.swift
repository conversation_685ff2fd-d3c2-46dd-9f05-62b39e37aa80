//
//  GoogleBannerAdsCarouselViewModel.swift
//  PayMaya
//
//  Created by Augment Agent on 12/19/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import GoogleMobileAds
import ReactiveSwift
import UIKit
import Injector

/// Protocol for Google Banner Ads Carousel View Model
protocol GoogleBannerAdsCarouselViewModelProtocol: ViewModelProtocol {
    var adUnitIds: [String] { get }
    var adSize: GADAdSize { get }
    var autoScrollDelay: TimeInterval { get }
    var isEnabled: Bool { get }
    
    func refreshAds()
}

/// View model for Google Banner Ads Carousel
class GoogleBannerAdsCarouselViewModel: ViewModel, GoogleBannerAdsCarouselViewModelProtocol {
    
    // MARK: - Constants
    private enum Constants {
        static let defaultScrollDelay: TimeInterval = 5.0
        static let dashboardAdUnitIds = [
            "ca-app-pub-3940256099942544/6300978111", // Google test banner ad unit 1
            "ca-app-pub-3940256099942544/6300978111", // Google test banner ad unit 2
            "ca-app-pub-3940256099942544/6300978111"  // Google test banner ad unit 3
        ]
    }
    
    // MARK: - Properties
    @Inject private var googleInterstitialAdsHelper: GoogleInterstitialAdsHelper
    
    let adUnitIds: [String]
    let adSize: GADAdSize
    let autoScrollDelay: TimeInterval
    let isEnabled: Bool
    
    // MARK: - Initialization
    init(
        adUnitIds: [String] = Constants.dashboardAdUnitIds,
        adSize: GADAdSize = GADAdSizeBanner,
        autoScrollDelay: TimeInterval = Constants.defaultScrollDelay,
        isEnabled: Bool = true
    ) {
        self.adUnitIds = adUnitIds
        self.adSize = adSize
        self.autoScrollDelay = autoScrollDelay
        self.isEnabled = isEnabled
        
        super.init()
    }
    
    // MARK: - Public Methods
    func refreshAds() {
        // This method can be called to trigger ad refresh
        // The actual refresh logic is handled by the view controller
    }
}

// MARK: - Factory Methods
extension GoogleBannerAdsCarouselViewModel {
    
    /// Create a view model configured for dashboard usage
    static func forDashboard() -> GoogleBannerAdsCarouselViewModel {
        return GoogleBannerAdsCarouselViewModel(
            adUnitIds: Constants.dashboardAdUnitIds,
            adSize: GADAdSizeBanner,
            autoScrollDelay: 5.0,
            isEnabled: true
        )
    }
    
    /// Create a view model with custom configuration
    static func custom(
        adUnitIds: [String],
        adSize: GADAdSize = GADAdSizeBanner,
        autoScrollDelay: TimeInterval = Constants.defaultScrollDelay,
        isEnabled: Bool = true
    ) -> GoogleBannerAdsCarouselViewModel {
        return GoogleBannerAdsCarouselViewModel(
            adUnitIds: adUnitIds,
            adSize: adSize,
            autoScrollDelay: autoScrollDelay,
            isEnabled: isEnabled
        )
    }
}
