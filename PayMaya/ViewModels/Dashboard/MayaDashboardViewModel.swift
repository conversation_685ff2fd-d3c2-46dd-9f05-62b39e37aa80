//
//  MayaDashboardViewModel.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/13/18.
//  Copyright © 2018 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import AssetProvider
import ConfigurationService
import Error
import Injector
import MayaCoreData
import MayaFormatterManager
import ReactiveSwift
import StoreProvider
import UIKit

private let errorTitle = CommonStrings.Error.Default.title

enum DashboardError: Swift.Error, Equatable {
    case activityError
    case balanceErrorWithCache
    case balanceErrorWithoutCache
    case balanceAndActivityErrorWithCache
    case noInternetConnection
}

enum LoginError: Equatable {
    case generic
    case loginMaxAttempts
}

enum APICallStatus: Equatable {
    case success
    case failure(Error)

    static func == (lhs: APICallStatus, rhs: APICallStatus) -> Bool {
        switch (lhs, rhs) {
        case (.success, .success): return true
        case (.failure(let errorA), .failure(let errorB)): return errorA == errorB
        default:
            return false
        }
    }
}

enum CreditBannerState {
    case apply
    case availableCredit
    case hidden
}

class MayaDashboardViewModel: MayaEKYCBaseViewModel, MayaDashboardViewModelProtocol {
    @Inject var appThemeService: AppThemeService
    @Inject private var apiProvider: APIProvider
    @Inject private var storeProvider: StoreProvider
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2
    @Inject private var notificationService: NotificationService
    @Inject private var contactsImporter: ContactsImporter
    @Inject private var formatter: MayaFormatterManager

    var importContactsAction: Action<Void, Void, Error>!
    var refreshDashboardAction: Action<Void, Void, DashboardError>!
    var userProfileAction: Action<Void, Void, Error>!
    var getActionCardsAction: Action<Void, Void, PayMayaError>!
    var getMayaCreditCustomerAction: Action<Void, Void, PayMayaError>!
    var getMayaCreditLoanApplicationAction: Action<Void, Void, PayMayaError>!
    var getMayaCreditLoanAccountAction: Action<String, Void, PayMayaError>!
    var getMayaCreditAccountStatusAction: Action<String, Void, PayMayaError>!

    var walletRewardsReorderTreatment: String {
        return configurationServiceV2.walletRewardsReorderExperimentation.value
    }

    let balanceProperty: MutableProperty<Balance?> = .init(nil)
    let creditBannerState: MutableProperty<CreditBannerState?> = .init(.hidden)
    let creditAccount: MutableProperty<MayaCreditAccount?> = .init(nil)
    let creditAccountAvailableCredit: MutableProperty<Float?> = .init(nil)
    var creditAccountCanTransfer: MutableProperty<Bool?> = .init(false)
    var creditLoanApplication: MutableProperty<MayaCreditCurrentLoanApplication?> = .init(nil)
    let showReKyc: MutableProperty<Bool?> = .init(false)
    let showExpandedReKyc: MutableProperty<Bool?> = .init(false)
    private let actionCardsProperty: MutableProperty<[ActionCard]> = .init([])

    private let disposeBag = CompositeDisposable()

    var isLoggedIn: Bool {
        guard let databaseStore = storeProvider.target(DatabaseStore.self),
            (databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User) != nil
        else {
            return false
        }

        return true
    }

    var helpUrl: URL? {
        return Constants.WebView.mayaHelpWithProfilePassingParamsURL()
    }

    var isActionCardsEnabled: Bool { configurationServiceV2.actionCardsEnabled.value }
    var isEasyCreditBannerEnabled: Bool { configurationServiceV2.easyCreditBannerEnabled.value }
    var isEasyCreditBannerV2Enabled: Bool { configurationServiceV2.mecBannerV2Enabled.value }
    var isMecAPIGatewayEnabled: Bool { configurationServiceV2.mecAPIGatewayEnabled.value }

    override init() {
        super.init()
        importContactsAction = Action { [weak self] in
            guard let self = self else { return SignalProducer<Void, Error>(error: Error()) }
            return SignalProducer { observer, _ in
                self.contactsImporter.importDeviceContacts(completionBlock: { _ in
                    observer.send(value: ())
                    observer.sendCompleted()
                })
            }
        }

        let contactsChange = NotificationCenter.default.reactive.notifications(forName: .CNContactStoreDidChange)
        contactsChange.observeValues { [weak self] _ in
            self?.importContactsAction.apply().start()
        }?.addToDisposeBag(disposeBag)

        let getBalance: () -> Balance? = { [weak self] in
            guard let databaseStore = self?.storeProvider.target(DatabaseStore.self),
                  let accountBalance = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.balance)).value as? Balance
            else {
                return nil
            }

            return accountBalance
        }
        balanceProperty.value = getBalance()

        let getActionCards: () -> [ActionCard] = { [weak self] in
            guard let self, let databaseStore = storeProvider.target(DatabaseStore.self) else {
                return []
            }

            let sortDescriptor = NSSortDescriptor(keyPath: \ActionCard.priority, ascending: true)
            let result = databaseStore.fetch(.init(DatabaseEntity.actionCard, sort: [sortDescriptor]))

            guard let actionCards = result.value as? [ActionCard] else {
                return []
            }
            return actionCards
        }

        if isActionCardsEnabled {
            actionCardsProperty.value = getActionCards()
        }

        getActionCardsAction = Action { [weak self] in
            guard let self else { return PayMayaError.validationError() }

            let userDefaultsStore = storeProvider.target(UserDefaultsStore.self)
            let lastUpdated = userDefaultsStore?.readRaw(UserDefaultsStoreId.actionCardsLastUpdatedOn) as? Date

            let recommendationsMetadataProducer = apiProvider.reactive
                .request(
                    API.ActionCards.recommendationMetadata,
                    lastUpdated: lastUpdated,
                    dateFormatter: DateFormatters.getDateFormatter(for: .lastModifiedHeader))
                .map { _ in }
                .mapMayaError()

            let recommendationsProducer = apiProvider.reactive
                .request(API.ActionCards.recommendations(size: 5))
                .on(event: { event in
                    switch event {
                    case .failed(let error):
                        /// If ever the request had succeeded but persistency failed, still display the action cards fetched
                        guard case .database = error else { break }
                        fallthrough
                    case .value:
                        self.actionCardsProperty.value = getActionCards()
                    case .completed:
                        userDefaultsStore?.write(Date(), options: UserDefaultsStoreId.actionCardsLastUpdatedOn.writingOptions)
                    default: break
                    }
                })
                .map { _ in }
                .mapMayaError()

            return recommendationsMetadataProducer.concat(recommendationsProducer)
        }

        getMayaCreditLoanAccountAction = Action<String, Void, PayMayaError> { [weak self] loanAccountId in
            guard let self else { return PayMayaError.validationError() }

            let apiTargetType: APITargetType = isMecAPIGatewayEnabled ?
                API.MayaCredit.getLoanAccountV2(loanAccountId) :
                API.MayaCredit.getLoanAccount(loanAccountId)

            return self.apiProvider.reactive.request(apiTargetType, type: MayaCreditAccount.self, decoder: MayaCreditAccount.decoder)
                .mapMayaError()
                .on(value: { creditAccount in
                    if creditAccount.status == LoanAccountStatus.active.rawValue {
                        self.getMayaCreditCustomerAction.apply().start()
                        self.creditBannerState.value = .availableCredit
                        self.creditAccount.value = creditAccount
                    } else {
                        self.creditBannerState.value = .hidden
                    }
                })
                .map { _ in }
        }

        getMayaCreditCustomerAction = Action<Void, Void, PayMayaError> { [weak self] in
            guard let self else { return PayMayaError.validationError() }
            return self.apiProvider.reactive.request(API.MayaCredit.getPersonDetails, type: MayaCreditPersonDetails.self)
                .mapMayaError()
                .on(value: { customerAccount in
                    self.creditAccountCanTransfer.value = customerAccount.mothersMaidenName != nil && customerAccount.contactReference != nil
                })
                .map { _ in }
        }

        getMayaCreditLoanApplicationAction = Action<Void, Void, PayMayaError> { [weak self] _ in
            guard let self else { return PayMayaError.validationError() }
            return self.apiProvider.reactive.request(API.MayaCredit.getLoanApplication, type: MayaCreditCurrentLoanApplication.self)
                .mapMayaError()
                .on(failed: { error in
                    if error.type == .contentNotFound {
                        self.creditBannerState.value = .apply
                    } else {
                        self.creditBannerState.value = .hidden
                    }
                }, value: { currentLoanApplication in
                    self.creditLoanApplication.value = currentLoanApplication
                    if currentLoanApplication.status != CreditApplicationStatus.booked.rawValue {
                        self.creditBannerState.value = .apply
                    } else if currentLoanApplication.status == CreditApplicationStatus.booked.rawValue, let loanAccountId = currentLoanApplication.loanAccountId {
                        self.getMayaCreditLoanAccountAction.apply(loanAccountId).start()
                    } else {
                        self.creditBannerState.value = .hidden
                    }
                }
                ).map { _ in }
        }

        getMayaCreditAccountStatusAction = Action<String, Void, PayMayaError> { [weak self] rrn in
            guard let self else { return PayMayaError.validationError() }
            return apiProvider.reactive.request(API.MayaCredit.getWalletBanner(rrn), type: MayaCreditAccountStatus.self, decoder: MayaCreditAccountStatus.decoder)
                .mapMayaError()
                .on(failed: { _ in
                    self.creditBannerState.value = .hidden
                }, value: { creditAccountStatus in
                    self.creditLoanApplication.value = creditAccountStatus.applicationDetails
                    self.creditAccount.value = creditAccountStatus.accountDetails
                    self.creditAccountAvailableCredit.value = creditAccountStatus.details?.availableCredit

                    switch creditAccountStatus.status {
                    case .loanActive, .loanActiveMissingInfo:
                        let isLoanActive = creditAccountStatus.status == .loanActive

                        self.creditAccountCanTransfer.value = isLoanActive
                        self.creditBannerState.value = .availableCredit
                    case .loanMissing:
                        self.creditBannerState.value = .apply
                    case .loanInvalid:
                        self.creditBannerState.value = .hidden
                    }
                }
                ).map { _ in }
        }

        refreshDashboardAction = Action { [weak self] in
            guard let self else {
                return SignalProducer<Void, DashboardError>(error: .noInternetConnection)
            }

            let balanceProducer = self.apiProvider.reactive.request(API.Balance.current)
                .map({ _ in
                    self.balanceProperty.value = getBalance()
                    return APICallStatus.success
                }).flatMapError({ error in
                    return SignalProducer<APICallStatus, Error>(value: APICallStatus.failure(error))
                })

            let activitiesRequest = ActivitiesRequest(fromDate: nil, toDate: nil, limit: 25, page: 1, order: nil, sort: nil, mode: .latest)
            let activitiesProducer = self.apiProvider.reactive.request(API.Activity.activities(activitiesRequest))
                .map({ _ in
                    return APICallStatus.success
                }).flatMapError({ error in
                    return SignalProducer<APICallStatus, Error>(value: APICallStatus.failure(error))
                })

            return SignalProducer.zip(activitiesProducer, balanceProducer)
                .mapError({ _ in DashboardError.noInternetConnection })
                .flatMap(.merge) { activityStatus, balanceStatus -> SignalProducer<Void, DashboardError> in
                    let balanceValue = getBalance()

                    if case .failure = activityStatus, balanceStatus == .success {
                        return .init(error: .activityError)
                    } else if case .failure = balanceStatus, activityStatus == .success {
                        if balanceValue != nil {
                            return .init(error: .balanceErrorWithCache)
                        } else {
                            return .init(error: .balanceErrorWithoutCache)
                        }
                    } else if case .failure(let activityError) = activityStatus, case .failure(let balanceError) = balanceStatus {
                        if balanceValue != nil {
                            return .init(error: .balanceAndActivityErrorWithCache)
                        } else if case .backend(let apiError) = activityError, case .failed(let message) = apiError, message.error.code == APIConstants.HTTPStatusCode.internalServerError.rawValue {
                            return .init(error: .noInternetConnection)
                        } else if case .backend(let apiError) = balanceError, case .failed(let message) = apiError, message.error.code == APIConstants.HTTPStatusCode.internalServerError.rawValue {
                            return .init(error: .noInternetConnection)
                        }
                        return .init(error: .balanceErrorWithoutCache)
                    }

                    return .init(value: ())
                }
        }

        userProfileAction = Action { [weak self] _ in
            guard let self = self else { return SignalProducer<Void, Error>(error: Error()) }
            return self.apiProvider.reactive.request(API.Profile.fetch)
                .on(completed: {
                    if self.isExpandedKYCEnabled {
                        if self.getExpandedReKYCAction() != nil {
                            self.showExpandedReKyc.value = true
                        }
                    } else {
                        self.updateReKyc()
                    }
                }).map { _ in }
        }
    }

    func isReachable() -> Bool {
        return apiProvider.isReachable()
    }

    func hasNoActivities() -> Bool {
        guard let result = storeProvider.target(DatabaseStore.self)?.fetch(DatabaseStore.ReadingOptions(DatabaseEntity.activity(.any)), limit: 1),
              let activities = result.value as? [Activity]
        else {
            return true
        }

        return activities.count == 0
    }

    // MARK: Re-KYC related functions
    func shouldShowReKYCBottomSheet() -> Bool {
        guard let databaseStore = storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
               else { return false }
        if user.reKYCStatusValue == .tagged || user.reKYCStatusValue == .rejected {
            return true
        }

        return false
    }

    func generateAdCarouselViewModel() -> AdCarouselViewModel {
        let width = AdBannerProperties.dynamicWidth
        let height = AdBannerProperties.dynamicHeight(desiredHeight: AdBannerProperties.dashboardHeight)
        return AdCarouselViewModel(adWidth: width, adHeight: height)
    }

    func generateMayaActivitiesViewModel() -> MayaActivitiesViewModel {
        return MayaActivitiesViewModel()
    }

    func generateRewardsDataModel() -> MayaRewardsDataModel {
        return MayaRewardsDataModel()
    }

    func generateWalletBalanceViewModel() -> MayaWalletBalanceViewModel {
        return MayaWalletBalanceViewModel(balanceProperty: balanceProperty, actionCardsProperty: actionCardsProperty, getActionCardsAction: getActionCardsAction)
    }

    func generateCreditBalanceBannerViewModel() -> MayaCreditBalanceBannerViewModel {
        return MayaCreditBalanceBannerViewModel(creditAccount: creditAccount, canTransfer: creditAccountCanTransfer, loanApplication: creditLoanApplication, availableCredit: creditAccountAvailableCredit)
    }

    func generateCreditApplicationBannerViewModel() -> MayaCreditApplicationBannerViewModel {
        return MayaCreditApplicationBannerViewModel(loanApplication: creditLoanApplication)
    }

    func generateServicesViewModel() -> MayaWalletServicesViewModel {
        return MayaWalletServicesViewModel()
    }

    func isVariantActiveForWalletRewardsReorderExperiment() -> Bool {
        guard let experimentation = MayaToggle.walletRewardsReorder.experiment, let variantKey = experimentation.variantKeys.first else {
            return false
        }

        return walletRewardsReorderTreatment == variantKey
    }
}

private extension MayaDashboardViewModel {
    func updateReKyc() {
        guard let databaseStore = storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
              let userDefaultsStore = storeProvider.target(UserDefaultsStore.self)
        else { return }

        switch user.reKYCStatusValue {
        case .tagged, .rejected:
            guard let reKYCInitialDateTime = userDefaultsStore.readRaw(UserDefaultsStoreId.reKYCInitialDateTime) as? Date else {
                userDefaultsStore.write(Date(), options: UserDefaultsStoreId.reKYCInitialDateTime)
                self.showReKyc.value = true
                return
            }

            var difference = reKYCInitialDateTime.minuteDifference(from: Date())
            var durationForReKYC = Constants.ReKYC.reKYCIntroScreenDuration

        // TO DO: Remove this logic and change vars to let when move to production, reKYC10MinutesTest is for testing to reshow Rekyc-Intro screen in 10 minutes
            if configurationService.reKYC10MinutesTestEnabled.value {
                difference = reKYCInitialDateTime.minuteDifference(from: Date())
                durationForReKYC = Constants.ReKYC.reKYCIntroScreenDurationTest
            }

            let showReKYCIntro = difference >= durationForReKYC
            if showReKYCIntro {
                userDefaultsStore.write(Date(), options: UserDefaultsStoreId.reKYCInitialDateTime)
                self.showReKyc.value = true
            }

        default:
            userDefaultsStore.remove(UserDefaultsStoreId.reKYCInitialDateTime, completion: nil)
        }
    }
}
