//
//  MayaDashboardMenuPagerViewModel.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 2/10/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import ConfigurationService
import Error
import Injector
import MayaCoreData
import ReactiveSwift
import StoreProvider
import UIKit

class MayaDashboardMenuPagerViewModel: MayaEKYCBaseViewModel, MayaDashboardMenuPagerViewModelProtocol {
    @Inject private var storeProvider: StoreProvider
    @Inject private var inboxManager: InboxManagerProtocol
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2
    @Inject private var notificationService: NotificationService
    @Inject private var signoutService: SignoutService

    let hasUnreadMessages = MutableProperty(false)
    private let disposeBag = CompositeDisposable()

    var signoutAction: Action<Void, Void, Error>!

    var dashboardMenuArray: [MayaDashboardMenu] {
        let defaultTabs = MayaDashboardMenu.allCases.sorted().filter { $0.isEnabled }
        let tabs = configurationService.dashboardSettings?.tabs
                .compactMap({ MayaDashboardMenu(rawValue: $0.id) })
                .filter({ $0.isEnabled })

        guard let tabs, !tabs.isEmpty else {
            return defaultTabs
        }
        return tabs
    }

    var isMayaUsernameEnabled: Bool {
        return configurationService.mayaSetUsernameEnabled.value
    }

    var isRegistrationMGMEnabled: Bool {
        return configurationService.registrationMGMEnabled.value
    }

    var isMayaInboxFlutterEnabled: Bool {
        return configurationService.mayaInboxFlutterEnabled.value
    }

    var isSecurityCenterEnabled: Bool {
        return configurationServiceV2.securityCenterEnabled.value
    }

    var helpUrl: URL? {
        return Constants.WebView.mayaHelpWithProfilePassingParamsURL()
    }

    override init() {
        super.init()
        hasUnreadMessages.value = inboxManager.hasUnreadMessages.value
        inboxManager.hasUnreadMessages.signal.observe(on: UIScheduler()).observeValues { [weak self] hasUnread in
            guard let self = self else { return }
            self.hasUnreadMessages.value = hasUnread
        }?.addToDisposeBag(disposeBag)

        signoutAction = Action { [weak self] in
            guard let self = self else { return SignalProducer<Void, Error>(error: Error()) }
            // This is non-breaking regardless if success or not
            self.notificationService.deletePushTokenAction.apply().start()
            return self.signoutService.signout()
        }
    }

    func isAccountLimited() -> Bool {
        guard let databaseStore = storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
        else {
            return false
        }
        return user.accountStatusValue == .limited
    }

    func shouldShowReferralInputCode() -> Bool {
        if let userDefaults = storeProvider.target(UserDefaultsStore.self),
            let shouldShowInputCode = userDefaults.readRaw(UserDefaultsStoreId.shouldShowInputInviteCode) as? Bool {
            return shouldShowInputCode
        }
        return false
    }

    func shouldShowUpgradeAccount() -> Bool {
        if let userDefaults = storeProvider.target(UserDefaultsStore.self),
            let shouldShowUpgradeAccount = userDefaults.readRaw(UserDefaultsStoreId.shouldShowUpgradeAccountUponRegistration) as? Bool {
            return shouldShowUpgradeAccount
        }
        return false
    }

    func wasDuplicateAccountAlertShownForUser() -> Bool {
        if let userDefaults = storeProvider.target(UserDefaultsStore.self),
            let wasShownForUser = userDefaults.readRaw(UserDefaultsStoreId.dedupAlertShownForUser) as? Bool {
            return wasShownForUser
        }
        return false
    }

    func setUserDefaultShouldShowReferral() {
        if let userDefaults = storeProvider.target(UserDefaultsStore.self) {
            userDefaults.write(false, options: UserDefaultsStoreId.shouldShowInputInviteCode)
        }
    }

    func setRestriction(restriction: String) {
        guard
            let userDefaults = storeProvider.target(UserDefaultsStore.self)
        else { return }

        userDefaults.write(restriction, options: UserDefaultsStoreId.restriction)
    }

    func setUserDefaultForDuplicateAccountAlertShown() {
        if let userDefaults = storeProvider.target(UserDefaultsStore.self) {
            userDefaults.write(true, options: UserDefaultsStoreId.dedupAlertShownForUser.writingOptions)
        }
    }

    func setUserDefaultShouldNotShowUpgradeAccount() {
        if let userDefaults = storeProvider.target(UserDefaultsStore.self) {
            userDefaults.write(false, options: UserDefaultsStoreId.shouldShowUpgradeAccountUponRegistration)
        }
    }

    func getProfileUsername() -> String? {
        guard
            let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
        else {
            return nil
        }
        return user.username
    }

    func getIndexForDashboardMenu(menu: MayaDashboardMenu) -> Int? {
        return dashboardMenuArray.firstIndex(of: menu)
    }

    func getUserRestrictionCode() -> String {
        if configurationService.customerRestrictionsEnabled.value {
            return getUserDefaultRestriction()
        } else {
            return getDatabaseRestriction()
        }
    }

    private func getDatabaseRestriction() -> String {
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let restriction = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.restriction)).value as? Restriction,
              let restrictionReason = restriction.reason
        else {
            return ""
        }

        return restrictionReason
    }

    private func getUserDefaultRestriction() -> String {
        guard
            let userDefaults = self.storeProvider.target(UserDefaultsStore.self),
            let restrictionReason = userDefaults.readRaw(UserDefaultsStoreId.restriction) as? String
        else {
            return ""
        }

        return restrictionReason
    }

    func isUserTaggedForRestrictedLogin() -> Bool {
        let restrictionCode = getUserRestrictionCode()
        return restrictionCode != "DUPLICATE" && !restrictionCode.isEmpty && configurationService.restrictedLoginFlowEnabled.value
    }

    func formatUnreadCount(count: Int) -> String {
        if count <= 0 {
           return ""
        }

        if count >= 100 {
           return "99+"
        }

        return "\(count)"
    }
}
