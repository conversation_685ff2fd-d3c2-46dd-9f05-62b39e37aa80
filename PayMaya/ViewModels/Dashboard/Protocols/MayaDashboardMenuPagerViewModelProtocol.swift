//
//  MayaDashboardMenuPagerViewModelProtocol.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 2/10/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import Error
import Foundation
import ReactiveSwift

protocol MayaDashboardMenuPagerViewModelProtocol: ViewModelProtocol {
    var isEddEnabled: Bool { get }
    var isMayaUsernameEnabled: Bool { get }
    var isRegistrationMGMEnabled: Bool { get }
    var isMayaInboxFlutterEnabled: Bool { get }
    var isSecurityCenterEnabled: Bool { get }
    var dashboardMenuArray: [MayaDashboardMenu] { get }
    var hasUnreadMessages: MutableProperty<Bool> { get }
    var signoutAction: Action<Void, Void, Error>! { get }
    var helpUrl: URL? { get }

    func isTaggedForEDD() -> Bool
    func isAccountLimited() -> Bool
    func shouldShowReferralInputCode() -> Bool
    func shouldShowUpgradeAccount() -> Bool
    func wasDuplicateAccountAlertShownForUser() -> Bool
    func setUserDefaultShouldShowReferral()
    func setRestriction(restriction: String)
    func setUserDefaultForDuplicateAccountAlertShown()
    func setUserDefaultShouldNotShowUpgradeAccount()
    func getProfileUsername() -> String?
    func getIndexForDashboardMenu(menu: MayaDashboardMenu) -> Int?
    func getUserRestrictionCode() -> String
    func isUserTaggedForRestrictedLogin() -> Bool
    func formatUnreadCount(count: Int) -> String
}
