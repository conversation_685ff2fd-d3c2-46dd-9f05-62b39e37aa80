//
//  MayaDashboardViewModelProtocol.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 14/09/2018.
//  Copyright © 2018 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import ConfigurationService
import Error
import Foundation
import MayaCoreData
import ReactiveSwift

protocol MayaDashboardViewModelProtocol: ViewModelProtocol, ThemedViewModelProtocol {
    var importContactsAction: Action<Void, Void, Error>! { get }
    var refreshDashboardAction: Action<Void, Void, DashboardError>! { get }
    var getActionCardsAction: Action<Void, Void, PayMayaError>! { get }
    var getMayaCreditCustomerAction: Action<Void, Void, PayMayaError>! { get }
    var getMayaCreditLoanApplicationAction: Action<Void, Void, PayMayaError>! { get }
    var getMayaCreditLoanAccountAction: Action<String, Void, PayMayaError>! { get }
    var getMayaCreditAccountStatusAction: Action<String, Void, PayMayaError>! { get }
    var userProfileAction: Action<Void, Void, Error>! { get }
    var getReKYCDataAction: Action<Void, ReKYCDataWrapper, Error>! { get }

    var balanceProperty: MutableProperty<Balance?> { get }
    var creditBannerState: MutableProperty<CreditBannerState?> { get }
    var creditAccount: MutableProperty<MayaCreditAccount?> { get }
    var creditAccountAvailableCredit: MutableProperty<Float?> { get }
    var creditAccountCanTransfer: MutableProperty<Bool?> { get }
    var creditLoanApplication: MutableProperty<MayaCreditCurrentLoanApplication?> { get }
    var isLoggedIn: Bool { get }
    var showReKyc: MutableProperty<Bool?> { get }
    var showExpandedReKyc: MutableProperty<Bool?> { get }

    var reKYCData: MutableProperty<ReKYCData?> { get }
    var helpUrl: URL? { get }

    var isExpandedKYCEnabled: Bool { get }
    var isActionCardsEnabled: Bool { get }
    var isEasyCreditBannerEnabled: Bool { get }
    var isEasyCreditBannerV2Enabled: Bool { get }
    var shouldShowEDD: Bool { get }

    var walletRewardsReorderTreatment: String { get }

    func hasNoActivities() -> Bool
    func isReachable() -> Bool
    func shouldShowReKYCBottomSheet() -> Bool
    func getExpandedReKYCAction() -> String?
    func removeReKYCNudge()

    func isVariantActiveForWalletRewardsReorderExperiment() -> Bool

    func generateAdCarouselViewModel() -> AdCarouselViewModel
    func generateMayaActivitiesViewModel() -> MayaActivitiesViewModel
    func generateRewardsDataModel() -> MayaRewardsDataModel
    func generateWalletBalanceViewModel() -> MayaWalletBalanceViewModel
    func generateCreditBalanceBannerViewModel() -> MayaCreditBalanceBannerViewModel
    func generateCreditApplicationBannerViewModel() -> MayaCreditApplicationBannerViewModel
    func generateServicesViewModel() -> MayaWalletServicesViewModel
}
