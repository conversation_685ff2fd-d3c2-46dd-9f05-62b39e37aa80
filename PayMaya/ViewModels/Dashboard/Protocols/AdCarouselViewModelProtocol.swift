//
//  AdCarouselViewModelProtocol.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 05/05/2019.
//  Copyright © 2019 PayMaya Philippines, Inc. All rights reserved.
//

import ConfigurationService
import Foundation
import Injector
import ReactiveSwift
import UIKit

/// Modules that uses the clevertap banner with specific UI construction each
enum AdModule {
    static var allCases: [Self] {
        return [.billsCategory, .cashIn, .creatorStore, .creatorStoreBannerV2, .creatorStoreInterstitial, .dashboard, .profileBanner, .receipt, .shop, .shopCategories(""), .shopReceipt]
    }

    case billsCategory
    case cashIn
    case creatorStore
    case creatorStoreBannerV2
    case creatorStoreInterstitial
    case dashboard
    case receipt
    case shop
    case shopCategories(String)
    case shopReceipt
    case servicesBanner
    case profileBanner

    /// Screen value from customExtras field of CleverTapDisplayUnit
    /// Usually the same with trigger event name of implemented module
    var screen: String? {
        switch self {
        case .billsCategory: "BILLS_PAGE_APPEAR"
        case .cashIn: "CASH_IN_APPEAR"
        case .creatorStore: "CREATOR_STORE_BANNER_APPEAR"
        case .creatorStoreBannerV2: "CREATOR_STORE_BANNER_V2_APPEAR"
        case .creatorStoreInterstitial: "CREATOR_STORE_INTERSTITIAL_APPEAR"
        case .dashboard: "WALLET_APPEAR"
        case .profileBanner: "MENU_BANNER_APPEAR"
        case .receipt: "RECEIPT_APPEAR"
        case .shop: "SHOP_HOME_APPEAR"
        case .shopCategories(let category): "SHOP_\(category.uppercased())_APPEAR"
        case .shopReceipt: "SHOP_RECEIPT_APPEAR"
        case .servicesBanner: "SERVICES_BANNER_APPEAR"
        }
    }

    var cellReuseIdentifier: String {
        switch self {
        case .creatorStore, .creatorStoreBannerV2: "MayaCreatorStoreAdBannerCollectionViewCell"
        default: "MayaAdBannerCollectionViewCell"
        }
    }

    var adBannerSize: CGSize? {
        switch self {
        case .creatorStore, .creatorStoreBannerV2:
           return CGSize(width: UIScreen.main.bounds.size.width - 55, height: 226)
        case .servicesBanner:
           return CGSize(width: UIScreen.main.bounds.size.width - 55, height: 120)
        default:
            if let bannerAspectRatio {
                let bannerWidth = UIScreen.main.bounds.size.width - (adBannerSpacing * 2)
                let bannerHeight = bannerWidth * bannerAspectRatio
                return CGSize(width: bannerWidth, height: bannerHeight)
            }
            return nil
        }
    }

    var hasPageIndicator: Bool {
        switch self {
        case .creatorStore: true
        default: false
        }
    }

    var adBannerSpacing: CGFloat {
        switch self {
        case .creatorStore, .creatorStoreBannerV2, .servicesBanner: 12
        case .profileBanner: 24
        default: AdBannerProperties.defaultSpacing
        }
    }

    var errorImage: UIImage? {
        switch self {
        case .servicesBanner, .profileBanner: Asset.MayaImages.CreatorStore.imageBannerPlaceholder.image
        default: nil
        }
    }

    var bannerAspectRatio: CGFloat? {
        switch self {
        case .profileBanner: 5.0 / 16.0
        default: nil
        }
    }
}

extension AdModule: Equatable {
    public static func == (lhs: AdModule, rhs: AdModule) -> Bool {
        switch (lhs, rhs) {
        case (.billsCategory, .billsCategory): true
        case (.cashIn, .cashIn): true
        case (.creatorStore, .creatorStore): true
        case (.creatorStoreBannerV2, .creatorStoreBannerV2): true
        case (.creatorStoreInterstitial, .creatorStoreInterstitial): true
        case (.dashboard, .dashboard): true
        case (.shop, .shop): true
        case (.shopCategories(let category1), .shopCategories(let category2)): category1 == category2
        case (.shopReceipt, .shopReceipt): true
        case (.servicesBanner, .servicesBanner): true
        default: false
        }
    }
}

protocol AdCarouselViewModelProtocol: ViewModelProtocol {
    var adBannersProperty: MutableProperty<[AdBanner]> { get }
    var rotationIntervalProperty: MutableProperty<TimeInterval> { get }
    var numberOfItems: Int { get }
    var isAutoscrollEnabled: Bool { get }
    var adWidth: Double { get }
    var adHeight: Double { get }
    var module: AdModule { get }
    var additionalTapAnalyticsAttributes: [AnalyticsAttributeKey: Any]? { get }

    var isCleverTapUnliBannersEnabled: Bool { get }

    var shouldShowNewDashboard: Bool { get }
    func initializeAdBannerService()
    func sendAnalyticsForClickedEvent()
}

extension AdCarouselViewModelProtocol {
    var additionalTapAnalyticsAttributes: [AnalyticsAttributeKey: Any]? {
        return nil
    }
}
