//
//  MayaBankTransferFormViewModel.swift
//  PayMaya
//
//  Created by <PERSON> on 5/31/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import ConfigurationService
import Foundation
import Injector
import MayaCoreData
import MayaFormatterManager
import ReactiveSwift
import StoreProvider

private let instapay = "INSTAPAY"
private let purposeTextFieldLimit = 35

enum TextFieldPropertyType {
    case accountName
    case purpose
}

class MayaBankTransferFormViewModel: ViewModel, MayaBankTransferFormViewModelProtocol, FormatterProtocol {
    @Inject private var apiProvider: APIProvider
    @Inject private var storeProvider: StoreProvider
    @Inject private var configurationService: ConfigurationService
    @Inject var formatter: MayaFormatterManager

    private let disposeBag = CompositeDisposable()

    private var availableBalanceProperty: MutableProperty<Decimal>!
    var availableBalanceText: String {
        let balanceString = formatter.number.currencyPesoStringValue(decimal: availableBalanceProperty.value)
        return L10n.Banktransfer.Form.Amount.info(balanceString)
    }

    let defaultAmountValue = L10n.Banktransfer.Form.Amount.default

    private var prefilledBank: Bank?
    private var additionalData: CreateV3BankTransferRequest.AdditionalData?
    private lazy var bankList: [Bank]? = { [weak self] in
        let sortDescriptor = NSSortDescriptor(key: "name", ascending: true)
        guard let self = self,
              let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let banks = databaseStore.fetch(DatabaseStore.ReadingOptions(DatabaseEntity.bank, sort: [sortDescriptor])).value as? [Bank]
        else {
            return nil
        }
        return banks
    }()

    var bankNameProperty = MutableProperty<String?>(nil)
    let favoriteIDProperty: MutableProperty<String?>

    var validatingProperty: Property<Bool>!
    var amountProperty: NewFieldProperty!
    var purposeProperty = NewFieldProperty(with: L10n.Banktransfer.Form.Purpose.required)
    var accountNumberProperty = NewFieldProperty(with: L10n.Banktransfer.Form.AccountNumber.required)
    var accountNameProperty = NewFieldProperty(with: L10n.Banktransfer.Form.AccountName.required)

    var balanceAction: Action<Void, Void, PayMayaError>!
    var createAction: Action<Void, MayaConfirmationViewItem, PayMayaError>!
    var favoriteDetailsAction: Action<Void, FavoriteItem, PayMayaError>!
    var bankTransferFee: Decimal? { configurationService.bankTransferFee }

    var bankTransferFeeText: String {
        let fee = bankTransferFee ?? Decimal.zero
        return fee.isZero ? L10n.Banktransfer.Form.Amount.fees : L10n.Banktransfer.Form.Amount.feeApply(formatter.number.currencyStringValue(decimal: fee))
    }

    var isAppEventV2BankTransferEnabled: Bool {
        return configurationService.appEventV2BankTransferEnabled.value
    }

    init(bankDetails: MayaBankDetails? = nil, favoriteItemID: String?) {
        favoriteIDProperty = MutableProperty(favoriteItemID)
        super.init()

        if let bankDetails = bankDetails {
            setBankWithIdentifier(bankDetails.identifier)
        }

        let getAvailableBalance: () -> Decimal = { [weak self] in
            guard let self = self,
                  let databaseStore = self.storeProvider.target(DatabaseStore.self),
                  let balance = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.balance)).value as? Balance,
                  let available = balance.available?.value
            else {
                return 0.0
            }

            return available.decimalValue
        }
        availableBalanceProperty = MutableProperty<Decimal>(getAvailableBalance())

        let inlineValidator: ((String) -> ValidatingProperty<String, FieldValidationError>.Decision)? = { [weak self] amountValue in
            guard let self = self else { return .valid }

            let amount = self.formatter.number.currencyDecimalValue(string: amountValue)
            let balance = self.availableBalanceProperty.value
            let transferFee = bankTransferFee ?? 0.0
            let transferFeeText = transferFee.isZero ? L10n.Banktransfer.Form.Amount.fees : L10n.Banktransfer.Form.Amount.feeApply(self.formatter.number.currencyStringValue(decimal: transferFee))
            let balanceText = self.formatter.number.currencyPesoStringValue(decimal: balance)

            if amount == 0 {
               return .invalid(FieldValidationError(message: L10n.Banktransfer.Form.Amount.required(balanceText)))
            } else if amount + transferFee > balance {
                return .invalid(FieldValidationError(message: "\(L10n.Banktransfer.Form.Amount.insufficient(balanceText))\n\(transferFeeText)"))
            } else {
                return .valid
            }
        }

        let validator: ((String) -> ValidatingProperty<String, FieldValidationError>.Decision) = { [weak self] amountValue in
            guard let self = self else { return .valid }

            let isValid = amountValue != ""
            let balance = self.availableBalanceProperty.value
            let balanceText = self.formatter.number.currencyPesoStringValue(decimal: balance)

            return isValid ? .valid : .invalid(FieldValidationError(message: L10n.Banktransfer.Form.Amount.required(balanceText)))
        }

        let accountNameInlineValidator: ((String, TextFieldPropertyType) -> ValidatingProperty<String, FieldValidationError>.Decision)? = { [weak self] value, _ in
            guard let self = self else { return .valid }

            if value.isEmpty {
                return .invalid(FieldValidationError(message: L10n.Banktransfer.Form.AccountName.required))
            }

            let regexTest = NSPredicate(format: "SELF MATCHES %@", QRPHValidation.specialCharacterRegex)

            if regexTest.evaluate(with: value) {
                return .valid
            } else {
                return .invalid(FieldValidationError(message: L10n.Banktransfer.Form.hasInvalidCharacters(L10n.Banktransfer.Form.accountName)))
            }
        }

        let purposeInlineValidator: ((String, TextFieldPropertyType) -> ValidatingProperty<String, FieldValidationError>.Decision)? = { [weak self] value, _ in
            guard let self = self else { return .valid }

            if value.isEmpty {
                return .invalid(FieldValidationError(message: L10n.Banktransfer.Form.Purpose.required))
            }

            let regexTest = NSPredicate(format: "SELF MATCHES %@", QRPHValidation.specialCharacterRegex)

            let isPurposeValueWithinLimit = value.count <= purposeTextFieldLimit

            if regexTest.evaluate(with: value) && isPurposeValueWithinLimit {
                return .valid
            } else {
                return .invalid(FieldValidationError(message: isPurposeValueWithinLimit ? L10n.Banktransfer.Form.hasInvalidCharacters(L10n.Banktransfer.Form.purpose) : L10n.Banktransfer.Form.Purpose.limit(purposeTextFieldLimit)))
            }
        }

        let accountNamePurposeValidator: ((String) -> ValidatingProperty<String, FieldValidationError>.Decision) = { [weak self] _ in
            return .valid
        }

        purposeProperty = NewFieldProperty(validator: accountNamePurposeValidator, inlineValidator: { value in
            guard let validator = purposeInlineValidator else { return .valid }
            return validator(value, .purpose)
        })

        accountNameProperty = NewFieldProperty(validator: accountNamePurposeValidator, inlineValidator: { value in
            guard let validator = accountNameInlineValidator else { return .valid }
            return validator(value, .accountName)
        })

        amountProperty = NewFieldProperty(validator: validator, inlineValidator: inlineValidator)

        validatingProperty = Property.combineLatest(amountProperty.isValidProperty, purposeProperty.isValidProperty, accountNumberProperty.isValidProperty, accountNameProperty.isValidProperty)
            .map { first, second, third, fourth -> Bool in
                return first && second && third && fourth
            }

        balanceAction = Action { [weak self] in
            guard let self = self else { return PayMayaError.validationError(title: defaultErrorTitle) }

            return self.apiProvider.reactive.request(API.Balance.current)
                .map { _ in }
                .mapMayaError()
        }

        let mapToConfirmationViewItem: (CreatedBankTransferV3) -> (MayaConfirmationViewItem) = { [weak self] transfer in
            let accountName = String.unwrappedValue(self?.accountNameProperty.value)
            let accountNumber = String.unwrappedValue(self?.accountNumberProperty.value)
            let bankName = String.unwrappedValue(self?.bankNameProperty.value)
            let amount = String.unwrappedValue(self?.amountProperty.value)
            let currency = Constants.Defaults.Common.currency.rawValue
            let purpose = String.unwrappedValue(self?.purposeProperty.value)
            let bankIC = String.unwrappedValue(self?.prefilledBank?.identifier)
            return MayaConfirmationViewItem(
                accountName: accountName,
                accountNumber: accountNumber,
                bankName: bankName,
                amount: amount,
                currency: currency,
                purpose: purpose,
                createdBankTransfer: transfer,
                bankIC: bankIC
            )
        }

        createAction = Action { [weak self] in
            guard let self = self,
                  let identifier = self.prefilledBank?.identifier
            else { return PayMayaError.validationError(title: defaultErrorTitle) }

            let currency = Constants.Defaults.Common.currency.rawValue
            let amount = AmountRequest(currency: currency, value: self.formatter.number.currencyDoubleValue(string: self.amountProperty.mutableProperty.value))
            let recipient = CreateV3BankTransferRequest.Recipient(type: instapay, account: self.accountNumberProperty.mutableProperty.value, accountName: self.accountNameProperty.mutableProperty.value, institutionCode: identifier)
            let request = CreateV3BankTransferRequest(recipient: recipient, amount: amount, purpose: self.purposeProperty.mutableProperty.value, additionalData: self.additionalData)

            return self.apiProvider.reactive.request(API.BankTransfer.createV3(request), type: CreatedBankTransferV3.self)
                .map(mapToConfirmationViewItem)
                .mapMayaError()
        }

        favoriteDetailsAction = Action { [weak self] in
            guard let self = self,
                  let favoriteID = self.favoriteIDProperty.value
            else { return PayMayaError.validationError(title: defaultErrorTitle) }

            return self.apiProvider.reactive.request(API.Favorites.getDetails(favoriteID), type: FavoriteItem.self)
                .mapMayaError()
        }

        balanceAction.completed
            .observeValues { [weak self] in
                guard let self = self else { return }
                self.availableBalanceProperty.value = getAvailableBalance()
            }?.addToDisposeBag(disposeBag)
    }

    func setAdditionalData(from bankTransferQR: BankTransferQR) {
        additionalData = CreateV3BankTransferRequest.AdditionalData(
            paymentType: bankTransferQR.paymentType,
            uniqueID: bankTransferQR.uniqueID,
            merchantMobileNumber: bankTransferQR.merchantMobileNumber,
            loyaltyNumber: nil,
            merchantCategoryCode: bankTransferQR.merchantCategoryCode,
            merchantCity: bankTransferQR.merchantCity,
            merchantPostalCode: bankTransferQR.merchantPostalCode,
            billNumber: bankTransferQR.billNumber,
            additionalMobileNumber: nil,
            storeLabel: bankTransferQR.storeLabel,
            referenceLabel: bankTransferQR.referenceLabel,
            customerLabel: nil,
            terminalLabel: bankTransferQR.terminalLabel,
            additionalRequestData: bankTransferQR.additionalRequestData
        )
    }

    func setBankWithCode(_ code: String) {
        guard let bank = bankList?.first(where: { $0.code == code }) else { return }
        prefilledBank = bank
        bankNameProperty.value = bank.name
    }

    func setBankWithIdentifier(_ bic: String) {
        guard let bank = bankList?.first(where: { $0.identifier == bic }) else { return }
        prefilledBank = bank
        bankNameProperty.value = bank.name
    }

    func isAccessoryLabelVisible(isError: Bool) -> Bool {
       let amount = self.formatter.number.currencyDecimalValue(string: self.amountProperty.mutableProperty.value ?? "")
       return !isError || amount.isZero
    }
}
