//
//  MayaRegistrationFormV2ViewModel.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 5/15/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import StoreProvider

final class MayaRegistrationFormV2ViewModel: MayaFieldValidatorViewModel, MayaRegistrationFormV2ViewModelProtocol {
    @Inject private var apiProvider: APIProvider
    @Inject private var storeProvider: StoreProvider
    @Inject var locationAuthorizerService: LocationAuthorizerServiceProtocol
    @Inject var locationCheckerService: LocationCheckerServiceProtocol

    var firstNameField: NewFieldProperty!
    var middleNameField: NewFieldProperty!
    var lastNameField: NewFieldProperty!
    var emailAddressField: NewFieldProperty!
    var mobileNumberField: NewFieldProperty!
    var passwordField: NewFieldProperty!

    var validateAndFetchConsentsAction: Action<Void, Void, PayMayaError>!
    var registerAction: Action<Void, Void, PayMayaError>!

    var hasNoLegalMiddleName: MutableProperty<Bool> = MutableProperty(false)
    let secondaryConsentsProperty = MutableProperty([SecondaryConsent]())
    let allConsentsAreEnabledProperty: MutableProperty<Bool> = MutableProperty(true)
    var shouldShowPolicyConsents: MutableProperty<Bool> = MutableProperty(false)

    override init() {
        super.init()

        setupFields()

        validateAndFetchConsentsAction = Action { [weak self] in
            guard let self else { return PayMayaError.validationError(title: defaultErrorTitle) }

            let validatePasswordRequest = ValidatePasswordRequest(password: passwordField.mutableProperty.value)

            let validatePasswordProducer = apiProvider.reactive
                .request(API.Registration.validatePassword(validatePasswordRequest))
                .map { _ in }
                .mapMayaError()

            let getPolicyConsentsProducer = apiProvider.reactive
                .request(API.DataPrivacy.getPolicyConsents)
                .map { _ in }
                .mapMayaError()

            return validatePasswordProducer.then(getPolicyConsentsProducer)
        }

        registerAction = Action { [weak self] _ in
            guard let self, let request = createRegistrationRequest() else {
                return PayMayaError.validationError(title: defaultErrorTitle)
            }

            let requestProducer = apiProvider.reactive.request(API.Registration.registerV2(request))
                .map { _ in }
            return requestProducer.mapMayaError()
        }

        allConsentsAreEnabledProperty <~ secondaryConsentsProperty.signal.skipRepeats().map { consents -> Bool in
            let enabledConsents = consents.filter { return $0.enabled ?? false }
            return !enabledConsents.isEmpty && consents.count == enabledConsents.count
        }
    }
}

// MARK: - Registration Form Methods
extension MayaRegistrationFormV2ViewModel {
    private func setupFields() {
        let firstNameFieldName = L10n.Shortened.Registration.Firstname.Field.name
        let middleNameFieldName = L10n.Shortened.Registration.Middlename.Field.name
        let lastNameFieldName = L10n.Shortened.Registration.Lastname.Field.name
        let passwordFieldName = L10n.Shortened.Registration.Password.Field.name
        let nameRegexErrorMessage = L10n.Registration.Name.Field.Error.Format.Regex.invalid
        let consecCharErrorMessage = L10n.Maya.UpdateProfile.Validation.Field.Repeated.characters

        let firstNameValidator = getIsRequiredValidator(
            validationErrorMessage: L10n.Maya.UpdateProfile.Validation.Field.isRequired(firstNameFieldName),
            wrappe: getRegexValidator(pattern: Constants.Registration.Field.Name.regEx,
                                      validationErrorMessage: nameRegexErrorMessage,
                                      wrappe: getConsecutiveCharactersValidator(validationErrorMessage: consecCharErrorMessage)))
        firstNameField = NewFieldProperty(validator: firstNameValidator, inlineValidator: firstNameValidator)

        let middleNameValidator = getIsRequiredValidator(
            minimumLength: Constants.Field.MiddleName.minimumChars,
            validationErrorMessage: L10n.Maya.UpdateProfile.Validation.Field.isRequired(middleNameFieldName),
            wrappe: getRegexValidator(pattern: Constants.Registration.Field.Name.regEx,
                                      validationErrorMessage: nameRegexErrorMessage,
                                      wrappe: getConsecutiveCharactersValidator(validationErrorMessage: consecCharErrorMessage)))

        middleNameField = NewFieldProperty(validator: middleNameValidator, inlineValidator: middleNameValidator)

        let lastNameValidator = getIsRequiredValidator(
            minimumLength: Constants.Field.LastName.minimumChars,
            validationErrorMessage: L10n.Maya.UpdateProfile.Validation.Field.isRequired(lastNameFieldName),
            wrappe: getRegexValidator(pattern: Constants.Registration.Field.Name.regEx,
                                      validationErrorMessage: nameRegexErrorMessage,
                                      wrappe: getConsecutiveCharactersValidator(validationErrorMessage: consecCharErrorMessage)))
        lastNameField = NewFieldProperty(validator: lastNameValidator, inlineValidator: lastNameValidator)

        let emailRegExValidator = getRegexValidator(pattern: Constants.Registration.Field.Email.regEx,
                                                    validationErrorMessage: L10n.Registration.Email.Field.Error.Format.Regex.invalid)
        emailAddressField = NewFieldProperty(validator: emailRegExValidator, inlineValidator: emailRegExValidator)

        let mobileNumberIsRequiredErrorMessage = L10n.Maya.UpdateProfile.Validation.Field.isRequired(L10n.Shortened.Registration.Mobilenumber.Field.name)
        let mobileNumberValidator = getIsRequiredValidator(validationErrorMessage: mobileNumberIsRequiredErrorMessage,
                                                           wrappe: getRegexValidator(pattern: Constants.MobileNumber.regEx,
                                                                                     validationErrorMessage: L10n.Registration.Second.Inlinevalidation.validnumber))

        mobileNumberField = NewFieldProperty(validator: mobileNumberValidator, inlineValidator: mobileNumberValidator)
        let passwordValidator = getIsRequiredValidator(validationErrorMessage: L10n.Maya.UpdateProfile.Validation.Field.isRequired(passwordFieldName))
        passwordField = NewFieldProperty(validator: passwordValidator, inlineValidator: passwordValidator)
    }
}

// MARK: - Account Provisioning Methods
extension MayaRegistrationFormV2ViewModel {
    func toggleEnableConsent(index: Int) -> Bool? {
        var tempSecondaryConsents = secondaryConsentsProperty.value
        guard var consent = tempSecondaryConsents[safeIndex: index] else { return nil }
        consent.enabled = !(consent.enabled ?? false)
        tempSecondaryConsents[index] = consent
        secondaryConsentsProperty.value = tempSecondaryConsents
        return consent.enabled
    }

    func enableAllConsents(_ enabled: Bool) {
        let updatedSecondaryConsents = secondaryConsentsProperty.value.map { SecondaryConsent(name: $0.name, identifier: $0.identifier, consentDescription: $0.consentDescription, enabled: enabled, expanded: $0.expanded) }
        secondaryConsentsProperty.value = updatedSecondaryConsents
    }

    func toggleShowConsentDescription(at index: Int) -> Bool? {
        var tempSecondaryConsents = secondaryConsentsProperty.value
        guard var consent = tempSecondaryConsents[safeIndex: index] else { return nil }
        consent.expanded = !(consent.expanded ?? false)
        tempSecondaryConsents[index] = consent
        secondaryConsentsProperty.value = tempSecondaryConsents
        return consent.expanded
    }

    func getConsentGroup() -> ConsentGroup {
        var secondaryConsentDictionary = ConsentGroup()
        secondaryConsentsProperty.value.forEach { consentViewModel in
            secondaryConsentDictionary[consentViewModel.identifier] = consentViewModel.accepted
        }
        return secondaryConsentDictionary
    }

    func setConsents() {
        guard
            let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
            let dpOptIns = encryptedStore.read(EncryptedDefaultsStoreId.secondaryConsents, type: DataPrivacyOptIns.self).value
        else { return }
        secondaryConsentsProperty.value = dpOptIns.consents
    }

    func resetConsents() {
        enableAllConsents(true)
        shouldShowPolicyConsents.value = false
    }

    private func createRegistrationRequest() -> RegistrationRequest? {
        var countriesDictionary: [String: Any]?
        if let path = Bundle.main.path(forResource: Constants.Keys.Resources.countries.rawValue, ofType: Constants.Keys.Resources.plist.rawValue) {
            countriesDictionary = NSDictionary(contentsOfFile: path) as? [String: Any]
        }

        var mobileNumberProperty: Property<String> {
            mobileNumberField.mutableProperty.map { mobileNumber -> String in
                guard mobileNumber != String.empty,
                    let countryDictionary = countriesDictionary,
                    let phCountry = countryDictionary[Constants.Keys.Common.defaultCountry.rawValue] as? [String: Any],
                    let prefix = phCountry[Constants.Keys.Resources.prefix.rawValue] as? String
                else {
                    return mobileNumber
                }
                return "\(prefix)\(mobileNumber)"
            }
        }

        let identityRequest = IdentityRequest(type: IdentityType.msisdn.rawValue, value: mobileNumberProperty.value)

        var backupIdentityRequest: IdentityRequest?
        if let email = emailAddressField.mutableProperty.value.nonEmptyValue {
            backupIdentityRequest = IdentityRequest(type: IdentityType.email.rawValue, value: email)
        }

        return RegistrationRequest(
            firstName: String.unwrappedValue(firstNameField.value),
            middleName: middleNameField.value,
            lastName: String.unwrappedValue(lastNameField.value),
            noMiddleNameFlag: hasNoLegalMiddleName.value,
            identity: identityRequest,
            password: String.unwrappedValue(passwordField.value),
            source: Constants.Defaults.Common.source.rawValue,
            birthDate: nil,
            consent: getConsentGroup(),
            backupIdentity: backupIdentityRequest
        )
    }
}
