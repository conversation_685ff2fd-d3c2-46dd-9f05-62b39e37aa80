//
//  MayaRegistrationFormV2ViewModelProtocol.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 5/15/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import ReactiveSwift

protocol MayaRegistrationFormV2ViewModelProtocol: LocationAuthorizerServiceConfigurator, LocationCheckerServiceConfigurator {
    var firstNameField: NewFieldProperty! { get }
    var middleNameField: NewFieldProperty! { get }
    var lastNameField: NewFieldProperty! { get }
    var emailAddressField: NewFieldProperty! { get }
    var mobileNumberField: NewFieldProperty! { get }
    var passwordField: NewFieldProperty! { get }
    var hasNoLegalMiddleName: MutableProperty<Bool> { get set }
    var validateAndFetchConsentsAction: Action<Void, Void, PayMayaError>! { get }
    var registerAction: Action<Void, Void, PayMayaError>! { get }

    var secondaryConsentsProperty: MutableProperty<[SecondaryConsent]> { get }
    var allConsentsAreEnabledProperty: MutableProperty<Bool> { get }

    func toggleEnableConsent(index: Int) -> Bool?
    func toggleShowConsentDescription(at index: Int) -> Bool?
    func enableAllConsents(_ enabled: Bool)
    func setConsents()
    func resetConsents()
    func getConsentGroup() -> ConsentGroup

    var shouldShowPolicyConsents: MutableProperty<Bool> { get }
}
