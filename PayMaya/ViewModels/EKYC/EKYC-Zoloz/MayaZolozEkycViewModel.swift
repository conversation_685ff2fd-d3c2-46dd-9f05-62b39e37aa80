//
//  MayaZolozEkycViewModel.swift
//  PayMaya
//
//  Created by <PERSON> on 11/08/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import ConfigurationService
import Error
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import StoreProvider

final class MayaZolozEkycViewModel: MayaEKYCBaseViewModel {
    let kycData: MutableProperty<KYCData?> = MutableProperty(nil)
    let preSubmissionId: MutableProperty<String?> = MutableProperty(nil)
    let metaInfo: MutableProperty<String?> = MutableProperty(nil)
    let selectedDocument: MutableProperty<MayaEKYCSelectedDocument?> = MutableProperty(nil)
    let selectedSecondaryDocument: MutableProperty<MayaEKYCSelectedDocument?> = MutableProperty(nil)
    let secondaryIDFlow: MutableProperty<Bool> = MutableProperty(false)
    let primaryCaptureMethod: MutableProperty<String?> = MutableProperty(nil)
    let secondaryCaptureMethod: MutableProperty<String?> = MutableProperty(nil)
    let documents = MutableProperty<[MayaZolozAdditionalDocumentType: UIImage]>([:])
    let uploadedDocuments = MutableProperty<[MayaZolozAdditionalDocumentType: EKYCZolozFileUploadResponse.FileData]>([:])
    let submissionData = MutableProperty<PostSubmissionRequest?>(nil)
    let submissionDataV2 = MutableProperty<PostSubmissionRequestV2?>(nil)
    let provinces: MutableProperty<[KYCAddress]?> = MutableProperty(nil)

    let regulatoryStatus = MutableProperty<MayaRegulatoryStatus?>(nil)
    let regulatoryCMS = MutableProperty<MayaRegulatoryCMSData?>(nil)
    let isFlaggedFatca: MutableProperty<Bool> = MutableProperty(false)
    let fatcaReasons: MutableProperty<[String]?> = MutableProperty(nil)
    let hasSubmittedAdditionalDocs: MutableProperty<Bool> = MutableProperty(false)
    let isMinor: MutableProperty<Bool> = MutableProperty(false)
    let frontIDKey: MutableProperty<String?> = MutableProperty(nil)
    let backIDKey: MutableProperty<String?> = MutableProperty(nil)
    let isFromReg: MutableProperty<Bool> = MutableProperty(false)

    var fatcaRequestData: ProfileData?
    var profileResponseData: ProfileData?

    private(set) var getKycDataAction: Action<Void, Void, PayMayaError>!

    @Inject private var apiProvider: APIProvider
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2
    @Inject private var storeProvider: StoreProvider

    lazy var patchUserFatca: Action<Void, ProfileData, PayMayaError> = Action { [weak self] _ in
        guard let self = self,
              let request = self.fatcaRequestData else { return PayMayaError.validationError() }
        return self.apiProvider.reactive.request(API.UpdateProfile.updateProfile(request: request), type: ProfileData.self).on(value: { data in
                self.profileResponseData = data
            })
            .showLoginScreenIfNeededByProfile()
            .mapMayaError()
    }

    private var user: User? {
        guard
            let databaseStore = storeProvider.target(DatabaseStore.self),
            let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
        else { return nil }
        return user
    }

    var welcomeButtonTreatment: String {
        return configurationServiceV2.welcomeButtonLabel.value
    }

    var changeWelcomeBackButtonEnabled: Bool {
        return configurationServiceV2.changeWelcomeBackButtonEnabled.value
    }

    var showWelcomeDoItLaterButtonEnabled: Bool {
        return configurationServiceV2.showWelcomeDoItLaterButtonEnabled.value
    }

    // Hardcoded platform caused by zoloz meta info returning platform in lowercase
    // which is not accepted by server
    private let devicePlatform = "iOS"

    override init() {
        super.init()
        getKycDataAction = Action { [unowned self] in
            apiProvider.reactive.request(getCMSRequestEndPoint(), type: KYCDataWrapper.self)
                .on(value: { [weak self] wrapper in
                    if let isMockCMSEnabled = self?.isKYCCMSv8MockEnabled,
                       isMockCMSEnabled,
                       let defaultKYCData = KYCDataWrapper.mockedResponse {
                        self?.kycData.value = defaultKYCData.data
                    } else {
                        self?.kycData.value = wrapper.data
                    }
                    // ---PreSubmission API Call
                }).flatMap(.latest) { [weak self] _ -> SignalProducer<PreSubmissionsResponse, Error> in
                    guard let self = self,
                          let request = self.makePreSubmissionsRequest()
                    else { return .never }
                    return self.apiProvider.reactive.request(API.ZolozKYC.preSubmissions(request), type: PreSubmissionsResponse.self)
                }
                .on(value: { [weak self] response in
                    self?.preSubmissionId.value = response.preSubmissionId
                    // ---Province API Call
                }).flatMap(.latest) { [weak self] _ -> SignalProducer<KYCAddressWrapper, Error> in
                    guard let self = self else { return .never }
                    if configurationService.shortenedFlowAddresssDropdownEnabled.value {
                        return self.apiProvider.reactive.request(API.ZolozKYC.getProvinces, type: KYCAddressWrapper.self)
                    } else {
                        return .empty
                    }
                }
                .on(value: { [weak self] response in
                    self?.provinces.value = response.data
                })
                .showLoginScreenIfNeededByZoloz()
                .flatMap(.latest) { _ in
                    SignalProducer<Void, Error>.empty
                }
                .mapMayaError()
        }
    }

    private func getCMSRequestEndPoint() -> APITargetType {
        if isShortenedFlowEnabled == true {
            if isPhilsysFlowEnabled {
                return API.ShortenedKYC.kycCmsDataV7
            } else {
                return API.ShortenedKYC.kycCmsDataV6
            }
        }
        return API.ZolozKYC.kycCmsData
    }

    private func makePreSubmissionsRequest() -> ZolozKYCPreSubmissionsRequest? {
        let appVersion = Bundle.main.object(forInfoDictionaryKey: Constants.Keys.BuildVersion.version.rawValue) as? String ?? Constants.Defaults.Common.version.rawValue
        let osVersion = String(format: "%@(iOS %@)", UIDevice.current.model, UIDevice.current.systemVersion)

        var systemInfo = utsname()
        uname(&systemInfo)
        let machineMirror = Mirror(reflecting: systemInfo.machine)
        let identifier = machineMirror.children.reduce("") { identifier, element in
            guard let value = element.value as? Int8, value != 0 else { return identifier }
            return identifier + String(UnicodeScalar(UInt8(value)))
        }
        let model = identifier.isEmpty ? Constants.Defaults.Common.model.rawValue : identifier

        let app = ZolozKYCPreSubmissionsRequest.App(version: appVersion)
        let device = ZolozKYCPreSubmissionsRequest.Device(platform: devicePlatform, version: osVersion, model: model)
        let metadata = ZolozKYCPreSubmissionsRequest.Metadata(app: app, device: device)
        return ZolozKYCPreSubmissionsRequest(metadata: metadata)
    }

    func resetSecondaryIDValues() {
        secondaryIDFlow.value = false
        selectedSecondaryDocument.value = nil
        secondaryCaptureMethod.value = nil
    }

    func callPatchFatca() {
        fatcaRequestData = ProfileData(fatcaDetails: ProfileData.FatcaDetails(fatcaValue: true, fatcaReason: fatcaReasons.value, updateRequestedBy: Constants.Profile.UpdateRequestedBy.kyc))
        patchUserFatca.apply().start()
    }
}
