//
//  MayaCreatorStoreViewModel.swift
//  PayMaya
//
//  Created by <PERSON> on 11/10/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import AssetProvider
import ConfigurationService
import Error
import Foundation
import Injector
import MayaCoreData
import MayaFormatterManager
import ReactiveSwift
import StoreProvider
import UIKit

class MayaCreatorStoreViewModel: ViewModel, MayaCreatorStoreViewModelProtocol {
    @Inject private var apiProvider: APIProvider
    @Inject private var storeProvider: StoreProvider
    @Inject private var configurationServiceV2: ConfigurationServiceV2

    let creatorStore: CreatorStore
    let onlinePaymentStatusProperty: MutableProperty<CreatorStoreStatus> = MutableProperty(.normal)
    let merchantsProperty = MutableProperty([Merchant]())
    let pageProperty: MutableProperty<Int> = MutableProperty(1)
    let shouldFetchNextPageProperty: MutableProperty<Bool> = MutableProperty(true)

    var initialMerchantsAction: Action<Void, Void, PayMayaError>!
    var pageMerchantsAction: Action<Void, Void, PayMayaError>!
    var getMerchantAction: Action<String, Void, PayMayaError>!

    var getMerchantActionError: PayMayaError?
    var getMerchantActionErrorModel: MayaCreatorStoreErrorModel? {
        if getMerchantActionError?.type == .luckyGamesUserGovernmentEmployee {
            let paragraph = NSMutableParagraphStyle()
            paragraph.alignment = .left
            paragraph.lineSpacing = 3

            let attributes: [NSAttributedString.Key: Any] = [
                .foregroundColor: CommonAsset.MayaColors.Content.contentGrey6.color,
                .font: CommonFontFamily.CerebriSansPro.book.font(size: 14)!,
                .paragraphStyle: paragraph
            ]

            let text = L10n.CreatorStore.Error.Restricted.message(creatorStore.title)
            let attributedString = NSMutableAttributedString(string: text, attributes: attributes)

            if let helpCenterURL = Constants.WebView.luckyGamesGovtRestriction.url,
               let helpCenterText = text.range(of: L10n.CreatorStore.Error.Restricted.helpCenter) {
                attributedString.addAttribute(.link, value: helpCenterURL, range: NSRange(helpCenterText, in: text))
            }

            return MayaCreatorStoreErrorModel(title: L10n.CreatorStore.Error.Restricted.title, message: attributedString)
        }

        return nil
    }

    var adCarouselViewModel: AdCarouselViewModel

    var title: String { creatorStore.title }
    var category: String { creatorStore.category }

    var isCreatorStoreBannerEnabled: Bool { configurationServiceV2.creatorStoreBannersEnabled.value }
    var isCleverTapUnlimitedBannerEnabled: Bool { configurationServiceV2.cleverTapUnliBannersEnabled.value  }

    init(creatorStore: CreatorStore) {
        self.creatorStore = creatorStore
        let width = AdBannerProperties.dynamicWidth
        let height = AdBannerProperties.dynamicHeight

        let module = ContainerWrapper.shared.resolve(ConfigurationServiceV2.self).cleverTapUnliBannersEnabled.value ? AdModule.creatorStoreBannerV2 : AdModule.creatorStore
        adCarouselViewModel = AdCarouselViewModel(adWidth: width, adHeight: height, module: module, tapAnalyticsAttributes: [.category: creatorStore.category])

        super.init()

        let fetchMerchants: () -> [Merchant] = { [weak self] in
            let predicate = NSPredicate(format: "category == %@", creatorStore.category)
            let prioritySortDescriptor = NSSortDescriptor(key: "priority", ascending: true)
            let nameSortDescriptor = NSSortDescriptor(key: "name", ascending: true)
            let sortDescriptor = [prioritySortDescriptor, nameSortDescriptor]

            guard let databaseStore = self?.storeProvider.target(DatabaseStore.self),
                let merchants = databaseStore.fetch(DatabaseStore.ReadingOptions(DatabaseEntity.merchant, predicate: predicate, sort: sortDescriptor)).value as? [Merchant] else {
                self?.onlinePaymentStatusProperty.value = .empty
                return [Merchant]()
            }

            self?.onlinePaymentStatusProperty.value = merchants.isEmpty ? .empty : .normal
            return merchants
        }

        initialMerchantsAction = Action { [weak self] in
            guard let self = self else { return SignalProducer(error: PayMayaError()) }

            let userDefaultsStore = self.storeProvider.target(UserDefaultsStore.self)
            let merchantsLastUpdate = userDefaultsStore?.readRaw(creatorStore.lastUpdatedKey) as? Date
            let merchantsMetadataSignalProducer =
                self.apiProvider.reactive.request(API.OnlinePayment.merchantsMetadata, lastUpdated: merchantsLastUpdate,
                                             dateFormatter: DateFormatters.getDateFormatter(for: .lastModifiedHeader),
                                             updateRequired: self.merchantsProperty.value.isEmpty)
                    .map { _ in }
                    .mapMayaError()

            let currentPage = 1
            let request = MerchantListRequest(page: currentPage, category: creatorStore.category)
            let merchantsSignalProducer = self.apiProvider.reactive.request(API.OnlinePayment.merchants(request))
                .map { _ in }
                .mapMayaError()
                .on(failed: { error in
                    self.getMerchantActionError = error
                }, completed: {
                    self.merchantsProperty.value = fetchMerchants()
                    if let userDefaultsStore = self.storeProvider.target(UserDefaultsStore.self) {
                        userDefaultsStore.write(Date(), options: creatorStore.lastUpdatedKey)
                    }
                })
            return merchantsMetadataSignalProducer.concat(merchantsSignalProducer)
        }

        pageMerchantsAction = Action { [weak self] in
            guard let self = self else { return SignalProducer(error: PayMayaError()) }

            let currentPage = self.pageProperty.value
            let request = MerchantListRequest(page: currentPage + 1, category: creatorStore.category)
            return self.apiProvider.reactive.request(API.OnlinePayment.merchants(request))
                .map { _ in }
                .mapMayaError()
                .on(failed: { error in
                    if case .contentNotFound = error.type {
                        self.shouldFetchNextPageProperty.value = false
                    }
                }, completed: {
                    self.merchantsProperty.value = fetchMerchants()
                    self.pageProperty.value = currentPage + 1
                    if let userDefaultsStore = self.storeProvider.target(UserDefaultsStore.self) {
                        userDefaultsStore.write(Date(), options: creatorStore.lastUpdatedKey)
                    }
                })
        }

        getMerchantAction = Action { [weak self] merchantId in
            guard let self = self else { return SignalProducer(error: PayMayaError()) }
            return self.apiProvider.reactive.request(API.OnlinePayment.merchant(id: merchantId))
                .map { _ in }
                .mapMayaError()
        }

        merchantsProperty.value = fetchMerchants()
    }

    func refreshMerchants() {
        pageProperty.value = 1
        shouldFetchNextPageProperty.value = true
        initialMerchantsAction.apply().start()
    }

    func fetchNextPage() {
        shouldFetchNextPageProperty.value = true
        pageMerchantsAction.apply().start()
    }

    func fetchMerchant(merchantId: String) -> Merchant? {
        let predicate = NSPredicate(format: "id == %@", merchantId)
        let options = DatabaseStore.ReadingOptions(DatabaseEntity.merchant, predicate: predicate)
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
            let merchant = databaseStore.read(options).value as? Merchant
            else { return nil }
        return merchant
    }
}
