//
//  MayaProfileViewModelProtocol.swift
//  PayMaya
//
//  Created by <PERSON> on 3/31/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import ConfigurationService
import Error
import Foundation
import Injector
import ReactiveSwift

protocol MayaProfileViewModelProtocol: ViewModelProtocol {
    var username: MutableProperty<String> { get }
    var name: MutableProperty<String> { get }
    var mobileNumber: MutableProperty<String> { get }
    var isAccountLimited: MutableProperty<Bool> { get }
    var profileMenuProperty: MutableProperty<[MayaProfileMenuItem]?> { get }
    var kycClientStatusProperty: MutableProperty<KYCClientStatus?> { get }
    var reKYCStatusProperty: MutableProperty<ReKYCStatus> { get }
    var canInviteFriendsProperty: MutableProperty<Bool> { get }
    var hasPendingApprovalRequests: MutableProperty<Bool> { get }

    var inviteCodeBannerSpiel: String { get }
    var helpUrl: URL? { get }
    var isEddEnabled: Bool { get }
    var isSetUsernameEnabled: Bool { get }
    var isProfileClevertapUnliBannersEnabled: Bool { get }
    var expandedReKYCAction: String? { get }
    var deviceID: String? { get }
    var walletID: String? { get }

    var adCarouselViewModel: AdCarouselViewModel { get }

    var userProfileAction: Action<Void, Void, Error>! { get }
    var customerAccountAction: Action<Void, Void, Error>! { get }
    var referralEligibilityAction: Action<Void, Void, Error>! { get }
    var signoutAction: Action<Void, Void, Error>! { get }
    var fetchApprovalRequestsAction: Action<Void, [PushApproval], PayMayaError> { get }
}

enum MayaProfileMenuItem: Equatable {
    case securityCenter
    case quickGuide
    case favorites
    case missions
    case vouchers
    case submitInviteCode
    case accountLimits
    case settings
    case getHelp
    case signout
    case auth
    case rateThisApp
    case inviteAFriend

    var viewModel: MayaProfileMenuDataModel? {
        switch self {
        case .securityCenter:
            return MayaProfileMenuDataModel(title: L10n.Profile.Menu.Title.securityCenter, iconImageAsset: Asset.MayaImages.Services.iconServicesActionSecurityCenter)
        case .quickGuide:
            return MayaProfileMenuDataModel(title: L10n.Profile.Menu.Title.mayaGuide, iconImageAsset: Asset.MayaImages.Profile.iconMayaQuickGuideMenu)
        case .favorites:
            return MayaProfileMenuDataModel(title: L10n.Profile.Menu.Title.mayaFavorites, iconImageAsset: Asset.MayaImages.Profile.iconMayaFavoritesMenu)
        case .missions:
            return MayaProfileMenuDataModel(title: L10n.Profile.Menu.Title.missions, iconImageAsset: Asset.MayaImages.Profile.iconMayaMissionsMenu)
        case .vouchers:
            return MayaProfileMenuDataModel(title: L10n.Profile.Menu.Title.vouchers, iconImageAsset: Asset.MayaImages.Profile.iconMayaVouchersMenu)
        case .submitInviteCode:
            return MayaProfileMenuDataModel(title: L10n.Profile.Menu.Title.submitInviteCode, iconImageAsset: Asset.MayaImages.Profile.iconMayaSubmitInviteCodeMenu)
        case .accountLimits:
            return MayaProfileMenuDataModel(title: L10n.Profile.Menu.Title.mayaAccountLimits, iconImageAsset: Asset.MayaImages.Profile.iconMayaAccountLimitsMenu)
        case .settings:
            return MayaProfileMenuDataModel(title: L10n.Profile.Menu.Title.settings, iconImageAsset: Asset.MayaImages.Profile.iconMayaSettingsMenu)
        case .getHelp:
            return MayaProfileMenuDataModel(title: L10n.Profile.Menu.Title.help, iconImageAsset: Asset.MayaImages.Profile.iconMayaHelpMenu)
        case .signout:
            return MayaProfileMenuDataModel(title: L10n.Profile.Menu.Title.logout, iconImageAsset: nil)
        case .auth:
            return MayaProfileMenuDataModel(title: L10n.Profile.Menu.Title.authorizeRequests, iconImageAsset: Asset.MayaImages.Profile.iconMayaAuthorizeRequests)
        case .rateThisApp:
            return MayaProfileMenuDataModel(title: L10n.Profile.Menu.Title.rateThisApp, iconImageAsset: Asset.MayaImages.Profile.iconRateThisApp)

        case .inviteAFriend:
            return MayaProfileMenuDataModel(title: L10n.Profile.Menu.Title.inviteAFriend, iconImageAsset: Asset.MayaImages.Profile.iconInviteAFriend)
        }
    }

    var analyticsModule: AnalyticsModule? {
        switch self {
        case .securityCenter:
            return Analytics.Menu.securityCenter
        case .quickGuide:
            return Analytics.Menu.quickGuide
        case .favorites:
            return Analytics.Menu.myFavorites
        case .missions:
            return Analytics.Menu.missions
        case .vouchers:
            return Analytics.Menu.vouchers
        case .submitInviteCode:
            return Analytics.Menu.submitInviteCode
        case .accountLimits:
            return Analytics.Menu.accountLimits
        case .settings:
            return Analytics.Menu.settings
        case .getHelp:
            return Analytics.Menu.getHelp
        case .signout:
            return Analytics.Menu.logout
        case .auth:
            return Analytics.Menu.auth
        case .rateThisApp:
            return Analytics.Menu.rateThisApp
        case .inviteAFriend:
            // TODO: Rubeus - App event not yet finalized
            return Analytics.Menu.none
        }
    }
}
