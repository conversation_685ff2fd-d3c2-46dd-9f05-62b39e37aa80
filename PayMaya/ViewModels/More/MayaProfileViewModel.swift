//
//  MayaProfileViewModel.swift
//  PayMaya
//
//  Created by <PERSON> on 3/31/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import ConfigurationService
import Error
import Injector
import MayaCoreData
import MayaFormatterManager
import ReactiveSwift
import StoreProvider
import UIKit

class MayaProfileViewModel: ViewModel, MayaProfileViewModelProtocol {
    @Inject private var apiProvider: APIProvider
    @Inject private var storeProvider: StoreProvider
    @Inject private var pushApprovalProvider: PushApprovalProvider
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2
    @Inject private var notificationService: NotificationService
    @Inject private var signoutService: SignoutService
    @Inject private var authenticationProvider: AuthenticationProviderManager

    var userProfileAction: Action<Void, Void, Error>!
    var customerAccountAction: Action<Void, Void, Error>!
    var referralEligibilityAction: Action<Void, Void, Error>!
    var signoutAction: Action<Void, Void, Error>!
    var fetchApprovalRequestsAction: Action<Void, [PushApproval], PayMayaError> {
        pushApprovalProvider.fetchPushApprovalAction
    }

    let profileMenuProperty: MutableProperty<[MayaProfileMenuItem]?> = MutableProperty(nil)
    let username: MutableProperty<String> = MutableProperty(String.empty)
    let name: MutableProperty<String> = MutableProperty(String.empty)
    let mobileNumber: MutableProperty<String> = MutableProperty(String.empty)
    let isAccountLimited: MutableProperty<Bool> = MutableProperty(false)
    let kycClientStatusProperty: MutableProperty<KYCClientStatus?> = MutableProperty(nil)
    let reKYCStatusProperty: MutableProperty<ReKYCStatus> = MutableProperty(ReKYCStatus.none)
    let canInviteFriendsProperty: MutableProperty<Bool> = MutableProperty(false)
    var hasPendingApprovalRequests: MutableProperty<Bool> {
        pushApprovalProvider.hasPendingApprovalRequests
    }

    var isEddEnabled: Bool {
        return configurationService.eddEnabled.value
    }

    var isSetUsernameEnabled: Bool {
        return configurationService.mayaSetUsernameEnabled.value
    }

    var isProfileClevertapUnliBannersEnabled: Bool {
        return configurationServiceV2.profileCleverTapUnliBannersEnabled.value
    }

    var inviteCodeBannerSpiel: String {
        guard let referral = configurationService.referral else { return String.empty }
        return referral.spielBannerInviteFriends
    }

    var helpUrl: URL? {
        return Constants.WebView.mayaHelpWithProfilePassingParamsURL()
    }

    var expandedReKYCAction: String? {
        if let userDefaults = storeProvider.target(UserDefaultsStore.self),
            let expandedReKYCAction = userDefaults.readRaw(UserDefaultsStoreId.expandedReKYCAction) as? String {
            return expandedReKYCAction
        }
        return nil
    }

    var canSubmitInviteCode: Bool {
        guard
            let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
            let eligibility = encryptedStore.read(EncryptedDefaultsStoreId.eligibility, type: Eligibility.self).value,
            let mgmConfig = configurationService.mgm
        else {
            return false
        }

        return mgmConfig.enabled && eligibility.canBeReferred
    }

    var deviceID: String? { UIDevice.current.identifierForVendor?.uuidString }
    var walletID: String?

    var adCarouselViewModel: AdCarouselViewModel = AdCarouselViewModel(module: .profileBanner)

    private let disposeBag = CompositeDisposable()

    override init() {
        super.init()
        let updateProfileDetails = { [weak self] in
            guard let self = self,
                let databaseStore = self.storeProvider.target(DatabaseStore.self),
                let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                let msisdn = user.msisdn
                else { return }
            self.mobileNumber.value = msisdn
            self.username.value = user.username ?? String.empty
            self.name.value = user.profile?.fullName ?? String.empty
            self.isAccountLimited.value = (user.accountStatusValue == .limited)
            self.kycClientStatusProperty.value = user.kycClientStatus
            self.reKYCStatusProperty.value = user.reKYCStatusValue
            self.walletID = user.walletId
        }

        updateProfileDetails()

        signoutAction = Action { [weak self] in
            guard let self = self else { return SignalProducer<Void, Error>(error: Error()) }
            // This is non-breaking regardless if success or not
            self.notificationService.deletePushTokenAction.apply().start()
            return self.signoutService.signout()
        }

        setupMenuItems()

        let updateReferralEligibility = { [weak self] in
            guard
                let self = self,
                let encryptedStore = self.storeProvider.target(EncryptedDefaultsStore.self),
                let mgmConfig = self.configurationService.mgm,
                let eligibility = encryptedStore.read(EncryptedDefaultsStoreId.eligibility, type: Eligibility.self).value
            else { return }
            self.canInviteFriendsProperty.value = mgmConfig.enabled && eligibility.canRefer && !eligibility.hasReachedQuota
        }

        let updateMenuValues = { [weak self] in
            guard let self else { return }
            updateSubmitInviteCodeMenuPosition()
        }

        referralEligibilityAction = Action { [weak self] _ in
            guard let self = self else { return SignalProducer<Void, Error>(error: Error()) }
            return self.apiProvider.reactive.request(API.Referral.eligibility)
                .on(completed: {
                    updateReferralEligibility()
                    updateMenuValues()
                }).map { _ in }
        }

        userProfileAction = Action { [weak self] _ in
            guard let self = self else { return SignalProducer<Void, Error>(error: Error()) }
            return self.apiProvider.reactive.request(API.Profile.fetch)
                .on(completed: {
                    updateProfileDetails()
                    updateMenuValues()
                }).map { _ in }
        }

        customerAccountAction = Action { [weak self] _ in
            guard let self = self else { return SignalProducer<Void, Error>(error: Error()) }
            return self.apiProvider.reactive.request(API.Profile.customerAccount)
                .on(completed: {
                    updateProfileDetails()
                }).map { _ in }
        }
    }
}

// MARK: - Helper Methods
extension MayaProfileViewModel {
    func setupMenuItems() {
        profileMenuProperty.value = [
            .quickGuide,
            .favorites,
            .missions,
            .vouchers,
            .accountLimits,
            .auth,
            .settings,
            .getHelp,
            .rateThisApp,
            .signout
        ]

        if configurationServiceV2.securityCenterEnabled.value {
            insertMenuItem(.securityCenter, at: 0)
        }

        if configurationServiceV2.mgmV2Enabled.value {
            insertMenuItem(.inviteAFriend, at: 0)
        }

        if !configurationService.pushApprovalEnabled.value {
            removeMenuItem(.auth)
        }

        if !configurationServiceV2.rateThisAppButtonEnabled.value {
            removeMenuItem(.rateThisApp)
        }

        updateSubmitInviteCodeMenuPosition()
    }

    func updateSubmitInviteCodeMenuPosition() {
        removeMenuItem(.submitInviteCode)
        guard canSubmitInviteCode else { return }

        if configurationServiceV2.mgmV2Enabled.value {
            insertMenuItem(.submitInviteCode, at: 0)
            return
        }

        if let position = profileMenuProperty.value?.firstIndex(of: .accountLimits) {
            insertMenuItem(.submitInviteCode, at: position)
        }
    }

    func insertMenuItem(_ item: MayaProfileMenuItem, at index: Int) {
        profileMenuProperty.value?.insert(item, at: index)
    }

    func removeMenuItem(_ item: MayaProfileMenuItem) {
        guard
            let indexToRemove = profileMenuProperty.value?.firstIndex(of: item)
        else {
            return
        }
        profileMenuProperty.value?.remove(at: indexToRemove)
    }
}
