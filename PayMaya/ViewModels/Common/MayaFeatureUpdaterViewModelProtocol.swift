//
//  MayaFeatureUpdaterViewModelProtocol.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 7/4/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//
import AssetProvider
import UIKit

protocol MayaFeatureUpdaterViewModelProtocol: ViewModelProtocol, AnyObject {
    var configuration: FeatureUpdaterContentConfiguration { get }
    var style: FeatureUpdaterStyle { get }
    var appStoreLink: URL? { get }
}

struct FeatureUpdaterContentConfiguration: Equatable {
    let leftBarButtonImage: UIImage?
    let navigationBarTitle: String
    let centerImage: UIImage
    let buttonTitle: String
    let subtitle: String
    let title: String

    init(
        leftBarButtonImage: UIImage? = nil,
        navigationBarTitle: String? = nil,
        centerImage: UIImage? = nil,
        buttonTitle: String? = nil,
        subtitle: String? = nil,
        title: String? = nil
    ) {
        self.leftBarButtonImage = leftBarButtonImage ?? CommonAsset.Images.Icons.iconBack.image
        self.centerImage = centerImage ?? CommonAsset.Images.Alert.imageMaintenance.image
        self.buttonTitle = buttonTitle ?? L10n.Maya.MgmV2.Button.updateNow
        self.subtitle = subtitle ?? L10n.Maya.MgmV2.Spiel.forceUpdate
        self.title = title ?? L10n.Maya.MgmV2.Title.updateRequired
        self.navigationBarTitle = navigationBarTitle ?? ""
    }
}

struct FeatureUpdaterStyle: Equatable {
    let leftBarButtonTintColor: UIColor?
    let navigationBarColor: UIColor?
    let backgroundColor: UIColor?
    let titleStyle: LabelStyle?
    let subtitleStyle: LabelStyle?

    init(
        leftBarButtonTintColor: UIColor? = nil,
        navigationBarColor: UIColor? = nil,
        backgroundColor: UIColor? = nil,
        titleStyle: LabelStyle? = nil,
        subtitleStyle: LabelStyle? = nil
    ) {
        self.leftBarButtonTintColor = leftBarButtonTintColor
        self.navigationBarColor = navigationBarColor
        self.backgroundColor = backgroundColor
        self.subtitleStyle = subtitleStyle
        self.titleStyle = titleStyle
    }
}

struct LabelStyle: Equatable {
    let font: UIFont?
    let textColor: UIColor?

    init(
        font: UIFont? = nil,
        textColor: UIColor? = nil
    ) {
        self.font = font
        self.textColor = textColor
    }
}
