//
//  MayaEKYCBaseViewModel.swift
//  PayMaya
//
//  Created by <PERSON> on 6/6/23.
//  Copyright © 2023 PayMaya Philippines, Inc. All rights reserved.
//

import ConfigurationService
import Error
import Foundation
import Injector
import MayaCoreData
import Moya
import ReactiveSwift
import StoreProvider

class MayaEKYCBaseViewModel: ViewModel {
    @Inject private var storeProvider: StoreProvider
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2

    let reKYCData: MutableProperty<ReKYCData?> = MutableProperty(nil)
    var getReKYCDataAction: Action<Void, ReKYCDataWrapper, Error>!

    var kycActionValue: String {
        guard let user = user else { return "" }
        switch user.kycLevelValue {
        case .zero:
            return AnalyticsConstants.EKYC.consumerKyc1.rawValue
        case .one:
            return AnalyticsConstants.EKYC.consumerRekyc.rawValue
        default:
            return ""
        }
    }

    private var user: User? {
        guard
            let databaseStore = storeProvider.target(DatabaseStore.self),
            let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
        else { return nil }
        return user
    }

    var kycLevel: KYCLevel {
        return user?.kycLevelValue ?? .zero
    }

    var isShortenedFlowEnabled: Bool {
        return MayaEKYCUtility().isShortenedFlowEnabled()
    }

    var isBackIDCaptureEnabled: Bool {
        return MayaEKYCUtility().isBackIDCaptureEnabled()
    }

    var isShowIDValScoreEnabled: Bool {
        return MayaEKYCUtility().isShowIDValScoreEnabled()
    }

    var isShowIDQualityScoreEnabled: Bool {
        return MayaEKYCUtility().isShowIDQualityScoreEnabled()
    }

    var isSecondaryIDFlowEnabled: Bool {
        return MayaEKYCUtility().isSecondaryIDFlowEnabled()
    }

    var isIDImageQualityEnabled: Bool {
        return MayaEKYCUtility().isKycIdImageQualityEnabled()
    }

    var isExpandedKYCEnabled: Bool {
        return configurationService.expandedReKYCEnabled.value && configurationService.reKYCEnabled.value
    }

    var isShortenedFlowAddressDropdownEnabled: Bool {
        return MayaEKYCUtility().isShortenedFlowAddressDropdownEnabled()
    }

    var isShortenedFlowIdCaptureV3Enabled: Bool {
        return MayaEKYCUtility().isShortenedFlowIdCaptureV3Enabled()
    }

    var isShortenedFlowForeignAddressComplianceEnabled: Bool {
        return MayaEKYCUtility().isShortenedFlowForeignAddressComplianceEnabled()
    }

    var isFATCAEnabled: Bool {
        return MayaEKYCUtility().isFATCAEnabled()
    }

    var isDosriEnabled: Bool {
        return MayaEKYCUtility().isDosriEnabled()
    }

    var isPhilsysFlowEnabled: Bool {
        return MayaEKYCUtility().isPhilsysFlowEnabled()
    }

    var isForcePHNationalIDEnabled: Bool {
        return MayaEKYCUtility().forcePHNationalIDEnabled()
    }

    var isEddEnabled: Bool {
        return configurationService.eddEnabled.value
    }

    var shouldShowEDD: Bool {
        return isEddEnabled && isTaggedForEDD()
    }

    var isKYCTofuUXRedesignEnabled: Bool {
        return configurationServiceV2.kycTofuUXRedesignEnabled.value
    }

    var isKYCCMSv8MockEnabled: Bool {
        return configurationServiceV2.kycCMSv8MockEnabled.value
    }

    override init() {
        super.init()
        // TODO: CMS BE integration
        getReKYCDataAction = Action { [weak self] _ in
            guard let defaultReKYCData = ReKYCDataWrapper.defaultResponse else {
                return SignalProducer(error: Error())
            }

            return SignalProducer(value: defaultReKYCData)
                .on(value: { [weak self] wrapper in
                    self?.reKYCData.value = wrapper.data
                })
        }
    }

    func getExpandedReKYCAction() -> String? {
        guard let databaseStore = storeProvider.target(DatabaseStore.self),
              let user = user,
              let reKYCReason = user.reKycReason,
              let reKYCStatus = user.reKycStatus,
              let reKYCDataGuides = reKYCData.value?.promptGuides else {
            removeExpandedReKYCAction()
            return nil
        }

        for guide in reKYCDataGuides {
            if guide.criteria.hasReason(reKYCReason) && guide.criteria.hasStatus(reKYCStatus) {
                setExpandedReKYCAction(action: guide.action.code)
                return guide.action.code
            }
        }

        removeExpandedReKYCAction()
        return nil
    }

    func setExpandedReKYCAction(action: String) {
        if let userDefaults = storeProvider.target(UserDefaultsStore.self) {
            userDefaults.write(action, options: UserDefaultsStoreId.expandedReKYCAction)
        }
    }

    func removeExpandedReKYCAction() {
        guard let userDefaultsStore = storeProvider.target(UserDefaultsStore.self) else { return }
        userDefaultsStore.remove(UserDefaultsStoreId.expandedReKYCAction, completion: nil)
    }

    func removeReKYCNudge() {
        guard let userDefaultsStore = storeProvider.target(UserDefaultsStore.self) else { return }
        userDefaultsStore.remove(UserDefaultsStoreId.shouldShowReKYCNudge, completion: nil)
    }

    func setEntryPointReferral() {
        guard let userDefaults = storeProvider.target(UserDefaultsStore.self),
              let partner = userDefaults.readRaw(UserDefaultsStoreId.onboardingPartner) as? String,
              let onboardingPartner = Constants.Partners.Onboarding(rawValue: partner),
              let setOnboardingEntryPoint = userDefaults.readRaw(UserDefaultsStoreId.shouldOnboardingEntryPoint) as? Bool,
              setOnboardingEntryPoint
        else { return }

        let entrypoint = onboardingPartner.referral()
        userDefaults.write(entrypoint, options: UserDefaultsStoreId.onboardingEntryPoint)
        userDefaults.write(false, options: UserDefaultsStoreId.shouldOnboardingEntryPoint)
    }

    func setImageQualityRetryCount( _ count: Int) {
        guard let userDefaultsStore = storeProvider.target(UserDefaultsStore.self) else { return }
        userDefaultsStore.write(count, options: UserDefaultsStoreId.imageQualityRetryCount)
    }

    func getImageQualityRetryCount() -> Int {
        guard let userDefaultsStore = storeProvider.target(UserDefaultsStore.self) else { return 0}
        if let imageQualityRetryCount = userDefaultsStore.readRaw(UserDefaultsStoreId.imageQualityRetryCount) as? Int {
           return imageQualityRetryCount
        }
        return 0
    }

    func returnErrorCodeReason(code: Int?) -> String {
        guard let code = code else { return Constants.EKYC.ErrorReasons.defaultError }
        switch code {
        case 0, 500:
            return Constants.EKYC.ErrorReasons.nonHTTP
        case 40200:
            return Constants.EKYC.ErrorReasons.presubmissionIDNotFound
        case 30100:
            return Constants.EKYC.ErrorReasons.sessionTimeout
        case 1001:
            return Constants.EKYC.ErrorReasons.tencentUpload
        case 217:
            return Constants.EKYC.ErrorReasons.tencentFaceDetectionTimeout
        case 211:
            return Constants.EKYC.ErrorReasons.tencentLocalRef
        case 212:
            return Constants.EKYC.ErrorReasons.tencentUserStops
        case 213:
            return Constants.EKYC.ErrorReasons.tencentInnerError
        case 214:
            return Constants.EKYC.ErrorReasons.tencentAppSwitch
        case 215:
            return Constants.EKYC.ErrorReasons.tencentCameraPermission
        case 216:
            return Constants.EKYC.ErrorReasons.tencentInitSDK
        case 218:
            return Constants.EKYC.ErrorReasons.tencentPermissionCheck
        case 219:
            return Constants.EKYC.ErrorReasons.tencentAppStop
        case 220:
            return Constants.EKYC.ErrorReasons.tencentLiveData
        case 222:
            return Constants.EKYC.ErrorReasons.tencentLocalTimeout
        case 223:
            return Constants.EKYC.ErrorReasons.tencentPrepareTimeout
        case 224:
            return Constants.EKYC.ErrorReasons.tencentCheckPermission
        case 10000...19999:
            return Constants.EKYC.ErrorReasons.invalidRequest
        case 40000...49999:
            return Constants.EKYC.ErrorReasons.resourceNotFound
        case 50000...59999:
            return Constants.EKYC.ErrorReasons.internalServerError
        default:
            return Constants.EKYC.ErrorReasons.defaultError
        }
    }

    func returnIDQualityDecision(with inference: Inference) -> ImageQualityModelDataHandler.ImageQualityLabels {
        let confidence = inference.confidence
        let label = inference.label

        return ImageQualityModelDataHandler.ImageQualityLabels(rawValue: inference.label) ?? ImageQualityModelDataHandler.ImageQualityLabels.goodQuality
    }

    func checkImageQualityGuide(result: ImageQualityModelDataHandler.ImageQualityLabels, confidence: Int, imageQualityGuides: [KYCData.ImageQualityGuide], document: MayaEKYCSelectedDocument) -> Bool {
       for guide in imageQualityGuides {
           let matchesImageQualityFeedback = guide.key.caseInsensitiveCompare(result.rawValue) == .orderedSame
           let passesConfidenceThreshold = guide.passesThreshold(confidenceScore: confidence, decision: result)
           let hasAllowedAction = guide.isAllowed(document.key.lowercased())

           // Checks if all conditions met then "forcefully" return true
           if matchesImageQualityFeedback && passesConfidenceThreshold && hasAllowedAction {
               return true
           }
       }
       return false
   }

    func shouldRunIDClassification(idGuides: [KYCData.IDGuide], side: KYCData.CaptureGuide.ScanType, frontKey: String?) -> Bool {
        if idGuides.count == 0 {
            return false
        } else if side == .front {
            let frontNilGuide = idGuides.first(where: { $0.front == nil })
            return frontNilGuide == nil
        } else if side == .back,
                  let guide = idGuides.first(where: { $0.front == frontKey }) {
            return guide.back != nil
        }
        return true
    }

    func checkIDGuide(inference: Inference, idGuides: [KYCData.IDGuide], side: KYCData.CaptureGuide.ScanType, frontKey: String?, backKey: String?) -> KYCData.CaptureGuide.Decision {
        let label = inference.label

        // MARK: Provided frontKey, need to match with back
        if let frontKey = frontKey,
           side == .back {
            let filteredGuides = idGuides.filter({ $0.front == frontKey })

            for item in filteredGuides {
                if item.back == nil || item.back == label {
                    return KYCData.CaptureGuide.Decision.passed
                }
            }
            return KYCData.CaptureGuide.Decision.mismatch
        }

        // MARK: Provided backKey, need to match with front
        if let backKey = backKey,
           side == .front {
            let filteredGuides = idGuides.filter({ $0.back == backKey })

            for item in filteredGuides {
                if item.front == nil || item.front == label {
                    return KYCData.CaptureGuide.Decision.passed
                }
            }
            return KYCData.CaptureGuide.Decision.mismatch
        }

        // MARK: No provided keys, just check value with no matching
        for item in idGuides {
            // MARK: Front label matches
            if side == .front,
               let frontLabel = item.front,
               label == frontLabel {
                return KYCData.CaptureGuide.Decision.passed
            }
            // MARK: Front is null
            else if side == .front,
               item.front == nil {
                return KYCData.CaptureGuide.Decision.passed
            }
            // MARK: Back label matches
            else if side == .back,
                    let backLabel = item.back,
                    label == backLabel {
                return KYCData.CaptureGuide.Decision.passed
            }
            // MARK: Back is null
            else if side == .back,
               item.back == nil {
                return KYCData.CaptureGuide.Decision.passed
            }
        }

        return KYCData.CaptureGuide.Decision.failed
    }

    func isTaggedForEDD() -> Bool {
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
              let userEddStatus = user.eddStatus,
              let eddStatus = EDDStatus(rawValue: userEddStatus)
        else {
            return false
        }
        return eddStatus == .forSubmission
    }
}
