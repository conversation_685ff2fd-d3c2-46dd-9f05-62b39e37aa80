//
//  WebViewViewModelProtocol.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 3/17/21.
//  Copyright © 2021 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
import ConfigurationService
import Foundation
import Injector
import ReactiveSwift
import UIKit

enum WebViewModule {
    case _3ds
    case normal
}

enum WebViewResultStatus {
    case processing
    case success
    case failed
    case cancelled
    case none
}

protocol WebViewViewModelProtocol: ViewModelProtocol {
    var deepLinkRouter: PaymayaDeepLinkRouter { get }
    var configurationService: ConfigurationService { get }
    var backgroundSessionManager: BackgroundSessionManager { get }

    var link: URL { get }
    var title: String? { get }
    var willUseDefaultTitle: Bool { get }
    var shouldShowBackConfirmation: Bool { get }

    var response: Codable? { get }
    var resultStatus: WebViewResultStatus { get }
    var refreshSessionState: Property<RefreshSessionState> { get }

    var module: WebViewModule { get }
    var analyticsModule: AnalyticsModule { get }
    var webViewConfiguration: WebViewConfiguration { get }
    var isExtenalLinkPromptEnabled: Bool { get }
    var shouldShowExternalLinkPrompt: Bool { get }

    func setResultStatus(from url: URL)
    func triggerInitialSessionRefresh()
    func disableSessionRefreshIntervals()
    func updateRefreshSessionState(_ state: UpdateRefreshSessionState)
    func getAnalyticsAttributes(for resultStatus: WebViewResultStatus) -> [AnalyticsAttributeKey: String]?
    func checkURLForExternalLinkPrompt() -> Bool
}

extension WebViewViewModelProtocol {
    var willUseDefaultTitle: Bool {
        return false
    }

    var shouldShowBackConfirmation: Bool {
        return false
    }

    var response: Codable? {
        return nil
    }

    var resultStatus: WebViewResultStatus {
        return .none
    }

    var refreshSessionState: Property<RefreshSessionState> {
        return backgroundSessionManager.refreshSessionState
    }

    var module: WebViewModule {
        return .normal
    }

    var analyticsModule: AnalyticsModule {
        return Analytics.WebView()
    }

    var webViewConfiguration: WebViewConfiguration {
        return WebViewConfiguration()
    }

    var isExtenalLinkPromptEnabled: Bool {
        return ContainerWrapper.shared.resolve(ConfigurationServiceV2.self).externalLinkPromptEnabled.value
    }

    var shouldShowExternalLinkPrompt: Bool {
        return false
    }

    func setResultStatus(from url: URL) {
        // Do nothing
    }

    func triggerInitialSessionRefresh() {
        backgroundSessionManager.triggerInitialSessionRefresh()
    }

    func disableSessionRefreshIntervals() {
        backgroundSessionManager.disableSessionRefreshIntervals()
    }

    func updateRefreshSessionState(_ state: UpdateRefreshSessionState) {
        backgroundSessionManager.updateRefreshSessionState(state)
    }

    func getAnalyticsAttributes(for resultStatus: WebViewResultStatus) -> [AnalyticsAttributeKey: String]? {
        return nil
    }

    func checkURLForExternalLinkPrompt() -> Bool {
        guard isExtenalLinkPromptEnabled else { return false }
        if shouldShowExternalLinkPrompt {
            return true
        } else if module == ._3ds {
            return false
        } else {
            let components = URLComponents(url: link, resolvingAgainstBaseURL: false)

            if let host = components?.host {
                return !Constants.WebView.internalDomains.contains { domainRange in
                    host.range(of: domainRange, options: .caseInsensitive) != nil
                }
            }
            return false
        }
    }
}

struct WebViewConfiguration {
    var allowsInlineMediaPlayback: Bool
    var allowsLandscape: Bool

    let appBarConfiguration: AppBarConfiguration?
    let backgroundColor: UIColor?
    let statusBarStyle: Int?

    init(allowsInlineMediaPlayback: Bool = false,
         allowsLandscape: Bool = false,
         appBarConfiguration: AppBarConfiguration? = nil,
         backgroundColor: UIColor? = nil,
         statusBarStyle: UIStatusBarStyle? = nil
    ) {
        self.allowsInlineMediaPlayback = allowsInlineMediaPlayback
        self.allowsLandscape = allowsLandscape

        self.appBarConfiguration = appBarConfiguration
        self.backgroundColor = backgroundColor
        self.statusBarStyle = statusBarStyle?.rawValue ?? nil
    }
}

struct AppBarConfiguration {
    let color: UIColor?
    let iconColor: UIColor?

    init(color: UIColor? = nil, iconColor: UIColor? = nil) {
        self.color = color
        self.iconColor = iconColor
    }
}
