//
//  MayaFeatureUpdaterViewModel.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 7/4/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//
import Foundation

class MayaFeatureUpdaterViewModel: ViewModel, MayaFeatureUpdaterViewModelProtocol {
    let configuration: FeatureUpdaterContentConfiguration
    let style: FeatureUpdaterStyle
    let appStoreLink: URL?

    init(configuration: FeatureUpdaterContentConfiguration = FeatureUpdaterContentConfiguration(),
         style: FeatureUpdaterStyle = FeatureUpdaterStyle(),
         appStoreLink: URL? = Constants.AppStoreLinks.store.url) {
        self.configuration = configuration
        self.style = style
        self.appStoreLink = appStoreLink
    }
}
