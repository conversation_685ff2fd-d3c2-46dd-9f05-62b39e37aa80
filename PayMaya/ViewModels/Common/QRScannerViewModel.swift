//
//  QRScannerViewModel.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 28/04/2018.
//  Copyright © 2018 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import ConfigurationService
import Foundation
import Injector
import MayaCoreData
import MayaFormatterManager
import ReactiveSwift
import StoreProvider

private let createMerchantPaymentErrorTitle = L10n.Error.Merchant.Payment.title

class QRScannerViewModel: ViewModel, QRScannerViewModelProtocol {
    @Inject private var storeProvider: StoreProvider
    @Inject private var configurationService: ConfigurationService
    @Inject private var apiProvider: APIProvider
    @Inject private var qrValidationUtility: QRValidationUtilityProtocol
    @Inject var formatter: MayaFormatterManager

    let scanType: CodeType
    var classifyCodeAction: Action<CodeInput, CodeDetectionOutput, PayMayaError>!
    var createQRPHDynamicMerchantPaymentAction: Action<QRPHMerchant, CreatedMerchantPaymentV2, PayMayaError>?
    var restartCameraScanAction: Action<Void, Void, PayMayaError>?

    var isQrWithCheckoutFlowEnabled: Bool {
        return configurationService.qrWithCheckoutFlowEnabled.value
    }

    var isEcommAppEventV2QrEnabled: Bool {
        return configurationService.ecommAppEventV2QrEnabled.value
    }

    var isQROffUsAvailable: Bool {
        guard let isQROffUsAvailable = configurationService.maintenance?.serviceAvailability.qrOffUs else { return true }
        return !isQROffUsAvailable.isUnderMaintenance
    }

    init(scanType: CodeType) {
        self.scanType = scanType
        super.init()

        classifyCodeAction = Action { [weak self] scannedCode in
            let qrSource = scannedCode.source
            let classificationError = PayMayaError(type: qrSource.errorType, viewModel: ErrorAlertViewModel(title: qrSource.errorTitle, message: qrSource.errorMessage, image: qrSource.image))

            guard let self = self,
                  let qr = scannedCode.text
            else {
                return SignalProducer<CodeDetectionOutput, PayMayaError>(error: classificationError)
            }

            return SignalProducer<CodeDetectionOutput, PayMayaError> { observer, _ in
               do {
                    try self.qrValidationUtility.checkQRPHFormatIndicator(qrCode: qr)
                    try self.qrValidationUtility.checkQRPHCrc(qrCode: qr)

                    let parsedQRPH = try self.qrValidationUtility.parseQRPH(qrCode: qr)
                    let qrPHType = try self.qrValidationUtility.getQRPHType(qrDetails: parsedQRPH)
                    let bic = parsedQRPH.acquirerId ?? ""
                    try self.qrValidationUtility.validateQRPHCrc(qrCode: qr, bic: bic)

                    switch qrPHType {
                    case .p2pSendMoney:
                        try self.qrValidationUtility.validateQRPHP2p(qrDetails: parsedQRPH)

                        guard let p2pMerchantCreditAccount = parsedQRPH.creditAccountNumber else {
                            let validationError = self.setPayMayaError(for: QRError.missingRequiredField(field: QRPHField.p2pMerchantCreditAccount, bic: bic), qrSource: qrSource)
                            observer.send(error: validationError ?? classificationError)
                            return
                        }

                        let sendMoneyQR = SendMoneyQR(transactionPayload: SendMoneyQR.SendMoneyTransactionPayload(recipient: p2pMerchantCreditAccount, message: parsedQRPH.additionalData?.purpose, amount: parsedQRPH.transactionAmount, type: .payMaya))

                        observer.send(value: CodeDetectionOutput(source: qrSource, detectedCode: sendMoneyQR))
                    case .p2pBankTransfer:
                        try self.qrValidationUtility.validateQRPHP2p(qrDetails: parsedQRPH)
                        guard let bankTransferQR = BankTransferQR(details: parsedQRPH) else {
                            observer.send(error: classificationError)
                            return
                        }

                        observer.send(value: CodeDetectionOutput(source: qrSource, detectedCode: bankTransferQR))
                    case .p2mOnUs,
                         .p2mOffUs:
                        try self.qrValidationUtility.validateQRPHP2m(qrDetails: parsedQRPH)

                        let isDynamicQR = parsedQRPH.pointOfInitiation == QRPHConstants.pointOfInitiationDynamic
                        let isOnUs = qrPHType == .p2mOnUs
                        let merchantType = self.getMerchantType(isOnUs: isOnUs, isDynamicQR: isDynamicQR)

                         guard let qrphMerchant = QRPHMerchant(
                             qrDetails: parsedQRPH,
                             pointOfInitiation: isDynamicQR ? .dynamic : .static,
                             qrRawValue: qr,
                             createMerchantType: merchantType,
                             isOnUsQr: isOnUs
                         ) else {
                             observer.send(error: classificationError)
                             return
                         }

                         observer.send(value:
                            CodeDetectionOutput(
                              source: qrSource,
                              detectedCode: qrphMerchant,
                              isDynamicQr: isDynamicQR
                            )
                         )
                    case .p2b:
                        let validationError = self.setPayMayaError(for: QRError.parsingError(field: QRPHField.qrTypeP2B), qrSource: qrSource, errorType: .invalidScanP2B)
                        observer.send(error: validationError ?? classificationError)
                    default:
                        observer.send(error: classificationError)
                    }
                } catch let error {
                    if let qrError = error as? QRError,
                       let validationError = self.setPayMayaError(for: qrError, qrSource: qrSource) {
                        observer.send(error: validationError)
                    } else {
                        observer.send(error: classificationError)
                    }
                }

                observer.sendCompleted()
            }
        }

        createQRPHDynamicMerchantPaymentAction = Action { [weak self] scannedQR in
            guard let self = self else {
                return PayMayaError.validationError(title: createMerchantPaymentErrorTitle)
            }

            if self.isQrWithCheckoutFlowEnabled {
                return self.createQRPHDynamicMerchantPayment(scannedQR: scannedQR)
            } else {
                return SignalProducer<CreatedMerchantPaymentV2, PayMayaError>(error: PayMayaError(viewModel: ErrorAlertViewModel(title: createMerchantPaymentErrorTitle)))
            }
        }

        restartCameraScanAction = Action { [weak self] _ in
            guard self != nil else {
                return PayMayaError.validationError(title: createMerchantPaymentErrorTitle)
            }

            return SignalProducer<Void, PayMayaError>(value: Void())
        }
    }
}

private extension QRScannerViewModel {
    var currency: String {
        var currency = Constants.Defaults.Common.currency.rawValue
        if let fetchResult = storeProvider.target(DatabaseStore.self)?.read(DatabaseStore.ReadingOptions(DatabaseEntity.balance)),
            let newBalance = fetchResult.value as? Balance,
            let availableNewBalance = newBalance.available,
            let availableNewBalanceCurrency = availableNewBalance.currency {
            currency = availableNewBalanceCurrency
        }
        return currency
    }

    func createQRPHDynamicMerchantPayment(scannedQR: QRPHMerchant) -> SignalProducer<CreatedMerchantPaymentV2, PayMayaError> {
        guard let type = scannedQR.createMerchantType?.rawValue, let qrPayload = scannedQR.qrRawValue else {
            return PayMayaError.validationError(title: createMerchantPaymentErrorTitle)
        }

        let amountRequest = AmountRequest(currency: self.currency, value: self.formatter.number.currencyDoubleValue(string: scannedQR.transactionAmount!))
        let additionalInfo = MerchantPaymentQRPHRequest.Merchant.AdditionalInfo(
            systemId: scannedQR.globallyUniquePaymentSystemID,
            acquirerRequiredInfo: scannedQR.acquirerRequiredInformation,
            loyaltyNum: scannedQR.loyaltyNumber,
            proxyNotifyFlag: scannedQR.proxyNotifFlag,
            merchantCreditAccountNumber: scannedQR.merchantCreditAccountNumber,
            countryCode: scannedQR.countryCode,
            merchantPostalCode: scannedQR.merchantPostalCode
        )
        let merchant = MerchantPaymentQRPHRequest.Merchant(
            acquirerId: scannedQR.acquirerID,
            id: scannedQR.id,
            name: scannedQR.name,
            claimId: scannedQR.claimID,
            terminalId: scannedQR.terminalID,
            storeName: scannedQR.storeName,
            categoryCode: scannedQR.categoryCode,
            city: scannedQR.city,
            additionalInfo: additionalInfo
        )
        let request = MerchantPaymentQRPHRequest(type: type, amount: amountRequest, qrPayload: qrPayload, merchant: merchant)

        return self.apiProvider.reactive.request(API.MerchantPaymentV2.createQRPH(request), type: CreatedMerchantPaymentV2.self, decoder: CreatedMerchantPaymentV2.decoder)
            .mapMayaError(title: createMerchantPaymentErrorTitle)
    }

    func setPayMayaError(for error: QRError, qrSource: CodeInputSource, errorType: ErrorType? = nil) -> PayMayaError? {
        let encoder = JSONEncoder()

        if let data = try? encoder.encode(error) {
            return PayMayaError(
                type: errorType ?? qrSource.errorType,
                viewModel: ErrorAlertViewModel(
                    title: qrSource.errorTitle,
                    message: qrSource.errorMessage,
                    image: qrSource.image
                ),
                data: data
            )
        }

        return nil
    }

    func getMerchantType(isOnUs: Bool, isDynamicQR: Bool) -> CreateMerchantType {
        switch (isOnUs, isDynamicQR) {
        case (true, true): return .onUsDynamicQrPh
        case (true, false): return .onUsStaticQrPh
        case (false, true): return .offUsDynamicQrPh
        case (false, false): return .offUsStaticQrPh
        }
    }
}
