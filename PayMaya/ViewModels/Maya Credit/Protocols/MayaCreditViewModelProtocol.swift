//
//  MayaCreditViewModelProtocol.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 2/23/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import Error
import ReactiveSwift
import UIKit

enum LoanApplicationStatus {
    typealias LoanAccountID = String

    case booked(LoanAccountID)
    case approved
    case submitted
    case none

    init?(status: String?, loanAccountID: String? = nil) {
        guard let status = status else {
            self = .none
            return
        }

        switch status.uppercased() {
        case "BOOKED":
            guard let loanAccountID = loanAccountID else {
                self = .none
                return
            }

            self = .booked(loanAccountID)
        case "APPROVED":
            self = .approved
        case "SUBMITTED":
            self = .submitted
        default: self = .none
        }
    }
}

extension LoanApplicationStatus: Equatable {
    static func == (lhs: LoanApplicationStatus, rhs: LoanApplicationStatus) -> Bool {
        switch (lhs, rhs) {
        case (.booked(let lhsLoanAccountID), .booked(let rhsLoanAccountID)):
            return lhsLoanAccountID == rhsLoanAccountID
        case (.approved, .approved):
            return true
        case (.submitted, .submitted):
            return true
        case (.none, .none):
            return true
        default: return false
        }
    }
}

enum LoanAccountStatus: String {
    case active = "ACTIVE"
    case pastDue = "PAST_DUE"
    case suspended = "SUSPENDED"
    case writeOff = "WRITE_OFF"
    case writtenOff = "WRITTEN_OFF"
    case pendingWrittenOff = "PENDING_WRITTEN_OFF"
}

enum MayaCreditCellType {
    case spacer
    case approved
    case submitted
    case contactInformation
    case logo
    case helpCenter
    case ineligible
    case loading
    case error
    case availableCredit
    case billingStatementRevamp
    case manageMyCredit
    case transactions
    case partnerMerchant
    case initialEligibility
    case writeOff
    case writtenOff
}

extension MayaCreditCellType {
    var identifier: String {
        switch self {
        case .spacer: return "MayaCreditSpacerTableViewCell"
        case .approved: return "MayaCreditApprovedTableViewCell"
        case .submitted: return "MayaCreditSubmittedTableViewCell"
        case .contactInformation: return "MayaCreditContactInformationTableViewCell"
        case .logo: return "MayaCreditLogoTableViewCell"
        case .helpCenter: return "MayaCreditHelpCenterTableViewCell"
        case .ineligible: return "MayaCreditIneligibleTableViewCell"
        case .loading: return "MayaCreditShimmerTableViewCell"
        case .error: return "MayaCreditErrorTableViewCell"
        case .availableCredit: return "NewMayaAvailableCreditTableViewCell"
        case .billingStatementRevamp: return "MayaBillingStatementRevampTableViewCell"
        case .manageMyCredit: return "NewMayaManageMyCreditTableViewCell"
        case .transactions: return "MayaCreditTransactionsTableViewCell"
        case .partnerMerchant: return "MayaCreditPartnerMerchantTableViewCell"
        case .initialEligibility: return "MayaCreditInitialEligibilityTableViewCell"
        case .writeOff: return "MayaCreditWriteOffTableViewCell"
        case .writtenOff: return "MayaCreditWrittenOffTableViewCell"
        }
    }
}

extension MayaCreditCellType: Equatable {}

protocol MayaCreditViewModelProtocol: ViewModelProtocol {
    var displayBalance: String { get }
    var totalCreditBalance: String { get }
    var progress: Float { get }
    var creditLimit: String { get }
    var usedBalance: String { get }
    var isAccountUpgraded: Bool { get }
    var isAccountLocked: MutableProperty<Bool> { get }
    var header: String { get }
    var amountDue: String { get }
    var outstandingBalance: String { get }
    var latestBill: String { get }
    var statementMonth: String { get }
    var dueDate: String { get }
    var isPayNowEnabled: Bool { get }
    var isPayThisBillEnabled: Bool { get }
    var billingStatementState: BillingStatementState? { get }
    var isBalanceHiddenProperty: MutableProperty<Bool> { get }
    var pullToRefreshTriggered: MutableProperty<Bool> { get }
    var items: MutableProperty<[MayaCreditCellType]> { get }
    var analyticsModule: MutableProperty<AnalyticsModule?> { get }
    var loanApplicationStatusProperty: MutableProperty<LoanApplicationStatus?> { get }
    var creditAccount: MutableProperty<MayaCreditAccount?> { get }
    var eligibility: MutableProperty<MayaCreditEligibility?> { get }
    var feeRate: MutableProperty<Float?> { get }
    var currentLoanApplication: MutableProperty<MayaCreditCurrentLoanApplication?> { get }
    var latestBillDueDateColor: UIColor { get }

    var getMayaCreditLoanApplication: Action<Void, MayaCreditCurrentLoanApplication, PayMayaError>! { get }
    var getMayaCreditLoanAccount: Action<String, MayaCreditAccount, PayMayaError>! { get }
    var getMayaCreditConsents: Action<Void, [MayaCreditOptInConsent], PayMayaError>! { get }
    var billingStatementUrlAction: Action<Void, MayaCreditBillingStatementUrl, PayMayaError>! { get }
    var submitConsentAction: Action<[MayaCreditOptInConsent], Void, PayMayaError>! { get }

    func toggleHideBalance()
    func buildPayCreditBillDeeplink() -> URL?
    func buildPayEarlyCreditBillDeeplink() -> URL?
    func buildPayNowDeeplink() -> URL?
    func submitConsent(consents: [MayaCreditOptInConsent])
    func buildTransferAlertViewModel(account: MayaCreditAccount, url: URL) -> MayaAlertViewModel

    var mayaCreditActivitiesViewModel: MayaCreditActivitiesViewModelProtocol? { get }

    var showContactReference: MutableProperty<Bool> { get }
    var getContactReference: Action<Void, MayaCreditContactReference, PayMayaError>! { get }

    var getPersonDetails: Action<Void, MayaCreditPersonDetails, PayMayaError>! { get }

    var isWriteOffEnabled: Bool { get }
}
