//
//  MayaCreditTransferFundsViewModelProtocol.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 11/3/21.
//  Copyright © 2021 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import ConfigurationService
import Error
import Foundation
import Injector
import RavenLocation
import ReactiveSwift

enum MayaCreditTransferFundsAmountInputState {
  case valid
  case maximumCreditLimitReached
  case amountEmpty
  case availableCreditLimitExceeds
  case cashInLimitExceeds
  case availableAndCashInLimitsExceed

  var validationMessage: String? {
    switch self {
    case .valid: return nil
    case .maximumCreditLimitReached: return L10n.Maya.Credit.Transfer.Spiel.Maximum.Credit.Limit.reached
    case .amountEmpty: return CommonStrings.Common.No.Amount.spiel
    case .availableCreditLimitExceeds: return L10n.Maya.Credit.Transfer.Spiel.Exceeds.Available.credit
    case .cashInLimitExceeds: return L10n.Maya.Credit.Transfer.Spiel.Exceeds.Cashin.credit
    case .availableAndCashInLimitsExceed: return L10n.Maya.Credit.Transfer.Spiel.Exceeds.Available.credit
    }
  }

  var isValid: Bool {
    switch self {
    case .valid: return true
    default: return false
    }
  }
}

enum NewMayaCreditTransferFundsAmountInputState {
    case valid
    case maximumCreditLimitReached
    case amountEmpty
    case availableCreditLimitExceeds
    case cashInLimitExceeds
    case availableAndCashInLimitsExceed

    var validationMessage: String? {
        switch self {
        case .valid: return nil
        case .maximumCreditLimitReached: return L10n.Maya.Credit.Transfer.Spiel.Maximum.Credit.Limit.New.reached
        case .amountEmpty: return CommonStrings.Common.No.Amount.spiel
        case .availableCreditLimitExceeds: return L10n.Maya.Credit.Transfer.Spiel.Exceeds.Available.credit
        case .cashInLimitExceeds: return L10n.Maya.Credit.Transfer.Spiel.Exceeds.Cashin.credit
        case .availableAndCashInLimitsExceed: return L10n.Maya.Credit.Transfer.Spiel.Exceeds.Available.credit
        }
    }

    var isValid: Bool {
        switch self {
        case .valid: return true
        default: return false
        }
    }
}

protocol MayaCreditTransferFundsViewModelProtocol: AmountFormatterViewModelProtocol {
    var creditAccount: MayaCreditAccount { get }
    var cashInLimitValue: String? { get }
    var availableCreditLimitValue: String { get }

    var isExceedsCashInLimit: MutableProperty<Bool> { get }
    var isExceedsAvailableCreditLimit: MutableProperty<Bool> { get }
    var isCashInLimitHidden: MutableProperty<Bool> { get }
    var inlineValidationMessage: MutableProperty<String?> { get }

    var createTransferAction: Action<Void, String, PayMayaError>! { get }
    var getAccountLimitsAction: Action<Void, Void, PayMayaError>! { get }
}

protocol NewMayaCreditTransferFundsViewModelProtocol: MayaCreditTransferFundsViewModelProtocol {
    var accountNumberLastFourDigits: String { get }
    var isContinueButtonEnabled: MutableProperty<Bool> { get }
    var calculateDisbursementAction: Action<(String, String), MayaCreditFeesAndTaxes, PayMayaError>! { get }
    var handleRavenGetGeolocation: Action<Void, RavenLocation.Location?, RavenLocation.Error>! { get }
    var handleRavenExtract: Action<Void, Void, PayMayaError>! { get }
    var isLendingDataExtractionEnabled: Bool { get }
    var isGeolocationEnabled: Bool { get }
}
