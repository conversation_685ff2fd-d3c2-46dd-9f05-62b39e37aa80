//
//  NewMayaCreditActivationViewModelProtocol.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 21/04/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation
import RavenLocation
import ReactiveSwift

protocol NewMayaCreditActivationViewModelProtocol: ViewModelProtocol {
    var amountWithCurrency: String { get }
    var expiry: String { get }

    var billingEndDate: MutableProperty<String> { get }
    var feeRate: String { get }

    var backupIdentity: String { get }
    var backupEmailStatus: EmailIdentityStatus { get }
    var backupIdentityInfo: String { get }
    var activationTerms: MutableProperty<[ActivationTerm]> { get }

    var isUpdateButtonEnabled: MutableProperty<Bool> { get }

    var isButtonEnabled: MutableProperty<Bool> { get }
    var submitLoanAction: Action<Void, OTPResponse, PayMayaError>! { get }

    func getBillingEndDateSelection() -> BillingEndDateSelection?

    func setAcceptTerms()

    var riskBasedPricingRate: Float? { get }

    var assignContactReferenceViewModel: MayaCreditAssignContactReferenceViewModel { get }

    func getPersonalDetailsSelection() -> PersonalDetailsSelection

    var emailField: NewFieldProperty! { get }
    var alternativeMobileNumberField: NewFieldProperty! { get }
    var genderField: NewFieldProperty { get }
    var maritalStatusField: NewFieldProperty { get }
    var mothersMaidenFirstNameField: NewFieldProperty! { get }
    var mothersMaidenMiddleNameField: NewFieldProperty! { get }
    var mothersMaidenLastNameField: NewFieldProperty! { get }

    var selectedGender: Constants.PersonalDetails.Gender? { get }
    var selectedMaritalStatus: Constants.PersonalDetails.MaritalStatus? { get }
    var isNoMaidenMiddleNameChecked: MutableProperty<Bool> { get set }

    var getContactReference: Action<Void, MayaCreditContactReference, PayMayaError>! { get }
    var submitContactReference: Action<Void, MayaCreditContactReferencePatchResponse, PayMayaError>! { get }
    var getMayaCreditFullEligibility: Action<(String, String?, MayaCreditEligibility?), MayaCreditEligibility, PayMayaError>! { get }
    var handleRavenGetGeolocation: Action<Void, RavenLocation.Location?, RavenLocation.Error>! { get }
    var handleRavenExtract: Action<Void, Void, PayMayaError>! { get }

    func formatAlternativeNumber(with text: String, and replacement: String) -> String?

    var personalDetailsState: PersonalDetailsState? { get }

    var shouldInitializeDeviceScoring: Bool { get }
    var riskLevel: String { get }

    var getPersonDetails: Action<Void, MayaCreditPersonDetails, PayMayaError>! { get }
    var submitPersonDetails: Action<Void, MayaCreditPersonDetailsPatchResponse, PayMayaError>! { get }

    var isLendingDataExtractionEnabled: Bool { get }
    var isGeolocationEnabled: Bool { get }
}

enum PersonalDetailsState {
    case add
    case edit
}
