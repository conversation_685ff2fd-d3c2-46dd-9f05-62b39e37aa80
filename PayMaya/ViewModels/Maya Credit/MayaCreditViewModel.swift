//
//  MayaCreditViewModel.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> Barroga on 2/23/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
import AssetProvider
import ConfigurationService
import Error
import Foundation
import Injector
import MayaCoreData
import MayaFormatterManager
import ReactiveSwift
import ShieldFraud
import StoreProvider
import UIKit

class MayaCreditViewModel: ViewModel, MayaCreditViewModelProtocol {
    @Inject private var storeProvider: StoreProvider
    @Inject private var apiProvider: APIProvider
    @Inject private var toggleBalanceShownService: ToggleBalanceShownService
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2
    @Inject private var formatter: MayaFormatterManager

    let disposeBag = CompositeDisposable()
    let items: MutableProperty<[MayaCreditCellType]> = MutableProperty<[MayaCreditCellType]>([])

    var mayaCreditActivitiesViewModel: MayaCreditActivitiesViewModelProtocol?

    var isAccountUpgraded: Bool {
        if let databaseStore = storeProvider.target(DatabaseStore.self),
           let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User {
            return user.kycStatusValue == .approved
        } else {
            return false
        }
    }

    var isAccountLocked = MutableProperty(false)

    var isBalanceHidden: Bool {
        toggleBalanceShownService.getBalanceHiddenProperty(for: .credit).value
    }

    var showContactReference = MutableProperty(false)

    var displayBalance: String {
        if let creditAccount = creditAccount.value {
            let creditLimit = creditAccount.loanAmount - creditAccount.balances.principal
            let formattedBalance = formatter.number.currencyStringValue(double: Double(creditLimit))
            return showFinancialValue(value: formattedBalance)
        }

        return showFinancialValue(value: Constants.Defaults.Common.amount.rawValue)
    }

    var totalCreditBalance: String {
        let currency = Constants.Defaults.Common.peso.rawValue
        if let creditAccount = creditAccount.value {
            if creditAccount.balances.total.truncatingRemainder(dividingBy: 1) == 0 {
                return showFinancialValue(value: String(format: "%.0f", creditAccount.balances.total))
            } else {
                return showFinancialValue(value: formatter.number.currencyStringValue(double: Double(creditAccount.balances.total)))
            }
        }
        return currency
    }

    var progress: Float {
        if let creditAccount = creditAccount.value {
            return (creditAccount.balances.principal / creditAccount.loanAmount)
        } else {
            return 0.0
        }
    }

    var creditLimit: String {
        let currency = Constants.Defaults.Common.peso.rawValue
        if let creditAccount = creditAccount.value {
            let creditLimit = getFinancialValue(value: formatter.number.currencyStringValue(double: Double(creditAccount.loanAmount)))
            return L10n.Maya.Credit.Dashboard.Balance.limit(String(format: "%@%@", currency, creditLimit))
        } else {
            return currency
        }
    }

    var usedBalance: String {
        let currency = Constants.Defaults.Common.peso.rawValue
        if let creditAccount = creditAccount.value {
            let usedBalance = getFinancialValue(value: formatter.number.currencyStringValue(double: Double(creditAccount.balances.principal)))
            return L10n.Maya.Credit.Dashboard.Balance.used(String(format: "%@%@", currency, usedBalance))
        } else {
            return "\(currency)\(Constants.Defaults.Common.amount.rawValue)"
        }
    }

    var header: String {
        L10n.Maya.Credit.Bill.Period.label(billingStatement?.endDateTime.format(with: .month).uppercased() ?? "", billingStatement?.endDateTime.format(with: .year) ?? "")
    }

    var amountDue: String {
        showFinancialValue(value: formatter.number.currencyStringValue(double: billingStatement?.totalAmountDue ?? Double.init()))
    }

    var outstandingBalance: String {
        showFinancialValue(value: formatter.number.currencyStringValue(double: Double(creditAccount.value?.balances.total ?? Float.init())))
    }

    var latestBill: String {
        showFinancialValue(value: formatter.number.currencyStringValue(double: Double(creditAccount.value?.dues.total ?? Float.init())))
    }

    var statementMonth: String {
        guard let endDate = billingStatement?.endDateTime else { return String.empty }

        let dateFormatter = DateFormatters.getDateFormatter(for: .custom(dateFormat: "MMM yyyy"))
        return dateFormatter.string(from: endDate)
    }

    var dueDate: String {
        "\(billingStatement?.dueDate.format(with: .custom(dateFormat: "MMMM d, yyyy")) ?? "")"
    }

    var isPayNowEnabled: Bool {
        guard let creditAccount = creditAccount.value else { return false }

        let totalBalance = creditAccount.balances.total

        let isTotalBalanceGreaterThanZero = totalBalance > 0
        let isPendingWrittenOff = creditAccount.status == LoanAccountStatus.pendingWrittenOff.rawValue

        guard isWriteOffEnabled else {
            return isTotalBalanceGreaterThanZero
        }

        return !isPendingWrittenOff && isTotalBalanceGreaterThanZero
    }

    var isPayThisBillEnabled: Bool {
        guard let totalDues = creditAccount.value?.dues.total else { return false }
        return totalDues > 0
    }

    var latestBillDueDateColor: UIColor {
        var color = CommonAsset.MayaColors.Primary.primaryBlack.color

        guard let status = creditAccount.value?.status,
              let daysLate = creditAccount.value?.daysLate,
              let daysInArrears = creditAccount.value?.daysInArrears
        else { return color }

        if status == LoanAccountStatus.active.rawValue {
            if MayaCreditAccount.DaysLateGracePeriodLowerBound...MayaCreditAccount.DaysLateGracePeriodUpperBound ~= daysLate {
                color = CommonAsset.MayaColors.System.systemWarning.color
            }
        } else if daysInArrears > 0 {
            color = CommonAsset.MayaColors.System.systemError.color
        }

        return color
    }

    var isWriteOffEnabled: Bool {
        configurationServiceV2.mecWriteOffEnabled.value
    }

    var isMecAPIGatewayEnabled: Bool {
        configurationServiceV2.mecAPIGatewayEnabled.value
    }

    let isBalanceHiddenProperty = MutableProperty(false)
    let analyticsModule: MutableProperty<AnalyticsModule?> = MutableProperty<AnalyticsModule?>(nil)

    var getMayaCreditLoanApplication: Action<Void, MayaCreditCurrentLoanApplication, PayMayaError>!
    var getMayaCreditLoanAccount: Action<String, MayaCreditAccount, PayMayaError>!
    var getMayaCreditConsents: Action<Void, [MayaCreditOptInConsent], PayMayaError>!
    var billingStatementUrlAction: Action<Void, MayaCreditBillingStatementUrl, PayMayaError>!
    var submitConsentAction: Action<[MayaCreditOptInConsent], Void, PayMayaError>!
    var getContactReference: Action<Void, MayaCreditContactReference, PayMayaError>!
    var getPersonDetails: Action<Void, MayaCreditPersonDetails, PayMayaError>!

    let loanApplicationStatusProperty: MutableProperty<LoanApplicationStatus?> = MutableProperty<LoanApplicationStatus?>(nil)
    let creditAccount: MutableProperty<MayaCreditAccount?> = MutableProperty<MayaCreditAccount?>(nil)
    let eligibility: MutableProperty<MayaCreditEligibility?> = MutableProperty<MayaCreditEligibility?>(nil)
    let feeRate: MutableProperty<Float?> = MutableProperty<Float?>(nil)
    let currentLoanApplication: MutableProperty<MayaCreditCurrentLoanApplication?> = MutableProperty<MayaCreditCurrentLoanApplication?>(nil)

    var pullToRefreshTriggered: MutableProperty<Bool> = MutableProperty<Bool>(false)

    private var billingStatement: MayaCreditBillingStatement?
    var billingStatementState: BillingStatementState?

    private let minLockedDaysInArrears: Int = 30
    let device: EligibilityRequest.Device

    init(deviceId: String = Shield.shared().sessionId, osVersion: String = UIDevice.current.systemVersion) {
        self.device = EligibilityRequest.Device(id: deviceId, channel: DeviceType.kind, version: osVersion)
        super.init()
        isBalanceHiddenProperty <~ toggleBalanceShownService.getBalanceHiddenProperty(for: .credit).producer

        let mapError: (Error) -> PayMayaError = { error in
            if case .backend(let apiError) = error {
                switch apiError {
                case .failed(let message):
                    if let statusCode = APIConstants.APIStatusCode(rawValue: message.error.code),
                       statusCode == .contentNotFound {
                        return PayMayaError(type: .contentNotFound)
                    }
                    return PayMayaError(viewModel: ErrorAlertViewModel(title: defaultErrorTitle, error: message.error))
                case .sessionTimeout(let message):
                    return PayMayaError(type: .sessionTimeout(message.error.getDisplayMessage()))
                default:
                    return PayMayaError(viewModel: ErrorAlertViewModel(title: defaultErrorTitle))
                }
            }
            return PayMayaError(viewModel: ErrorAlertViewModel(title: defaultErrorTitle))
        }

        getMayaCreditLoanApplication = Action { [weak self] _ in
            guard let self = self else { return PayMayaError.validationError() }
            return self.apiProvider.reactive.request(API.MayaCredit.getLoanApplication, type: MayaCreditCurrentLoanApplication.self)
                .mapError(mapError)
                .on(value: { [weak self] currentLoanApplication in
                    self?.currentLoanApplication.value = currentLoanApplication
                })
        }

        getMayaCreditLoanAccount = Action { [weak self] loanAccountId in
            guard let self = self else { return SignalProducer<MayaCreditAccount, PayMayaError>(error: PayMayaError()) }

            self.mayaCreditActivitiesViewModel = MayaCreditActivitiesViewModel(accountId: loanAccountId, mode: .latest)

            let apiTargetType: APITargetType = isMecAPIGatewayEnabled ?
                API.MayaCredit.getLoanAccountV2(loanAccountId) :
                API.MayaCredit.getLoanAccount(loanAccountId)

            return self.apiProvider.reactive.request(apiTargetType, type: MayaCreditAccount.self, decoder: MayaCreditAccount.decoder)
                .on(value: { [weak self] creditAccount in
                    guard let self = self else { return }
                    self.creditAccount.value = creditAccount
                    self.isAccountLocked.value = creditAccount.status == LoanAccountStatus.suspended.rawValue || creditAccount.daysInArrears >= self.minLockedDaysInArrears
                })
                .zip(with: self.apiProvider.reactive.request(API.MayaCredit.getBillingStatement(loanAccountId)))
                .on(value: { [weak self] _, response in
                    if let self = self, let billingStatement = try? response.map(MayaCreditBillingStatement.self, using: MayaCreditBillingStatement.decoder) {
                        let billingStatementCell = MayaCreditBillingStatementCell(billingStatement, numberFormatter: self.formatter.number)
                        self.billingStatement = billingStatement
                        self.billingStatementState = .available(billingStatementCell)
                    } else {
                        if response.statusCode == APIConstants.HTTPStatusCode.contentNotFound.rawValue {
                            self?.billingStatementState = .unavailable
                        }
                    }
                })
                .map { $0.0 }
                .mapMayaError()
        }

        getMayaCreditConsents = Action { [weak self] _ in
            guard let self = self else {
                return SignalProducer(error: PayMayaError(viewModel: ErrorAlertViewModel(title: defaultErrorTitle)))
            }
            return self.apiProvider.reactive.request(API.MayaCredit.getConsents, type: [MayaCreditOptInConsent].self)
                .mapMayaError()
        }

        billingStatementUrlAction = Action { [weak self] request in
            guard let self = self, case let .available(cell) = self.billingStatementState, let loanAccountId = self.creditAccount.value?.id else { return SignalProducer<MayaCreditBillingStatementUrl, PayMayaError>(error: PayMayaError()) }
            let request = MayaCreditBillingStatementUrlRequest(id: loanAccountId, statementId: cell.statementId)
            return self.apiProvider.reactive.request(API.MayaCredit.getBillingStatementUrl(request), type: MayaCreditBillingStatementUrl.self)
                .mapMayaError()
        }

        getMayaCreditLoanApplication
            .isExecuting.signal
            .withLatest(from: pullToRefreshTriggered)
            .observeValues { [weak self] isExecuting, pullToRefreshTriggered in
                guard let self = self else { return }
                if case .booked = self.loanApplicationStatusProperty.value {
                    return
                }
                if isExecuting && !pullToRefreshTriggered {
                    self.items.value = [.loading]
                }
            }?.addToDisposeBag(disposeBag)

        getMayaCreditLoanApplication.values
            .observeValues { [weak self] loanApplication in
                guard let self = self,
                      let loanApplicationStatus = LoanApplicationStatus(status: loanApplication.status, loanAccountID: loanApplication.loanAccountId) else { return }
                self.loanApplicationStatusProperty.value = loanApplicationStatus
            }?.addToDisposeBag(disposeBag)

        getMayaCreditLoanApplication.errors
            .observeValues { [weak self] error in
                guard let self = self else { return }
                switch error.type {
                case .contentNotFound:
                    let loanApplicationStatus = LoanApplicationStatus(status: nil)
                    self.loanApplicationStatusProperty.value = loanApplicationStatus
                case .sessionTimeout:
                    return
                default:
                    self.items.value = [.error]
                }
            }?.addToDisposeBag(disposeBag)

        getMayaCreditLoanAccount.isExecuting.signal
            .withLatest(from: pullToRefreshTriggered)
            .observeValues { [weak self] isExecuting, pullToRefreshTriggered in
                guard let self = self else { return }
                if case .booked = self.loanApplicationStatusProperty.value {
                    return
                }
                if isExecuting && !pullToRefreshTriggered {
                    self.items.value = [.loading]
                }
            }?.addToDisposeBag(disposeBag)

        getMayaCreditLoanAccount.values
            .observeValues { [weak self] loanAccount in
                guard let self = self else { return }
                self.analyticsModule.value = Analytics.NewMayaCreditSummary()

                let accountStatus = LoanAccountStatus(rawValue: loanAccount.status) ?? LoanAccountStatus.active
                let creditDetailsCell: [MayaCreditCellType] = getCreditCellType(accountStatus)

                if self.configurationService.mayaCreditPartnerMerchantEnabled.value {
                    self.items.value = [.availableCredit, .partnerMerchant, .billingStatementRevamp, .transactions, .manageMyCredit, .contactInformation, .spacer]
                } else {
                    self.items.value = creditDetailsCell + [.transactions, .manageMyCredit, .contactInformation, .spacer]
                }
            }?.addToDisposeBag(disposeBag)

        getMayaCreditLoanAccount.errors
            .observeValues { [weak self] error in
                guard let self = self else { return }
                if error.type == .mcIneligible {
                    self.analyticsModule.value = Analytics.CreditNotEligible()
                    self.items.value = [.ineligible, .helpCenter, .logo, .contactInformation, .spacer]
                } else {
                    self.analyticsModule.value = Analytics.CreditSummary()
                    self.items.value = [.error]
                }
            }?.addToDisposeBag(disposeBag)

        loanApplicationStatusProperty.signal
            .observeValues { [weak self] loanApplicationStatus in
                guard let self = self, let status = loanApplicationStatus else { return }
                switch status {
                case .booked(let loanAccountID):
                    self.getMayaCreditLoanAccount.apply(loanAccountID).start()
                    self.getContactReference.apply().start()
                case .approved:
                    self.analyticsModule.value = Analytics.MayaCreditApprovedReview()
                    self.items.value = [.approved, .helpCenter, .logo, .contactInformation, .spacer]
                case .submitted:
                    self.analyticsModule.value = Analytics.CreditSubmittedReview()
                    self.items.value = [.submitted, .helpCenter, .logo, .contactInformation, .spacer]
                case .none:
                    self.items.value = [.initialEligibility, .helpCenter, .logo, .contactInformation, .spacer]
                    return
                }
            }?.addToDisposeBag(disposeBag)

        submitConsentAction = Action { [weak self] optInConsent in
            let optInConsent = optInConsent[0]
            guard let self = self else {
                return SignalProducer(error: PayMayaError(viewModel: ErrorAlertViewModel(title: L10n.Error.Maya.Credit.Optin.title)))
            }
            let request = MayaCreditSubmitConsentRequest(type: optInConsent.type, version: optInConsent.version, majorVersion: optInConsent.majorVersion)
            return self.apiProvider.reactive.request(API.MayaCredit.submitConsent([request]))
                .map({ _ in })
                .mapPayMayaError(title: L10n.Error.Maya.Credit.Optin.title)
        }

        getContactReference = Action { [weak self] in
            guard let self = self else {
                return SignalProducer(error: PayMayaError(viewModel: ErrorAlertViewModel(title: defaultErrorTitle)))
            }
            return self.apiProvider.reactive.request(API.MayaCredit.getContactReference, type: MayaCreditContactReference.self)
                .mapMayaError()
                .on(failed: { error in
                    if case .mayaCreditEmptyContactRef = error.type {
                        self.showContactReference.value = true
                    }
                })
        }

        getContactReference.values.observeValues { [weak self] value in
            guard let self = self else { return }
            self.showContactReference.value = value.contactReference == nil
        }

        getPersonDetails = Action { [weak self] in
            guard let self = self else {
                return SignalProducer(error: PayMayaError(viewModel: ErrorAlertViewModel(title: defaultErrorTitle)))
            }
            return self.apiProvider.reactive.request(API.MayaCredit.getPersonDetails, type: MayaCreditPersonDetails.self)
                .mapMayaError()
        }
    }

    func toggleHideBalance() {
        toggleBalanceShownService.toggle(for: .credit)
    }

    func buildPayCreditBillDeeplink() -> URL? {
        var url = Constants.DeepLinkPath.paybills.url
        guard let creditAccount = creditAccount.value,
              let biller = creditAccount.repaymentSettings?.biller, let billingStatement = billingStatement else { return url }

        url = url?
            .appendQueryItem(name: "account_number", value: creditAccount.promissoryNoteNumber)?
            .appendQueryItem(name: "biller", value: biller)?
            .appendQueryItem(name: "amount", value: String(billingStatement.totalAmountDue))
        return url
    }

    func buildPayEarlyCreditBillDeeplink() -> URL? {
        var url = Constants.DeepLinkPath.paybills.url
        guard let creditAccount = creditAccount.value,
              let biller = creditAccount.repaymentSettings?.biller else { return url }

        url = url?
            .appendQueryItem(name: "account_number", value: creditAccount.promissoryNoteNumber)?
            .appendQueryItem(name: "biller", value: biller)?
            .appendQueryItem(name: "amount", value: String(creditAccount.balances.total))
        return url
    }

    func buildPayNowDeeplink() -> URL? {
        var url = Constants.DeepLinkPath.paybills.url
        guard let creditAccount = creditAccount.value,
              let biller = creditAccount.repaymentSettings?.biller else { return url }

        url = url?
            .appendQueryItem(name: "account_number", value: creditAccount.promissoryNoteNumber)?
            .appendQueryItem(name: "biller", value: biller)?
            .appendQueryItem(name: "amount", value: String(creditAccount.dues.total))
        return url
    }

    func submitConsent(consents: [MayaCreditOptInConsent]) {
        submitConsentAction.apply(consents).start()
    }

    private func showFinancialValue(_ currency: String = Constants.Defaults.Common.peso.rawValue, value: String) -> String {
        return String(format: "%@%@", currency, getFinancialValue(value: value))
    }

    private func getFinancialValue(value: String) -> String {
        if isBalanceHiddenProperty.value {
            return Constants.Defaults.Common.hidenAmount.rawValue
        } else {
            return value
        }
    }

    private func getOverdueAlertMessage(account: MayaCreditAccount) -> String {
        let days = minLockedDaysInArrears - account.daysInArrears
        let amountDue = formatter.number.currencyStringValue(double: Double(account.dues.total))

        if days <= 1 {
            return L10n.Maya.Credit.Transfer.Alert.Spiel.Overdue.Tomorrow.message(amountDue)
        }

        return L10n.Maya.Credit.Transfer.Alert.Spiel.Overdue.message(days, amountDue)
    }

    func buildTransferAlertViewModel(account: MayaCreditAccount, url: URL) -> MayaAlertViewModel {
        var alertTitle = L10n.Maya.Credit.Transfer.Alert.Spiel.Overdue.title
        var alertMessage = getOverdueAlertMessage(account: account)
        var alertImage = CommonAsset.Images.Alert.image3DFailed.image

        if account.status == LoanAccountStatus.suspended.rawValue || account.daysInArrears >= minLockedDaysInArrears {
            if account.dues.total > 0 {
                let amountDue = formatter.number.currencyStringValue(double: Double(account.dues.total))

                alertTitle = L10n.Maya.Credit.Transfer.Alert.Spiel.Locked.title
                alertMessage = L10n.Maya.Credit.Transfer.Alert.Spiel.Locked.message(amountDue)
            } else {
                alertTitle = L10n.Maya.Credit.Transfer.Alert.Spiel.Locked.Active.title
                alertMessage = L10n.Maya.Credit.Transfer.Alert.Spiel.Locked.Active.message
                alertImage = CommonAsset.Images.Alert.image3DPending.image
            }
        }

        let alertViewModel = MayaAlertViewModel(title: alertTitle, message: alertMessage, image: alertImage)

        return alertViewModel
    }

    private func getCreditCellType(_ accountStatus: LoanAccountStatus) -> [MayaCreditCellType] {
        guard isWriteOffEnabled else { return [.availableCredit, .billingStatementRevamp] }

        switch accountStatus {
        case .active, .pastDue, .suspended:
            return [.availableCredit, .billingStatementRevamp]
        case .writeOff, .pendingWrittenOff:
            return [.writeOff, .billingStatementRevamp]
        case .writtenOff:
            return [.writtenOff]
        }
    }
}
