//
//  NewMayaCreditTransferFundsViewModel.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 13/04/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import AssetProvider
import ConfigurationService
import Error
import Foundation
import Injector
import MayaCoreData
import MayaFormatterManager
import RavenLocation
import ReactiveSwift
import StoreProvider

private let errorTitle = CommonStrings.Error.Default.title

class NewMayaCreditTransferFundsViewModel: ViewModel, NewMayaCreditTransferFundsViewModelProtocol, FormatterProtocol {
    @Inject private var apiProvider: APIProvider
    @Inject private var storeProvider: StoreProvider
    @Inject var formatter: MayaFormatterManager
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2
    @Inject private var ravenActionProvider: RavenActionProvider

    let isExceedsCashInLimit = MutableProperty(false)
    let isExceedsAvailableCreditLimit = MutableProperty(false)
    let isCashInLimitHidden = MutableProperty(true)
    let inlineValidationMessage: MutableProperty<String?> = MutableProperty(nil)

    let creditAccount: MayaCreditAccount
    var amountProperty: NewFieldProperty!
    var createTransferAction: Action<Void, String, PayMayaError>!
    var getAccountLimitsAction: Action<Void, Void, PayMayaError>!
    var calculateDisbursementAction: Action<(String, String), MayaCreditFeesAndTaxes, PayMayaError>!
    var handleRavenGetGeolocation: Action<Void, RavenLocation.Location?, RavenLocation.Error>!
    var handleRavenExtract: Action<Void, Void, PayMayaError>!

    let isContinueButtonEnabled = MutableProperty(false)

    private var isInitialState = true
    private var cashInLimitDoubleValue: Double?
    private let disposeBag = CompositeDisposable()
    private let amountInputState: MutableProperty<NewMayaCreditTransferFundsAmountInputState> = MutableProperty(.valid)

    var cashInLimitValue: String? {
        guard let cashIn = cashInLimitDoubleValue else { return nil }
        let cashInLimit = self.formatter.number.currencyStringValue(double: cashIn)
        return "\(Constants.Defaults.Common.peso.rawValue) \(cashInLimit)"
    }

    var availableCreditLimitValue: String {
        let creditLimit = creditAccount.loanAmount - creditAccount.balances.principal
        return "\(Constants.Defaults.Common.peso.rawValue)\(formatter.number.currencyStringValue(double: Double(creditLimit)))"
    }

    var accountNumberLastFourDigits: String {
        let digitsLength = 4
        return String(creditAccount.promissoryNoteNumber.suffix(digitsLength))
    }

    var isLendingDataExtractionEnabled: Bool {
        configurationServiceV2.lendingDataExtractionEnabled.value
    }

    var isGeolocationEnabled: Bool {
        configurationServiceV2.mecGeolocationEnabled.value
    }

    init(creditAccount: MayaCreditAccount) {
        self.creditAccount = creditAccount
        super.init()

        amountInputState.signal.observeValues({ [weak self] state in
            guard let self = self else { return }
            self.isInitialState = false
            self.inlineValidationMessage.value = state.validationMessage
            // Resets the state to default
            self.isExceedsAvailableCreditLimit.value = false
            self.isExceedsCashInLimit.value = false

            switch state {
            case .valid:
                self.isContinueButtonEnabled.value = true
            case .amountEmpty:
                self.isContinueButtonEnabled.value = false
            case .maximumCreditLimitReached:
                self.isExceedsAvailableCreditLimit.value = true
                self.isContinueButtonEnabled.value = false
            case .availableCreditLimitExceeds:
                self.isExceedsAvailableCreditLimit.value = true
                self.isContinueButtonEnabled.value = false
                self.isContinueButtonEnabled.value = false
            case .cashInLimitExceeds:
                self.isExceedsCashInLimit.value = true
                self.isContinueButtonEnabled.value = false
            case .availableAndCashInLimitsExceed:
                self.isExceedsAvailableCreditLimit.value = true
                self.isExceedsCashInLimit.value = true
                self.isContinueButtonEnabled.value = false
            }
        })?.addToDisposeBag(disposeBag)

        let isCreditLimitReachedMaximum: () -> Bool = { [weak self] in
            guard let self = self else { return false }
            if creditAccount.loanAmount - creditAccount.balances.principal <= 0 {
                self.amountInputState.value = .maximumCreditLimitReached
                return true
            }
            return false
        }
        _ = isCreditLimitReachedMaximum()

        let amountIsEmpty: (String) -> Bool = { [weak self] amount in
            guard let self = self else { return true }
            if !self.isInitialState && (amount.isEmpty || (self.formatter.number.currencyDoubleValue(string: amount) <= 0)) {
                self.amountInputState.value = .amountEmpty
                return true
            }
            return false
        }

        let validateCreditLimitReachedAndAmountEmpty: (String) -> Bool = { amountValue in
            if isCreditLimitReachedMaximum() || amountIsEmpty(amountValue) {
                return true
            }
            return false
        }

        let inlineValidator: ((String) -> ValidatingProperty<String, FieldValidationError>.Decision)? = { [weak self] amountValue in
            guard let self = self else { return .valid }
            let amount = self.formatter.number.currencyDoubleValue(string: amountValue)
            let availableCreditLimit = Double(creditAccount.loanAmount - creditAccount.balances.principal)
            if validateCreditLimitReachedAndAmountEmpty(amountValue) {
                // Do nothing
            } else if let cashInLimit = self.cashInLimitDoubleValue {
                if amount > cashInLimit, amount > availableCreditLimit {
                    self.amountInputState.value = .availableAndCashInLimitsExceed
                } else if amount > cashInLimit {
                    self.amountInputState.value = .cashInLimitExceeds
                } else if amount > availableCreditLimit {
                    self.amountInputState.value = .availableCreditLimitExceeds
                } else {
                    self.amountInputState.value = .valid
                }
            } else if amount > availableCreditLimit {
                self.amountInputState.value = .availableCreditLimitExceeds
            } else {
                self.amountInputState.value = .valid
            }

            return self.amountInputState.value.isValid ? .valid : .invalid(FieldValidationError(message: self.amountInputState.value.validationMessage ?? ""))
        }
        amountProperty = NewFieldProperty(inlineValidator: inlineValidator)

        createTransferAction = Action { [weak self] in
            guard let self = self else { return PayMayaError.validationError(title: errorTitle) }

            self.isInitialState = false
            _ = validateCreditLimitReachedAndAmountEmpty(self.amountProperty.value ?? "")
            guard self.amountInputState.value.isValid else { return PayMayaError.validationError(title: errorTitle) }

            return SignalProducer<String, PayMayaError>(value: UUID().uuidString)
        }

        getAccountLimitsAction = Action { [weak self] in
            guard let self = self else { return PayMayaError.validationError(title: errorTitle) }
            return self.apiProvider.reactive.request(API.AccountLimits.getAccountLimits, type: AccountLimitList.self)
                .mapMayaError(title: errorTitle)
                .map({ [weak self] accountLimitList in
                    guard let self = self, let monthly = accountLimitList.monthly.first else { return }
                    if let limit = monthly.amountLimit {
                        self.cashInLimitDoubleValue = Double(limit.remaining)
                        self.isCashInLimitHidden.value = false
                    }
                    return
                })
        }

        calculateDisbursementAction = Action { [weak self] rrn, amount in
            guard let self = self,
                  let databaseStore = self.storeProvider.target(DatabaseStore.self),
                  let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                  let customerId = user.walletId
            else { return PayMayaError.validationError(title: errorTitle) }

            let amountValue = self.formatter.number.currencyDoubleValue(string: amount)
            let amountRequest = AmountRequest(currency: Constants.Defaults.Common.currency.rawValue, value: amountValue)
            let request = MayaCreditCalculateDisbursementRequest(customerId: customerId, loanAccountId: creditAccount.id, amount: amountRequest)

            return self.apiProvider.reactive.request(API.MayaCredit.calculateDisbursement(rrn, request), type: MayaCreditFeesAndTaxes.self)
                .mapMayaError(title: errorTitle)
        }

        handleRavenGetGeolocation = ravenActionProvider.makeGetGeolocationAction()
        handleRavenExtract = ravenActionProvider.makeExtractAction()
    }
}
