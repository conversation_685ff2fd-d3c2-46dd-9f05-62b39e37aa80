//
//  NewMayaCreditActivationViewModel.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 14/04/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import AssetProvider
import ConfigurationService
import Error
import Foundation
import Injector
import MayaCoreData
import MayaFormatterManager
import RavenLocation
import ReactiveSwift
import ShieldFraud
import StoreProvider
import UIKit

private let errorTitle = CommonStrings.Maya.Error.Default.title

enum EmailIdentityStatus {
    case verified
    case unverified
    case noEmail

    var title: String {
        switch self {
        case .verified:
            return L10n.Maya.New.Credit.Activation.IdentityStatus.verified
        case .unverified:
            return L10n.Maya.New.Credit.Activation.IdentityStatus.unverified
        case .noEmail:
            return L10n.Maya.New.Credit.Activation.IdentityStatus.noEmail
        }
    }

    var backgroundColor: UIColor {
        switch self {
        case .verified:
            return CommonAsset.MayaColors.Primary.primaryGrownGreen.color
        case .unverified, .noEmail:
            return CommonAsset.MayaColors.Grey.grey4.color
        }
    }
}

private enum AgreementsKeys: String {
    case termsAndConditions
    case truthAndTransparency
    case disclosureStatement

    var order: Int {
        switch self {
        case .termsAndConditions:
            return 0
        case .truthAndTransparency:
            return 1
        case .disclosureStatement:
            return 2
        }
    }
}

final class NewMayaCreditActivationViewModel: MayaUpdateProfileBaseViewModel, NewMayaCreditActivationViewModelProtocol {
    @Inject private var apiProvider: APIProvider!
    @Inject private var storeProvider: StoreProvider!
    @Inject private var formatter: MayaFormatterManager!
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2
    @Inject private var ravenActionProvider: RavenActionProvider

    // For Signup with contact reference
    let assignContactReferenceViewModel = MayaCreditAssignContactReferenceViewModel()

    private var isSubmittedOnce = false
    private var selectedTermKeys = Set<String>()
    private let disposeBag = CompositeDisposable()
    private var selectedBillingEndDate: Int?
    private var creditAccount: MayaCreditAccount?
    private var eligibilityTerm: MutableProperty<MayaCreditEligibility.Term?> = MutableProperty<MayaCreditEligibility.Term?>(nil)
    private let scheduleSettings: MayaCreditScheduleSettings?
    internal let riskBasedPricingRate: Float?

    private let mobileNumberLength = 11
    private let allowedMobilePrefix = "09"
    private let mothersMaidenNameMinLength = 2
    private let mothersMaidenNameMaxLength = 50
    private let mothersMaidenNameRagex = "^[a-zA-ZñÑ -]*$"

    var amountWithCurrency: String {
        guard let eligibilityTerm = eligibilityTerm.value else {
            return ""
        }
        let amount = formatter.number.currencyStringValue(double: eligibilityTerm.loanAmount, removeTrailingDecimals: eligibilityTerm.loanAmount.truncatingRemainder(dividingBy: 1.0) == 0)
        return "\(Constants.Defaults.Common.peso.rawValue)\(amount)"
    }

    var expiry: String {
        guard let eligibilityTerm = eligibilityTerm.value else {
            return ""
        }
        let dateFormatter = DateFormatters.getDateFormatter(for: .custom(dateFormat: "MMMM d, yyyy 'at' h:mm a"))
        return dateFormatter.string(from: eligibilityTerm.expiresOn)
    }

    var feeRate: String {
        guard let eligibilityTerm = eligibilityTerm.value, let feeRate = eligibilityTerm.feeRate else {
            return ""
        }
        let interests = (feeRate * 100)
        return interests.truncatingRemainder(dividingBy: 1) == 0
            ? String(Int(interests))
            : String(interests)
    }

    var shouldInitializeDeviceScoring: Bool {
        guard !riskLevel.isEmpty, configurationServiceV2.mecDeviceScoringEnabled.value else { return false }
        let isRiskLevelLow = riskLevel == Constants.RiskLevel.low.rawValue
        return !isRiskLevelLow && !isCreditScoringDataValid
    }

    var riskLevel: String

    var isCreditScoringDataValid: Bool

    var isLendingDataExtractionEnabled: Bool {
        configurationServiceV2.lendingDataExtractionEnabled.value
    }

    var isGeolocationEnabled: Bool {
        configurationServiceV2.mecGeolocationEnabled.value
    }

    var activationTerms: MutableProperty<[ActivationTerm]> = MutableProperty<[ActivationTerm]>([])
    var backupIdentity = ""
    var backupIdentityInfo = ""
    var backupEmailStatus: EmailIdentityStatus = .noEmail

    let isUpdateButtonEnabled = MutableProperty(false)

    let isButtonEnabled = MutableProperty(false)
    private let agreementsAreChecked = MutableProperty(false)

    var submitLoanAction: Action<Void, OTPResponse, PayMayaError>!
    let billingEndDate: MutableProperty<String> = MutableProperty("")

    var emailField: NewFieldProperty!
    var alternativeMobileNumberField: NewFieldProperty!
    let genderField = NewFieldProperty()
    let maritalStatusField = NewFieldProperty()
    var mothersMaidenFirstNameField: NewFieldProperty!
    var mothersMaidenMiddleNameField: NewFieldProperty!
    var mothersMaidenLastNameField: NewFieldProperty!

    var selectedGender: PersonalDetails.Gender?
    var selectedMaritalStatus: PersonalDetails.MaritalStatus?
    var isNoMaidenMiddleNameChecked: MutableProperty<Bool> = MutableProperty(false)

    var getContactReference: Action<Void, MayaCreditContactReference, PayMayaError>!
    var submitContactReference: Action<Void, MayaCreditContactReferencePatchResponse, PayMayaError>!
    var getMayaCreditFullEligibility: Action<(String, String?, MayaCreditEligibility?), MayaCreditEligibility, PayMayaError>!
    var handleRavenGetGeolocation: Action<Void, RavenLocation.Location?, RavenLocation.Error>!
    var handleRavenExtract: Action<Void, Void, PayMayaError>!

    var getPersonDetails: Action<Void, MayaCreditPersonDetails, PayMayaError>!
    var submitPersonDetails: Action<Void, MayaCreditPersonDetailsPatchResponse, PayMayaError>!

    var personalDetailsState: PersonalDetailsState?
    var device: EligibilityRequest.Device

    init(eligibilityTerm: MayaCreditEligibility.Term?, initialEligibility: MayaCreditInitialEligibility? = nil, feeRate: Float?, creditAccount: MayaCreditAccount? = nil, personalDetailsState: PersonalDetailsState? = nil, deviceId: String = Shield.shared().sessionId, osVersion: String = UIDevice.current.systemVersion) {
        MayaCreditTermDetailsViewModel.dynamicInstanceCount = eligibilityTerm?.agreements.count ?? 0
        self.eligibilityTerm.value = eligibilityTerm
        self.scheduleSettings = eligibilityTerm?.scheduleSettings ?? initialEligibility?.term.scheduleSettings
        self.riskBasedPricingRate = feeRate
        self.creditAccount = creditAccount
        self.personalDetailsState = personalDetailsState
        self.riskLevel = initialEligibility?.riskLevel ?? String.empty
        self.isCreditScoringDataValid = initialEligibility?.isCreditScoringDataValid ?? false
        let agreements = eligibilityTerm?.agreements.sorted { agreement1, agreement2 in
            guard let key1 = AgreementsKeys(rawValue: agreement1.key), let key2 = AgreementsKeys(rawValue: agreement2.key) else { return false }
            return key1.order < key2.order
        } ?? []

        activationTerms.value = agreements.map({ ActivationTerm(term: $0, viewModel: MayaCreditTermDetailsViewModel(agreement: $0)) })
        self.device = EligibilityRequest.Device(id: deviceId, channel: DeviceType.kind, version: osVersion)
        super.init(data: [ProfileParameterKeys.payload: []])

        // Email validation
        let emailInlineValidator: ((String) -> ValidatingProperty<String, FieldValidationError>.Decision) = { [weak self] text in
            let emailPredicate = NSPredicate(format: "name == %@", Constants.Keys.AccountRecovery.email.rawValue)
            guard !text.isEmpty,
                let databaseStore = self?.storeProvider.target(DatabaseStore.self),
                let emailFieldInfo = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.fieldInformation, predicate: emailPredicate)).value as? FieldInformation,
                let emailRegex = emailFieldInfo.value else {
                    return .valid
            }
            return text.matchRegex(emailRegex) ? .valid : .invalid(FieldValidationError(message: L10n.Maya.Credit.Signup.EmailVerification.Invalid.email))
        }
        emailField = NewFieldProperty(validationMessage: L10n.Field.Error.required(L10n.Field.Name.email), inlineValidator: emailInlineValidator)

        // Mobile number validation
        let mobileNumberValidator: ((String) -> ValidatingProperty<String, FieldValidationError>.Decision) = { [weak self] text in
            guard self != nil else { return .valid }
            let textValue = text.removeAllWhitespaces()
            let numberRegex = "^(09)\\d{9}$"
            let databaseStore = self?.storeProvider.target(DatabaseStore.self)

            if  let user = databaseStore?.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User, let userNumber = user.msisdn, textValue == userNumber.formatMobileNumberToLocal() {
                return .invalid(FieldValidationError(message: L10n.Maya.Credit.Signup.MobileVerification.Invalid.current))
            }

            return text.isEmpty || textValue.matchRegex(numberRegex) ? .valid : .invalid(FieldValidationError(message: L10n.Maya.Credit.Signup.MobileVerification.Invalid.email))
        }
        alternativeMobileNumberField = NewFieldProperty(inlineValidator: mobileNumberValidator)

        let mothersNameValidator = getIsRequiredValidator(
            minimumLength: mothersMaidenNameMinLength,
            validationErrorMessage: L10n.Maya.Credit.Signup.NameVerification.Invalid.Length.minimum,
            wrappe: getRegexValidator(
                pattern: mothersMaidenNameRagex,
                validationErrorMessage: L10n.Maya.Credit.Signup.NameVerification.Invalid.characters,
                wrappe: getConsecutiveCharactersValidator(
                    validationErrorMessage: L10n.Maya.Credit.Signup.NameVerification.Invalid.Characters.repeated,
                    wrappe: getLimitedCharactersValidator(
                        characterLimit: mothersMaidenNameMaxLength,
                        validationErrorMessage: L10n.Maya.Credit.Signup.NameVerification.Invalid.Length.maximum
                    )
                )
            )
        )

        let middleNameValidator: ((String) -> ValidatingProperty<String, FieldValidationError>.Decision) = { [weak self] text in
            guard let self = self, !self.isNoMaidenMiddleNameChecked.value else { return .valid }

            return mothersNameValidator(text)
        }

        mothersMaidenFirstNameField = NewFieldProperty(inlineValidator: mothersNameValidator)
        mothersMaidenMiddleNameField = NewFieldProperty(inlineValidator: middleNameValidator)
        mothersMaidenLastNameField = NewFieldProperty(inlineValidator: mothersNameValidator)

        submitLoanAction = Action { [weak self] in
            guard let self = self, let eligibilityTerm = self.eligibilityTerm.value else {
                return PayMayaError.validationError(title: errorTitle)
            }

            let selectedTermKeys = Array(self.selectedTermKeys)
            self.isSubmittedOnce = true
            self.setBillingEndDate(self.selectedBillingEndDate)

            guard let billingEndDate = self.selectedBillingEndDate, !selectedTermKeys.isEmpty else {
                return PayMayaError.validationError(title: errorTitle)
            }

            let term = MayaCreditSubmitLoanRequest.Term(id: eligibilityTerm.id, userPreference: MayaCreditSubmitLoanRequest.Term.UserPreference(periodEndDayOfMonth: billingEndDate, loanAmount: eligibilityTerm.loanAmount))
            let request = MayaCreditSubmitLoanRequest(term: term, agreements: Array(self.selectedTermKeys))
            return self.apiProvider.reactive.request(API.MayaCredit.submitLoanApplication(request), type: OTPResponse.self)
                .mapMayaError(title: errorTitle)
        }

        getMayaCreditFullEligibility = Action { [weak self] rrn, dataReferenceId, creditEligibility in
            guard let self = self else { return SignalProducer<MayaCreditEligibility, PayMayaError>(error: PayMayaError()) }

            if let mayaCreditEligibility = creditEligibility {
                return SignalProducer(value: mayaCreditEligibility)
            } else {
                self.device.dataReferenceId = dataReferenceId
                self.device.isCreditScoringEnabled = configurationServiceV2.mecDeviceScoringEnabled.value
                let eligibilityRequest = EligibilityRequest(requestReferenceNumber: rrn, productKey: Constants.Defaults.Common.mayaCreditProductKey.rawValue, device: self.device)

                return self.apiProvider.reactive.request(API.MayaCredit.loanApplicationEligibility(eligibilityRequest), type: MayaCreditEligibility.self, decoder: MayaCreditEligibility.decoder).mapMayaError()
            }
        }

        getMayaCreditFullEligibility.values.observeValues { [weak self] eligibility in
            guard let self = self else { return }
            self.eligibilityTerm.value = eligibility.term
            let agreements = self.eligibilityTerm.value?.agreements.sorted { agreement1, agreement2 in
                guard let key1 = AgreementsKeys(rawValue: agreement1.key), let key2 = AgreementsKeys(rawValue: agreement2.key) else { return false }
                return key1.order < key2.order
            } ?? []

            self.activationTerms.value = agreements.map({ ActivationTerm(term: $0, viewModel: MayaCreditTermDetailsViewModel(agreement: $0)) })
            self.setSelectedTerms()
            self.setAcceptTerms()
        }

        handleRavenGetGeolocation = ravenActionProvider.makeGetGeolocationAction()
        handleRavenExtract = ravenActionProvider.makeExtractAction()

        if let databaseStore = storeProvider.target(DatabaseStore.self),
           let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
           let backupIdentity = user.backupIdentity {
            self.backupIdentity = backupIdentity.value
            let isVerified = backupIdentity.isVerified?.boolValue ?? false
            backupEmailStatus = isVerified ? .verified : .unverified
            backupIdentityInfo = isVerified
            ? L10n.Maya.New.Credit.Activation.Verified.email
            : L10n.Maya.New.Credit.Activation.NotVerified.email

            emailField.mutableProperty.value = self.backupIdentity
        } else {
            backupIdentityInfo = L10n.Maya.New.Credit.Activation.No.email
            backupEmailStatus = .noEmail
        }

        setSelectedTerms()

        var producers = activationTerms.value.map { $0.viewModel.isConfirmed.producer }
        producers.append( billingEndDate.producer.map { !$0.isEmpty })
        var updateProducers = [genderField.mutableProperty.producer.map { !$0.trim().isEmpty }]

        // required fields on contact reference flow
        producers.append(emailField.mutableProperty.producer.map { !$0.trim().isEmpty })
        producers.append(genderField.mutableProperty.producer.map { !$0.trim().isEmpty })
        producers.append(maritalStatusField.mutableProperty.producer.map { !$0.trim().isEmpty })
        producers.append(alternativeMobileNumberField.mutableProperty.producer.map { !$0.trim().isEmpty || $0.trimmingCharacters(in: .whitespacesAndNewlines).removeAllWhitespaces().count == self.mobileNumberLength })
        updateProducers.append(maritalStatusField.mutableProperty.producer.map { !$0.trim().isEmpty })
        updateProducers.append(alternativeMobileNumberField.mutableProperty.producer.map { !$0.trim().isEmpty || $0.trimmingCharacters(in: .whitespacesAndNewlines).removeAllWhitespaces().count == self.mobileNumberLength })

        // additional required fields for mother's maiden name
        producers.append(mothersMaidenFirstNameField.mutableProperty.producer.map { !$0.trim().isEmpty && self.mothersMaidenFirstNameField.isValid })
        producers.append(mothersMaidenLastNameField.mutableProperty.producer.map { !$0.trim().isEmpty && self.mothersMaidenLastNameField.isValid })
        producers.append(
            SignalProducer.combineLatest(
                    mothersMaidenMiddleNameField.mutableProperty.producer,
                    isNoMaidenMiddleNameChecked.producer
                ).map { text, isNoMaidenMiddleNameChecked in
                    if isNoMaidenMiddleNameChecked {
                        return true
                    }
                    return !text.trim().isEmpty && self.mothersMaidenMiddleNameField.isValid
            }
        )

        getContactReference = Action { [weak self] in
            guard let self = self else {
                return SignalProducer(error: PayMayaError(viewModel: ErrorAlertViewModel(title: defaultErrorTitle)))
            }
            return self.apiProvider.reactive.request(API.MayaCredit.getContactReference, type: MayaCreditContactReference.self)
                .mapMayaError()
        }

        getContactReference.values.observeValues { [weak self] contactReference in
            guard let self = self else { return }
            self.selectedGender = PersonalDetails.Gender(rawValue: contactReference.gender)
            self.selectedMaritalStatus = PersonalDetails.MaritalStatus(rawValue: contactReference.maritalStatus)

            self.alternativeMobileNumberField.mutableProperty.value = (contactReference.alternativeMobileNumber ?? "").formatMobileNumberToLocal()
            self.genderField.mutableProperty.value = self.selectedGender?.value ?? ""
            self.maritalStatusField.mutableProperty.value = self.selectedMaritalStatus?.value ?? ""

            guard let assignedContactRef = contactReference.contactReference else { return }
            self.assignContactReferenceViewModel.selectedContactRelationship = PersonalDetails.ContactRelationship(rawValue: assignedContactRef.relationship)
            self.assignContactReferenceViewModel.firstNameField.mutableProperty.value = assignedContactRef.firstName
            self.assignContactReferenceViewModel.lastNameField.mutableProperty.value = assignedContactRef.lastName
            self.assignContactReferenceViewModel.contactRelationship.value = self.assignContactReferenceViewModel.selectedContactRelationship?.value ?? ""
            self.assignContactReferenceViewModel.mobileNumber.value = assignedContactRef.mobileNumber
        }?.addToDisposeBag(disposeBag)

        getPersonDetails = Action { [weak self] in
            guard let self = self else {
                return SignalProducer(error: PayMayaError(viewModel: ErrorAlertViewModel(title: defaultErrorTitle)))
            }
            return self.apiProvider.reactive.request(API.MayaCredit.getPersonDetails, type: MayaCreditPersonDetails.self)
                .mapMayaError()
        }

        getPersonDetails.values.observeValues { [weak self] personDetails in
            guard let self = self else { return }

            selectedGender = PersonalDetails.Gender(rawValue: String.unwrappedValue(personDetails.gender))
            selectedMaritalStatus = PersonalDetails.MaritalStatus(rawValue: String.unwrappedValue(personDetails.maritalStatus))

            alternativeMobileNumberField.mutableProperty.value = (String.unwrappedValue(personDetails.alternativeMobileNumber)).formatMobileNumberToLocal()
            genderField.mutableProperty.value = String.unwrappedValue(selectedGender?.value)
            maritalStatusField.mutableProperty.value = String.unwrappedValue(selectedMaritalStatus?.value)

            if let assignedContactRef = personDetails.contactReference {
                assignContactReferenceViewModel.selectedContactRelationship = PersonalDetails.ContactRelationship(rawValue: String.unwrappedValue(assignedContactRef.relationship))
                assignContactReferenceViewModel.firstNameField.mutableProperty.value = String.unwrappedValue(assignedContactRef.firstName)
                assignContactReferenceViewModel.lastNameField.mutableProperty.value = String.unwrappedValue(assignedContactRef.lastName)
                assignContactReferenceViewModel.contactRelationship.value = String.unwrappedValue(assignContactReferenceViewModel.selectedContactRelationship?.value)
                assignContactReferenceViewModel.mobileNumber.value = String.unwrappedValue(assignedContactRef.mobileNumber)
            }

            if let mothersMaidenName = personDetails.mothersMaidenName {
                mothersMaidenFirstNameField.mutableProperty.value = String.unwrappedValue(mothersMaidenName.firstName)
                mothersMaidenLastNameField.mutableProperty.value = String.unwrappedValue(mothersMaidenName.lastName)
                isNoMaidenMiddleNameChecked.value = mothersMaidenName.hasNoMiddleName ?? false
                mothersMaidenMiddleNameField.mutableProperty.value = String.unwrappedValue(mothersMaidenName.middleName)
            }
        }?.addToDisposeBag(disposeBag)

        submitContactReference = Action { [weak self] in
            guard let self = self,
                  let gender = self.selectedGender,
                  let maritalStatus = self.selectedMaritalStatus,
                  let relationship = self.assignContactReferenceViewModel.selectedContactRelationship else {
                return SignalProducer(error: PayMayaError(viewModel: ErrorAlertViewModel(title: defaultErrorTitle)))
            }

            let alternativeMobileNumber = String.unwrappedValue(self.alternativeMobileNumberField.mutableProperty.value).formatMobileNumberToLocal()
            let firstName = String.unwrappedValue(self.assignContactReferenceViewModel.firstNameField.mutableProperty.value).trim()
            let lastName = String.unwrappedValue(self.assignContactReferenceViewModel.lastNameField.value).trim()
            let mobileNumber = String.unwrappedValue(self.assignContactReferenceViewModel.mobileNumber.value)
            let contactReference = MayaCreditSubmitContactReferenceRequest.ContactReference(firstName: firstName, lastName: lastName, relationship: relationship.rawValue, mobileNumber: mobileNumber.formatMobileNumberToLocal())

            let request = MayaCreditSubmitContactReferenceRequest(alternativeMobileNumber: alternativeMobileNumber.isEmpty ? nil : alternativeMobileNumber, gender: gender.rawValue, maritalStatus: maritalStatus.rawValue, contactReference: contactReference)
            return self.apiProvider.reactive.request(API.MayaCredit.submitContactReference(request), type: MayaCreditContactReferencePatchResponse.self)
                .mapMayaError()
        }

        submitPersonDetails = Action { [weak self] in
            guard let self = self,
                  let gender = self.selectedGender,
                  let maritalStatus = self.selectedMaritalStatus,
                  let relationship = self.assignContactReferenceViewModel.selectedContactRelationship else {
                return SignalProducer(error: PayMayaError(viewModel: ErrorAlertViewModel(title: defaultErrorTitle)))
            }

            let alternativeMobileNumber = String.unwrappedValue(alternativeMobileNumberField.mutableProperty.value).formatMobileNumberToLocal()
            let firstName = String.unwrappedValue(assignContactReferenceViewModel.firstNameField.mutableProperty.value).trim()
            let lastName = String.unwrappedValue(assignContactReferenceViewModel.lastNameField.value).trim()
            let mobileNumber = String.unwrappedValue(assignContactReferenceViewModel.mobileNumber.value)
            let contactReference = MayaCreditPersonDetails.ContactReference(firstName: firstName, lastName: lastName, relationship: relationship.rawValue, mobileNumber: mobileNumber.formatMobileNumberToLocal())

            let maidenFirstName = String.unwrappedValue(mothersMaidenFirstNameField.mutableProperty.value).trim()
            let maidenMiddleName = String.unwrappedValue(mothersMaidenMiddleNameField.mutableProperty.value).trim()
            let maidenLastName = String.unwrappedValue(mothersMaidenLastNameField.mutableProperty.value).trim()
            let mothersMaidenName = MayaCreditPersonDetails.MothersMaidenName(firstName: maidenFirstName, middleName: maidenMiddleName, lastName: maidenLastName, hasNoMiddleName: isNoMaidenMiddleNameChecked.value)

            let request = MayaCreditPersonDetails(alternativeMobileNumber: alternativeMobileNumber.isEmpty ? nil : alternativeMobileNumber, gender: gender.rawValue, maritalStatus: maritalStatus.rawValue, contactReference: contactReference, mothersMaidenName: mothersMaidenName)
            return self.apiProvider.reactive.request(API.MayaCredit.submitPersonDetails(request), type: MayaCreditPersonDetailsPatchResponse.self)
                .mapMayaError()
        }

        if creditAccount != nil {
            self.setBillingEndDate(creditAccount?.scheduleSettings?.userPreference.periodEndDayOfMonth)
        }

        SignalProducer.combineLatest(producers)
            .observe(on: UIScheduler())
            .startWithValues { [weak self] values in
                let isEmailValid = self?.emailField.isValid ?? false
                let isAlternativeNumberValid = self?.alternativeMobileNumberField.isValid ?? false
                self?.isButtonEnabled.value = values.allSatisfy { $0 == true } && isEmailValid && isAlternativeNumberValid
            }
            .addToDisposeBag(disposeBag)

        SignalProducer.combineLatest(updateProducers)
            .observe(on: UIScheduler())
            .startWithValues { [weak self] values in
                let isAlternativeNumberValid = self?.alternativeMobileNumberField.isValid ?? false
                self?.isUpdateButtonEnabled.value = values.allSatisfy { $0 == true } && isAlternativeNumberValid
            }
            .addToDisposeBag(disposeBag)
    }

    func getBillingEndDateSelection() -> BillingEndDateSelection? {
        guard let scheduleSettings = scheduleSettings else { return nil }
        return BillingEndDateSelection(currentValue: selectedBillingEndDate, minValue: scheduleSettings.billingSettings.periodEndDayOfMonth.minValue, maxValue: scheduleSettings.billingSettings.periodEndDayOfMonth.maxValue, days: scheduleSettings.billingSettings.periodEndDayOfMonth.days, onSelect: { [weak self] selected in
            self?.setBillingEndDate(selected)
        })
    }

    func setAcceptTerms() {
        for activationTerm in activationTerms.value {
            activationTerm.viewModel.isConfirmed.value = true
        }
    }

    private func setBillingEndDate(_ value: Int?) {
        selectedBillingEndDate = value
        guard let endDate = selectedBillingEndDate else { return }
        billingEndDate.value = endDate.ordinal
    }

    private func setSelectedTerms() {
        for activationTerm in activationTerms.value {
            activationTerm.viewModel.isConfirmed.signal.observeValues({ [weak self, weak activationTerm] isSelected in
                guard let self = self, let activationTerm = activationTerm else { return }
                self.selectActivationTerm(activationTerm, isSelected: isSelected)
            })?.addToDisposeBag(disposeBag)
        }
    }

    private func selectActivationTerm(_ activationTerm: ActivationTerm, isSelected: Bool) {
        if isSelected {
            selectedTermKeys.insert(activationTerm.term.key)
        } else {
            selectedTermKeys.remove(activationTerm.term.key)
        }
    }

    func getPersonalDetailsSelection() -> PersonalDetailsSelection {
        return PersonalDetailsSelection(gender: .male, maritalStatus: .single, onSelectGender: { [weak self] selectedGender in
            self?.selectedGender = selectedGender
            self?.genderField.mutableProperty.value = selectedGender.value
        }, onSelectMaritalStatus: { [weak self] selectedStatus in
            self?.selectedMaritalStatus = selectedStatus
            self?.maritalStatusField.mutableProperty.value = selectedStatus.value
        }, onSelectContactRelationship: { _ in }, onSelectContactNumber: { _ in })
    }

    func formatAlternativeNumber(with text: String, and replacement: String) -> String? {
        var text = text

        if text.count == 4 || text.count == 8 {
            if replacement.isEmpty {
                text = String(text.trim().dropLast())
            } else {
                text += " "
            }
            return text
        }

        return nil
    }
}
