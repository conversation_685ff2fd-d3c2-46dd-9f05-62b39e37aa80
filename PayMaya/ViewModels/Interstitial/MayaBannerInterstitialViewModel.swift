//
//  MayaBannerInterstitialViewModel.swift
//  PayMaya
//
//  Created by <PERSON> on 6/5/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Foundation
import Injector
import StoreProvider
import UIKit

class MayaBannerInterstitialViewModel: MayaBannerInterstitialViewModelProtocol {
    @Inject private var storeProvider: StoreProvider

    let interstitialShownStoreID: UserDefaultsStoreId?

    let navigationTitle: String?
    let iconImage: ImageAsset
    let title: String
    let messages: [MayaInterstitialMessage]
    let footerText: NSAttributedString?

    let buttonText: String
    let isButtonSticky: Bool

    let analyticsModule: AnalyticsModule
    let tapContinueModule: AnalyticsModule
    let tapBackModule: AnalyticsModule
    let tapURLModules: [URL: AnalyticsModule]

    let analyticsAttributes: [AnalyticsAttributeKey: Any]
    let appearAnalyticsAttributes: [AnalyticsAttributeKey: Any]

    let adCarouselViewModel: AdCarouselViewModel = {
        let adHeight = AdBannerProperties.dynamicHeight(desiredWidth: 327, desiredHeight: 245)
        return AdCarouselViewModel(adWidth: AdBannerProperties.dynamicWidth, adHeight: adHeight, module: .creatorStoreInterstitial)
    }()

    init?(model: BannerInterstitialModel) {
        guard model.hasInterstitial,
            model.doesInterstitialHaveBanners,
            let iconImage = model.interstitialImage,
            let analyticsModule = model.interstitialScreenModule,
            let tapContinueModule = model.bannerTapIntestitialContinueModule,
            let tapBackModule = model.tapInterstitialBackModule
        else {
            return nil
        }

        self.navigationTitle = model.interstitialNavigationTitle
        self.iconImage = iconImage
        self.title = model.bannerInterstitialTitle
        self.messages = model.bannerInterstitialMessages
        self.footerText = model.footerText

        self.buttonText = model.interstitialButtonText
        self.isButtonSticky = model.isBannerInterstitialButtonSticky

        self.interstitialShownStoreID = model.interstitialShownStoreID

        self.analyticsModule = analyticsModule
        self.tapContinueModule = tapContinueModule
        self.tapBackModule = tapBackModule
        self.tapURLModules = model.tapInterstitialURLModules

        self.analyticsAttributes = model.analyticsAttributes
        self.appearAnalyticsAttributes = model.appearAnalyticsAttributes
    }

    func setInterstitialShown() {
        guard
            let userDefaultsStore = storeProvider.target(UserDefaultsStore.self),
            let storeID = interstitialShownStoreID
        else {
            return
        }

        userDefaultsStore.write(true, options: storeID.writingOptions)
    }
}
