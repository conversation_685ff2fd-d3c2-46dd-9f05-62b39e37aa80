//
//  MayaInterstitialViewModelProtocol.swift
//  PayMaya
//
//  Created by <PERSON> on 4/12/23.
//  Copyright © 2023 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import Foundation
import Injector
import StoreProvider

protocol InterstitialModel {
    /// Returns true if an interstitial has been configured for the creator store
    var hasInterstitial: Bool { get }

    /// ID stored in user defaults that is checked if the interstitial screen has been shown.
    /// Not all interstitials have this (i.e. lucky games), if they need to be shown everytime
    var interstitialShownStoreID: UserDefaultsStoreId? { get }

    var interstitialNavigationTitle: String? { get }
    var interstitialImage: ImageAsset? { get }

    var interstitialTitle: String { get }
    var interstitialMessages: [MayaInterstitialMessage] { get }

    var interstitialButtonText: String { get }
    var isInterstitialButtonSticky: Bool { get }

    var interstitialScreenModule: AnalyticsModule? { get }
    var tapIntestitialContinueModule: AnalyticsModule? { get }
    var tapInterstitialBackModule: AnalyticsModule? { get }
    var tapInterstitialURLModules: [URL: AnalyticsModule] { get }
    var analyticsAttributes: [AnalyticsAttributeKey: Any] { get }
}

extension InterstitialModel {
    var shouldShowInterstitial: Bool {
        guard hasInterstitial else {
            return false
        }

        if let storeID = interstitialShownStoreID,
           let userDefaultsStore = ContainerWrapper.shared.resolve(StoreProvider.self).target(UserDefaultsStore.self),
           let isShown = userDefaultsStore.readRaw(storeID) as? Bool {
            return !isShown
        } else {
            return true
        }
    }
}

protocol MayaInterstitialViewModelProtocol: ViewModelProtocol {
    var interstitialShownStoreID: UserDefaultsStoreId? { get }

    var navigationTitle: String? { get }
    var iconImage: ImageAsset { get }
    var title: String { get }
    var messages: [MayaInterstitialMessage] { get }

    var buttonText: String { get }
    var isButtonSticky: Bool { get }

    var analyticsModule: AnalyticsModule { get }
    var tapContinueModule: AnalyticsModule { get }
    var tapBackModule: AnalyticsModule { get }
    var tapURLModules: [URL: AnalyticsModule] { get }
    var analyticsAttributes: [AnalyticsAttributeKey: Any] { get }

    func setInterstitialShown()
}
