//
//  MayaBannerInterstitialViewModelProtocol.swift
//  PayMaya
//
//  Created by <PERSON> on 6/5/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import Foundation

protocol BannerInterstitialModel: InterstitialModel {
    /// Returns true if the interstitial has been configured to have CleverTap banners
    var doesInterstitialHaveBanners: Bool { get }

    // Adjust properties to avoid duplicates when accomodating MayaBannerInsterstitialViewController
    var bannerInterstitialTitle: String { get }
    var bannerInterstitialMessages: [MayaInterstitialMessage] { get }

    var isBannerInterstitialButtonSticky: Bool { get }
    var footerText: NSAttributedString? { get }

    var appearAnalyticsAttributes: [AnalyticsAttributeKey: Any] { get }
    var bannerTapIntestitialContinueModule: AnalyticsModule? { get }
}

protocol MayaBannerInterstitialViewModelProtocol: MayaInterstitialViewModelProtocol {
    var adCarouselViewModel: AdCarouselViewModel { get }
    var footerText: NSAttributedString? { get }
    var appearAnalyticsAttributes: [AnalyticsAttributeKey: Any] { get }
}
