//
//  MayaServicesViewModel.swift
//  PayMaya
//
//  Created by <PERSON> on 3/7/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import ConfigurationService
import Injector
import MayaCoreData
import ReactiveSwift
import StoreProvider
import UIKit

private typealias WebViewConstants = Constants.WebView

class MayaServicesViewModel: ViewModel, MayaServicesViewModelProtocol {
    enum Constants {
        static let height: CGFloat = 104
        static let minimumSpacing: CGFloat = 8
        static let numberOfCategoryColumns = 4
    }
    private let disposeBag = CompositeDisposable()

    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2
    @Inject private var storeProvider: StoreProvider

    var categoriesProperty: MutableProperty<[MayaServiceCategory]> = MutableProperty([])

    let adCarouselViewModel = AdCarouselViewModel(module: AdModule.servicesBanner)

    private var services: [Service] = []
    private var coreServices: [Service] = []
    private var menus: [ServicesAction] = ServicesAction.filteredActions

    var bannerLocation: MayaServicesBannerLocation = .none
    private var bannerIndex: Int? { configurationService.mayaServicesCategory?.bannerIndex }
    private static let bannerId = "FakeiOSBannerCategory"

    private(set) var sourceScreen: MayaServicesSourceScreen?

    override init() {
        super.init()
        fetchAllServices()
        fetchCoreServices()
        fetchCategories()
        bannerLocation = getBannerLocation()
    }

    var helpUrl: URL? {
        guard let propertyListStore = storeProvider.target(PropertyListStore.self),
            let supportUrl = propertyListStore.read(PropertyListStoreId.helpBaseUrl).value as? String else {
            return nil
        }

        if let databaseStore = storeProvider.target(DatabaseStore.self),
            let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
            let encodedParameter = getEncodedProfileData(of: user) {
            let supportUrlWithParameter = supportUrl + WebViewConstants.salesForceQuery.rawValue + encodedParameter
            return URL(string: supportUrlWithParameter)
        }
        return URL(string: supportUrl)
    }

    var numberOfCoreServices: Int { min(coreServices.count, 5) }
    var numberOfCategories: Int { categoriesProperty.value.count }
    var numberOfActions: Int { menus.count }

    var isDashboardNavigationRefactorEnabled: Bool { configurationServiceV2.dashboardNavigationRefactorEnabled.value }
    var isServicesClevertapUnliBannersEnabled: Bool { configurationServiceV2.servicesClevertapUnliBannerEnabled.value }

    func coreService(forItemAt indexPath: IndexPath) -> Service? {
        return coreServices[safeIndex: indexPath.row]
    }

    func categoryViewModel(forRowAt indexPath: IndexPath) -> MayaServiceCategoryViewModel? {
        guard let category = category(forRowAt: indexPath) else {
            return nil
        }
        let services = servicesForCategory(category)

        return MayaServiceCategoryViewModel(category: category, services: services, categoryRow: indexPath.row)
    }

    func categoryRow(forID id: String) -> Int? {
        return categoriesProperty.value.firstIndex { $0.id == id }
    }

    func action(forRowAt indexPath: IndexPath) -> ServicesAction? {
        return menus[safeIndex: indexPath.row]
    }

    func categoryHeight(forRowAt indexPath: IndexPath) -> CGFloat {
        guard let category = category(forRowAt: indexPath) else {
            return 0
        }
        let services = servicesForCategory(category)

        let numberOfServices = CGFloat(services.count)
        let numberOfColumns = CGFloat(Constants.numberOfCategoryColumns)
        let minimumSpacing = Constants.minimumSpacing
        let cellHeight = Constants.height

        let numberOfRows = ceil(numberOfServices / numberOfColumns)
        let totalLineSpacing = (numberOfRows - 1) * minimumSpacing
        let totalCellHeight = numberOfRows * cellHeight

        return totalCellHeight + totalLineSpacing
    }

    func updateSourceScreen(_ sourceScreen: MayaServicesSourceScreen) {
        self.sourceScreen = sourceScreen
    }

    func removeClevertapBannerCategory() {
        guard isServicesClevertapUnliBannersEnabled else { return }

        categoriesProperty.value.removeAll { category in
            category.id == Self.bannerId
        }
    }

    func addClevertapBannerCategory() {
        guard let bannerIndex = self.bannerIndex,
              isServicesClevertapUnliBannersEnabled,
              bannerLocation == .categoriesSection,
              categoriesProperty.value.filter { $0.id == Self.bannerId }.isEmpty
        else { return  }

        let fakeBannerCategory = MayaServiceCategory(id: Self.bannerId, name: "", serviceIDs: [], backgroundColorString: "")
        categoriesProperty.value.insert(fakeBannerCategory, at: bannerIndex)
    }

    func isCategoryRowBanner(forRowAt indexPath: IndexPath) -> Bool {
        guard bannerLocation == .categoriesSection,
              let category = category(forRowAt: indexPath)
        else { return false }

        return category.id == Self.bannerId
    }

    private func getBannerLocation() -> MayaServicesBannerLocation {
        guard let bannerIndex = self.bannerIndex,
            isServicesClevertapUnliBannersEnabled
        else { return .none }

        switch bannerIndex {
        case 0...categoriesProperty.value.count - 1: return .categoriesSection
        default: return .none
        }
    }
}

private extension MayaServicesViewModel {
    func fetchAllServices() {
        guard let configServices = ServiceUtility.filteredServices else {
            services = Service.defaultServices
            return
        }

        services = configServices
    }

    func fetchCoreServices() {
        guard let coreServiceIDs = configurationService.mayaServicesCategory?.coreServiceIDs else {
            return
        }

        let coreServices = services(fromIDs: coreServiceIDs).prefix(5) // Use only up to first 5 as precaution
        self.coreServices = Array(coreServices)
    }

    func fetchCategories() {
        guard let configCategories = configurationService.mayaServicesCategory?.categories else {
            categoriesProperty.value = MayaServiceCategory.defaultCategories
            return
        }

        categoriesProperty.value = configCategories.filter { [weak self] category in
            guard let self = self else { return false }
            return !self.servicesForCategory(category).isEmpty
        }
    }

    func category(forRowAt indexPath: IndexPath) -> MayaServiceCategory? {
        return categoriesProperty.value[safeIndex: indexPath.row]
    }

    func servicesForCategory(_ category: MayaServiceCategory) -> [Service] {
        return services(fromIDs: category.serviceIDs)
    }

    func services(fromIDs ids: [String]) -> [Service] {
        return ids.compactMap { [weak self] serviceID in
            self?.services.first { $0.id == serviceID }
        }
    }

    func getEncodedProfileData(of user: User) -> String? {
        guard let mobileNumber = user.msisdn else { return nil }
        let profileId = user.id
        let os = String(format: "%@(iOS %@)", UIDevice.current.model, UIDevice.current.systemVersion)
        let profileParameter = ProfileParameter(profileId: profileId, mobileNumber: mobileNumber, email: user.backupIdentity?.value, os: os)
        let encoder = JSONEncoder()
        if let encodedData = try? encoder.encode(profileParameter) {
            let encodedStringFromData = encodedData.base64EncodedString()
            return encodedStringFromData.encodedURLString()
        }
        return nil
    }
}
