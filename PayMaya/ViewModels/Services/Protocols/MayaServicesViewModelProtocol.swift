//
//  MayaServicesViewModelProtocol.swift
//  PayMaya
//
//  Created by <PERSON> on 3/7/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import ConfigurationService
import Foundation
import ReactiveSwift

protocol MayaServicesViewModelProtocol: ViewModelProtocol {
    var helpUrl: URL? { get }
    var sourceScreen: MayaServicesSourceScreen? { get }

    var numberOfCoreServices: Int { get }
    var numberOfCategories: Int { get }
    var numberOfActions: Int { get }

    var categoriesProperty: MutableProperty<[MayaServiceCategory]> { get }

    var isDashboardNavigationRefactorEnabled: Bool { get }
    var isServicesClevertapUnliBannersEnabled: Bool { get }
    var adCarouselViewModel: AdCarouselViewModel { get }
    var bannerLocation: MayaServicesBannerLocation { get set }

    func coreService(forItemAt indexPath: IndexPath) -> Service?
    func categoryViewModel(forRowAt indexPath: IndexPath) -> MayaServiceCategoryViewModel?
    func categoryRow(forID id: String) -> Int?
    func action(forRowAt indexPath: IndexPath) -> ServicesAction?
    func categoryHeight(forRowAt indexPath: IndexPath) -> CGFloat
    func updateSourceScreen(_ sourceScreen: MayaServicesSourceScreen)
    func isCategoryRowBanner(forRowAt indexPath: IndexPath) -> Bool
    func removeClevertapBannerCategory()
    func addClevertapBannerCategory()
}

enum MayaServicesSourceScreen {
    case floatingTab
    case dashboard

    var attributeValue: String {
        switch self {
        case .floatingTab: return AnalyticsConstants.Services.navigationTapped.rawValue
        case .dashboard: return AnalyticsConstants.Services.moreClicked.rawValue
        }
    }
}

enum MayaServicesBannerLocation {
    case categoriesSection
    case coreServicesSection
    case none
}
