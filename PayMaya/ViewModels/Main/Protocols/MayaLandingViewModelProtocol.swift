//
//  MayaLandingViewModelProtocol.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 11/11/20.
//  Copyright © 2020 PayMaya Philippines, Inc. All rights reserved.
//

protocol MayaLandingViewModelProtocol: ViewModelProtocol,
                                       ThemedViewModelProtocol,
                                       LocationAuthorizerServiceConfigurator,
                                       LocationCheckerServiceConfigurator {
    var locationCheckStatus: LocationCheckStatus { get }
    var shouldShowMayaIntroductionScreens: Bool { get }
    var shouldShowTinboPrompt: Bool { get }
    var deviceID: String? { get }
    var isShortenedRegistrationEnabled: Bool { get }
    var isLandingPageV2Enabled: Bool { get }
    var shouldShowNativeLocationPermissionDialog: Bool { get }

    func requestIsInPH()
}
