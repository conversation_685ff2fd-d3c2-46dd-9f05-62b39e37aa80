//
//  MayaLoginViewModelProtocol.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON>nco on 31/08/2018.
//  Copyright © 2018 PayMaya Philippines, Inc. All rights reserved.
//

import Error
import Foundation
import MayaCoreData
import ReactiveSwift

protocol MayaLoginViewModelProtocol: AuthenticationViewModelProtocol,
                                     ThemedViewModelProtocol,
                                     RestrictionsViewModelProtocol,
                                     LocationAuthorizerServiceConfigurator {
    var user: MutableProperty<User?> { get }
    var validatingProperty: Property<Bool>! { get }

    var signoutAction: Action<Void, Void, Error>! { get }
    var touchIdAuthenticationAction: Action<Void, TouchIdResult, Error>! { get }
    var needsLocationAuthorization: Bool { get }

    var shouldShowMayaIntroductionScreens: Bool { get }

    func getUserDisplayName() -> String?
    func getUserFirstName() -> String?
    func clearSessionToken()
}
