//
//  MayaLandingViewModel.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 11/11/20.
//  Copyright © 2020 PayMaya Philippines, Inc. All rights reserved.
//

import ConfigurationService
import Injector
import ReactiveSwift
import StoreProvider
import UIKit

class MayaLandingViewModel: ViewModel, MayaLandingViewModelProtocol {
    @Inject var configurationService: ConfigurationService
    @Inject var configurationServiceV2: ConfigurationServiceV2
    @Inject var appThemeService: AppThemeService
    @Inject var locationAuthorizerService: LocationAuthorizerServiceProtocol
    @Inject var locationCheckerService: LocationCheckerServiceProtocol
    @Inject var storeProvider: StoreProvider

    var locationCheckStatus: LocationCheckStatus {
        return locationCheckerService.checkStatus.value
    }

    var isShortenedRegistrationEnabled: Bool {
        return configurationServiceV2.shortenedRegistrationEnabled.value
    }

    var isLandingPageV2Enabled: Bool {
        return configurationServiceV2.landingPageV2Enabled.value
    }

    var shouldShowMayaIntroductionScreens: Bool {
        guard let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
            let userDefaultsStore = storeProvider.target(UserDefaultsStore.self) else {
            return false
        }

        if let shownConfig = encryptedStore.read(EncryptedDefaultsStoreId.introductionScreensShown, type: ShownConfig.self).value {
            return !shownConfig.hasBeenShown
        } else {
            if let shown = userDefaultsStore.readRaw(UserDefaultsStoreId.mayaIntroductionScreensShown) as? Bool {
                // Migrate value from user defaults store to encrypted defaults store
                encryptedStore.write(ShownConfig(hasBeenShown: shown), options: EncryptedDefaultsStoreId.introductionScreensShown)
                return !shown
            } else {
                return true
            }
        }
    }

    var shouldShowTinboPrompt: Bool {
        guard let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self) else { return false }

        let isNotInPH = locationCheckStatus == .notInPH
        if let tinboShownConfig = encryptedStore.read(
            EncryptedDefaultsStoreId.tinboPromptShown,
            type: ShownConfig.self
        ).value {
            return !tinboShownConfig.hasBeenShown && isNotInPH
        } else {
            return isNotInPH
        }
    }

    var shouldShowNativeLocationPermissionDialog: Bool {
        return locationAuthorizerService.authorizationStatus == .notDetermined
    }

    var deviceID: String? { UIDevice.current.identifierForVendor?.uuidString }

    func requestIsInPH() {
        guard configurationService.locationBasedVirtualNumberRegistrationEnabled.value else { return }
        locationCheckerService.checkIsInPH()
    }
}
