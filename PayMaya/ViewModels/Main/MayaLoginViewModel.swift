//
//  MayaLoginViewModel.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 3/30/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import AssetProvider
import ConfigurationService
import Error
import Foundation
import Injector
import MayaCoreData
import Moya
import ReactiveSwift
import StoreProvider

private let errorTitle = L10n.Error.Login.title

class MayaLoginViewModel: ViewModel, MayaLoginViewModelProtocol, RestrictionsViewModelProtocol {
    @Inject private var apiProvider: APIProvider
    @Inject internal var storeProvider: StoreProvider
    @Inject var configurationService: ConfigurationService
    @Inject var appThemeService: AppThemeService
    @Inject private var notificationService: NotificationService
    @Inject private var signoutService: SignoutService
    @Inject private var authenticationProvider: AuthenticationProviderManager
    @Inject var locationAuthorizerService: LocationAuthorizerServiceProtocol
    @Inject private var flutterEngineManager: FlutterEngineManager

    var loginAction: Action<Void, Void, PayMayaError>!
    var validateAction: Action<Void, Void, PayMayaError>!
    var signoutAction: Action<Void, Void, Error>!
    var touchIdAuthenticationAction: Action<Void, TouchIdResult, Error>!

    let usernameField = NewFieldProperty(validationMessage: L10n.Field.Error.required(L10n.Field.Name.mobileNumber))
    let passwordField = NewFieldProperty(validationMessage: L10n.Field.Error.required(L10n.Field.Name.password))

    let user: MutableProperty<User?> = MutableProperty(nil)
    var validatingProperty: Property<Bool>!
    var mfaChallengeDetailsProperty: MutableProperty<(challengeId: String, transactionType: String)?> = MutableProperty(nil)

    var biometricType: BiometricType {
        return authenticationProvider.touchIdManager.biometricType
    }

    var showBiometryButton: Bool {
        return authenticationProvider.isTouchIdAllowedForAuthentication()
    }

    var shouldShowMayaIntroductionScreens: Bool {
        guard let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
              let userDefaultsStore = storeProvider.target(UserDefaultsStore.self) else {
            return false
        }

        if let shownConfig = encryptedStore.read(EncryptedDefaultsStoreId.introductionScreensShown, type: ShownConfig.self).value {
            return !shownConfig.hasBeenShown
        } else {
            if let shown = userDefaultsStore.readRaw(UserDefaultsStoreId.mayaIntroductionScreensShown) as? Bool {
                // Migrate value from user defaults store to encrypted defaults store
                encryptedStore.write(ShownConfig(hasBeenShown: shown), options: EncryptedDefaultsStoreId.introductionScreensShown)
                return !shown
            } else {
                return true
            }
        }
    }

    var needsLocationAuthorization: Bool {
        if let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
           let wasSkippedByUser = encryptedStore.read(EncryptedDefaultsStoreId.locationPermissionSkipped, type: ShownConfig.self).value {
            return !wasSkippedByUser.hasBeenShown
        }

        return locationAuthorizerService.authorizationStatus == .notDetermined
    }

    override init() {
        super.init()

        let inlineIsValid: () -> Bool = { [weak self] in
            guard let self = self else { return true }
            return self.usernameField.isValid && self.passwordField.isValid
        }

        let loginCompletionHandler: ((String) -> Void) = { [weak self] password in
            guard let self = self else { return }
            self.clearRestrictions()
            self.flutterEngineManager.initializeEngines()
            self.authenticationProvider.saveCredential(credential: password)
            self.notificationService.sendPushTokenAction.apply().start()
            guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
                  let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User else { return }
            self.user.value = user
        }

        validatingProperty = Property.combineLatest(usernameField.validation.result, passwordField.validation.result)
            .map { tuple -> Bool in
                let (first, second) = tuple
                return (!first.isInvalid && !second.isInvalid)
            }

        validateAction = Action { _ in
            if inlineIsValid() {
                return SignalProducer<Void, PayMayaError>.empty
            }
            return SignalProducer<Void, PayMayaError>(error: PayMayaError())
        }

        loginAction = Action { [weak self] in
            guard let self = self else {
                return PayMayaError.validationError(title: errorTitle)
            }

            let device = self.storeProvider.target(EncryptedDefaultsStore.self)?.read(EncryptedDefaultsStoreId.device, type: Device.self).value

            let deviceToken = device?.token
            let username = String.formatToPhilippinesMSISDN(self.usernameField.mutableProperty.value)
            let identityRequest = IdentityRequest(type: IdentityType.msisdn.rawValue,
                                                  value: username)
            let request = SessionRequest(identity: identityRequest,
                                         password: self.passwordField.mutableProperty.value,
                                         source: Constants.Defaults.Common.source.rawValue,
                                         deviceToken: deviceToken)

            let noInternetErrorAlertViewModel = ErrorAlertViewModel(title: CommonStrings.Error.Spiel.No.Internet.Title.new,
                                                                    message: CommonStrings.Error.Spiel.No.Internet.Message.new,
                                                                    image: CommonAsset.Images.Alert.imageDefaultError.image,
                                                                    closeButtonTitle: CommonStrings.Common.close)

            let errorHandler: (PayMayaError) -> SignalProducer<Void, PayMayaError> = { error in
                if case let .mfaChallengeOTPRequired(challengeId, transactionType, paramType) = error.type {
                    let startRequest = MFAChallengeStartRequest(method: SessionMFAChallengeMethod.otp.rawValue, params: MFAChallengeStartRequest.Params(deviceData: nil, lifestyleId: nil, type: paramType, value: username))
                    return self.apiProvider.reactive.request(API.MFAChallenge.startChallenge(transactionType: transactionType, challengeId: challengeId, request: startRequest), useNoInternetConnectionType: true)
                        .on(completed: {
                            loginCompletionHandler(request.password)
                        }).on(value: { _ in
                            self.mfaChallengeDetailsProperty.value = (challengeId, transactionType)
                        }).map { _ in }
                        .mapMayaError(title: CommonStrings.Login.Error.title, image: CommonAsset.Images.Alert.image3DMaintenance.image, closeButtonTitle: CommonStrings.Common.close)
                } else {
                    return SignalProducer(error: error)
                }
            }

            let sessionRequest: SignalProducer<Void, Error>

            if !self.isRestrictedLoginFlowEnabled {
                sessionRequest = self.getSessionRequestDefault(request: request, loginCompletionHandler: loginCompletionHandler)
            } else {
                sessionRequest = self.getSessionRequestRestrictionsEnabled(request: request, loginCompletionHandler: loginCompletionHandler)
            }

            return sessionRequest.mapMayaError(title: CommonStrings.Login.Error.title, image: CommonAsset.Images.Alert.image3DMaintenance.image, closeButtonTitle: CommonStrings.Common.close)
                .flatMapError(errorHandler)
                .mapMayaErrorToCustomMayaError(errorType: .shieldStandardError, .shieldSpecialError)
                .mapErrorTypeToViewModel(errorType: .noInternetConnection, viewModel: noInternetErrorAlertViewModel)
        }

        touchIdAuthenticationAction = Action { [weak self] in
            guard let self = self else { return SignalProducer<TouchIdResult, Error>(error: Error()) }
            return self.authenticationProvider.authenticateUsingTouchId()
        }

        signoutAction = Action { [weak self] in
            guard let self = self else { return SignalProducer<Void, Error>(error: Error()) }
            self.user.value = nil
            // This is non-breaking regardless if success or not
            self.notificationService.deletePushTokenAction.apply().start()
            return self.signoutService.signout()
        }
    }

    func getSessionRequestDefault(request: SessionRequest, loginCompletionHandler: @escaping (String) -> Void) -> SignalProducer<Void, Error> {
        return self.apiProvider.reactive.request(API.Session.sessions(request), ignoresQueue: true, useNoInternetConnectionType: true).map { _ in
            loginCompletionHandler(request.password)
        }
    }

    func getSessionRequestRestrictionsEnabled(request: SessionRequest, loginCompletionHandler: @escaping (String) -> Void) -> SignalProducer<Void, Error> {
        return self.apiProvider.reactive.request(API.Session.sessions(request), ignoresQueue: true, useNoInternetConnectionType: true).flatMap(.concat, { _ in
            loginCompletionHandler(request.password)
            let restrictionRequest = self.getRestrictedLoginRequest()

            return self.apiProvider.reactive.request(restrictionRequest, ignoresQueue: true, useNoInternetConnectionType: true).timeout(after: Constants.Restriction.getRestrictionTimeOut, raising: Error(), on: QueueScheduler()).on(failed: { _ in
                self.clearRestrictions()
            }).materialize().map { _ in
            }
        })
    }

    var isRestrictedLoginFlowEnabled: Bool {
        return configurationService.restrictedLoginFlowEnabled.value
    }

    func getRestrictedLoginRequest() -> APITargetType {
        guard let user = getUser() else { return API.Restrictions.getAccountRestrictions }

        let customerRestrictionRequest = RestrictionRequest(accountStatus: user.accountStatusValue.rawValue)

        let request = configurationService.customerRestrictionsEnabled.value ? API.Restrictions.getCustomerRestrictions(customerRestrictionRequest) : API.Restrictions.getAccountRestrictions
        return request
    }

    func getUser() -> User? {
        guard let databaseStore = storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
        else { return nil }
        return user
    }

    func getPrivacyPolicyVersion() -> String {
        guard let user = getUser(),
              let version = user.privacyPolicy.version
        else { return "" }
        return version
    }

    func getUserDisplayName() -> String? {
        guard let user = getUser() else { return nil }
        return user.profile?.fullName
    }

    func getUserFirstName() -> String? {
        guard let user = getUser() else { return nil }

        return user.profile?.firstName
    }

    func updateLocalAuthenticationPermission(didAllow: Bool) {
        if didAllow {
            authenticationProvider.saveTouchIdStatus(status: .touchIdAllowed)
        } else {
            authenticationProvider.saveTouchIdStatus(status: .touchIdDisallowed)
        }
    }

    func canUseTouchId() -> Bool {
        return authenticationProvider.canUseTouchId()
    }

    func isTouchIdAllowedForAuthentication() -> Bool {
        return authenticationProvider.isTouchIdAllowedForAuthentication()
    }

    func getSavedCredential() -> String? {
        return authenticationProvider.retrieveCredential()
    }

    func getPreviousMobileNumber() -> String? {
        guard let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
              let previousAccount = encryptedStore.read(EncryptedDefaultsStoreId.previousAccount, type: PreviousAccount.self).value
        else { return nil }
        return previousAccount.msisdn
    }

    func clearSessionToken() {
        guard let user = getUser(),
              user.token != nil,
              let databaseStore = storeProvider.target(DatabaseStore.self) else { return }

        user.token = nil

        databaseStore.write(user, options: DatabaseStore.WritingOptions(DatabaseEntity.user))
    }
}
