//
//  MayaRegistrationOTPViewModel.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 2/22/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import ConfigurationService
import Error
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import StoreProvider
import UIKit

class MayaRegistrationOTPViewModel: MayaOTPBaseViewModel, MayaOTPViewModelProtocol, RestrictionsViewModelProtocol {
    @Inject private var apiProvider: APIProvider
    @Inject var storeProvider: StoreProvider
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2
    @Inject private var notificationService: NotificationService
    @Inject private var flutterEngineManager: FlutterEngineManager

    var authenticateAction: Action<Void, AuthenticationResult, PayMayaError>!
    var resendOTPCodeAction: Action<Void, Void, PayMayaError>!
    var savingsAutoProvisionAction: Action<Void, Void, PayMayaError>?
    var startOTPChallengeAction: Action<Void, MFAChallengeStartResponse, PayMayaError>?
    let user: MutableProperty<User?> = MutableProperty(nil)
    var otpType: OTPType

    var mobileNumber: String {
        guard
            let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
            let registration = encryptedStore.read(EncryptedDefaultsStoreId.registration, type: UserRegistration.self).value
            else {
                return String.empty
        }
        return registration.identity.value
    }

    var canBeReferred: Bool {
        // Issue: Timeout occurs on BE when calling referal eligibility endpoint causing not to show the invite code banner
        // Solution: Always show the invite code banner
        // Note: BE properly handles error if MGM campaign is inactive
        return true
    }

    var isRegistrationMGMEnabled: Bool {
        return configurationService.registrationMGMEnabled.value
    }

    var shouldLogRefereeMGMAnalytics: Bool {
        return canBeReferred && isRegistrationMGMEnabled
    }

    var upgradeNowLabelTreatment: String {
        return configurationServiceV2.upgradeNowLabel.value
    }

    var isRegToKYCFlowEnabled: Bool {
        return configurationServiceV2.regToKYCFlowEnabled.value
    }

    var isShortenedRegistrationEnabled: Bool {
        configurationServiceV2.shortenedRegistrationEnabled.value
    }

    init(otpType: OTPType) {
        self.otpType = otpType
        super.init()

        var registrationId = ""
        if let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
           let registration = encryptedStore.read(EncryptedDefaultsStoreId.registration, type: UserRegistration.self).value {
            registrationId = registration.registrationID
        }

        authenticateAction = Action { [weak self] in
            guard let self = self else {
                return PayMayaError.validationError(title: defaultErrorTitle)
            }

            if self.isValidVerificationCodeProperty.value {
                if configurationService.registrationRestrictionsEnabled.value {
                    return self.getRegistrationOtpRequestRestrictedEnabled(registrationId: registrationId)
                } else {
                    let request = VerificationRequest(registrationId: registrationId, verificationCode: self.verificationCodeProperty.value)
                    let requestProducer = self.apiProvider.reactive.request(API.Registration.verify(request))
                        .map { _ -> AuthenticationResult in
                            self.flutterEngineManager.initializeEngines()
                            self.notificationService.sendPushTokenAction.apply().start()
                            if let databaseStore = self.storeProvider.target(DatabaseStore.self),
                                let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User {
                                self.user.value = user
                            }
                            return .success(nil)
                        }
                    return requestProducer.mapMayaError()
                        .on(failed: { error in
                            if case .verificationRedirect = error.type,
                                let encryptedStore = self.storeProvider.target(EncryptedDefaultsStore.self) {
                                encryptedStore.remove(EncryptedDefaultsStoreId.registration, completion: nil)
                            }
                        })
                }
            } else {
                return SignalProducer<AuthenticationResult, PayMayaError>(error: PayMayaError(type: .validation, viewModel: ErrorAlertViewModel(title: defaultErrorTitle, message: L10n.Error.Spiel.Otp.required)))
            }
        }

        resendOTPCodeAction = Action { [weak self] in
            guard let self = self else {
                return PayMayaError.validationError(title: defaultErrorTitle)
            }

            let request = VerificationRequest(registrationId: registrationId, verificationCode: nil)
            let requestProducer = self.apiProvider.reactive.request(API.Registration.resendCode(request))
                .map { _ in }
            return requestProducer.mapMayaError()
        }

        savingsAutoProvisionAction = Action { [weak self] in
            guard let self = self else {
                return PayMayaError.validationError(title: defaultErrorTitle)
            }

            let request = [SavingsConsentData(type: Constants.Consent.Savings.Terms.type,
                                              version: Constants.Consent.Savings.Terms.version),
                           SavingsConsentData(type: Constants.Consent.Savings.Privacy.type,
                                              version: Constants.Consent.Savings.Privacy.version)]
            let requestProducer = self.apiProvider.reactive.request(API.Consent.setSavingsConsent(request: request))
                .map { _ in }
            return requestProducer.mapMayaError()
        }
    }

    func getRegistrationOtpRequestRestrictedEnabled(registrationId: String) -> SignalProducer<AuthenticationResult, PayMayaError> {
        self.clearRestrictions()
        let request = VerificationRequest(registrationId: registrationId, verificationCode: self.verificationCodeProperty.value)
        let requestProducer = self.apiProvider.reactive.request(API.Registration.verify(request)).flatMap(.concat, { _ -> SignalProducer<AuthenticationResult, Never> in
            let restrictionProducer = self.apiProvider.reactive.request(self.getRestrictedLoginRequest(), ignoresQueue: true, useNoInternetConnectionType: true)
            return restrictionProducer.timeout(after: Constants.Restriction.getRestrictionTimeOut, raising: Error(), on: QueueScheduler()).on(failed: { _ in
                self.clearRestrictions()
            }).materialize().take(first: 1).map { _ -> AuthenticationResult in
                self.flutterEngineManager.initializeEngines()
                self.notificationService.sendPushTokenAction.apply().start()
                if let databaseStore = self.storeProvider.target(DatabaseStore.self),
                    let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User {
                    self.user.value = user
                }
                return .success(nil)
            }
        })

        return requestProducer.mapMayaError()
            .on(failed: { error in
                if case .verificationRedirect = error.type,
                    let encryptedStore = self.storeProvider.target(EncryptedDefaultsStore.self) {
                    encryptedStore.remove(EncryptedDefaultsStoreId.registration, completion: nil)
                }
            })
    }

    var isRestrictedLoginFlowEnabled: Bool {
        return configurationService.restrictedLoginFlowEnabled.value
    }

    func getRestrictedLoginRequest() -> APITargetType {
        guard let user = getUser() else { return API.Restrictions.getAccountRestrictions }

        let customerRestrictionRequest = RestrictionRequest(accountStatus: user.accountStatusValue.rawValue)

        let request = configurationService.customerRestrictionsEnabled.value ? API.Restrictions.getCustomerRestrictions(customerRestrictionRequest) : API.Restrictions.getAccountRestrictions
        return request
    }

    func getUser() -> User? {
        guard let databaseStore = storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
        else { return nil }
        return user
    }

    func setUserDefaultShouldShowUpgradeAccount() {
        if let userDefaults = storeProvider.target(UserDefaultsStore.self) {
            userDefaults.write(true, options: UserDefaultsStoreId.shouldShowUpgradeAccountUponRegistration)
        }
    }

    func setUserDefaultShouldSubmitInviteCode() {
        if let userDefaults = storeProvider.target(UserDefaultsStore.self) {
            userDefaults.write(true, options: UserDefaultsStoreId.shouldShowInputInviteCode)
        }
    }

    func setShouldPrefillReferralCode(shouldPrefill: Bool) {
        if let userDefaults = storeProvider.target(UserDefaultsStore.self) {
            userDefaults.write(shouldPrefill, options: UserDefaultsStoreId.shouldPrefillReferralCode)
        }
    }
}
