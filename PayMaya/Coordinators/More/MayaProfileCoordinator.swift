//
//  MayaProfileCoordinator.swift
//  PayMaya
//
//  Created by <PERSON> on 3/24/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//
import ConfigurationService
import Foundation
import Injector
import ReactiveSwift

class MayaProfileCoordinator: MayaEKYCBaseCoordinator, DeepLinkHandler {
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2

    var viewModel: MayaProfileViewModel?

    override func start(completion: (() -> Void)? = nil) {
        viewModel = MayaProfileViewModel()
        let viewController = StoryboardScene.More.mayaProfileViewController.instantiate()
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.bind(viewModel)
        setRootModule(viewController, animated: false)
        presenter?.present(router, animated: true, completion: completion)

        viewModel?.canInviteFriendsProperty.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self else { return }
                complete(deepLinkHandling: deepLinkHandling, error: nil)
            }?.addToDisposeBag(disposeBag)
    }

    override func didTapBack(_ viewController: UIViewController) {
        self.router.toPresentable().hideMayaLoader()
        super.didTapBack(viewController)
    }
}

// MARK: - DeepLink Handler Methods
extension MayaProfileCoordinator {
    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .dashboard(.profile(.chatWithUs)):
            return .opened(deepLink) { [weak self] in
                self?.showHelpScreen()
            }
        case .dashboard(.profile(.refer)):
            return handleReferralDeepLink(deepLink)
        case .dashboard(.profile(.accountLimits)):
            return .normal(deepLink, identifier, String(describing: MayaAccountLimitsCoordinator.self), { [weak self] in
                self?.startAccountLimitsFlow()
            }, false)
        case .dashboard(.profile(.settings)):
            return .normal(deepLink, identifier, String(describing: MayaSettingsCoordinator.self), { [weak self] in
                self?.startSettingsFlow()
            }, false)
        case .dashboard(.profile(.quickguide)):
            return .opened(deepLink) { [weak self] in
                self?.showQuickGuideScreen()
            }
        case .dashboard(.profile(.help)):
            return .opened(deepLink) { [weak self] in
                self?.showHelpScreen()
            }
        case .dashboard(.profile(.approvalRequests)):
            return .opened(deepLink) { [weak self] in
                self?.startAuthorizeRequestsFlow()
            }
        case .dashboard(.profile(.underReview)):
            return .opened(deepLink) { [weak self] in
                self?.startReviewApplicationFlow()
            }
        case .dashboard(.profile(.profile)):
            return .opened(deepLink, nil)
        default: return .rejected(deepLink, .notHandled, identifier)
        }
    }

    func handleReferralDeepLink(_ deepLink: DeepLink) -> DeepLinkHandling {
        if configurationServiceV2.mgmV2ForceUpdateEnabled.value {
            return .opened(deepLink) { [weak self] in
                guard let self else { return }
                showMGMV2ForceUpdateScreen()
            }
        }

        guard let viewModel = viewModel, viewModel.canInviteFriendsProperty.value else {
            return .delayed(deepLink, identifier, nil)
        }

        return .normal(deepLink, identifier, String(describing: MayaReferralShareCodeCoordinator.self), { [weak self] in
            self?.startReferralShareCodeFlow()
        }, false)
    }
}

// MARK: Navigation functions
extension MayaProfileCoordinator {
    func startProfileQRFlow() {
        let coordinator = MayaRequestMoneyCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func showQuickGuideScreen() {
        guard let link = Constants.WebView.quickGuide.url else { return }
        showWebView(link: link, willPresent: true)
    }

    func startFavoritesFlow() {
        let coordinator = MayaMyFavoritesCoordinator(presenter: router)
        coordinator.presentationDirection = .right
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startMissionsFlow() {
        let coordinator = MayaMissionsCoordinator(presenter: router)
        coordinator.presentationDirection = .right
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start { [weak self] in
            guard let self = self else { return }
            self.complete(deepLinkHandling: self.deepLinkHandling, error: nil)
        }
    }

    func startVouchersFlow() {
        guard let url = Constants.DeepLinkPath.vouchers.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
    }

    func startReferralInputCodeFlow() {
        let coordinator = MayaReferralInputCodeCoordinator(presenter: router)
        coordinator.source = .profile
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startAccountLimitsFlow() {
        let coordinator = MayaAccountLimitsCoordinator(presenter: router)
        coordinator.delegate = self
        coordinator.presentationDirection = .right
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start { [weak self] in
            guard let self = self else { return }
            self.complete(deepLinkHandling: self.deepLinkHandling, error: nil)
        }
    }

    func showHelpScreen() {
        guard
            let viewModel = viewModel,
            let link = viewModel.helpUrl
        else { return }
        showWebView(link: link, title: Constants.ScreenTitle.WebView.help.rawValue, willPresent: true)
    }

    func showInboxSupportScreen() {
        let coordinator = MayaInboxSupportCoordinator(presenter: router)
        coordinator.inboxSupportDelegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startSettingsFlow() {
        let coordinator = MayaSettingsCoordinator(presenter: router)
        coordinator.presentationDirection = .right
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start { [weak self] in
            guard let self = self else { return }
            self.complete(deepLinkHandling: self.deepLinkHandling, error: nil)
        }
    }

    func startReferralShareCodeFlow() {
        let coordinator = MayaReferralShareCodeCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start { [weak self] in
            guard let self = self else { return }
            self.complete(deepLinkHandling: self.deepLinkHandling, error: nil)
        }
    }

    func startKYCFlow() {
        if isShortenedFlowEnabled {
            let coordinator = EKYCZolozCoordinator(presenter: router)
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            coordinator.delegate = self
            coordinator.analyticsModule = Analytics.Menu()
            addChild(coordinator)
            coordinator.start()
        }
    }

    func startReKYCFlow() {
        let coordinator = EKYCZolozCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        coordinator.analyticsModule = Analytics.Menu()
        addChild(coordinator)
        if isShortenedFlowEnabled {
            coordinator.start()
        } else {
            coordinator.startManualReKYCFlow()
        }
    }

    func startReviewApplicationFlow() {
        if isShortenedFlowEnabled {
            let coordinator = EKYCZolozReviewCoordinator(presenter: router)
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            addChild(coordinator)
            coordinator.start()
        }
    }

    func startAddMoneyFlow() {
        let coordinator = MayaCashInCoordinator(
            presenter: router,
            analyticsSourcePage: AnalyticsConstants.SourcePage.profile
        )
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startCashInFlow() {
        let coordinator = MayaCashInCoordinator(
            presenter: router,
            analyticsSourcePage: AnalyticsConstants.SourcePage.profile
        )
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startReviewEDDFlow() {
        let coordinator = EDDReviewCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func showDuplicateAccountAlert() {
        let alertViewModel = MayaAlertViewModel(
            title: L10n.Profile.Duplicate.Account.title,
            message: L10n.Profile.Duplicate.Account.spiel,
            image: Asset.MayaImages.Profile.imageAccountError.image,
            actionsAxis: .vertical
        )

        let coordinator = MayaAlertModalCoordinator(presenter: router, viewModel: alertViewModel)
        coordinator.routeActionDelegate = self
        coordinator.backActionDelegate = self

        let visitSupportAction = MayaAlertAction(title: L10n.Profile.Duplicate.Account.Visit.Support.page, style: .primary) { [weak self, weak coordinator] in
            guard let self = self, let coordinator = coordinator else { return }
            self.router.dismissModule(animated: true) {
                self.removeChild(coordinator)
                self.showSupportPageScreen()
            }
        }

        alertViewModel.addAction(visitSupportAction)

        addChild(coordinator)
        coordinator.start()
    }

    func showSupportPageScreen() {
        guard let link = Constants.WebView.duplicateAccount.url else { return }
        showWebView(link: link)
    }

    func showAboutVerifiedSellerScreen() {
        let viewController = StoryboardScene.Others.mayaAboutVerifiedSellerViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        router.present(viewController, animated: true, completion: nil)
    }

    func startAuthorizeRequestsFlow() {
        let coordinator = PushApprovalCoordinator(presenter: router)
        coordinator.presentationDirection = .right
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startSecurityCenterFlow() {
        let coordinator = MayaSecurityCenterCoordinator(presenter: router)
        coordinator.presentationDirection = .right
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func showMGMV2ForceUpdateScreen() {
        let coordinator = MGMV2UpdaterCoordinator(router: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }
}

// MARK: MayaProfileViewControllerDelegate methods
extension MayaProfileCoordinator: MayaProfileViewControllerDelegate {
    func didTapProfileQr(_ viewController: MayaProfileViewController) {
        startProfileQRFlow()
    }

    func didTapQuickGuide(_ viewController: MayaProfileViewController) {
        showQuickGuideScreen()
    }

    func didTapFavorites(_ viewController: MayaProfileViewController) {
        startFavoritesFlow()
    }

    func didTapMissions(_ viewController: MayaProfileViewController) {
        startMissionsFlow()
    }

    func didTapVouchers(_ viewController: MayaProfileViewController) {
        startVouchersFlow()
    }

    func didTapSubmitInviteCode(_ viewController: MayaProfileViewController) {
        startReferralInputCodeFlow()
    }

    func didTapAccountLimits(_ viewController: MayaProfileViewController) {
        startAccountLimitsFlow()
    }

    func didTapGetHelp(_ viewController: MayaProfileViewController) {
        showHelpScreen()
    }

    func didTapSettings(_ viewController: MayaProfileViewController) {
        startSettingsFlow()
    }

    func didTapUpgradeAccount(_ viewController: MayaProfileViewController) {
        startKYCFlow()
    }

    func didTapUpdateAccount(_ viewController: MayaProfileViewController) {
        startReKYCFlow()
    }

    func didTapReviewApplication(_ viewController: MayaProfileViewController) {
        startReviewApplicationFlow()
    }

    func didTapReviewEDD(_ viewController: MayaProfileViewController) {
        startReviewEDDFlow()
    }

    func didTapInviteFriends(_ viewController: MayaProfileViewController) {
        if configurationServiceV2.mgmV2ForceUpdateEnabled.value {
            showMGMV2ForceUpdateScreen()
        } else {
            startReferralShareCodeFlow()
        }
    }

    func didTapVisitSupportPage(_ viewController: MayaProfileViewController) {
        showSupportPageScreen()
    }

    func didTapDuplicateAccount(_ viewController: MayaProfileViewController) {
        showDuplicateAccountAlert()
    }

    func didTapVerifiedSeller(_ viewController: MayaProfileViewController) {
        showAboutVerifiedSellerScreen()
    }

    func didTapCreateUsername(_ viewController: MayaProfileViewController) {
        let usernameCoordinator = MayaSetUsernameCoordinator(presenter: router)
        usernameCoordinator.routeActionDelegate = self
        usernameCoordinator.backActionDelegate = self
        usernameCoordinator.delegate = self
        addChild(usernameCoordinator)
        usernameCoordinator.start()
    }

    func didTapAuthorizeRequests(_ viewController: MayaProfileViewController) {
        startAuthorizeRequestsFlow()
    }

    func didTapRateThisApp(_ viewController: MayaProfileViewController) {
        if let mayaAppStoreURL = Constants.ExternalURLs.mayaAppStoreLink.url {
            UIApplication.shared.open(mayaAppStoreURL)
        }
    }

    func didTapSecurityCenter(_ viewController: MayaProfileViewController) {
        startSecurityCenterFlow()
    }

    func didTapInviteAFriend(_ viewController: MayaProfileViewController) {
        // TODO: Rubeus Team - Add flow implementation in later MR
    }

    func didTapAdBanner(url: URL, _ viewController: MayaProfileViewController) {
        routeActionDelegate?.didRequestStartDeepLink(self, with: url) { [weak self] success in
            if !success {
                self?.showWebView(link: url, willPresent: true)
            }
        }
    }
}

// MARK: MayaSetUsernameCoordinatorDelegate methods
extension MayaProfileCoordinator: MayaSetUsernameCoordinatorDelegate {
    func didDismissCoordinator(coordinator: MayaSetUsernameCoordinator) {
        removeChild(coordinator)
    }
}

// MARK: MayaReferralInputCodeCoordinatorDelegate methods
extension MayaProfileCoordinator: MayaReferralInputCodeCoordinatorDelegate {
    func didTapUpgradeAccount(_ coordinator: MayaReferralInputCodeCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.startKYCFlow()
        }
    }

    func didTapReviewEDD(_ coordinator: MayaReferralInputCodeCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.startReviewEDDFlow()
        }
    }

    func didTapReviewApplication(_ coordinator: MayaReferralInputCodeCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.startReviewApplicationFlow()
        }
    }

    func didRequestRedirectToDashboard(_ coordinator: MayaReferralInputCodeCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            guard let self = self,
                let url = Constants.DeepLinkPath.dashboard.url else {
                return
            }
            self.removeChild(coordinator)
            self.routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
        }
    }
}

// MARK: MayaAccountLimitsCoordinatorDelegate methods
extension MayaProfileCoordinator: MayaAccountLimitsCoordinatorDelegate {
    func didFinishAccountLimits(_ coordinator: MayaAccountLimitsCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            self?.removeChild(coordinator)
            self?.startCashInFlow()
        }
    }
}

// MARK: EDDReviewCoordinatorDelegate Methods
extension MayaProfileCoordinator: EDDReviewCoordinatorDelegate {
    func didReceiveError(_ error: PayMayaError, coordinator: EDDReviewCoordinator) {
        self.removeChild(coordinator)
        if let errorViewModel = error.viewModel {
            if case .sessionTimeout = error.type {
                // Do nothing
            } else {
                showErrorAlert(errorViewModel, useMayaModal: true)
            }
        }
    }
}

// MARK: EKYCZolozCoordinatorDelegate methods
extension MayaProfileCoordinator: EKYCZolozCoordinatorDelegate {
    func dismissCoordinator(_ coordinator: Coordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
        })
    }

    func dismissAndPushProfileCoordinator(_ coordinator: Coordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
        })
    }

    func sessionTimeoutErrorOccured(_ coordinator: EKYCZolozCoordinator) {
        self.removeChild(coordinator)
    }
}

// MARK: MayaInboxSupportCoordinatorDelegate methods
extension MayaProfileCoordinator: MayaInboxSupportCoordinatorDelegate {
    func didDismiss(coordinator: MayaInboxSupportCoordinator) {
        self.removeChild(coordinator)
    }
}

// MARK: MayaSecurityCenterCoordinatorDelegate Methods
extension MayaProfileCoordinator: MayaSecurityCenterCoordinatorDelegate {
    func didDismiss(coordinator: MayaSecurityCenterCoordinator) {
        removeChild(coordinator)
    }

    func didSetSourceAttributes(coordinator: MayaSecurityCenterCoordinator) -> Array<String> {
        return [AnalyticsConstants.SourcePage.profile.rawValue, AnalyticsConstants.Screen.SecurityCenter.Button.securityCenterButton.rawValue]
    }
}
