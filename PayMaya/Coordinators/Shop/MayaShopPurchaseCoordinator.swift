//
//  MayaShopPurchaseCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 10/05/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
import AssetProvider
import ConfigurationService
import Error
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import StoreProvider
import UIKit

private let shopReceipt = "SHOP_RECEIPT"

protocol MayaShopPurchaseCoordinatorDelegate: AnyObject {
    func didCancelOrder(coordinator: MayaShopPurchaseCoordinator)
    func didSuccessfullyOrder(coordinator: MayaShopPurchaseCoordinator)
    func didFailOrder(coordinator: MayaShopPurchaseCoordinator)
}

class MayaShopPurchaseCoordinator: MayaProgressBarCoordinator, MayaReceiptCoordinatorDelegate, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    private let mode: ShopFlowMode

    override var progressSegments: Int {
        return 2
    }

    private var productId: String?
    private var productType: String?
    private var viewModel: MayaShopPaymentViewModelProtocol?
    private var productDetailsAction: Action<(String, String?), ProductItem?, PayMayaError>!
    private var prefilledFields: [String: String]?
    private let widgetName: String?
    private let analyticsSourcePage: AnalyticsConstants.SourcePage?

    @Inject var apiProvider: APIProvider
    @Inject var storeProvider: StoreProvider
    @Inject var configurationService: ConfigurationService

    weak var delegate: MayaShopPurchaseCoordinatorDelegate?

    init(
        presenter: RouterProtocol? = nil,
        productId: String,
        productType: String? = nil,
        prefilledFields: [String: String]? = nil,
        mode: ShopFlowMode = .normal,
        widgetName: String?,
        analyticsSourcePage: AnalyticsConstants.SourcePage? = nil
    ) {
        self.mode = mode
        self.productId = productId
        self.productType = productType
        self.prefilledFields = prefilledFields
        self.widgetName = widgetName
        self.analyticsSourcePage = analyticsSourcePage
        super.init(presenter: presenter)

        productDetailsAction = Action { [weak self] productID, productType in
            guard let self = self else {
                return PayMayaError.validationError(title: L10n.Error.Shop.title)
            }
            let request = ProductDetailsRequest(productType: productType)
            let requestProducer = self.apiProvider.reactive.request(API.Shop.productDetails(productID, request))
                .map { [weak self] _ in
                    return self?.fetchProductDetails(productId: productID)
                }
            return requestProducer.mapMayaError(title: CommonStrings.Maya.Error.Default.title)
        }
    }

    override func start(completion: (() -> Void)? = nil) {
        guard let id = self.productId else { return }
        productDetailsAction.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                guard let self = self else { return }
                let presenter = self.presenter == nil ? self.router.toPresentable() : self.presenter?.toPresentable()
                isExecuting ? presenter?.showMayaLoader() : presenter?.hideMayaLoader()
            }?.addToDisposeBag(disposeBag)

        productDetailsAction.values.signal
            .filter { $0 != nil }
            .map { $0! }
            .skipRepeats()
            .observe(on: UIScheduler())
            .observeValues { [weak self] product in
                guard let self = self else { return }
                self.showPaymentScreen(product: product, completion: completion)
            }?.addToDisposeBag(disposeBag)

        productDetailsAction.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self = self else { return }
                let presenter = self.presenter == nil ? self.router.rootViewController : self.presenter?.rootViewController
                if let errorViewModel = error.viewModel {
                    (presenter as? ViewController)?.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
                }
                self.delegate?.didCancelOrder(coordinator: self)
            }?.addToDisposeBag(disposeBag)

        productDetailsAction.apply((id, self.productType)).start()
    }

    func didTapAddToFavorites(_ coordinator: MayaReceiptCoordinator) {
        // Do nothing
    }
}

// MARK: - Helper Methods
private extension MayaShopPurchaseCoordinator {
    func showPaymentScreen(product: ProductItem, completion: (() -> Void)? = nil) {
        let viewController = StoryboardScene.Shop.mayaShopPaymentViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.delegate = self
        viewController.routeActionDelegate = self
        viewController.analyticsSourcePage = analyticsSourcePage
        viewController.title = L10n.Shop.Purchase.Title.buy

        let viewModel = MayaShopPaymentViewModel(product: product, prefilledFields: prefilledFields, mode: mode, widgetName: widgetName)
        self.viewModel = viewModel
        viewController.bind(viewModel)
        if let presenter = self.presenter {
            setRootModule(viewController, animated: false)
            presenter.present(router, animated: true, completion: completion)
        } else {
            router.push(viewController, animated: true, popCompletion: completion)
        }
    }

    func startContactsFlow() {
        let contactsCoordinator = MayaContactsCoordinator(presenter: router)
        contactsCoordinator.backActionDelegate = self
        contactsCoordinator.routeActionDelegate = self
        contactsCoordinator.delegate = self
        contactsCoordinator.presentationDirection = .right
        addChild(contactsCoordinator)
        contactsCoordinator.start()
    }

    func fetchProductDetails(productId: String) -> ProductItem? {
        let predicate = NSPredicate(format: "id == %@", productId)
        let options = DatabaseStore.ReadingOptions(DatabaseEntity.product, predicate: predicate)
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
            let product = databaseStore.read(options).value as? Product
            else { return nil }

        return product.productItem
    }

    func showMayaReceipt(viewModel: MayaReceiptViewModelProtocol) {
        let coordinator = MayaReceiptCoordinator(presenter: router, viewModel: viewModel, attributeModule: .shop, customAnalyticsModule: shopReceipt)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func logContactsViewed(isContactPermissionAllowed: Bool) {
        if let viewModel = viewModel, viewModel.isEcommAppEventV2ShopEnabled {
            analyticsService.logMayaEvents(
                AnalyticsConstants.Screen.Shop.contacts.viewed(),
                attributes: [
                    .sourcePage: AnalyticsConstants.SourcePage.productDetails.rawValue,
                    .grantedPermission: isContactPermissionAllowed
                ]
            )
        }
    }

    func logContactNameTapped() {
        if let viewModel = viewModel, viewModel.isEcommAppEventV2ShopEnabled {
            analyticsService.logMayaEvents(AnalyticsConstants.Screen.Shop.contactName.tapped())
        }
    }
}

// MARK: - DeepLinkHandler Methods
extension MayaShopPurchaseCoordinator: DeepLinkHandler {
    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .shop(.product):
            return .opened(deepLink, nil)
        default:
            return .rejected(deepLink, .notHandled, identifier)
        }
    }
}

// MARK: - MayaCheckoutCoordinatorDelegate
extension MayaShopPurchaseCoordinator: MayaCheckoutCoordinatorDelegate {
    func didComplete(_ coordinator: MayaCheckoutCoordinator, receiptViewModel: MayaReceiptViewModelProtocol) {
        self.removeChild(coordinator)
        self.showMayaReceipt(viewModel: receiptViewModel)
        self.delegate?.didSuccessfullyOrder(coordinator: self)
    }

    func didCompleteNoReceipt(_ coordinator: MayaCheckoutCoordinator) {
        self.removeChild(coordinator)
        self.delegate?.didSuccessfullyOrder(coordinator: self)

        guard let dashboardURL = Constants.DeepLinkPath.dashboard.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: dashboardURL, completion: nil)
    }

    func didDismiss(_ coordinator: MayaCheckoutCoordinator, error: PayMayaError?, needsKYCUpgrade: Bool = false) {
        if error != nil {
            if let errorViewModel = error?.viewModel {
                showErrorAlert(errorViewModel, useMayaModal: true)
            }
        }

        self.removeChild(coordinator)

        if needsKYCUpgrade {
            showPreKYCFlow()
        }
    }
}

extension MayaShopPurchaseCoordinator: MayaShopPaymentViewControllerDelegate {
    func didCreateAndExecuteOrder(response: ShopCreateAndExecuteResponse, _ viewController: MayaShopPaymentViewController) {
        let viewModel = viewModel?.getMayaCheckoutViewModel(response: response)
        let checkoutCoordinator = MayaCheckoutCoordinator(presenter: router, viewModel: viewModel!)
        checkoutCoordinator.backActionDelegate = self
        checkoutCoordinator.routeActionDelegate = self
        checkoutCoordinator.delegate = self
        addChild(checkoutCoordinator)
        checkoutCoordinator.start()
    }

    func didCreateOrder(orderID: String, _ viewController: MayaShopPaymentViewController) {
        previousViewController = viewController
        let viewController = StoryboardScene.Shop.mayaShopPaymentConfirmationViewController.instantiate()
        let confirmationViewModel = viewModel?.getConfirmationViewModel(with: orderID)
        viewController.bind(confirmationViewModel)
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.navigationBarTitleView = progressBarView
        router.push(viewController, animated: false, popCompletion: nil)
    }

    func didCancelOrder(viewController: MayaShopPaymentViewController) {
        delegate?.didCancelOrder(coordinator: self)
    }

    func didTapShowAllContacts(viewController: MayaShopPaymentViewController) {
        startContactsFlow()
    }
}

// MARK: - MayaShopPaymentConfirmationViewControllerDelegate
extension MayaShopPurchaseCoordinator: MayaShopPaymentConfirmationViewControllerDelegate {
    func didSuccessfullyOrder(_ viewController: MayaShopPaymentConfirmationViewController) {
        delegate?.didSuccessfullyOrder(coordinator: self)
    }

    func didFailOrder(_ viewController: MayaShopPaymentConfirmationViewController) {
        delegate?.didFailOrder(coordinator: self)
    }

    func didRequestWebView(url: URL, viewController: MayaShopPaymentConfirmationViewController) {
        super.didRequestWebView(self.router.toPresentable(), with: url, willPresent: false, completion: nil)
    }

    func showSuccessReceipt(_ viewController: MayaShopPaymentConfirmationViewController, viewModel: MayaReceiptViewModelProtocol) {
        showMayaReceipt(viewModel: viewModel)
    }
}

// MARK: - MayaContactsCoordinatorDelegate Methods
extension MayaShopPurchaseCoordinator: MayaContactsCoordinatorDelegate {
    func didSelectContactNumber(_ coordinator: MayaContactsCoordinator, contactNumber: ContactNumber) {
        logContactNameTapped()
        viewModel?.selectedContactNumberProperty.value = contactNumber
    }

    func didTapBack(_ coordinator: MayaContactsCoordinator, isContactPermissionAllowed: Bool) {
        logContactsViewed(isContactPermissionAllowed: isContactPermissionAllowed)
    }
}

// MARK: MayaPreKYCCoordinatorDelegate Methods
extension MayaShopPurchaseCoordinator: MayaPreKYCCoordinatorDelegate {
    private func showPreKYCFlow() {
        let coordinator = MayaPreKYCCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        coordinator.routeActionDelegate = self
        coordinator.analyticsModule = Analytics.LoansDashboard()
        addChild(coordinator)
        coordinator.start()
    }

    func preKYCCoordinatorDidFinishKYC(_ coordinator: MayaPreKYCCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
    }

    func preKYCCoordinatorDidTapBack(_ coordinator: MayaPreKYCCoordinator) {
        removeChild(coordinator)
    }
}
