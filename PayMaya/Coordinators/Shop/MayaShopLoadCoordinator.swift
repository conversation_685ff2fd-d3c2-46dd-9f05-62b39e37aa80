//
//  MayaShopLoadCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 3/13/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
import AssetProvider
import ConfigurationService
import Flutter
import FlutterPluginRegistrant
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider
import UIKit

protocol MayaShopLoadCoordinatorDelegate: AnyObject {
    func didComplete(_ coordinator: MayaShopLoadCoordinator)
    func didDismiss(_ coordinator: MayaShopLoadCoordinator, error: PayMayaError?)
}

class MayaShopLoadCoordinator: Coordinator, AnalyticsProtocol {
    private enum CoordinatorConstants {
        static let flutterModule = FlutterModule.loadShop
    }

    @Inject var storeProvider: StoreProvider
    @Inject private var flutterEngineManager: FlutterEngineManager
    @Inject var configurationService: ConfigurationService
    @Inject var configurationServiceV2: ConfigurationServiceV2

    private var loadFullscreenViewController: FlutterViewController?

    weak var delegate: MayaShopLoadCoordinatorDelegate?
    let viewModel: MayaShopLoadViewModelProtocol

    var params: String?

    var sessionTimeoutResult: FlutterResult?

    private var initialRoute: String {
        var initialRoute = Constants.Flutter.Load.entryPoint
        if let params, !params.isEmpty {
            initialRoute += "?\(params)"
        }
        return initialRoute
    }

    init(
        params: String? = nil,
        router: RouterProtocol = Router(),
        presenter: RouterProtocol? = nil,
        viewModel: MayaShopLoadViewModelProtocol
    ) {
        self.params = params
        self.viewModel = viewModel

        super.init(router: router, presenter: presenter)
    }

    override func start(completion: (() -> Void)? = nil) {
        flutterEngineManager.startEngine(with: CoordinatorConstants.flutterModule, customRoute: initialRoute)
        setMethodHandler()
        if let engine = flutterEngineManager.getEngine(with: CoordinatorConstants.flutterModule) {
            loadFullscreenViewController = FlutterViewController(engine: engine, nibName: nil, bundle: nil)

            if let controller = loadFullscreenViewController {
                // set background to white for now
                controller.view.backgroundColor = CommonAsset.MayaColors.Background.backgroundPrimaryWhite.color
                presenter?.present(controller, animated: false, completion: completion)
            }
        }
    }

    private func setMethodHandler() {
        flutterEngineManager.getChannel(with: CoordinatorConstants.flutterModule)?.setMethodCallHandler { [weak self] call, result in
            guard let self = self,
                  let method = FlutterMethod(rawValue: call.method)
            else { return result(nil) }

            switch method {
            case .getSessionToken:
                guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
                      let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                      let token = user.token
                else {
                    return
                }
                result(self.flutterEngineManager.responseGenerator.getSessionToken(token))
            case .getAccessToken:
                guard let encryptedStore = self.storeProvider.target(EncryptedDefaultsStore.self),
                      let authorization = encryptedStore.read(EncryptedDefaultsStoreId.authorization, type: Authorization.self).value
                else {
                    return
                }
                result(self.flutterEngineManager.responseGenerator.getAccessToken(authorization.accessToken))
            case .getShieldSessionId:
                result(self.flutterEngineManager.responseGenerator.getShieldSessionId(Shield.shared().sessionId))
            case .onSessionTimeout:
                self.sessionTimeoutResult = result
                NotificationCenter.default.post(name: NSNotification.Name(rawValue: sessionTimeoutNotification), object: nil)
            case .complete:
                self.dismiss(error: nil) {
                    self.delegate?.didComplete(self)
                }
            case .dismiss:
                self.dismiss(error: nil)
            case .getPinningFingerprint:
                if let propertyListStore = self.storeProvider.target(PropertyListStore.self),
                   let pinningFingerprint = propertyListStore.read(PropertyListStoreId.pinningFingerprint).value as? String {
                    result(self.flutterEngineManager.responseGenerator.getPinningFingerprint(pinningFingerprint))
                }
            case .getToggle:
                let togglesV1 = flutterEngineManager.responseGenerator.getToggles(from: [
                    FlutterToggles.loadV2Favorites: configurationService.loadV2FavoritesEnabled,
                    FlutterToggles.loadV2Recommender: configurationService.loadV2RecommenderEnabled
                ])
                let togglesV2 = flutterEngineManager.responseGenerator.getTogglesV2(from: [
                    FlutterToggles.loadV2MinDetection: configurationServiceV2.loadMinDetectionEnabled
                ])

                let mergedToggles = togglesV1.merged(with: togglesV2)

                result(mergedToggles)
            case .getMin:
                guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
                      let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                      let msisdn = user.msisdn
                else {
                    return
                }
                result(self.flutterEngineManager.responseGenerator.getMin(msisdn))
            case .getKycStatus:
                if let databaseStore = self.storeProvider.target(DatabaseStore.self),
                   let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User {
                    result(self.flutterEngineManager.responseGenerator.getKycStatus(kycStatus: user.kycStatus))
                } else {
                    result(self.flutterEngineManager.responseGenerator.getKycStatus(kycStatus: "kyc0"))
                }
            case .showUpgradeScreen:
                self.showPreKYCFlow()
            case .getAvailability:
                let availability = [ FlutterAvailability.load ]

                result(self.flutterEngineManager.responseGenerator.getAvailability(from: availability))
            case .requestCheckout:
                if let args = call.arguments as? Dictionary<String, Any>,
                   let loadPaymentId = args[Constants.Flutter.Checkout.paymentId] as? String,
                   let viewController = self.loadFullscreenViewController {
                    self.setUpCheckoutFullScreen(
                        paymentId: loadPaymentId,
                        viewController: viewController
                    )
                }
            case .logAnalyticsEvent:
                guard let jsonString = call.arguments as? String,
                      let jsonData = jsonString.data(using: .utf8),
                      let dictionary = try? JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
                      let name = dictionary["event_name"] as? String
                else { return }

                let attributes = dictionary["attributes"] as? [String: Any]
                self.analyticsUtils.logMayaEvents(name, attributes: attributes)
            default: result(nil)
            }
        }
    }

    private func setUpCheckoutFullScreen(
        paymentId: String,
        viewController: FlutterViewController
    ) {
        let coordinator = MayaShopLoadCheckoutCoordinator(
            presenter: router,
            viewController: viewController,
            paymentId: paymentId
        )
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    private func dismiss(error: PayMayaError?, completion: (() -> Void)? = nil) {
        self.presenter?.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }
            self.flutterEngineManager.getChannel(with: CoordinatorConstants.flutterModule)?.setMethodCallHandler(nil)
            self.flutterEngineManager.clearEngine(with: CoordinatorConstants.flutterModule)
            self.flutterEngineManager.initializeEngine(with: CoordinatorConstants.flutterModule)
            self.delegate?.didDismiss(self, error: error)
            completion?()
        }
    }
}

// MARK: - MayaShopLoadCheckoutCoordinatorDelegate Methods
extension MayaShopLoadCoordinator: MayaShopLoadCheckoutCoordinatorDelegate {
    func didDismiss(coordinator: MayaShopLoadCheckoutCoordinator) {
        removeChild(coordinator)
    }
}

// MARK: MayaPreKYCCoordinatorDelegate Methods
extension MayaShopLoadCoordinator: MayaPreKYCCoordinatorDelegate {
    private func showPreKYCFlow() {
        let coordinator = MayaPreKYCCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        coordinator.routeActionDelegate = self
        coordinator.analyticsModule = Analytics.LoansDashboard()
        addChild(coordinator)
        coordinator.start()
    }

    func preKYCCoordinatorDidFinishKYC(_ coordinator: MayaPreKYCCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
    }

    func preKYCCoordinatorDidTapBack(_ coordinator: MayaPreKYCCoordinator) {
        removeChild(coordinator)
    }
}

extension MayaShopLoadCoordinator: DeepLinkHandler {
    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .dashboard(.load):
            return .opened(deepLink, nil)
        default: return .rejected(deepLink, .notHandled, identifier)
        }
    }
}
