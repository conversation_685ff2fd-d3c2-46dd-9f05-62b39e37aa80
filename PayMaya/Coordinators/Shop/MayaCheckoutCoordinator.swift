//
//  MayaCheckoutCoordinator.swift
//  PayMaya
//
//  Created by john.cinco on 7/12/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import AssetProvider
import ConfigurationService
import Flutter
import FlutterPluginRegistrant
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider
import UIKit

protocol MayaCheckoutCoordinatorDelegate: AnyObject {
    func didComplete(_ coordinator: <PERSON><PERSON>heckoutCoordinator, receiptViewModel: MayaReceiptViewModelProtocol)
    func didCompleteNoReceipt(_ coordinator: <PERSON><PERSON>heckoutCoordinator)
    func didDismiss(_ coordinator: <PERSON><PERSON><PERSON>ckoutCoordinator, error: PayMayaError?, needsKYCUpgrade: Bool)
}

class MayaCheckoutCoordinator: Coordinator, AnalyticsProtocol {
    @Inject var storeProvider: StoreProvider
    @Inject private var flutterEngineManager: FlutterEngineManager
    @Inject var configurationService: ConfigurationService

    weak var delegate: MayaCheckoutCoordinatorDelegate?
    let viewModel: MayaCheckoutViewModelProtocol
    private let paymentId: String
    var sessionTimeoutResult: FlutterResult?
    init(router: RouterProtocol = Router(), presenter: RouterProtocol? = nil, viewModel: MayaCheckoutViewModelProtocol) {
        self.viewModel = viewModel
        self.paymentId = viewModel.response.paymentId ?? ""
        super.init(router: router, presenter: presenter)

        viewModel.verifyOrderAction.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                guard let self = self else { return }
                let presenter = self.presenter == nil ? self.router.toPresentable() : self.presenter?.toPresentable()
                isExecuting ? presenter?.showMayaLoader() : presenter?.hideMayaLoader()
            }?.addToDisposeBag(disposeBag)

        viewModel.verifyOrderAction.values
            .observe(on: UIScheduler())
            .observeValues({[weak self] receiptViewModel in
                guard let self = self else { return }
                self.dismiss(error: nil) {
                    if receiptViewModel.status != OrderResponse.paymentFailedStatus {
                        self.delegate?.didComplete(self, receiptViewModel: receiptViewModel)
                    }
                }
            })?.addToDisposeBag(self.disposeBag)

        viewModel.verifyOrderAction.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self = self else { return }
                self.dismiss(error: error)
            }?.addToDisposeBag(self.disposeBag)
    }

    override func start(completion: (() -> Void)? = nil) {
        flutterEngineManager.startEngine(with: .checkout)
        setMethodHandler()
        if let engine = flutterEngineManager.getEngine(with: .checkout) {
            let flutterViewController = FlutterViewController(engine: engine, nibName: nil, bundle: nil)
            let args = [Constants.Flutter.Checkout.route: Constants.Flutter.Checkout.buyLoadScreen,
                        Constants.Flutter.Checkout.paymentId: paymentId]
            // set background to white for now
            flutterViewController.view.backgroundColor = CommonAsset.MayaColors.Background.backgroundPrimaryWhite.color
            flutterEngineManager.getChannel(with: .checkout)?.invokeMethod(FlutterMethod.requestFullscreen.rawValue, arguments: args ) { [weak self] _ in
                self?.presenter?.present(flutterViewController, animated: false, completion: completion)
            }
        }
    }

    private func setMethodHandler() {
        flutterEngineManager.getChannel(with: .checkout)?.setMethodCallHandler { [weak self] call, result in
            guard let self = self,
                  let method = FlutterMethod(rawValue: call.method)
            else { return result(nil) }

            switch method {
            case .getSessionToken:
                guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
                      let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                      let token = user.token
                else {
                    return
                }
                result(self.flutterEngineManager.responseGenerator.getSessionToken(token))
            case .getAccessToken:
                guard let encryptedStore = self.storeProvider.target(EncryptedDefaultsStore.self),
                      let authorization = encryptedStore.read(EncryptedDefaultsStoreId.authorization, type: Authorization.self).value
                else {
                    return
                }
                result(self.flutterEngineManager.responseGenerator.getAccessToken(authorization.accessToken))
            case .getShieldSessionId:
                result(self.flutterEngineManager.responseGenerator.getShieldSessionId(Shield.shared().sessionId))
            case .onSessionTimeout:
                self.sessionTimeoutResult = result
                NotificationCenter.default.post(name: NSNotification.Name(rawValue: sessionTimeoutNotification), object: nil)
            case .complete:
                guard let arguments: [String: Any] = call.arguments as? Dictionary<String, Any>,
                      let showReceipt: Bool = arguments[FlutterArgument.showReceipt.rawValue] as? Bool,
                      showReceipt
                else {
                    dismiss(error: nil) {
                        self.delegate?.didCompleteNoReceipt(self)
                    }
                    return
                }

                viewModel.verifyOrderAction.apply().start()
            case .dismiss:
                self.dismiss(error: nil)
            case .getPinningFingerprint:
                if let propertyListStore = self.storeProvider.target(PropertyListStore.self),
                   let pinningFingerprint = propertyListStore.read(PropertyListStoreId.pinningFingerprint).value as? String {
                    result(self.flutterEngineManager.responseGenerator.getPinningFingerprint(pinningFingerprint))
                }
            case .getToggle:
                let toggles = [
                    FlutterToggles.simplifiedCheckout: self.configurationService.mayaLoansSimplifiedCheckoutEnabled,
                    FlutterToggles.loansV3: self.configurationService.mayaLoansV3Enabled,
                    FlutterToggles.loansICL: self.configurationService.mayaLoansICLEnabled
                ]

                result(self.flutterEngineManager.responseGenerator.getToggles(from: toggles))
            case .getMin:
                guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
                      let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                      let msisdn = user.msisdn
                else {
                    return
                }
                result(self.flutterEngineManager.responseGenerator.getMin(msisdn))
            case .getKycStatus:
                if let databaseStore = self.storeProvider.target(DatabaseStore.self),
                   let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User {
                    result(self.flutterEngineManager.responseGenerator.getKycStatus(kycStatus: user.kycStatus))
                } else {
                    result(self.flutterEngineManager.responseGenerator.getKycStatus(kycStatus: "kyc0"))
                }
            case .showUpgradeScreen:
                self.dismiss(error: nil, needsKYCUpgrade: true)
            case .logAnalyticsEvent:
                guard let jsonString = call.arguments as? String,
                      let jsonData = jsonString.data(using: .utf8),
                      let dictionary = try? JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
                      let name = dictionary["event_name"] as? String
                else { return }

                let attributes = dictionary["attributes"] as? [String: Any]
                self.analyticsUtils.logMayaEvents(name, attributes: attributes)
            default: result(nil)
            }
        }
    }

    private func dismiss(error: PayMayaError?, completion: (() -> Void)? = nil, needsKYCUpgrade: Bool = false) {
        self.presenter?.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }
            self.flutterEngineManager.getChannel(with: .checkout)?.setMethodCallHandler(nil)
            self.flutterEngineManager.clearEngine(with: .checkout)
            self.flutterEngineManager.initializeEngine(with: .checkout)
            self.delegate?.didDismiss(self, error: error, needsKYCUpgrade: needsKYCUpgrade)
            completion?()
        }
    }
}
