//
//  MayaCreditUpdatePersonalDetailsCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 11/8/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import Contacts
import Foundation

class MayaCreditUpdatePersonalDetailsCoordinator: MayaProgressBarCoordinator {
    private let eligibilityTerm: MayaCreditEligibility.Term?
    private var billingEndDateSelection: BillingEndDateSelection?
    private var personalDetailsSelection: PersonalDetailsSelection?
    private var feeRate: Float?
    private var creditAccount: MayaCreditAccount
    private var personalDetailsState: PersonalDetailsState?
    private var viewModel: NewMayaCreditActivationViewModel

    init(presenter: RouterProtocol? = nil, eligibilityTerm: MayaCreditEligibility.Term?, feeRate: Float?, creditAccount: MayaCreditAccount, personalDetailsState: PersonalDetailsState? = nil) {
        self.eligibilityTerm = eligibilityTerm
        self.feeRate = feeRate
        self.creditAccount = creditAccount
        self.personalDetailsState = personalDetailsState
        self.viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: eligibilityTerm, feeRate: feeRate, creditAccount: creditAccount, personalDetailsState: personalDetailsState)
        super.init(presenter: presenter)
    }

    override func start(completion: (() -> Void)? = nil) {
        let viewController = StoryboardScene.MayaCredit.mayaCreditUpdatePersonalDetailsViewController.instantiate()
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        if personalDetailsState == nil {
            viewController.navigationBarTitleView = progressBarView
        }
        viewController.bind(viewModel)
        setRootModule(viewController, animated: false)
        presenter?.present(router, animated: true, completion: completion)
    }

    override var progressSegments: Int {
        return 3
    }
}

// MARK: - MayaCreditActivationViewControllerDelegate
extension MayaCreditUpdatePersonalDetailsCoordinator: MayaCreditUpdatePersonalDetailsViewControllerDelegate {
    func didTapGender(_ viewController: MayaCreditUpdatePersonalDetailsViewController, personalDetailsSelection: PersonalDetailsSelection) {
        self.personalDetailsSelection = personalDetailsSelection
        showGenderPicker()
    }

    func didTapMaritalStatus(_ viewController: MayaCreditUpdatePersonalDetailsViewController, personalDetailsSelection: PersonalDetailsSelection) {
        self.personalDetailsSelection = personalDetailsSelection
        showMaritalStatusPicker()
    }

    func didTapContinue(_ viewController: MayaCreditUpdatePersonalDetailsViewController) {
        if viewModel.personalDetailsState != nil {
            viewModel.submitContactReference.apply().start()
        } else {
            previousViewController = viewController
            showAssignContactReference()
        }
    }

    func didSuccessfullySubmitted(_ viewController: MayaCreditUpdatePersonalDetailsViewController) {
        guard let mayaCreditURL = Constants.DeepLinkPath.mayaCredit.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: mayaCreditURL, completion: nil)
    }
}

private extension MayaCreditUpdatePersonalDetailsCoordinator {
    // 2nd screen
    func showAssignContactReference() {
        let viewController = StoryboardScene.MayaCredit.mayaCreditAssignContactReferenceViewController.instantiate()
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.navigationBarTitleView = progressBarView
        viewController.bind(viewModel.assignContactReferenceViewModel)
        router.push(viewController, animated: false, popCompletion: nil)
    }

    // 3rd screen
    func showConfirmDetails() {
        let viewController = StoryboardScene.MayaCredit.mayaCreditConfirmDetailsViewController.instantiate()
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.navigationBarTitleView = progressBarView
        viewController.bind(viewModel)
        router.push(viewController, animated: false, popCompletion: nil)
    }

    func showGenderPicker() {
        guard personalDetailsSelection != nil else { return }
        let viewModel = MayaCreditBottomsheetSelectorViewModel(isCancelButtonHidden: true)
        let viewController = StoryboardScene.MayaCredit.mayaCreditGenderSelectorViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        if self.viewModel.personalDetailsState != nil {
            viewController.bind(viewModel)
        }
        router.present(viewController, animated: false, completion: nil)
    }

    func showMaritalStatusPicker() {
        guard personalDetailsSelection != nil else { return }
        let viewModel = MayaCreditBottomsheetSelectorViewModel(isCancelButtonHidden: true)
        let viewController = StoryboardScene.MayaCredit.mayaCreditMaritalStatusSelectorViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        if self.viewModel.personalDetailsState != nil {
            viewController.bind(viewModel)
        }
        router.present(viewController, animated: false, completion: nil)
    }

    func showContactRelationshipPicker() {
        guard personalDetailsSelection != nil else { return }
        let viewController = StoryboardScene.MayaCredit.mayaCreditContactRelationshipSelectorViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        router.present(viewController, animated: false, completion: nil)
    }

    func showLearnMoreContactReference() {
        let viewController = StoryboardScene.MayaCredit.mayaCreditLearnMoreContactReferenceViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        router.present(viewController, animated: false, completion: nil)
    }

    func showTermsAndConditions(activationTerm: ActivationTerm?) {
        guard let activationTerm = activationTerm else { return }
        let viewController = StoryboardScene.MayaCredit.newMayaCreditTermDetailsViewController.instantiate()
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.bind(activationTerm.viewModel)
        viewController.setButtonTitle(L10n.Maya.Credit.Signup.Acknowledge.Button.title)
        viewController.setDefaultConfirmed(confirmed: true)
        router.present(viewController, animated: false, completion: nil)
    }

    func showDataPrivacyPolicy() {
        let viewController = StoryboardScene.MayaCredit.mayaCreditDataPrivacyPolicyViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        router.present(viewController, animated: false, completion: nil)
    }

    func showAutomatedPayments() {
        let viewController = StoryboardScene.MayaCredit.mayaCreditAutomatedPaymentsViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        router.present(viewController, animated: false, completion: nil)
    }

    func showContactPermission() {
        let alertController = UIAlertController(title: L10n.Maya.Credit.Contacts.No.Permission.title, message: L10n.Maya.Credit.Contacts.No.Permission.message, preferredStyle: .alert)
        let cancelAction = UIAlertAction(title: CommonStrings.Common.cancel, style: .cancel, handler: { _ in
            alertController.dismiss(animated: true, completion: nil)
        })
        let openSettings = UIAlertAction(title: CommonStrings.Common.Open.settings, style: .default, handler: { _ in
            showExternalURI(URL(string: UIApplication.openSettingsURLString))
        })
        alertController.addAction(cancelAction)
        alertController.addAction(openSettings)
        router.present(alertController, animated: true, completion: nil)
    }
}

// MARK: MayaCreditContactRelationshipSelectorViewControllerDelegate
extension MayaCreditUpdatePersonalDetailsCoordinator: MayaCreditContactRelationshipSelectorViewControllerDelegate {
    func didTapContactRelationship(_ viewController: MayaCreditContactRelationshipSelectorViewController, contactRelationship: Constants.PersonalDetails.ContactRelationship) {
        personalDetailsSelection?.onSelectContactRelationship(contactRelationship)
    }
}

// MARK: - MayaContactsCoordinatorDelegate
extension MayaCreditUpdatePersonalDetailsCoordinator: MayaContactsCoordinatorDelegate {
    func didSelectContactNumber(_ coordinator: MayaContactsCoordinator, contactNumber: ContactNumber) {
        personalDetailsSelection?.onSelectContactNumber(contactNumber.number)
    }

    func didTapBack(_ coordinator: MayaContactsCoordinator, isContactPermissionAllowed: Bool) {
        // Do nothing
    }
}

// MARK: MayaCreditGenderSelectorViewControllerDelegate
extension MayaCreditUpdatePersonalDetailsCoordinator: MayaCreditGenderSelectorViewControllerDelegate {
    func didTapGender(_ viewController: MayaCreditGenderSelectorViewController, gender: Constants.PersonalDetails.Gender) {
        personalDetailsSelection?.onSelectGender(gender)
    }
}

// MARK: MayaCreditMaritalStatusSelectorViewControllerDelegate
extension MayaCreditUpdatePersonalDetailsCoordinator: MayaCreditMaritalStatusSelectorViewControllerDelegate {
    func didTapStatus(_ viewController: MayaCreditMaritalStatusSelectorViewController, status: Constants.PersonalDetails.MaritalStatus) {
        personalDetailsSelection?.onSelectMaritalStatus(status)
    }
}

// MARK: - MayaCreditTermDetailsViewControllerDelegate
extension MayaCreditUpdatePersonalDetailsCoordinator: NewMayaCreditTermDetailsViewControllerDelegate {
    func didTapConfirm(_ viewController: NewMayaCreditTermDetailsViewController) {
        viewController.viewModel?.isConfirmed.value = true
        router.dismissModule(animated: true, completion: nil)
    }
}

// MARK: MayaCreditAssignContactReferenceViewControllerDelegate
extension MayaCreditUpdatePersonalDetailsCoordinator: MayaCreditAssignContactReferenceViewControllerDelegate {
    func didTapLearnMore(_ viewController: MayaCreditAssignContactReferenceViewController) {
        showLearnMoreContactReference()
    }

    func didTapContactRelationship(_ viewController: MayaCreditAssignContactReferenceViewController, personalDetailsSelection: PersonalDetailsSelection) {
        self.personalDetailsSelection = personalDetailsSelection
        showContactRelationshipPicker()
    }

    func didTapMobileNumber(_ viewController: MayaCreditAssignContactReferenceViewController, personalDetailsSelection: PersonalDetailsSelection) {
        if CNContactStore.authorizationStatus(for: .contacts) == .authorized {
            self.personalDetailsSelection = personalDetailsSelection
            let contactsCoordinator = MayaContactsCoordinator(presenter: router)
            contactsCoordinator.shouldIncludeOwnNumber = false
            contactsCoordinator.backActionDelegate = self
            contactsCoordinator.routeActionDelegate = self
            contactsCoordinator.delegate = self
            addChild(contactsCoordinator)
            contactsCoordinator.startContactsV2Flow()
        } else {
            showContactPermission()
        }
    }

    func didTapContinue(_ viewController: MayaCreditAssignContactReferenceViewController) {
        previousViewController = viewController
        showConfirmDetails()
    }
}

// MARK: - MayaCreditConfirmDetailsViewControllerDelegate Methods
extension MayaCreditUpdatePersonalDetailsCoordinator: MayaCreditConfirmDetailsViewControllerDelegate {
    func didTapTermsAndConditions(_ viewController: MayaCreditConfirmDetailsViewController) {
        guard let activationTerm = viewModel.activationTerms.value[safeIndex: PoliciesTypes.termsAndConditions.index] else { return }
        showTermsAndConditions(activationTerm: activationTerm)
    }

    func didTapDisclosureStatement(_ viewController: MayaCreditConfirmDetailsViewController) {
        guard let activationTerm = viewModel.activationTerms.value[safeIndex: PoliciesTypes.disclosureStatement.index] else { return }
        showTermsAndConditions(activationTerm: activationTerm)
    }

    func didTapTruthAndTransparencyInLending(_ viewController: MayaCreditConfirmDetailsViewController) {
        guard let activationTerm = viewModel.activationTerms.value[safeIndex: PoliciesTypes.truthAndTransparency.index] else { return }
        showTermsAndConditions(activationTerm: activationTerm)
    }

    func didTapDataPrivacyPolicy(_ viewController: MayaCreditConfirmDetailsViewController) {
        showDataPrivacyPolicy()
    }

    func didTapAutomatedPayments(_ viewController: MayaCreditConfirmDetailsViewController) {
        showAutomatedPayments()
    }

    func didTapConfirm(_ viewController: MayaCreditConfirmDetailsViewController) {
        viewModel.submitContactReference.apply().start()
    }

    func willShowAllSetScreen(_ viewController: MayaCreditConfirmDetailsViewController, term: MayaCreditEligibility.Term) {
        // Do nothing
    }

    func willShowIneligibleView(_ viewController: MayaCreditConfirmDetailsViewController) {
        // Do nothing
    }

    func willStartCreditScoring(_ viewController: MayaCreditConfirmDetailsViewController, riskLevel: String) {
        // Do nothing
    }
}
