//
//  MayaCreditCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 3/2/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import ConfigurationService
import Foundation
import Injector
import UIKit

class MayaCreditCoordinator: Coordinator {
    @Inject var configurationService: ConfigurationService
    @Inject private var flutterEngineManager: FlutterEngineManager

    let module: Module = .mayaCredit

    private var mothersMaidenNameEntryPoint: Constants.PersonalDetails.EntryPoint?

    override func start(completion: (() -> Void)? = nil) {
        checkMaintenanceMode(completion: { [weak self] in
            completion?()
            guard let self = self else { return }
            /// To allow scrolling pass floating tab bar
            if let navigationController = self.router.toPresentable() as? UINavigationController,
               let serviceMaintenanceViewController = navigationController.visibleViewController as? MayaServiceMaintenanceViewController {
                serviceMaintenanceViewController.view.layoutIfNeeded()
                if serviceMaintenanceViewController.contentView.bounds.size.height >= serviceMaintenanceViewController.view.bounds.size.height - (serviceMaintenanceViewController.contentView.frame.origin.y * 2) - 122 {
                    serviceMaintenanceViewController.bottomConstraint.constant = serviceMaintenanceViewController.bottomConstraint.constant + 107
                }
            }
        })
    }
}

// MARK: - MayaServiceMaintenanceProtocol Methods
extension MayaCreditCoordinator: MayaServiceMaintenanceProtocol {
    func startNormalFlow(completion: (() -> Void)?) {
        let viewController = StoryboardScene.MayaCredit.mayaCreditViewController.instantiate()
        viewController.delegate = self
        viewController.routeActionDelegate = self
        let mayaCreditViewModel = MayaCreditViewModel()
        viewController.bind(mayaCreditViewModel)
        setRootModule(viewController, animated: false)
    }
}

// MARK: MayaCreditViewControllerDelegate Functions
extension MayaCreditCoordinator: MayaCreditViewControllerDelegate {
    func didRequestMayaCreditDiscoveryFlow(_ viewController: MayaCreditViewController, consents: [MayaCreditOptInConsent]) {
        startMayaCreditOptInFlow(consents: consents)
    }

    func didRequestMayaCreditSignUpFlow(_ viewController: MayaCreditViewController, term: MayaCreditEligibility.Term, feeRate: Float?) {
        startMayaCreditSignUpFlow(term: term, feeRate: feeRate)
    }

    func didRequestMayaCreditUpdatePersonalDetailsFlow(_ viewController: MayaCreditViewController, term: MayaCreditEligibility.Term?, feeRate: Float?, account: MayaCreditAccount) {
        startMayaCreditUpdatePersonalDetailsFlow(term: term, feeRate: feeRate, creditAccount: account)
    }

    func didRequestAccountSummaryFlow(_ viewController: MayaCreditViewController, term: MayaCreditCurrentLoanApplication.Term, account: MayaCreditAccount, eligibilityTerm: MayaCreditEligibility.Term?) {
        startMayaCreditSummaryFlow(term: term, creditAccount: account, eligibilityTerm: eligibilityTerm)
    }

    func didRequestCreditSettingsFlow(_ viewController: MayaCreditViewController, term: MayaCreditCurrentLoanApplication.Term, account: MayaCreditAccount) {
        startMayaCreditSettingsFlow(term: term, creditAccount: account)
    }

    func didRequestPayCreditBillFlow(_ viewController: MayaCreditViewController, url: URL) {
        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
    }

    func didRequestCreditDataPrivacy(_ viewController: MayaCreditViewController, delegate: NewMayaCreditDiscoveryDataPrivacyViewControllerDelegate?) {
        showDataPrivacyPolicy()
    }

    func didRequestTransactionFlow(_ viewController: MayaCreditViewController, account: MayaCreditAccount) {
        let coordinator = MayaCreditTransactionsCoordinator(presenter: router, creditAccount: account)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func didRequestBillingStatementsFlow(_ viewController: MayaCreditViewController) {
        // TODO: implement billing statements flow
    }

    func didRequestLatestBillInfoFlow(_ viewController: MayaCreditViewController) {
        let viewController = StoryboardScene.MayaCredit.mayaCreditLatestBillInfoViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        router.present(viewController, animated: false, completion: nil)
    }

    func didRequestMayaCreditInitalEligibilityFlow(_ viewController: MayaCreditViewController) {
        startMayaCreditInitialEligibilityFlow()
    }

    func didTapChatWithUs(_ viewController: MayaCreditViewController) {
        startInboxSupportChatbotFlow()
    }

    func didReceiveNoMaidenName(_ viewController: MayaCreditViewController, entryPoint: Constants.PersonalDetails.EntryPoint) {
        mothersMaidenNameEntryPoint = entryPoint
        startLoansFullScreen(args: [Constants.Flutter.BNPL.route: Constants.Flutter.BNPL.motherMaidenName])
    }

    func didReceiveMaidenName(_ viewController: MayaCreditViewController, entryPoint: Constants.PersonalDetails.EntryPoint) {
        mothersMaidenNameEntryPoint = entryPoint
        startEntryPointFlow()
    }
}

// MARK: Private functions
fileprivate extension MayaCreditCoordinator {
    func startMayaCreditOptInFlow(consents: [MayaCreditOptInConsent]) {
        let coordinator = MayaCreditOptInCoordinator(presenter: router, optInConsents: consents)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startMayaCreditSignUpFlow(term: MayaCreditEligibility.Term, feeRate: Float?) {
        let coordinator = MayaCreditSignUpCoordinator(presenter: router, eligibilityTerm: term, feeRate: feeRate)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        let completion: () -> Void = {
            // TODO: VELARYON - add logic for displaying bottomsheet and guard for ineligible accounts
            guard self.configurationService.mayaCreditPartnerMerchantEnabled.value else {
                return
            }
            self.didRequestPartnerSignupSuccessFlow(term: term)
        }
        coordinator.start(completion: completion)
    }

    func startMayaCreditUpdatePersonalDetailsFlow(term: MayaCreditEligibility.Term?, feeRate: Float?, creditAccount: MayaCreditAccount) {
        let coordinator = MayaCreditUpdatePersonalDetailsCoordinator(presenter: router, eligibilityTerm: term, feeRate: feeRate, creditAccount: creditAccount)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startMayaCreditTransferFundsFlow(term: MayaCreditCurrentLoanApplication.Term, creditAccount: MayaCreditAccount, feeRate: Float?) {
        let coordinator = NewMayaCreditTransferFundsCoordinator(presenter: router, creditAccount: creditAccount, feeRate: feeRate)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startMayaCreditSummaryFlow(term: MayaCreditCurrentLoanApplication.Term, creditAccount: MayaCreditAccount, eligibilityTerm: MayaCreditEligibility.Term?) {
        let coordinator = NewMayaCreditAccountSummaryCoordinator(presenter: router, creditAccount: creditAccount, term: term, elibigilityTerm: eligibilityTerm)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startMayaCreditSettingsFlow(term: MayaCreditCurrentLoanApplication.Term, creditAccount: MayaCreditAccount) {
        let coordinator = MayaCreditHomeCoordinator(presenter: router, creditAccount: creditAccount, term: term, isAnimated: false)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: { [weak coordinator] in
            coordinator?.showSettingsScreen()
        })
    }

    func showDataPrivacyPolicy() {
        let viewController = StoryboardScene.MayaCredit.mayaCreditDataPrivacyPolicyViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        router.present(viewController, animated: false, completion: nil)
    }

    func didRequestPartnerSignupSuccessFlow(term: MayaCreditEligibility.Term) {
        let viewController = StoryboardScene.MayaCredit.mayaCreditPartnerMerchantSignupSuccessBottomSheet.instantiate()
        let viewModel = MayaCreditPartnerMerchantViewModel(eligibilityTerm: term)
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        viewController.bind(viewModel)
        router.present(viewController, animated: false, completion: nil)
    }

    func startMayaCreditInitialEligibilityFlow() {
        let coordinator = MayaCreditInitialEligibilityCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        // TODO: VELARYON - add completion for partner merchant
        coordinator.start()
    }

    func startInboxSupportChatbotFlow() {
        let coordinator = MayaInboxSupportCoordinator(presenter: router)
        coordinator.inboxSupportDelegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startEntryPointFlow() {
        guard let entryPoint = mothersMaidenNameEntryPoint else { return }

        switch entryPoint {
        case .transferToWallet(let term, let account, let feeRate):
            startMayaCreditTransferFundsFlow(term: term, creditAccount: account, feeRate: feeRate)
        case .payBill(let url):
            routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
        }
    }

    func startLoansFullScreen(args: Any?) {
        let coordinator = MayaLoansFullScreenCoordinator(presenter: router)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        self.flutterEngineManager.getChannel(with: .bnplFull)?.invokeMethod(FlutterMethod.requestFullscreen.rawValue, arguments: args) { _ in
            coordinator.start()
        }
    }
}

// MARK: - MayaCreditPartnerMerchantSignupSuccessBottomSheetViewControllerDelegate functions
extension MayaCreditCoordinator: MayaCreditPartnerMerchantSignupSuccessBottomSheetViewControllerDelegate {
    func didTapBuyNow(_ viewController: MayaCreditPartnerMerchantSignupSuccessBottomSheetViewController) {
        // TODO: VELARYON - Gigalife deeplink
    }
}

// MARK: - DeepLinkHandler Methods
extension MayaCreditCoordinator: DeepLinkHandler {
    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .dashboard(.mayaCredit(let param)):
            if case .partnerMerchant(let merchant) = param {
                // TODO: VELARYON - functionality if opened from deeplink and with partner merchant
                return .opened(deepLink, nil)
            } else {
                return .opened(deepLink, nil)
            }
        default:
            return .rejected(deepLink, .notHandled, identifier)
        }
    }
}

// MARK: MayaInboxSupportCoordinatorDelegate methods
extension MayaCreditCoordinator: MayaInboxSupportCoordinatorDelegate {
    func didDismiss(coordinator: MayaInboxSupportCoordinator) {
        self.removeChild(coordinator)
    }
}

// MARK: MayaLoansFullScreenCoordinatorDelegate methos
extension MayaCreditCoordinator: MayaLoansFullScreenCoordinatorDelegate {
    func didDismissLoans(coordinator: MayaLoansFullScreenCoordinator) {
        self.removeChild(coordinator)
    }

    func didCompleteLoans(coordinator: MayaLoansFullScreenCoordinator) {
        startEntryPointFlow()
    }

    func didRequestDocumentUpload(coordinator: MayaLoansFullScreenCoordinator, completion: (() -> Void)?) {
        // Do nothing
    }
}
