//
//  MayaCreditSignUpCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> Jamolod on 7/27/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Contacts
import Flutter
import Foundation
import Injector

class MayaCreditSignUpCoordinator: MayaProgressBarCoordinator, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject private var flutterEngineManager: FlutterEngineManager

    private let eligibilityTerm: MayaCreditEligibility.Term?
    private var billingEndDateSelection: BillingEndDateSelection?
    private var personalDetailsSelection: PersonalDetailsSelection?
    private var feeRate: Float?
    private var creditAccount: MayaCreditAccount?
    private var viewModel: NewMayaCreditActivationViewModel
    private var completion: (() -> Void)?

    init(presenter: RouterProtocol? = nil, eligibilityTerm: MayaCreditEligibility.Term?, initialEligibility: MayaCreditInitialEligibility? = nil, feeRate: Float?, creditAccount: MayaCreditAccount? = nil) {
        self.eligibilityTerm = eligibilityTerm
        self.feeRate = feeRate
        self.creditAccount = creditAccount
        self.viewModel = NewMayaCreditActivationViewModel(eligibilityTerm: eligibilityTerm, initialEligibility: initialEligibility, feeRate: feeRate, creditAccount: creditAccount)
        super.init(presenter: presenter)
    }

    override func start(completion: (() -> Void)? = nil) {
        self.completion = completion
        let viewController = StoryboardScene.MayaCredit.mayaCreditSignUpViewController.instantiate()
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.navigationBarTitleView = progressBarView
        viewController.bind(viewModel)
        setRootModule(viewController, animated: false)
        presenter?.present(router, animated: true, completion: completion)
    }

    override var progressSegments: Int {
        return 3
    }
}

private extension MayaCreditSignUpCoordinator {
    func showCustomizeBillingEndDatePicker() {
        guard let billingEndDateSelection = billingEndDateSelection else { return }
        let viewController = StoryboardScene.MayaCredit.newMayaCreditBillingEndDateSelectorViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        let viewModel = NewMayaCreditBillingEndDateSelectorViewModel(billingEndDateSelection: billingEndDateSelection)
        viewController.bind(viewModel)
        router.present(viewController, animated: false, completion: nil)
    }

    func showCreditConfirmChangeBillingDateScreen(selectedBillingEndDate: Int) {
        let viewController = StoryboardScene.MayaCredit.newMayaCreditConfirmChangeBillingDateViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        viewController.loadViewIfNeeded()
        viewController.setupView(selectedBillingEndDate: selectedBillingEndDate)
        router.present(viewController, animated: false, completion: nil)
    }

    // 2nd screen
    func showAssignContactReference() {
        let viewController = StoryboardScene.MayaCredit.mayaCreditAssignContactReferenceViewController.instantiate()
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.navigationBarTitleView = progressBarView
        viewController.bind(viewModel.assignContactReferenceViewModel)
        router.push(viewController, animated: false, popCompletion: nil)
    }

    // 3rd screen
    func showConfirmDetails() {
        let viewController = StoryboardScene.MayaCredit.mayaCreditConfirmDetailsViewController.instantiate()
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.navigationBarTitleView = progressBarView
        viewController.bind(viewModel)
        router.push(viewController, animated: false, popCompletion: nil)
    }

    func showAllSetScreen(term: MayaCreditEligibility.Term?) {
        guard let eligibilityTerm = eligibilityTerm ?? term else { return }
        let viewController = StoryboardScene.MayaCredit.mayaCreditAllSetViewController.instantiate()
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        let viewModel = MayaCreditAllSetViewModel(eligibilityTerm: eligibilityTerm, billingEndDate: billingEndDateSelection?.currentValue?.ordinal)
        viewController.bind(viewModel)
        router.push(viewController, animated: false, popCompletion: nil)
    }

    func showGenderPicker() {
        guard personalDetailsSelection != nil else { return }
        let viewController = StoryboardScene.MayaCredit.mayaCreditGenderSelectorViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        router.present(viewController, animated: false, completion: nil)
    }

    func showMaritalStatusPicker() {
        guard personalDetailsSelection != nil else { return }
        let viewController = StoryboardScene.MayaCredit.mayaCreditMaritalStatusSelectorViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        router.present(viewController, animated: false, completion: nil)
    }

    func showContactRelationshipPicker() {
        guard personalDetailsSelection != nil else { return }
        let viewController = StoryboardScene.MayaCredit.mayaCreditContactRelationshipSelectorViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        router.present(viewController, animated: false, completion: nil)
    }

    func showLearnMoreContactReference() {
        let viewController = StoryboardScene.MayaCredit.mayaCreditLearnMoreContactReferenceViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        router.present(viewController, animated: false, completion: nil)
    }

    func showTermsAndConditions(activationTerm: ActivationTerm) {
        let viewController = StoryboardScene.MayaCredit.newMayaCreditTermDetailsViewController.instantiate()
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.bind(activationTerm.viewModel)
        viewController.setButtonTitle(L10n.Maya.Credit.Signup.Acknowledge.Button.title)
        viewController.setDefaultConfirmed(confirmed: true)
        router.present(viewController, animated: false, completion: nil)
    }

    func showDataPrivacyPolicy() {
        let viewController = StoryboardScene.MayaCredit.mayaCreditDataPrivacyPolicyViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        router.present(viewController, animated: false, completion: nil)
    }

    func showAutomatedPayments() {
        let viewController = StoryboardScene.MayaCredit.mayaCreditAutomatedPaymentsViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        router.present(viewController, animated: false, completion: nil)
    }

    func showContactPermission() {
        let alertController = UIAlertController(title: L10n.Maya.Credit.Contacts.No.Permission.title, message: L10n.Maya.Credit.Contacts.No.Permission.message, preferredStyle: .alert)
        let cancelAction = UIAlertAction(title: CommonStrings.Common.cancel, style: .cancel, handler: { _ in
            alertController.dismiss(animated: true, completion: nil)
        })
        let openSettings = UIAlertAction(title: CommonStrings.Common.Open.settings, style: .default, handler: { _ in
            showExternalURI(URL(string: UIApplication.openSettingsURLString))
        })
        alertController.addAction(cancelAction)
        alertController.addAction(openSettings)
        router.present(alertController, animated: true, completion: nil)
    }

    func showIneligibleView() {
        let viewController = StoryboardScene.MayaCredit.mayaCreditIneligibleViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        viewController.delegate = self
        router.present(viewController, animated: true, completion: nil)
    }

    func startCreditScoring(riskLevel: String) {
        let coordinator = CreditScoringCoordinator(presenter: router, riskLevel: riskLevel, triggeredBy: Constants.Trigger.lending, eventTrigger: Constants.EventTrigger.credit)
        coordinator.routeActionDelegate = self
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start()
    }
}

// MARK: - MayaCreditActivationViewControllerDelegate
extension MayaCreditSignUpCoordinator: MayaCreditSignUpViewControllerDelegate {
    func didTapContinue(_ viewController: MayaCreditSignUpViewController) {
        previousViewController = viewController
        showAssignContactReference()
    }

    func didTapSampleComputation(_ viewController: MayaCreditSignUpViewController) {
        let viewController = StoryboardScene.MayaCredit.newMayaCreditInterestDetailsViewController.instantiate()
        viewController.analyticsModule = Analytics.MayaCreditTransferCreditFeeSheet()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        let viewModel = MayaCreditInterestDetailsViewModel(feeRate: feeRate)
        viewController.bind(viewModel)
        router.present(viewController, animated: false, completion: nil)
    }

    func didTapCustomizeBillingEndDate(_ viewController: MayaCreditSignUpViewController, billingEndDateSelection: BillingEndDateSelection) {
        self.billingEndDateSelection = billingEndDateSelection
        showCustomizeBillingEndDatePicker()
    }

    func didTapGender(_ viewController: MayaCreditSignUpViewController, personalDetailsSelection: PersonalDetailsSelection) {
        self.personalDetailsSelection = personalDetailsSelection
        showGenderPicker()
    }

    func didTapMaritalStatus(_ viewController: MayaCreditSignUpViewController, personalDetailsSelection: PersonalDetailsSelection) {
        self.personalDetailsSelection = personalDetailsSelection
        showMaritalStatusPicker()
    }

    func didTapTermsAndConditions(_ viewController: MayaCreditSignUpViewController, activationTerm: ActivationTerm) {
        showTermsAndConditions(activationTerm: activationTerm)
    }

    func didSuccessfullySubmitted(_ viewController: MayaCreditSignUpViewController, response: OTPResponse) {
        let otpType: OTPType = .newMayaCredit(otpId: response.otpId)
        let viewController = StoryboardScene.Account.mayaOTPViewController.instantiate()
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.analyticsModuleString = otpType.analyticsModuleString
        viewController.bind(otpType.generateViewModel())
        router.push(viewController, animated: true, popCompletion: nil)
    }
}

// MARK: - NewMayaCreditBillingEndDateSelectorViewControllerDelegate
extension MayaCreditSignUpCoordinator: NewMayaCreditBillingEndDateSelectorViewControllerDelegate {
    func didTapSelectDate(_ viewController: NewMayaCreditBillingEndDateSelectorViewController, selectedDate: Int) {
        billingEndDateSelection?.currentValue = selectedDate
        showCreditConfirmChangeBillingDateScreen(selectedBillingEndDate: selectedDate)
    }
}

// MARK: - NewMayaCreditConfirmChangeBillingDateViewControllerDelegate
extension MayaCreditSignUpCoordinator: NewMayaCreditConfirmChangeBillingDateViewControllerDelegate {
    func changeDateButtonTapped(_ viewController: NewMayaCreditConfirmChangeBillingDateViewController) {
        router.dismissModule(animated: true) { [weak self] in
            self?.showCustomizeBillingEndDatePicker()
        }
    }

    func confirmButtonTapped(_ viewController: NewMayaCreditConfirmChangeBillingDateViewController) {
        billingEndDateSelection?.onSelect(billingEndDateSelection?.currentValue ?? 0)
        router.dismissModule(animated: true, completion: nil)
    }
}

// MARK: - MayaCreditTermDetailsViewControllerDelegate
extension MayaCreditSignUpCoordinator: NewMayaCreditTermDetailsViewControllerDelegate {
    func didTapConfirm(_ viewController: NewMayaCreditTermDetailsViewController) {
        viewController.viewModel?.isConfirmed.value = true
        router.dismissModule(animated: true, completion: nil)
    }
}

// MARK: - MayaOTPViewControllerDelegate
extension MayaCreditSignUpCoordinator: MayaOTPViewControllerDelegate {
    func didReceiveOTPError(error: PayMayaError, _ viewController: MayaOTPViewController) {
        didTapBack(viewController)
    }

    func didViewTermsAndCondition(_ viewController: MayaOTPViewController) {
        // Do nothing, will not trigger data privacy
    }

    func didAuthenticate(_ viewController: MayaOTPViewController, response: Codable?) {
        guard let mayaCreditURL = Constants.DeepLinkPath.mayaCredit.url else {
            backActionDelegate?.didTapBack(self, completion: nil)
            return
        }
        routeActionDelegate?.didRequestStartDeepLink(self, with: mayaCreditURL, completion: nil)
    }

    func didReceivePostAuthenticationActionError(_ viewController: MayaOTPViewController) {
        // Do nothing.
    }

    func didRequestDisplayMayaAlertViewModel(viewModel: MayaAlertViewModel, response: Codable?, viewController: MayaOTPViewController) {
        // Do nothing.
    }

    func didRequestDisplayMayaReceiptViewModel(viewModel: MayaReceiptViewModelProtocol, response: Codable?, viewController: MayaOTPViewController) {
        // Do nothing.
    }
}

// MARK: MayaCreditGenderSelectorViewControllerDelegate
extension MayaCreditSignUpCoordinator: MayaCreditGenderSelectorViewControllerDelegate {
    func didTapGender(_ viewController: MayaCreditGenderSelectorViewController, gender: Constants.PersonalDetails.Gender) {
        personalDetailsSelection?.onSelectGender(gender)
    }
}

// MARK: MayaCreditMaritalStatusSelectorViewControllerDelegate
extension MayaCreditSignUpCoordinator: MayaCreditMaritalStatusSelectorViewControllerDelegate {
    func didTapStatus(_ viewController: MayaCreditMaritalStatusSelectorViewController, status: Constants.PersonalDetails.MaritalStatus) {
        personalDetailsSelection?.onSelectMaritalStatus(status)
    }
}

// MARK: MayaCreditMaritalStatusSelectorViewControllerDelegate
extension MayaCreditSignUpCoordinator: MayaCreditAssignContactReferenceViewControllerDelegate {
    func didTapLearnMore(_ viewController: MayaCreditAssignContactReferenceViewController) {
        showLearnMoreContactReference()
    }

    func didTapContactRelationship(_ viewController: MayaCreditAssignContactReferenceViewController, personalDetailsSelection: PersonalDetailsSelection) {
        self.personalDetailsSelection = personalDetailsSelection
        showContactRelationshipPicker()
    }

    func didTapMobileNumber(_ viewController: MayaCreditAssignContactReferenceViewController, personalDetailsSelection: PersonalDetailsSelection) {
        if CNContactStore.authorizationStatus(for: .contacts) == .authorized {
            self.personalDetailsSelection = personalDetailsSelection
            let contactsCoordinator = MayaContactsCoordinator(presenter: router)
            contactsCoordinator.shouldIncludeOwnNumber = false
            contactsCoordinator.backActionDelegate = self
            contactsCoordinator.routeActionDelegate = self
            contactsCoordinator.delegate = self
            addChild(contactsCoordinator)
            contactsCoordinator.startContactsV2Flow()
        } else {
            showContactPermission()
        }
    }

    func didTapContinue(_ viewController: MayaCreditAssignContactReferenceViewController) {
        previousViewController = viewController
        showConfirmDetails()
    }
}

// MARK: MayaCreditMaritalStatusSelectorViewControllerDelegate
extension MayaCreditSignUpCoordinator: MayaCreditContactRelationshipSelectorViewControllerDelegate {
    func didTapContactRelationship(_ viewController: MayaCreditContactRelationshipSelectorViewController, contactRelationship: Constants.PersonalDetails.ContactRelationship) {
        personalDetailsSelection?.onSelectContactRelationship(contactRelationship)
    }
}

// MARK: - MayaContactsCoordinatorDelegate Methods
extension MayaCreditSignUpCoordinator: MayaContactsCoordinatorDelegate {
    func didSelectContactNumber(_ coordinator: MayaContactsCoordinator, contactNumber: ContactNumber) {
        personalDetailsSelection?.onSelectContactNumber(contactNumber.number)
    }

    func didTapBack(_ coordinator: MayaContactsCoordinator, isContactPermissionAllowed: Bool) {
        // Do nothing
    }
}

// MARK: - MayaContactsCoordinatorDelegate Methods
extension MayaCreditSignUpCoordinator: MayaCreditConfirmDetailsViewControllerDelegate {
    func didTapTermsAndConditions(_ viewController: MayaCreditConfirmDetailsViewController) {
        guard let activationTerm = viewModel.activationTerms.value[safeIndex: PoliciesTypes.termsAndConditions.index] else { return }
        showTermsAndConditions(activationTerm: activationTerm)
    }

    func didTapDisclosureStatement(_ viewController: MayaCreditConfirmDetailsViewController) {
        guard let activationTerm = viewModel.activationTerms.value[safeIndex: PoliciesTypes.disclosureStatement.index] else { return }
        showTermsAndConditions(activationTerm: activationTerm)
    }

    func didTapTruthAndTransparencyInLending(_ viewController: MayaCreditConfirmDetailsViewController) {
        guard let activationTerm = viewModel.activationTerms.value[safeIndex: PoliciesTypes.truthAndTransparency.index] else { return }
        showTermsAndConditions(activationTerm: activationTerm)
    }

    func didTapDataPrivacyPolicy(_ viewController: MayaCreditConfirmDetailsViewController) {
        showDataPrivacyPolicy()
    }

    func didTapAutomatedPayments(_ viewController: MayaCreditConfirmDetailsViewController) {
        showAutomatedPayments()
    }

    func didTapConfirm(_ viewController: MayaCreditConfirmDetailsViewController) {
        let geolocationAttributes = [
            AnalyticsAttributeKey.enabled: viewModel.isGeolocationEnabled
        ]
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditApplication.geolocation), keyAttributes: geolocationAttributes)

        if viewModel.isGeolocationEnabled {
            viewModel.handleRavenGetGeolocation.apply().start()
            return
        }

        if viewModel.shouldInitializeDeviceScoring {
            startCreditScoring(riskLevel: viewModel.riskLevel)
        } else {
            viewModel.getMayaCreditFullEligibility.apply((UUID().uuidString, nil, nil)).start()
        }

        let deviceScoringAttributes = [
            AnalyticsAttributeKey.enabled: viewModel.shouldInitializeDeviceScoring
        ]
        analyticsService.logMayaEvents(Analytics.LendingToggles.mecDeviceScoring.rawValue, attributes: deviceScoringAttributes)
    }

    func willShowAllSetScreen(_ viewController: MayaCreditConfirmDetailsViewController, term: MayaCreditEligibility.Term) {
        showAllSetScreen(term: term)
    }

    func willShowIneligibleView(_ viewController: MayaCreditConfirmDetailsViewController) {
        showIneligibleView()
    }

    func willStartCreditScoring(_ viewController: MayaCreditConfirmDetailsViewController, riskLevel: String) {
        startCreditScoring(riskLevel: riskLevel)
    }
}

// MARK: MayaCreditAllSetViewControllerDelegate
extension MayaCreditSignUpCoordinator: MayaCreditAllSetViewControllerDelegate {
    func didTapSeeSampleComputation(_ viewController: MayaCreditAllSetViewController, feeRate: Float?) {
        let viewController = StoryboardScene.MayaCredit.newMayaCreditInterestDetailsViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        let viewModel = MayaCreditInterestDetailsViewModel(feeRate: feeRate)
        viewController.bind(viewModel)
        viewController.analyticsModule = Analytics.NewMayaCreditServiceFee()
        router.present(viewController, animated: false, completion: nil)
    }

    func didTapProceed(_ viewController: MayaCreditAllSetViewController) {
        viewModel.submitPersonDetails.apply().start()
    }

    func didTapTermsAndConditions(_ viewController: MayaCreditAllSetViewController) {
        guard let activationTerm = viewModel.activationTerms.value[safeIndex: PoliciesTypes.termsAndConditions.index] else { return }
        showTermsAndConditions(activationTerm: activationTerm)
    }

    func didTapDisclosureStatement(_ viewController: MayaCreditAllSetViewController) {
        guard let activationTerm = viewModel.activationTerms.value[safeIndex: PoliciesTypes.disclosureStatement.index] else { return }
        showTermsAndConditions(activationTerm: activationTerm)
    }

    func didTapTruthAndTransparencyInLending(_ viewController: MayaCreditAllSetViewController) {
        guard let activationTerm = viewModel.activationTerms.value[safeIndex: PoliciesTypes.truthAndTransparency.index] else { return }
        showTermsAndConditions(activationTerm: activationTerm)
    }

    func didTapDataPrivacyPolicy(_ viewController: MayaCreditAllSetViewController) {
        showDataPrivacyPolicy()
    }

    func didTapAutomatedPayments(_ viewController: MayaCreditAllSetViewController) {
        showAutomatedPayments()
    }
}

// MARK: - MayaCreditIneligibleViewControllerDelegate functions
extension MayaCreditSignUpCoordinator: MayaCreditIneligibleViewControllerDelegate {
    func didTapDone(_ viewController: MayaCreditIneligibleViewController) {
        guard let mayaCreditURL = Constants.DeepLinkPath.mayaCredit.url else {
            backActionDelegate?.didTapBack(self, completion: nil)
            return
        }
        routeActionDelegate?.didRequestStartDeepLink(self, with: mayaCreditURL, completion: nil)
    }

    func didShowIneligibleScreen(_ viewController: MayaCreditIneligibleViewController) {
        analyticsService.logMayaEvents(action: .appear(Analytics.MayaCreditInitialEligibility.fullEligibilityIneligible))
    }
}

// MARK: MayaRestrictedLoginCoordinatorDelegate methods
extension MayaCreditSignUpCoordinator: CreditScoringCoordinatorDelegate {
    func didDismiss(coordinator: CreditScoringCoordinator) {
        removeChild(coordinator)
    }

    func showAllSetScreen(dataReferenceId: String?, creditEligibility: MayaCreditEligibility?) {
        viewModel.getMayaCreditFullEligibility.apply((UUID().uuidString, dataReferenceId, creditEligibility)).start()
    }

    func showIneligibleScreen() {
        showIneligibleView()
    }
}
