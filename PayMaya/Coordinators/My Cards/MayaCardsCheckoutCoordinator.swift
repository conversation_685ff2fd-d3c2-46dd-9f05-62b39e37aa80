//
//  MayaCardsCheckoutCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 9/19/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import ConfigurationService
import Flutter
import FlutterPluginRegistrant
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider

protocol MayaCardsCheckoutCoordinatorDelegate: AnyObject {
    func didDismiss(coordinator: MayaCardsCheckoutCoordinator)
}

class MayaCardsCheckoutCoordinator: MayaFlutterBaseCoordinator {
    @Inject private var configurationService: ConfigurationService
    weak var delegate: MayaCardsCheckoutCoordinatorDelegate?

    init(
        router: RouterProtocol = Router(),
        presenter: RouterProtocol? = nil
    ) {
        super.init(
            flutterModule: .checkout,
            router: router,
            presenter: presenter
        )
    }

    override var flutterToggles: [FlutterToggles: CSConfig<Bool>] {
        return [
            FlutterToggles.simplifiedCheckout: configurationService.mayaLoansSimplifiedCheckoutEnabled,
            FlutterToggles.loansV3: configurationService.mayaLoansV3Enabled,
            FlutterToggles.loansICL: configurationService.mayaLoansICLEnabled
        ]
    }

    override func setCustomMethodHandler(method: FlutterMethod, call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch method {
        case .finishCurrentModuleForCard: handleFinishCurrentModule(call.arguments, result)
        default: result(nil)
        }
    }

    private func handleFinishCurrentModule(_ arguments: Any?, _ result: @escaping FlutterResult) {
        result(true)
        flutterEngineManager.getChannel(with: .cardsFull)?
            .invokeMethod("onModuleResult", arguments: arguments)
    }
}
