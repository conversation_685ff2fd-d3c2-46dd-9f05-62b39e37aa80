//
//  MayaCardsSetUsernameCoordinator.swift
//  PayMaya
//
//  Created by <PERSON> on 9/8/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import ConfigurationService
import Flutter
import FlutterPluginRegistrant
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider

protocol MayaCardsSetUsernameCoordinatorDelegate: AnyObject {
    func didDismissCoordinator(coordinator: MayaCardsSetUsernameCoordinator)
}

class MayaCardsSetUsernameCoordinator: MayaFlutterBaseCoordinator {
    weak var delegate: MayaCardsSetUsernameCoordinatorDelegate?

    init(
        router: RouterProtocol = Router(),
        presenter: RouterProtocol? = nil
    ) {
        super.init(
            flutterModule: .setUsername,
            router: router,
            presenter: presenter
        )
    }

    override func setCustomMethodHandler(method: FlutterMethod, call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch method {
        case .finishCurrentModuleForCard: finishCurrentModuleForCard(call: call)
        default: result(nil)
        }
    }

    private func finishCurrentModuleForCard(call: FlutterMethodCall) {
        self.dismiss {
            self.delegate?.didDismissCoordinator(coordinator: self)
            self.flutterEngineManager.getChannel(with: .cardsFull)?.invokeMethod("onModuleResult", arguments: call.arguments)
        }
    }
}
