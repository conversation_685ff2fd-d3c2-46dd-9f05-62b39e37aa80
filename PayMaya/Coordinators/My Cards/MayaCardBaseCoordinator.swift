//
//  MayaCardBaseCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 6/24/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider

class MayaCardBaseCoordinator: MayaFlutterBaseCoordinator {}

// MARK: - LoansCreditCardFullScreenCoordinatorDelegate Methods
extension MayaCardBaseCoordinator: LoansCreditCardFullScreenCoordinatorDelegate {
    func startCreditCardFullScreen(args: Any?, completion: (() -> Void)? = nil) {
        if let arguments = args as? [String: Any],
           let route = arguments[Constants.Flutter.Cards.route] as? String,
           let params = arguments[Constants.Flutter.Cards.params] as? String {
            let coordinator = LoansCreditCardFullScreenCoordinator(route: routeName(route: route), params: params, presenter: router)
            coordinator.delegate = self
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            coordinator.presentationDirection = .right
            addChild(coordinator)
            coordinator.start(completion: completion)
        }
    }

    private func routeName(route: String) -> String {
        switch route {
        case Constants.DeepLinkPath.loansCreditCardAbout.rawValue:
            return Constants.Flutter.LoansCreditCard.aboutYourCardScreen
        case Constants.DeepLinkPath.loansCreditCardPending.rawValue:
            return Constants.Flutter.LoansCreditCard.applicationPendingScreen
        case Constants.DeepLinkPath.loansCreditCardRepayment.rawValue:
            return Constants.Flutter.LoansCreditCard.repaymentScreen
        case Constants.Flutter.LoansCreditCard.ccReviewingApplicationScreen:
            return Constants.Flutter.LoansCreditCard.ccReviewingApplicationScreen
        case Constants.Flutter.LoansCreditCard.securityDepositReprocessScreen:
            return Constants.Flutter.LoansCreditCard.securityDepositReprocessScreen
        case Constants.Flutter.LoansCreditCard.securityDepositScreen:
            return Constants.Flutter.LoansCreditCard.securityDepositScreen
        case Constants.Flutter.LoansCreditCard.selectTransactionScreen:
            return Constants.Flutter.LoansCreditCard.selectTransactionScreen
        case Constants.Flutter.LoansCreditCard.installmentPlanScreen:
            return Constants.Flutter.LoansCreditCard.installmentPlanScreen
        case Constants.Flutter.LoansCreditCard.dblSecuritySelectionScreen:
            return Constants.Flutter.LoansCreditCard.dblSecuritySelectionScreen
        default:
            return Constants.Flutter.LoansCreditCard.cardOverviewScreen
        }
    }

    func didDismiss(coordinator: LoansCreditCardFullScreenCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
        completion?()
    }

    func didRequestFullScreen(coordinator: LoansCreditCardFullScreenCoordinator) {
        startCreditCardFullScreen(args: [
            Constants.Flutter.LoansCreditCard.route: Constants.Flutter.LoansCreditCard.ccReviewingApplicationScreen,
            Constants.Flutter.Cards.params: ""
        ])
    }

    func didRequestFullScreen(coordinator: LoansCreditCardFullScreenCoordinator, route: String, args: [String: String], completion: (() -> Void)? = nil) {
        let params = args.map({ "\($0)=\($1)" }).joined(separator: "&")

        startCreditCardFullScreen(args: [
            Constants.Flutter.LoansCreditCard.route: route,
            Constants.Flutter.Cards.params: params
        ], completion: completion)
    }
}
