//
//  CreditScoringCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 4/11/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
import ConfigurationService
import Flutter
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider

protocol CreditScoringCoordinatorDelegate: AnyObject {
    func didDismiss(coordinator: CreditScoringCoordinator)
    func showAllSetScreen(dataReferenceId: String?, creditEligibility: MayaCreditEligibility?)
    func showIneligibleScreen()
}

class CreditScoringCoordinator: Coordinator, MayaAnalyticsDurationManagerProtocol {
    @Inject var storeProvider: StoreProvider
    @Inject private var flutterEngineManager: FlutterEngineManager
    @Inject private var configurationService: ConfigurationService
    @Inject private var ravenWrapper: RavenWrapper
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject var configServiceV2: ConfigurationServiceV2

    private let riskLevel: String
    private let triggeredBy: String
    private var eventTrigger: String?
    private var credolabChannel: FlutterMethodChannel?
    private static let referenceIdEnabled: Bool = true

    weak var delegate: CreditScoringCoordinatorDelegate?

    var sessionTimeoutResult: FlutterResult?

    init(router: RouterProtocol = Router(), presenter: RouterProtocol? = nil, riskLevel: String, triggeredBy: String?, eventTrigger: String?) {
        self.riskLevel = riskLevel
        self.triggeredBy = triggeredBy ?? Constants.Trigger.baseApp
        self.eventTrigger = eventTrigger
        super.init(router: router, presenter: presenter)
        let timeoutNotificationSignal = NotificationCenter.default.reactive.notifications(forName: NSNotification.Name(rawValue: sessionTimeoutSuccessNotification)).map { _ in }
        timeoutNotificationSignal.observe(on: UIScheduler()).observeValues { [weak self] in
            guard let self = self,
                  let databaseStore = self.storeProvider.target(DatabaseStore.self),
                  let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                  let token = user.token
            else {
                return
            }

            self.sessionTimeoutResult?(self.flutterEngineManager.responseGenerator.getSessionToken(token))
            self.sessionTimeoutResult = nil
        }?.addToDisposeBag(disposeBag)
    }

    override func start(completion: (() -> Void)? = nil) {
        let fullScreenEntryPointInitialRoute = Constants.Flutter.CreditScoring.fullScreenEntryPointInitialRoute
        let enableDataReferenceId = Constants.Flutter.CreditScoring.enableDataReferenceId
        let customRoute = "\(fullScreenEntryPointInitialRoute)\(riskLevel)\(enableDataReferenceId)\(Self.referenceIdEnabled)"

        flutterEngineManager.startEngine(with: .creditScoring, customRoute: customRoute)
        setMethodHandler()

        if let engine = flutterEngineManager.getEngine(with: .creditScoring) {
            setCredolabMethodHandler(engine)

            let flutterViewController = FlutterViewController(engine: engine, nibName: nil, bundle: nil)
            flutterViewController.isViewOpaque = false
            flutterViewController.modalPresentationStyle = UIModalPresentationStyle.overFullScreen
            presenter?.present(flutterViewController, animated: false, completion: completion)
        }
    }

    private func setCredolabMethodHandler(_ engine: FlutterEngine) {
        credolabChannel = FlutterMethodChannel(name: Constants.Flutter.CreditScoring.credolabChannel, binaryMessenger: engine.binaryMessenger)
        credolabChannel?.setMethodCallHandler({ [weak self] call, result in
            guard let method = FlutterMethod(rawValue: call.method),
                    let self = self
            else { return }

            if method == .collect {
                CredolabDataService().collect { res in
                    switch res {
                    case .success(let data):
                        result(data)
                    case .error(let errorCode, let errorMessage):
                        result(FlutterError(code: String(errorCode), message: errorMessage, details: nil))
                    @unknown default:
                        result(FlutterError(code: String(-1), message: nil, details: nil))
                    }
                }

                // trigger raven data extraction
                self.handleRavenExtract()
            }
        })
    }

    private func setMethodHandler() {
        let channel = flutterEngineManager.getChannel(with: .creditScoring)

        channel?.setMethodCallHandler { [weak self] call, result in
            guard let self = self,
                  let method = FlutterMethod(rawValue: call.method)
            else { return }

            switch method {
            case .getSessionToken:
                handleGetSessionToken(result)
            case .getAccessToken:
                handleGetAccessToken(result)
            case .getShieldSessionId:
                result(self.flutterEngineManager.responseGenerator.getShieldSessionId(Shield.shared().sessionId))
            case .onSessionTimeout:
                handleSessionTimeout(result)
            case .getPinningFingerprint:
                handleGetPinningFingerprint(result)
            case .logAnalyticsEvent:
                handleLogAnalyticsEvent(call, result)
            case .dismiss:
                handleDismiss()
            case .complete:
                handleDismiss()
            case .finishCurrentModuleForCard:
                handleCreditScoringComplete(call)
                handleDismiss()
            case .creditScoringComplete:
                handleCreditScoringComplete(call)
            default: return
            }
        }
    }

    private func handleRavenExtract() {
        if configServiceV2.lendingDataExtractionEnabled.value {
            guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
                  let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
            else {
                return
            }

            ravenWrapper.extract(customerID: user.walletId,
                                 transactionID: nil,
                                 triggeredBy: self.triggeredBy,
                                 eventTrigger: self.eventTrigger,
                                 completion: { [weak self] res in
                let analyticsService = ContainerWrapper.shared.resolve(AnalyticsService.self) as AnalyticsService
                var attributes = [AnalyticsAttributeKey: String]()

                if let triggeredBy = self?.triggeredBy {
                    attributes[AnalyticsAttributeKey.triggeredBy] = triggeredBy
                }

                if let eventTrigger = self?.eventTrigger {
                    attributes[AnalyticsAttributeKey.eventTrigger] = eventTrigger
                }

                switch res {
                case .success(let data):
                    analyticsService.logMayaEvents(action: .success(Analytics.MayaDataExtraction.extract),
                                                         keyAttributes: attributes)
                case .failure(let err):
                    analyticsService.logMayaEvents(action: .failure(Analytics.MayaDataExtraction.extract),
                                                         keyAttributes: attributes)
                }
            })
        }
    }

    private func handleGetSessionToken(_ result: FlutterResult) {
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
              let token = user.token
        else { return }
        result(self.flutterEngineManager.responseGenerator.getSessionToken(token))
    }

    private func handleGetAccessToken(_ result: FlutterResult) {
        guard let encryptedStore = self.storeProvider.target(EncryptedDefaultsStore.self),
              let authorization = encryptedStore.read(EncryptedDefaultsStoreId.authorization, type: Authorization.self).value
        else { return }
        result(self.flutterEngineManager.responseGenerator.getAccessToken(authorization.accessToken))
    }

    private func handleSessionTimeout(_ result: @escaping FlutterResult) {
        self.sessionTimeoutResult = result
        NotificationCenter.default.post(name: NSNotification.Name(rawValue: sessionTimeoutNotification), object: nil)
    }

    private func handleGetPinningFingerprint(_ result: FlutterResult) {
        guard let propertyListStore = self.storeProvider.target(PropertyListStore.self),
              let pinningFingerprint = propertyListStore.read(PropertyListStoreId.pinningFingerprint).value as? String else { return }
        result(self.flutterEngineManager.responseGenerator.getPinningFingerprint(pinningFingerprint))
    }

    private func handleLogAnalyticsEvent(_ call: FlutterMethodCall, _ result: FlutterResult) {
        guard let jsonString = call.arguments as? String,
              let jsonData = jsonString.data(using: .utf8),
              let dictionary = try? JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
              let name = dictionary[Constants.Flutter.Key.eventName] as? String
        else { return }

        let attributes = dictionary[Constants.Flutter.Key.attributes] as? [String: Any]
        self.analyticsService.logMayaEvents(name: name, attributes: attributes)
    }

    private func handleDismiss() {
        presenter?.dismissModule(animated: false) { [weak self] in
            guard let self = self else { return }
            self.flutterEngineManager.getChannel(with: .creditScoring)?.setMethodCallHandler(nil)
            self.flutterEngineManager.clearEngine(with: .creditScoring)
            self.flutterEngineManager.initializeEngine(with: .creditScoring)
            self.delegate?.didDismiss(coordinator: self)
        }
    }

    private func handleCreditScoringComplete(_ call: FlutterMethodCall) {
        guard let dictionary = call.arguments as? [String: Any],
              let isSuccessful = dictionary[Constants.Flutter.Key.success] as? Bool,
              isSuccessful
        else { return handleDismiss() }

        if isSuccessful, let isEligible = dictionary[Constants.Flutter.Key.isEligible] as? Bool, isEligible {
            handleEligibilityFlow(with: dictionary)
        } else {
            handleDismiss()
            delegate?.showIneligibleScreen()
        }
    }

    private func handleEligibilityFlow(with dictionary: [String: Any]) {
        let dataReferenceId = dictionary[Constants.Flutter.Key.dataReferenceId] as? String

        if let jsonString = dictionary[Constants.Flutter.Key.data] as? String,
           let jsonData = jsonString.data(using: .utf8),
            let creditEligibilityModel = decodeMayaCreditEligibilityModel(jsonData: jsonData) {
            delegate?.showAllSetScreen(dataReferenceId: dataReferenceId, creditEligibility: creditEligibilityModel)
            analyticsService.logMayaEvents(action: .success(Analytics.MayaCreditScoringExtract.extract))
        } else {
            handleDismiss()
            analyticsService.logMayaEvents(.failure(Analytics.MayaCreditScoringExtract.extract))
        }
    }

    private func decodeMayaCreditEligibilityModel(jsonData: Data) -> MayaCreditEligibility? {
        do {
            return try MayaCreditEligibility.decoder.decode(MayaCreditEligibility.self, from: jsonData)
        } catch _ {
            return nil
        }
    }
}
