//
//  EKYCZolozCoordinator.swift
//  PayMaya
//
//  Created by <PERSON> on 03/08/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import ConfigurationService
import Error
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import StoreKit
import StoreProvider
import UIKit

protocol EKYCZolozCoordinatorDelegate: AnyObject {
    func dismissCoordinator(_: Coordinator)
    func dismissAndPushProfileCoordinator(_: Coordinator)
    func sessionTimeoutErrorOccured(_ coordinator: EKYCZolozCoordinator)
}

final class EKYCZolozCoordinator: MayaProgressBarCoordinator, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    weak var delegate: EKYCZolozCoordinatorDelegate?

    // This property will contain triggering Module
    var analyticsModule: AnalyticsModule?

    @Inject private var configurationServices: ConfigurationService
    @Inject private var backgroundSessionManager: BackgroundSessionManager
    @Inject private var storeProvider: StoreProvider

    private var commonViewModel: MayaZolozEkycViewModel?
    private var zolozDisposableBag = CompositeDisposable()

    var onCompleteKyc: (() -> Void)?
    var isFromReg: Bool = false

    private var user: User? {
        guard
            let databaseStore = storeProvider.target(DatabaseStore.self),
            let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
        else { return nil }
        return user
    }

    private var isAccountUpgraded: Bool {
        if let user = user {
            return user.kycClientStatus == .one
        }
        return false
    }

    override func start(completion: (() -> Void)? = nil) {
        if configurationServices.kycMaintenanceEnabled.value {
            startMaintenanceFlow(completion: completion)
        } else {
            if let userDefaults = storeProvider.target(UserDefaultsStore.self) {
                userDefaults.remove(UserDefaultsStoreId.kycEntryPoint, completion: nil)
            }

            startAction(completion: completion)
        }
    }

    func startWithEntryPoint(completion: (() -> Void)? = nil, entryPoint: String) {
        if configurationServices.kycMaintenanceEnabled.value {
            startMaintenanceFlow(completion: completion)
        } else {
            if let userDefaults = storeProvider.target(UserDefaultsStore.self) {
                userDefaults.write(entryPoint, options: UserDefaultsStoreId.kycEntryPoint)
            }

            startAction(completion: completion)
        }
    }
}

private extension EKYCZolozCoordinator {
    func generateNewXTraceID() {
        guard let encryptedDefaultStore = storeProvider.target(EncryptedDefaultsStore.self) else { return }
        let xTraceId = UUID().uuidString
        encryptedDefaultStore.write(xTraceId, options: EncryptedDefaultsStoreId.zolozPreSubmissionsXTraceId)
    }

    func getSelectedDocument() -> MayaEKYCSelectedDocument? {
        guard let viewModel = commonViewModel else { return nil }

        if viewModel.secondaryIDFlow.value {
            guard let secondaryDocument = viewModel.selectedSecondaryDocument.value else { return nil }
            return secondaryDocument
        } else {
            guard let primaryDocument = viewModel.selectedDocument.value else { return nil }
            return primaryDocument
        }
    }

    func startAction(completion: (() -> Void)? = nil) {
        guard let user = user else { return }
        let kycStatus: KYCStatus = user.kycStatusValue
        let reKYCStatus: ReKYCStatus = user.reKYCStatusValue

        let shouldShowProfilePage = reKYCStatus == .submitted || kycStatus == .submitted
        if shouldShowProfilePage {
            self.showProfilePage(completion: completion)
        } else {
            generateNewXTraceID()
            commonViewModel = MayaZolozEkycViewModel()
            commonViewModel?.isFromReg.value = isFromReg

            commonViewModel?.getKycDataAction.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak presenter] isExecuting in
                 if isExecuting {
                     presenter?.toPresentable().showMayaLoader()
                 } else {
                     presenter?.toPresentable().hideMayaLoader()
                 }
            }?.addToDisposeBag(disposeBag)

            commonViewModel?.patchUserFatca.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak presenter] isExecuting in
                 if isExecuting {
                     presenter?.toPresentable().showMayaLoader()
                 } else {
                     presenter?.toPresentable().hideMayaLoader()
                 }
            }?.addToDisposeBag(disposeBag)

            commonViewModel?.getKycDataAction.errors.signal.observe(on: UIScheduler()).observeValues { [weak self] error in
                 self?.logEKYCErrorEvent(source: AnalyticsConstants.ScreenName.loader, error: error)
                 guard case .sessionTimeout = error.type
                 else {
                     // Present the error screen on top of the maya loader's parent screen.
                     self?.showError()
                     return
                 }
                 guard let self = self else { return }
                 self.delegate?.sessionTimeoutErrorOccured(self)
            }?.addToDisposeBag(disposeBag)

            commonViewModel?.patchUserFatca.errors.signal.observe(on: UIScheduler()).observeValues { [weak self] error in
                self?.logEKYCErrorEvent(source: AnalyticsConstants.ScreenName.confirmSubmission, error: error)
                guard case .sessionTimeout = error.type
                else {
                    // MARK: Show generic error
                    let errorViewModel = ErrorAlertViewModel(title: CommonStrings.Maya.Error.Default.title)
                    self?.showMayaError(viewModel: errorViewModel)
                    return
                }
                guard let self = self else { return }
                self.delegate?.sessionTimeoutErrorOccured(self)
            }?.addToDisposeBag(disposeBag)

             commonViewModel?.getKycDataAction.completed.signal.observe(on: UIScheduler()).observeValues { [weak self] _ in
                 if let isKYCTofuUXRedesignEnabled = self?.commonViewModel?.isKYCTofuUXRedesignEnabled,
                    isKYCTofuUXRedesignEnabled {
                     self?.startNewKYCIntroFlow(completion: completion)
                 } else {
                     self?.startNormalFlow(completion: completion)
                 }
             }?.addToDisposeBag(disposeBag)

            commonViewModel?.patchUserFatca.completed.signal.observe(on: UIScheduler()).observeValues { [weak self] _ in
                if let isBackIDEnabled = self?.commonViewModel?.isBackIDCaptureEnabled,
                   isBackIDEnabled {
                    self?.showSubmissionScreenBackIDFlow()
                } else {
                    self?.showSubmissionScreen()
                }
            }?.addToDisposeBag(disposeBag)

            commonViewModel?.getKycDataAction.apply().start()
        }
    }

    func dismissAndPushProfile() {
        delegate?.dismissAndPushProfileCoordinator(self)
    }
}

// MARK: - - EKYC TOFU Events
private extension EKYCZolozCoordinator {
    func logEKYCBackEvent(source: String, destination: String, isForSplitFlow: Bool = false) {
        guard var eventAttributes = getCommonEventAttributes(isForSplitFlow: isForSplitFlow) else { return }

        let otherAttributes = [
            AnalyticsAttributeKey.sourcePage: source,
            AnalyticsAttributeKey.destination: destination
        ]
        eventAttributes.merge(otherAttributes) { _, new in new }

        analyticsService.logMayaEvents(
            name: AnalyticsConstants.UserAction.kycBack,
            keyAttributes: eventAttributes
        )
    }

    func logEKYCErrorEvent(source: String, error: PayMayaError, isForSplitFlow: Bool = false) {
        guard var eventAttributes = getCommonEventAttributes(isForSplitFlow: isForSplitFlow),
              let viewModel = commonViewModel
        else { return }

        let errorCode = error.viewModel?.error?.code ?? 0
        let otherAttributes = [
            AnalyticsAttributeKey.page: source,
            AnalyticsAttributeKey.errorCode: String(errorCode),
            AnalyticsAttributeKey.reason: viewModel.returnErrorCodeReason(code: errorCode)
        ]
        eventAttributes.merge(otherAttributes) { _, new in new }
        analyticsService.logMayaEvents(
            name: AnalyticsConstants.Screen.Error.ekycV6.appear(),
            keyAttributes: eventAttributes
        )
    }

    func logEKYCToastErrorEvent(source: String, reason: String, isForSplitFlow: Bool = false) {
        guard var eventAttributes = getCommonEventAttributes(isForSplitFlow: isForSplitFlow),
              let viewModel = commonViewModel
        else { return }

        let otherAttributes = [
            AnalyticsAttributeKey.page: source,
            AnalyticsAttributeKey.reason: reason
        ]
        eventAttributes.merge(otherAttributes) { _, new in new }
        analyticsService.logMayaEvents(
            name: AnalyticsConstants.Screen.Error.ekycV6.appear(),
            keyAttributes: eventAttributes
        )
    }

    func logEKYCAppearEvent(name: String, source: String, otherEvents: [AnalyticsAttributeKey: String]?, isForSplitFlow: Bool = false) {
        guard var eventAttributes = getCommonEventAttributes(isForSplitFlow: isForSplitFlow) else { return }

        let source = [AnalyticsAttributeKey.source: source]
        eventAttributes.merge(source) { _, new in new }
        if let otherEvents = otherEvents {
            eventAttributes.merge(otherEvents) { _, new in new }
        }
        analyticsService.logMayaEvents(
            name: name,
            keyAttributes: eventAttributes
        )
    }

    func logEKYCTapEvent(name: String, destination: String, action: String, otherEvents: [AnalyticsAttributeKey: String]? = nil, isForSplitFlow: Bool = false) {
        guard var eventAttributes = getCommonEventAttributes(isForSplitFlow: isForSplitFlow) else { return }

        let standardTapEvents = [AnalyticsAttributeKey.destination: destination, AnalyticsAttributeKey.action: action]
        eventAttributes.merge(standardTapEvents) { _, new in new }
        if let otherEvents = otherEvents {
            eventAttributes.merge(otherEvents) { _, new in new }
        }
        analyticsService.logMayaEvents(
            name: name,
            keyAttributes: eventAttributes
        )
    }

    func logEKYCInlineErrorEvent(name: String, field: String, error: String, isForSplitFlow: Bool = false) {
        guard var eventAttributes = getCommonEventAttributes(isForSplitFlow: isForSplitFlow) else { return }

        let otherEvents = [AnalyticsAttributeKey.field: field,
                           AnalyticsAttributeKey.error: error]
        eventAttributes.merge(otherEvents) { _, new in new }
        analyticsService.logMayaEvents(
            name: name,
            keyAttributes: eventAttributes
        )
    }

    func getCommonEventAttributes(isForSplitFlow: Bool = false) -> [AnalyticsAttributeKey: String]? {
        guard let viewModel = commonViewModel,
              let user = user
        else { return  nil }
        var commonEventAttributes = [
            AnalyticsAttributeKey.partner: MayaOnboardingUtility().fetchOnboardingPartner() ?? "",
            AnalyticsAttributeKey.branch: MayaOnboardingUtility().fetchOnboardingBranch(),
            AnalyticsAttributeKey.kycStatus: user.kycStatus,
            AnalyticsAttributeKey.kycAction: viewModel.kycActionValue,
            AnalyticsAttributeKey.kycPresubmissionId: viewModel.preSubmissionId.value ?? "",
            AnalyticsAttributeKey.idtype1: viewModel.selectedDocument.value?.key ?? "",
            AnalyticsAttributeKey.idtype2: viewModel.selectedSecondaryDocument.value?.key ?? ""
        ]

        if isForSplitFlow {
            commonEventAttributes[AnalyticsAttributeKey.path] = Constants.EKYC.tofuFlowAnalytics
        }
        return commonEventAttributes
    }
}

extension EKYCZolozCoordinator {
    func startMaintenanceFlow(completion: (() -> Void)? = nil) {
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User else { return }

        let viewController = StoryboardScene.MayaEKYC.mayaEKYCMaintenanceViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.kycLevel = user.kycLevelValue
        setRootModule(viewController, animated: true)
        presenter?.present(router, animated: true, completion: completion)
    }

    func startNormalFlow(completion: (() -> Void)? = nil) {
        let viewController = StoryboardScene.EKYCZoloz.mayaZolozBenefitsAndStepsViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(commonViewModel)
        viewController.analyticsModule = analyticsModule
        setRootModule(viewController, animated: true)
        presenter?.present(router, animated: true, completion: completion)
    }

    func startNewKYCIntroFlow(completion: (() -> Void)? = nil) {
        let viewController = StoryboardScene.EKYCZoloz.mayaEKYCIntroScreenViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(commonViewModel)
        setRootModule(viewController, animated: true)
        presenter?.present(router, animated: true, completion: completion)
    }

    func startReferralInputCodeFlow() {
        let coordinator = MayaReferralInputCodeCoordinator(presenter: router)
        coordinator.source = .profile
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startManualReKYCFlow() {
        let viewController = StoryboardScene.MayaEKYC.mayaReKYCOtherIDViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        setRootModule(viewController, animated: true)
        presenter?.present(router, animated: true, completion: nil)
    }

    func showProfilePage(completion: (() -> Void)? = nil) {
        let viewController = StoryboardScene.More.mayaProfileViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.bind(MayaProfileViewModel())
        setRootModule(viewController, animated: false)
        presenter?.present(router, animated: true, completion: completion)
    }
}

// MARK: Default Error View Delegate Methods
extension EKYCZolozCoordinator: MayaDefaultErrorViewDelegate {
    func didTapRetry(_ view: MayaDefaultErrorView) {
        guard let presenterView = presenter?.toPresentable().view else { return }
        if view.isDescendant(of: presenterView) {
            view.removeFromSuperview()
        }
    }
}

// MARK: Beneftis And Steps View Controller Delegate Methods
extension EKYCZolozCoordinator: MayaZolozBenefitsAndStepsViewControllerDelegate {
    func didTapPresentAcceptedIDs(_ viewController: MayaZolozBenefitsAndStepsViewController, action: String) {
        showAcceptedIDsScreen()
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.intro.tapped(), destination: AnalyticsConstants.ScreenName.acceptedIds, action: action)
    }

    func didTapLetsGo(_ viewController: MayaZolozBenefitsAndStepsViewController, action: String) {
        guard let viewModel = commonViewModel else { return }
        if viewModel.isKYCTofuUXRedesignEnabled {
            showRedesignedSelectIDScreen()
        } else {
            showSelectIDScreen()
        }
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.intro.tapped(), destination: AnalyticsConstants.ScreenName.selectId, action: action)
    }

    func didTapLearnMore(_ viewController: MayaZolozBenefitsAndStepsViewController) {
	   showExampleTransactionDetailsScreen()
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.intro.tapped(), destination: AnalyticsConstants.ScreenName.exampleWhenSendingMoney, action: AnalyticsConstants.ScreenName.learnMore)
    }

    func didTapDataPrivacy(_ viewController: MayaZolozBenefitsAndStepsViewController, action: String) {
        showPrivacyStatementScreen()
        let destination = Constants.WebView.privacy.url?.absoluteString ?? ""
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.intro.tapped(), destination: destination, action: action)
    }

    func didCancelEKYCBenefits() {
        logEKYCBackEvent(source: AnalyticsConstants.ScreenName.intro, destination: AnalyticsConstants.ScreenName.dashboard)
    }

    func didAppearEKYCBenefits() {
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.intro.appear(), source: AnalyticsConstants.ScreenName.loader, otherEvents: nil)
    }

    func didTapBenefitsToggle(action: String) {
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.intro.tapped(), destination: "", action: action)
    }
}

// MARK: Intro Screen View Controller Delegate Methods
extension EKYCZolozCoordinator: MayaEKYCIntroScreenViewControllerDelegate {
    func didCancelEKYCIntroScreen() {
        logEKYCBackEvent(source: AnalyticsConstants.ScreenName.intro, destination: AnalyticsConstants.ScreenName.dashboard)
    }

    func didAppearEKYCIntroScreen() {
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.intro.appear(), source: AnalyticsConstants.ScreenName.loader, otherEvents: nil, isForSplitFlow: true)
    }

    func didTapPrimaryButton(_ viewController: MayaEKYCIntroScreenViewController, action: String) {
        guard let viewModel = commonViewModel else { return }
        if viewModel.isKYCTofuUXRedesignEnabled {
            showRedesignedSelectIDScreen()
        } else {
            showSelectIDScreen()
        }
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.intro.tapped(), destination: AnalyticsConstants.ScreenName.selectId, action: action, isForSplitFlow: true)
    }

    func didTapSecondaryButton(_ viewController: MayaEKYCIntroScreenViewController, action: String) {
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.intro.tapped(), destination: AnalyticsConstants.ScreenName.dashboard, action: action, isForSplitFlow: true)
    }

    func didTapLearnMore(_ viewController: MayaEKYCIntroScreenViewController, action: String) {
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.intro.tapped(), destination: AnalyticsConstants.ScreenName.exampleWhenSendingMoney, action: action, isForSplitFlow: true)
        showExampleTransactionDetailsScreen()
    }

    func didTapSeeAcceptedIDs(_ viewController: MayaEKYCIntroScreenViewController, action: String) {
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.intro.tapped(), destination: AnalyticsConstants.ScreenName.acceptedIds, action: action, isForSplitFlow: true)
        showAcceptedIDsScreen()
    }

    func didTapInviteCode(_ viewController: MayaEKYCIntroScreenViewController, action: String) {
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.intro.tapped(), destination: AnalyticsConstants.ScreenName.submitInviteCode, action: action, isForSplitFlow: true)
        startReferralInputCodeFlow()
    }
}

extension EKYCZolozCoordinator: MayaComplianceCoordinatorDelegate {
    func onCompleteCompliance(_ coordinator: MayaComplianceCoordinator) {
        self.removeChild(coordinator)
        showSubmissionFlowWithFatcaChecking()
    }

    func onTapBack(source: String, destination: String) {
        logEKYCBackEvent(source: source, destination: destination)
    }

    func didAppearScreen(screen: MayaComplianceScreens, caller: String, source: String) {
        var name: String
        switch screen {
        case .fatca:
            name = AnalyticsConstants.Screen.Fatca.none.appear()
        case .dosri:
            name = AnalyticsConstants.Screen.Dosri.none.appear()
        }

        let appearEvents: [AnalyticsAttributeKey: String] = [
            AnalyticsAttributeKey.caller: caller
        ]

        logEKYCAppearEvent(name: name, source: source, otherEvents: appearEvents)
    }

    func didTapElement(screen: MayaComplianceScreens, caller: String, action: String, destination: String) {
        var name: String
        switch screen {
        case .fatca:
            name = AnalyticsConstants.Screen.Fatca.none.tapped()
        case .dosri:
            name = AnalyticsConstants.Screen.Dosri.none.tapped()
        }

        let tapEvents: [AnalyticsAttributeKey: String] = [
            AnalyticsAttributeKey.caller: caller
        ]

        logEKYCTapEvent(name: name, destination: destination, action: action, otherEvents: tapEvents)
    }

    func didShowDosriInlineError(field: String, error: String) {
        logEKYCInlineErrorEvent(name: AnalyticsConstants.Screen.Dosri.none.error(), field: field, error: error)
    }
}

// MARK: Accepted IDs View Controller Delegate Methods
extension EKYCZolozCoordinator: MayaZolozAcceptedIDsViewControllerDelegate {
    func didTapClose(_ viewController: MayaZolozAcceptedIDsViewController) {
        closeAcceptedIDsScreen(viewController)
    }
}

// MARK: MayaZolozExampleTransactionViewControllerDelegate Methods
extension EKYCZolozCoordinator: MayaZolozExampleTransactionViewControllerDelegate {
    func didTapGotIt(_ viewController: MayaZolozExampleTransactionViewController) {
	   closeExampleTransactionDetailsScreen(viewController)
    }
}

// MARK: MayaZolozIdSubmitViewControllerDelegate Methods
extension EKYCZolozCoordinator: MayaZolozIdSubmitViewControllerDelegate {
    func didTapContinue(_ viewController: MayaZolozIdSubmitViewController, selectedDocument: MayaEKYCSelectedDocument, action: String) {
        guard let commonViewModel = commonViewModel else { return }

        commonViewModel.frontIDKey.value = nil
        commonViewModel.backIDKey.value = nil

        commonViewModel.selectedDocument.value = selectedDocument
        if let captureMethod = selectedDocument.captureGuide?.captureMethods.first?.method {
            commonViewModel.primaryCaptureMethod.value = selectedDocument.key + ";" + captureMethod
        } else {
            commonViewModel.primaryCaptureMethod.value = nil
        }

        // NOTE: Will always be false if Other ID toggle is OFF
        if selectedDocument.key == Constants.EKYC.Document.OtherID.key {
            if isAccountUpgraded {
                showOtherIDForReKYCScreen()
                return
            }
            return
        }

        if selectedDocument.captureGuide?.samples?.count != 0 && commonViewModel.isPhilsysFlowEnabled {
            showPhilsysIDGuideScreen()
        } else {
            showPhotoTipsScreen()
        }

        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.selectID.tapped(), destination: AnalyticsConstants.ScreenName.idTips, action: action)
    }

    func didCancelIDSubmit() {
        logEKYCBackEvent(source: AnalyticsConstants.ScreenName.selectId, destination: AnalyticsConstants.ScreenName.intro)
    }

    func didAppearSelectID() {
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.selectID.appear(), source: AnalyticsConstants.ScreenName.intro, otherEvents: nil)
    }

    func didSelectID(firstID: String) {
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.selectID.tapped(), destination: "", action: firstID)
    }

    func didTapShowMoreDisclosure(action: String) {
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.selectID.tapped(), destination: "", action: action)
    }
}

// MARK: MayaEKYCIDSelectViewControllerDelegate Methods
extension EKYCZolozCoordinator: MayaEKYCIDSelectViewControllerDelegate {
    func didTapBackIDSelect() {
        logEKYCBackEvent(source: AnalyticsConstants.ScreenName.selectId, destination: AnalyticsConstants.ScreenName.intro, isForSplitFlow: true)
    }

    func didAppearIDSelect() {
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.selectID.appear(), source: AnalyticsConstants.ScreenName.intro, otherEvents: nil, isForSplitFlow: true)
    }

    func didTapShowMoreIDSelect(action: String) {
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.selectID.tapped(), destination: "", action: action, isForSplitFlow: true)
    }

    func didSelectID(selectedDocument: MayaEKYCSelectedDocument) {
        guard let commonViewModel = commonViewModel else { return }

        commonViewModel.frontIDKey.value = nil
        commonViewModel.backIDKey.value = nil

        commonViewModel.selectedDocument.value = selectedDocument
        if let captureMethod = selectedDocument.captureGuide?.captureMethods.first?.method {
            commonViewModel.primaryCaptureMethod.value = selectedDocument.key + ";" + captureMethod
        } else {
            commonViewModel.primaryCaptureMethod.value = nil
        }

        // NOTE: Will always be false if Other ID toggle is OFF
        if selectedDocument.key == Constants.EKYC.Document.OtherID.key {
            if isAccountUpgraded {
                showOtherIDForReKYCScreen()
                return
            }
            return
        }

        if selectedDocument.captureGuide?.samples?.count != 0 && commonViewModel.isPhilsysFlowEnabled {
            logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.selectID.tapped(), destination: AnalyticsConstants.ScreenName.idGuide, action: selectedDocument.document.description, isForSplitFlow: true)
            showPhilsysIDGuideScreen()
        } else {
            logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.selectID.tapped(), destination: AnalyticsConstants.ScreenName.idCapture, action: selectedDocument.document.description, isForSplitFlow: true)
            showCameraPermisionScreenIfNeeded()
        }
    }
}

extension EKYCZolozCoordinator: MayaZolozTipsViewControllerDelegate {
    func didTapContinue(_ viewController: MayaZolozTipsViewController, action: String) {
        guard let commonViewModel = commonViewModel else { return }

        // MARK: Add switch cases here when new CaptureMethods are introduced
        showCameraPermisionScreenIfNeeded()
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.idTips.tapped(), destination: AnalyticsConstants.ScreenName.idCapture, action: action)
    }

    func didCancelTips(source: String) {
        logEKYCBackEvent(source: source, destination: AnalyticsConstants.ScreenName.selectId)
    }

    func didAppearIDTips() {
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.idTips.appear(), source: AnalyticsConstants.ScreenName.selectId, otherEvents: nil)
    }
}

// MARK: Maya Liveness Tips View Controller Delegate Methods
extension EKYCZolozCoordinator: MayaLivenessTipsViewControllerDelegate {
    func didTapContinue(_ viewController: MayaLivenessTipsViewController, action: String) {
        // MARK: Add switch cases here when new CaptureMethods are introduced
        #if targetEnvironment(simulator)
        // Tencent flow camera won't work on simulator
        // NOTE: Bypass so we can test Personal info screen. Personal Info screen cannot post submission in simulator.
        showCompletePersonalInformationScreen()
        #else
        startTencentLivenessCaptureFlow()
        #endif
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.livenessTips.tapped(), destination: AnalyticsConstants.ScreenName.livenessCapture, action: action)
    }

    func didTapBack(_ viewController: MayaLivenessTipsViewController) {
        showChangedYourMindScreen(source: AnalyticsConstants.ScreenName.livenessTips)
    }

    func didAppearLivenessTips() {
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.livenessTips.appear(), source: AnalyticsConstants.ScreenName.idCaptureResult, otherEvents: nil)
    }
}

// MARK: Maya Selfie Tips View Controller Delegate Methods
extension EKYCZolozCoordinator: MayaEKYCSelfieTipsViewControllerDelegate {
    func didTapContinue(_ viewController: MayaEKYCSelfieTipsViewController, action: String) {
        // MARK: Add switch cases here when new CaptureMethods are introduced
        #if targetEnvironment(simulator)
        // Tencent flow camera won't work on simulator
        // NOTE: Bypass so we can test Personal info screen. Personal Info screen cannot post submission in simulator.
        showCompletePersonalInformationScreen()
        #else
        startTencentLivenessCaptureFlow()
        #endif
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.livenessTips.tapped(), destination: AnalyticsConstants.ScreenName.livenessCapture, action: action)
    }

    func didTapBack(_ viewController: MayaEKYCSelfieTipsViewController) {
        showChangedYourMindScreen(source: AnalyticsConstants.ScreenName.livenessTips)
    }

    func didAppearSelfieTips() {
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.livenessTips.appear(), source: AnalyticsConstants.ScreenName.idCaptureResult, otherEvents: nil)
    }
}

// MARK: Personal Information View Controller Delegate Methods
extension EKYCZolozCoordinator: MayaPersonalInformationViewControllerDelegate {
    func didReceivePreSubmissionError(withError error: PayMayaError) {
        logEKYCErrorEvent(source: AnalyticsConstants.ScreenName.personalInfo, error: error)
        let errorViewModel = ErrorAlertViewModel(title: CommonStrings.Maya.Error.Default.title)

        if case .sessionTimeout = error.type {
            return
        } else if case .preSubmissionIdExpired = error.type {
            self.analyticsService.logMayaEvents(action: AnalyticsAction.invalid(Analytics.EKYCV6.preSubmissionId))
            self.showMayaError(viewModel: errorViewModel, shouldExitKYC: true)
        } else {
            // MARK: Show generic error
            self.showMayaError(viewModel: errorViewModel)
        }
    }

    func presentPickerView(_ viewController: MayaPersonalInformationViewController, model: MayaPersonalInformationPickerViewModel) {
        viewController.view.endEditing(true)
        showPickerScreen(model: model)
    }

    func didTapLearnMore(_ viewController: MayaPersonalInformationViewController) {
        showNameRulesScreen()
    }

    func didTapContinueButton(_ submissionData: PostSubmissionRequest, isMinor: Bool, regulatoryStatus: MayaRegulatoryStatus?, regulatoryCMS: MayaRegulatoryCMSData?, isFlaggedFatca: Bool, fatcaReasons: [String]?) {
        guard let viewModel = commonViewModel else { return }
        viewModel.submissionData.value = submissionData
        viewModel.regulatoryStatus.value = regulatoryStatus
        viewModel.regulatoryCMS.value = regulatoryCMS
        viewModel.isFlaggedFatca.value = isFlaggedFatca
        viewModel.fatcaReasons.value = fatcaReasons
        viewModel.isMinor.value = isMinor
        showAllGoodScreen(source: AnalyticsConstants.ScreenName.personalInfo, minor: isMinor, onConfirm: { [weak self] in
            if isMinor {
                self?.showAdditionalDocumentsScreen()
            } else if viewModel.isFATCAEnabled,
                      let isFatcaRequired = viewModel.regulatoryStatus.value?.fatca.isRequired,
                      isFatcaRequired,
                      !isFlaggedFatca {
                self?.showFATCAFlow()
            } else if viewModel.isDosriEnabled,
                      let isDosriRequired = viewModel.regulatoryStatus.value?.dosri.isRequired,
                      isDosriRequired {
                self?.showDosriFlow()
            } else if isFlaggedFatca {
                viewModel.callPatchFatca()
            } else {
                self?.showSubmissionScreen()
            }
        })
    }

    func didTapContinueButtonBackIDFlow(_ submissionData: PostSubmissionRequestV2, isMinor: Bool, regulatoryStatus: MayaRegulatoryStatus?, regulatoryCMS: MayaRegulatoryCMSData?, isFlaggedFatca: Bool, fatcaReasons: [String]?) {
        guard let viewModel = commonViewModel else { return }
        viewModel.submissionDataV2.value = submissionData
        viewModel.regulatoryStatus.value = regulatoryStatus
        viewModel.regulatoryCMS.value = regulatoryCMS
        viewModel.isFlaggedFatca.value = isFlaggedFatca
        viewModel.fatcaReasons.value = fatcaReasons
        viewModel.isMinor.value = isMinor
        showAllGoodScreen(source: AnalyticsConstants.ScreenName.personalInfo, minor: isMinor, onConfirm: { [weak self] in
            if isMinor {
                self?.showAdditionalDocumentsScreen()
            } else if viewModel.isFATCAEnabled,
                      let isFatcaRequired = viewModel.regulatoryStatus.value?.fatca.isRequired,
                      isFatcaRequired,
                      !isFlaggedFatca {
                self?.showFATCAFlow()
            } else if viewModel.isDosriEnabled,
                      let isDosriRequired = viewModel.regulatoryStatus.value?.dosri.isRequired,
                      isDosriRequired {
                self?.showDosriFlow()
            } else if isFlaggedFatca {
                viewModel.callPatchFatca()
            } else {
                self?.showSubmissionScreenBackIDFlow()
            }
        })
    }

    func didTapBack(_ viewController: MayaPersonalInformationViewController) {
        showChangedYourMindScreen(source: AnalyticsConstants.ScreenName.personalInfo)
    }

    func didShowInlineError(field: String, error: String) {
        self.logEKYCInlineErrorEvent(name: AnalyticsConstants.Screen.Ekyc.personalInfo.error(), field: field, error: error)
    }

    func didTapElement(action: String?, destination: String?, header: String?, toBottomSheet: Bool) {
        guard var action = action,
              var destination = destination else { return }

        if let header = header {
            action = header.replacingOccurrences(of: "*", with: "") + " " + action.lowercased()
        }

        if toBottomSheet {
            destination = destination + " " + Constants.EKYC.bottomsheetAnalytics
        }

        self.logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.personalInfo.tapped(), destination: destination, action: action)
    }

    func didReceiveAddressDropDownError() {
        let errorViewModel = ErrorAlertViewModel(
            title: L10n.Maya.Ekyc.CompletingPersonalInformation.AddressDropdown.Error.title,
            message: L10n.Maya.Ekyc.CompletingPersonalInformation.AddressDropdown.Error.message,
            image: CommonAsset.Images.Alert.imageDefaultError.image
        )
        // MARK: Show generic error
        self.showMayaError(viewModel: errorViewModel)
    }

    func didAppearPersonalInformationScreen(events: [AnalyticsAttributeKey: String]) {
        var source: String
        guard let viewModel = commonViewModel else { return }

        if viewModel.selectedSecondaryDocument.value != nil && self.configurationServices.secondaryIDFlowEnabled.value {
            source = AnalyticsConstants.ScreenName.captureSecondId
        } else {
            source = AnalyticsConstants.ScreenName.livenessCapture
        }
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.personalInfo.appear(), source: source, otherEvents: events)
    }
}

// MARK: MayaZolozUnderReviewViewControllerDelegate Methods
extension EKYCZolozCoordinator: MayaZolozUnderReviewViewControllerDelegate {
    func didTapShowAccountDetails(action: String) {
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.idSubmitted.tapped(), destination: AnalyticsConstants.ScreenName.kycSubmitted, action: action)
        if configurationServices.kycCompletedCallbackEnabled.value == true, let callback = self.onCompleteKyc {
            callback()
        }
        showAccountDetails()
    }

    func didTapBackToHome(action: String) {
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.idSubmitted.tapped(), destination: AnalyticsConstants.ScreenName.dashboard, action: action)
        if configurationServices.kycCompletedCallbackEnabled.value == true, let callback = self.onCompleteKyc {
            callback()
        }
        dismissModule()
    }

    func didAppearPersonalInformationScreen() {
        var source: String
        guard let viewModel = commonViewModel else { return }

        if viewModel.isMinor.value {
            source = AnalyticsConstants.ScreenName.docsMinor
        } else {
            source = AnalyticsConstants.ScreenName.personalInfo
        }
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.idSubmitted.appear(), source: source, otherEvents: nil)
    }
}

// MARK: Private Presentation Methods
private extension EKYCZolozCoordinator {
    func showError() {
        let viewController = ZolozKYCDataErrorViewController()
        let actionClosure = { [weak self, weak viewController] in
            guard let self = self,
                  let viewController = viewController else { return }
            viewController.didTapBack(self)
        }
        let errorAlertViewModel = ErrorAlertViewModel(title: CommonStrings.Maya.Error.Default.title,
                                                      closeButtonClosure: actionClosure,
                                                      closeButtonTitle: L10n.EkycZoloz.ErrorView.actionTitle)
        viewController.backActionDelegate  = self
        viewController.routeActionDelegate = self
        viewController.setErrorViewModel(errorAlertViewModel)
        setRootModule(viewController, animated: true)
        presenter?.present(router, animated: true, completion: nil)
    }

    func showMayaError(viewModel: ErrorAlertViewModel, shouldExitKYC: Bool = false, modalViewController: ViewController? = nil) {
        let alertViewModel = MayaAlertViewModel(title: viewModel.title,
                                                message: viewModel.message,
                                                image: viewModel.image,
                                                closeButtonTitle: viewModel.closeButtonTitle)
        let coordinator = MayaAlertModalCoordinator(presenter: router, viewModel: alertViewModel, withCloseButton: false)

        let cancelAction = MayaAlertAction(title: alertViewModel.closeButtonTitle, style: .primary) { [weak self, weak coordinator] in
            guard let self = self, let coordinator = coordinator else { return }
            coordinator.router.dismissModule(animated: true) {
                self.removeChild(coordinator)
                if shouldExitKYC {
                    // MARK: Pre-submission timeout occurred in Simple Capture Image ID Upload Modal ViewController
                    modalViewController?.dismiss(animated: false)

                    self.dismissModule()
                }
            }
        }

        alertViewModel.addAction(cancelAction)
        coordinator.routeActionDelegate = self
        coordinator.backActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func showMayaErrorMultipleSubmission(viewModel: ErrorAlertViewModel, modalViewController: ViewController? = nil) {
        let alertViewModel = MayaAlertViewModel(title: L10n.Maya.Ekyc.Alert.MultipleSubmission.title,
                                                message: L10n.Maya.Ekyc.Alert.MultipleSubmission.message,
                                                image: viewModel.image,
                                                closeButtonTitle: viewModel.closeButtonTitle)
        let coordinator = MayaAlertModalCoordinator(presenter: router, viewModel: alertViewModel, withCloseButton: false)

        let cancelAction = MayaAlertAction(title: alertViewModel.closeButtonTitle, style: .primary) { [weak self, weak coordinator] in
            guard let self = self, let coordinator = coordinator else { return }
            coordinator.router.dismissModule(animated: true) {
                self.removeChild(coordinator)
                modalViewController?.dismiss(animated: false)
                self.dismissAndPushProfile()
            }
        }

        alertViewModel.addAction(cancelAction)
        coordinator.routeActionDelegate = self
        coordinator.backActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func showAcceptedIDsScreen() {
        let viewController = StoryboardScene.EKYCZoloz.mayaZolozAcceptedIDsViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(commonViewModel)
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func closeAcceptedIDsScreen(_ viewController: MayaZolozAcceptedIDsViewController) {
        router.popModule(animated: true)
    }

    func showExampleTransactionDetailsScreen() {
	   let viewController = StoryboardScene.EKYCZoloz.mayaZolozExampleTransactionViewController.instantiate()
	   viewController.backActionDelegate = self
	   viewController.routeActionDelegate = self
	   viewController.delegate = self
	   router.present(viewController, animated: true, completion: nil)
    }

    func closeExampleTransactionDetailsScreen(_ viewController: MayaZolozExampleTransactionViewController) {
	   viewController.dismiss(animated: true)
    }

    func showSelectIDScreen() {
        guard let commonViewModel = commonViewModel,
              let kycData = commonViewModel.kycData.value,
              let recommendedDocuments = kycData.orderedVisibleDocuments()
        else { return }

        commonViewModel.resetSecondaryIDValues()
        let documents = MayaEKYCIdSubmitDataSource(
            recommendedPrimaryIds: recommendedDocuments,
            otherPrimaryIds: kycData.otherPrimaryDocuments,
            secondaryIds: kycData.secondaryDocuments,
            captureGuides: kycData.captureGuides
        )

        let viewModel = MayaZolozIdSubmitViewModel(inputData: documents)
        let viewController = StoryboardScene.EKYCZoloz.mayaZolozIdSubmitViewController.instantiate()
        progressBarView?.progressBar.setProgress(to: viewController.segment, animated: true)
        viewController.navigationBarTitleView = progressBarView
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(viewModel)
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func showRedesignedSelectIDScreen() {
        guard let commonViewModel = commonViewModel,
              let kycData = commonViewModel.kycData.value,
              let recommendedDocuments = kycData.orderedVisibleDocuments()
        else { return }

        commonViewModel.resetSecondaryIDValues()
        let documents = MayaEKYCIdSubmitDataSource(
            recommendedPrimaryIds: recommendedDocuments,
            otherPrimaryIds: kycData.otherPrimaryDocuments,
            secondaryIds: kycData.secondaryDocuments,
            captureGuides: kycData.captureGuides
        )

        let viewModel = MayaZolozIdSubmitViewModel(inputData: documents)
        let viewController = StoryboardScene.EKYCZoloz.mayaEKYCIDSelectViewController.instantiate()
        progressBarView?.progressBar.setProgress(to: viewController.segment, animated: true)
        viewController.navigationBarTitleView = progressBarView
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(viewModel)
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func showPhotoTipsScreen() {
        guard let commonViewModel = commonViewModel,
              let kycData = commonViewModel.kycData.value else { return }

        let photoTips = kycData.orderedPhotoTips
        let viewModel = MayaZolozTipsViewModel(tips: photoTips, type: KYCData.MediaType.photo, isSecondaryFlow: commonViewModel.secondaryIDFlow.value)
        let viewController = StoryboardScene.EKYCZoloz.mayaZolozTipsViewController.instantiate()
        viewController.navigationBarTitleView = progressBarView
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(viewModel)
        progressBarView?.progressBar.setProgress(to: viewController.segment, animated: true)
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func showPhilsysIDGuideScreen() {
        guard let commonViewModel = commonViewModel,
              let kycData = commonViewModel.kycData.value,
              let selectedDocument = commonViewModel.selectedDocument.value else { return }
        let viewController = StoryboardScene.MayaEKYC.mayaEKYCPhilsysIDGuideViewController.instantiate()
        let viewModel = MayaEKYCPhilsysIDGuideViewModel(selectedDocument: selectedDocument)
        viewController.navigationBarTitleView = progressBarView
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(viewModel)
        progressBarView?.progressBar.setProgress(to: viewController.segment, animated: true)
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func showPhilsysIDUploadScreen() {
        guard let commonViewModel = commonViewModel,
              let kycData = commonViewModel.kycData.value,
              let selectedDocument = commonViewModel.selectedDocument.value,
              let uploadGuide = kycData.uploadGuides,
              let imageQguides = kycData.imageQualityGuides,
              let presubmissionId = commonViewModel.preSubmissionId.value else { return }
        let viewController = StoryboardScene.MayaEKYC.mayaEKYCPhilsysIDUploadViewController.instantiate()
        let viewModel = MayaEKYCPhilsysIDUploadViewModel(uploadGuide: uploadGuide, document: selectedDocument, imageQGuides: imageQguides, presubmissionId: presubmissionId)
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(viewModel)
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func showLivenessTipsScreen() {
        guard let commonViewModel = commonViewModel,
              let kycData = commonViewModel.kycData.value else { return }

        let livenessTips = kycData.orderedLivenessTips
        let viewModel = MayaZolozTipsViewModel(tips: livenessTips, type: KYCData.MediaType.liveness)
        let viewController = StoryboardScene.MayaEKYC.mayaLivenessTipsViewController.instantiate()
        viewController.navigationBarTitleView = progressBarView
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(viewModel)
        progressBarView?.progressBar.setProgress(to: viewController.segment, animated: true)
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func showSelfieTipsScreen() {
        guard let commonViewModel = commonViewModel else { return }

        let viewController = StoryboardScene.MayaEKYC.mayaEKYCSelfieTipsViewController.instantiate()
        viewController.navigationBarTitleView = progressBarView
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(commonViewModel)
        progressBarView?.progressBar.setProgress(to: viewController.segment, animated: true)
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func showSelectAnotherIDScreen() {
        guard let commonViewModel = commonViewModel,
              let kycData = commonViewModel.kycData.value,
              let captureGuides = kycData.captureGuides,
              let secondaryDocumentList = kycData.secondaryDocuments,
              let selectedDocument = commonViewModel.selectedDocument.value
        else { return }

        let viewModel = MayaEKYCSecondaryIDViewModel(secondaryDocumentList: secondaryDocumentList, captureGuideList: captureGuides, initialDocument: selectedDocument)

        if viewModel.isKYCTofuUXRedesignEnabled {
            let viewController = StoryboardScene.MayaEKYC.mayaEKYCNewSecondaryIDViewController.instantiate()
            viewController.navigationBarTitleView = progressBarView
            viewController.backActionDelegate = self
            viewController.routeActionDelegate = self
            viewController.delegate = self
            viewController.bind(viewModel)
            progressBarView?.progressBar.setProgress(to: viewController.segment, animated: true)
            router.push(viewController, animated: true, popCompletion: nil)
        } else {
            let viewController = StoryboardScene.MayaEKYC.mayaEKYCSecondaryIDViewController.instantiate()
            viewController.navigationBarTitleView = progressBarView
            viewController.backActionDelegate = self
            viewController.routeActionDelegate = self
            viewController.delegate = self
            viewController.bind(viewModel)
            progressBarView?.progressBar.setProgress(to: viewController.segment, animated: true)
            router.push(viewController, animated: true, popCompletion: nil)
        }
    }

    func showCameraPermisionScreenIfNeeded() {
        let coordinator = MayaCameraPermissionCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start()
    }

    // MARK: - SimpleCapture V1
    func showSimpleCaptureIDScreen() {
        guard let commonViewModel = commonViewModel,
              let livePhotoTipsString = commonViewModel.kycData.value?.livePhotoTipsString,
              let selectedDocument = getSelectedDocument()
        else { return }

        let viewModel = MayaEKYCSimpleCaptureIDViewModel(document: selectedDocument, scanType: .front,
                                                         progressSegments: progressSegments,
                                                         livePhotoTipsString: livePhotoTipsString,
                                                         isSecondaryFlow: commonViewModel.secondaryIDFlow.value,
                                                         imageQualityGuides: commonViewModel.kycData.value?.imageQualityGuides ?? nil,
                                                         imageQualityMaxRetries: commonViewModel.kycData.value?.imageQualityMaxRetries ?? Constants.EKYC.defaultImageQualityMaxRetries)
        let viewController = StoryboardScene.MayaEKYC.mayaEKYCSimpleCaptureIDViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(viewModel)
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func showSimpleCaptureBackIDScreen() {
        guard let commonViewModel = commonViewModel,
              let livePhotoTipsString = commonViewModel.kycData.value?.livePhotoTipsString,
              let selectedDocument = getSelectedDocument()
        else { return }

        let viewModel = MayaEKYCSimpleCaptureIDViewModel(document: selectedDocument, scanType: .back,
                                                         progressSegments: progressSegments, livePhotoTipsString: livePhotoTipsString,
                                                         isSecondaryFlow: commonViewModel.secondaryIDFlow.value,
                                                         imageQualityMaxRetries: commonViewModel.kycData.value?.imageQualityMaxRetries ?? Constants.EKYC.defaultImageQualityMaxRetries)
        let viewController = StoryboardScene.MayaEKYC.mayaEKYCSimpleCaptureIDViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(viewModel)
        router.push(viewController, animated: true, popCompletion: nil)
    }

    // MARK: - SimpleCapture V3
    func showSimpleCaptureIDV3Screen() {
        guard let commonViewModel = commonViewModel,
              let livePhotoTipsString = commonViewModel.kycData.value?.livePhotoNumberedTipsString,
              let selectedDocument = getSelectedDocument()
        else { return }

        let viewModel = MayaEKYCSimpleCaptureIDViewModel(
            document: selectedDocument,
            scanType: .front,
            progressSegments: progressSegments,
            livePhotoTipsString: livePhotoTipsString,
            isSecondaryFlow: commonViewModel.secondaryIDFlow.value,
            imageQualityGuides: commonViewModel.kycData.value?.imageQualityGuides ?? nil,
            imageQualityMaxRetries: commonViewModel.kycData.value?.imageQualityMaxRetries ?? Constants.EKYC.defaultImageQualityMaxRetries,
            frontKey: commonViewModel.frontIDKey.value)
        let viewController = StoryboardScene.MayaEKYC.mayaEKYCSimpleCaptureIDV3ViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(viewModel)
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func showSimpleCaptureBackIDV3Screen() {
        guard let commonViewModel = commonViewModel,
              let livePhotoTipsString = commonViewModel.kycData.value?.livePhotoNumberedTipsString,
              let selectedDocument = getSelectedDocument()
        else { return }

        let viewModel = MayaEKYCSimpleCaptureIDViewModel(document: selectedDocument, scanType: .back, progressSegments: progressSegments, livePhotoTipsString: livePhotoTipsString, isSecondaryFlow: commonViewModel.secondaryIDFlow.value, imageQualityMaxRetries: commonViewModel.kycData.value?.imageQualityMaxRetries ?? Constants.EKYC.defaultImageQualityMaxRetries, frontKey: commonViewModel.frontIDKey.value)
        let viewController = StoryboardScene.MayaEKYC.mayaEKYCSimpleCaptureIDV3ViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(viewModel)
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func showCompletePersonalInformationScreen() {
        guard let commonViewModel = commonViewModel,
              let kycData = commonViewModel.kycData.value,
              let preSubmissionId = commonViewModel.preSubmissionId.value,
              let selectedDocument = commonViewModel.selectedDocument.value
        else { return }

        let viewModel = MayaPersonalInformationViewModel(data: kycData,
                                                         provinces: commonViewModel.provinces.value,
                                                         preSubmissionId: preSubmissionId,
                                                         selectedDocument: selectedDocument,
                                                         selectedSecondaryDocument: commonViewModel.selectedSecondaryDocument.value)
        let viewController = StoryboardScene.EKYCZoloz.mayaPersonalInformationViewController.instantiate()
        progressBarView?.progressBar.setProgress(to: viewController.segment, animated: true)
        viewController.delegate = self
        viewController.routeActionDelegate = self
        viewController.backActionDelegate = self
        viewController.bind(viewModel)
        viewController.navigationBarTitleView = progressBarView
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func showPickerScreen(model viewModel: MayaPersonalInformationPickerViewModel) {
        let viewController = StoryboardScene.EKYCZoloz.mayaPersonalInformationPickerViewController.instantiate()
        viewController.bind(viewModel)
        viewController.backActionDelegate = self
        router.present(viewController, animated: true, completion: nil)
    }

    func showAllGoodScreen(source: String, minor: Bool, onConfirm: @escaping () -> Void) {
        let alertViewModel = MayaAlertViewModel(title: L10n.EkycZoloz.AllGoods.Alert.title,
                                                message: L10n.EkycZoloz.AllGoods.Alert.message,
                                                image: Asset.MayaImages.EKYCZoloz.bioSheet.image,
                                                actionsAxis: .horizontal)

        let coordinator = MayaAlertBottomSheetCoordinator(presenter: router,
                                                          viewModel: alertViewModel,
                                                          withCloseButton: false)
        let continueAction = MayaAlertAction(title: L10n.EkycZoloz.AllGoods.Alert.ContinueButton.title, style: .primary, handler: { [weak self, weak coordinator] in
                guard let self = self,
                      let coordinator = coordinator else { return }
                self.router.dismissModule(animated: true) {
                    self.logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.confirmSubmission.tapped(), destination: source, action: L10n.EkycZoloz.AllGoods.Alert.ContinueButton.title)
                    self.removeChild(coordinator)
                    onConfirm()
                }
        })

        let checkAgainAction = MayaAlertAction(title: L10n.EkycZoloz.AllGoods.Alert.CheckAgainButton.title, style: .secondary, handler: { [weak self, weak coordinator] in
            guard let self = self,
                  let coordinator = coordinator else { return }
            self.router.dismissModule(animated: true) {
                self.logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.confirmSubmission.tapped(), destination: source, action: L10n.EkycZoloz.AllGoods.Alert.CheckAgainButton.title)
                self.removeChild(coordinator)
            }
        })

        alertViewModel.addAction(continueAction)
        alertViewModel.addAction(checkAgainAction)

        let text = NSAttributedString(string: L10n.EkycZoloz.AllGoods.Alert.AdditionalInfo.message, attributes: [
            .font: CommonFontFamily.CerebriSansPro.medium.font(size: 12) as Any,
            .foregroundColor: CommonAsset.MayaColors.Grey.grey6.color
        ])
        alertViewModel.additionalInfo = MayaAlertAdditionalInfoType.custom(icon: CommonAsset.Images.Icons.iconInfo.image, title: "", subtitle: text, handler: nil)

        coordinator.routeActionDelegate = self
        coordinator.backActionDelegate = self
        addChild(coordinator)
        coordinator.start()
        guard let viewController = coordinator.rootViewController as? MayaAlertBottomSheetViewController else { return }
        viewController.additionalInfoCustomView.backgroundColor = CommonAsset.MayaColors.Grey.grey1.color
        viewController.additionalInfoCustomView.layer.borderColor = CommonAsset.MayaColors.Grey.grey1.color.cgColor
        let appearEvents: [AnalyticsAttributeKey: String] = [
            AnalyticsAttributeKey.isSuspectedMinor: AnalyticsConstants.ActionFunction().boolean(minor)
        ]
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.confirmSubmission.appear(), source: source, otherEvents: appearEvents)
    }

    func showChangedYourMindScreen(source: String) {
        // Log appear event
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.changeYourMind.appear(), source: source, otherEvents: nil)
        let alertViewModel = MayaAlertViewModel(title: L10n.EkycZoloz.ChangedYourMind.Alert.title,
                                                message: L10n.EkycZoloz.ChangedYourMind.Alert.message,
                                                image: Asset.MayaImages.EKYCZoloz.warning.image,
                                                actionsAxis: .horizontal)

        let coordinator = MayaAlertBottomSheetCoordinator(presenter: router,
                                                          viewModel: alertViewModel,
                                                          withCloseButton: false)

        let confirmAction = MayaAlertAction(title: L10n.EkycZoloz.ChangedYourMind.Alert.SubmitButton.title,
                                            style: .primary,
                                            handler: { [weak self, weak coordinator] in
            guard let self = self,
                  let coordinator = coordinator else { return }
            // Log EKYC6 Back Event then go back
            self.logEKYCBackEvent(source: source, destination: AnalyticsConstants.ScreenName.selectId)
            // Log EKYC6 Tap Event
            logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.changeYourMind.tapped(), destination: AnalyticsConstants.ScreenName.selectId, action: L10n.EkycZoloz.ChangedYourMind.Alert.SubmitButton.title)
            self.router.dismissModule(animated: true) {
                self.removeChild(coordinator)
            }
            if let viewController = self.router.navigationController?.viewControllers.first(where: { $0 is MayaZolozIdSubmitViewController }) {
                self.commonViewModel?.resetSecondaryIDValues()
                self.router.popToViewController(viewController)
            } else {
                self.router.popToRootViewController(animated: true)
                self.showSelectIDScreen()
            }
        })

        let cancelAction = MayaAlertAction(title: L10n.EkycZoloz.ChangedYourMind.Alert.CheckAgainButton.title,
                                           style: .secondary,
                                           handler: { [weak self, weak coordinator] in
            guard let self = self,
                  let coordinator = coordinator else { return }
            // Log Cancel Tap Action
            logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.changeYourMind.tapped(), destination: source, action: L10n.EkycZoloz.ChangedYourMind.Alert.CheckAgainButton.title)
            self.router.dismissModule(animated: true) {
                self.removeChild(coordinator)
            }
        })

        alertViewModel.addAction(confirmAction)
        alertViewModel.addAction(cancelAction)
        coordinator.routeActionDelegate = self
        coordinator.backActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func showChangedYourMindScreenSecondaryIDFlow() {
        // Log appear event
        let source = AnalyticsConstants.ScreenName.idCapture
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.changeYourMind.appear(), source: source, otherEvents: nil)
        let alertViewModel = MayaAlertViewModel(title: L10n.EkycZoloz.ChangedYourMind.Alert.title,
                                                message: L10n.EkycZoloz.ChangedYourMind.Alert.message,
                                                image: Asset.MayaImages.EKYCZoloz.warning.image,
                                                actionsAxis: .horizontal)

        let coordinator = MayaAlertBottomSheetCoordinator(presenter: router,
                                                          viewModel: alertViewModel,
                                                          withCloseButton: false)

        let confirmAction = MayaAlertAction(title: L10n.EkycZoloz.ChangedYourMind.Alert.SubmitButton.title,
                                            style: .primary,
                                            handler: { [weak self, weak coordinator] in
            guard let self = self,
                  let coordinator = coordinator else { return }
            // Log EKYC6 Back Event
            logEKYCBackEvent(source: source, destination: AnalyticsConstants.ScreenName.selectSecondId)
            // Log EKYC6 Tap Event
            logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.changeYourMind.tapped(), destination: AnalyticsConstants.ScreenName.selectSecondId, action: L10n.EkycZoloz.ChangedYourMind.Alert.SubmitButton.title)
            self.router.dismissModule(animated: true) {
                self.removeChild(coordinator)
            }
            if let viewController = self.router.navigationController?.viewControllers.first(where: { $0 is MayaEKYCSecondaryIDViewController }) {
                self.router.popToViewController(viewController)
            } else {
                self.router.popToRootViewController(animated: true)
                self.showSelectAnotherIDScreen()
            }
        })

        let cancelAction = MayaAlertAction(title: L10n.EkycZoloz.ChangedYourMind.Alert.CheckAgainButton.title,
                                           style: .secondary,
                                           handler: { [weak self, weak coordinator] in
            guard let self = self,
                  let coordinator = coordinator else { return }
            self.router.dismissModule(animated: true) {
                self.removeChild(coordinator)
            }
            // Log Cancel Tap Action
            logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.changeYourMind.tapped(), destination: "", action: L10n.EkycZoloz.ChangedYourMind.Alert.CheckAgainButton.title)
        })

        alertViewModel.addAction(confirmAction)
        alertViewModel.addAction(cancelAction)
        coordinator.routeActionDelegate = self
        coordinator.backActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func showNameRulesScreen() {
        guard let commonViewModel = commonViewModel,
              let kycData = commonViewModel.kycData.value
        else { return }
        let viewModel = kycData.nameRule
        let viewController = StoryboardScene.EKYCZoloz.mayaNameRulesBottomSheetViewControllert.instantiate()
        viewController.routeActionDelegate = self
        viewController.backActionDelegate = self
        viewController.bind(viewModel)
        router.present(viewController, animated: true, completion: nil)
    }

    func showSubmissionScreen() {
        guard let commonViewModel = commonViewModel, let viewModel = MayaZolozSubmissionViewModel(
            submissionInfo: MayaZolozSubmissionInfo(
                preSubmissionId: commonViewModel.preSubmissionId,
                documents: commonViewModel.documents,
                submissionData: commonViewModel.submissionData,
                captureGuide: MutableProperty(commonViewModel.selectedDocument.value?.captureGuide)
            ), kycSubmissionOnly: commonViewModel.hasSubmittedAdditionalDocs.value
        ) else { return }
        let viewController = MayaZolozSubmissionViewController()
        viewController.previousViewController = router.visibleViewController
        viewController.delegate = self
        viewController.routeActionDelegate = self
        viewController.backActionDelegate = self
        viewController.bind(viewModel)
        viewController.navigationBarTitleView = progressBarView
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func showSubmissionScreenBackIDFlow() {
        guard let commonViewModel = commonViewModel, let viewModel = MayaEKYCSubmissionViewModel(
            submissionInfo: MayaEKYCSubmissionInfo(
                preSubmissionId: commonViewModel.preSubmissionId,
                documents: commonViewModel.documents,
                submissionData: commonViewModel.submissionDataV2,
                captureGuide: MutableProperty(commonViewModel.selectedDocument.value?.captureGuide),
                primaryCaptureMethod: commonViewModel.primaryCaptureMethod,
                secondaryCaptureMethod: commonViewModel.secondaryCaptureMethod
            ), kycSubmissionOnly: commonViewModel.hasSubmittedAdditionalDocs.value
        ) else { return }
        let viewController = MayaZolozSubmissionViewController()
        viewController.previousViewController = router.visibleViewController
        viewController.delegate = self
        viewController.routeActionDelegate = self
        viewController.backActionDelegate = self
        viewController.bind(viewModel)
        viewController.navigationBarTitleView = progressBarView
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func showAdditionalDocumentsSubmissionScreen() {
        guard let commonViewModel = commonViewModel, let viewModel = MayaEKYCSubmissionBaseViewModel(
            submissionInfo: MayaEKYCSubmissionInfo(
                preSubmissionId: commonViewModel.preSubmissionId,
                documents: commonViewModel.documents,
                submissionData: commonViewModel.submissionDataV2,
                captureGuide: MutableProperty(commonViewModel.selectedDocument.value?.captureGuide),
                primaryCaptureMethod: commonViewModel.primaryCaptureMethod,
                secondaryCaptureMethod: commonViewModel.secondaryCaptureMethod
            )
        ) else { return }
        let viewController = MayaEKYCAdditionalDocumentsSubmissionViewController()
        viewController.previousViewController = router.visibleViewController
        viewController.delegate = self
        viewController.routeActionDelegate = self
        viewController.backActionDelegate = self
        viewController.bind(viewModel)
        viewController.navigationBarTitleView = progressBarView
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func showFATCAFlow() {
        guard let viewModel = commonViewModel,
              let regulatoryStatus = viewModel.regulatoryStatus.value,
              let regulatoryCMS = viewModel.regulatoryCMS.value else { return }
        let coordinator = MayaComplianceCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start(screen: .fatca, updateRequestedBy: Constants.Profile.UpdateRequestedBy.kyc, regulatoryStatus: regulatoryStatus, regulatoryCMS: regulatoryCMS)
    }

    func showDosriFlow() {
        guard let viewModel = commonViewModel,
              let regulatoryStatus = viewModel.regulatoryStatus.value,
              let regulatoryCMS = viewModel.regulatoryCMS.value else { return }
        let coordinator = MayaComplianceCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start(screen: .dosri, updateRequestedBy: Constants.Profile.UpdateRequestedBy.kyc, regulatoryStatus: regulatoryStatus, regulatoryCMS: regulatoryCMS)
    }

    func openDashboard() {
        guard let url = Constants.DeepLinkPath.dashboard.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
        router.dismissModule(animated: false, completion: nil)
    }

    func openAppStore() {
        let viewController = SKStoreProductViewController()
        viewController.loadProduct(withParameters: [SKStoreProductParameterITunesItemIdentifier: NSNumber(*********)])
        router.present(viewController, animated: true, completion: nil)
    }

    func showAccountDetails() {
        let viewController = StoryboardScene.EKYCZoloz.mayaZolozAccountUnderReviewViewController.instantiate()
        let viewModel = MayaZolozAccountReviewViewModel()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(viewModel)
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func showPrivacyStatementScreen() {
        guard let link = Constants.WebView.privacy.url else { return }
        showWebView(link: link)
    }

    func showOtherIDForReKYCScreen() {
        let viewController = StoryboardScene.MayaEKYC.mayaReKYCOtherIDViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func popToSelectIDScreen(completion: (() -> Void)? = nil) {
        guard let viewModel = commonViewModel else { return }
        if viewModel.isKYCTofuUXRedesignEnabled,
           let viewController = router.navigationController?.viewControllers.first(where: { $0 is MayaEKYCIDSelectViewController }) {
            viewModel.resetSecondaryIDValues()
            router.popToViewController(viewController, animated: false) {
                // Call the completion handler if provided
                completion?()
            }
        } else if viewModel.isKYCTofuUXRedesignEnabled {
            router.popToRootViewController(animated: false)
            showRedesignedSelectIDScreen()
            // Call the completion handler if provided
            completion?()
        } else if let viewController = router.navigationController?.viewControllers.first(where: { $0 is MayaZolozIdSubmitViewController }) {
            viewModel.resetSecondaryIDValues()
            router.popToViewController(viewController, animated: false) {
                // Call the completion handler if provided
                completion?()
            }
        } else {
            router.popToRootViewController(animated: false)
            showSelectIDScreen()
            // Call the completion handler if provided
            completion?()
        }
    }

    func popToChooseSecondaryIDScreen(completion: (() -> Void)? = nil) {
        if let viewController = router.navigationController?.viewControllers.first(where: { $0 is MayaEKYCSecondaryIDViewController }) {
            router.popToViewController(viewController, animated: false) {
                // Call the completion handler if provided
                completion?()
            }
        } else {
            router.popToRootViewController(animated: false)
            showSelectAnotherIDScreen()
            // Call the completion handler if provided
            completion?()
        }
    }

    func showSubmissionFlowWithFatcaChecking() {
        guard let viewModel = commonViewModel else { return }
        if viewModel.isFlaggedFatca.value {
            viewModel.callPatchFatca()
        } else if viewModel.isBackIDCaptureEnabled {
            showSubmissionScreenBackIDFlow()
        } else {
            showSubmissionScreen()
        }
    }
}

// MARK: MayaZolozAccountUnderReviewViewControllerDelegate Delegate Methods
extension EKYCZolozCoordinator: MayaZolozAccountUnderReviewViewControllerDelegate {
    func dismissModule() {
        delegate?.dismissCoordinator(self)
    }
}

// MARK: Maya Picker View Controller Delegate Methods
extension EKYCZolozCoordinator {
    func showUnderReviewScreen() {
        let viewController = StoryboardScene.EKYCZoloz.mayaZolozUnderReviewViewController.instantiate()
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(commonViewModel?.kycData.value?.submit)
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func showAdditionalDocumentsScreen() {
        guard let commonViewModel = commonViewModel else { return }
        let viewController = StoryboardScene.EKYCZoloz.mayaZolozAdditionalDocumentsViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(commonViewModel.documents, consentFormURL: commonViewModel.kycData.value?.additionalDocuments.consentURL)
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func confirmUseOf(image: UIImage, forAdditionalDocumentType additionalDocumentType: MayaZolozAdditionalDocumentType) {
        let viewController = StoryboardScene.EKYCZoloz.mayaZolozCaptureAdditionalDocumentConfirmationViewController.instantiate()
        viewController.routeActionDelegate = self
        viewController.backActionDelegate = self
        viewController.delegate = self
        viewController.bind(.init(image: image, additionalDocumentType: additionalDocumentType))
        router.push(viewController, animated: true, popCompletion: nil)
    }
}

// MARK: CaptureAdditionalDocumentViewControllerDelegate methods
extension EKYCZolozCoordinator: MayaZolozCaptureAdditionalDocumentViewControllerDelegate {
    func captured(additionalDocumentOfType additionalDocumentType: MayaZolozAdditionalDocumentType, image: UIImage) {
        confirmUseOf(image: image, forAdditionalDocumentType: additionalDocumentType)
    }

    func didCancelAdditionalDocumentCapture() {
        logEKYCBackEvent(source: AnalyticsConstants.ScreenName.captureDocsMinors, destination: AnalyticsConstants.ScreenName.docsMinor)
    }

    func didAppearAdditionalDocumentCapture(otherEvents: [AnalyticsAttributeKey: String]) {
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.additionalDocsCapture.appear(), source: AnalyticsConstants.ScreenName.docsMinor, otherEvents: otherEvents)
    }

    func didTapAdditionalDocumentCapture(action: String, otherEvents: [AnalyticsAttributeKey: String]) {
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.additionalDocsCapture.tapped(), destination: AnalyticsConstants.ScreenName.resultDocsMinors, action: action, otherEvents: otherEvents)
    }
}

// MARK: CaptureAdditionalDocumentConfirmationViewControllerDelegate methods
extension EKYCZolozCoordinator: MayaZolozCaptureAdditionalDocumentConfirmationViewControllerDelegate {
    func didConfirm(useOfDocumentImage image: UIImage, forAdditionalDocumentType documentType: MayaZolozAdditionalDocumentType) {
        var documents = commonViewModel?.documents.value ?? [:]
        documents[documentType] = image
        commonViewModel?.documents.swap(documents)
        let additionalDocumentsViewController = router.navigationController?.viewControllers.first {
            $0 is MayaZolozAdditionalDocumentsViewController
        }
        guard let viewController = additionalDocumentsViewController else { return }
        router.popToViewController(viewController)
    }

    func didAppearResult(otherEvents: [AnalyticsAttributeKey: String]) {
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.additionalDocsCaptureResults.appear(), source: AnalyticsConstants.ScreenName.captureDocsMinors, otherEvents: otherEvents)
    }

    func didTapButton(action: String, destination: String, otherEvents: [AnalyticsAttributeKey: String]) {
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.additionalDocsCaptureResults.tapped(), destination: destination, action: action, otherEvents: otherEvents)
    }
}

// MARK: AdditionalDocumentsViewControllerDelegate methods
extension EKYCZolozCoordinator: MayaZolozAdditionalDocumentsViewControllerDelegate {
    func takePhoto(forDocumentType documentType: MayaZolozAdditionalDocumentType) {
        let viewController = StoryboardScene.EKYCZoloz.mayaZolozCaptureAdditionalDocumentViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.documentType = documentType
        viewController.delegate = self
        router.push(viewController, animated: true, popCompletion: nil)
    }

    func allPhotosTaken() {
        showAllGoodScreen(source: AnalyticsConstants.ScreenName.docsMinor, minor: true, onConfirm: { [weak self] in
            guard let self = self,
                  let commonViewModel = self.commonViewModel
            else { return }

            let isFatcaRequired = commonViewModel.regulatoryStatus.value?.fatca.isRequired ?? false
            let isDosriRequired = commonViewModel.regulatoryStatus.value?.dosri.isRequired ?? false

            if (commonViewModel.isFATCAEnabled && isFatcaRequired && !commonViewModel.isFlaggedFatca.value) ||
               (commonViewModel.isDosriEnabled && isDosriRequired) {
                self.showAdditionalDocumentsSubmissionScreen()
            } else {
                showSubmissionFlowWithFatcaChecking()
            }
        })
    }

    func didCancelCapturingAdditionalDocuments() {
        commonViewModel?.documents.value = [:]
        logEKYCBackEvent(source: AnalyticsConstants.ScreenName.docsMinor, destination: AnalyticsConstants.ScreenName.personalInfo)
    }

    func didAppearAdditionalDocuments() {
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.additionalDocs.appear(), source: AnalyticsConstants.ScreenName.personalInfo, otherEvents: nil)
    }

    func didTapElement(action: String, destination: String) {
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.additionalDocs.tapped(), destination: destination, action: action)
    }
}

extension EKYCZolozCoordinator: MayaZolozSubmissionViewControllerDelegate {
    func submissionFinished() {
        showUnderReviewScreen()
    }

    func submissionFailed(withError error: PayMayaError, returnTo previousViewController: UIViewController?) {
        logEKYCErrorEvent(source: AnalyticsConstants.ScreenName.confirmSubmission, error: error)
        if let previousViewController = previousViewController {
            router.popToViewController(previousViewController)
        }

        if case .sessionTimeout = error.type {
            return
        } else if case .preSubmissionIdExpired = error.type {
            self.analyticsService.logMayaEvents(action: AnalyticsAction.invalid(Analytics.EKYCV6.preSubmissionId))
            let errorViewModel = ErrorAlertViewModel(title: CommonStrings.Maya.Error.Default.title)
            self.showMayaError(viewModel: errorViewModel, shouldExitKYC: true)
        } else if case .kycMultipleSubmissionError = error.type {
            self.analyticsService.logMayaEvents(action: AnalyticsAction.invalid(Analytics.EKYCV6.kycMultipleSubmission))
            let errorViewModel = ErrorAlertViewModel(title: CommonStrings.Maya.Error.Default.title)
            self.showMayaErrorMultipleSubmission(viewModel: errorViewModel)
        } else {
            if let errorViewModel = error.viewModel {
                showErrorAlert(errorViewModel, useMayaModal: true)
            } else {
                let errorViewModel = ErrorAlertViewModel(title: CommonStrings.Maya.Error.Default.title)
                showErrorAlert(errorViewModel, useMayaModal: true)
            }
        }
    }
}

extension EKYCZolozCoordinator: MayaEKYCAdditionalDocumentsSubmissionViewControllerDelegate {
    func submissionFinished(returnTo previousViewController: UIViewController?, additionalDocs: [PostSubmissionIdV2]?) {
        if let previousViewController = previousViewController {
            router.popToViewController(previousViewController)
        }
        guard let viewModel = commonViewModel else { return }
        viewModel.hasSubmittedAdditionalDocs.value = true

        if viewModel.isBackIDCaptureEnabled {
            viewModel.submissionDataV2.value?.additionalDocs = additionalDocs
        } else {
            if let additionalDocs = additionalDocs {
                for item in additionalDocs {
                    viewModel.submissionData.value?.additionalDocs.append(SubmissionAdditionalDoc(type: MayaZolozAdditionalDocumentType(rawValue: item.type)!, url: item.parts.first?.url ?? ""))
                }
            }
        }

        if viewModel.isFATCAEnabled,
           let isFatcaRequired = viewModel.regulatoryStatus.value?.fatca.isRequired,
           isFatcaRequired,
           !viewModel.isFlaggedFatca.value {
            self.showFATCAFlow()
        } else if viewModel.isDosriEnabled,
               let isDosriRequired = viewModel.regulatoryStatus.value?.dosri.isRequired,
               isDosriRequired {
            self.showDosriFlow()
        } else {
            if viewModel.isBackIDCaptureEnabled {
                self.showSubmissionScreenBackIDFlow()
            } else {
                self.showSubmissionScreen()
            }
        }
    }
}

// MARK: - MayaCameraPermissionCoordinatorDelegate Methods
extension EKYCZolozCoordinator: MayaCameraPermissionCoordinatorDelegate {
    func cameraPermissionDidAccept(_ coordinator: MayaCameraPermissionCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
        // MARK: Show SimpleCapture or New SimplifyCapture based on toggle shortenedFlowIdCaptureV3
        guard let commonViewModel = commonViewModel else { return }

        if commonViewModel.isShortenedFlowIdCaptureV3Enabled {
            // Move to New SimpleCapture V3 Screen
            showSimpleCaptureIDV3Screen()
        } else {
            showSimpleCaptureIDScreen()
        }
    }

    func cameraPermissionDidTapBack(_ coordinator: MayaCameraPermissionCoordinator, completion: (() -> Void)?) {
        router.dismissModule(animated: true, completion: { [weak self] in
            self?.removeChild(coordinator)
        })
    }
}

// MARK: - MayaEKYCSimpleCaptureIDViewControllerDelegate Methods
extension EKYCZolozCoordinator: MayaEKYCSimpleCaptureIDViewControllerDelegate {
    func didTapBack(_ viewController: MayaEKYCSimpleCaptureIDViewController, idPage: KYCData.CaptureGuide.ScanType) {
        guard let viewModel = commonViewModel else { return }

        if viewModel.secondaryIDFlow.value {
            switch idPage {
            case .back:
                showChangedYourMindScreenSecondaryIDFlow()
            default:
                logEKYCBackEvent(source: AnalyticsConstants.ScreenName.idCapture, destination: AnalyticsConstants.ScreenName.idTips)
                router.popModule(animated: true)
            }
        } else {
            switch idPage {
            case .back:
                showChangedYourMindScreen(source: AnalyticsConstants.ScreenName.idCapture)
            default:
                logEKYCBackEvent(source: AnalyticsConstants.ScreenName.idCapture, destination: AnalyticsConstants.ScreenName.idTips)
                router.popModule(animated: true)
            }
        }
    }

    func didCapture(imageData: Data, decision: Any, imageQDecisionString: String, idPage: KYCData.CaptureGuide.ScanType) {
        guard let selectedDocument = getSelectedDocument(),
              let presubmissionId = commonViewModel?.preSubmissionId.value,
              let secondaryIDFlow = commonViewModel?.secondaryIDFlow.value,
              let kycData = commonViewModel?.kycData.value
        else { return }

        if decision is KYCData.CaptureGuide.Decision || decision is ImageQualityModelDataHandler.ImageQualityLabels {
            let data = [
                .imageData: imageData,
                .selectedDocument: selectedDocument,
                .decision: decision,
                .preSubmissionId: presubmissionId,
                .idPage: idPage,
                .secondaryIDFlow: secondaryIDFlow,
                .idLevel: kycData.getIDLevel(selectedDocument.key),
                .imageQDecisionString: imageQDecisionString
            ] as [MayaEKYCSimpleCaptureIDReviewParameterKeys: Any]
            let viewModel = MayaEKYCSimpleCaptureIDReviewViewModel(data: data)
            let viewController = StoryboardScene.MayaEKYC.mayaEKYCSimpleCaptureIDReviewViewController.instantiate()
            viewController.routeActionDelegate = self
            viewController.delegate = self
            viewController.bind(viewModel)
            router.present(viewController, animated: true, completion: nil)
        }
    }
}

// MARK: - MayaEKYCSimpleCaptureIDV3ViewControllerDelegate Methods
extension EKYCZolozCoordinator: MayaEKYCSimpleCaptureIDV3ViewControllerDelegate {
    func didTapBack(_ viewController: MayaEKYCSimpleCaptureIDV3ViewController, idPage: KYCData.CaptureGuide.ScanType) {
        guard let viewModel = commonViewModel else { return }

        if viewModel.secondaryIDFlow.value {
            switch idPage {
            case .back:
                showChangedYourMindScreenSecondaryIDFlow()
            default:
                logEKYCBackEvent(source: AnalyticsConstants.ScreenName.idCapture, destination: AnalyticsConstants.ScreenName.idTips)
                router.popModule(animated: true)
            }
        } else {
            switch idPage {
            case .back:
                showChangedYourMindScreen(source: AnalyticsConstants.ScreenName.idCapture)
            default:
                logEKYCBackEvent(source: AnalyticsConstants.ScreenName.idCapture, destination: AnalyticsConstants.ScreenName.idTips)
                router.popModule(animated: true)
            }
        }
    }

    func didCapture(_ viewController: MayaEKYCSimpleCaptureIDV3ViewController, imageData: Data, decision: Any, imageQDecisionString: String, idPage: KYCData.CaptureGuide.ScanType, imageQAttributes: ImageQualityResultAttributes?, inference: Inference?) {
        guard let selectedDocument = getSelectedDocument(),
              let viewModel = commonViewModel,
              let presubmissionId = commonViewModel?.preSubmissionId.value,
              let secondaryIDFlow = commonViewModel?.secondaryIDFlow.value,
              let kycData = commonViewModel?.kycData.value
        else { return }

        if decision is KYCData.CaptureGuide.Decision || decision is ImageQualityModelDataHandler.ImageQualityLabels {
            if let decisionCast = decision as? KYCData.CaptureGuide.Decision,
               decisionCast == .passed,
               idPage == .front,
               let inference = inference {
                commonViewModel?.frontIDKey.value = inference.label

                // MARK: Change primary and secondary capture method based on DS model result
                if viewModel.isPhilsysFlowEnabled,
                   !viewModel.secondaryIDFlow.value,
                   let captureMethod = selectedDocument.captureGuide?.captureMethods.first?.method {
                    // MARK: Force Philsys Digital to PH National ID
                    if viewModel.isForcePHNationalIDEnabled,
                       inference.label == Constants.EKYC.Document.Philsys.digitalKey {
                        commonViewModel?.primaryCaptureMethod.value = Constants.EKYC.Document.Philsys.key + ";" + captureMethod
                    } else {
                        commonViewModel?.primaryCaptureMethod.value = inference.label + ";" + captureMethod
                    }
                } else if viewModel.isPhilsysFlowEnabled,
                          viewModel.secondaryIDFlow.value,
                          let captureMethodCount = selectedDocument.captureGuide?.captureMethods.count,
                          captureMethodCount >= 2,
                          let method = selectedDocument.captureGuide?.captureMethods[1].method {
                    commonViewModel?.secondaryCaptureMethod.value = inference.label + ";" + method
                }
            } else if let decisionCast = decision as? KYCData.CaptureGuide.Decision,
                      decisionCast == .passed,
                      idPage == .back,
                      let inference = inference {
                commonViewModel?.backIDKey.value = inference.label
            }

            let data = [
                .imageData: imageData,
                .selectedDocument: selectedDocument,
                .decision: decision,
                .preSubmissionId: presubmissionId,
                .idPage: idPage,
                .secondaryIDFlow: secondaryIDFlow,
                .idLevel: kycData.getIDLevel(selectedDocument.key),
                .imageQDecisionString: imageQDecisionString,
                .imageBlurResult: imageQAttributes?.imageBlurResult,
                .idTypingResult: imageQAttributes?.idTypingResult,
                .imageQScore: imageQAttributes?.imageQScore,
                .idTypingScore: imageQAttributes?.idTypingScore,
                .retryCount: commonViewModel?.getImageQualityRetryCount() ?? 0,
                .frontIDKey: commonViewModel?.frontIDKey.value,
                .backIDKey: commonViewModel?.backIDKey.value
                ] as [MayaEKYCSimpleCaptureIDReviewParameterKeys: Any]
            let viewModel = MayaEKYCSimpleCaptureIDReviewViewModel(data: data)
            let viewController = StoryboardScene.MayaEKYC.mayaEKYCSimpleCaptureIDReviewViewController.instantiate()
            viewController.routeActionDelegate = self
            viewController.delegate = self
            viewController.bind(viewModel)
            router.present(viewController, animated: true, completion: nil)
        }
    }

    func didAppearSimpleCaptureIDV3(source: String, events: [AnalyticsAttributeKey: String], side: KYCData.CaptureGuide.ScanType) {
        switch side {
        case .front:
            logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.simpleCaptureFront.appear(), source: source, otherEvents: events)
        case .back:
            logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.simpleCaptureBack.appear(), source: source, otherEvents: events)
        default: return
        }
    }

    func didTapAction(action: String, destination: String, side: KYCData.CaptureGuide.ScanType) {
        switch side {
        case .front:
            logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.simpleCaptureFront.tapped(), destination: destination, action: action)
        case .back:
            logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.simpleCaptureBack.tapped(), destination: destination, action: action)
        default: return
        }
    }
}

// MARK: - MayaEKYCSimpleCaptureIDReviewViewControllerDelegate Methods
extension EKYCZolozCoordinator: MayaEKYCSimpleCaptureIDReviewViewControllerDelegate {
    func didFailIDUpload(_ viewController: MayaEKYCSimpleCaptureIDReviewViewController, withError error: PayMayaError) {
        logEKYCErrorEvent(source: AnalyticsConstants.ScreenName.idCaptureResult, error: error)
        let errorViewModel = ErrorAlertViewModel(title: CommonStrings.Maya.Error.Default.title)

        if case .sessionTimeout = error.type {
            return
        } else if case .preSubmissionIdExpired = error.type {
            self.analyticsService.logMayaEvents(action: AnalyticsAction.invalid(Analytics.EKYCV6.preSubmissionId))
            self.showMayaError(viewModel: errorViewModel, shouldExitKYC: true, modalViewController: viewController)
        } else {
            // MARK: Show generic error
            self.showMayaError(viewModel: errorViewModel)
        }
    }

    func didFailImageQualityUpload(_ viewController: MayaEKYCSimpleCaptureIDReviewViewController, withError error: PayMayaError) {
        logEKYCErrorEvent(source: AnalyticsConstants.ScreenName.idCaptureResult, error: error)
        let errorViewModel = ErrorAlertViewModel(title: L10n.Maya.Ekyc.Capture.ImageQuality.Error.title,
                                                 message: L10n.Maya.Ekyc.Capture.ImageQuality.Error.description)

        if case .sessionTimeout = error.type {
            return
        } else if case .preSubmissionIdExpired = error.type {
            self.analyticsService.logMayaEvents(action: AnalyticsAction.invalid(Analytics.EKYCV6.preSubmissionId))
            self.showMayaError(viewModel: errorViewModel, shouldExitKYC: true, modalViewController: viewController)
        } else {
            // MARK: Show generic error
            self.showMayaError(viewModel: errorViewModel)
        }
    }

    func didSuccessfullyUpload(_ viewController: MayaEKYCSimpleCaptureIDReviewViewController, idPage: KYCData.CaptureGuide.ScanType) {
        guard let commonViewModel = commonViewModel,
              let requiresBackID = getSelectedDocument()?.requiresBackID
        else { return }

        let isBackIDCaptureEnabled = commonViewModel.isBackIDCaptureEnabled
        let action = L10n.Maya.Ekyc.IdReview.UseThis.title
        let name = AnalyticsConstants.Screen.Ekyc.idCaptureResult.tapped()

        viewController.dismiss(animated: false) { [weak self] in
            guard let self = self else { return }
            if idPage == .front && isBackIDCaptureEnabled && requiresBackID {
                if commonViewModel.secondaryIDFlow.value {
                    self.popToChooseSecondaryIDScreen()
                    self.logEKYCTapEvent(name: name, destination: AnalyticsConstants.ScreenName.selectSecondId, action: action)
                } else {
                    self.popToSelectIDScreen()
                    self.logEKYCTapEvent(name: name, destination: AnalyticsConstants.ScreenName.selectId, action: action)
                }
                if commonViewModel.isShortenedFlowIdCaptureV3Enabled {
                    self.showSimpleCaptureBackIDV3Screen()
                    self.logEKYCTapEvent(name: name, destination: AnalyticsConstants.ScreenName.idCapture, action: action)
                } else {
                    self.showSimpleCaptureBackIDScreen()
                    self.logEKYCTapEvent(name: name, destination: AnalyticsConstants.ScreenName.idCapture, action: action)
                }
            } else {
                if commonViewModel.secondaryIDFlow.value {
                    self.popToChooseSecondaryIDScreen { [weak self] in
                        self?.showCompletePersonalInformationScreen()
                    }
                    self.logEKYCTapEvent(name: name, destination: AnalyticsConstants.ScreenName.personalInfo, action: action)
                } else {
                    self.popToSelectIDScreen { [weak self] in
                        if commonViewModel.isKYCTofuUXRedesignEnabled {
                            self?.showSelfieTipsScreen()
                        } else {
                            self?.showLivenessTipsScreen()
                        }
                    }
                    self.logEKYCTapEvent(name: name, destination: AnalyticsConstants.ScreenName.livenessTips, action: action)
                }
            }
        }
    }

    func didTapChooseAnotherId(_ viewController: MayaEKYCSimpleCaptureIDReviewViewController, action: String) {
        guard let commonViewModel = commonViewModel else { return }
        viewController.dismiss(animated: true)
        if commonViewModel.secondaryIDFlow.value {
            popToChooseSecondaryIDScreen()
            logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.idCaptureResult.tapped(), destination: AnalyticsConstants.ScreenName.selectSecondId, action: action)
        } else {
            popToSelectIDScreen()
            logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.idCaptureResult.tapped(), destination: AnalyticsConstants.ScreenName.selectId, action: action)
        }
    }

    func didTapRetake(_ viewController: MayaEKYCSimpleCaptureIDReviewViewController, action: String, destination: String) {
        viewController.dismiss(animated: true)
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.idCaptureResult.tapped(), destination: destination, action: action)
    }

    func didAppearIDCaptureResult(events: [AnalyticsAttributeKey: String], isForUpload: Bool) {
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.idCaptureResult.appear(), source: isForUpload ? AnalyticsConstants.ScreenName.uploadID : AnalyticsConstants.ScreenName.idCapture, otherEvents: events)
    }

    func dismissIDReview(_ viewController: MayaEKYCSimpleCaptureIDReviewViewController, action: String) {
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.idCaptureResult.tapped(), destination: AnalyticsConstants.ScreenName.uploadID, action: action)
        viewController.dismiss(animated: true)
    }
}

// MARK: MayaEKYCFaceVerificationCoordinatorV2Delegate Methods
extension EKYCZolozCoordinator: MayaEKYCFaceVerificationCoordinatorV2Delegate {
    func didCaptureLiveness(_ coordinator: MayaEKYCFaceVerificationCoordinatorV2) {
        guard let viewModel = commonViewModel,
              let kycData = viewModel.kycData.value,
              let selectedDocument = viewModel.selectedDocument.value
        else { return }

        self.removeChild(coordinator)
        if kycData.isSecondaryID(selectedDocument.key) && configurationServices.secondaryIDFlowEnabled.value {
            showSelectAnotherIDScreen()
            return
        }

        showCompletePersonalInformationScreen()
    }

    func didReceiveAPIError(_ error: PayMayaError, coordinator: MayaEKYCFaceVerificationCoordinatorV2) {
        logEKYCErrorEvent(source: AnalyticsConstants.ScreenName.livenessCapture, error: error)
        self.removeChild(coordinator)
        guard case .sessionTimeout = error.type
        else {
            // MARK: we can add analytics logs here for Tencent/BE initialization/ednpoint errors (use error.viewModel)
            // Show generic error
            let errorViewModel = ErrorAlertViewModel(title: CommonStrings.Maya.Error.Default.title)
            if case .preSubmissionIdExpired = error.type {
                self.analyticsService.logMayaEvents(action: AnalyticsAction.invalid(Analytics.EKYCV6.preSubmissionId))
                self.showMayaError(viewModel: errorViewModel, shouldExitKYC: true)
            } else {
                self.showErrorAlert(errorViewModel, useMayaModal: true)
            }
            return
        }
    }

    func didReceiveTencentError(_ errorCode: TencentErrorCode, coordinator: MayaEKYCFaceVerificationCoordinatorV2) {
        self.removeChild(coordinator)

        switch errorCode {
        case .userCancel:
            return
        case .severalFaces:
            let errorViewModel = ErrorAlertViewModel(title: L10n.Maya.Ekyc.Tencent.Error.title, message: L10n.Maya.Ekyc.Tencent.Error.SeveralFaces.subtitle, image: CommonAsset.Images.Alert.imageCamera.image, closeButtonTitle: L10n.Maya.Ekyc.Tencent.Error.action)
            self.showErrorAlert(errorViewModel, useMayaModal: true)
            return
        case .incompleteFace:
            let errorViewModel = ErrorAlertViewModel(title: L10n.Maya.Ekyc.Tencent.Error.title, message: L10n.Maya.Ekyc.Tencent.Error.IncompleteFace.subtitle, image: CommonAsset.Images.Alert.imageCamera.image, closeButtonTitle: L10n.Maya.Ekyc.Tencent.Error.action)
            self.showErrorAlert(errorViewModel, useMayaModal: true)
            return
        default:
            let errorViewModel = ErrorAlertViewModel(title: L10n.Maya.Ekyc.Tencent.Error.title, message: L10n.Maya.Ekyc.Tencent.Error.subtitle, image: CommonAsset.Images.Alert.imageCamera.image, closeButtonTitle: L10n.Maya.Ekyc.Tencent.Error.action)
            self.showErrorAlert(errorViewModel, useMayaModal: true)
            return
        }
    }

    func didAppearTencent(events: [AnalyticsAttributeKey: String]) {
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.liveness.appear(), source: AnalyticsConstants.ScreenName.livenessTips, otherEvents: events)
    }

    func logLivenessResult(events: [AnalyticsAttributeKey: String]) {
        guard var eventAttributes = getCommonEventAttributes() else { return }
        eventAttributes.merge(events) { _, new in new }

        analyticsService.logMayaEvents(
            name: AnalyticsConstants.Screen.Ekyc.liveness.result(),
            keyAttributes: eventAttributes
        )
    }
}

// MARK: Private Tencent Releated Methods
private extension EKYCZolozCoordinator {
    func startTencentLivenessCaptureFlow() {
        guard let preSubmissionId = commonViewModel?.preSubmissionId.value else { return }
        let coordinator = MayaEKYCFaceVerificationCoordinatorV2(presenter: router, preSubmissionId: preSubmissionId)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func shouldProcessTencentError(_ errorCode: TencentErrorCode) -> Bool {
        return (errorCode == .userCancel || errorCode == .appSwitchRestriction || errorCode == .faceDetectionFailed)
    }
}

extension EKYCZolozCoordinator: MayaEKYCSecondaryIDViewControllerDelegate {
    func didTapContinue(_ viewController: MayaEKYCSecondaryIDViewController, selectedDocument: MayaEKYCSelectedDocument, action: String) {
        guard let viewModel = commonViewModel else { return }
        viewModel.secondaryIDFlow.value = true
        viewModel.selectedSecondaryDocument.value = selectedDocument
        viewModel.frontIDKey.value = nil
        viewModel.backIDKey.value = nil
        if let captureMethodCount = selectedDocument.captureGuide?.captureMethods.count,
           captureMethodCount >= 2,
           let method = selectedDocument.captureGuide?.captureMethods[1].method {
            viewModel.secondaryCaptureMethod.value = selectedDocument.key + ";" + method
        } else {
            viewModel.secondaryCaptureMethod.value = nil
        }

        if viewModel.isKYCTofuUXRedesignEnabled {
            showCameraPermisionScreenIfNeeded()
        } else {
            showPhotoTipsScreen()
            logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.selectIDSecondary.tapped(), destination: AnalyticsConstants.ScreenName.idTips, action: action)
        }
    }

    func didTapBack(_ viewController: MayaEKYCSecondaryIDViewController) {
        showChangedYourMindScreen(source: AnalyticsConstants.ScreenName.selectSecondId)
    }

    func didAppearSecondaryID() {
        guard let viewModel = commonViewModel else { return }
        if viewModel.isKYCTofuUXRedesignEnabled {
            logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.selectIDSecondary.appear(), source: AnalyticsConstants.ScreenName.livenessCapture, otherEvents: nil, isForSplitFlow: true)
        } else {
            logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.selectIDSecondary.appear(), source: AnalyticsConstants.ScreenName.livenessCapture, otherEvents: nil)
        }
    }

    func didSelectID(secondID: String) {
        guard let viewModel = commonViewModel else { return }
        if viewModel.isKYCTofuUXRedesignEnabled {
            logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.selectIDSecondary.tapped(), destination: AnalyticsConstants.ScreenName.captureSecondId, action: secondID, isForSplitFlow: true)
        } else {
            logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.selectIDSecondary.tapped(), destination: "", action: secondID)
        }
    }
}

extension EKYCZolozCoordinator: MayaEKYCPhilsysIDGuideViewControllerDelegate {
    func didTapTakePhoto() {
        guard let viewModel = commonViewModel else { return }
        if viewModel.isKYCTofuUXRedesignEnabled {
            showCameraPermisionScreenIfNeeded()
        } else {
            showPhotoTipsScreen()
        }
    }

    func didTapUploadID() {
        showPhilsysIDUploadScreen()
    }

    func didAppearIDGuide(events: [AnalyticsAttributeKey: String]) {
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.idTypeGuide.appear(), source: AnalyticsConstants.ScreenName.selectId, otherEvents: events)
    }

    func didTapIDGuideElement(action: String, destination: String?) {
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.idTypeGuide.tapped(), destination: destination ?? "", action: action)
    }
}

extension EKYCZolozCoordinator: MayaEKYCPhilsysIDUploadViewControllerDelegate {
    func didSuccessfullyUpload(_ viewController: MayaEKYCPhilsysIDUploadViewController, documentKey: String) {
        guard let viewModel = commonViewModel,
              let selectedDocument = getSelectedDocument(),
              let captureMethod = selectedDocument.captureGuide?.captureMethods.first?.method else { return }

        // MARK: Force Philsys Digital to PH National ID
        if viewModel.isForcePHNationalIDEnabled,
           documentKey == Constants.EKYC.Document.Philsys.digitalKey {
            commonViewModel?.primaryCaptureMethod.value = Constants.EKYC.Document.Philsys.key + ";" + captureMethod
        } else {
            commonViewModel?.primaryCaptureMethod.value = documentKey + ";" + captureMethod
        }

        if viewModel.isKYCTofuUXRedesignEnabled {
            showSelfieTipsScreen()
        } else {
            showLivenessTipsScreen()
        }
    }

    func didFailIDUpload(_ viewController: MayaEKYCPhilsysIDUploadViewController, withError error: PayMayaError) {
        logEKYCErrorEvent(source: AnalyticsConstants.ScreenName.uploadID, error: error)
        let errorViewModel = ErrorAlertViewModel(title: CommonStrings.Maya.Error.Default.title)

        if case .sessionTimeout = error.type {
            return
        } else if case .preSubmissionIdExpired = error.type {
            self.analyticsService.logMayaEvents(action: AnalyticsAction.invalid(Analytics.EKYCV6.preSubmissionId))
            self.showMayaError(viewModel: errorViewModel, shouldExitKYC: true, modalViewController: viewController)
        } else {
            // MARK: Show generic error
            self.showMayaError(viewModel: errorViewModel)
        }
    }

    func didUploadPhoto(_ viewController: MayaEKYCPhilsysIDUploadViewController, side: KYCData.CaptureGuide.ScanType, imageData: Data, decision: Any, imageQDecisionString: String, inference: Inference?, imageQAttributes: ImageQualityResultAttributes?, completion: @escaping (() -> Void)) {
        guard let selectedDocument = getSelectedDocument(),
              let presubmissionId = commonViewModel?.preSubmissionId.value,
              let secondaryIDFlow = commonViewModel?.secondaryIDFlow.value,
              let kycData = commonViewModel?.kycData.value
        else { return }

        if decision is KYCData.CaptureGuide.Decision || decision is ImageQualityModelDataHandler.ImageQualityLabels {
            if let decisionCast = decision as? KYCData.CaptureGuide.Decision,
               decisionCast == .passed,
               side == .front,
               let inference = inference {
                commonViewModel?.frontIDKey.value = inference.label
            } else if let decisionCast = decision as? KYCData.CaptureGuide.Decision,
                      decisionCast == .passed,
                      side == .back,
                      let inference = inference {
                commonViewModel?.backIDKey.value = inference.label
            }

            let data = [
                .imageData: imageData,
                .selectedDocument: selectedDocument,
                .decision: decision,
                .preSubmissionId: presubmissionId,
                .idPage: side,
                .secondaryIDFlow: secondaryIDFlow,
                .idLevel: kycData.getIDLevel(selectedDocument.key),
                .imageQDecisionString: imageQDecisionString,
                .retryCount: commonViewModel?.getImageQualityRetryCount() ?? 0,
                .isForIDUpload: true,
                .completion: completion,
                .frontIDKey: commonViewModel?.frontIDKey.value,
                .backIDKey: commonViewModel?.backIDKey.value,
                .idTypingResult: imageQAttributes?.idTypingResult,
                .imageQScore: imageQAttributes?.imageQScore,
                .idTypingScore: imageQAttributes?.idTypingScore
            ] as [MayaEKYCSimpleCaptureIDReviewParameterKeys: Any]
            let viewModel = MayaEKYCSimpleCaptureIDReviewViewModel(data: data, completion: completion)
            let viewController = StoryboardScene.MayaEKYC.mayaEKYCSimpleCaptureIDReviewViewController.instantiate()
            viewController.routeActionDelegate = self
            viewController.delegate = self
            viewController.bind(viewModel)
            router.present(viewController, animated: true, completion: nil)
        }
    }

    func didAppearIDUpload(events: [AnalyticsAttributeKey: String]) {
        logEKYCAppearEvent(name: AnalyticsConstants.Screen.Ekyc.uploadID.appear(), source: AnalyticsConstants.ScreenName.idGuide, otherEvents: events)
    }

    func didTapElement(action: String, destination: String?) {
        logEKYCTapEvent(name: AnalyticsConstants.Screen.Ekyc.uploadID.tapped(), destination: destination ?? "", action: action)
    }

    func didAppearErrorToast(reason: String) {
        logEKYCToastErrorEvent(source: AnalyticsConstants.ScreenName.uploadID, reason: reason)
    }
}

// MARK: MayaReferralInputCodeCoordinatorDelegate methods
extension EKYCZolozCoordinator: MayaReferralInputCodeCoordinatorDelegate {
    func didTapUpgradeAccount(_ coordinator: MayaReferralInputCodeCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
        }
    }

    func didTapReviewEDD(_ coordinator: MayaReferralInputCodeCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
        }
    }

    func didTapReviewApplication(_ coordinator: MayaReferralInputCodeCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.showAccountDetails()
        }
    }

    func didRequestRedirectToDashboard(_ coordinator: MayaReferralInputCodeCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            guard let self = self,
                let url = Constants.DeepLinkPath.dashboard.url else {
                return
            }
            self.removeChild(coordinator)
            self.routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
        }
    }
}
