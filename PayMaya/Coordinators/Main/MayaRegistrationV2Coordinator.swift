//
//  MayaRegistrationV2Coordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 5/8/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation

class MayaRegistrationV2Coordinator: MayaProgressBarCoordinator {
    private let viewModel = MayaRegistrationFormV2ViewModel()
    override var progressSegments: Int { 2 }

    weak var delegate: MayaRegistrationCoordinatorDelegate?

    override func start(completion: (() -> Void)? = nil) {
        let viewController = StoryboardScene.Registration.mayaRegistrationFormV2Controller.instantiate()
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.navigationBarTitleView = progressBarView
        viewController.bind(viewModel)
        setRootModule(viewController, animated: false)
        presenter?.present(router, animated: true, completion: completion)
    }

    private func startAutoProvisionFlow() {
        let viewController = StoryboardScene.Registration.mayaAutoProvisionV2ViewController.instantiate()
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.navigationBarTitleView = progressBarView
        viewController.bind(viewModel)
        router.push(viewController, animated: false, popCompletion: nil)
    }

    private func startVerificationFlow() {
        let coordinator = MayaOTPCoordinator(presenter: router, otpType: .registration)
        coordinator.authDelegate = delegate
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }
}

// MARK: - MayaRegistrationV2ViewControllerDelegate Methods
extension MayaRegistrationV2Coordinator: MayaRegistrationFormV2ControllerDelegate {
    func didTapPasswordTips(_ viewController: MayaRegistrationFormV2Controller) {
        let viewController = StoryboardScene.More.mayaChangePasswordTipsViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        router.present(viewController, animated: true, completion: nil)
    }

    func didCompletePasswordValidationAndPolicyConsent(_ viewController: MayaRegistrationFormV2Controller) {
        previousViewController = viewController
        startAutoProvisionFlow()
    }
}

// MARK: - MayaAutoProvisionV2ControllerDelegate Methods
extension MayaRegistrationV2Coordinator: MayaAutoProvisionV2ControllerDelegate {
    func didStartVerificationFlow(_ viewController: MayaAutoProvisionV2ViewController) {
        startVerificationFlow()
    }

    func didTapLink(_ viewController: MayaAutoProvisionV2ViewController, link: URL, title: String, analyticsName: String) {
        showWebView(link: link, title: title, showInBottomSheet: true)
    }
}

// MARK: - MayaOTPCoordinatorDelegate Methods
extension MayaRegistrationV2Coordinator: MayaOTPCoordinatorDelegate {
    func didReceiveOTPError(error: PayMayaError, _ coordinator: MayaOTPCoordinator) {
        router.dismissModule(animated: true) { [weak self, weak coordinator] in
            guard let self, let coordinator = coordinator else { return }
            removeChild(coordinator)
            popToRootViewController(animated: true)
            viewModel.resetConsents()
        }
    }
    func didReceivePostAuthenticationActionError(_ coordinator: MayaOTPCoordinator) {/* do nothing */}
    func didRequestDisplayMayaAlertViewModel(viewModel: MayaAlertViewModel, response: Codable?, coordinator: MayaOTPCoordinator) {/* do nothing */}
    func didRequestDisplayMayaReceiptViewModel(viewModel: MayaReceiptViewModelProtocol, response: Codable?, coordinator: MayaOTPCoordinator) {/* do nothing */}
}
