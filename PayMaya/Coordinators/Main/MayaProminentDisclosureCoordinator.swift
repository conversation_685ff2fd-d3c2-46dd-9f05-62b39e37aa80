//
//  MayaProminentDisclosureCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 2/28/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import ConfigurationService
import Foundation
import Injector

protocol MayaProminentDisclosureCoordinatorDelegate: AnyObject {
    func didAgreeAndContinue(_ coordinator: MayaProminentDisclosureCoordinator)
}

class MayaProminentDisclosureCoordinator: Coordinator {
    @Inject private var configurationServiceV2: ConfigurationServiceV2

    weak var delegate: MayaProminentDisclosureCoordinatorDelegate?

    override func start(completion: (() -> Void)? = nil) {
        let viewModel = MayaProminentDisclosureViewModel()
        if configurationServiceV2.shortenedRegistrationEnabled.value {
            let viewController = StoryboardScene.Account.mayaProminentDisclosureV2ViewController.instantiate()
            viewController.delegate = self
            viewController.routeActionDelegate = self
            viewController.bind(viewModel)
            presenter?.present(viewController, animated: true, completion: completion)
        } else {
            let viewController = StoryboardScene.Account.mayaProminentDisclosureViewController.instantiate()
            viewController.delegate = self
            viewController.routeActionDelegate = self
            viewController.bind(viewModel)
            presenter?.present(viewController, animated: true, completion: completion)
        }
    }
}

// MARK: MayaProminentDisclosureViewControllerDelegate
extension MayaProminentDisclosureCoordinator: MayaProminentDisclosureViewControllerDelegate {
    func didAgreeAndContinue(_ viewController: MayaProminentDisclosureViewController) {
        delegate?.didAgreeAndContinue(self)
    }
}

// MARK: MayaProminentDisclosureViewV2ControllerDelegate
extension MayaProminentDisclosureCoordinator: MayaProminentDisclosureV2ViewControllerDelegate {
    func didAgreeAndContinue(_ viewController: MayaProminentDisclosureV2ViewController) {
        delegate?.didAgreeAndContinue(self)
    }
}
