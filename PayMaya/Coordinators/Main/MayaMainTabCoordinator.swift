//
//  MayaMainTabCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 2/8/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
import AssetProvider
import ConfigurationService
import Injector
import MayaCoreData
import ReactiveSwift
import StoreProvider
import Swift<PERSON>
import UIKit

enum MayaTab: Int, CaseIterable {
    case dashboard = 0
    case scan
    case securityCenter
    case services

    var associatedCoordinator: String {
        switch self {
        case .dashboard: return String(describing: MayaDashboardCoordinator.self)
        case .scan: return String(describing: MayaQRScannerCoordinator.self)
        case .securityCenter: return String(describing: MayaSecurityCenterCoordinator.self)
        case .services: return String(describing: MayaServicesCoordinator.self)
        }
    }

    var analyticsValue: String {
        switch self {
        case .dashboard: return "wallet_tab"
        case .scan: return "scan_qr"
        case .securityCenter: return "security_center"
        case .services: return "more"
        }
    }

    var isActive: Bool {
        let configurationServiceV2 = ContainerWrapper.shared.resolve(ConfigurationServiceV2.self)
        switch self {
        case .dashboard, .scan: return true
        case .securityCenter: return configurationServiceV2.securityCenterEnabled.value
        case .services: return !configurationServiceV2.dashboardNavigationRefactorEnabled.value
        }
    }

    var index: Int {
        Self.allCases.filter { $0.isActive }.firstIndex(of: self) ?? 0
    }
}

protocol MayaMainTabCoordinatorDelegate: AnyObject {
    func didSuccessfullyAuthenticate(_ coordinator: MayaMainTabCoordinator)
    func didSwitchAccount(_ coordinator: MayaMainTabCoordinator)
}

class MayaMainTabCoordinator: MayaEKYCBaseCoordinator, DeepLinkHandler, AnalyticsServiceProtocol {
    @Inject var analyticsService: AnalyticsService
    @Inject private var storeProvider: StoreProvider
    @Inject private var configurationService: ConfigurationService
    @Inject var configurationServiceV2: ConfigurationServiceV2
    @Inject private var interstitialService: InterstitialServiceProtocol

    weak var delegate: MayaMainTabCoordinatorDelegate?

    let sessionTimeoutSignalProducer: SignalProducer<Void, Never>

    private class ScanViewController: ViewController, MayaTabBarChildViewControllerProtocol { }
    private let scanViewController = ScanViewController()

    private class SecurityCenterViewController: ViewController, MayaTabBarChildViewControllerProtocol { }
    private let securityCenterViewController = SecurityCenterViewController()

    private var ********************************: ********************************?

    private var isKYCCompleted: Bool?

    private var mayaReKYCCoordinator: MayaReKYCCoordinator?
    private var ekycBaseViewModel: MayaEKYCBaseViewModel?

    let mainTabBarController: MayaFloatingTabBarViewController = {
        let tabBarController = MayaFloatingTabBarViewController(nibName: nil, bundle: nil)
        let viewModel = MayaFloatingTabBarViewModel()
        tabBarController.bind(viewModel)
        return tabBarController
    }()

    override init(router: RouterProtocol = Router(), presenter: RouterProtocol? = nil) {
        let timeoutNotificationSignal = NotificationCenter.default.reactive.notifications(forName: NSNotification.Name(rawValue: sessionTimeoutNotification)).map { _ in }
        sessionTimeoutSignalProducer = SignalProducer<Void, Never> { observer, _ in
            timeoutNotificationSignal.observeValues {
                observer.send(value: ())
                observer.sendCompleted()
            }
        }
        super.init(router: router, presenter: presenter)
        observeSessionTimeout()

        if configurationService.mayaInboxFlutterEnabled.value {
            ******************************** = ********************************(presenter: router)
        }

        UIApplication.shared.registerForRemoteNotifications()
    }

    override func start(completion: (() -> Void)? = nil) {
        startSplitBlockingFlow(for: .user, completion: completion)
    }

    // MARK: DeepLinkHandler
    func open(deepLink: DeepLink) -> DeepLinkHandling {
        let deepLinkCompletion = { [weak self] in
            guard let self = self else { return }
            self.complete(deepLinkHandling: self.deepLinkHandling, error: nil)
        }

        switch deepLink.route {
        case .dashboard:
            return handleDashboardDeepLink(deepLink: deepLink)
        case .shop:
            let analyticsSourcePage: AnalyticsConstants.SourcePage = {
                if configurationService.ecommAppEventV2ShopEnabled.value {
                    return deepLink.analyticsSourcePage
                } else {
                    return .deepLink
                }
            }()

            return .normal(deepLink, identifier, String(describing: MayaShopCoordinator.self), { [weak self] in
                self?.startShopFlow(analyticsSourcePage: analyticsSourcePage, completion: deepLinkCompletion)
            }, false)
        case .inbox(.inboxRoute(let route)?, .inboxMessageTimestamp(let messageTimestamp)?, .inboxChannelUrl(let channelUrl)?):
            if configurationService.mayaInboxFlutterEnabled.value {
                guard let coordinator = ********************************
                else { return .rejected(deepLink, .notHandled, identifier) }

                if coordinator.isFullScreen {
                    return .passedThrough(deepLink, coordinator)
                }

                guard !resetFlow(requested: String(describing: ********************************.self), forceResetFlow: true) else {
                    return .normal(deepLink, identifier, String(describing: coordinator), { [weak self] in
                        self?.showStartInboxFlow(coordinator: coordinator, route: route, messageTimestamp: messageTimestamp, channelUrl: channelUrl)
                    }, true)
                }
                return .passedThrough(deepLink, coordinator)
            } else {
                return .normal(deepLink, identifier, String(describing: MayaInboxCoordinator.self), { [weak self] in
                    self?.startInboxFlow(completion: deepLinkCompletion)
                }, false)
            }
        case .merchant(.merchantPaymentId(let paymentId)?):
            if let decodedMerchant = decodeMerchant(with: paymentId) {
                return .normal(deepLink, identifier, String(describing: MayaP2MCoordinator.self), { [weak self] in
                    self?.startMerchantPaymentFlow(with: decodedMerchant, completion: deepLinkCompletion)
                }, false)
            } else {
                return .normal(deepLink, identifier, String(describing: MayaP2MCoordinator.self), { [weak self] in
                    self?.startPWPFlow(paymentId: paymentId, completion: deepLinkCompletion)
                }, false)
            }
        case .scanQr:
            let analyticsSourcePage: AnalyticsConstants.SourcePage = deepLink.fromServices ? .services : .dashboard

            return .normal(
                deepLink,
                identifier,
                String(describing: MayaQRScannerCoordinator.self),
                { [weak self] in
                    self?.startQRScannerFlow(
                        direction: .right,
                        analyticsSourcePage: analyticsSourcePage,
                        completion: deepLinkCompletion
                    )
                },
                false
            )
        case .services(let param):
            guard !resetFlow(requested: String(describing: MayaDashboardCoordinator.self), forceResetFlow: true) else {
                return .delayed(deepLink, identifier, nil)
            }

            showServicesFlow()

            if let handler = getChildCoordinator(identifier: MayaTab.services.associatedCoordinator) as? DeepLinkHandler {
                return .passedThrough(deepLink, handler)
            }
            return .opened(deepLink, nil)
        case .loansCreditCard where configurationService.loansCreditCardEnabled.value:
            return .normal(deepLink, identifier, String(describing: LoansCreditCardFullScreenCoordinator.self), { [weak self] in
                let route = deepLink.route.creditCardInitialRoute
                let params = deepLink.url.query
                self?.startCreditCardFlow(route: route, params: params, completion: deepLinkCompletion)
            }, false)
        default:
            return .rejected(deepLink, .notHandled, identifier)
        }
    }

    private func handleDashboardDeepLink(deepLink: DeepLink) -> DeepLinkHandling {
        let deepLinkCompletion = { [weak self] in
            guard let self = self else { return }
            self.complete(deepLinkHandling: self.deepLinkHandling, error: nil)
        }

        if isKYCCompleted == nil,
           let service = getService(from: deepLink),
           let isKYCRequired = service.isKYCRequired,
           isKYCRequired {
            return .delayed(deepLink, identifier, { [weak self] in
                guard let self = self else { return }

                let analyticsModule = self.getAnalyticsModule(from: service)
                self.showPreKYCFlow(analyticsModule: analyticsModule)
            })
        } else {
            isKYCCompleted = nil
        }

        if let service = getService(from: deepLink),
           let ageRestriction = service.ageRestriction,
           let databaseStore = storeProvider.target(DatabaseStore.self),
           let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User {
            if let birthDate = user.profile?.birthDate {
                if ageRestriction.isRestricted(birthDate: birthDate as Date) {
                    showAgeRestrictedFlow(ageRestriction, serviceID: service.id)
                    return .rejected(deepLink, .ageRequirementNotMet, identifier)
                }
            } else {
                showAgeRestrictedFlow(ageRestriction, serviceID: service.id)
                return .rejected(deepLink, .ageRequirementNotMet, identifier)
            }
        }

        switch deepLink.route {
        case .dashboard(.onboarding):
            if let action = ekycBaseViewModel?.getExpandedReKYCAction(),
               action != ReKYCData.ActionCode.noAction.rawValue {
                guard !resetFlow(requested: String(describing: MayaDashboardCoordinator.self), forceResetFlow: true) else {
                    return .delayed(deepLink, identifier, nil)
                }

                showDashboardMenu(.wallet)

                return .opened(deepLink, nil)
            }

            if configurationService.loansCreditCardEnabled.value {
                return .normal(deepLink, identifier, String(describing: LoansCreditCardFullScreenCoordinator.self), { [weak self] in
                    let route = Constants.Flutter.LoansCreditCard.cardOverviewScreen
                    let params = Constants.EKYC.CreditCardOverView.analytics
                    self?.startCreditCardFlow(route: route, params: params, completion: deepLinkCompletion)
                }, false)
            } else {
                return .opened(deepLink, nil)
            }
        case .dashboard(.pal):
            return .normal(deepLink, identifier, String(describing: MayaMMACoordinator.self), { [weak self] in
                self?.startMMAFlow(.philippineAirlines, completion: deepLinkCompletion)
            }, false)
        case .dashboard(.pbb):
            return .normal(deepLink, identifier, String(describing: MayaMMACoordinator.self), { [weak self] in
                self?.startMMAFlow(.pbb, completion: deepLinkCompletion)
            }, false)
        case .dashboard(.rafflePromo):
            return .normal(deepLink, identifier, String(describing: MayaMMACoordinator.self), { [weak self] in
                self?.startMMAFlow(.rafflePromo, completion: deepLinkCompletion)
            }, false)
        case .dashboard(.dashboard):
            if let action = ekycBaseViewModel?.getExpandedReKYCAction(),
               action != ReKYCData.ActionCode.noAction.rawValue {
                return .opened(deepLink, nil)
            }

            guard !resetFlow(requested: String(describing: MayaDashboardCoordinator.self), forceResetFlow: true) else {
                return .delayed(deepLink, identifier, nil)
            }

            showDashboardMenu(.wallet)

            return .opened(deepLink, nil)
        case .dashboard(.payBills):
            let analyticsSourcePage: AnalyticsConstants.SourcePage = {
                if configurationService.ecommAppEventV2BillsPayEnabled.value {
                    return deepLink.analyticsSourcePage
                } else {
                    return AnalyticsConstants.SourcePage.deepLink
                }
            }()

            return .normal(deepLink, identifier, String(describing: MayaPayBillsCoordinator.self), { [weak self] in
                self?.startPayBillsFlow(analyticsSourcePage: analyticsSourcePage, completion: deepLinkCompletion)
            }, false)
        case .dashboard(.sendMoney):
            return .normal(deepLink, identifier, String(describing: MayaSendMoneyCoordinator.self), { [weak self] in
                self?.startSendMoneyFlow(completion: deepLinkCompletion)
            }, false)
        case .dashboard(.requestMoney):
            return .normal(deepLink, identifier, String(describing: MayaRequestMoneyCoordinator.self), { [weak self] in
                self?.startProfileQRFlow(completion: deepLinkCompletion)
            }, false)
        case .dashboard(.bankTransfer):
            return .normal(deepLink, identifier, String(describing: MayaBankTransferCoordinator.self), { [weak self] in
                self?.startBankTransferFlow(analyticsSourcePage: .dashboard, completion: deepLinkCompletion)
            }, false)
        case .dashboard(.addMoney):
            let analyticsSourcePage: AnalyticsConstants.SourcePage = {
                if configurationService.appEventV2CashInEnabled.value {
                    return deepLink.analyticsSourcePage
                } else {
                    return AnalyticsConstants.SourcePage.deepLink
                }
            }()

            return .normal(deepLink, identifier, String(describing: MayaCashInCoordinator.self), { [weak self] in
                self?.startAddMoneyFlow(analyticsSourcePage: analyticsSourcePage, completion: deepLinkCompletion)
            }, false)
        case .dashboard(.upgrade):
            guard let databaseStore = storeProvider.target(DatabaseStore.self),
                  let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                  user.kycStatusValue == .none || user.kycStatusValue == .rejected
            else {
                return .rejected(deepLink, .notHandled, identifier)
            }

            return .normal(deepLink, identifier, String(describing: EKYCZolozCoordinator.self), { [weak self] in
                self?.startUpgradeFlow(completion: deepLinkCompletion)
            }, false)
        case .dashboard(.vouchers):
            return handleVouchersDeepLink(deepLink: deepLink, completion: deepLinkCompletion)
        case .dashboard(.missions):
            return .normal(deepLink, identifier, String(describing: MayaMissionsCoordinator.self), { [weak self] in
                self?.startMissionsFlow(completion: deepLinkCompletion)
            }, false)
        case .dashboard(.profile):
            return .normal(deepLink, identifier, String(describing: MayaProfileCoordinator.self), { [weak self] in
                self?.startProfileFlow(completion: deepLinkCompletion)
            }, false)
        case .dashboard(.transport):
            return .normal(deepLink, identifier, String(describing: MayaTravelCoordinator.self), { [weak self] in
                self?.startTravelFlow(completion: deepLinkCompletion)
            }, false)
        case .dashboard(.mayaMall):
            return .normal(deepLink, identifier, String(describing: WebViewCoordinator.self), { [weak self] in
                self?.startMayaMallFlow(completion: deepLinkCompletion)
            }, false)
        case .dashboard(.favorites(let param)):
            var type = ""
            if case .type(let favoriteType) = param {
                type = favoriteType
            }
            return .normal(deepLink, identifier, String(describing: MayaMyFavoritesCoordinator.self), { [weak self] in
                self?.startMyFavoritesFlow(with: FavoriteType(rawValue: type), completion: deepLinkCompletion)
            }, false)
        case .dashboard(.protect):
            if #available(iOS 14.3, *) {
                return .normal(deepLink, identifier, String(describing: WebViewCoordinator.self), { [weak self] in
                    self?.startProtectFlow(completion: deepLinkCompletion)
                }, false)
            } else {
                return .opened(deepLink) { [weak self] in
                    self?.startProtectFlow()
                }
            }
        case .dashboard(.moneycard):
            return .normal(deepLink, identifier, String(describing: MayaSendMoneyCardActivityCoordinator.self), { [weak self] in
                self?.startSendMoneyCardActivityFlow(completion: deepLinkCompletion)
            }, false)
        case .dashboard(.partnerMerchant):
            return .normal(deepLink, identifier, String(describing: WebViewCoordinator.self), { [weak self] in
                self?.showPartnerMerchantsScreen(completion: deepLinkCompletion)
            }, false)
        case .dashboard(.deals):
            return .normal(deepLink, identifier, String(describing: WebViewCoordinator.self), { [weak self] in
                self?.showDealsScreen(completion: deepLinkCompletion)
            }, false)
        case .dashboard(.invest):
            return .normal(deepLink, identifier, String(describing: InvestCoordinator.self), { [weak self] in
                self?.startInvestFlow(completion: deepLinkCompletion)
            }, false)
        case .dashboard(.cryptoV2):
            return .normal(deepLink, identifier, String(describing: CryptoCoordinator.self), { [weak self] in
                self?.startCryptoV2Flow(completion: deepLinkCompletion)
            }, false)
        case .dashboard(.mayaCredit):
            guard !resetFlow(requested: String(describing: MayaDashboardCoordinator.self), forceResetFlow: true) else {
                return .delayed(deepLink, identifier, nil)
            }

            if let coordinator = showDashboardMenu(.credit) {
                if let handler = coordinator.getChildCoordinator(identifier: String(describing: MayaCreditCoordinator.self)) as? DeepLinkHandler {
                    return .passedThrough(deepLink, handler)
                }
            }

            return .opened(deepLink, nil)
        case .dashboard(.loans):
            guard !resetFlow(requested: String(describing: MayaDashboardCoordinator.self), forceResetFlow: true) else {
                return .delayed(deepLink, identifier, nil)
            }

            if let coordinator = showDashboardMenu(.loans) {
                if let handler = coordinator.getChildCoordinator(identifier: String(describing: MayaLoansCoordinator.self)) as? DeepLinkHandler {
                    return .passedThrough(deepLink, handler)
                }
            }

            return .opened(deepLink, nil)
        case .dashboard(.myCards):
            guard !resetFlow(requested: String(describing: MayaDashboardCoordinator.self), forceResetFlow: true) else {
                return .delayed(deepLink, identifier, nil)
            }

            if let coordinator = showDashboardMenu(.cards) {
                if let handler = coordinator.getChildCoordinator(identifier: String(describing: MayaCardsV2Coordinator.self)) as? DeepLinkHandler {
                    return .passedThrough(deepLink, handler)
                }
            }

            return .opened(deepLink, nil)
        case .dashboard(.savings):
            guard !resetFlow(requested: String(describing: MayaDashboardCoordinator.self), forceResetFlow: true) else {
                return .delayed(deepLink, identifier, nil)
            }

            showDashboardMenu(.savings)

            return .opened(deepLink, nil)
        case .dashboard(.mayaSavingsToWallet):
            guard !resetFlow(requested: String(describing: MayaDashboardCoordinator.self), forceResetFlow: true) else {
                return .delayed(deepLink, identifier, nil)
            }

            if let coordinator = showDashboardMenu(.savings) {
                showSavingsToWalletFlow(parentCoordinator: coordinator)
            }

            return .opened(deepLink, nil)
        case .dashboard(.mayaAutoCashIn):
            return .normal(deepLink, identifier, String(describing: MayaAutoCashinFullscreenCoordinator.self), { [weak self] in
                self?.startMayaWalletSettingsAutoCashInFlow()
            }, false)
        case .dashboard(.mayaStartSavings):
            guard !resetFlow(requested: String(describing: MayaDashboardCoordinator.self), forceResetFlow: true) else {
                return .delayed(deepLink, identifier, nil)
            }

            if let coordinator = showDashboardMenu(.savings) {
                showStartSavingsFlow(parentCoordinator: coordinator)
            }

            return .opened(deepLink, nil)
        case .dashboard(.donate):
            return .normal(deepLink, identifier, String(describing: WebViewCoordinator.self), { [weak self] in
                self?.showDonateScreen(completion: deepLinkCompletion)
            }, false)
        case .dashboard(.submitInviteCode):
            return .normal(deepLink, identifier, String(describing: MayaReferralInputCodeCoordinator.self), { [weak self] in
                self?.startReferralInputCodeFlow(completion: deepLinkCompletion)
            }, false)
        case .dashboard(.funds):
            if configurationService.fundsCreatorStoreEnabled.value {
                return .normal(deepLink, identifier, String(describing: MayaCreatorStoreCoordinator.self), { [weak self] in
                    self?.startCreatorStoreFlow(.funds, completion: deepLinkCompletion)
                }, false)
            } else {
                return .normal(deepLink, identifier, String(describing: MayaFundsCoordinator.self), { [weak self] in
                    self?.startFundsFlow(completion: deepLinkCompletion)
                }, false)
            }
        case .dashboard(let dashboardRoute) where dashboardRoute.creatorStore != nil:
            guard let creatorStore = dashboardRoute.creatorStore else {
                return .rejected(deepLink, .notHandled, identifier)
            }
            return .normal(deepLink, identifier, String(describing: MayaCreatorStoreCoordinator.self), { [weak self] in
                self?.startCreatorStoreFlow(creatorStore, completion: deepLinkCompletion)
            }, false)
        case .dashboard(.addlPersonalDetails(let param)) where param != nil:
            if configurationService.updateProfileDeeplinkEnabled.value {
                var payload: [String: String]?
                if case .profilePayload(let inPayload) = param {
                    payload = inPayload
                }
                guard payload != nil  else {
                    return .rejected(deepLink, .notHandled, identifier)
                }
                return .normal(deepLink, identifier, String(describing: MayaUpdateProfileCoordinator.self), { [weak self] in
                    self?.startUpdateProfileFlow(payload, completion: deepLinkCompletion)
                }, false)
            } else {
                return .rejected(deepLink, .notHandled, identifier)
            }
        case .dashboard(.workDetails(let param)) where param != nil:
            if configurationService.updateProfileDeeplinkEnabled.value {
                var payload: [String: String]?
                if case .profilePayload(let inPayload) = param {
                    payload = inPayload
                }
                guard payload != nil else {
                    return .rejected(deepLink, .notHandled, identifier)
                }
                return .normal(deepLink, identifier, String(describing: MayaUpdateProfileCoordinator.self), { [weak self] in
                    self?.startProfileWorkDetailsFlow(payload, completion: deepLinkCompletion)
                }, false)
            } else {
                return .rejected(deepLink, .notHandled, identifier)
            }
        case .dashboard(.contactReference(let param)) where param != nil:
            if configurationService.updateProfileDeeplinkEnabled.value {
                var payload: [String: String]?
                if case .profilePayload(let inPayload) = param {
                    payload = inPayload
                }
                guard payload != nil else {
                    return .rejected(deepLink, .notHandled, identifier)
                }
                return .normal(deepLink, identifier, String(describing: MayaUpdateProfileCoordinator.self), { [weak self] in
                    self?.startProfileContactReferenceFlow(payload, completion: deepLinkCompletion)
                }, false)
            } else {
                return .rejected(deepLink, .notHandled, identifier)
            }
        case .dashboard(.load):
            return .normal(deepLink, identifier, String(describing: MayaShopLoadCoordinator.self), { [weak self] in
                let params = deepLink.url.query
                self?.startLoadFlow(params: params, completion: deepLinkCompletion)
            }, false)
        case .dashboard(.securityCenter):
            if ContainerWrapper.shared.resolve(ConfigurationServiceV2.self).securityCenterEnabled.value {
                return .normal(deepLink, identifier, String(describing: MayaSecurityCenterCoordinator.self), { [weak self] in
                    let params = deepLink.url.query
                    self?.startSecurityCenterFlow(completion: deepLinkCompletion)
                }, false)
            } else {
                return .rejected(deepLink, .notHandled, identifier)
            }
        case .dashboard(.purchaseFinancing):
            return .normal(deepLink, identifier, String(describing: MayaPurchaseFinancingCoordinator.self), { [weak self] in
                self?.startPurchaseFinancing(completion: deepLinkCompletion)
            }, false)
        case .dashboard(.scheduler):
            return .normal(deepLink, identifier, String(describing: MayaAutoDebitArrangementCoordinator.self), { [weak self] in
                let params = deepLink.url.query
                self?.startAutoDebitArrangementFlow(
                    params: params,
                    animated: true,
                    completion: deepLinkCompletion
                )
            }, false)
        case .dashboard(.blackpinkGiveaway):
            return handleBlackPinkGiveawayDeepLink(deepLink, completion: deepLinkCompletion)
        default:
            return .rejected(deepLink, .notHandled, identifier)
        }
    }

    private func getService(from deepLink: DeepLink) -> Service? {
        guard var deepLinkURLComponents = URLComponents(url: deepLink.url, resolvingAgainstBaseURL: false),
            let services = ServiceUtility.filteredServices else {
            return nil
        }

        if let queryItem = deepLinkURLComponents.queryItems?.first(where: { $0.name == ServiceTypeConstants.serviceType }),
           let queryItemValue = queryItem.value,
           let serviceType = ServiceType(rawValue: queryItemValue) {
            return services.first { serviceType == $0.serviceType }
        } else {
            deepLinkURLComponents.queryItems = nil
            return services.first { $0.url == deepLinkURLComponents.url }
        }
    }

    private func getAnalyticsModule(from service: Service) -> AnalyticsModule {
        let label = service.label.uppercased().replacingOccurrences(of: " ", with: "_")

        return Analytics.DynamicServices.module(service.eventName?.uppercased() ?? label)
    }

    private func observeSessionTimeout() {
        sessionTimeoutSignalProducer
            .observe(on: UIScheduler())
            .startWithCompleted { [weak self] in
                guard let self else { return }
                self.applicationDidSessionTimeout()
            }
            .addToDisposeBag(disposeBag)
    }

    func showSavingsToWalletFlow(parentCoordinator: MayaDashboardCoordinator) {
        parentCoordinator.childCoordinators.forEach {
            if let coordinator = $0 as? MayaSavingsCoordinator {
                coordinator.redirectToFullScreenFlow(flow: .savingsToWallet)
            }
        }
    }

    func showStartSavingsFlow(parentCoordinator: MayaDashboardCoordinator) {
        parentCoordinator.childCoordinators.forEach {
            if let coordinator = $0 as? MayaSavingsCoordinator {
                coordinator.redirectToFullScreenFlow(flow: .startSavings)
            }
        }
    }

    func showStartInboxFlow(coordinator: ********************************, completion: (() -> Void)? = nil, route: String?, messageTimestamp: String?, channelUrl: String?) {
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.presentFullScreen(completion: completion, arguments: ["route": route, "messageTimestamp": messageTimestamp, "channelUrl": channelUrl])
    }

    func resetFlow(requested: String, forceResetFlow: Bool = false) -> Bool {
        let willReset = forceResetFlow || getRecursiveChildCoordinator(identifier: requested) == nil
        if willReset,
           let navigationController = router.toPresentable().presentedViewController as? UINavigationController,
           let firstController = navigationController.viewControllers[safeIndex: 0] as? ViewController,
           let presentedCoordinator = firstController.routeActionDelegate as? Coordinator {
            router.dismissModule(animated: true) {
                self.removeChild(presentedCoordinator)
                self.complete(deepLinkHandling: self.deepLinkHandling, error: nil)
            }
            return true
        } else if willReset,
            let viewController = router.toPresentable().presentedViewController {
            viewController.dismiss(animated: true) { [weak self] in
                guard let self = self else { return }
                if let coordinator = self.childCoordinators[safeIndex: self.childCoordinators.count - 1],
                   !(coordinator is MayaDashboardCoordinator) {
                    self.removeChild(coordinator)
                }
                self.complete(deepLinkHandling: self.deepLinkHandling, error: nil)
            }
            return true
        }
        return false
    }

    private func decodeMerchant(with paymentId: String) -> MerchantScanQRProtocol? {
        let decoder = JSONDecoder()
        var merchant: MerchantScanQRProtocol?
        guard let decodedData = Data(base64Encoded: paymentId) else { return nil }
        if let dynamicMerchantQR = try? decoder.decode(DynamicMerchantQR.self, from: decodedData) {
            merchant = dynamicMerchantQR
        } else if let merchantQR = try? decoder.decode(MerchantQR.self, from: decodedData) {
            merchant = merchantQR
        }
        return merchant
    }

    override func getTopViewController() -> UIViewController? {
        if let presentedFromTabController = mainTabBarController.presentedViewController {
            var topViewControllerFromTab = presentedFromTabController
            while let presentedViewController = topViewControllerFromTab.presentedViewController {
                topViewControllerFromTab = presentedViewController
            }
            return topViewControllerFromTab
        }

        guard let selectedViewController = mainTabBarController.selectedViewController else { return mainTabBarController }
        var topViewController = selectedViewController
        while let presentedViewController = topViewController.presentedViewController {
            topViewController = presentedViewController
        }
        return topViewController
    }

    override func didRequestRedirectToDashboardThenStartDeepLink(_ coordinator: Coordinator, with url: URL) {
        router.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }

            self.removeChild(coordinator)

            self.didRequestStartDeepLink(self, with: url) { canOpen in
                if !canOpen {
                    self.showWebView(link: url, willPresent: true)
                }
            }
        }
    }

    func handleBlackPinkGiveawayDeepLink(_ deepLink: DeepLink, completion: (() -> Void)? = nil) -> DeepLinkHandling {
        guard configurationServiceV2.blackpinkGiveawayDeeplinkEnabled.value else {
            return .rejected(deepLink, .notHandled, identifier)
        }

        return .normal(deepLink, identifier, String(describing: MayaMMACoordinator.self), { [weak self] in
            self?.startMMAFlow(.blackpinkGiveaway, completion: completion)
        }, false)
    }

    // MARK: - BackActionDelegate
    override func didTapBack(_ coordinator: Coordinator, completion: (() -> Void)?) {
        super.didTapBack(coordinator, completion: completion)
        switch coordinator {
        case is MayaBankTransferCoordinator:
            interstitialService.resume(for: .bankTransfer)

        case is MayaP2MCoordinator:
            interstitialService.resume(for: .merchantPayment)

        case is MayaSendMoneyCoordinator:
            interstitialService.resume(for: .sendMoney)

        case is MayaQRScannerCoordinator:
            didDismissQRScanner()

        default:
            break
        }
    }
}

// MARK: - SplitBlocking Methods
extension MayaMainTabCoordinator: SplitBlocking {
    func didFinishBlocking(completion: (() -> Void)?) {
        let dashboardCoordinator = MayaDashboardCoordinator()
        dashboardCoordinator.delegate = self
        dashboardCoordinator.routeActionDelegate = self
        addChild(dashboardCoordinator)
        dashboardCoordinator.start()

        scanViewController.view.backgroundColor = .clear
        scanViewController.navigationBarHidden = true
        scanViewController.floatingTabBarItem = MayaFloatingTabBarItem(model: .init(mayaTab: .scan))
        scanViewController.floatingTabBarItem?.accessibilityIdentifier = "pmios_tab_scan"

        var viewControllers = [dashboardCoordinator.router.toPresentable(), scanViewController]

        if configurationServiceV2.securityCenterEnabled.value {
            // TODO use maya tab with security center
            securityCenterViewController.view.backgroundColor = .clear
            securityCenterViewController.navigationBarHidden = true
            securityCenterViewController.floatingTabBarItem = MayaFloatingFreezeTabBarItem(model: .init(mayaTab: .services))

            viewControllers.append(securityCenterViewController)
            mainTabBarController.dashboardMenuPagerViewController = dashboardCoordinator.rootViewController as? MayaDashboardMenuPagerViewController
        }

        if !configurationServiceV2.dashboardNavigationRefactorEnabled.value {
            let servicesCoordinator = MayaServicesCoordinator()
            servicesCoordinator.delegate = self
            servicesCoordinator.routeActionDelegate = self
            addChild(servicesCoordinator)
            servicesCoordinator.start()

            viewControllers.append(servicesCoordinator.router.toPresentable())
        }

        mainTabBarController.viewControllers = viewControllers
        mainTabBarController.selectedViewController = dashboardCoordinator.router.toPresentable()

        setRootModule(mainTabBarController, animated: true)

        if let navigationController = router.toPresentable() as? UINavigationController {
            navigationController.isNavigationBarHidden = true
            navigationController.modalTransitionStyle = .crossDissolve
        }
        presenter?.present(router.toPresentable(), animated: true, completion: nil)
        mainTabBarController.delegate = self

        ekycBaseViewModel = MayaEKYCBaseViewModel()
        ekycBaseViewModel?.setEntryPointReferral()
        ekycBaseViewModel?.getReKYCDataAction.apply().start()
    }
}

// MARK: - MayaFloatingTabBarViewControllerDelegate Methods
extension MayaMainTabCoordinator: MayaFloatingTabBarViewControllerDelegate {
    func floatingTabBarController(_ tabBarController: MayaFloatingTabBarViewController, didTap viewController: UIViewController) {
        let selectedTab = MayaTab(rawValue: tabBarController.viewControllers.firstIndex(of: viewController) ?? 0) ?? .dashboard
        let attributes = [AnalyticsAttributeKey.button.rawValue: selectedTab.analyticsValue]

        analyticsService.logMayaEvents(action: .tap(Analytics.FloatingNavigation.item), attributes: attributes)
    }

    func floatingTabBarController(_ tabBarController: MayaFloatingTabBarViewController, shouldSelect viewController: UIViewController) -> Bool {
        switch viewController {
        case is ScanViewController:
            startQRScannerFlow(animated: false, analyticsSourcePage: .dashboard)
            return false
        case is SecurityCenterViewController:
            startSecurityCenterFlow(animated: false)
            return false
        default: return true
        }
    }

    func floatingTabBarController(_ tabBarController: MayaFloatingTabBarViewController, didSelect viewController: UIViewController) {
        if viewController == mainTabBarController.viewControllers[safeIndex: MayaTab.services.index] {
            NotificationCenter.default.post(name: Notification.Name.willOpenMoreServices, object: nil, userInfo: [.source: MayaServicesSourceScreen.floatingTab])
        }
    }

    func floatingTabBarControllerDidStartFlutterInterstitial(_ tabBarController: MayaFloatingTabBarViewController) {
        startFlutterInterstitial()
    }
}

// MARK: - MayaDashboardCoordinatorDelegate Methods
extension MayaMainTabCoordinator: MayaDashboardCoordinatorDelegate {
    func willShowCreditTransferFundsFlow(_ coordinator: MayaDashboardCoordinator, account: MayaCreditAccount, feeRate: Float?) {
        startCreditTransferFundsFlow(creditAccount: account, feeRate: feeRate)
    }

    func willShowReKYCFlow(_ coordinator: MayaDashboardCoordinator) {
        guard let visibleViewController = router.visibleViewController else {
            // expect non nil only
            return
        }
        guard !visibleViewController.isKind(of: MayaProfileViewController.self) else {
            // only show the rekyc flow from dashboard tab features (wallet/savings/etc) not from profile
            return
        }
        guard !visibleViewController.isKind(of: MayaReKYCAlertBottomSheetViewController.self) else {
            // dont show again bottomsheet if it is currently displaying
            return
        }
        guard mayaReKYCCoordinator == nil else { return }

        showReKYCFlow(analyticsModule: Analytics.Dashboard())
    }

    func willShowWalletSettingsService(_ coordinator: MayaDashboardCoordinator) {
        startMayaWalletSettingsFlow()
    }

    func willShowProfileFlow(_ coordinator: MayaDashboardCoordinator) {
        startProfileFlow()
    }

    func willShowInboxFlow(_ coordinator: MayaDashboardCoordinator) {
        startInboxFlow()
    }

    func willShowInboxIcon(_ coordinator: MayaDashboardCoordinator, mayaDashboardMenuPagerViewController: MayaDashboardMenuPagerViewController) {
        setupInboxIcon(mayaDashboardMenuPagerViewController: mayaDashboardMenuPagerViewController)
    }

    func willShowAddMoneyFlow(_ coordinator: MayaDashboardCoordinator) {
        startAddMoneyFlow(analyticsSourcePage: AnalyticsConstants.SourcePage.dashboard)
    }

    func willShowMissionsFlow(_ coordinator: MayaDashboardCoordinator) {
        startMissionsFlow()
    }

    func willShowSendMoneyFlow(_ coordinator: MayaDashboardCoordinator) {
        startSendMoneyFlow()
    }

    func willShowVouchersFlow(_ coordinator: MayaDashboardCoordinator) {
        startVouchersFlow()
    }

    func willShowUpgradeFlow(_ coordinator: MayaDashboardCoordinator) {
        startUpgradeFlow(completion: nil, isFromReg: true)
    }

    func willShowReviewApplicationFlow(_ coordinator: MayaDashboardCoordinator) {
        startReviewApplicationFlow()
    }

    func willShowReviewEDDFlow(_ coordinator: MayaDashboardCoordinator) {
        startReviewEDDFlow()
    }

    func willShowWalletService(_ coordinator: MayaDashboardCoordinator, service: Service) {
        startServiceFlow(service: service)
    }

    func willShowMoreServices(_ coordinator: MayaDashboardCoordinator) {
        showServicesFlow()
        NotificationCenter.default.post(name: Notification.Name.willOpenMoreServices, object: nil, userInfo: [.source: MayaServicesSourceScreen.dashboard])
    }
}

// MARK: - MayaServicesCoordinatorDelegate Methods
extension MayaMainTabCoordinator: MayaServicesCoordinatorDelegate {
    func didTapService(_ coordinator: MayaServicesCoordinator, service: Service) {
        startServiceFlow(service: service)
    }

    func didTapServicesAction(_ coordinator: MayaServicesCoordinator, url: URL?, title: String?) {
        guard let link = url else { return }
        showWebView(link: link, title: title, willPresent: true)
    }
}

// MARK: - MayaQRScannerCoordinatorDelegate Methods
extension MayaMainTabCoordinator: MayaQRScannerCoordinatorDelegate {
    func didScanMerchantQr(_ coordinator: Coordinator, merchant: MerchantQR) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.startMerchantPaymentFlow(with: merchant)
            self.didDismissQRScanner()
        })
    }

    func didScanSendMoneyQr(_ coordinator: Coordinator, transfer: SendMoneyQR) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.startSendMoneyFlow(transfer)
            self.didDismissQRScanner()
        })
    }

    func didScanDynamicMerchantQrOldFlow(_ coordinator: Coordinator, dynamicMerchant: DynamicMerchantQR) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.startMerchantPaymentFlow(with: dynamicMerchant)
            self.didDismissQRScanner()
        })
    }

    func didScanSendMoneyToBankQr(_ coordinator: Coordinator, bankTransfer: BankTransferQRProtocol) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.startBankTransferFlow(bankTransfer, analyticsSourcePage: .qr)
            self.didDismissQRScanner()
        })
    }

    func didScanOffUsMerchantQrOldFlow(_ coordinator: Coordinator, offUsMerchant: OffUSQRPHMerchant) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.startMerchantPaymentFlow(with: offUsMerchant)
            self.didDismissQRScanner()
        })
    }

    func didScanQRPHMerchantQr(_ coordinator: Coordinator, qrphMerchant: QRPHMerchant) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.startMerchantPaymentFlow(with: qrphMerchant)
            self.didDismissQRScanner()
        })
    }

    func didRequestRedirectToDashboard(_ coordinator: MayaQRScannerCoordinator) {
        removeChild(coordinator)
        guard let url = Constants.DeepLinkPath.dashboard.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
        didDismissQRScanner()
    }

    func didDismissQRScanner() {
        interstitialService.resume(for: .qrScanner)
    }
}

// MARK: - MayaP2MCoordinatorDelegate Methods
extension MayaMainTabCoordinator: MayaP2MCoordinatorDelegate {
    func didFinishMerchantPayment(_ coordinator: MayaP2MCoordinator, redirectURL: URL?) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            removeChild(coordinator)
            showExternalURI(redirectURL)
            interstitialService.resume(for: .merchantPayment)
        })
    }

    func didRequestRedirectToDashboard(_ coordinator: MayaP2MCoordinator) {
        removeChild(coordinator)
        guard let url = Constants.DeepLinkPath.dashboard.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
        interstitialService.resume(for: .merchantPayment)
    }
}

// MARK: - WalletSettingsCoordinatorDelegate

extension MayaMainTabCoordinator: MayaWalletSettingsCoordinatorDelegate {
    func didShowStartSavings(_ coordinator: MayaWalletSettingsCoordinator) {
        guard let savingsDeepLink = Constants.DeepLinkPath.mayaStartSavings.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: savingsDeepLink, completion: nil)
    }

    func didDismissWalletSettings(_ coordinator: MayaWalletSettingsCoordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
        })
    }

    private func startMayaWalletSettingsFlow() {
        let coordinator = MayaWalletSettingsCoordinator(presenter: router)
        coordinator.presentationDirection = .right
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    private func startMayaWalletSettingsAutoCashInFlow() {
        let coordinator = MayaWalletSettingsCoordinator(presenter: router)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
        coordinator.showAutoCashinFullScreen()
    }
}

// MARK: - CommonEKYCCoordinatorDelegate Methods
extension MayaMainTabCoordinator: CommonEKYCCoordinatorDelegate {
    func didRequireKYCUpgrade(_ coordinator: Coordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.startUpgradeFlow()
        })
    }

    func didTapReviewApplication(_ coordinator: Coordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.startReviewApplicationFlow()
        })
    }
}

// MARK: - MayaSessionTimeoutCoordinatorDelegate Methods
extension MayaMainTabCoordinator: MayaSessionTimeoutCoordinatorDelegate {
    func didSwitchAccount(_ coordinator: MayaSessionTimeoutCoordinator) {
        dispatchToMain { [weak self] in
            guard let self = self else { return }
            coordinator.router.dismissRootModule(animated: true) {
                self.removeChild(coordinator)
                self.delegate?.didSwitchAccount(self)
            }
        }
    }

    func didSuccessfullyAuthenticate(_ coordinator: MayaSessionTimeoutCoordinator, completion: (() -> Void)?) {
        dispatchToMain { [weak self] in
            guard let self else { return }
            coordinator.router.dismissRootModule(animated: true, completion: {
                self.removeChild(coordinator)
                completion?()
                self.delegate?.didSuccessfullyAuthenticate(self)
            })

            self.observeSessionTimeout()
        }
    }
}

// MARK: - InvestCoordinatorDelegate Methods
extension MayaMainTabCoordinator: InvestCoordinatorDelegate {
    func didDismissInvest(coordinator: InvestCoordinator) {
        removeChild(coordinator)
    }

    func didReceiveError(error: PayMayaError, coordinator: InvestCoordinator) {
        if case .sessionTimeout = error.type {
            // Do nothing
        } else if let errorViewModel = error.viewModel {
            showErrorAlert(errorViewModel)
        }
        removeChild(coordinator)
    }
}

extension MayaMainTabCoordinator: CryptoCoordinatorDelegate {
    func didDismissInvest(coordinator: CryptoCoordinator) {
        removeChild(coordinator)
    }

    func didReceiveError(error: PayMayaError, coordinator: CryptoCoordinator) {
        if case .sessionTimeout = error.type {
            // Do nothing
        } else if let errorViewModel = error.viewModel {
            showErrorAlert(errorViewModel)
        }
        removeChild(coordinator)
    }
}

// MARK: - Private Methods
fileprivate extension MayaMainTabCoordinator {
    func applicationDidSessionTimeout(completion: (() -> Void)? = nil) {
        let router = MayaSessionTimeoutCoordinator.createOrderedWindowRouter()
        let sessionTimeoutCoordinator = MayaSessionTimeoutCoordinator(router: router)
        addChild(sessionTimeoutCoordinator)
        sessionTimeoutCoordinator.delegate = self
        sessionTimeoutCoordinator.backActionDelegate = self
        sessionTimeoutCoordinator.routeActionDelegate = self
        sessionTimeoutCoordinator.start(completion: completion)
    }

    func startQRScannerFlow(
        direction: PresentationDirection? = nil,
        animated: Bool = true,
        analyticsSourcePage: AnalyticsConstants.SourcePage,
        completion: (() -> Void)? = nil
    ) {
        let coordinator = MayaQRScannerCoordinator(
            presenter: router,
            isAnimated: animated,
            analyticsSourcePage: analyticsSourcePage
        )
        coordinator.presentationDirection = direction
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)

        interstitialService.suspend(for: .qrScanner)
        coordinator.start(completion: completion)
    }

    func startMMAFlow(_ mma: MMA, completion: (() -> Void)? = nil) {
        let coordinator = MayaMMACoordinator(mma: mma, presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startCreditCardFlow(route: String, params: String?, completion: (() -> Void)? = nil) {
        let coordinator = LoansCreditCardFullScreenCoordinator(route: route, params: params, presenter: router)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startPurchaseFinancing(completion: (() -> Void)? = nil) {
        guard configurationServiceV2.pfMvpEnabled.value else { return }

        let coordinator = MayaPurchaseFinancingCoordinator(presenter: router)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startMerchantPaymentFlow(with merchant: MerchantScanQRProtocol, completion: (() -> Void)? = nil) {
        let coordinator = MayaP2MCoordinator(presenter: router, merchant: merchant)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)

        interstitialService.suspend(for: .merchantPayment)
        coordinator.start(completion: completion)
    }

    func startSendMoneyFlow(_ transfer: SendMoneyQR? = nil, completion: (() -> Void)? = nil) {
        let coordinator = MayaSendMoneyCoordinator(presenter: router, analyticsSourcePage: transfer != nil ? .qr : .dashboard)
        coordinator.presentationDirection = .right
        coordinator.delegate = self
        coordinator.routeActionDelegate = self
        coordinator.backActionDelegate = self
        if let transferDetails = transfer {
            coordinator.transferDetails = transferDetails
        }
        addChild(coordinator)

        interstitialService.suspend(for: .sendMoney)
        coordinator.start(completion: completion)
    }

    func startBankTransferFlow(_ transferToBank: BankTransferQRProtocol? = nil, analyticsSourcePage: AnalyticsConstants.Screen.BankTransfer.Page, completion: (() -> Void)? = nil) {
        let coordinator = MayaBankTransferCoordinator(presenter: router, analyticsSourcePage: analyticsSourcePage)
        coordinator.presentationDirection = .right
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        if let transferToBank {
            coordinator.bankQRDetails = transferToBank
        }
        addChild(coordinator)

        interstitialService.suspend(for: .bankTransfer)
        coordinator.start(completion: completion)
    }

    func startShopFlow(
        analyticsSourcePage: AnalyticsConstants.SourcePage,
        completion: (() -> Void)? = nil
    ) {
        let shopCoordinator = MayaShopCoordinator(presenter: router, analyticsSourcePage: analyticsSourcePage)
        shopCoordinator.presentationDirection = .right
        shopCoordinator.delegate = self
        shopCoordinator.backActionDelegate = self
        shopCoordinator.routeActionDelegate = self
        addChild(shopCoordinator)
        shopCoordinator.start(completion: completion)
    }

    func startInboxFlow(completion: (() -> Void)? = nil, arguments: Any? = nil) {
        if configurationService.mayaInboxFlutterEnabled.value {
            guard let coordinator = ******************************** else { return }
            coordinator.presentationDirection = .right
            coordinator.delegate = self
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            addChild(coordinator)
            coordinator.presentFullScreen(completion: completion, arguments: arguments)
        } else {
            let coordinator = MayaInboxCoordinator(presenter: router)
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            addChild(coordinator)
            coordinator.start(completion: completion)
        }
    }

    func setupInboxIcon(mayaDashboardMenuPagerViewController: MayaDashboardMenuPagerViewController) {
        guard let coordinator = ******************************** else { return }
        coordinator.setupFlutter(mayaDashboardViewController: mayaDashboardMenuPagerViewController)
    }

    func startCreditTransferFundsFlow(completion: (() -> Void)? = nil, creditAccount: MayaCreditAccount, feeRate: Float?) {
        let coordinator = NewMayaCreditTransferFundsCoordinator(presenter: router, creditAccount: creditAccount, feeRate: feeRate)
        coordinator.presentationDirection = .right
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startAddMoneyFlow(
        analyticsSourcePage: AnalyticsConstants.SourcePage,
        completion: (() -> Void)? = nil
    ) {
        let coordinator = MayaCashInCoordinator(presenter: router, analyticsSourcePage: analyticsSourcePage)
        coordinator.presentationDirection = .right
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startMissionsFlow(completion: (() -> Void)? = nil) {
        let coordinator = MayaMissionsCoordinator(presenter: router)
        coordinator.presentationDirection = .right
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startPayBillsFlow(
        analyticsSourcePage: AnalyticsConstants.SourcePage,
        completion: (() -> Void)? = nil
    ) {
        let coordinator = MayaPayBillsCoordinator(presenter: router, analyticsSourcePage: analyticsSourcePage)
        coordinator.presentationDirection = .right
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startProfileQRFlow(completion: (() -> Void)? = nil) {
        let coordinator = MayaRequestMoneyCoordinator(presenter: router)
        coordinator.presentationDirection = .right
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startTravelFlow(completion: (() -> Void)? = nil) {
        let coordinator = MayaTravelCoordinator(presenter: router)
        coordinator.presentationDirection = .right
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startMayaMallFlow(completion: (() -> Void)? = nil) {
        guard let url = Constants.WebView.mayaMall.baseUrl else { return }
        let viewModel = MayaMallWebViewViewModel(url: url)
        let coordinator = WebViewCoordinator(presenter: router, viewModel: viewModel)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startMyFavoritesFlow(with favoriteType: FavoriteType? = nil, completion: (() -> Void)? = nil) {
        let coordinator = MayaMyFavoritesCoordinator(presenter: router, favoriteType: favoriteType)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startProtectFlow(completion: (() -> Void)? = nil) {
        let viewModel = ProtectWebViewViewModel()
        if #available(iOS 14.3, *) {
            let coordinator = WebViewCoordinator(presenter: router, viewModel: viewModel)
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            addChild(coordinator)
            coordinator.start(completion: completion)
        } else {
            showExternalURI(viewModel.link)
        }
    }

    func startSendMoneyCardActivityFlow(completion: (() -> Void)? = nil) {
        let coordinator = MayaSendMoneyCardActivityCoordinator(presenter: router, viewModel: nil)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startCreatorStoreFlow(_ creatorStore: CreatorStore, completion: (() -> Void)? = nil) {
        let coordinator = MayaCreatorStoreCoordinator(creatorStore: creatorStore, presenter: router)
        coordinator.presentationDirection = .right
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func showPartnerMerchantsScreen(completion: (() -> Void)? = nil) {
        guard let link = Constants.WebView.partnerMerchants.url else { return }
        showWebView(link: link, willPresent: true, startCompletion: completion)
    }

    func showDealsScreen(completion: (() -> Void)? = nil) {
        guard let link = Constants.WebView.promos.url else { return }
        showWebView(link: link, willPresent: true, startCompletion: completion)
    }

    func showDonateScreen(completion: (() -> Void)? = nil) {
        guard let link = Constants.WebView.donate.baseUrl else { return }
        showWebView(link: link, willPresent: true, startCompletion: completion)
    }

    func startInvestFlow(completion: (() -> Void)? = nil) {
        let coordinator = InvestCoordinator(presenter: router)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startCryptoV2Flow(completion: (() -> Void)? = nil) {
        let coordinator = CryptoCoordinator(presenter: router)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startUpgradeFlow(completion: (() -> Void)? = nil, isFromReg: Bool = false) {
        if isShortenedFlowEnabled {
            let coordinator = EKYCZolozCoordinator(presenter: router)
            coordinator.isFromReg = isFromReg
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            coordinator.delegate = self
            coordinator.analyticsModule = Analytics.Dashboard()
            addChild(coordinator)
            coordinator.start()
        }
    }

    func startReviewApplicationFlow(completion: (() -> Void)? = nil) {
        if isShortenedFlowEnabled {
            let coordinator = EKYCZolozReviewCoordinator(presenter: router)
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            addChild(coordinator)
            coordinator.start()
        }
    }

    func startReviewEDDFlow() {
        let coordinator = EDDReviewCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startPWPFlow(paymentId: String, completion: (() -> Void)? = nil) {
        let coordinator = MayaP2MCoordinator(presenter: router, merchantPaymentId: paymentId, pwpMode: .external)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startReferralInputCodeFlow(completion: (() -> Void)? = nil) {
        let coordinator = MayaReferralInputCodeCoordinator(presenter: router)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startFundsFlow(completion: (() -> Void)? = nil) {
        let coordinator = MayaFundsCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func showPreKYCFlow(analyticsModule: AnalyticsModule) {
        let coordinator = MayaPreKYCCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        coordinator.routeActionDelegate = self
        coordinator.analyticsModule = analyticsModule
        addChild(coordinator)
        coordinator.start()
    }

    func showAgeRestrictedFlow(_ ageRestriction: Service.AgeRestriction, serviceID: String) {
        let title = ageRestriction.title ?? L10n.Services.Underage.title
        let message = ageRestriction.message ?? L10n.Services.Underage.message
        let image = CommonAsset.Images.Alert.imageID.image
        let keyAttributes: [AnalyticsAttributeKey: String] = [.serviceTile: serviceID]

        let alertViewModel = MayaAlertViewModel(title: title, message: message, image: image)
        let coordinator = MayaAlertBottomSheetV2Coordinator(presenter: router, viewModel: alertViewModel, withCloseButton: false)
        coordinator.analyticsModuleType = .sheet(Analytics.AgeRestricted())
        coordinator.additionalAttributes = keyAttributes
        coordinator.routeActionDelegate = self
        coordinator.backActionDelegate = self

        alertViewModel.addAction(MayaAlertAction(title: CommonStrings.Common.Modal.Got.it, style: .primary) { [weak self, weak coordinator] in
            guard let self, let coordinator else { return }

            self.analyticsService.logMayaEvents(action: .tap(Analytics.AgeRestricted.gotIt), keyAttributes: keyAttributes)
            self.router.dismissModule(animated: true) {
                self.removeChild(coordinator)
            }
        })

        addChild(coordinator)
        coordinator.start()
    }

    func showReKYCFlow(analyticsModule: AnalyticsModule) {
        if mayaReKYCCoordinator == nil {
            mayaReKYCCoordinator = MayaReKYCCoordinator(presenter: router)
        }

        mayaReKYCCoordinator?.backActionDelegate = self
        mayaReKYCCoordinator?.delegate = self
        mayaReKYCCoordinator?.routeActionDelegate = self
        mayaReKYCCoordinator?.analyticsModule = analyticsModule
        addChild(mayaReKYCCoordinator!)
        mayaReKYCCoordinator?.start()
    }

    func showServicesFlow() {
        if configurationServiceV2.dashboardNavigationRefactorEnabled.value {
            let servicesCoordinator = MayaServicesCoordinator(presenter: router)
            servicesCoordinator.presentationDirection = .right
            servicesCoordinator.backActionDelegate = self
            servicesCoordinator.delegate = self
            servicesCoordinator.routeActionDelegate = self
            addChild(servicesCoordinator)
            servicesCoordinator.start()
        } else {
            guard let moreServicesViewController = mainTabBarController.viewControllers[safeIndex: MayaTab.services.index] else { return }
            mainTabBarController.selectedViewController = moreServicesViewController
        }
    }

    func startServiceFlow(service: Service) {
        guard let uri = service.uri else { return }

        var updatedURLString = uri
        if let url = service.url,
            let serviceType = service.serviceType,
            let updatedURL = url.appendQueryItem(name: ServiceTypeConstants.serviceType, value: serviceType.rawValue) {
            updatedURLString = updatedURL.absoluteString
        }

        guard let updatedURL = URL(string: updatedURLString) else { return }

        routeActionDelegate?.didRequestStartDeepLink(self, with: updatedURL, attributes: [.url: uri], completion: nil)
    }

    func startUpdateProfileFlow(_ payload: [String: String]?, completion: (() -> Void)? = nil) {
        if configurationService.updateProfileDeeplinkEnabled.value {
            // Dissmiss old coordinator if UpdateProfileCoordinator is already present (when called via deeplink)
            if let coordinator = getActiveUpdateProfileCoordinator(), payload != nil {
                coordinator.delegate?.dismissCoordinator(coordinator)
            }

            let coordinator = MayaUpdateProfileCoordinator(payload: payload, presenter: router)
            coordinator.presentationDirection = .right
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            coordinator.delegate = self
            addChild(coordinator)

            coordinator.start(screen: .updateProfile)
        }
    }

    func startProfileWorkDetailsFlow(_ payload: [String: String]?, completion: (() -> Void)? = nil) {
        if configurationService.updateProfileDeeplinkEnabled.value {
            // Dissmiss old coordinator if UpdateProfileCoordinator is already present (when called via deeplink)
            if let coordinator = getActiveUpdateProfileCoordinator(), payload != nil {
                coordinator.delegate?.dismissCoordinator(coordinator)
            }

            let coordinator = MayaUpdateProfileCoordinator(payload: payload, presenter: router)
            coordinator.presentationDirection = .right
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            coordinator.delegate = self
            addChild(coordinator)
            coordinator.start(screen: .workDetails)
        }
    }

    func startProfileContactReferenceFlow(_ payload: [String: String]?, completion: (() -> Void)? = nil) {
        if configurationService.updateProfileDeeplinkEnabled.value {
            // Dissmiss old coordinator if UpdateProfileCoordinator is already present (when called via deeplink)
            if let coordinator = getActiveUpdateProfileCoordinator(), payload != nil {
                coordinator.delegate?.dismissCoordinator(coordinator)
            }

            let coordinator = MayaUpdateProfileCoordinator(payload: payload, presenter: router)
            coordinator.presentationDirection = .right
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            coordinator.delegate = self
            addChild(coordinator)
            coordinator.start(screen: .contactReference)
        }
    }

    func startLoadFlow(params: String?, completion: (() -> Void)? = nil) {
        let viewModel = MayaShopLoadViewModel()
        let shopLoadCoordinator = MayaShopLoadCoordinator(params: params, presenter: router, viewModel: viewModel)
        shopLoadCoordinator.backActionDelegate = self
        shopLoadCoordinator.routeActionDelegate = self
        shopLoadCoordinator.delegate = self
        addChild(shopLoadCoordinator)
        shopLoadCoordinator.start()
    }

    func startDeviceManagementFlow(completion: (() -> Void)? = nil) {
        let coordinator = MayaDeviceManagementCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startSecurityCenterFlow(animated: Bool = true, completion: (() -> Void)? = nil) {
        let coordinator = MayaSecurityCenterCoordinator(presenter: router, animated: animated)
        coordinator.presentationDirection = .right
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startFlutterInterstitial(animated: Bool = true, completion: (() -> Void)? = nil) {
        let coordinator = MayaFlutterInterstialCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        coordinator.animated = false
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startAutoDebitArrangementFlow(params: String?, animated: Bool = true, completion: (() -> Void)? = nil) {
        let autoDebitArrangementCoordinator = MayaAutoDebitArrangementCoordinator(
            params: params,
            presenter: router,
            animated: animated
        )
        autoDebitArrangementCoordinator.presentationDirection = .right
        autoDebitArrangementCoordinator.backActionDelegate = self
        autoDebitArrangementCoordinator.routeActionDelegate = self
        autoDebitArrangementCoordinator.delegate = self
        addChild(autoDebitArrangementCoordinator)
        autoDebitArrangementCoordinator.start(completion: completion)
    }

    @discardableResult
    func showDashboardMenu(_ menu: MayaDashboardMenu) -> MayaDashboardCoordinator? {
        if let dashboardViewController = mainTabBarController.viewControllers[safeIndex: MayaTab.dashboard.index] {
            mainTabBarController.selectedViewController = dashboardViewController
            if let coordinator = getChildCoordinator(identifier: MayaTab.dashboard.associatedCoordinator) as? MayaDashboardCoordinator {
                coordinator.selectDashboardMenu(menu)
                return coordinator
            }
        }
        return nil
    }
}

// MARK: - Private Method for UpdateProfileCoordinator
fileprivate extension MayaMainTabCoordinator {
    func getActiveUpdateProfileCoordinator() -> MayaUpdateProfileCoordinator? {
        guard let coordinator = childCoordinators.first(where: { $0 is MayaUpdateProfileCoordinator }) as? MayaUpdateProfileCoordinator else {
            return nil
        }
        return coordinator
    }
}

// MARK: - ********************************Delegate Methods
extension MayaMainTabCoordinator: ********************************Delegate {
    func didDismiss(coordinator: ********************************) {
        removeChild(coordinator)
    }
}

// MARK: MayaReKYCCoordinatorDelegate Functions
extension MayaMainTabCoordinator: MayaReKYCCoordinatorDelegate {
    func reKYCCoordinatorDispose(_ coordinator: MayaReKYCCoordinator) {
        removeChild(coordinator)
        mayaReKYCCoordinator = nil
    }

    func reKYCCoordinatorDidFinishKYC(_ coordinator: MayaReKYCCoordinator, shouldProceedToProfile: Bool = false, completion: (() -> Void)?) {
        removeChild(coordinator)
        mayaReKYCCoordinator = nil

        // MARK: Check values with CMS again
        if let action = ekycBaseViewModel?.getExpandedReKYCAction(),
           isExpandedReKYCEnabled {
            if shouldProceedToProfile,
               action == ReKYCData.ActionCode.noAction.rawValue || action == ReKYCData.ActionCode.nudge.rawValue {
                startProfileFlow()
            } else if action != ReKYCData.ActionCode.noAction.rawValue {
                showReKYCFlow(analyticsModule: Analytics.Dashboard())
            }
        }
    }

    func reKYCCoordinatorDidTapBack(_ coordinator: MayaReKYCCoordinator) {
        removeChild(coordinator)
        if let rootCoordinator = backActionDelegate as? Coordinator {
            rootCoordinator.removeChild(self)
        }
        mayaReKYCCoordinator = nil

        // Add handling for UI Block + Submitted when expandedReKYC is ON
        if isExpandedReKYCEnabled {
            showReKYCFlow(analyticsModule: Analytics.Dashboard())
        } else {
            // Force to show Re-KYC reminder bottom sheet
            guard let databaseStore = storeProvider.target(DatabaseStore.self),
                  let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
                   else { return }
            if user.reKYCStatusValue == .tagged || user.reKYCStatusValue == .rejected {
                showReKYCFlow(analyticsModule: Analytics.Dashboard())
            }
        }
    }
}

// MARK: MayaBankTransferCoordinatorDelegate Functions
extension MayaMainTabCoordinator: MayaBankTransferCoordinatorDelegate {
    func didRequestBankTransferList(_ coordinator: MayaBankTransferCoordinator) {
        removeChild(coordinator)
        guard let bankTransferURL = Constants.DeepLinkPath.banktransfer.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: bankTransferURL, completion: nil)
        interstitialService.resume(for: .bankTransfer)
    }

    func didRequestRedirectToDashboard(_ coordinator: MayaBankTransferCoordinator) {
        removeChild(coordinator)
        guard let url = Constants.DeepLinkPath.dashboard.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
        interstitialService.resume(for: .bankTransfer)
    }
}

// MARK: - MayaSendMoneyCoordinator Methods
extension MayaMainTabCoordinator: MayaSendMoneyCoordinatorDelegate {
    func didRequestRedirectToDashboard(_ coordinator: MayaSendMoneyCoordinator) {
        removeChild(coordinator)
        interstitialService.resume(for: .sendMoney)
        guard let url = Constants.DeepLinkPath.dashboard.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
    }
}

// MARK: - MayaPayBillsCoordinator Methods
extension MayaMainTabCoordinator: MayaPayBillsCoordinatorDelegate {
    func didRequestRedirectToDashboard(_ coordinator: MayaPayBillsCoordinator) {
        removeChild(coordinator)
        guard let url = Constants.DeepLinkPath.dashboard.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
    }
}

// MARK: MayaReferralInputCodeCoordinatorDelegate Functions
extension MayaMainTabCoordinator: MayaReferralInputCodeCoordinatorDelegate {
    func didTapUpgradeAccount(_ coordinator: MayaReferralInputCodeCoordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.startUpgradeFlow()
        })
    }

    func didTapReviewEDD(_ coordinator: MayaReferralInputCodeCoordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.startReviewEDDFlow()
        })
    }

    func didTapReviewApplication(_ coordinator: MayaReferralInputCodeCoordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.startReviewApplicationFlow()
        })
    }

    func didRequestRedirectToDashboard(_ coordinator: MayaReferralInputCodeCoordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
        })
    }
}

// MARK: MayaFundsCoordinatorDelegate Functions
extension MayaMainTabCoordinator: MayaFundsCoordinatorDelegate {
    func didReceiveFundsError(error: PayMayaError, coordinator: MayaFundsCoordinator) {
        if case .sessionTimeout = error.type {
            // Do nothing
        } else if let errorViewModel = error.viewModel {
            showErrorAlert(errorViewModel)
        }
        removeChild(coordinator)
    }

    func didFinishFunds(coordinator: MayaFundsCoordinator) {
        // TODO: Update code
        removeChild(coordinator)
    }
}

// MARK: MayaPreKYCCoordinatorDelegate Functions
extension MayaMainTabCoordinator: MayaPreKYCCoordinatorDelegate {
    func preKYCDidShowBottomSheet(_ coordinator: MayaPreKYCCoordinator) {
        complete(deepLinkHandling: deepLinkHandling, error: .kycRequired)
    }

    func preKYCCoordinatorDidFinishKYC(_ coordinator: MayaPreKYCCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
        isKYCCompleted = true
        complete(deepLinkHandling: deepLinkHandling, error: nil)
    }
}

// MARK: - EKYCZolozCoordinatorDelegate Methods
extension MayaMainTabCoordinator: EKYCZolozCoordinatorDelegate {
    func sessionTimeoutErrorOccured(_ coordinator: EKYCZolozCoordinator) {
        removeChild(coordinator)
    }

    func dismissCoordinator(_ coordinator: Coordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
        })
    }

    func dismissAndPushProfileCoordinator(_ coordinator: Coordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)

            guard let visibleViewController = router.visibleViewController,
                  !visibleViewController.isKind(of: MayaProfileViewController.self) else {
                return
            }

            self.startProfileFlow()
        })
    }
}

// MARK: - EDDReviewCoordinatorDelegate Methods
extension MayaMainTabCoordinator: EDDReviewCoordinatorDelegate {
    func didReceiveError(_ error: PayMayaError, coordinator: EDDReviewCoordinator) {
        removeChild(coordinator)

        guard let errorViewModel = error.viewModel else { return }
        if case .sessionTimeout = error.type {
            // Do nothing
        } else {
            showErrorAlert(errorViewModel, useMayaModal: true)
        }
    }
}

// MARK: - LoansCreditCardFullScreenCoordinatorDelegate Methods
extension MayaMainTabCoordinator: LoansCreditCardFullScreenCoordinatorDelegate {
    func didDismiss(coordinator: LoansCreditCardFullScreenCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
    }

    func didRequestFullScreen(coordinator: LoansCreditCardFullScreenCoordinator) {
        // Do nothing
    }

    func didRequestFullScreen(coordinator: LoansCreditCardFullScreenCoordinator, route: String, args: [String: String], completion: (() -> Void)?) {
        // Do nothing
    }
}

// MARK: - PurchaseFinancingCoordinatorDelegate Methods
extension MayaMainTabCoordinator: MayaPurchaseFinancingCoordinatorDelegate {
    func didDismiss(coordinator: MayaPurchaseFinancingCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
    }
}

// MARK: MayaUpdateProfileCoordinatorDelegate methods
extension MayaMainTabCoordinator: MayaUpdateProfileCoordinatorDelegate {
    func dismissCoordinator(_ coordinator: MayaUpdateProfileCoordinator) {
        router.dismissModule(animated: false) { [weak self] in
            self?.removeChild(coordinator)
        }
    }

    func didTapNext(_ coordinator: MayaUpdateProfileCoordinator) {
        router.dismissModule(animated: false) { [weak self] in
            self?.removeChild(coordinator)
        }
    }
}

// MARK: - MayaShopCoordinatorDelegate Methods
extension MayaMainTabCoordinator: MayaShopCoordinatorDelegate {
    func didCompleteLoadTransaction(_ coordinator: MayaShopCoordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)

            self.showDashboardMenu(.wallet)
        })
    }
}

extension MayaMainTabCoordinator: MayaShopLoadCoordinatorDelegate {
    func didComplete(_ coordinator: MayaShopLoadCoordinator) {
        self.removeChild(coordinator)

        self.showDashboardMenu(.wallet)
    }

    func didDismiss(_ coordinator: MayaShopLoadCoordinator, error: PayMayaError?) {
        if let error, let errorViewModel = error.viewModel {
            showErrorAlert(errorViewModel, useMayaModal: true)
        }

        self.removeChild(coordinator)
    }
}

extension MayaMainTabCoordinator: MayaAutoDebitArrangementCoordinatorDelegate {
    func didComplete(_ coordinator: MayaAutoDebitArrangementCoordinator) {
        self.removeChild(coordinator)

        self.showDashboardMenu(.wallet)
    }

    func didDismiss(_ coordinator: MayaAutoDebitArrangementCoordinator) {
        self.removeChild(coordinator)
    }
}

// MARK: - Voucher Module Methods
private extension MayaMainTabCoordinator {
    func handleVouchersDeepLink(deepLink: DeepLink, completion: (() -> Void)? = nil) -> DeepLinkHandling {
        let isVoucherDetailsV2Enabled = configurationService.voucherDetailsV2Enabled.value
        let className = String(describing: isVoucherDetailsV2Enabled ? MayaVoucherListCoordinator.self : MayaVouchersCoordinator.self)
        return .normal(deepLink, identifier, className, { [weak self] in
            self?.startVouchersFlow(fromDeepLink: true, completion: completion)
        }, false)
    }

    func startVouchersFlow(fromDeepLink: Bool = false, completion: (() -> Void)? = nil) {
        if configurationService.voucherDetailsV2Enabled.value {
            startVoucherListFlowV2(completion: completion)
            return
        }

        startVoucherListFlowV1(fromDeepLink: fromDeepLink, completion: completion)
    }

    func startVoucherListFlowV1(fromDeepLink: Bool = false, completion: (() -> Void)? = nil) {
        let coordinator = MayaVouchersCoordinator(presenter: router)
        coordinator.presentationDirection = .right
        coordinator.isOpeningFromDeepLink = fromDeepLink
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startVoucherListFlowV2(completion: (() -> Void)? = nil) {
        let coordinator = MayaVoucherListCoordinator(presenter: router)
        coordinator.presentationDirection = .right
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }
}

// MARK: - MayaSecurityCenterCoordinatorDelegate Methods
extension MayaMainTabCoordinator: MayaSecurityCenterCoordinatorDelegate {
    func didDismiss(coordinator: MayaSecurityCenterCoordinator) {
        removeChild(coordinator)
    }

    func didSetSourceAttributes(coordinator: MayaSecurityCenterCoordinator) -> [String] {
        return [AnalyticsConstants.SourcePage.dashboard.rawValue, AnalyticsConstants.Screen.SecurityCenter.Button.freezeButton.rawValue]
    }
}

// MARK: - MayaFlutterInterstialCoordinatorDelegate methods
extension MayaMainTabCoordinator: MayaFlutterInterstialCoordinatorDelegate {
    func didDismiss(coordinator: MayaFlutterInterstialCoordinator) {
        removeChild(coordinator)
    }

    func didContinue(coordinator: MayaFlutterInterstialCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }
            removeChild(coordinator)
            startSecurityCenterFlow()
        }
    }
}

// MARK: - MayaMMACoordinatorDelegate methods
extension MayaMainTabCoordinator: MayaMMACoordinatorDelegate {
    func coordinatorDidTapBack(_ coordinator: MayaMMACoordinator) {
        removeChild(coordinator)
    }

    func coordinatorDidTapContinue(_ coordinator: MayaMMACoordinator, mma: MMA) {
        /*
         Since the web view controller is presented modally (when used with WebViewCoordinator),
         we first have to dismiss the interstitial then start the MMA flow again.
         This is a hacky fix, that will need to be refactored when the interstitial needs
         to be presented every time (like in Lucky Games).
        */
        router.dismissModule(animated: true) { [weak self] in
            guard let self else { return }
            removeChild(coordinator)
            startMMAFlow(mma)
        }
    }
}
