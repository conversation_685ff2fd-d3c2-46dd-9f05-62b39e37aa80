//
//  MayaLandingCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 3/3/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import ConfigurationService
import Injector
import StoreProvider

class MayaLandingCoordinator: Coordinator, MayaAnalyticsDurationManagerProtocol {
    @Inject var storeProvider: StoreProvider
    @Inject var shieldDataCollector: ShieldDataCollectorProtocol
    @Inject var configurationServiceV2: ConfigurationServiceV2
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    var analyticsSourcePage: AnalyticsConstants.SourcePage?
    weak var delegate: AuthenticationCoordinatorDelegate?

    override func start(completion: (() -> Void)? = nil) {
        startSplitBlockingFlow(for: .anonymous, completion: completion)
    }

    func showOptionalLocationPermissionFlow(completion: (() -> Void)? = nil) {
        let locationPermissionCoordinator = OptionalLocationPermissionCoordinator(presenter: router)
        locationPermissionCoordinator.routeActionDelegate = self
        locationPermissionCoordinator.delegate = self
        addChild(locationPermissionCoordinator)
        locationPermissionCoordinator.start(completion: completion)
    }
}

// MARK: LandingScreenViewControllerDelegate Methods
extension MayaLandingCoordinator: MayaWelcomeViewControllerDelegate {
    func didRequireMayaIntroductionScreens(_ viewController: MayaWelcomeViewController) {
        showIntroductionScreen()
    }

    func didTapRegistration(_ viewController: MayaWelcomeViewController) {
        handleRegistrationFlow()
    }

    func didTapLogin(_ viewController: MayaWelcomeViewController) {
        showLoginScreen()
    }

    func didRequestLocationPermission(_ viewController: MayaWelcomeViewController) {
        showOptionalLocationPermissionFlow()
    }

    func didTapRegisterToShowTinbo(_ viewController: MayaWelcomeViewController) {
        showTinboBottomSheet(viewController)
    }
}

extension MayaLandingCoordinator: MayaWelcomeV2ViewControllerDelegate {
    func didRequireMayaIntroductionScreens(_ viewController: MayaWelcomeV2ViewController) {
        showIntroductionScreen()
    }

    func didTapRegistration(_ viewController: MayaWelcomeV2ViewController) {
        handleRegistrationFlow()
    }

    func didTapLogin(_ viewController: MayaWelcomeV2ViewController) {
        showLoginScreen()
    }

    func didRequestLocationPermission(_ viewController: MayaWelcomeV2ViewController) {
        showOptionalLocationPermissionFlow()
    }

    func didTapRegisterToShowTinbo(_ viewController: MayaWelcomeV2ViewController) {
        showTinboBottomSheet(viewController)
    }
}

// MARK: Navigation Methods
fileprivate extension MayaLandingCoordinator {
    func showIntroductionScreen() {
        let introCoordinator = MayaIntroductionCoordinator(presenter: router)
        introCoordinator.backActionDelegate = self
        introCoordinator.routeActionDelegate = self
        addChild(introCoordinator)
        introCoordinator.start()
    }

    func showRegistrationScreen() {
        let coordinator = MayaRegistrationCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func showRegistrationV2Screen() {
        let coordinator = MayaRegistrationV2Coordinator(presenter: router)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func handleRegistrationFlow() {
        if configurationServiceV2.shortenedRegistrationEnabled.value {
            showRegistrationV2Screen()
        } else {
            showRegistrationScreen()
        }
    }

    func showLoginScreen() {
        let loginCoordinator = MayaLoginCoordinator(presenter: router)
        addChild(loginCoordinator)
        loginCoordinator.delegate = delegate
        loginCoordinator.backActionDelegate = self
        loginCoordinator.routeActionDelegate = self
        loginCoordinator.start()
    }

    func showTinboBottomSheet(_ viewController: UIViewController) {
        let alertViewModel = MayaAlertViewModel(
            title: L10n.Registration.Second.Tinbo.Alert.title,
            message: L10n.Registration.Second.Tinbo.Alert.message,
            image: Asset.MayaImages.Prelogin.iconTinbo.image
        )

        let openAction = MayaAlertAction(title: L10n.Registration.Second.Tinbo.Alert.Button.title, style: .primary) { [weak self] in
            guard let self else { return }
            if let encryptedStore = self.storeProvider.target(EncryptedDefaultsStore.self) {
                encryptedStore.write(ShownConfig(hasBeenShown: true), options: EncryptedDefaultsStoreId.tinboPromptShown)
            }
            self.analyticsService.logMayaEvents(action: .tap(Analytics.Tinbo.openTinbo), attributes: [AnalyticsAttributeKey.screen.rawValue: "landing page"])
            self.didRequestDismissMayaBottomAlert(viewController, viewModel: alertViewModel) {
                showExternalURI(Constants.WebView.tinboRegistration.url)
            }
        }
        alertViewModel.addAction(openAction)

        let closeAction = MayaAlertAction(title: CommonStrings.Common.close, style: .link) { [weak self] in
            guard let self else { return }
            if let encryptedStore = self.storeProvider.target(EncryptedDefaultsStore.self) {
                encryptedStore.write(ShownConfig(hasBeenShown: true), options: EncryptedDefaultsStoreId.tinboPromptShown)
            }
            self.analyticsService.logMayaEvents(action: .tap(Analytics.Tinbo.close), attributes: [AnalyticsAttributeKey.screen.rawValue: "landing page"])
            self.didRequestDismissMayaBottomAlert(viewController, viewModel: alertViewModel, completion: nil)
        }
        alertViewModel.addAction(closeAction)

        showMayaBottomAlert(alertViewModel, withCloseButton: false, analyticsModuleType: .sheet(Analytics.Tinbo()), analyticsAttributes: [.screen: "landing page"])
    }
}

// MARK: OptionalLocationPermissionCoordinatorDelegate Methods
extension MayaLandingCoordinator: OptionalLocationPermissionCoordinatorDelegate {
    func didFinishLocationPermission(_ coordinator: OptionalLocationPermissionCoordinator) {
        if coordinator.router.rootViewController != nil {
            router.dismissModule(animated: true) { [weak self] in
                guard let self = self else { return }
                self.removeChild(coordinator)
            }
        } else {
            removeChild(coordinator)
        }
    }
}

// MARK: - MayaRegistrationCoordinatorDelegate Methods
extension MayaLandingCoordinator: MayaRegistrationCoordinatorDelegate {
    func didSuccessfullyAuthenticate(_ coordinator: Coordinator, response: Codable?) {
        self.removeChild(coordinator)
        self.delegate?.didSuccessfullyAuthenticate(self, response: response)
    }

    func didTapLogin(_ coordinator: MayaRegistrationCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }

            self.removeChild(coordinator)
            self.showLoginScreen()
        }
    }
}

// MARK: - SplitBlocking Methods
extension MayaLandingCoordinator: SplitBlocking {
    func didFinishBlocking(completion: (() -> Void)?) {
        let viewModel = MayaLandingViewModel()
        if viewModel.isLandingPageV2Enabled {
            let viewController = StoryboardScene.Main.mayaWelcomeViewControllerV2.instantiate()
            viewController.delegate = self
            viewController.routeActionDelegate = self
            viewController.bind(viewModel)
            viewController.analyticsSourcePage = analyticsSourcePage
            setRootModule(viewController, animated: true)
            presenter?.present(router, animated: true, completion: completion)
        } else {
            let viewController = StoryboardScene.Main.mayaWelcomeViewController.instantiate()
            viewController.delegate = self
            viewController.routeActionDelegate = self
            viewController.bind(viewModel)
            setRootModule(viewController, animated: true)
            presenter?.present(router, animated: true, completion: completion)
        }
    }
}
