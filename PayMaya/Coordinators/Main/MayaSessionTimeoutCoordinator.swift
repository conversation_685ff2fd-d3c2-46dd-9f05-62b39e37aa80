//
//  MayaSessionTimeoutCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 3/10/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import ConfigurationService
import Foundation
import Injector
import MayaCoreData
import StoreProvider

/// Used for posting notification after successful re-login on session timeout
let sessionTimeoutSuccessNotification = "Application Session Timeout Success"

protocol MayaSessionTimeoutCoordinatorDelegate: AnyObject {
    func didSwitchAccount(_ coordinator: MayaSessionTimeoutCoordinator)
    func didSuccessfullyAuthenticate(_ coordinator: MayaSessionTimeoutCoordinator, completion: (() -> Void)?)
}

class MayaSessionTimeoutCoordinator: Coordinator {
    @Inject var configurationService: ConfigurationService
    @Inject var configurationServiceV2: ConfigurationServiceV2
    @Inject private var flutterEngineManager: FlutterEngineManager
    @Inject private var notificationService: NotificationService
    @Inject private var storeProvider: StoreProvider
    @Inject private var profileTrackingManager: ProfileTrackingManager

    weak var delegate: MayaSessionTimeoutCoordinatorDelegate?

    /// Posts success session timeout notification after delegate handles transition of flows
    private let successCompletion: (() -> Void)? = {
        NotificationCenter.default.post(name: NSNotification.Name(rawValue: sessionTimeoutSuccessNotification), object: nil)
    }

    override func start(completion: (() -> Void)? = nil) {
        startSplitBlockingFlow(for: .anonymous, completion: completion)
    }
}

// MARK: SplitBlocking methods
extension MayaSessionTimeoutCoordinator: SplitBlocking {
    func didFinishBlocking(completion: (() -> Void)?) {
        let viewModel = MayaLoginViewModel()
        let viewController = StoryboardScene.Account.mayaSessionTimeoutViewController.instantiate()
        viewController.delegate = self
        viewController.routeActionDelegate = self
        viewController.bind(viewModel)
        rootViewController = viewController
        router.presentRootModule(viewController, animated: true, completion: completion)
    }
}

// MARK: SessionTimeoutViewControllerDelegate Methods
extension MayaSessionTimeoutCoordinator: MayaSessionTimeoutViewControllerDelegate {
    func didAuthenticate(_ viewController: MayaSessionTimeoutViewController) {
        delegate?.didSuccessfullyAuthenticate(self, completion: successCompletion)
    }

    func didReceiveOTPId(_ viewController: MayaSessionTimeoutViewController, otpId: String, mobileNumber: String?) {
        let otpType = OTPType.sessionTimeout(otpId: otpId, mobileNumber: String.unwrappedValue(mobileNumber))
        startOTPFlow(otpType: otpType)
    }

    func didViewTermsAndCondition(_ viewController: MayaSessionTimeoutViewController) {
        showDataPrivacyFlow()
    }

    func didForgotPassword(_ viewController: MayaSessionTimeoutViewController) {
        showForgotPassword()
    }

    func didSwitchAccount(_ viewController: MayaSessionTimeoutViewController) {
        delegate?.didSwitchAccount(self)
    }

    func didRequestLocationPermission(_ viewController: MayaSessionTimeoutViewController) {
        showOptionalLocationPermissionFlow()
    }

    func didStartMFAOTPChallenge(_ viewController: MayaSessionTimeoutViewController, challengeId: String, mobileNumber: String?, transactionType: String) {
        let otpType = OTPType.sessionTimeoutv5(challengeId: challengeId, mobileNumber: String.unwrappedValue(mobileNumber), transactionType: transactionType)
        startOTPFlow(otpType: otpType)
    }

    func didReceiveMFAFaceChallenge(_ viewController: MayaSessionTimeoutViewController, challengeId: String, lifestyleId: String, transactionType: String) {
        let number = viewController.numberLabel.text ?? ""
        let mobileNumber = String.formatToPhilippinesMSISDN(number)
        let data = [.mfaType: MFAType.face as Any,
                    .challengeId: challengeId,
                    .lifestyleId: lifestyleId,
                    .mobileNumber: mobileNumber,
                    .transactionType: transactionType,
                    .sourcePage: AnalyticsConstants.ScreenName.loginPage,
                    .successPage: AnalyticsConstants.ScreenName.dashboard] as [MFAParameterKeys: Any]
        startMayaMFACoordinator(data: data)
    }

    func didReceiveMFANoFaceDataError(_ viewController: MayaSessionTimeoutViewController, errorViewModel: ErrorAlertViewModel) {
        showMFANoFaceDataAlert(viewController: viewController, viewModel: errorViewModel)
    }
}

// MARK: MayaDataPrivacyCoordinatorDelegate methods
extension MayaSessionTimeoutCoordinator: MayaDataPrivacyCoordinatorDelegate {
    func didAccept(_ coordinator: MayaDataPrivacyCoordinator, previousViewController: ViewController?, with consent: ConsentGroup?) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.delegate?.didSuccessfullyAuthenticate(self, completion: self.successCompletion)
        })
    }

    func didDisagreeToDataPrivacy(_ coordinator: MayaDataPrivacyCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            self?.removeChild(coordinator)
        }
    }
}

// MARK: AuthenticationCoordinatorDelegate Methods
extension MayaSessionTimeoutCoordinator: AuthenticationCoordinatorDelegate {
    func didSuccessfullyAuthenticate(_ coordinator: Coordinator, response: Codable?) {
        delegate?.didSuccessfullyAuthenticate(self, completion: successCompletion)
    }
}

// MARK: OptionalLocationPermissionCoordinatorDelegate Methods
extension MayaSessionTimeoutCoordinator: OptionalLocationPermissionCoordinatorDelegate {
    func didFinishLocationPermission(_ coordinator: OptionalLocationPermissionCoordinator) {
        if coordinator.router.rootViewController != nil {
            router.dismissModule(animated: true) { [weak self] in
                guard let self = self else { return }
                self.removeChild(coordinator)
            }
        } else {
            removeChild(coordinator)
        }
    }
}

// MARK: MayaWKWebViewControllerDelegate Methods
extension MayaSessionTimeoutCoordinator: MayaWKWebViewControllerDelegate {
    func didFinishWebView(_ viewController: ViewController, isSuccessful: Bool, response: Codable?) {
        let successAlertViewModel = SuccessAlertViewModel(title: L10n.Forgot.Password.success, closeCompletion: { [weak self] in
            self?.router.popToRootViewController(animated: true)
        })
        self.router.toPresentable().showSuccessAlert(successAlertViewModel)
    }

    func didCancelWebView(_ viewController: ViewController) {
        // Do nothing
    }
}

// MARK: Private Methods
private extension MayaSessionTimeoutCoordinator {
    func showForgotPassword() {
        if configurationService.forgotPasswordV2Enabled.value {
            let coordinator = MayaForgotPasswordCoordinator(presenter: router)
            // TODO: delegate to be implemented on another ticket
            // coordinator.delegate = self
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            coordinator.presentationDirection = .rightToLeft
            addChild(coordinator)
            coordinator.start()
            return
        }

        guard let baseUrl = Constants.WebView.forgotPassword.url,
              let successUrlString = Constants.WebView.forgotPasswordSuccess.url?.absoluteString
        else {
            return
        }

        showWebView(link: baseUrl, title: Constants.WebView.forgotPassword.description, successPath: successUrlString, popCompletion: nil, delegate: self)
    }

    func showDataPrivacyFlow() {
        let dpCoordinator = MayaDataPrivacyCoordinator(presenter: router)
        dpCoordinator.source = .sessionTimeout
        dpCoordinator.delegate = self
        dpCoordinator.backActionDelegate = self
        dpCoordinator.routeActionDelegate = self
        addChild(dpCoordinator)
        dpCoordinator.start()
    }

    func startOTPFlow(otpType: OTPType, requestOTP: Bool = false) {
        let otpCoordinator = MayaOTPCoordinator(presenter: router, otpType: otpType)
        addChild(otpCoordinator)
        otpCoordinator.delegate = self
        otpCoordinator.authDelegate = self
        otpCoordinator.backActionDelegate = self
        otpCoordinator.routeActionDelegate = self
        dispatchToMain { [weak otpCoordinator] in
            guard let coordinator = otpCoordinator else { return }
            coordinator.start(requestOTP: requestOTP)
        }
    }

    func showOptionalLocationPermissionFlow() {
        let locationPermissionCoordinator = OptionalLocationPermissionCoordinator(presenter: router)
        locationPermissionCoordinator.routeActionDelegate = self
        locationPermissionCoordinator.delegate = self
        addChild(locationPermissionCoordinator)
        locationPermissionCoordinator.start()
    }

    func startMayaMFACoordinator(data: [MFAParameterKeys: Any]) {
        let coordinator = MayaMFACoordinator(data: data,
                                             router: router,
                                             presenter: presenter)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func showMFANoFaceDataAlert(viewController: ViewController, viewModel: ErrorAlertViewModel) {
        let alertViewModel = MayaAlertViewModel(title: CommonStrings.Error.Additional.Verification.title, message: viewModel.message, image: CommonAsset.Images.Alert.imageAccountError.image, actionsAxis: .vertical)
        let coordinator = MayaAlertModalCoordinator(presenter: router, viewModel: alertViewModel)
        coordinator.routeActionDelegate = self
        coordinator.backActionDelegate = self

        if let actionTitle = viewModel.primaryActionTitle,
           let actionURL = viewModel.primaryActionURL,
           let url = URL(string: actionURL) {
            alertViewModel.addAction(MayaAlertAction(title: actionTitle, style: .primary, handler: { [weak coordinator] in
                guard let coordinator = coordinator else { return }
                coordinator.didRequestWebView(viewController, with: url, willPresent: true, completion: nil)
            }))
        }
        alertViewModel.addAction(MayaAlertAction(title: CommonStrings.Common.Modal.Got.it, style: .secondary, handler: { [weak self, weak coordinator] in
            guard let self = self, let coordinator = coordinator else { return }
            self.router.dismissModule(animated: true) {
                self.removeChild(coordinator)
            }
        }))
        addChild(coordinator)
        coordinator.start()
    }
}

// MARK: MayaOTPCoordinatorDelegate Methods
extension MayaSessionTimeoutCoordinator: MayaOTPCoordinatorDelegate {
    func didReceiveOTPError(error: PayMayaError, _ coordinator: MayaOTPCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            self?.removeChild(coordinator)
        }
    }

    func didReceivePostAuthenticationActionError(_ coordinator: MayaOTPCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            self?.removeChild(coordinator)
        }
    }

    func didRequestDisplayMayaAlertViewModel(viewModel: MayaAlertViewModel, response: Codable?, coordinator: MayaOTPCoordinator) {
        // do nothing
    }

    func didRequestDisplayMayaReceiptViewModel(viewModel: MayaReceiptViewModelProtocol, response: Codable?, coordinator: MayaOTPCoordinator) {
        // do nothing
    }
}

// MARK: - MayaMFACoordinatorDelegate Methods
extension MayaSessionTimeoutCoordinator: MayaMFACoordinatorDelegate {
    func didCancelMFA(_ coordinator: MayaMFACoordinator, info: [MFAParameterKeys: Any]) {
        removeChild(coordinator)
        // TODO: add extra handling here if needed
    }

    func didEncounterError(_ coordinator: MayaMFACoordinator, info: [MFAParameterKeys: Any], dismissCaller: Bool) {
        removeChild(coordinator)
        // TODO: add extra handling here if needed
    }

    func didFailMFA(_ coordinator: MayaMFACoordinator, info: [MFAParameterKeys: Any]) {
        removeChild(coordinator)
        // TODO: add extra handling here if needed
    }

    func didSuccessfullyAuthenticate(_ coordinator: MayaMFACoordinator, info: [MFAParameterKeys: Any]) {
        removeChild(coordinator)

        // Do extra init after successful MFA authentication
        flutterEngineManager.initializeEngines()
        notificationService.sendPushTokenAction.apply().start()
        guard let databaseStore = storeProvider.target(DatabaseStore.self),
            let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
            else { return }
        var mfaSource: SendProfileModule = .loginOTP
        switch info[.mfaType] as! MFAType {
        case .face:
            mfaSource = .faceChallenge
        case .otp:
            mfaSource = .loginOTP
        }
        profileTrackingManager.setUserProfile(user, source: mfaSource)

        delegate?.didSuccessfullyAuthenticate(self, completion: successCompletion)
    }

    func shouldPerformOTPFallback(_ coordinator: MayaMFACoordinator, info: [MFAParameterKeys: Any]) {
        removeChild(coordinator)
        // show OTP fallback screen, reuse challenge ID
        guard let challengeId = info[.challengeId] as? String,
              let mobileNumber = info[.mobileNumber] as? String,
              let transactionType = info[.transactionType] as? String
        else { return }

        let otpType = OTPType.sessionTimeoutv5(challengeId: challengeId,
                                               mobileNumber: mobileNumber,
                                               transactionType: transactionType)
        startOTPFlow(otpType: otpType, requestOTP: true)
    }
}
