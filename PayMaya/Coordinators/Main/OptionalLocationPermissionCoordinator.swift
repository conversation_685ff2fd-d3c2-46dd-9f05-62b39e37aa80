//
//  OptionalLocationPermissionCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 12/15/21.
//  Copyright © 2021 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import ConfigurationService
import Injector
import StoreProvider
import UIKit

protocol OptionalLocationPermissionCoordinatorDelegate: AnyObject {
    func didFinishLocationPermission(_ coordinator: OptionalLocationPermissionCoordinator)
}

class OptionalLocationPermissionCoordinator: Coordinator, MayaAnalyticsDurationProtocol, AnalyticsServiceProtocol {
    @Inject var analyticsService: AnalyticsService
    @Inject private var configurationService: ConfigurationService
    @Inject private var storeProvider: StoreProvider
    @Inject private var locationAuthorizerService: LocationAuthorizerServiceProtocol

    weak var delegate: OptionalLocationPermissionCoordinatorDelegate?

    var shouldShowBottomSheet: Bool {
        var wasShown = false
        if let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
           let wasShownForUser = encryptedStore.read(EncryptedDefaultsStoreId.locationPermissionBottomSheetShown, type: ShownConfig.self).value {
            wasShown = wasShownForUser.hasBeenShown
        }
        return !wasShown
    }

    var hasBeenSkipped: Bool {
        if let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
           let wasSkippedByUser = encryptedStore.read(EncryptedDefaultsStoreId.locationPermissionSkipped, type: ShownConfig.self).value {
            return wasSkippedByUser.hasBeenShown
        }
        return false
    }

    override func start(completion: (() -> Void)? = nil) {
        guard !hasBeenSkipped && locationAuthorizerService.authorizationStatus == .notDetermined else {
            delegate?.didFinishLocationPermission(self)
            return
        }

        locationAuthorizerService.setupService(delegate: self)
    }

    func startLocationPermissionFlow() {
        let viewController = StoryboardScene.Main.mayaInitialViewController.instantiate()
        viewController.bind(MayaInitialViewModel())
        viewController.canShowHud = false
        setRootModule(viewController, animated: true)
        presenter?.present(router, animated: false, completion: { [weak self] in
            guard let self = self else { return }
            self.showLocationPermissionBottomSheet()
        })
    }

    func startNativeLocationPermissionModalFlow() {
        let viewController = StoryboardScene.Main.mayaInitialViewController.instantiate()
        viewController.bind(MayaInitialViewModel())
        viewController.canShowHud = false
        setRootModule(viewController, animated: true)
        presenter?.present(router, animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.showNativeLocationPermissionModal()
        })
    }

    func showLocationPermissionBottomSheet() {
        let alert1 = MayaAlertAction(title: L10n.Location.Permission.Bottomsheet.continue, style: .primary) { [weak self] in
            guard let self = self else { return }
            self.analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.location.tapped(),
                                                keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.BottomSheet.locationPersmission,
                                                                AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.locationOS,
                                                                AnalyticsAttributeKey.button: L10n.Location.Permission.Bottomsheet.continue])

            self.setLocationPermissionBottomSheetWasShown()
            self.showNativeLocationPermissionModal()
        }

        let viewModel = MayaAlertViewModel(title: L10n.Location.Permission.Bottomsheet.title,
                                           attributedMessage: NSAttributedString(string: L10n.Location.Permission.Bottomsheet.message),
                                           image: Asset.MayaImages.Prelogin.imageLocationPermission.image,
                                           actions: [alert1],
                                           actionsAxis: .vertical)

        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.location.viewed(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.BottomSheet.locationPersmission])
        showMayaBottomAlert(viewModel, withCloseButton: false, analyticsModuleType: .screen(Analytics.LocationPermission()))
    }

    func showNativeLocationPermissionModal() {
        locationAuthorizerService.requestAuthorization()
    }

    // MARK: - Bottom Sheet Methods
    func setLocationPermissionBottomSheetWasShown() {
        guard let encryptedDefaultsStore = storeProvider.target(EncryptedDefaultsStore.self) else { return }

        encryptedDefaultsStore.write(ShownConfig(hasBeenShown: true), options: EncryptedDefaultsStoreId.locationPermissionBottomSheetShown)
    }

    /// This is unused already, at least the setting of value for this config
    /// However the reading of value is still in use for backwards compatibility
    /// Retaining this code to avoid confusion as the read config is still needed
    func setLocationPermissionWasSkipped() {
        guard let encryptedDefaultsStore = storeProvider.target(EncryptedDefaultsStore.self) else { return }

        encryptedDefaultsStore.write(ShownConfig(hasBeenShown: true), options: EncryptedDefaultsStoreId.locationPermissionSkipped)
    }
}

// MARK: - LocationAuthorizerServiceDelegate methods
extension OptionalLocationPermissionCoordinator: LocationAuthorizerServiceDelegate {
    func didDenyLocationAuthorization(didShowPrompt: Bool) {
        if didShowPrompt {
            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.locationOS.tapped(),
                                           keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.locationOS,
                                                           AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.mayaStartPage,
                                                           AnalyticsAttributeKey.os: DeviceType.kind,
                                                           AnalyticsAttributeKey.button: AnalyticsConstants.ButtonText.deny])
        }

        delegate?.didFinishLocationPermission(self)
    }

    func didAcceptLocationAuthorization(didShowPrompt: Bool) {
        if didShowPrompt {
            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.locationOS.tapped(),
                                           keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.locationOS,
                                                           AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.mayaStartPage,
                                                           AnalyticsAttributeKey.os: DeviceType.kind,
                                                           AnalyticsAttributeKey.button: AnalyticsConstants.ButtonText.allow])
        }

        delegate?.didFinishLocationPermission(self)
    }

    func didNotDetermineLocationAuthorization() {
        if shouldShowBottomSheet {
            startLocationPermissionFlow()
        } else {
            startNativeLocationPermissionModalFlow()
        }
    }
}
