//
//  MayaMMACoordinator.swift
//  PayMaya
//
//  Created by <PERSON> on 5/2/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

protocol MayaMMACoordinatorDelegate: AnyObject {
    func coordinatorDidTapBack(_ coordinator: MayaMMACoordinator)
    func coordinatorDidTapContinue(_ coordinator: MayaMMACoordinator, mma: MMA)
}

class MayaMMACoordinator: Coordinator, DeepLinkHandler {
    private let mma: MMA
    private var shouldShowInterstitial: Bool { mma.shouldShowInterstitial }

    weak var delegate: MayaMMACoordinatorDelegate?

    init(mma: MMA, router: RouterProtocol = Router(), presenter: RouterProtocol? = nil) {
        self.mma = mma
        super.init(router: router, presenter: presenter)
    }

    override func start(completion: (() -> Void)? = nil) {
        if shouldShowInterstitial {
            showInterstitialScreen(completion: completion)
        } else {
            showMMAScreen(completion: completion)
        }
    }

    override func didTapBack(_ coordinator: Coordinator, completion: (() -> Void)? = nil) {
        router.dismissModule(animated: true) { [weak self] in
            completion?()
            guard let self else { return }
            remove<PERSON>hil<PERSON>(coordinator)
            delegate?.coordinatorDidTapBack(self)
        }
    }

    private func showInterstitialScreen(completion: (() -> Void)?) {
        guard let viewModel = MayaInterstitialViewModel(model: mma) else {
            return
        }
        let viewController = StoryboardScene.Home.mayaInterstitialViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.delegate = self
        viewController.bind(viewModel)
        setRootModule(viewController, animated: false)
        presenter?.present(router, animated: true, completion: completion)
    }

    private func showMMAScreen(completion: (() -> Void)?) {
        let module: WebViewParametersRequiringModule = .miniapp(code: mma.tpid, title: mma.title, category: "")

        let coordinator = WebViewCoordinator(router: router,
                                             presenter: presenter,
                                             webViewParametersRequiringModule: module,
                                             webViewConfiguration: mma.webviewConfiguration)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .dashboard(.pal), .dashboard(.pbb), .dashboard(.rafflePromo), .dashboard(.blackpinkGiveaway):
            return .opened(deepLink, nil)
        default:
            return .rejected(deepLink, .notHandled, identifier)
        }
    }
}

// MARK: MayaInterstitialViewControllerDelegate
extension MayaMMACoordinator: MayaInterstitialViewControllerDelegate {
    func viewControllerDidTapContinue(_ viewController: MayaInterstitialViewController) {
        delegate?.coordinatorDidTapContinue(self, mma: mma)
    }

    func viewController(_ viewController: MayaInterstitialViewController, didTapURL url: URL) {
        showWebView(link: url)
    }
}
