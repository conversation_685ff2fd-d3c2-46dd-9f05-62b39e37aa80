//
//  PaymayaDeepLinkRouter.swift
//  PayMaya
//
//  Created by Jazmine Barroga on 24/10/2019.
//  Copyright © 2019 PayMaya Philippines, Inc. All rights reserved.
//

import ConfigurationService
import Foundation
import Injector
import MayaCoreData
import StoreProvider

private let appLinkScheme = "paymaya"
private let appLinkHost = "www.paymaya.com"
private let mayaAppLinkHost = "www.maya.ph"
private let pwpHostRegex = "(test-|staging-|sandbox-|)assets.paymaya.com"
private let prefilledFieldsPrefix = "pf_"

protocol DeepLinkRouter {
    var rootDeepLinkHandler: DeepLinkHandler { get }

    func isPWPLink(_ url: URL) -> Bool
    func canOpenUrl(_ url: URL, resetToDashboard: Bool) -> Bool
    func open(url: URL) -> Bool

    func isShopDirectURL(_ url: URL) -> (productId: String, prefilledFields: [String: String]?)?
}

class PaymayaDeepLinkRouter: DeepLinkRouter {
    var rootDeepLinkHandler: DeepLinkHandler

    init(rootDeepLinkHandler: DeepLinkHandler) {
        self.rootDeepLinkHandler = rootDeepLinkHandler
    }

    /// Determines if url is the same format as what pwp handle url is expected.
    func isPWPLink(_ url: URL) -> Bool {
        guard let components = URLComponents(url: url, resolvingAgainstBaseURL: true),
              let regex = try? NSRegularExpression(pattern: pwpHostRegex),
              let host = components.host
        else {
            return false
        }

        let range = NSRange(location: 0, length: host.count)
        if regex.firstMatch(in: host, options: [], range: range) != nil,
           url.path == Constants.DeepLinkPath.pwp.appLinkPath {
            return true
        }
        return false
    }

    /// Determines if url is a direct shop deep link. Currently only used for features using web views (e.g. maya mall).
    /// Returns nil if deep link is a normal one.
    func isShopDirectURL(_ url: URL) -> (productId: String, prefilledFields: [String: String]?)? {
        guard let components = URLComponents(url: url, resolvingAgainstBaseURL: true) else { return nil }

        var path = ""
        if components.scheme == appLinkScheme {
            path = components.host ?? ""
        } else if let host = components.host,
                  host.contains("paymaya.com"),
                  components.path.starts(with: "/app") {
            path = String(components.path.dropFirst(5))
        } else {
            return nil
        }

        var parameters: [String: String]?
        if let query = components.queryItems {
            parameters = [:]
            query.forEach { queryItem in
                parameters?[queryItem.name] = queryItem.value
            }
        }

        guard let deepLinkPath = Constants.DeepLinkPath(rawValue: path) else { return nil }
        switch deepLinkPath {
        case .shop:
            guard let productID = parameters?["product"],
                  let mode = parameters?["mode"],
                  mode == "direct"
            else {
                return nil
            }

            var prefilledFields: [String: String]?
            if let prefilledFieldsWithPrefix = parameters?.filter({ return $0.key.hasPrefix(prefilledFieldsPrefix) }),
               !prefilledFieldsWithPrefix.isEmpty {
                var fields = [String: String]()
                for (key, value) in prefilledFieldsWithPrefix {
                    let cleanKey = String(key.dropFirst(prefilledFieldsPrefix.count))
                    fields[cleanKey] = value
                }
                prefilledFields = fields
            }

            return (productID, prefilledFields)
        default:
            return nil
        }
    }

    /// Determines if url is a supported deep link format.
    func canOpenUrl(_ url: URL, resetToDashboard: Bool = true) -> Bool {
        return deepLink(for: url, resetToDashboard: resetToDashboard) != nil
    }

    /// Opens deep link from root if supported.
    @discardableResult
    func open(url: URL) -> Bool {
        guard let deepLink = deepLink(for: url) else { return false }
        self.rootDeepLinkHandler.open(deepLink: deepLink) as Void?
        return true
    }

    private func deepLink(for url: URL, resetToDashboard: Bool = true) -> DeepLink? {
        guard let components = URLComponents(url: url, resolvingAgainstBaseURL: true) else { return nil }

        var path = ""
        if components.scheme == appLinkScheme {
            path = components.host ?? ""
            if !components.path.isEmpty {
                path += components.path
            }
        } else if components.host == appLinkHost, components.path.starts(with: "/app") {
            path = String(components.path.dropFirst(5))
        } else if components.host == mayaAppLinkHost, components.path.starts(with: "/app") {
            path = String(components.path.dropFirst(5))
        } else if isPWPLink(url) {
            path = String(components.path.dropFirst(5))
        } else {
            return nil
        }

        var parameters: [String: String]?
        if let query = components.queryItems {
            parameters = [:]
            query.forEach { queryItem in
                parameters?[queryItem.name] = queryItem.value
            }
        }

        if let route = routeFor(path: path, with: parameters, resetToDashboard: resetToDashboard) {
            return DeepLink(url: url, route: route)
        }
        return nil
    }

    private func routeFor(path: String, with params: [String: String]?, resetToDashboard: Bool = true) -> Route? {
        guard let path = Constants.DeepLinkPath(rawValue: path) else {
            return resetToDashboard ? .dashboard(.dashboard) : nil
        }

        switch path {
        case .chatWithUs:
            guard ContainerWrapper.shared.resolve(ConfigurationService.self).inboxSupportEnabled.value else { return .dashboard(.profile(.chatWithUs)) }
            let route = params?["route"] ?? "support"
            return .inbox(.inboxRoute(route), .inboxMessageTimestamp(""), .inboxChannelUrl(nil))
        case .more: return .services(nil)
        case .rafflePromo: return .dashboard(.rafflePromo)
        case .blackpinkGiveaway: return .dashboard(.blackpinkGiveaway)
        case .services:
            if let id = params?["id"] {
                let section = Param.ServicesSection(id: id)
                return .services(.servicesSection(section))
            }
            return .services(nil)
        case .pal: return .dashboard(.pal)
        case .paybills:
            if let params, params["biller"] != nil {
                return .dashboard(.payBills(.payment(.customFields(params))))
            } else if let category = params?["category"] {
                return .dashboard(.payBills(.category(.slug(category))))
            } else if let favorite = params?["favorite"] {
                return .dashboard(.payBills(.favorite(.favoriteId(favorite))))
            } else {
                return .dashboard(.payBills(.payment(nil)))
            }
        case .paybillsReceipt:
            if let billsPaymentId = params?["bills_payment_id"] {
                return .dashboard(.payBills(.receipt(.billsPaymentId(billsPaymentId))))
            } else {
                return .dashboard(.payBills(.receipt(nil)))
            }
        case .mycards: return .dashboard(.myCards(.myCards))
        case .mycardsLink: return .dashboard(.myCards(.link))
        case .mycardsVirtualView: return .dashboard(.myCards(.virtual))
        case .mycardsStore: return .dashboard(.myCards(.store))
        case .cardDashboardBenefits: return .dashboard(.myCards(.cardDashboardBenefits))
        case .cardDashboard: return .dashboard(.myCards(.cardDashboard))
        case .activateCreditCard: return .dashboard(.myCards(.activateCreditCard))
        case .creditCardTransactionReceipt: return .dashboard(.myCards(.creditCardTransactionReceipt))
        case .sendmoney:
            if let requestMoney = params?["p"] {
                return .dashboard(.sendMoney(.requestMoney(.requestMoneyDetails(requestMoney))))
            } else {
                return .dashboard(.sendMoney(.requestMoney(nil)))
            }
        case .banktransfer: return .dashboard(.bankTransfer)
        case .shop:
            // Category is used as property key but means technically the product type
            if let productType = params?["category"] {
                return .shop(.product(.type(productType), nil, nil))
            } else if let productID = params?["product"] {
                var modeParam: Param?
                if let mode = params?["mode"] {
                    modeParam = .mode(mode)
                }

                var prefilledFieldsParam: Param?
                if let fields = params?.filter({ return $0.key.hasPrefix(prefilledFieldsPrefix) }),
                   !fields.isEmpty {
                    var prefilledFields = [String: String]()
                    for (key, value) in fields {
                        let cleanKey = String(key.dropFirst(prefilledFieldsPrefix.count))
                        prefilledFields[cleanKey] = value
                    }
                    prefilledFieldsParam = .customFields(prefilledFields)
                }

                return .shop(.product(.productID(productID), modeParam, prefilledFieldsParam))
            } else if let keyword = params?["search"] {
                return .shop(.search(.keyword(keyword)))
            } else if let provider = params?["brand"] {
                return .shop(.provider(.slug(provider)))
            } else {
                return .shop(.product(nil, nil, nil))
            }
        case .addmoney:
            // Channel is used for readability
            if let slug = params?["channel"] {
                return .dashboard(.addMoney(.addMoney(.slug(slug))))
            } else {
                return .dashboard(.addMoney(.addMoney(nil)))
            }
        case .addmoneyCard: return .dashboard(.addMoney(.viaCard))
        case .pullfunds:
            guard ContainerWrapper.shared.resolve(ConfigurationService.self).bankPullFundsEnabled.value else { return .dashboard(.addMoney(.addMoney(nil))) }
            if let bank = params?["bank"] {
                return .dashboard(.addMoney(.pullFunds(.slug(bank))))
            } else {
                return .dashboard(.addMoney(.pullFunds(nil)))
            }
        case .refer: return .dashboard(.profile(.refer))
        case .accountLimits:
            return .dashboard(.profile(.accountLimits))
        case .inbox:
            if let route = params?["route"], let messageTimestamp = params?["messageTimestamp"], let channelUrl = params?["channelUrl"] {
                return .inbox(.inboxRoute(route), .inboxMessageTimestamp(messageTimestamp), .inboxChannelUrl(channelUrl))
            } else {
                return .inbox(.inboxRoute(""), .inboxMessageTimestamp(""), .inboxChannelUrl(""))
            }
        case .accountrecovery:
            return .dashboard(.profile(.settings(.accountRecovery)))
        case .upgrade: return .dashboard(.upgrade)
        case .missions:
            return routeForMissions(with: params)
        case .login: return .prelogin(.login)
        case .registration:
            if let inviteCode = params?["invite"] {
                MayaReferralInputCodeViewModel.setShouldPrefillReferralCode(shouldPrefill: true)
                MayaReferralInputCodeViewModel.saveReferralCodeForLater(referralCode: inviteCode)
            }
            return .prelogin(.registration)
        case .vouchers:
            return routeForVouchers(with: params)
        case .merchant:
            if let paymentId = params?["p"] {
                return .merchant(.merchantPaymentId(paymentId))
            } else {
                return .merchant(nil)
            }
        case .changeMobileNumber:
            return ContainerWrapper.shared.resolve(ConfigurationService.self).changeMinEnabled.value ? .dashboard(.profile(.settings(.changeMobileNumber))) : nil
        case .dashboard, .empty: return .dashboard(.dashboard)
        case .pwp:
            if let paymentId = params?["id"] {
                return .merchant(.merchantPaymentId(paymentId))
            } else {
                return .merchant(nil)
            }
        case .transport: return .dashboard(.transport(nil))
        case .shopPurchaseHistory:
            if let status = params?["status"] {
                return .shop(Route.Shop.purchaseHistory(.shopPurchaseHistoryStatus(status)))
            } else {
                return .shop(Route.Shop.purchaseHistory(nil))
            }
        case .quickguide: return .dashboard(.profile(.quickguide))
        case .publicName: return .dashboard(.profile(.settings(.publicName)))
        case .help: return .dashboard(.profile(.help))
        case .mayaMall: return .dashboard(.mayaMall)
        case .favorites:
            if let favoriteType = params?["type"] {
                return .dashboard(.favorites(.type(favoriteType)))
            } else {
                return .dashboard(.favorites(nil))
            }
        case .requestMoney: return .dashboard(.requestMoney)
        case .scanQr: return .scanQr(.scanQr)
        case .dataAndPersonalization: return .dashboard(.profile(.settings(.dataPersonalization)))
        case .protect: return .dashboard(.protect)
        case .moneycard:
            guard let designCardData = params?["data"] else { return nil }
            return .dashboard(.moneycard(.data(designCardData)))
        case .food:
            if let merchantId = params?["partner"] {
                return .dashboard(.food(.creatorStoreMerchantId(merchantId)))
            } else {
                return .dashboard(.food(nil))
            }
        case .retail:
            if let merchantId = params?["partner"] {
                return .dashboard(.retail(.creatorStoreMerchantId(merchantId)))
            } else {
                return .dashboard(.retail(nil))
            }
        case .government:
            if let merchantId = params?["partner"] {
                return .dashboard(.government(.creatorStoreMerchantId(merchantId)))
            } else {
                return .dashboard(.government(nil))
            }
        case .partnerMerchant: return .dashboard(.partnerMerchant)
        case .deals: return .dashboard(.deals)
        case .crypto:
            guard ContainerWrapper.shared.resolve(ConfigurationService.self).cryptoEnabled.value else { return nil }
            return .dashboard(.invest)
        case .cryptoV2:
            guard ContainerWrapper.shared.resolve(ConfigurationServiceV2.self).cryptoV2Enabled.value else { return nil }
            return .dashboard(.cryptoV2)
        case .mayaCredit:
            if let merchant = params?["partner"] {
                return .dashboard(.mayaCredit(.partnerMerchant(merchant)))
            }
            return .dashboard(.mayaCredit(nil))
        case .loansCreditCard:
            if ContainerWrapper.shared.resolve(ConfigurationService.self).loansCreditCardEnabled.value {
                return .loansCreditCard(.overview)
            }
            return nil
        case .loansCreditCardPending:
            if ContainerWrapper.shared.resolve(ConfigurationService.self).loansCreditCardEnabled.value {
                return .loansCreditCard(.pending)
            }
            return nil
        case .loansCreditCardAbout:
            if ContainerWrapper.shared.resolve(ConfigurationService.self).loansCreditCardEnabled.value {
                return .loansCreditCard(.about)
            }
            return nil
        case .loansCreditCardRepayment:
            if ContainerWrapper.shared.resolve(ConfigurationService.self).loansCreditCardEnabled.value {
                return .loansCreditCard(.repayment)
            }
            return nil
        case .loans: return .dashboard(.loans(.home))
        case .loansICL: return .dashboard(.loans(.icl))
        case .savings:
            if ContainerWrapper.shared.resolve(ConfigurationService.self).savingsEnabled.value {
                return .dashboard(.savings)
            }
            return nil
        case .mayaSavingsToWallet:
            if ContainerWrapper.shared.resolve(ConfigurationService.self).savingsEnabled.value {
                return .dashboard(.mayaSavingsToWallet)
            }
            return nil
        case .mayaStartSavings:
            if ContainerWrapper.shared.resolve(ConfigurationService.self).savingsEnabled.value {
                return .dashboard(.mayaStartSavings)
            }
            return nil
        case .donate: return .dashboard(.donate)
        case .submitInviteCode: return .dashboard(.submitInviteCode)
        case .funds:
            if ContainerWrapper.shared.resolve(ConfigurationService.self).fundsCreatorStoreEnabled.value {
                if let merchantId = params?["partner"] {
                    return .dashboard(.funds(.creatorStoreMerchantId(merchantId)))
                } else {
                    return .dashboard(.funds(nil))
                }
            } else if ContainerWrapper.shared.resolve(ConfigurationService.self).fundsEnabled.value {
                return .dashboard(.funds(nil))
            }
            return nil
        case .beep:
            return .dashboard(.transport(.beep))
        case .approvalRequests:
            return ContainerWrapper.shared.resolve(ConfigurationService.self).pushApprovalEnabled.value
            ? .dashboard(.profile(.approvalRequests))
            : .dashboard(.dashboard)
        case .insurance:
            if let merchantId = params?["partner"] {
                return .dashboard(.insurance(.creatorStoreMerchantId(merchantId)))
            } else {
                return .dashboard(.insurance(nil))
            }
        case .payLater:
            if let merchantId = params?["partner"] {
                return .dashboard(.payLater(.creatorStoreMerchantId(merchantId)))
            } else {
                return .dashboard(.payLater(nil))
            }
        case .pbb: return .dashboard(.pbb)
        case .merchantRewards:
            if let merchantId = params?["partner"] {
                return .dashboard(.merchantRewards(.creatorStoreMerchantId(merchantId)))
            } else {
                return .dashboard(.merchantRewards(nil))
            }
        case .stocks:
            if let merchantId = params?["partner"] {
                return .dashboard(.stocks(.creatorStoreMerchantId(merchantId)))
            } else {
                return .dashboard(.stocks(nil))
            }
        case .globalStocks:
            if let merchantId = params?["partner"] {
                return .dashboard(.globalStocks(.creatorStoreMerchantId(merchantId)))
            } else {
                return .dashboard(.globalStocks(nil))
            }
        case .stocksGame:
            if let merchantId = params?["partner"] {
                return .dashboard(.stocksGame(.creatorStoreMerchantId(merchantId)))
            } else {
                return .dashboard(.stocksGame(nil))
            }
        case .globalRemittance:
            if let merchantId = params?["partner"] {
                return .dashboard(.globalRemittance(.creatorStoreMerchantId(merchantId)))
            } else {
                return .dashboard(.globalRemittance(nil))
            }
        case .onboarding:
            guard ContainerWrapper.shared.resolve(ConfigurationService.self).onboardingRoutingEnabled.value else { return nil }

            @Inject var storeProvider: StoreProvider
            if let partner = params?["partner"],
               Constants.Partners.isPresent(rawValue: partner) {
                // MARK: Clear saved data first, then save
                if let userDefaults = storeProvider.target(UserDefaultsStore.self) {
                    userDefaults.remove(UserDefaultsStoreId.onboardingPartner, completion: nil)
                    userDefaults.remove(UserDefaultsStoreId.onboardingBranchCode, completion: nil)
                    userDefaults.write(partner, options: UserDefaultsStoreId.onboardingPartner)
                    if let branchCode = params?["branch"] {
                        userDefaults.write(branchCode, options: UserDefaultsStoreId.onboardingBranchCode)
                    }
                    userDefaults.write(true, options: UserDefaultsStoreId.shouldOnboardingEntryPoint)
                }

                if let databaseStore = storeProvider.target(DatabaseStore.self),
                   let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User {
                    if user.kycLevelValue == .zero {
                        if user.kycStatusValue == .submitted {
                            return .dashboard(.profile(.underReview))
                        } else if user.kycStatusValue == .forEdd {
                            return .dashboard(.dashboard)
                        } else {
                            return .dashboard(.upgrade)
                        }
                    } else if user.kycLevelValue == .one {
                        return .dashboard(.onboarding)
                    }
                }
            }

            // MARK: If no partner parameter, do nothing
            return nil

        case .luckyGames:
            if let merchantId = params?["partner"] {
                return .dashboard(.luckyGames(.creatorStoreMerchantId(merchantId)))
            } else {
                return .dashboard(.luckyGames(nil))
            }
        case .mayaAutoCashIn:
            return .dashboard(.mayaAutoCashIn)
        case .profile:
            return .dashboard(.profile(.profile))
        case .addlPersonalDetails:
            if let nextPayload = params?[nextPayloadKey], let backPayload = params?[backPayloadKey],
               URL(string: nextPayload) != nil, URL(string: backPayload) != nil {
                return .dashboard(.addlPersonalDetails(.profilePayload([nextPayloadKey: nextPayload, backPayloadKey: backPayload])))
            } else {
                return .dashboard(.addlPersonalDetails(nil))
            }
        case .contactReference:
            if let nextPayload = params?[nextPayloadKey], let backPayload = params?[backPayloadKey],
               URL(string: nextPayload) != nil, URL(string: backPayload) != nil {
                return .dashboard(.contactReference(.profilePayload([nextPayloadKey: nextPayload, backPayloadKey: backPayload])))
            } else {
                return .dashboard(.contactReference(nil))
            }
        case .workDetails:
            if let nextPayload = params?[nextPayloadKey], let backPayload = params?[backPayloadKey],
               URL(string: nextPayload) != nil, URL(string: backPayload) != nil {
                return .dashboard(.workDetails(.profilePayload([nextPayloadKey: nextPayload, backPayloadKey: backPayload])))
            } else {
                return .dashboard(.workDetails(nil))
            }
        case .load:
            if ContainerWrapper.shared.resolve(ConfigurationService.self).loadV2Enabled.value {
                return .dashboard(.load)
            }
            return nil
        case .esims:
            if let merchantId = params?["partner"] {
                return .dashboard(.esims(.creatorStoreMerchantId(merchantId)))
            } else {
                return .dashboard(.esims(nil))
            }
        case .stream:
            if let merchantId = params?["partner"] {
                return .dashboard(.stream(.creatorStoreMerchantId(merchantId)))
            } else {
                return .dashboard(.stream(nil))
            }
        case .sparkHackathon:
            if let merchantId = params?["partner"] {
                return .dashboard(.sparkHackathon(.creatorStoreMerchantId(merchantId)))
            } else {
                return .dashboard(.sparkHackathon(nil))
            }
        case .loginHistory:
            return .dashboard(.securityCenter(.loginHistory))
        case .securityCenter:
            return .dashboard(.securityCenter(.securityCenter))
        case .purchaseFinancing:
            return .dashboard(.purchaseFinancing)
        case .scheduler:
            if ContainerWrapper.shared.resolve(ConfigurationServiceV2.self).autoDebitArrangementSdkEnabled.value {
                return .dashboard(.scheduler)
            }
            return nil
        }
    }

    private func routeForVouchers(with params: [String: String]?) -> Route? {
        if let voucherDerivedId = params?["derivedId"], let campaignName = params?["campaignName"] {
            return .dashboard(.vouchers(.details(.voucherDerivedIdAndName(voucherDerivedId, campaignName))))
        }

        if let code = params?["code"] {
            return .dashboard(.vouchers(.details(.voucherCode(code))))
        }

        return .dashboard(.vouchers(.details(nil)))
    }

    private func routeForMissions(with params: [String: String]?) -> Route? {
        guard let campaignID = params?["campaignID"] else {
            return .dashboard(.missions(.mission))
        }

        var param: Param?
        if let taskID = params?["task"] {
            param = .taskID(taskID)
        }

        return .dashboard(.missions(.details(.campaignID(campaignID), param)))
    }
}
