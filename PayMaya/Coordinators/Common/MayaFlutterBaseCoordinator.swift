//
//  MayaFlutterBaseCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 10/11/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
import AssetProvider
import ConfigurationService
import Flutter
import FlutterPluginRegistrant
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider
import UIKit

class MayaFlutterBaseCoordinator: Coordinator, AnalyticsProtocol {
    @Inject var storeProvider: StoreProvider
    @Inject var flutterEngineManager: FlutterEngineManager

    let flutterModule: FlutterModule

    // Custom initial route, will default to `FlutterModule.initialRoute`
    let route: String?

    /// Custom parameters on the route
    let routeParams: String?

    /// Override to declare Flutter Toggles that will be retrieved by `.getToggles` method channel
    var flutterToggles: [FlutterToggles: CSConfig<Bool>] { [:] }

    /// Override to declare v2 Flutter Toggles that will be retrieved by `.getTogglesV2` method channel
    var flutterTogglesV2: [FlutterToggles: CSConfigV2<Bool>] { [:] }

    /// Override to declare Flutter Features Availability that will be retrieved by `.getAvailability` method channel
    var flutterAvailabilities: [FlutterAvailability] = []

    /// Override to determine whether the navigation is animated or not
    var animated: Bool = false

    /// Override to determine whether the router should be presented
    var shouldPresentRouter: Bool = false

    /// Override to determine whether for full or partial screens
    var isFullScreen: Bool = true

    private var sessionTimeoutResult: FlutterResult?

    var fullscreenViewController: FlutterViewController?

    private var initialRoute: String? {
        var initialRoute = route ?? flutterModule.initialRoute
        if let routeParams, !routeParams.isEmpty, initialRoute != nil {
            initialRoute? += "?\(routeParams)"
        }
        return initialRoute
    }

    private var databaseStore: DatabaseStore? {
       return storeProvider.target(DatabaseStore.self)
    }

    private var responseGenerator: FlutterResponseGenerator {
        return flutterEngineManager.responseGenerator
    }

    init(
        flutterModule: FlutterModule,
        flutterAvailabilities: [FlutterAvailability] = [],
        route: String? = nil,
        routeParams: String? = nil,
        router: RouterProtocol = Router(),
        presenter: RouterProtocol? = nil,
        animated: Bool = false,
        shouldPresentRouter: Bool = false,
        isFullScreen: Bool = true
    ) {
        self.flutterModule = flutterModule
        self.flutterAvailabilities = flutterAvailabilities
        self.route = route
        self.routeParams = routeParams
        self.animated = animated
        self.shouldPresentRouter = shouldPresentRouter
        self.isFullScreen = isFullScreen

        super.init(router: router, presenter: presenter)
    }

    override func start(completion: (() -> Void)? = nil) {
        if let engine = flutterEngineManager.getEngine(with: flutterModule) {
            flutterEngineManager.startEngine(with: flutterModule, customRoute: initialRoute)
            setMethodHandler()
            fullscreenViewController = FlutterViewController(engine: engine, nibName: nil, bundle: nil)
            if let controller = fullscreenViewController {
                if isFullScreen {
                    // set background to white for now
                    controller.view.backgroundColor = CommonAsset.MayaColors.Background.backgroundPrimaryWhite.color
                    if shouldPresentRouter {
                        setRootModule(controller, animated: false)
                        router.navigationController?.setNavigationBarHidden(true, animated: false)
                        presenter?.present(router, animated: animated, completion: completion)
                    } else {
                        presenter?.present(controller, animated: animated, completion: completion)
                    }
                } else {
                    let containerViewController = StoryboardScene.Others.flutterScrollViewControllerContainer.instantiate()
                    containerViewController.view.setNeedsLayout() // Needed to load the view
                    controller.willMove(toParent: containerViewController)
                    containerViewController.addChild(controller)
                    containerViewController.contentView.addSubview(controller.view)
                    controller.didMove(toParent: containerViewController)
                    controller.view.translatesAutoresizingMaskIntoConstraints = false
                    NSLayoutConstraint.enclose(view: controller.view, inside: containerViewController.contentView)
                    router.navigationController?.setNavigationBarHidden(true, animated: false)
                    setRootModule(containerViewController, animated: false)
                }
            }
        }
    }

    /// Override for custom logic when an SDK calls a `.complete` method channel
    /// This is usually called when an SDK completes a transaction
    func didComplete(_ coordinator: MayaFlutterBaseCoordinator) {
        // (Optional) Add default implementation
    }

    /// Override for custom logic when a dismiss behavior is triggered:
    /// - When the `.dismiss` method channel is called
    func didDismiss(_ coordinator: MayaFlutterBaseCoordinator) {
        // (Optional) Add default implementation
    }

    /// Override if a non-common method channel outside of `setMethodHandler` will be used.
    /// If used, its important to handle the default case of the method to return result(nil):
    /// ```
    /// override func setCustomMethodHandler(method: FlutterMethod, result: FlutterResult) {
    ///     switch method {
    ///     ...
    ///     // Return nil result as default
    ///     default: result(nil)
    ///     }
    /// }
    /// ```
    func setCustomMethodHandler(method: FlutterMethod, call: FlutterMethodCall, result: @escaping FlutterResult) {
        result(nil)
    }

    private func setMethodHandler() {
        flutterEngineManager.getChannel(with: flutterModule)?.setMethodCallHandler { [weak self] call, result in
            guard let self = self,
                  let method = FlutterMethod(rawValue: call.method)
            else { return result(nil) }

            switch method {
            case .getSessionToken:
                getSessionToken(result: result)
            case .getAccessToken:
                getAccessToken(result: result)
            case .getShieldSessionId:
                getShieldSessionId(result: result)
            case .onSessionTimeout:
                sessionTimeoutResult = result
                NotificationCenter.default.post(name: NSNotification.Name(rawValue: sessionTimeoutNotification), object: nil)
            case .complete:
                dismiss {
                    self.didComplete(self)
                }
            case .dismiss:
                dismiss {
                    self.didDismiss(self)
                }
            case .getPinningFingerprint:
                getPinningFingerprint(result: result)
            case .getToggle:
                getToggle(result: result)
            case .getAvailability:
                result(responseGenerator.getAvailability(from: flutterAvailabilities))
            case .getMin:
                getMin(result: result)
            case .getKycStatus:
                getKycStatus(result: result)
            case .getKycLevel:
                getKycLevel(result: result)
            case .showUpgradeScreen:
                showPreKYCFlow()
            case .logAnalyticsEvent:
                logAnalyticsEvent(methodCall: call)
            case .getUserInfo:
                getUserInfo(result: result)

            default:
                setCustomMethodHandler(method: method, call: call, result: result)
            }
        }
    }

    func getToggle(result: @escaping FlutterResult) {
        let togglesV1 = responseGenerator.getToggles(from: flutterToggles)
        let togglesV2 = responseGenerator.getTogglesV2(from: flutterTogglesV2)

        let mergedToggles = togglesV1.merged(with: togglesV2)

        result(mergedToggles)
    }

    func dismiss(completion: (() -> Void)? = nil) {
        self.presenter?.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }
            flutterEngineManager.getChannel(with: flutterModule)?.setMethodCallHandler(nil)
            flutterEngineManager.clearEngine(with: flutterModule)
            flutterEngineManager.initializeEngine(with: flutterModule)
            completion?()
        }
    }

    private func getSessionToken(result: FlutterResult) {
        guard let token = databaseStore?.fetchUserToken() else { return }

        result(responseGenerator.getSessionToken(token))
    }

    private func getAccessToken(result: FlutterResult) {
        guard let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
              let authorization = encryptedStore.read(EncryptedDefaultsStoreId.authorization, type: Authorization.self).value
        else { return }

        result(responseGenerator.getAccessToken(authorization.accessToken))
    }

    private func getShieldSessionId(result: FlutterResult) {
        result(responseGenerator.getShieldSessionId(Shield.shared().sessionId))
    }

    private func getPinningFingerprint(result: FlutterResult) {
        guard let propertyListStore = self.storeProvider.target(PropertyListStore.self),
              let pinningFingerprint = propertyListStore.read(PropertyListStoreId.pinningFingerprint).value as? String
        else { return }

        result(responseGenerator.getPinningFingerprint(pinningFingerprint))
    }

    private func getMin(result: FlutterResult) {
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
              let msisdn = user.msisdn
        else {
            return
        }

        result(responseGenerator.getMin(msisdn))
    }

    private func getKycStatus(result: FlutterResult) {
        if let databaseStore = self.storeProvider.target(DatabaseStore.self),
           let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User {
            result(responseGenerator.getKycStatus(kycStatus: user.kycStatus))
        } else {
            result(responseGenerator.getKycStatus(kycStatus: "kyc0"))
        }
    }

    private func getKycLevel(result: FlutterResult) {
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
              let kycLevel = user.kycLevel
        else { return }

        result(responseGenerator.getKycLevel(kycLevel: kycLevel))
    }

    private func getUserInfo(result: FlutterResult) {
        guard
            let databaseStore,
            let user = databaseStore.fetchUser()
        else {
            return
        }

        let response = responseGenerator.generateUserInfoFromProfile(user.profile)
        result(response)
    }

    private func logAnalyticsEvent(methodCall: FlutterMethodCall) {
        guard let jsonString = methodCall.arguments as? String,
              let jsonData = jsonString.data(using: .utf8),
              let dictionary = try? JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
              let name = dictionary["event_name"] as? String
        else { return }

        let attributes = dictionary["attributes"] as? [String: Any]
        self.analyticsUtils.logMayaEvents(name, attributes: attributes)
    }
}

// MARK: MayaPreKYCCoordinatorDelegate Methods
extension MayaFlutterBaseCoordinator: MayaPreKYCCoordinatorDelegate {
    private func showPreKYCFlow() {
        let coordinator = MayaPreKYCCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        coordinator.routeActionDelegate = self
        coordinator.analyticsModule = Analytics.LoansDashboard()
        addChild(coordinator)
        coordinator.start()
    }

    func preKYCCoordinatorDidFinishKYC(_ coordinator: MayaPreKYCCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
    }

    func preKYCCoordinatorDidTapBack(_ coordinator: MayaPreKYCCoordinator) {
        removeChild(coordinator)
    }
}
