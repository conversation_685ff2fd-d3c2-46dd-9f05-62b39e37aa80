//
//  Route.swift
//  PayMaya
//
//  Created by Jazmine <PERSON> on 27/10/2019.
//  Copyright © 2019 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation

enum Param {
    case billsPaymentId(String)
    case campaignID(String)
    case creatorStoreMerchantId(String)
    case customFields([String: String])
    case data(String)
    case favoriteId(String)
    case inboxChannelUrl(String?)
    case inboxMessageTimestamp(String)
    case inboxRoute(String)
    case keyword(String)
    case merchantPaymentId(String)
    case mode(String)
    case partnerMerchant(String)
    case productID(String)
    case profilePayload([String: String]?) // next, back, etc
    case requestMoneyDetails(String)
    case servicesSection(ServicesSection)
    case shopPurchaseHistoryStatus(String)
    case slug(String)
    case taskID(String)
    case type(String)
    case voucherCode(String)
    case voucherDerivedIdAndName(String, String) // Voucher Id saved in core data, campaign name.

    enum ServicesSection {
        case category(id: String)
        case moreActions
    }
}

enum Route {
    case dashboard(Route.Dashboard)
    case inbox(Param?, Param?, Param?)
    case loansCreditCard(Route.LoansCreditCard)
    case merchant(Param?)
    case prelogin(Route.PreLogin)
    case scanQr(Route.ScanQr)
    case services(Param?)
    case shop(Route.Shop)

    enum Dashboard {
        case addMoney(Route.Dashboard.AddMoney)
        case addlPersonalDetails(Param?)
        case bankTransfer
        case contactReference(Param?)
        case dashboard
        case deals
        case donate
        case esims(Param?)
        case favorites(Param?)
        case food(Param?)
        case funds(Param?)
        case globalRemittance(Param?)
        case globalStocks(Param?)
        case government(Param?)
        case insurance(Param?)
        case invest
        case cryptoV2
        case load
        case loans(Route.Dashboard.Loans)
        case luckyGames(Param?)
        case mayaAutoCashIn
        case mayaCredit(Param?)
        case mayaMall
        case mayaSavingsToWallet
        case mayaStartSavings
        case merchantRewards(Param?)
        case missions(Route.Dashboard.Missions)
        case moneycard(Param?)
        case myCards(Route.Dashboard.MyCards)
        case pal
        case partnerMerchant
        case payBills(Route.Dashboard.PayBills)
        case payLater(Param?)
        case pbb
        case profile(Route.Dashboard.Profile)
        case protect
        case purchaseFinancing
        case rafflePromo
        case requestMoney
        case retail(Param?)
        case savings
        case scheduler
        case securityCenter(Route.Dashboard.SecurityCenter)
        case sendMoney(Route.Dashboard.SendMoney)
        case sparkHackathon(Param?)
        case stocks(Param?)
        case stocksGame(Param?)
        case stream(Param?)
        case submitInviteCode
        case transport(Route.Dashboard.Transport?)
        case upgrade
        case vouchers(Route.Dashboard.Voucher)
        case workDetails(Param?)
        case onboarding
        case blackpinkGiveaway

        enum Loans {
            case home
            case icl
        }

        enum PayBills {
            case payment(Param?)
            case category(Param?)
            case favorite(Param?)
            case receipt(Param?)
        }

        enum AddMoney {
            case addMoney(Param?)
            case pullFunds(Param?)
            case viaCard
        }

        enum SendMoney {
            case requestMoney(Param?)
        }

        enum SecurityCenter {
            case securityCenter
            case loginHistory
        }

        enum Profile {
            case profile
            case chatWithUs
            case refer
            case accountLimits
            case settings(Route.Dashboard.Profile.Settings)
            case quickguide
            case help
            case approvalRequests
            case underReview

            enum Settings {
                case accountRecovery
                case changeMobileNumber
                case publicName
                case dataPersonalization
            }
        }

        enum Voucher {
            case details(Param?)
        }

        enum MyCards {
            case myCards
            case virtual
            case link
            case store
            case cardDashboardBenefits
            case cardDashboard
            case activateCreditCard
            case creditCardTransactionReceipt
        }

        enum Missions {
            case mission
            case details(Param?, Param?)
        }

        enum Transport {
            case beep
        }
    }

    enum PreLogin {
        case login
        case registration
    }

    enum Shop {
        case product(Param?, Param?, Param?)
        case purchaseHistory(Param?)
        case search(Param?)
        case provider(Param?)
    }

    enum ScanQr {
        case scanQr
    }

    enum LoansCreditCard {
        case overview
        case pending
        case about
        case repayment
    }
}

extension Param.ServicesSection {
    init(id: String) {
        switch id {
        case "more_actions": self = .moreActions
        default: self = .category(id: id)
        }
    }
}

extension Route.Dashboard {
    var creatorStore: CreatorStore? {
        switch self {
        case .esims: return .esims
        case .food: return .food
        case .funds: return .funds
        case .globalRemittance: return .globalRemittance
        case .globalStocks: return .globalStocks
        case .government: return .government
        case .insurance: return .insurance
        case .luckyGames: return .luckyGames
        case .merchantRewards: return .merchantRewards
        case .payLater: return .payLater
        case .retail: return .retail
        case .sparkHackathon: return .sparkHackathon
        case .stocks: return .stocks
        case .stocksGame: return .stocksGame
        case .stream: return .stream
        default: return nil
        }
    }
}
