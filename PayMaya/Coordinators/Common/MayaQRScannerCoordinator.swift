//
//  MayaQRScannerCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 24/03/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import ConfigurationService
import Foundation
import Injector
import UIKit

private enum CoordinatorAnalytics {
  static let scanReceipt = "SCAN_RECEIPT"
}

protocol MayaQRScannerCoordinatorDelegate: AnyObject {
    func didScanMerchantQr(_ coordinator: Coordinator, merchant: MerchantQR)
    func didScanSendMoneyQr(_ coordinator: Coordinator, transfer: SendMoneyQR)
    func didScanDynamicMerchantQrOldFlow(_ coordinator: Coordinator, dynamicMerchant: DynamicMerchantQR)
    func didScanSendMoneyToBankQr(_ coordinator: Coordinator, bankTransfer: BankTransferQRProtocol)
    func didScanOffUsMerchantQrOldFlow(_ coordinator: Coordinator, offUsMerchant: OffUSQRPHMerchant)
    func didScanQRPHMerchantQr(_ coordinator: Coordinator, qrphMerchant: QRPHMerchant)
    func didRequestRedirectToDashboard(_ coordinator: MayaQRScannerCoordinator)
}

class MayaQRScannerCoordinator: Coordinator, MayaAnalyticsDurationManagerProtocol {
    @Inject private var configurationService: ConfigurationService
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    let scanType: CodeType
    let isAnimated: Bool
    let analyticsSourcePage: AnalyticsConstants.SourcePage

    weak var delegate: MayaQRScannerCoordinatorDelegate?

    var offUsMerchant: OffUSQRPHMerchant?
    var qrphMerchant: QRPHMerchant?

    private var mayaReceiptViewModel: MayaReceiptViewModelProtocol?
    private var viewModel: QRScannerViewModelProtocol?

    init(
        presenter: RouterProtocol? = nil,
        scanType: CodeType = .any,
        isAnimated: Bool = true,
        analyticsSourcePage: AnalyticsConstants.SourcePage
    ) {
        self.scanType = scanType
        self.isAnimated = isAnimated
        self.analyticsSourcePage = analyticsSourcePage
        super.init(presenter: presenter)
    }

    override func start(completion: (() -> Void)? = nil) {
        let dataModel = MayaCameraPermissionDataModel(title: L10n.Dashboard.Service.Title.scanqr)
        let coordinator = MayaCameraPermissionCoordinator(router: router, presenter: presenter, dataModel: dataModel)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func showPreKYCFlow(completion: (() -> Void)? = nil) {
        let coordinator = MayaPreKYCCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    // MARK: - BackActionDelegate
    override func didTapBack(_ coordinator: Coordinator, completion: (() -> Void)? = nil) {
        if coordinator == self {
            backActionDelegate?.didTapBack(coordinator, completion: completion)
        } else {
            super.didTapBack(coordinator, completion: completion)
        }
    }
}

// MARK: Utility Methods
private extension MayaQRScannerCoordinator {
    func showMayaReceipt(viewModel: MayaReceiptViewModelProtocol) {
        let coordinator = MayaReceiptCoordinator(presenter: router, viewModel: viewModel, attributeModule: .qrPay, customAnalyticsModule: CoordinatorAnalytics.scanReceipt)
        mayaReceiptViewModel = viewModel
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func showQRScannerFlow(completion: (() -> Void)?) {
        viewModel = QRScannerViewModel(scanType: scanType)
        let viewController = StoryboardScene.Others.mayaQRScannerViewController.instantiate()
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.analyticsSourcePage = analyticsSourcePage
        viewController.bind(viewModel)
        if !isAnimated {
            viewController.modalTransitionStyle = .crossDissolve
        }
        setRootModule(viewController, animated: false)
        if presenter?.toPresentable().presentedViewController == nil {
            presenter?.present(router, animated: true, completion: completion)
        }
    }
}

// MARK: DeepLinkHandler Methods
extension MayaQRScannerCoordinator: DeepLinkHandler {
    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .scanQr:
            return .opened(deepLink, nil)
        default:
            return .rejected(deepLink, .notHandled, identifier)
        }
    }
}

// MARK: CameraPermissionCoordinatorDelegate Methods
extension MayaQRScannerCoordinator: CameraPermissionCoordinatorDelegate {
    func cameraPermissionDidAccept(_ coordinator: CameraPermissionCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
        showQRScannerFlow(completion: completion)
    }
}

// MARK: MayaCameraPermissionCoordinatorDelegate Methods
extension MayaQRScannerCoordinator: MayaCameraPermissionCoordinatorDelegate {
    func cameraPermissionDidAccept(_ coordinator: MayaCameraPermissionCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
        showQRScannerFlow(completion: completion)
    }
}

// MARK: MayaQRScannerViewControllerDelegate Methods
extension MayaQRScannerCoordinator: MayaQRScannerViewControllerDelegate {
    func didScanMerchantQr(_ viewController: MayaQRScannerViewController, merchant: MerchantQR) {
        delegate?.didScanMerchantQr(self, merchant: merchant)
    }

    func didScanSendMoneyQr(_ viewController: MayaQRScannerViewController, transfer: SendMoneyQR) {
        delegate?.didScanSendMoneyQr(self, transfer: transfer)
    }

    func didScanDynamicMerchantQrOldFlow(_ viewController: MayaQRScannerViewController, dynamicMerchant: DynamicMerchantQR) {
        delegate?.didScanDynamicMerchantQrOldFlow(self, dynamicMerchant: dynamicMerchant)
    }

    func didScanSendMoneyToBankQr(_ viewController: MayaQRScannerViewController, bankTransfer: BankTransferQRProtocol) {
        delegate?.didScanSendMoneyToBankQr(self, bankTransfer: bankTransfer)
    }

    func didScanOffUsMerchantQrOldFlow(_ viewController: MayaQRScannerViewController, offUsMerchant: OffUSQRPHMerchant) {
        self.offUsMerchant = offUsMerchant
        showPreKYCFlow()
    }

    func didScanQRPHMerchantQr(_ viewController: MayaQRScannerViewController, qrphMerchant: QRPHMerchant) {
        if qrphMerchant.isOnUsQr {
            delegate?.didScanQRPHMerchantQr(self, qrphMerchant: qrphMerchant)
        } else {
            self.qrphMerchant = qrphMerchant
            showPreKYCFlow()
        }
    }

    func didCreateMerchantPayment(_ viewController: MayaQRScannerViewController, createdMerchantPayment: CreatedMerchantPaymentV2) {
        let viewModel = MayaP2MCheckoutViewModel(createdMerchantPayment: createdMerchantPayment)
        let checkoutCoordinator = MayaP2MCheckoutCoordinator(presenter: router, viewModel: viewModel)
        checkoutCoordinator.backActionDelegate = self
        checkoutCoordinator.routeActionDelegate = self
        checkoutCoordinator.delegate = self
        addChild(checkoutCoordinator)
        checkoutCoordinator.start()
    }

    func didTapShowMyQR(_ viewController: MayaQRScannerViewController) {
        let coordinator = MayaRequestMoneyCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func showMaintenanceModeOffUsMerchantQR() {
        let mayaServiceMaintenanceViewModel: MayaServiceMaintenanceViewModel = MayaServiceMaintenanceViewModel(
            hidesBackButton: false,
            screenTitle: L10n.P2m.Maintenance.screenTitle,
            maintenanceImage: AssetProvider.CommonAsset.Images.Alert.imageMaintenance.image,
            title: L10n.P2m.Maintenance.title,
            message: L10n.P2m.Maintenance.message,
            actionButtonTitle: nil,
            analyticsModule: nil
            )

        var maintenanceViewController = StoryboardScene.Others.mayaServiceMaintenanceViewController.instantiate()
        maintenanceViewController.backActionDelegate = self
        maintenanceViewController.routeActionDelegate = self
        maintenanceViewController.bind(mayaServiceMaintenanceViewModel)

        router.push(maintenanceViewController, animated: true, popCompletion: nil)
    }
}

// MARK: MayaPreKYCCoordinatorDelegate Methods
extension MayaQRScannerCoordinator: MayaPreKYCCoordinatorDelegate {
    func preKYCCoordinatorDidFinishKYC(_ coordinator: MayaPreKYCCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
        if viewModel?.isQrWithCheckoutFlowEnabled ?? false {
            guard let qrphMerchant = qrphMerchant else { return }
            delegate?.didScanQRPHMerchantQr(self, qrphMerchant: qrphMerchant)
        } else {
            guard let offUsMerchant = offUsMerchant else { return }
            delegate?.didScanOffUsMerchantQrOldFlow(self, offUsMerchant: offUsMerchant)
        }
    }

    func preKYCCoordinatorDidTapBack(_ coordinator: MayaPreKYCCoordinator) {
        coordinator.getTopViewController()?.dismiss(animated: true, completion: { [weak self] in
            self?.removeChild(coordinator)
        })
    }
}

// MARK: MayaReceiptCoordinatorDelegate Methods
extension MayaQRScannerCoordinator: MayaReceiptCoordinatorDelegate {
    func didTapAddToFavorites(_ coordinator: MayaReceiptCoordinator) {
        // Do nothing
    }

    func didTapClose(_ coordinator: MayaReceiptCoordinator) {
        if let mayaReceiptViewModel = mayaReceiptViewModel as? MayaP2MSuccessReceiptViewModel,
            let redirectURL = mayaReceiptViewModel.customRedirectURL,
            let dashboardURL = Constants.DeepLinkPath.dashboard.url {
            routeActionDelegate?.didRequestStartDeepLink(self, with: dashboardURL, completion: { _ in
                showExternalURI(redirectURL)
            })
        } else {
            removeChild(coordinator)
            delegate?.didRequestRedirectToDashboard(self)
        }
    }
}

// MARK: MayaP2MCheckoutCoordinatorDelegate Methods
extension MayaQRScannerCoordinator: MayaP2MCheckoutCoordinatorDelegate {
    func didComplete(_ coordinator: MayaP2MCheckoutCoordinator, mayaReceiptViewModel: MayaReceiptViewModelProtocol) {
        removeChild(coordinator)
        showMayaReceipt(viewModel: mayaReceiptViewModel)
    }

    func didCompleteNoReceipt(_ coordinator: MayaP2MCheckoutCoordinator) {
        self.removeChild(coordinator)

        guard let dashboardURL = Constants.DeepLinkPath.dashboard.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: dashboardURL, completion: nil)
    }

    func didDismiss(_ coordinator: MayaP2MCheckoutCoordinator, error: PayMayaError?, needsKYCUpgrade: Bool) {
        removeChild(coordinator)
        if needsKYCUpgrade {
            showPreKYCFlow()
            return
        }

        if let errorViewModel = error?.viewModel {
            showErrorAlert(errorViewModel, useMayaModal: true) {
                self.viewModel?.restartCameraScanAction?.apply().start()
            }
        } else if error == nil {
            viewModel?.restartCameraScanAction?.apply().start()
        }
    }
}
