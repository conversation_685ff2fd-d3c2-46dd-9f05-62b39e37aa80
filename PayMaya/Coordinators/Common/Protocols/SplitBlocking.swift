//
//  SplitBlocking.swift
//  PayMaya
//
//  Created by <PERSON> on 6/24/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import ConfigurationService
import ReactiveSwift

protocol SplitBlocking: Coordinator {
    var configurationServiceV2: ConfigurationServiceV2 { get }

    func didFinishBlocking(completion: (() -> Void)?)
    func startSplitBlockingFlow(for type: MayaToggle.TrafficType, completion: (() -> Void)?)
}

extension SplitBlocking {
    func startSplitBlockingFlow(for type: MayaToggle.TrafficType, completion: (() -> Void)?) {
        let eventProperty = configurationServiceV2.getEventProperty(trafficType: type)
        let currentEvent = eventProperty.value

        guard currentEvent != .ready && currentEvent != .readyTimedOut else {
            didFinishBlocking(completion: completion)
            return
        }

        let viewController = StoryboardScene.Main.mayaInitialViewController.instantiate()
        let viewModel = MayaInitialViewModel()
        viewController.bind(viewModel)

        let presenter = presenter ?? router
        presenter.present(viewController, animated: false) { [weak self] in
            guard let self else { return }

            eventProperty.producer
                .observe(on: UIScheduler())
                .skip { $0 != .ready && $0 != .readyTimedOut }
                .take(first: 1)
                .startWithValues { event in
                    if event == .ready || event == .readyTimedOut {
                        presenter.dismissModule(animated: false) {
                            self.didFinishBlocking(completion: completion)
                        }
                    }
                }.addToDisposeBag(disposeBag)
        }
    }
}
