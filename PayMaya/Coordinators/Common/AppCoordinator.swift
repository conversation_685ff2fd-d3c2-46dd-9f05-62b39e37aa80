//
//  AppCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 7/25/18.
//  Copyright © 2018 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
import ConfigurationService
import FirebaseAnalytics
import Injector
import MayaCoreData
import ReactiveSwift
import StoreProvider
import UIKit
class AppCoordinator: Coordinator, AnalyticsProtocol, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject private var storeProvider: StoreProvider
    @Inject private var appThemeService: AppThemeService
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2
    @Inject private var ravenWrapper: RavenWrapper

    private var deepLinkRouter: PaymayaDeepLinkRouter!

    init() {
        super.init()
        router.navigationController?.isNavigationBarHidden = true
        let paymayaDeepLinkRouter = PaymayaDeepLinkRouter(rootDeep<PERSON>inkHandler: self)
        ContainerWrapper.shared.register(type: PaymayaDeepLinkRouter.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return paymayaDeepLinkRouter
        })
        ContainerWrapper.shared.synchronize()
        deepLinkRouter = paymayaDeepLinkRouter

        setupForegroundHandling()
        setupUpdaterHandling()
        setupMaintenanceHandling()
        setupShopHandling()
    }

    override func startDeepLinkFlow(url: URL) -> Bool {
        startDeepLinkFlow(url: url, attributes: nil)
    }

    override func startDeepLinkFlow(url: URL, attributes: [AnalyticsAttributeKey: Any]?) -> Bool {
        let attributes = attributes ?? [.url: url.absoluteString]
        analyticsUtils.logAllEvents(.tap(Analytics.DeepLink.link), attributes: attributes)
        if deepLinkRouter.canOpenUrl(url) {
            return deepLinkRouter.open(url: url)
        }
        return false
    }

    override func start() {
        if let encryptedDefaultsStore = storeProvider.target(EncryptedDefaultsStore.self), encryptedDefaultsStore.read(EncryptedDefaultsStoreId.prominentDisclosureWasShown, type: ShownConfig.self).error != nil || !((encryptedDefaultsStore.read(EncryptedDefaultsStoreId.prominentDisclosureWasShown, type: ShownConfig.self).value)?.hasBeenShown ?? false) {
            setupMayaInitialViewController(canShowHud: false)
            startProminentDisclosureFlow()
        } else {
            setupExtraction()
            setupMayaInitialViewController()
            startMainFlow()
        }
        analyticsService.logMayaEvents(.start(Analytics.AppStart.launch))
    }

    override func didTapBack(_ coordinator: Coordinator, completion: (() -> Void)? = nil) {
        router.dismissModule(animated: true, completion: { [weak self] in
            completion?()
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.start()
        })
    }

    override func didTapBack(_ viewController: UIViewController, completion: (() -> Void)? = nil) {
        if viewController is MayaWKWebViewController {
            self.startLandingScreenFlow()
            completion?()
        } else {
            super.didTapBack(viewController, completion: completion)
        }
    }

    override func didRequestStartDeepLink(_ coordinator: Coordinator, with url: URL, completion: ((Bool) -> Void)?) {
        didRequestStartDeepLink(coordinator, with: url, attributes: nil, completion: completion)
    }

    override func didRequestStartDeepLink(_ coordinator: Coordinator, with url: URL, attributes: [AnalyticsAttributeKey: Any]?, completion: ((Bool) -> Void)?) {
        guard deepLinkRouter.canOpenUrl(url) else {
            completion?(false)
            return
        }
        completion?(true)
        _ = startDeepLinkFlow(url: url, attributes: attributes)
    }

    override func didSignout(_ coordinator: Coordinator, with viewModel: ErrorAlertViewModel?, signoutRoute: SignoutRoute, performAction: Bool, showAlert: Bool) {
        dispatchToMain { [weak self] in
            guard let self = self else { return }
            self.router.dismissModule(animated: false) { [weak coordinator] in
                guard let coordinator = coordinator else { return }
                self.removeChild(coordinator)
                let completion: ((Coordinator) -> Void) = { presentingCoordinator in
                    guard let viewModel = viewModel else { return }
                    if performAction {
                        if let actionURL = viewModel.primaryActionURL,
                           let url = URL(string: actionURL) {
                            let isExternal = viewModel.isActionUrlOpenedExternally ?? false
                            if isExternal {
                                showExternalURI(url)
                            } else {
                                presentingCoordinator.showWebView(link: url, willPresent: false)
                            }
                        }
                    } else if showAlert {
                        if self.configurationService.forgotPasswordV2Enabled.value &&
                           viewModel.error?.code == APIConstants.APIStatusCode.loginMaxAttempts.rawValue {
                            self.showResetPasswordAlert(presentingCoordinator, errorViewModel: viewModel)
                            return
                        }
                        presentingCoordinator.showErrorAlert(viewModel, useMayaModal: true)
                    }
                }

                switch signoutRoute {
                case .landing:
                    self.startLandingScreenFlow(completion: completion)
                case .login:
                    self.startLoginScreenFlow(completion: completion)
                }
            }
        }
    }

    func getTopCoordinator() -> Coordinator {
        var topCoordinator: Coordinator = self
        while let coordinator = topCoordinator.childCoordinators.first {
            topCoordinator = coordinator
        }
        return topCoordinator
    }
}

// MARK: DeepLinkHandler methods
extension AppCoordinator: DeepLinkHandler {
    func open(deepLink: DeepLink) -> DeepLinkHandling {
        if getChildCoordinator(identifier: String(describing: MayaMaintenanceCoordinator.self)) != nil || getChildCoordinator(identifier: String(describing: UpdaterCoordinator.self)) != nil {
            return .rejected(deepLink, .notHandled, identifier)
        }

        switch deepLink.route {
        case .prelogin(let prelogin):
            guard getChildCoordinator(identifier: String(describing: MayaMainTabCoordinator.self)) == nil,
                  getChildCoordinator(identifier: String(describing: MayaSessionTimeoutCoordinator.self)) == nil
            else {
                return .rejected(deepLink, .notHandled, identifier)
            }

            router.dismissModule(animated: true, completion: { [weak self] in
                guard let self = self else { return }
                self.removeAllChild()
                switch prelogin {
                case .login:
                    self.startLoginScreenFlow()
                case .registration:
                    self.startRegistrationScreenFlow()
                }
            })
            return .opened(deepLink, nil)
        default:
            guard let mainTabCoordinator = getChildCoordinator(identifier: String(describing: MayaMainTabCoordinator.self)) as? MayaMainTabCoordinator,
                  mainTabCoordinator.getChildCoordinator(identifier: String(describing: MayaSessionTimeoutCoordinator.self)) == nil
            else {
                return .delayed(deepLink, identifier, nil)
            }

            return .passedThrough(deepLink, mainTabCoordinator)
        }
    }
}

// MARK: AuthenticationCoordinatorDelegate Methods
extension AppCoordinator: AuthenticationCoordinatorDelegate {
    func didSuccessfullyAuthenticate(_ coordinator: Coordinator, response: Codable?) {
        dispatchToMain { [weak self] in
            guard let self = self else { return }
            self.router.dismissModule(animated: true) {
                self.removeAllChild()
                self.startLoggedInUserFlow()
                self.sendWalletIdToFirebaseAnalytics()
                self.complete(deepLinkHandling: self.deepLinkHandling, error: nil)
            }
        }
    }
}

// MARK: MayaMainTabCoordinatorDelegate Methods
extension AppCoordinator: MayaMainTabCoordinatorDelegate {
    func didSuccessfullyAuthenticate(_ coordinator: MayaMainTabCoordinator) {
        complete(deepLinkHandling: deepLinkHandling, error: nil)
    }

    func didSwitchAccount(_ coordinator: MayaMainTabCoordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.startLoginScreenFlow()
        })
    }
}

// MARK: MayaWKWebViewControllerDelegate Methods
extension AppCoordinator: MayaWKWebViewControllerDelegate {
    func didFinishWebView(_ viewController: ViewController, isSuccessful: Bool, response: Codable?) {
        router.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }
            let successAlertViewModel = SuccessAlertViewModel(title: L10n.Forgot.Password.success, closeCompletion: {
                self.startLandingScreenFlow()
            })
            self.router.toPresentable().showSuccessAlert(successAlertViewModel)
        }
    }

    func didCancelWebView(_ viewController: ViewController) {
        // do nothing
    }
}

// MARK: MayaProminentDisclosureCoordinatorDelegate methods
extension AppCoordinator: MayaProminentDisclosureCoordinatorDelegate {
    func didAgreeAndContinue(_ coordinator: MayaProminentDisclosureCoordinator) {
        setupExtraction()

        if let initialViewController = router.rootViewController as? MayaInitialViewController {
            initialViewController.canShowHud = true
        }

        router.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.startMainFlow(analyticsSourcePage: AnalyticsConstants.SourcePage.prominentDisclosure)
        }
    }
}

// MARK: MayaSessionTimeoutCoordinatorDelegate methods
extension AppCoordinator: MayaSessionTimeoutCoordinatorDelegate {
    func didSwitchAccount(_ coordinator: MayaSessionTimeoutCoordinator) {
        dispatchToMain { [weak self] in
            guard let self = self else { return }
            coordinator.router.dismissRootModule(animated: true) {
                self.removeAllChild()
                self.startLoginScreenFlow()
            }
        }
    }

    func didSuccessfullyAuthenticate(_ coordinator: MayaSessionTimeoutCoordinator, completion: (() -> Void)?) {
        dispatchToMain { [weak self] in
            guard let self = self else { return }
            coordinator.router.dismissRootModule(animated: true) {
                completion?()
                self.removeAllChild()
                self.startLoggedInUserFlow()
                self.sendWalletIdToFirebaseAnalytics()
                self.complete(deepLinkHandling: self.deepLinkHandling, error: nil)
            }
        }
    }
}

// MARK: Toggle, updater and maintenance handling methods
private extension AppCoordinator {
    func setupForegroundHandling() {
        NotificationCenter.default.reactive.notifications(forName: UIApplication.willEnterForegroundNotification)
            .observeValues { [weak self] _ in
                guard let self = self else { return }
                self.applicationEnterForeground()
            }?.addToDisposeBag(disposeBag)
    }

    func setupUpdaterHandling() {
        let updaterService = UpdaterService(storeProvider: storeProvider)
        #if !DEBUG
        let checkUpdateSignalProducer = updaterService.checkUpdateSignalProducer.on(value: { [weak self] hasUpdate in
            guard let self, hasUpdate else {
                return
            }

            self.router.dismissModule(animated: false) {
                self.removeAllChild()
                self.startUpdaterFlow()
            }
        })

        checkUpdateSignalProducer.start()
        #endif
    }

    func setupMaintenanceHandling() {
        var maintenanceCoordinator: Coordinator? { getChildCoordinator(identifier: String(describing: MayaMaintenanceCoordinator.self)) }
        var updaterCoordinator: Coordinator? { getChildCoordinator(identifier: String(describing: UpdaterCoordinator.self)) }
        var mainTabCoordinator: Coordinator? { getChildCoordinator(identifier: String(describing: MayaMainTabCoordinator.self)) }
        var isAppMaintenanceEnabled: Bool { configurationService.isAppMaintenanceEnabled.value }
        var isMaintenanceBypassEnabled: Bool { configurationServiceV2.maintenanceBypassEnabled.value }

        let handleAppLaunchMaintenance: () -> Void = { [weak self] in
            guard let self,
                !isMaintenanceBypassEnabled,
                isAppMaintenanceEnabled,
                updaterCoordinator == nil,
                maintenanceCoordinator == nil
            else {
                return
            }

            router.dismissModule(animated: false) {
                self.removeAllChild()
                self.startMaintenanceFlow()
            }
        }

        let handleRealtimeMaintenance: () -> Void = { [weak self] in
            guard let self, !isMaintenanceBypassEnabled else {
                return
            }

            let isUserLoggedIn = mainTabCoordinator != nil

            if isAppMaintenanceEnabled,
                maintenanceCoordinator == nil {
                // Preserve current flow if user is logged in
                if isUserLoggedIn {
                    startMaintenanceFlow()
                } else {
                    guard updaterCoordinator == nil else { return }
                    router.dismissModule(animated: false) {
                        self.removeAllChild()
                        self.startMaintenanceFlow()
                    }
                }
            } else if let maintenanceCoordinator {
                maintenanceCoordinator.router.dismissRootModule(animated: true) {
                    self.removeChild(maintenanceCoordinator)

                    if isUserLoggedIn {
                        // Show session timeout only if session is not suspended (e.g. web view)
                        if let timerApplication = UIApplication.shared as? TimerUIApplication,
                           timerApplication.isIdleTimerEnabled {
                            NotificationCenter.default.post(name: NSNotification.Name(rawValue: sessionTimeoutNotification), object: nil)
                        }
                    } else {
                        self.start()
                    }
                }
            }
        }

        // Success or fail, we listen to real time updates
        Signal.merge(configurationService.getRemoteConfigAction.completed, configurationService.getRemoteConfigAction.errors.map { _ in })
            .observe(on: UIScheduler())
            .observeValues { [weak self] in
                self?.configurationService.startRealtimeRemoteConfigListener()
            }?.addToDisposeBag(disposeBag)

        NotificationCenter.default.reactive.notifications(forName: Notification.Name.appLaunchMaintenance)
            .observe(on: UIScheduler())
            .observeValues { _ in
                handleAppLaunchMaintenance()
            }?.addToDisposeBag(disposeBag)

        NotificationCenter.default.reactive.notifications(forName: Notification.Name.realTimeMaintenance)
            .observe(on: UIScheduler())
            .observeValues { _ in
                handleRealtimeMaintenance()
            }?.addToDisposeBag(disposeBag)

        configurationService.getRemoteConfigAction.apply().start()
        handleAppLaunchMaintenance()
    }

    func setupShopHandling() {
        let saveShopConfig: () -> Void = { [weak self] in
            guard let self = self,
                let databaseStore = self.storeProvider.target(DatabaseStore.self),
                let shopConfig = self.configurationService.shop else {
                return
            }

            if let currentProductTypes = databaseStore.fetch(DatabaseStore.ReadingOptions(DatabaseEntity.productType)).value as? [ProductType],
                !currentProductTypes.isEmpty {
                let storedProductTypes = currentProductTypes.map { $0.name }
                let forDeletion = storedProductTypes.filter { !shopConfig.defaultProductTypes.contains($0) }
                if !forDeletion.isEmpty {
                    let deletePredicate = NSPredicate(format: "name IN %@", forDeletion)
                    databaseStore.remove(DatabaseStore.ReadingOptions(DatabaseEntity.productType, predicate: deletePredicate), completion: { result in
                        if let error = result.error {
                            print("Error in deleting configuration : %@", error)
                        }
                    })
                }
            }

            if let currentCategory = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.productCategory)).value as? ProductCategory,
                currentCategory.name != shopConfig.defaultCategory {
                databaseStore.remove(DatabaseStore.ReadingOptions(DatabaseEntity.productCategory), completion: { result in
                    if let error = result.error {
                        print("Error in deleting configuration : %@", error)
                    }
                })
            }

            var displayOrder = 0
            let productTypesDictionaries = shopConfig.defaultProductTypes.map { type -> [String: Any] in
                displayOrder += 1
                return [Constants.Keys.Shop.displayOrder.rawValue: displayOrder, Constants.Keys.Common.nameKey.rawValue: type, Constants.Keys.Shop.category.rawValue: shopConfig.defaultCategory]
            }
            let categoryDictionary: [String: Any] = [Constants.Keys.Common.nameKey.rawValue: shopConfig.defaultCategory]

            databaseStore.write(categoryDictionary as NSDictionary, options: DatabaseStore.WritingOptions(DatabaseEntity.productCategory))
            databaseStore.write(productTypesDictionaries as NSArray, options: DatabaseStore.WritingOptions(DatabaseEntity.productType))
        }

        configurationService.getConfigurationAction.values
            .observeValues { _ in
                saveShopConfig()
            }?.addToDisposeBag(disposeBag)
    }

    func setupExtraction() {
        let handleExtraction: () -> Void = { [weak self] in
            guard let self, configurationServiceV2.ddpLauncherEnabled.value  else { return }

            ravenWrapper.extract(
                customerID: nil,
                transactionID: nil,
                triggeredBy: "base-app",
                eventTrigger: "app-launch"
            ) { _ in }
        }

        configurationServiceV2.getEventProperty(trafficType: .anonymous).producer
            .skip(while: { $0 != .ready })
            .take(first: 1)
            .startWithValues { _ in
                handleExtraction()
            }.addToDisposeBag(disposeBag)
    }
}

// MARK: Private show and start coordinator methods
private extension AppCoordinator {
    func setupMayaInitialViewController(canShowHud: Bool = true) {
        let initialViewController = StoryboardScene.Main.mayaInitialViewController.instantiate()
        initialViewController.bind(MayaInitialViewModel())
        initialViewController.canShowHud = canShowHud
        setRootModule(initialViewController, animated: true)
    }

    func startMainFlow(analyticsSourcePage: AnalyticsConstants.SourcePage? = nil) {
        let completion: () -> Void = { [weak self] in
            self?.appThemeService.isLoaded = true
        }

        if shouldShowLandingScreen() {
            startLandingScreenFlow(analyticsSourcePage: analyticsSourcePage) { _ in
                completion()
            }
        } else {
            startExpiredSessionFlow(completion: completion)
        }

        getConfiguration()
    }

    func startLandingScreenFlow(analyticsSourcePage: AnalyticsConstants.SourcePage? = nil,
                                completion: ((Coordinator) -> Void)? = nil) {
        let landingCoordinator = MayaLandingCoordinator(presenter: router)
        let customCompletion: (() -> Void) = {
            completion?(landingCoordinator)
        }
        landingCoordinator.delegate = self
        landingCoordinator.routeActionDelegate = self
        landingCoordinator.analyticsSourcePage = analyticsSourcePage
        addChild(landingCoordinator)
        landingCoordinator.start(completion: customCompletion)
    }

    func startLoginScreenFlow(completion: ((Coordinator) -> Void)? = nil) {
        let loginCoordinator = MayaLoginCoordinator(presenter: router)
        let customCompletion: (() -> Void) = {
            completion?(loginCoordinator)
        }
        loginCoordinator.delegate = self
        loginCoordinator.backActionDelegate = self
        loginCoordinator.routeActionDelegate = self
        addChild(loginCoordinator)
        loginCoordinator.start(completion: customCompletion)
    }

    func startRegistrationScreenFlow() {
        let coordinator = MayaRegistrationCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startLoggedInUserFlow() {
        let mainRouter = Router()
        let mainCoordinator = MayaMainTabCoordinator(router: mainRouter, presenter: router)
        mainCoordinator.delegate = self
        mainCoordinator.routeActionDelegate = self
        addChild(mainCoordinator)
        mainCoordinator.start()
    }

    func startExpiredSessionFlow(completion: (() -> Void)? = nil) {
        let windowRouter = MayaSessionTimeoutCoordinator.createOrderedWindowRouter()
        let sessionTimeoutCoordinator = MayaSessionTimeoutCoordinator(router: windowRouter, presenter: router)
        sessionTimeoutCoordinator.delegate = self
        sessionTimeoutCoordinator.routeActionDelegate = self
        addChild(sessionTimeoutCoordinator)
        sessionTimeoutCoordinator.start(completion: completion)
    }

    func startUpdaterFlow() {
        let router = UpdaterCoordinator.createOrderedWindowRouter()
        let updaterCoordinator = UpdaterCoordinator(router: router)
        updaterCoordinator.routeActionDelegate = self
        addChild(updaterCoordinator)
        updaterCoordinator.start()
    }

    func startMaintenanceFlow() {
        let router = MayaMaintenanceCoordinator.createOrderedWindowRouter()
        let maintenanceCoordinator = MayaMaintenanceCoordinator(router: router)
        maintenanceCoordinator.routeActionDelegate = self
        addChild(maintenanceCoordinator)
        maintenanceCoordinator.start()
    }

    func startProminentDisclosureFlow() {
        let prominentDisclosureCoordinator = MayaProminentDisclosureCoordinator(presenter: router)
        prominentDisclosureCoordinator.routeActionDelegate = self
        prominentDisclosureCoordinator.delegate = self
        addChild(prominentDisclosureCoordinator)
        prominentDisclosureCoordinator.start()
    }

    /// Call this if you want to run importer flow. Edit start() and just call this method to trigger importer flow
    func showImporterScreen() {
        let viewController = StoryboardScene.Main.importerViewController.instantiate()
        let viewModel = ImporterViewModel()
        viewController.routeActionDelegate = self
        viewController.bind(viewModel)
        router.present(viewController, animated: true, completion: nil)
    }

    func startForgotPasswordScreenFlow(_ coordinator: Coordinator) {
        let forgotPasswordCoordinator = MayaForgotPasswordCoordinator(presenter: coordinator.router)
        forgotPasswordCoordinator.backActionDelegate = coordinator
        forgotPasswordCoordinator.routeActionDelegate = coordinator
        forgotPasswordCoordinator.presentationDirection = .rightToLeft
        coordinator.addChild(forgotPasswordCoordinator)
        forgotPasswordCoordinator.start()
    }

    func showResetPasswordAlert(_ coordinator: Coordinator, errorViewModel: ErrorAlertViewModel) {
        let alertViewModel = MayaAlertViewModel(title: errorViewModel.title,
                                                message: errorViewModel.message,
                                                image: errorViewModel.image)

        let alertModalCoordinator = MayaAlertModalCoordinator(presenter: coordinator.router, viewModel: alertViewModel)
        alertModalCoordinator.routeActionDelegate = coordinator
        alertModalCoordinator.backActionDelegate = coordinator

        if let actionTitle = errorViewModel.primaryActionTitle {
            let resetPasswordAction = MayaAlertAction(title: actionTitle, style: .primary) { [weak self, weak alertModalCoordinator] in
                guard let self = self,
                      let alertModalCoordinator = alertModalCoordinator
                else { return }
                coordinator.router.dismissModule(animated: true) {
                    coordinator.removeChild(alertModalCoordinator)
                    self.startForgotPasswordScreenFlow(coordinator)
                }
            }
            alertViewModel.addAction(resetPasswordAction)
        }

        let closeAction = MayaAlertAction(title: L10n.Maya.Mfa.Retry.SecondaryButton.title, style: .secondary) { [weak self, weak alertModalCoordinator] in
            guard let self = self,
                  let alertModalCoordinator = alertModalCoordinator
            else { return }
            coordinator.router.dismissModule(animated: true) {
                coordinator.removeChild(alertModalCoordinator)
            }
        }
        alertViewModel.addAction(closeAction)
        coordinator.addChild(alertModalCoordinator)
        alertModalCoordinator.start()
    }
}

// MARK: Private utility methods
private extension AppCoordinator {
    func applicationEnterForeground() {
        getConfiguration()
    }

    func getConfiguration() {
        configurationService.getConfigurationAction.apply().start()
    }

    func shouldShowLandingScreen() -> Bool {
        if let userDefaults = storeProvider.target(UserDefaultsStore.self),
            let dataPrivacyDisplayed = userDefaults.readRaw(UserDefaultsStoreId.privacyPolicyDisplayed) as? Bool,
            dataPrivacyDisplayed {
            userDefaults.write(false, options: UserDefaultsStoreId.privacyPolicyDisplayed)
            return true
        }
        return !isLoggedIn()
    }

    func isLoggedIn() -> Bool {
        guard let databaseStore = storeProvider.target(DatabaseStore.self),
            (databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User) != nil
            else {
                return false
        }
        return true
    }

    func sendWalletIdToFirebaseAnalytics() {
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
              let walletId = user.walletId
        else { return }

        FirebaseAnalytics.Analytics.setUserID(walletId)
    }
}

// MARK: MayaRegistrationCoordinatorDelegate Methods
extension AppCoordinator: MayaRegistrationCoordinatorDelegate {
    func didTapLogin(_ coordinator: MayaRegistrationCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }

            self.removeChild(coordinator)
            self.startLoginScreenFlow()
        }
    }
}
