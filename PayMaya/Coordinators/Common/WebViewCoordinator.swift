//
//  WebViewCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 10/15/20.
//  Copyright © 2020 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import ConfigurationService
import Foundation
import Injector

protocol WebViewCoordinatorDelegate: AnyObject {
    func didFinishBottomSheetWebView(coordinator: WebViewCoordinator)
}

class WebViewCoordinator: Coordinator {
    @Inject var configurationService: ConfigurationService

    let title: String?
    let willUseDefaultTitle: Bool
    let analyticsModule: AnalyticsModule
    let isKYC1Required: Bool
    let webViewConfiguration: WebViewConfiguration
    var shouldShowExternalLinkPrompt: Bool = false

    var viewModel: WebViewViewModelProtocol? {
        didSet {
            link = viewModel?.link
        }
    }

    weak var ekycDelegate: CommonEKYCCoordinatorDelegate?
    weak var delegate: WebViewCoordinatorDelegate?

    var dismissCompletion: (() -> Void)?

    private var link: URL?
    private var requiresParameters: Bool
    private var module: WebViewParametersRequiringModule?
    private var showInBottomSheet = false

    init(router: RouterProtocol = Router(), presenter: RouterProtocol? = nil, link: URL?, title: String?, willUseDefaultTitle: Bool, requiresParameters: Bool = false, analyticsModule: AnalyticsModule = Analytics.WebView(), showInBottomSheet: Bool = false, isKYC1Required: Bool = false, webViewConfiguration: WebViewConfiguration = WebViewConfiguration()) {
        self.link = link
        self.title = title
        self.willUseDefaultTitle = willUseDefaultTitle
        self.requiresParameters = requiresParameters
        self.analyticsModule = analyticsModule
        self.showInBottomSheet = showInBottomSheet
        self.isKYC1Required = isKYC1Required
        self.webViewConfiguration = webViewConfiguration
        super.init(router: router, presenter: presenter)
    }

    convenience init(router: RouterProtocol = Router(), presenter: RouterProtocol? = nil, viewModel: WebViewViewModelProtocol) {
        self.init(router: router, presenter: presenter, link: viewModel.link, title: viewModel.title, willUseDefaultTitle: viewModel.willUseDefaultTitle)
        self.viewModel = viewModel
    }

    convenience init(router: RouterProtocol = Router(), presenter: RouterProtocol? = nil, webViewParametersRequiringModule: WebViewParametersRequiringModule, parameters: [String: Any]? = nil, requiresParameters: Bool = true, isKYC1Required: Bool = false, webViewConfiguration: WebViewConfiguration = WebViewConfiguration()) {
        if let temporaryViewModel = webViewParametersRequiringModule.generateWebViewModel(queryParams: parameters ?? [:]) {
            self.init(router: router, presenter: presenter, link: temporaryViewModel.link, title: temporaryViewModel.title, willUseDefaultTitle: temporaryViewModel.willUseDefaultTitle, requiresParameters: requiresParameters, analyticsModule: webViewParametersRequiringModule.analyticsModule, isKYC1Required: isKYC1Required, webViewConfiguration: webViewConfiguration)
        } else {
            self.init(router: router, presenter: presenter, link: nil, title: webViewParametersRequiringModule.title, willUseDefaultTitle: false, requiresParameters: requiresParameters, analyticsModule: webViewParametersRequiringModule.analyticsModule, isKYC1Required: isKYC1Required, webViewConfiguration: webViewConfiguration)
        }
        module = webViewParametersRequiringModule
    }

    override func start(completion: (() -> Void)? = nil) {
        if isKYC1Required {
            startPreKYCFlow(completion: completion)
        } else {
            startNormalFlow(completion: completion)
        }
    }

    override func didTapBack(_ viewController: UIViewController) {
        guard showInBottomSheet else {
            super.didTapBack(viewController)
            return
        }
        presenter?.dismissModule(animated: true, completion: nil)
    }
}

// MARK: - DeepLinkHandler Methods
extension WebViewCoordinator: DeepLinkHandler {
    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .dashboard(.mayaMall):
            return .opened(deepLink, nil)
        case .dashboard(.pal):
            return .opened(deepLink, nil)
        case .dashboard(.pbb):
            return .opened(deepLink, nil)
        case .dashboard(.protect):
            return .opened(deepLink, nil)
        case .dashboard(.rafflePromo):
            return .opened(deepLink, nil)
        case .dashboard(.blackpinkGiveaway):
            return .opened(deepLink, nil)
        default:
            return .rejected(deepLink, .notHandled, identifier)
        }
    }
}

// MARK: - MayaWKWebViewControllerDelegate Methods
extension WebViewCoordinator: MayaWKWebViewControllerDelegate {
    func didFinishWebView(_ viewController: ViewController, isSuccessful: Bool, response: Codable?) {
        // Do nothing
    }

    func didCancelWebView(_ viewController: ViewController) {
        // Do nothing
    }
}

// MARK: - WebViewPWPDelegate Methods
extension WebViewCoordinator: WebViewPWPDelegate {
    func didRequestPWPFlow(_ viewController: ViewController, merchantPaymentId: String) {
        startPWPFlow(merchantPaymentId: merchantPaymentId)
    }
}

// MARK: - WebViewShopProductDelegate Methods
extension WebViewCoordinator: WebViewShopProductDelegate {
    func didRequestShopFlow(_ viewController: ViewController, productID: String, prefilledFields: [String: String]?) {
        startShopProductFlow(productID: productID, prefilledFields: prefilledFields)
    }
}

// MARK: - P2MCoordinatorDelegate Methods
extension WebViewCoordinator: MayaP2MCoordinatorDelegate {
    func didFinishMerchantPayment(_ coordinator: MayaP2MCoordinator, redirectURL: URL?) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self else { return }
            self.removeChild(coordinator)
            showExternalURI(redirectURL)
        })
    }

    func didRequestRedirectToDashboard(_ coordinator: MayaP2MCoordinator) {
        removeChild(coordinator)
        guard let url = Constants.DeepLinkPath.dashboard.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
    }
}

// MARK: - WebViewParametersRequiringLandingPageViewControllerDelegate Methods
extension WebViewCoordinator: WebViewParametersRequiringLandingPageViewControllerDelegate {
    func didGetWebViewParametersSuccessfully(for module: WebViewParametersRequiringModule, parameters: [String: Any], viewController: ViewController) {
        viewModel = module.generateWebViewModel(queryParams: parameters, webViewConfiguration: webViewConfiguration)
        startWebViewFlow(completion: nil, shouldPresent: false)
    }

    func didGetWebViewParametersError(error: PayMayaError, module: WebViewParametersRequiringModule, viewController: ViewController) {
        let viewController = StoryboardScene.Others.mayaWebViewParametersLandingErrorViewController.instantiate()
        let viewModel = MayaWebViewParametersLandingErrorViewModel(module: module, error: error)
        viewController.bind(viewModel)
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        setRootModule(viewController, animated: false)
    }
}

// MARK: - MayaBottomSheetWkWebViewViewControllerDelegate Methods
extension WebViewCoordinator: MayaBottomSheetWkWebViewViewControllerDelegate {
    func didTapDone(_ viewController: MayaBottomSheetWkWebViewViewController) {
        delegate?.didFinishBottomSheetWebView(coordinator: self)
        dismissCompletion?()
    }
}

// MARK: - MayaPreKYCCoordinatorDelegate Methods
extension WebViewCoordinator: MayaPreKYCCoordinatorDelegate {
    func preKYCCoordinatorDidFinishKYC(_ coordinator: MayaPreKYCCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
        startNormalFlow(completion: completion)
    }
}

// MARK: - Private Methods
fileprivate extension WebViewCoordinator {
    func startWebViewLandingPage(completion: (() -> Void)? = nil) {
        guard let module = self.module else { return }

        let viewController = StoryboardScene.Others.mayaWebViewParametersLandingPageViewController.instantiate()
        let viewModel = WebViewParametersRequiringLandingPageViewModel(module: module)
        viewController.bind(viewModel)
        viewController.routeActionDelegate = self
        viewController.backActionDelegate = self
        viewController.delegate = self
        setRootModule(viewController, animated: false)
        presenter?.present(router, animated: true, completion: completion)
    }

    func startWebViewFlow(completion: (() -> Void)? = nil, shouldPresent: Bool = true) {
        if viewModel == nil {
            guard let link = link else { return }
            viewModel = WebViewViewModel(link: link, title: title, willUseDefaultTitle: willUseDefaultTitle, analyticsModule: analyticsModule, webViewConfiguration: webViewConfiguration, shouldShowExternalLinkPrompt: shouldShowExternalLinkPrompt)
        }

        let viewController = StoryboardScene.Others.mayaWKWebViewController.instantiate()
        viewController.bind(viewModel!)
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.pwpDelegate = self
        viewController.shopDelegate = self
        viewController.dismissCompletion = dismissCompletion

        setRootModule(viewController, animated: false)
        if shouldPresent {
            presenter?.present(router, animated: true, completion: completion)
        }
    }

    func startPWPFlow(merchantPaymentId: String, completion: (() -> Void)? = nil) {
        let coordinator = MayaP2MCoordinator(presenter: router, merchantPaymentId: merchantPaymentId, pwpMode: .webView)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startShopProductFlow(productID: String, prefilledFields: [String: String]? = nil) {
        let coordinator = MayaShopPurchaseCoordinator(presenter: router, productId: productID, prefilledFields: prefilledFields, mode: .direct, widgetName: nil)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startBottomWebViewFlow(completion: (() -> Void)? = nil) {
        if viewModel == nil {
            guard let link = link else { return }
            viewModel = WebViewViewModel(link: link, title: title, willUseDefaultTitle: willUseDefaultTitle, analyticsModule: analyticsModule)
        }

        let webViewController = StoryboardScene.Others.mayaBottomSheetWkWebViewViewController.instantiate()
        webViewController.bind(viewModel!)
        webViewController.backActionDelegate = self
        webViewController.routeActionDelegate = self
        webViewController.delegate = self
        presenter?.present(webViewController, animated: true, completion: completion)
    }

    func startPreKYCFlow(completion: (() -> Void)? = nil) {
        let coordinator = MayaPreKYCCoordinator(router: router, presenter: presenter)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func startNormalFlow(completion: (() -> Void)? = nil) {
        if requiresParameters {
            startWebViewLandingPage(completion: completion)
        } else if showInBottomSheet {
            startBottomWebViewFlow(completion: completion)
        } else {
            startWebViewFlow(completion: completion)
        }
    }
}
