//
//  MayaP2MCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 28/03/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import ConfigurationService
import Foundation
import Injector

private enum CoordinatorAnalytics {
  static let p2mReceipt = "P2M_RECEIPT"
}

protocol MayaP2MCoordinatorDelegate: AnyObject {
    func didFinishMerchantPayment(_ coordinator: MayaP2MCoordinator, redirectURL: URL?)
    func didRequestRedirectToDashboard(_ coordinator: MayaP2MCoordinator)
}

class MayaP2MCoordinator: MayaProgressBarCoordinator, DeepLinkHandler {
    override var progressSegments: Int {
        return 2
    }

    let merchant: MerchantScanQRProtocol?
    let merchantPaymentId: String?
    let module = Module.p2m
    let pwpMode: PWPMode

    private var mayaReceiptViewModel: MayaReceiptViewModelProtocol?

    weak var delegate: MayaP2MCoordinatorDelegate?

    @Inject var configurationService: ConfigurationService

    init(presenter: RouterProtocol? = nil, merchant: MerchantScanQRProtocol? = nil, merchantPaymentId: String? = nil, pwpMode: PWPMode = .external) {
        self.merchant = merchant
        self.merchantPaymentId = merchantPaymentId
        self.pwpMode = pwpMode
        super.init(presenter: presenter)
    }

    override func start(completion: (() -> Void)? = nil) {
        checkMaintenanceMode(completion: completion)
    }

    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .merchant:
            return .opened(deepLink, nil)
        default: return .rejected(deepLink, .notHandled, identifier)
        }
    }

    override func showErrorAlert(_ viewModel: ErrorAlertViewModel, useMayaModal: Bool = false, completion: (() -> Void)? = nil) {
        super.showErrorAlert(viewModel, useMayaModal: useMayaModal, completion: completion)

        rootViewController?.resignFirstResponder()
    }
}

// MARK: - Helper Methods
private extension MayaP2MCoordinator {
    func showMayaReceipt(viewModel: MayaReceiptViewModelProtocol) {
        let coordinator = MayaReceiptCoordinator(presenter: router, viewModel: viewModel, attributeModule: .qrPay, customAnalyticsModule: CoordinatorAnalytics.p2mReceipt)
        mayaReceiptViewModel = viewModel
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }
}

// MARK: - MayaServiceMaintenanceProtocol Methods
extension MayaP2MCoordinator: MayaServiceMaintenanceProtocol {
    func startNormalFlow(completion: (() -> Void)? = nil) {
        if let merchant = merchant {
            var viewModel: MayaP2MAmountViewModelProtocol?
            if let merchant = merchant as? MerchantQR {
                viewModel = MayaP2MAmountViewModel(merchant: merchant, pwpMode: pwpMode)
            } else if let merchant = merchant as? DynamicMerchantQR {
                viewModel = MayaP2MDynamicAmountViewModel(merchant: merchant, pwpMode: pwpMode)
            } else if let merchant = merchant as? OffUSQRPHMerchant {
                switch merchant.pointOfInitiation {
                case .dynamic: viewModel = MayaOffUsP2MDynamicAmountViewModel(merchant: merchant, pwpMode: pwpMode)
                case .static: viewModel = MayaOffUsP2MAmountViewModel(merchant: merchant, pwpMode: pwpMode)
                }
            } else if let merchant = merchant as? QRPHMerchant {
                viewModel = MayaQRPHP2MAmountViewModel(merchant: merchant, pwpMode: pwpMode)
            }
            startP2MFlow(with: viewModel, completion: completion)
        } else {
            startPWPFlow(completion: completion)
        }
    }

    func startP2MFlow(with viewModel: MayaP2MAmountViewModelProtocol?, completion: (() -> Void)? = nil) {
        let viewController = StoryboardScene.P2M.mayaP2MAmountViewController.instantiate()

        if viewModel?.isQrWithCheckoutFlowEnabled ?? false {
            viewController.title = String.empty
            progressBarView?.hide()
        } else {
            viewController.navigationBarTitleView = progressBarView
        }

        viewController.bind(viewModel)
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.navigationBarTitleView = progressBarView
        setRootModule(viewController, animated: true)
        presenter?.present(router, animated: true, completion: completion)
    }

    func startPWPFlow(completion: (() -> Void)? = nil) {
        guard let merchantPaymentId = merchantPaymentId else { return }
        let viewController = StoryboardScene.P2M.mayaP2MEmptyAmountViewController.instantiate()
        viewController.bind(MayaP2MEmptyAmountViewModel(merchantPaymentId: merchantPaymentId, pwpMode: pwpMode))
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        setRootModule(viewController, animated: true)
        presenter?.present(router, animated: true, completion: completion)
    }

    func startP2MConfirmation(with viewModel: MayaP2MConfirmationViewModelProtocol) {
        let viewController = StoryboardScene.P2M.mayaP2MConfirmationViewController.instantiate()
        viewController.bind(viewModel)
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.navigationBarTitleView = progressBarView
        router.push(viewController, animated: false, popCompletion: nil)
    }

    func startOTPChallenge(with otpType: OTPType) {
        let otpCoordinator = MayaOTPCoordinator(presenter: router, otpType: otpType)
        addChild(otpCoordinator)
        otpCoordinator.delegate = self
        otpCoordinator.backActionDelegate = self
        otpCoordinator.routeActionDelegate = self
        dispatchToMain { [weak otpCoordinator] in
            guard let coordinator = otpCoordinator else { return }
            coordinator.start()
        }
    }

    func showBackConfirmationAlert() {
        let alertViewModel = MayaAlertViewModel(title: L10n.P2m.Cancel.Transaction.title,
                                                message: L10n.P2m.Cancel.Transaction.message,
                                                closeButtonTitle: CommonStrings.Common.no,
                                                actionsAxis: .horizontal)
        let coordinator = MayaAlertModalCoordinator(presenter: router, viewModel: alertViewModel)
        coordinator.routeActionDelegate = self
        coordinator.backActionDelegate = self
        alertViewModel.addAction(MayaAlertAction(title: L10n.P2m.Cancel.Transaction.approval, style: .primary, handler: { [weak self, weak coordinator] in
            guard let self = self, let coordinator = coordinator else { return }
            self.router.dismissModule(animated: true) {
                self.removeChild(coordinator)
                self.backActionDelegate?.didTapBack(self, completion: nil)
            }
        }))
        addChild(coordinator)
        coordinator.start()
    }

    func showMayaReceiptFlow(viewModel: MayaReceiptViewModelProtocol) {
        let coordinator = MayaReceiptCoordinator(presenter: router, viewModel: viewModel, attributeModule: .qrPay)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }
}

extension MayaP2MCoordinator: MayaP2MAmountViewControllerDelegate {
    func didCreatePayment(_ viewController: MayaP2MAmountViewController, viewModel: MayaP2MConfirmationViewModelProtocol) {
        previousViewController = viewController
        startP2MConfirmation(with: viewModel)
    }

    func didCreatePaymentV2(_ viewController: MayaP2MAmountViewController, createdMerchantPayment: CreatedMerchantPaymentV2) {
        let viewModel = MayaP2MCheckoutViewModel(createdMerchantPayment: createdMerchantPayment)
        let checkoutCoordinator = MayaP2MCheckoutCoordinator(presenter: router, viewModel: viewModel)
        checkoutCoordinator.backActionDelegate = self
        checkoutCoordinator.routeActionDelegate = self
        checkoutCoordinator.delegate = self
        addChild(checkoutCoordinator)
        checkoutCoordinator.start()
    }

    func didTapBackButton(_ viewController: MayaP2MAmountViewController) {
        showBackConfirmationAlert()
    }
}

extension MayaP2MCoordinator: MayaP2MEmptyAmountViewControllerDelegate {
    func didCreatePayment(_ viewController: MayaP2MEmptyAmountViewController, viewModel: MayaP2MConfirmationViewModelProtocol) {
        previousViewController = viewController
        startP2MConfirmation(with: viewModel)
    }
}

extension MayaP2MCoordinator: MayaP2MConfirmationViewControllerDelegate {
    func didTapBack(_ viewController: MayaP2MConfirmationViewController) {
        previousViewController = viewController
        backActionDelegate?.didTapBack(self, completion: nil)
    }

    func didSuccessfulPayment(_ viewController: MayaP2MConfirmationViewController) {
        delegate?.didRequestRedirectToDashboard(self)
    }

    func didStartOTPChallenge(otpType: OTPType, viewController: MayaP2MConfirmationViewController) {
        startOTPChallenge(with: otpType)
    }

    func showSuccessReceipt(_ viewModel: MayaReceiptViewModelProtocol, viewController: MayaP2MConfirmationViewController) {
        mayaReceiptViewModel = viewModel
        let coordinator = MayaReceiptCoordinator(presenter: router, viewModel: viewModel, attributeModule: .qrPay)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }
}

// MARK: - MayaReceiptCoordinatorDelegate Methods
extension MayaP2MCoordinator: MayaReceiptCoordinatorDelegate {
    func didTapAddToFavorites(_ coordinator: MayaReceiptCoordinator) {
        // Do nothing
    }

    func didTapClose(_ coordinator: MayaReceiptCoordinator) {
        if let mayaReceiptViewModel = mayaReceiptViewModel as? MayaP2MSuccessReceiptViewModel,
           let redirectURL = mayaReceiptViewModel.customRedirectURL,
           let dashboardURL = Constants.DeepLinkPath.dashboard.url {
            routeActionDelegate?.didRequestStartDeepLink(self, with: dashboardURL, completion: { _ in
                showExternalURI(redirectURL)
            })
        } else {
            removeChild(coordinator)
            delegate?.didRequestRedirectToDashboard(self)
        }
    }
}

// MARK: - MayaOTPCoordinatorDelegate Methods
extension MayaP2MCoordinator: MayaOTPCoordinatorDelegate {
    func didReceiveOTPError(error: PayMayaError, _ coordinator: MayaOTPCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            self?.removeChild(coordinator)
        }
    }

    func didReceivePostAuthenticationActionError(_ coordinator: MayaOTPCoordinator) {
        // Do nothing
    }

    func didRequestDisplayMayaAlertViewModel(viewModel: MayaAlertViewModel, response: Codable?, coordinator: MayaOTPCoordinator) {
        // Do nothing
    }

    func didRequestDisplayMayaReceiptViewModel(viewModel: MayaReceiptViewModelProtocol, response: Codable?, coordinator: MayaOTPCoordinator) {
        removeChild(coordinator)
        router.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }
            self.showMayaReceiptFlow(viewModel: viewModel)
        }
    }
}

// MARK: MayaP2MCheckoutCoordinatorDelegate Methods
extension MayaP2MCoordinator: MayaP2MCheckoutCoordinatorDelegate {
    func didComplete(_ coordinator: MayaP2MCheckoutCoordinator, mayaReceiptViewModel: MayaReceiptViewModelProtocol) {
        self.removeChild(coordinator)
        self.showMayaReceipt(viewModel: mayaReceiptViewModel)
    }

    func didCompleteNoReceipt(_ coordinator: MayaP2MCheckoutCoordinator) {
        self.removeChild(coordinator)

        guard let dashboardURL = Constants.DeepLinkPath.dashboard.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: dashboardURL, completion: nil)
    }

    func didDismiss(_ coordinator: MayaP2MCheckoutCoordinator, error: PayMayaError?, needsKYCUpgrade: Bool) {
        if error != nil {
            if let errorViewModel = error?.viewModel {
                showErrorAlert(errorViewModel, useMayaModal: true)
            }
        }
        self.removeChild(coordinator)

        if needsKYCUpgrade {
            showPreKYCFlow()
        }
    }
}

extension MayaP2MCoordinator: MayaPreKYCCoordinatorDelegate {
    private func showPreKYCFlow() {
        let coordinator = MayaPreKYCCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        coordinator.routeActionDelegate = self
        coordinator.analyticsModule = Analytics.LoansDashboard()
        addChild(coordinator)
        coordinator.start()
    }

    func preKYCCoordinatorDidFinishKYC(_ coordinator: MayaPreKYCCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
    }

    func preKYCCoordinatorDidTapBack(_ coordinator: MayaPreKYCCoordinator) {
        removeChild(coordinator)
    }
}
