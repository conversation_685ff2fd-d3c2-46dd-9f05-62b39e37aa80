//
//  MayaAutoDebitArrangementCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 6/16/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
import AssetProvider
import ConfigurationService
import Flutter
import FlutterPluginRegistrant
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider
import UIKit

protocol MayaAutoDebitArrangementCoordinatorDelegate: AnyObject {
    func didComplete(_ coordinator: MayaAutoDebitArrangementCoordinator)
    func didDismiss(_ coordinator: MayaAutoDebitArrangementCoordinator)
}

class MayaAutoDebitArrangementCoordinator: MayaFlutterBaseCoordinator {
    weak var delegate: MayaAutoDebitArrangementCoordinatorDelegate?

    init(
        params: String? = nil,
        router: RouterProtocol = Router(),
        presenter: RouterProtocol? = nil,
        animated: Bool = false
    ) {
        super.init(
            flutterModule: .autoDebitArrangement,
            routeParams: params,
            router: router,
            presenter: presenter,
            animated: animated,
            shouldPresentRouter: true
        )
    }

    override func didComplete(_ coordinator: MayaFlutterBaseCoordinator) {
        super.didComplete(coordinator)

        self.delegate?.didComplete(self)
    }

    override func didDismiss(_ coordinator: MayaFlutterBaseCoordinator) {
        super.didDismiss(coordinator)

        self.delegate?.didDismiss(self)
    }
}

extension MayaAutoDebitArrangementCoordinator: DeepLinkHandler {
    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .dashboard(.scheduler):
            return .opened(deepLink, nil)
        default: return .rejected(deepLink, .notHandled, identifier)
        }
    }
}
