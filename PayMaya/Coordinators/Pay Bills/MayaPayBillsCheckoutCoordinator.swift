//
//  MayaPayBillsCheckoutCoordinator.swift
//  PayMaya
//
//  Created by <PERSON> on 5/10/23.
//  Copyright © 2023 Maya Philippines, Inc. All rights reserved.
//

import AssetProvider
import Flutter
import FlutterPluginRegistrant
import Foundation
import Injector
import ReactiveSwift
import StoreProvider
import UIKit

protocol MayaPayBillsCheckoutCoordinatorDelegate: AnyObject {
    func didDismiss(_ coordinator: MayaPayBillsCheckoutCoordinator)
    func didCompletePayment(_ coordinator: MayaPayBillsCheckoutCoordinator)
    func didCompletePaymentNoReceipt(_ coordinator: <PERSON><PERSON>ayBillsCheckoutCoordinator)
}

class MayaPayBillsCheckoutCoordinator: Coordinator, AnalyticsProtocol {
    @Inject var storeProvider: StoreProvider
    @Inject private var flutterEngineManager: FlutterEngineManager

    private let checkedOutPayment: CheckedOutPaymentV3

    weak var delegate: MayaPayBillsCheckoutCoordinatorDelegate?

    init(router: RouterProtocol = Router(), presenter: RouterProtocol? = nil, checkedOutPayment: CheckedOutPaymentV3) {
        self.checkedOutPayment = checkedOutPayment

        super.init(router: router, presenter: presenter)

        flutterEngineManager.startEngine(with: .checkout)
        setMethodHandler()
    }

    override func start(completion: (() -> Void)? = nil) {
        if let engine = flutterEngineManager.getEngine(with: .checkout) {
            let flutterViewController = FlutterViewController(engine: engine, nibName: nil, bundle: nil)
            let args = [Constants.Flutter.Checkout.route: Constants.Flutter.Checkout.buyLoadScreen,
                        Constants.Flutter.Checkout.paymentId: self.checkedOutPayment.payment.paymentId]

            flutterViewController.view.backgroundColor = CommonAsset.MayaColors.Background.backgroundPrimaryWhite.color
            flutterEngineManager
                .getChannel(with: .checkout)?
                .invokeMethod(FlutterMethod.requestFullscreen.rawValue, arguments: args ) { [weak self] _ in
                    self?.presenter?.present(flutterViewController, animated: false, completion: completion)
                }
        }
    }

    private func setMethodHandler() {
        flutterEngineManager.getChannel(with: .checkout)?.setMethodCallHandler { [weak self] call, result in
            guard let self = self,
                  let method = FlutterMethod(rawValue: call.method)
            else { return result(nil) }

            switch method {
            case .complete:
                guard let arguments: [String: Any] = call.arguments as? Dictionary<String, Any>,
                      let showReceipt: Bool = arguments[FlutterArgument.showReceipt.rawValue] as? Bool,
                      showReceipt
                else {
                    dismiss {
                        self.delegate?.didCompletePaymentNoReceipt(self)
                    }
                    return
                }

                self.dismiss { self.delegate?.didCompletePayment(self) }
            case .dismiss:
                self.dismiss()
            case .logAnalyticsEvent:
                guard let jsonString = call.arguments as? String,
                      let jsonData = jsonString.data(using: .utf8),
                      let dictionary = try? JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
                      let name = dictionary["event_name"] as? String
                else { return }

                let attributes = dictionary["attributes"] as? [String: Any]
                self.analyticsUtils.logMayaEvents(name, attributes: attributes)
            default: result(nil)
            }
        }
    }

    private func dismiss(completion: (() -> Void)? = nil) {
        self.presenter?.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }
            self.flutterEngineManager.getChannel(with: .checkout)?.setMethodCallHandler(nil)
            self.flutterEngineManager.clearEngine(with: .checkout)
            self.flutterEngineManager.initializeEngine(with: .checkout)
            self.delegate?.didDismiss(self)
            completion?()
        }
    }
}
