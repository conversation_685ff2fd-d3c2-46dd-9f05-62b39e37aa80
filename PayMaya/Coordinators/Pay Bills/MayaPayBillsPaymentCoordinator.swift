//
//  MayaPayBillsPaymentCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 10/05/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
import AssetProvider
import ConfigurationService
import Injector
import Moya
import ReactiveSwift
import StoreProvider
import UIKit

protocol MayaPayBillsPaymentCoordinatorDelegate: AnyObject {
    func didPaySuccessfully(_ coordinator: MayaPayBillsPaymentCoordinator)
    func didReceiveError(_ coordinator: MayaPayBillsPaymentCoordinator, error: ErrorAlertViewModel?)
    func didTapBack(_ coordinator: MayaPayBillsPaymentCoordinator)
    func didRequestRedirectToDashboard(_ coordinator: MayaPayBillsPaymentCoordinator)
    func didRequestRedirectToPayBills(_ coordinator: MayaPayBillsPaymentCoordinator)
}

class MayaPayBillsPaymentCoordinator: MayaProgressBarCoordinator, DeepLink<PERSON><PERSON><PERSON>, AnalyticsProtocol, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject var apiProvider: APIProvider
    @Inject var storeProvider: StoreProvider
    @Inject var configurationService: ConfigurationService

    var viewModel: MayaPayBillsPaymentViewModelProtocol?
    var billerFormViewModel: MayaMainBillerFormViewModelProtocol?
    var billsPaymentConfirmationViewModel: MayaPayBillsConfirmationViewModelProtocol?
    var receiptV3ViewModel: MayaPayBillsReceiptV3ViewModelProtocol?
    var isFromFavorites: Bool = false

    private let analyticsSourcePage: AnalyticsConstants.SourcePage?
    private var presentationCompletion: (() -> Void)?
    private var isAddedToFavorite = false
    private var successBottomSheetCoordinator: MayaAlertBottomSheetCoordinator?
    private let favoriteLimitProperty = MutableProperty<FavoriteLimit?>(nil)
    private var favoriteLimitAction: Action<Void, FavoriteLimit, PayMayaError>!

    private var isFavoritesUnderMaintenance: Bool {
        guard let myFavoritesService = configurationService.maintenance?.serviceAvailability.maintenance(for: .myFavorites) else { return false }
        return myFavoritesService.isUnderMaintenance
    }

    override var progressSegments: Int { return 2 }
    weak var delegate: MayaPayBillsPaymentCoordinatorDelegate?

    init(
        presenter: RouterProtocol? = nil,
        analyticsSourcePage: AnalyticsConstants.SourcePage? = nil
    ) {
        self.analyticsSourcePage = analyticsSourcePage
        super.init(presenter: presenter)
    }

    override func start(completion: (() -> Void)? = nil) {
        presentationCompletion = completion
        subscribeToSignals()

        guard let billerSlug = viewModel?.selectedBiller?.slug else { return }
        if BillerHandling(with: billerSlug) == .bir {
            viewModel?.birDetailsAndFormSeriesAction.apply().start()
        } else {
            viewModel?.getBillerDetailsAction.apply(billerSlug).start()
        }
    }

    override func didTapBack(_ viewController: UIViewController, completion: (() -> Void)? = nil) {
        super.didTapBack(viewController) { [weak self] in
            completion?()
            guard let self = self else { return }
            self.delegate?.didTapBack(self)
        }
    }

    // MARK: DeepLinkHandler
    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .dashboard(.payBills(.payment(.customFields?))):
            return .opened(deepLink, nil)
        case .dashboard(.payBills(.favorite(.favoriteId?))):
            return .opened(deepLink, nil)
        default: return .rejected(deepLink, DeepLinkError.notHandled, identifier)
        }
    }

    func showReceiptBillsPayCheckoutFlowV3(billPaymentDetails: BillsPaymentDetailsV3, updateBillerFavoriteResult: UpdateBillerFavoriteResult?) {
        guard let confirmationViewItem = billerFormViewModel?.getMayaPayBillsConfirmationViewItem()
        else { return }

        let viewModel = MayaPayBillsReceiptV3ViewModel(receiptViewItem: billPaymentDetails.mapToMayaPayBillsReceiptViewItem(),
                                                       billsPaymentDetails: billPaymentDetails,
                                                       billerItem: confirmationViewItem.billerItem,
                                                       confirmationViewItem: confirmationViewItem,
                                                       updateBillerFavoriteResult: updateBillerFavoriteResult,
                                                       shouldShowAddToFavorites: shouldShowSaveToFavorites())

        receiptV3ViewModel = viewModel

        let coordinator = MayaReceiptCoordinator(presenter: router, viewModel: viewModel, attributeModule: .payBills)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }
}

// MARK: - Private Methods
private extension MayaPayBillsPaymentCoordinator {
    func subscribeToSignals() {
        guard let viewModel = viewModel else { return }

        let executingSignal = Signal.merge(viewModel.getBillerDetailsAction.isExecuting.signal, viewModel.birDetailsAndFormSeriesAction.isExecuting.signal)
        executingSignal.observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                isExecuting
                    ? self?.presenter?.toPresentable().showMayaLoader()
                    : self?.presenter?.toPresentable().hideMayaLoader()
            }?.addToDisposeBag(disposeBag)

        viewModel.getBillerDetailsAction.completed
            .observe(on: UIScheduler())
            .observeValues { [weak self] in
                guard let self = self else { return }
                self.startBillerFormFlow()
            }?.addToDisposeBag(disposeBag)

        viewModel.getBillerDetailsAction.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self = self else { return }
                if let errorViewModel = error.viewModel {
                    self.delegate?.didReceiveError(self, error: errorViewModel)
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.birDetailsAndFormSeriesAction.completed
            .observe(on: UIScheduler())
            .observeValues { _ in
                guard let billerSlug = viewModel.selectedBiller?.slug else { return }
                viewModel.getBillerDetailsAction.apply(billerSlug).start()
            }?.addToDisposeBag(disposeBag)

        viewModel.birDetailsAndFormSeriesAction.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self = self, let billerSlug = viewModel.selectedBiller?.slug else { return }
                if let errorViewModel = error.viewModel {
                    self.delegate?.didReceiveError(self, error: errorViewModel)
                } else {
                    viewModel.getBillerDetailsAction.apply(billerSlug).start()
                }
            }?.addToDisposeBag(disposeBag)
    }

    func startBillerFormFlow() {
        guard let viewModel = viewModel else { return }
        if configurationService.billsPayV3EndpointEnabled.value {
            if let billerItem = viewModel.selectedBiller,
               let isActive = billerItem.active,
               isActive {
                if BillerHandling(with: billerItem.slug) == .bir {
                    startBIRPayBillsFlow()
                } else {
                    startDefaultPayBillsFlow()
                }
            } else {
                startBillerErrorFlow()
            }
        } else {
            if let selectedSlug = viewModel.selectedBiller?.slug,
               BillerHandling(with: selectedSlug) == .bir {
                startBIRPayBillsFlow()
            } else {
                startDefaultPayBillsFlow()
            }
        }
    }

    func startDefaultPayBillsFlow() {
        let viewController = StoryboardScene.PayBills.mayaBillerFormViewController.instantiate()
        if let billerItem = viewModel?.selectedBiller,
           let billerDetails = viewModel?.billerDetails {
            billerFormViewModel = MayaBillerFormViewModel(billerItem: billerItem, favoriteItem: viewModel?.favoriteItem, otherFields: billerDetails.otherFields, metadata: billerDetails.metadata, category: billerDetails.category)
        }
        viewController.bind(billerFormViewModel)
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.navigationBarTitleView = progressBarView
        viewController.analyticsSourcePage = analyticsSourcePage
        setRootModule(viewController, animated: false)
        presenter?.present(router, animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.presentationCompletion?()
        })
        setupFavoritesLimit()
        setupUpdateFavorites()
    }

    func startBIRPayBillsFlow() {
        let viewController = StoryboardScene.PayBills.mayaBIRBillerFormViewController.instantiate()
        if let billerItem = viewModel?.selectedBiller,
            let billerDetails = viewModel?.billerDetails {
            billerFormViewModel = MayaBIRBillerFormViewModel(billerItem: billerItem, favoriteItem: viewModel?.favoriteItem, otherFields: billerDetails.otherFields, metadata: billerDetails.metadata)
        }
        viewController.bind(billerFormViewModel)
        viewController.birDelegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.navigationBarTitleView = progressBarView
        viewController.analyticsSourcePage = analyticsSourcePage
        setRootModule(viewController, animated: false)
        presenter?.present(router, animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.presentationCompletion?()
        })
        setupFavoritesLimit()
        setupUpdateFavorites()
    }

    func startBillerErrorFlow() {
        guard
            let viewModel = viewModel,
            let billerItem = viewModel.selectedBiller
        else { return }

        let viewController = StoryboardScene.PayBills.mayaBillerErrorViewController.instantiate()
        let errorViewModel = MayaBillerErrorViewModel(billerName: billerItem.name, isFromFavorites: isFromFavorites)
        viewController.bind(errorViewModel)
        viewController.delegate = self
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        setRootModule(viewController, animated: false)
        presenter?.present(router, animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.presentationCompletion?()
        })
    }

    func showConfirmationScreen() {
        guard let confirmationViewItem = billerFormViewModel?.getMayaPayBillsConfirmationViewItem() else { return }
        billsPaymentConfirmationViewModel = MayaPayBillsConfirmationViewModel(item: confirmationViewItem)
        let viewController = StoryboardScene.Others.mayaConfirmationViewController.instantiate()
        viewController.navigationBarTitleView = progressBarView
        viewController.currentPageNumberForProgress = 2
        viewController.totalPagesForProgress = progressSegments
        viewController.backActionDelegate = self
        viewController.delegate = self
        viewController.billsPayDelegate = self
        viewController.routeActionDelegate = self
        viewController.bind(billsPaymentConfirmationViewModel)
        router.push(viewController, animated: false, popCompletion: nil)
    }

    func showBarcodeScanInstructionScreen() {
        let viewController = StoryboardScene.PayBills.barcodeScanInstructionViewController.instantiate()
        viewController.delegate = self
        viewController.routeActionDelegate = self
        router.present(viewController, animated: true, completion: nil)
    }

    func startBarcodeScannerFlow() {
        let coordinator = BarcodeScannerCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startSaveToFavoritesFlow(executedPayment: ExecutedPayment, confirmationItem: MayaPayBillsConfirmationViewItem) {
        guard let sourceCoordinator = successBottomSheetCoordinator else { return }
        let viewModel = MayaPayBillsAddToFavoritesViewModel(billsPaymentDetails: executedPayment, confirmationItem: confirmationItem)
        let coordinator = MayaAddToFavoritesCoordinator(presenter: sourceCoordinator.router, viewModel: viewModel, analyticsAttributeModule: ReceiptAttributeModule.payBills.rawValue)
        coordinator.delegate = self
        coordinator.backActionDelegate = sourceCoordinator
        coordinator.routeActionDelegate = sourceCoordinator
        sourceCoordinator.addChild(coordinator)
        coordinator.start()
    }

    func logPaymentPageConfirmed() {
        guard let viewModel,
                viewModel.isEcommAppEventV2BillsPayEnabled,
                let confirmationViewModel = billsPaymentConfirmationViewModel  else { return }

        analyticsService.logMayaEvents(
            AnalyticsConstants.Screen.BillsPay.paymentPage.confirmed(),
            attributes: [
                .billerName: confirmationViewModel.billerItem.name,
                .billerSlug: confirmationViewModel.billerItem.slug,
                .categoryName: confirmationViewModel.billerItem.category,
                .categorySlug: confirmationViewModel.billerItem.categorySlug,
                .totalAmount: confirmationViewModel.billsPaymentBreakdown.totalAmount,
                .billAmount: confirmationViewModel.billsPaymentBreakdown.billAmount,
                .convenienceFee: confirmationViewModel.billsPaymentBreakdown.fees
            ].discardNil()
        )
    }

    func logPaymentFavoritesTapped() {
        guard let viewModel,
              viewModel.isEcommAppEventV2BillsPayEnabled,
              let receiptViewModel = receiptV3ViewModel else { return }

        let billsTransactionStatus = BillsPaymentTransactionStatus(rawValue: receiptViewModel.receiptItem.status) ?? .new

        analyticsService.logMayaEvents(
            AnalyticsConstants.Screen.BillsPay.paymentFavorites.tapped(),
            attributes: [
                .billerName: receiptViewModel.billerItem.name,
                .billerSlug: receiptViewModel.billerItem.slug,
                .categoryName: receiptViewModel.billerItem.category,
                .categorySlug: receiptViewModel.billerItem.categorySlug,
                .totalAmount: receiptViewModel.receiptItem.totalAmount.value,
                .billAmount: receiptViewModel.receiptItem.billAmount.value,
                .convenienceFee: receiptViewModel.receiptItem.billerFee.value,
                .status: billsTransactionStatus.statusMessage,
                .paymentMethod: receiptViewModel.receiptItem.paymentOption,
                .favorite: viewModel.favoriteItem != nil
            ].discardNil()
        )
    }

    func logReceiptPageViewed() {
        guard let viewModel,
                viewModel.isEcommAppEventV2BillsPayEnabled,
                let receiptViewModel = receiptV3ViewModel else { return }

        let billsTransactionStatus = BillsPaymentTransactionStatus(rawValue: receiptViewModel.receiptItem.status) ?? .new

        analyticsService.logMayaEvents(
            AnalyticsConstants.Screen.BillsPay.receiptPage.viewed(),
            attributes: [
                .billerName: receiptViewModel.billerItem.name,
                .billerSlug: receiptViewModel.billerItem.slug,
                .categoryName: receiptViewModel.billerItem.category,
                .categorySlug: receiptViewModel.billerItem.categorySlug,
                .totalAmount: receiptViewModel.receiptItem.totalAmount.value,
                .billAmount: receiptViewModel.receiptItem.billAmount.value,
                .convenienceFee: receiptViewModel.receiptItem.billerFee.value,
                .status: billsTransactionStatus.statusMessage,
                .paymentMethod: receiptViewModel.receiptItem.paymentOption,
                .favorite: viewModel.favoriteItem != nil
            ].discardNil()
        )
    }

    private func setupUpdateFavorites() {
        guard let viewModel = billerFormViewModel else { return }

        viewModel.updateFavoritesAction?.values
            .observe(on: UIScheduler())
            .observeValues { [weak self] result in
                guard let self = self else { return }
                switch result {
                case .updateFavoriteSuccess(let alertViewModel, let executedPayment):
                    if let alertViewModel = alertViewModel as? MayaAlertViewModel,
                        let confirmationItem = viewModel.getMayaPayBillsConfirmationViewItem() {
                        self.showSuccessBottomSheet(viewModel: alertViewModel, response: executedPayment, confirmationItem: confirmationItem)
                    }
                case .updateFavoriteSuccessCheckoutFlowV3(let billsPaymentDetails, _),
                        .updateFavoriteFailedCheckoutFlowV3(let billsPaymentDetails, _),
                        .updateFavoriteNoChange(let billsPaymentDetails):
                    self.showReceiptBillsPayCheckoutFlowV3(billPaymentDetails: billsPaymentDetails, updateBillerFavoriteResult: result)
                default: break
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.updateFavoritesAction.isExecuting.signal.observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                guard let self = self else { return }
                let presenter = self.presenter == nil ? self.router.toPresentable() : self.presenter?.toPresentable()
                isExecuting ? presenter?.showMayaLoader() : presenter?.hideMayaLoader()
            }?.addToDisposeBag(disposeBag)
    }

    private func showReceipt(executedPayment: ExecutedPayment, confirmationItem: MayaPayBillsConfirmationViewItem) {
        let viewModel = MayaPayBillsReceiptViewModel(executedPayment: executedPayment, item: confirmationItem, shouldShowSaveToFavorites: shouldShowSaveToFavorites())
        let coordinator = MayaReceiptCoordinator(presenter: router, viewModel: viewModel, attributeModule: .payBills)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    private func showSuccessBottomSheet(viewModel: MayaAlertViewModel?, response: Codable?, confirmationItem: MayaPayBillsConfirmationViewItem) {
        guard let executedPayment = response as? ExecutedPayment else { return }

        let title = L10n.Maya.Paybills.Confirmation.Success.Bottomsheet.title
        let message = L10n.Maya.Paybills.Confirmation.Success.Bottomsheet.subtitle(confirmationItem.billerItem.name)
        let image = CommonAsset.Images.Alert.imageYellowClock.image
        let alertViewModel = viewModel == nil ? MayaAlertViewModel(title: title, message: message, image: image, actionsAxis: .horizontal) : viewModel!
        let coordinator = MayaAlertBottomSheetCoordinator(presenter: router, viewModel: alertViewModel)
        successBottomSheetCoordinator = coordinator

        let alert1 = MayaAlertAction(title: CommonStrings.Common.View.receipt, style: .secondary) { [weak self, weak coordinator] in
            guard let self = self, let coordinator = coordinator else { return }
            self.analyticsUtils.logMayaEvents(.tap(Analytics.PayBillsProcessing.viewReceipt))
            self.router.dismissModule(animated: true) {
                self.removeChild(coordinator)
                self.showReceipt(executedPayment: executedPayment, confirmationItem: confirmationItem)
            }
        }
        let alert2 = MayaAlertAction(title: CommonStrings.Common.done, style: .primary, handler: { [weak self, weak coordinator] in
            guard let self = self, let coordinator = coordinator else { return }
            self.analyticsUtils.logMayaEvents(.tap(Analytics.PayBillsProcessing.done))
            self.removeChild(coordinator)
            self.delegate?.didRequestRedirectToDashboard(self)
        })
        alertViewModel.addAction(alert1)
        alertViewModel.addAction(alert2)

        if shouldShowSaveToFavorites() {
            let additionalButton = MayaAlertAdditionalInfoType.button(icon: CommonAsset.Images.Icons.iconGreenHeart.image, message: CommonStrings.Favorites.Save.spiel) { [weak self] in
                guard let self = self else { return }
                self.analyticsUtils.logMayaEvents(.tap(Analytics.PayBillsProcessing.favorites))
                self.startSaveToFavoritesFlow(executedPayment: executedPayment, confirmationItem: confirmationItem)
            }
            alertViewModel.additionalInfo = additionalButton
        }

        coordinator.analyticsModuleType = .sheet(Analytics.PayBillsProcessing())
        coordinator.routeActionDelegate = self
        coordinator.backActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    private func setupFavoritesLimit() {
        favoriteLimitAction = Action { [weak self] in
            guard let self = self, !self.isFavoritesUnderMaintenance else {
                return SignalProducer(error: PayMayaError())
            }

            return self.apiProvider.reactive.request(API.Favorites.limit(.billspay), type: FavoriteLimit.self)
                .on(value: { limit in
                    self.favoriteLimitProperty.value = limit
                })
                .mapMayaError()
        }

        favoriteLimitAction.apply().start()
    }

    private func shouldShowSaveToFavorites() -> Bool {
        guard let confirmationItem = billerFormViewModel?.getMayaPayBillsConfirmationViewItem() else { return false }
        let reachedLimit = favoriteLimitProperty.value?.typeLimit(.billspay)?.reachedLimit ?? false
        return confirmationItem.favoriteItem == nil && !reachedLimit && !isAddedToFavorite
    }
}

// MARK: - MayaMainBillerFormViewControllerDelegate Methods
extension MayaPayBillsPaymentCoordinator: MayaMainBillerFormViewControllerDelegate {
    func didCreatePayment(_ viewController: MayaMainBillerFormViewController) {
        previousViewController = viewController
        showConfirmationScreen()
    }

    func didTapBarcodeScan(_ viewController: MayaMainBillerFormViewController) {
        let userDefaults = storeProvider.target(UserDefaultsStore.self)
        guard let wasShownForUser = userDefaults?.readRaw(UserDefaultsStoreId.barcodeScanInstructionShownForUser) as? Bool, wasShownForUser else {
            showBarcodeScanInstructionScreen()
            userDefaults?.write(true, options: UserDefaultsStoreId.barcodeScanInstructionShownForUser)
            return
        }
        startBarcodeScannerFlow()
    }
}

// MARK: - MayaBIRBillerFormViewControllerDelegate Methods
extension MayaPayBillsPaymentCoordinator: MayaBIRBillerFormViewControllerDelegate {
    func didTapInformationButton(_ viewController: MayaBIRBillerFormViewController) {
        let viewController = StoryboardScene.PayBills.mayaBIRRemindersViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        viewController.modalPresentationStyle = .overCurrentContext
        viewController.modalTransitionStyle = .crossDissolve
        router.toPresentable().present(viewController, animated: true, completion: nil)
    }
}

// MARK: - BarcodeScanInstructionViewControllerDelegate Methods
extension MayaPayBillsPaymentCoordinator: BarcodeScanInstructionViewControllerDelegate {
    func didTapGotIt(_ viewController: BarcodeScanInstructionViewController) {
        router.dismissModule(animated: true, completion: { [weak self] in
            self?.startBarcodeScannerFlow()
        })
    }
}

// MARK: - BarcodeScannerCoordinatorDelegate Methods
extension MayaPayBillsPaymentCoordinator: BarcodeScannerCoordinatorDelegate {
    func didScanBarcode(_ coordinator: Coordinator, barcode: String) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self,
                  let viewModel = self.billerFormViewModel as? MayaBillerFormViewModelProtocol,
                  !barcode.isEmpty
            else { return }
            self.removeChild(coordinator)
            viewModel.scannedAccountNumberProperty.value = barcode
        })
    }
}

// MARK: - MayaBillerErrorViewControllerDelegate Methods
extension MayaPayBillsPaymentCoordinator: MayaBillerErrorViewControllerDelegate {
    func didRequestRedirectToPayBills(_ viewController: MayaBillerErrorViewController) {
        delegate?.didRequestRedirectToPayBills(self)
    }
}

// MARK: - MayaConfirmationViewControllerDelegate Methods
extension MayaPayBillsPaymentCoordinator: MayaConfirmationViewControllerDelegate {
    func didFinishContinueAction(_ viewController: MayaConfirmationViewController, response: Response) {
        if configurationService.billsPayCheckoutFlowV3Enabled.value {
            handleFinishedContinueActionBillsPayCheckoutFlowV3(viewController, response: response)
        } else {
            handleFinishedContinueAction(viewController, response: response)
        }
    }

    private func handleFinishedContinueAction(_ viewController: MayaConfirmationViewController, response: Response) {
        guard let confirmationItem = billerFormViewModel?.getMayaPayBillsConfirmationViewItem() else { return }
        if let executedPayment = try? response.map(ExecutedPayment.self) {
            if let favoriteItem = confirmationItem.favoriteItem {
                let billerFavorite = BillerFavorite(favoriteItem: favoriteItem, billsPaymentDetails: executedPayment, confirmationItem: confirmationItem)
                billerFormViewModel?.updateFavoritesAction.apply(billerFavorite).start()
            } else {
                showSuccessBottomSheet(viewModel: nil, response: executedPayment, confirmationItem: confirmationItem)
            }
        }
    }

    private func handleFinishedContinueActionBillsPayCheckoutFlowV3(_ viewController: MayaConfirmationViewController, response: Response) {
        presenter?.toPresentable().showMayaLoader()
        if let checkedOutPayment = try? response.map(CheckedOutPaymentV3.self) {
            startCheckoutFlow(checkedOutPayment: checkedOutPayment)
        }
    }
}

// MARK: - MayaAddToFavoritesCoordinatorDelegate Methods
extension MayaPayBillsPaymentCoordinator: MayaAddToFavoritesCoordinatorDelegate {
    func didSuccessfullyAddedFavorite(_ coordinator: MayaAddToFavoritesCoordinator) {
        successBottomSheetCoordinator?.router.dismissModule(animated: true, completion: { [weak self, weak coordinator] in
            guard let self = self, let coordinator = coordinator else { return }
            self.removeChild(coordinator)
            self.isAddedToFavorite = true
            self.successBottomSheetCoordinator?.reloadWithAdditionalInfo(nil)
        })
    }

    func didTapBackFromAddToFavorites(_ coordinator: MayaAddToFavoritesCoordinator) {
        // Do nothing
    }
}

// MARK: - MayaReceiptCoordinatorDelegate Methods
extension MayaPayBillsPaymentCoordinator: MayaReceiptCoordinatorDelegate {
    func didTapAddToFavorites(_ coordinator: MayaReceiptCoordinator) {
        logPaymentFavoritesTapped()
    }

    func didTapClose(_ coordinator: MayaReceiptCoordinator) {
        removeChild(coordinator)

        logReceiptPageViewed()

        delegate?.didRequestRedirectToDashboard(self)
    }
}

// MARK: - MayaPayBillsCheckoutCoordinatorDelegate Methods
extension MayaPayBillsPaymentCoordinator: MayaPayBillsCheckoutCoordinatorDelegate {
    func startCheckoutFlow(checkedOutPayment: CheckedOutPaymentV3) {
        let checkoutCoordinator = MayaPayBillsCheckoutCoordinator(presenter: router, checkedOutPayment: checkedOutPayment)

        checkoutCoordinator.backActionDelegate = self
        checkoutCoordinator.routeActionDelegate = self
        checkoutCoordinator.delegate = self
        addChild(checkoutCoordinator)
        checkoutCoordinator.start()

        presenter?.toPresentable().hideMayaLoader()
    }

    func didDismiss(_ coordinator: MayaPayBillsCheckoutCoordinator) {
        removeChild(coordinator)
    }

    func didCompletePayment(_ coordinator: MayaPayBillsCheckoutCoordinator) {
        guard let confirmationViewModel = billsPaymentConfirmationViewModel else { return }
        confirmationViewModel.getBillPaymentDetails.apply().start()
    }

    func didCompletePaymentNoReceipt(_ coordinator: MayaPayBillsCheckoutCoordinator) {
        self.removeChild(coordinator)

        guard let dashboardURL = Constants.DeepLinkPath.dashboard.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: dashboardURL, completion: nil)
    }
}

extension MayaPayBillsPaymentCoordinator: MayaPayBillsConfirmationViewControllerDelegate {
    func didTapContinue(_ viewController: MayaConfirmationViewController) {
        logPaymentPageConfirmed()
    }

    func didTapViewInitialReceipt(_ viewController: MayaConfirmationViewController, billPaymentDetails: BillsPaymentDetailsV3, billerItem: BillerItem) {
        guard let billerFormViewModel = billerFormViewModel,
                let confirmationItem = billerFormViewModel.getMayaPayBillsConfirmationViewItem()
        else { return }

        if let favoriteItem = confirmationItem.favoriteItem {
            let billerFavorite = BillerFavorite(favoriteItem: favoriteItem, billsPaymentDetails: billPaymentDetails, confirmationItem: confirmationItem)
            billerFormViewModel.updateFavoritesAction.apply(billerFavorite).start()
        } else {
            showReceiptBillsPayCheckoutFlowV3(billPaymentDetails: billPaymentDetails, updateBillerFavoriteResult: nil)
        }
    }

    func didRequestRedirectToDashboard(_ viewController: MayaConfirmationViewController) {
        delegate?.didRequestRedirectToDashboard(self)
    }
}
