//
//  MayaPurchaseFinancingCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 4/23/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
import AssetProvider
import ConfigurationService
import Flutter
import FlutterPluginRegistrant
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider

protocol MayaPurchaseFinancingCoordinatorDelegate: AnyObject {
    func didDismiss(coordinator: MayaPurchaseFinancingCoordinator, completion: (() -> Void)?)
}

class MayaPurchaseFinancingCoordinator: MayaFlutterBaseCoordinator, MayaAnalyticsDurationManagerProtocol {
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    weak var delegate: MayaPurchaseFinancingCoordinatorDelegate?

    private var sessionTimeoutResult: FlutterResult?
    private var profileResult: FlutterResult?

    let nextEventCode: Constants.PurchaseFinancing.EventCode = .next
    let backEventCode: Constants.PurchaseFinancing.EventCode = .back

    init(route: String? = nil, params: String? = nil, presenter: RouterProtocol? = nil, animated: Bool = false, completion: (() -> Void)? = nil) {
        super.init(
            flutterModule: .purchaseFinancing,
            route: route,
            routeParams: params,
            presenter: presenter,
            animated: animated,
            shouldPresentRouter: true
        )
    }

    override var flutterTogglesV2: [FlutterToggles: CSConfigV2<Bool>] {
        return [
            FlutterToggles.purchaseFinancing: configurationServiceV2.pfMvpEnabled
        ]
    }

    override func didDismiss(_ coordinator: MayaFlutterBaseCoordinator) {
        super.didDismiss(coordinator)
        delegate?.didDismiss(coordinator: self, completion: nil)
    }

    override func setCustomMethodHandler(method: FlutterMethod, call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch method {
        case .showUpdateProfileScreen:
            showUpdateProfileFlow(screen: .updateProfile, call)
            profileResult = result
        case .showWorkDetailsScreen:
            showUpdateProfileFlow(screen: .workDetails, call)
            profileResult = result
        case .showContactReferenceScreen:
            showUpdateProfileFlow(screen: .contactReference, call)
            profileResult = result
        case .requestBillsPay:
            guard let args = call.arguments as? [String: Any] else { return }
            let billerKey = Constants.Flutter.Key.biller
            let amountKey = Constants.Flutter.Key.amount
            let accountNumberKey = Constants.Flutter.Key.accountNumber

            let biller = args[billerKey] as? String ?? ""
            let amount = args[amountKey] as? String ?? ""
            let accountNumber = args[accountNumberKey] as? String ?? ""

            let billerItem = BillerItem(slug: biller, name: "", imageURL: nil, prefilledValues: [billerKey: biller, amountKey: amount, accountNumberKey: accountNumber])
            self.startPaymentFlow(billerItem)
        default: result(nil)
        }
    }
}

private extension MayaPurchaseFinancingCoordinator {
    func showUpdateProfileFlow(screen: MayaUpdateProfileScreens, _ call: FlutterMethodCall? = nil) {
        guard configurationService.updateProfileDeeplinkEnabled.value else { return }

        let coordinator = MayaUpdateProfileCoordinator(payload: nil, presenter: router)
        coordinator.presentationDirection = .rightToLeft
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)

        let sourceScreen = extractSourceScreen(from: call)
        coordinator.start(screen: screen, referrer: sourceScreen)
    }

    func extractSourceScreen(from call: FlutterMethodCall?) -> String? {
        guard let args = call?.arguments as? [String: Any],
              let sourceScreen = args[Constants.Flutter.PurchaseFinancing.sourceScreen] as? String else {
            return nil
        }
        return sourceScreen
    }

    func startPaymentFlow(_ billerItem: BillerItem, favoriteItem: FavoriteItem? = nil) {
        let coordinator = MayaPayBillsPaymentCoordinator(presenter: router)
        let paymentViewModel = MayaPayBillsPaymentViewModel(with: favoriteItem, and: billerItem)
        coordinator.viewModel = paymentViewModel
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.presentationDirection = .right
        addChild(coordinator)
        coordinator.start()
    }
}

// MARK: MayaUpdateProfileCoordinatorDelegate methods
extension MayaPurchaseFinancingCoordinator: MayaUpdateProfileCoordinatorDelegate {
    func didTapNext(_ coordinator: MayaUpdateProfileCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            self?.removeChild(coordinator)
        }
        self.profileResult?(nextEventCode.rawValue)
        self.profileResult = nil
    }

    func dismissCoordinator(_ coordinator: MayaUpdateProfileCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            self?.removeChild(coordinator)
        }
        self.profileResult?(backEventCode.rawValue)
        self.profileResult = nil
    }
}

// MARK: - MayaPayBillsPaymentCoordinatorDelegate Methods
extension MayaPurchaseFinancingCoordinator: MayaPayBillsPaymentCoordinatorDelegate {
    func didRequestRedirectToDashboard(_ coordinator: MayaPayBillsPaymentCoordinator) {
        removeChild(coordinator)
        guard let url = Constants.DeepLinkPath.dashboard.url else { return }
        router.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }
            self.routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
        }
    }

    func didTapBack(_ coordinator: MayaPayBillsPaymentCoordinator) {
        // do nothing
    }

    func didPaySuccessfully(_ coordinator: MayaPayBillsPaymentCoordinator) {
        router.popToRootViewController(animated: false)
        router.dismissModule(animated: true, completion: { [weak self, weak coordinator] in
            guard let self = self, let coordinator = coordinator else { return }
            self.removeChild(coordinator)
        })
    }

    func didReceiveError(_ coordinator: MayaPayBillsPaymentCoordinator, error: ErrorAlertViewModel?) {
        self.removeChild(coordinator)
        if let error = error {
            showErrorAlert(error, useMayaModal: true)
        }
    }

    func didRequestRedirectToPayBills(_ coordinator: MayaPayBillsPaymentCoordinator) {
        removeChild(coordinator)
        guard let url = Constants.DeepLinkPath.paybills.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
    }
}

// MARK: - DeepLinkHandler Methods
extension MayaPurchaseFinancingCoordinator: DeepLinkHandler {
    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .dashboard(.purchaseFinancing):
            return .opened(deepLink, nil)
        default:
            return .rejected(deepLink, .notHandled, identifier)
        }
    }
}
