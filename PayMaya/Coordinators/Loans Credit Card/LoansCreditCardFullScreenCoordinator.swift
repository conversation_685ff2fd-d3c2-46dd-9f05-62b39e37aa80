//
//  LoansCreditCardFullScreenCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> Jamolod on 1/29/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
import AssetProvider
import ConfigurationService
import Flutter
import FlutterPluginRegistrant
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider

protocol LoansCreditCardFullScreenCoordinatorDelegate: AnyObject {
    func didDismiss(coordinator: LoansCreditCardFullScreenCoordinator, completion: (() -> Void)?)
    func didRequestFullScreen(coordinator: LoansCreditCardFullScreenCoordinator)
    func didRequestFullScreen(coordinator: LoansCreditCardFullScreenCoordinator, route: String, args: [String: String], completion: (() -> Void)?)
}

class LoansCreditCardFullScreenCoordinator: MayaEKYCBaseCoordinator, MayaAnalyticsDurationManagerProtocol {
    @Inject var storeProvider: StoreProvider
    @Inject private var flutterEngineManager: FlutterEngineManager
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject private var ravenWrapper: RavenWrapper

    weak var delegate: LoansCreditCardFullScreenCoordinatorDelegate?

    private var credolabChannel: FlutterMethodChannel?
    var sessionTimeoutResult: FlutterResult?
    var profileResult: FlutterResult?
    var securityDepositResult: FlutterResult?
    var securityDepositArgs: [String: String]?
    let route: String
    var params: String?

    let nextEventCode = 1 // Event code for the user tapping the "Next" button
    let backEventCode = 0 // Event code for the user tapping the "Back" button

    private var initialRoute: String {
        var initialRoute = route
        if let params, !params.isEmpty {
            initialRoute += "?\(params)"
        }
        return initialRoute
    }

    init(route: String, params: String?, router: RouterProtocol = Router(), presenter: RouterProtocol? = nil) {
        self.route = route
        self.params = params
        super.init(router: router, presenter: presenter)

        flutterEngineManager.startEngine(with: .loansCreditCard, customRoute: initialRoute)
        setMethodHandler()

        let timeoutNotificationSignal = NotificationCenter.default.reactive.notifications(forName: NSNotification.Name(rawValue: sessionTimeoutSuccessNotification)).map { _ in }
        timeoutNotificationSignal.observe(on: UIScheduler()).observeValues { [weak self] in
            guard let self = self,
                  let databaseStore = storeProvider.target(DatabaseStore.self),
                  let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                  let token = user.token
            else {
                return
            }

            sessionTimeoutResult?(flutterEngineManager.responseGenerator.getSessionToken(token))
            sessionTimeoutResult = nil
        }?.addToDisposeBag(disposeBag)
    }

    override func start(completion: (() -> Void)? = nil) {
        if let engine = flutterEngineManager.getEngine(with: .loansCreditCard) {
            // Setup credolab support
            setCredolabMethodHandler(engine)

            let flutterViewController = FlutterViewController(engine: engine, nibName: nil, bundle: nil)
            flutterViewController.modalPresentationStyle = .fullScreen
            flutterViewController.view.backgroundColor = CommonAsset.MayaColors.Background.backgroundPrimaryWhite.color
            router.navigationController?.setNavigationBarHidden(true, animated: false)
            setRootModule(flutterViewController, animated: false)
            presenter?.present(router, animated: true, completion: completion)
        }
    }

    private func setMethodHandler() {
        flutterEngineManager.getChannel(with: .loansCreditCard)?.setMethodCallHandler { [weak self] call, result in
            guard let self = self else { return }

            if let method = FlutterMethod(rawValue: call.method) {
                switch method {
                case .getSessionToken:
                    handleGetSessionToken(result)
                case .getAccessToken:
                    handleGetAccessToken(result)
                case .getShieldSessionId:
                    result(flutterEngineManager.responseGenerator.getShieldSessionId(Shield.shared().sessionId))
                case .onSessionTimeout:
                    handleSessionTimeout(result)
                case .getPinningFingerprint:
                    handleGetPinningFingerprint(result)
                case .logAnalyticsEvent:
                    handleLogAnalyticsEvent(call, result)
                case .dismiss:
                    handleDismiss()
                    reloadCardsTab()
                case .getMin:
                    handleGetMin(result)
                case .getKycStatus:
                    handleGetKycStatus(result)
                case .showUpgradeScreen, .completeKyc:
                    showPreKYCFlow(call)
                case .showUpdateProfileScreen:
                    showUpdateProfileFlow(screen: .updateProfile, call)
                    profileResult = result
                case .showWorkDetailsScreen:
                    showUpdateProfileFlow(screen: .workDetails, call)
                    profileResult = result
                case .showContactReferenceScreen:
                    showUpdateProfileFlow(screen: .contactReference, call)
                    profileResult = result
                case .showAccountUnderReviewScreen:
                    showAccountUnderReview()
                    profileResult = result
                case .getUserInfo:
                    handleGetUserInfo(result)
                case .getToggle:
                    handleGetToggle(result)
                case .requestBillsPay:
                    handleRequestBillsPay(call)
                case .uploadDocument:
                    startIncomeDocumentation()
                case .openSecurityDepositAccount:
                    startSecurityDeposit(call, result: result)
                default: return
                }
            }
        }
    }

    private func handleRavenExtract() {
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
              configurationServiceV2.lendingDataExtractionEnabled.value
        else {
            return
        }

        ravenWrapper.extract(customerID: user.walletId,
                             transactionID: nil,
                             triggeredBy: Constants.Trigger.lending,
                             eventTrigger: Constants.EventTrigger.creditCard) { res in
            let attributes = [
                AnalyticsAttributeKey.triggeredBy: Constants.Trigger.lending,
                AnalyticsAttributeKey.eventTrigger: Constants.EventTrigger.creditCard
            ]

            switch res {
            case .success:
                self.analyticsService.logMayaEvents(action: .success(Analytics.MayaDataExtraction.extract), keyAttributes: attributes)
            case .failure:
                self.analyticsService.logMayaEvents(action: .failure(Analytics.MayaDataExtraction.extract), keyAttributes: attributes)
            }
        }
    }

    private func setCredolabMethodHandler(_ engine: FlutterEngine) {
        credolabChannel = FlutterMethodChannel(name: Constants.Flutter.CreditScoring.credolabChannel, binaryMessenger: engine.binaryMessenger)
        credolabChannel?.setMethodCallHandler { [weak self] call, result in
            guard let method = FlutterMethod(rawValue: call.method),
                  let self
            else { return }

            if method == .collect {
                CredolabDataService().collect { res in
                    switch res {
                    case .success(let data):
                        result(data)
                    case .error(let errorCode, let errorMessage):
                        result(FlutterError(code: String(errorCode), message: errorMessage, details: nil))
                    @unknown default:
                        result(FlutterError(code: String(-1), message: nil, details: nil))
                    }
                }

                // trigger raven data extraction
                handleRavenExtract()
            }
        }
    }

    private func handleGetSessionToken(_ result: FlutterResult) {
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
              let token = user.token else { return }
        result(self.flutterEngineManager.responseGenerator.getSessionToken(token))
    }

    private func handleGetAccessToken(_ result: FlutterResult) {
        guard let encryptedStore = self.storeProvider.target(EncryptedDefaultsStore.self),
              let authorization = encryptedStore.read(EncryptedDefaultsStoreId.authorization, type: Authorization.self).value else { return }
        result(self.flutterEngineManager.responseGenerator.getAccessToken(authorization.accessToken))
    }

    private func handleSessionTimeout(_ result: @escaping FlutterResult) {
        self.sessionTimeoutResult = result
        NotificationCenter.default.post(name: NSNotification.Name(rawValue: sessionTimeoutNotification), object: nil)
    }

    private func handleGetPinningFingerprint(_ result: FlutterResult) {
        guard let propertyListStore = self.storeProvider.target(PropertyListStore.self),
              let pinningFingerprint = propertyListStore.read(PropertyListStoreId.pinningFingerprint).value as? String else { return }
        result(self.flutterEngineManager.responseGenerator.getPinningFingerprint(pinningFingerprint))
    }

    private func handleLogAnalyticsEvent(_ call: FlutterMethodCall, _ result: FlutterResult) {
        guard let jsonString = call.arguments as? String,
              let jsonData = jsonString.data(using: .utf8),
              let dictionary = try? JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
              let name = dictionary["event_name"] as? String
        else { return }

        let attributes = dictionary["attributes"] as? [String: Any]
        self.analyticsService.logMayaEvents(name: name, attributes: attributes)
    }

    private func handleDismiss(animated: Bool = true, completion: (() -> Void)? = nil) {
        self.presenter?.dismissModule(animated: animated) { [weak self] in
            guard let self = self else { return }
            flutterEngineManager.getChannel(with: .loansCreditCard)?.setMethodCallHandler(nil)
            flutterEngineManager.clearEngine(with: .loansCreditCard)
            flutterEngineManager.initializeEngine(with: .loansCreditCard)
            delegate?.didDismiss(coordinator: self, completion: completion)
        }
    }

    private func handleGetMin(_ result: FlutterResult) {
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
              let msisdn = user.msisdn else { return }
        result(self.flutterEngineManager.responseGenerator.getMin(msisdn))
    }

    private func handleGetKycStatus(_ result: FlutterResult) {
        if let databaseStore = self.storeProvider.target(DatabaseStore.self),
           let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User {
            result(self.flutterEngineManager.responseGenerator.getKycStatus(kycStatus: user.kycStatus))
            return
        }
        result(self.flutterEngineManager.responseGenerator.getKycStatus(kycStatus: "kyc0"))
    }

    private func handleGetUserInfo(_ result: FlutterResult) {
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
        else { return }

        let response = self.flutterEngineManager.responseGenerator.generateUserInfo(firstName: user.profile?.firstName, lastName: user.profile?.lastName, middleName: user.profile?.middleName)
        result(response)
    }

    private func handleGetToggle(_ result: FlutterResult) {
        let s3Toggles = [
            FlutterToggles.simplifiedCheckout: self.configurationService.mayaLoansSimplifiedCheckoutEnabled,
            FlutterToggles.loansV3: self.configurationService.mayaLoansV3Enabled,
            FlutterToggles.loansICL: self.configurationService.mayaLoansICLEnabled,
            FlutterToggles.loansICLInitialEligibilityCheck: self.configurationService.iclInitialEligibilityCheckEnabled,
            FlutterToggles.loansICLDropdownV2: self.configurationService.iclDropdownV2Enabled,
            FlutterToggles.loansICLPhaseTwoV1: self.configurationService.iclPhaseTwoV1Enabled,
            FlutterToggles.bnplDeepFreeze: self.configurationService.bnplDeepFreeze,
            FlutterToggles.loansICLPhaseTwoV2: self.configurationService.iclPhaseTwoV2Enabled,
            FlutterToggles.loansCreditCard: self.configurationService.loansCreditCardEnabled,
            FlutterToggles.loansCreditCardDetailsV2: self.configurationService.loansCreditCardDetailsV2Enabled,
            FlutterToggles.loansCreditCardMayaBlack: self.configurationService.loansCreditCardMayaBlackEnabled
        ]

        let splitToggles = [
            FlutterToggles.ccCreditScoring: self.configurationServiceV2.ccCreditScoringEnabled,
            FlutterToggles.ccDBL: self.configurationServiceV2.ccDBLEnabled,
            FlutterToggles.ccSkipCreditScoring: self.configurationServiceV2.ccSkipCreditScoringEnabled
        ]

        let togglesV1 = self.flutterEngineManager.responseGenerator.getToggles(from: s3Toggles)
        let togglesV2 = self.flutterEngineManager.responseGenerator.getTogglesV2(from: splitToggles)

        let mergedToggles = togglesV1.merged(with: togglesV2)

        self.analyticsService.logMayaEvents(name: Analytics.LendingToggles.creditCard.rawValue, attributes: mergedToggles)

        result(mergedToggles)
    }

    private func handleRequestBillsPay(_ call: FlutterMethodCall) {
        let routeKey = Constants.Flutter.LoansCreditCard.route
        let otherFieldsKey = Constants.Flutter.Key.otherFields
        let billerKey = Constants.Flutter.ICL.Keys.biller
        let amountKey = Constants.Flutter.ICL.Keys.amount
        let maskedAccountNumberKey = Constants.Flutter.ICL.Keys.accountNumber
        let accountNumberKey = Constants.Flutter.Key.accountNumber
        let cardIdKey = Constants.Flutter.Key.cardId

        guard let args = call.arguments as? [String: Any],
              let route = args[routeKey] as? String,
              let encodedRoute = route.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
              let urlComponents = URLComponents(string: encodedRoute),
              let queryItems = urlComponents.queryItems
        else {
            self.analyticsService.logMayaEvents(action: .failure(Analytics.LoansCreditCard.repayment), keyAttributes: [AnalyticsAttributeKey.reason: routeKey])
            return
        }

        let queryDictionary = Dictionary(uniqueKeysWithValues: queryItems.map { ($0.name, $0.value) })

        guard let otherFieldsParam = queryDictionary[otherFieldsKey],
              let otherFieldsData = otherFieldsParam?.data(using: .utf8),
              let otherFields = try? JSONSerialization.jsonObject(with: otherFieldsData, options: []) as? [String: Any]
        else {
            self.analyticsService.logMayaEvents(action: .failure(Analytics.LoansCreditCard.repayment), keyAttributes: [AnalyticsAttributeKey.reason: otherFieldsKey])
            return
        }

        let biller = queryDictionary[billerKey] as? String ?? String.empty
        let amount = queryDictionary[amountKey] as? String ?? String.empty
        let maskedAccountNumber = queryDictionary[maskedAccountNumberKey] as? String ?? String.empty
        let accountNumber = otherFields[accountNumberKey] as? String ?? String.empty
        let cardId = otherFields[cardIdKey] as? String ?? String.empty

        let billerItem = BillerItem(slug: biller, name: "", imageURL: nil, prefilledValues: [billerKey: biller, amountKey: amount, maskedAccountNumberKey: maskedAccountNumber, accountNumberKey: accountNumber, cardIdKey: cardId])
        self.startPaymentFlow(billerItem)
    }

    private func startPaymentFlow(_ billerItem: BillerItem, favoriteItem: FavoriteItem? = nil) {
        let coordinator = MayaPayBillsPaymentCoordinator(presenter: router)
        let paymentViewModel = MayaPayBillsPaymentViewModel(with: favoriteItem, and: billerItem)
        coordinator.viewModel = paymentViewModel
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.presentationDirection = .right
        addChild(coordinator)
        coordinator.start()
    }

    private func startIncomeDocumentation(completion: (() -> Void)? = nil) {
        let coordinator = MayaIncomeDocumentationCoordinator(presenter: router)
        coordinator.routeActionDelegate = self
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    private func startSecurityDeposit(_ call: FlutterMethodCall, result: FlutterResult? = nil, completion: (() -> Void)? = nil) {
        let coordinator = MayaSecurityDepositCoordinator(presenter: router)
        securityDepositArgs = call.arguments as? [String: String]
        securityDepositResult = result
        coordinator.routeActionDelegate = self
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    private func reloadCardsTab() {
        let validRoutes: Set<String> = [
            Constants.Flutter.LoansCreditCard.applicationPendingScreen,
            Constants.Flutter.LoansCreditCard.repaymentScreen,
            Constants.Flutter.LoansCreditCard.securityDepositScreen,
            Constants.Flutter.LoansCreditCard.dblSecuritySelectionScreen,
            Constants.Flutter.LoansCreditCard.cardOverviewScreen
        ]

        if validRoutes.contains(route) {
            self.flutterEngineManager.getChannel(with: .cardsTab)?.invokeMethod(FlutterMethod.reloadHome.rawValue, arguments: nil)
        }
    }
}

// MARK: - DeepLinkHandler Methods
extension LoansCreditCardFullScreenCoordinator: DeepLinkHandler {
    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .loansCreditCard:
            return .opened(deepLink, nil)
        default:
            return .rejected(deepLink, .notHandled, identifier)
        }
    }
}

// MARK: - MayaPreKYCCoordinatorDelegate Methods
extension LoansCreditCardFullScreenCoordinator: MayaPreKYCCoordinatorDelegate {
    private func showPreKYCFlow(_ call: FlutterMethodCall, completion: (() -> Void)? = nil) {
        let configService = ContainerWrapper.shared.resolve(ConfigurationService.self)
        let coordinator = setupMayaPreKYCCoordinator()

        if configService.loansCreditCardMayaBlackEnabled.value {
            let entrypointKey = Constants.Flutter.LoansCreditCard.entrypoint
            guard let args = call.arguments as? [String: Any],
                  let entrypoint = args[entrypointKey] as? String
            else {
                analyticsService.logMayaEvents(
                    action: .failure(Analytics.LoansCreditCard.kyc),
                    keyAttributes: [AnalyticsAttributeKey.reason: entrypointKey]
                )
                return
            }
            coordinator.startWithEntryPoint(entryPoint: entrypoint)
        } else {
            coordinator.start()
        }
    }

    private func setupMayaPreKYCCoordinator() -> MayaPreKYCCoordinator {
        let coordinator: MayaPreKYCCoordinator
        coordinator = MayaPreKYCCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        coordinator.routeActionDelegate = self
        coordinator.analyticsModule = Analytics.LoansDashboard()

        addChild(coordinator)
        return coordinator
    }

    func preKYCCoordinatorDidFinishKYC(_ coordinator: MayaPreKYCCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
    }

    func preKYCCoordinatorDidTapBack(_ coordinator: MayaPreKYCCoordinator) {
        removeChild(coordinator)
    }
}

extension LoansCreditCardFullScreenCoordinator {
    private func showAccountUnderReview() {
        let coordinator: Coordinator
        coordinator = EKYCZolozReviewCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    private func showUpdateProfileFlow(screen: MayaUpdateProfileScreens, _ call: FlutterMethodCall? = nil) {
        if configurationService.updateProfileDeeplinkEnabled.value {
            let coordinator = MayaUpdateProfileCoordinator(payload: nil, presenter: router)
            coordinator.presentationDirection = .rightToLeft
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            coordinator.delegate = self
            addChild(coordinator)

            let sourceScreenKey = Constants.Flutter.LoansCreditCard.sourceScreen
            guard
                let args = call?.arguments as? [String: Any],
                let sourceScreen = args[sourceScreenKey] as? String
            else {
                analyticsService.logMayaEvents(
                    action: .failure(Analytics.LoansCreditCard.updateProfile),
                    keyAttributes: [AnalyticsAttributeKey.reason: sourceScreenKey]
                )
                return
            }

            coordinator.start(screen: screen, referrer: sourceScreen)
        }
    }
}

// MARK: MayaUpdateProfileCoordinatorDelegate methods
extension LoansCreditCardFullScreenCoordinator: MayaUpdateProfileCoordinatorDelegate {
    func didTapNext(_ coordinator: MayaUpdateProfileCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            self?.removeChild(coordinator)
        }
        self.profileResult?(nextEventCode)
        self.profileResult = nil
    }

    func dismissCoordinator(_ coordinator: MayaUpdateProfileCoordinator) {
        router.dismissModule(animated: true) { [weak self] in
            self?.removeChild(coordinator)
        }
        self.profileResult?(backEventCode)
        self.profileResult = nil
    }
}

// MARK: - MayaPayBillsPaymentCoordinatorDelegate Methods
extension LoansCreditCardFullScreenCoordinator: MayaPayBillsPaymentCoordinatorDelegate {
    func didRequestRedirectToDashboard(_ coordinator: MayaPayBillsPaymentCoordinator) {
        self.handleDismiss()
    }

    func didTapBack(_ coordinator: MayaPayBillsPaymentCoordinator) {
        // do nothing
    }

    func didPaySuccessfully(_ coordinator: MayaPayBillsPaymentCoordinator) {
        self.handleDismiss()
    }

    func didReceiveError(_ coordinator: MayaPayBillsPaymentCoordinator, error: ErrorAlertViewModel?) {
        self.removeChild(coordinator)
        if let error = error {
            showErrorAlert(error, useMayaModal: true)
        }
    }

    func didRequestRedirectToPayBills(_ coordinator: MayaPayBillsPaymentCoordinator) {
        removeChild(coordinator)
        guard let url = Constants.DeepLinkPath.paybills.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
    }
}

// MARK: MayaIncomeDocumentationCoordinatorDelegate methods
extension LoansCreditCardFullScreenCoordinator: MayaIncomeDocumentationCoordinatorDelegate {
    func didDismiss(coordinator: MayaIncomeDocumentationCoordinator) {
        handleDismiss(animated: false)
        removeChild(coordinator)
    }

    func didSetContentHeader(coordinator: MayaIncomeDocumentationCoordinator) -> [String] {
        return [L10n.Loans.Icl.Dbl.Upload.header, L10n.Loans.Icl.Dbl.Upload.subheader]
    }

    func didSubmitDocument(coordinator: MayaIncomeDocumentationCoordinator, data: [String: Any]) {
        handleDismiss(animated: false) {
            self.delegate?.didRequestFullScreen(coordinator: self)
        }
    }
}

// MARK: - MayaSecurityDepositCoordinatorDelegate Methods
extension LoansCreditCardFullScreenCoordinator: MayaSecurityDepositCoordinatorDelegate {
    func didSuccessfullyCreateDeposit(coordinator: MayaSecurityDepositCoordinator, result: [String: String]) {
        securityDepositResult?(result)

        flutterEngineManager.getChannel(with: .securityDeposit)?.setMethodCallHandler(nil)
        flutterEngineManager.clearEngine(with: .securityDeposit)
        flutterEngineManager.initializeEngine(with: .securityDeposit)

        removeChild(coordinator)
    }

    func didRequestInputData(coordinator: MayaSecurityDepositCoordinator) -> [String: String] {
        guard let args = securityDepositArgs,
              let applicationId = args[Constants.Flutter.SecurityDeposit.loanApplicationId],
              let limitMultiplier = args[Constants.Flutter.SecurityDeposit.creditLimitMultiplier]
        else {
            return [:]
        }
        let params = args

        return [
            Constants.Flutter.SecurityDeposit.loanApplicationId: applicationId,
            Constants.Flutter.SecurityDeposit.creditLimitMultiplier: limitMultiplier
        ]
    }

    func didDismiss(coordinator: MayaSecurityDepositCoordinator) {
        removeChild(coordinator)
    }
}
