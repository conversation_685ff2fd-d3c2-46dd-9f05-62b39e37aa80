//
//  MayaSendMoneyCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 6/29/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import ConfigurationService
import Injector
import MayaCoreData
import MayaFormatterManager
import Moya
import ReactiveSwift
import StoreProvider
import UIKit

protocol MayaSendMoneyCoordinatorDelegate: AnyObject {
    func didRequestRedirectToDashboard(_ coordinator: MayaSendMoneyCoordinator)
}

class MayaSendMoneyCoordinator: MayaProgressBarCoordinator, DeepLinkHandler, AnalyticsProtocol, AnalyticsServiceProtocol {
    typealias AnalyticsSendMoney = AnalyticsConstants.Screen.SendMoney
    let module = Module.sendMoney
    var viewModel: MayaSendMoneyViewModel?
    var transferDetails: SendMoneyQR?
    var sendMoneyFavorite: FavoriteItem?
    var sendMoneyViewController: MayaSendMoneyViewController?
    var user: User?
    var completion: (() -> Void)?

    @Inject var storeProvider: StoreProvider
    @Inject var configurationService: ConfigurationService
    @Inject private var formatter: MayaFormatterManager
    @Inject var analyticsService: AnalyticsService

    private var successBottomSheetCoordinator: MayaAlertBottomSheetCoordinator?
    private var isAddedToFavorite = false

    var analyticsSourcePage: AnalyticsConstants.Screen.SendMoney.Page

    weak var delegate: MayaSendMoneyCoordinatorDelegate?

    override var progressSegments: Int {
        return 2
    }

    init(presenter: RouterProtocol? = nil,
         analyticsSourcePage: AnalyticsConstants.Screen.SendMoney.Page) {
        self.analyticsSourcePage = analyticsSourcePage
        super.init(presenter: presenter)
        let databaseStore = storeProvider.target(DatabaseStore.self)
        user = databaseStore?.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
    }

    private var isAppEventV2SendMoneyEnabled: Bool {
        return configurationService.appEventV2SendMoneyEnabled.value
    }

    override func start(completion: (() -> Void)? = nil) {
        checkMaintenanceMode(completion: completion)
    }

    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .dashboard(.sendMoney(.requestMoney(let param))):
            if case .requestMoneyDetails(let requestMoneyEncoded) = param, let details = requestMoneyEncoded.decodeData(type: RequestMoneyDetails.self) {
                viewModel?.requestMoneyDetailsProperty.value = details
            }
            return .opened(deepLink, nil)
        default: return .rejected(deepLink, .notHandled, identifier)
        }
    }

    // MARK: - BackActionDelegate
    override func didTapBack(_ coordinator: Coordinator, completion: (() -> Void)? = nil) {
        if coordinator == self {
            backActionDelegate?.didTapBack(self, completion: completion)
        } else {
            super.didTapBack(coordinator, completion: completion)
        }
    }
}

extension MayaSendMoneyCoordinator {
    func startSendMoneyFlow(completion: (() -> Void)? = nil) {
        let sendMoneyViewController = StoryboardScene.SendMoney.mayaSendMoneyViewController.instantiate()
        viewModel = MayaSendMoneyViewModel(favoriteItem: sendMoneyFavorite)
        sendMoneyViewController.bind(viewModel)
        sendMoneyViewController.delegate = self
        sendMoneyViewController.backActionDelegate = self
        sendMoneyViewController.routeActionDelegate = self
        sendMoneyViewController.navigationBarTitleView = progressBarView
        sendMoneyViewController.analyticsSourcePage = analyticsSourcePage
        self.sendMoneyViewController = sendMoneyViewController
        setRootModule(sendMoneyViewController, animated: false)

        if let transfer = transferDetails {
            let formDetails = SendMoneyFormDetails(transfer)
            sendMoneyViewController.autoFillForm(formDetails)
        } else if let favoriteItem = sendMoneyFavorite, let template = favoriteItem.template as? SendMoneyFavoriteTemplate {
            let formDetails = SendMoneyFormDetails(template)
            sendMoneyViewController.autoFillForm(formDetails)
        }

        if let presenter = presenter,
            presenter.toPresentable().presentedViewController != router.toPresentable() {
            presenter.present(router, animated: true, completion: completion)
        }

        setupUpdateFavorites()
    }

    func startSaveToFavoritesFlow(executedTransfer: ExecutedTransferProtocol, confirmationItem: MayaSendMoneyConfirmationViewItem) {
        guard let sourceCoordinator = successBottomSheetCoordinator else { return }
        let viewModel = MayaSendMoneyAddToFavoriteViewModel(executedTransfer: executedTransfer, confirmationItem: confirmationItem)
        let coordinator = MayaAddToFavoritesCoordinator(presenter: sourceCoordinator.router, viewModel: viewModel, analyticsAttributeModule: ReceiptAttributeModule.sendMoney.rawValue)
        coordinator.delegate = self
        coordinator.backActionDelegate = sourceCoordinator
        coordinator.routeActionDelegate = sourceCoordinator
        sourceCoordinator.addChild(coordinator)
        coordinator.start()
    }

    private func setupUpdateFavorites() {
        guard let viewModel else { return }

        viewModel.updateFavoritesAction?.values
            .observe(on: UIScheduler())
            .observeValues { [weak self] result in
                guard
                    let self = self,
                    case .updateSuccess(let alertViewModel, let transfer) = result,
                    let alertViewModel = alertViewModel as? MayaAlertViewModel,
                    let confirmationItem = viewModel.getConfirmationViewItem()
                else { return }
                self.showSuccessBottomSheet(viewModel: alertViewModel, transfer: transfer, confirmationItem: confirmationItem)
            }?.addToDisposeBag(disposeBag)

        viewModel.updateFavoritesAction.isExecuting.signal.observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                guard let self = self else { return }
                let presenter = self.presenter == nil ? self.router.toPresentable() : self.presenter?.toPresentable()
                isExecuting ? presenter?.showMayaLoader() : presenter?.hideMayaLoader()
            }?.addToDisposeBag(disposeBag)
    }

    private func showSuccessBottomSheet(viewModel: MayaAlertViewModel?, transfer: ExecutedTransferProtocol, confirmationItem: MayaSendMoneyConfirmationViewItem) {
        let title = L10n.Sendmoney.Success.Bottomsheet.title(formatter.number.currencyPesoStringValue(string: confirmationItem.amount))
        let message = L10n.Sendmoney.Success.Bottomsheet.message(getRecipient(from: confirmationItem))
        let image = CommonAsset.Images.Common.imageStarSuccess.image
        let alertViewModel = viewModel == nil ? MayaAlertViewModel(title: title, message: message, image: image, actionsAxis: .horizontal) : viewModel!
        let coordinator = MayaAlertBottomSheetCoordinator(presenter: router, viewModel: alertViewModel)
        successBottomSheetCoordinator = coordinator

        let alert1 = MayaAlertAction(title: CommonStrings.Common.View.receipt, style: .secondary) { [weak self, weak coordinator] in
            guard let self = self, let coordinator = coordinator else { return }

            logViewReceiptButtonTap()

            self.router.dismissModule(animated: true) {
                self.removeChild(coordinator)
                self.showReceipt(executedTransfer: transfer, confirmationItem: confirmationItem)
            }
        }
        let alert2 = MayaAlertAction(title: CommonStrings.Common.done, style: .primary, handler: { [weak self, weak coordinator] in
            guard let self = self, let coordinator = coordinator else { return }

            logDoneButtonTap()

            self.removeChild(coordinator)
            self.delegate?.didRequestRedirectToDashboard(self)
        })
        alertViewModel.addAction(alert1)
        alertViewModel.addAction(alert2)

        if shouldShowSaveToFavorites() {
            let additionalButton = MayaAlertAdditionalInfoType.button(icon: CommonAsset.Images.Icons.iconGreenHeart.image, message: CommonStrings.Favorites.Save.spiel) { [weak self] in
                guard let self = self else { return }

                logSaveToFavoritesButtonTap()

                self.startSaveToFavoritesFlow(executedTransfer: transfer, confirmationItem: confirmationItem)
            }
            alertViewModel.additionalInfo = additionalButton
        }

        coordinator.analyticsModuleType = .sheet(Analytics.SendMoneySuccess())
        coordinator.routeActionDelegate = self
        coordinator.backActionDelegate = self

        logShowSuccessBottomSheet(hasAuthChallengeID: confirmationItem.createdTransfer.authChallengeId != nil)
        addChild(coordinator)
        coordinator.start()
    }

    private func showReceipt(executedTransfer: ExecutedTransferProtocol, confirmationItem: MayaSendMoneyConfirmationViewItem) {
        let viewModel = MayaSendMoneyReceiptViewModel(executedTransfer: executedTransfer, item: confirmationItem, shouldShowSaveToFavorites: shouldShowSaveToFavorites())
        let coordinator = MayaReceiptCoordinator(presenter: router, viewModel: viewModel, attributeModule: .sendMoney)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    private func getRecipient(from confirmationItem: MayaSendMoneyConfirmationViewItem) -> String {
        var recipient: String = confirmationItem.recipient
        if let publicName = confirmationItem.createdTransfer.displayName?.fullDisplayName {
            recipient = publicName
        } else if let contactName = confirmationItem.contactNumber?.fullName {
            recipient = contactName
        }
        return recipient
    }

    private func shouldShowSaveToFavorites() -> Bool {
        guard let confirmationItem = viewModel?.getConfirmationViewItem() else { return false }
        let reachedLimit = confirmationItem.favoriteLimit?.typeLimit(.moneysend)?.reachedLimit ?? false
        return confirmationItem.favoriteItem == nil && !reachedLimit && !isAddedToFavorite
    }
}

// MARK: - MayaServiceMaintenanceProtocol Methods
extension MayaSendMoneyCoordinator: MayaServiceMaintenanceProtocol {
    func startNormalFlow(completion: (() -> Void)? = nil) {
        startPreKYCFlow(completion: completion)
    }
}

// MARK: - SendMoneyViewControllerDelegate Methods
extension MayaSendMoneyCoordinator: SendMoneyViewControllerDelegate {
    func didTapViewAllFavorites(_ viewController: UIViewController) {
        guard let favoritesDeepLink = FavoriteType.moneysend.deepLinkURL else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: favoritesDeepLink, completion: nil)
    }

    func didTapViewAllContacts(_ viewController: UIViewController) {
        startContactsFlow()
    }

    func didTapContinue(_ viewController: UIViewController) {
        previousViewController = viewController
        showSendMoneyConfirmation()
    }

    func didTapPreviewDesign(_ viewController: UIViewController, viewModel: MayaSendMoneyDesignCardPreviewViewModel) {
        startSendMoneyDesignCardPreviewFlow(viewModel: viewModel)
    }
}

// MARK: Navigation Functions
extension MayaSendMoneyCoordinator {
    func startPreKYCFlow(completion: (() -> Void)? = nil) {
        let coordinator = MayaPreKYCCoordinator(router: router, presenter: presenter)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func showSendMoneyConfirmation() {
        guard let viewItems = viewModel?.getConfirmationViewItem() else { return }
        let confirmationViewModel = MayaSendMoneyConfirmationViewModel(item: viewItems)
        let viewController = StoryboardScene.Others.mayaSendMoneyBankTransferConfirmationViewController.instantiate()
        viewController.navigationBarTitleView = progressBarView
        viewController.currentPageNumberForProgress = 2
        viewController.totalPagesForProgress = progressSegments
        viewController.backActionDelegate = self
        viewController.delegate = self
        viewController.sendMoneyDelegate = self
        viewController.routeActionDelegate = self
        viewController.analyticsSourcePage = .sendMoney(.form)
        viewController.bind(confirmationViewModel)
        router.push(viewController, animated: false, popCompletion: nil)
    }

    func proceedToKYC(completion: (() -> Void)? = nil) {
        if isShortenedFlowEnabled {
            let coordinator = EKYCZolozCoordinator(presenter: router)
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            coordinator.delegate = self
            coordinator.analyticsModule = Analytics.SendMoney()
            addChild(coordinator)
            coordinator.start()
        }
    }

    func proceedToReviewKYC() {
        if isShortenedFlowEnabled {
            let coordinator = EKYCZolozReviewCoordinator(presenter: router)
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            addChild(coordinator)
            coordinator.start()
        }
    }

    func proceedToReviewEDD() {
        let coordinator = EDDReviewCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startSendMoneyDesignCardPreviewFlow(viewModel: MayaSendMoneyDesignCardPreviewViewModel) {
        let coordinator = MayaSendMoneyThemeCardPreviewCoordinator(viewModel: viewModel, presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startContactsFlow() {
        let contactsCoordinator = MayaContactsCoordinator(presenter: router)
        contactsCoordinator.shouldIncludeOwnNumber = false
        contactsCoordinator.backActionDelegate = self
        contactsCoordinator.routeActionDelegate = self
        contactsCoordinator.delegate = self
        contactsCoordinator.presentationDirection = .right
        addChild(contactsCoordinator)
        contactsCoordinator.start()
    }

    func showAboutVerifiedSellerScreen() {
        let viewController = StoryboardScene.Others.mayaAboutVerifiedSellerViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.routeActionDelegate = self
        router.present(viewController, animated: true, completion: nil)
    }

    func startMFAChallengeFlow(mfaId: String) {
        guard let confirmationItem = viewModel?.getConfirmationViewItem() else { return }
        let otpType: OTPType = .sendMoney(otpId: mfaId, confirmatioItem: confirmationItem)
        let otpCoordinator = MayaOTPCoordinator(presenter: router, otpType: otpType)
        addChild(otpCoordinator)
        otpCoordinator.delegate = self
        otpCoordinator.backActionDelegate = self
        otpCoordinator.routeActionDelegate = self
        dispatchToMain { [weak otpCoordinator] in
            guard let coordinator = otpCoordinator else { return }
            coordinator.start()
        }
    }
}

// MARK: - MayaPreKYCCoordinatorDelegate Methods
extension MayaSendMoneyCoordinator: MayaPreKYCCoordinatorDelegate {
    func preKYCCoordinatorDidFinishKYC(_ coordinator: MayaPreKYCCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
        startSendMoneyFlow(completion: completion)
    }
}

// MARK: - MayaContactsCoordinatorDelegate Methods
extension MayaSendMoneyCoordinator: MayaContactsCoordinatorDelegate {
    func didSelectContactNumber(_ coordinator: MayaContactsCoordinator, contactNumber: ContactNumber) {
        viewModel?.selectedContactNumberProperty.value = contactNumber
    }

    func didTapBack(_ coordinator: MayaContactsCoordinator, isContactPermissionAllowed: Bool) {
        // Do nothing
    }
}

// MARK: - MayaReceiptCoordinatorDelegate Methods
extension MayaSendMoneyCoordinator: MayaReceiptCoordinatorDelegate {
    func didTapAddToFavorites(_ coordinator: MayaReceiptCoordinator) {
        // Do nothing
    }

    func didTapClose(_ coordinator: MayaReceiptCoordinator) {
        removeChild(coordinator)
        delegate?.didRequestRedirectToDashboard(self)
    }
}

// MARK: - MayaSendMoneyThemeCardPreviewViewControllerDelegate
extension MayaSendMoneyCoordinator: MayaSendMoneyThemeCardPreviewCoordinatorDelegate {
    func didChangeDecoration(_ coordinator: Coordinator, decoration: SendMoneyDecoration) {
        guard let viewModel else { return }
        viewModel.selectedDecoration.value = decoration
    }
}

// MARK: - EDDReviewCoordinatorDelegate Methods
extension MayaSendMoneyCoordinator: EDDReviewCoordinatorDelegate {
    func didReceiveError(_ error: PayMayaError, coordinator: EDDReviewCoordinator) {
        self.removeChild(coordinator)
        if let errorViewModel = error.viewModel {
            showErrorAlert(errorViewModel)
        }
    }
}

// MARK: - MayaConfirmationViewControllerDelegate Methods
extension MayaSendMoneyCoordinator: MayaSendMoneyBankTransferConfirmationViewControllerDelegate {
    func didFinishContinueAction(_ viewController: MayaSendMoneyBankTransferConfirmationViewController, response: Response) {
        guard let confirmationItem = viewModel?.getConfirmationViewItem() else { return }

        if let otpResponse = try? response.map(OTPResponse.self) {
            startMFAChallengeFlow(mfaId: otpResponse.otpId)
        } else if let createdTransfer = try? response.map(CreatedTransfer.self),
                  let authChallengeId = createdTransfer.authChallengeId {
            startMFAChallengeFlow(mfaId: authChallengeId)
        } else if let transfer = try? response.map(ExecutedTransfer.self) {
            if let favoriteItem = confirmationItem.favoriteItem {
                let transferFavorite = TransferFavorite(favoriteItem: favoriteItem, executedTransfer: transfer, confirmationItem: confirmationItem)
                viewModel?.updateFavoritesAction.apply(transferFavorite).start()
            } else {
                showSuccessBottomSheet(viewModel: nil, transfer: transfer, confirmationItem: confirmationItem)
            }
        } else if let transferV2 = try? response.map(ExecutedTransferV2.self) {
            if let favoriteItem = confirmationItem.favoriteItem {
                let transferFavorite = TransferFavorite(favoriteItem: favoriteItem, executedTransfer: transferV2, confirmationItem: confirmationItem)
                viewModel?.updateFavoritesAction.apply(transferFavorite).start()
            } else {
                showSuccessBottomSheet(viewModel: nil, transfer: transferV2, confirmationItem: confirmationItem)
            }
        }
    }
}

// MARK: - MayaSendMoneyConfirmationViewControllerDelegate Methods
extension MayaSendMoneyCoordinator: MayaSendMoneyConfirmationViewControllerDelegate {
    func didTapVerifiedSeller(_ viewController: MayaSendMoneyBankTransferConfirmationViewController) {
        showAboutVerifiedSellerScreen()
    }

    func didTapViewDesignCard(_ viewController: MayaSendMoneyBankTransferConfirmationViewController) {
        guard let activeProvider = viewModel?.activePersonalizationProvider.value else { return }

        if case .paymaya = activeProvider, let designCardDetails = viewModel?.designCardPreviewDetails {
            let previewViewModel = MayaSendMoneyDesignCardPreviewViewModel(details: designCardDetails)
            startSendMoneyDesignCardPreviewFlow(viewModel: previewViewModel)
        }
    }
}

// MARK: - MayaOTPCoordinatorDelegate Methods
extension MayaSendMoneyCoordinator: MayaOTPCoordinatorDelegate {
    func didReceiveOTPError(error: PayMayaError, _ coordinator: MayaOTPCoordinator) {
        guard let viewController = sendMoneyViewController else { return }
        removeChild(coordinator)
        router.dismissModule(animated: true) { [weak self] in
            self?.router.popToViewController(viewController)
            viewController.resetForm()
        }
    }

    func didReceivePostAuthenticationActionError(_ coordinator: MayaOTPCoordinator) {
        guard let viewController = sendMoneyViewController else { return }
        removeChild(coordinator)
        router.dismissModule(animated: true) { [weak self] in
            self?.router.popToViewController(viewController)
            viewController.resetForm()
        }
    }

    func didRequestDisplayMayaAlertViewModel(viewModel: MayaAlertViewModel, response: Codable?, coordinator: MayaOTPCoordinator) {
        guard let confirmationItem = self.viewModel?.getConfirmationViewItem(),
            let transfer = response as? ExecutedTransfer else {
            return
        }

        removeChild(coordinator)
        router.dismissModule(animated: true) { [weak self] in
            self?.showSuccessBottomSheet(viewModel: viewModel, transfer: transfer, confirmationItem: confirmationItem)
        }
    }

    func didRequestDisplayMayaReceiptViewModel(viewModel: MayaReceiptViewModelProtocol, response: Codable?, coordinator: MayaOTPCoordinator) {
        // Do nothing
    }
}

// MARK: - MayaAddToFavoritesCoordinatorDelegate Methods
extension MayaSendMoneyCoordinator: MayaAddToFavoritesCoordinatorDelegate {
    func didSuccessfullyAddedFavorite(_ coordinator: MayaAddToFavoritesCoordinator) {
        successBottomSheetCoordinator?.router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
            self.isAddedToFavorite = true
            self.successBottomSheetCoordinator?.reloadWithAdditionalInfo(nil)
        })
    }

    func didTapBackFromAddToFavorites(_ coordinator: MayaAddToFavoritesCoordinator) {
        // Do nothing
    }
}

// MARK: - EKYCZolozCoordinatorDelegate Methods
extension MayaSendMoneyCoordinator: EKYCZolozCoordinatorDelegate {
    func dismissCoordinator(_ coordinator: Coordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
        })
    }

    func dismissAndPushProfileCoordinator(_ coordinator: Coordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)

            guard let visibleViewController = router.visibleViewController,
                  !visibleViewController.isKind(of: MayaProfileViewController.self) else {
                return
            }

            self.startProfileFlow()
        })
    }

    func sessionTimeoutErrorOccured(_ coordinator: EKYCZolozCoordinator) {
        self.removeChild(coordinator)
    }
}

// MARK: MayaSendMoneyCoordinator App Event Methods
extension MayaSendMoneyCoordinator {
    func logShowSuccessBottomSheet(hasAuthChallengeID: Bool) {
        guard isAppEventV2SendMoneyEnabled else { return }

        let keyAttributes: [AnalyticsAttributeKey: Any] = [.sourcePage: hasAuthChallengeID ? AnalyticsSendMoney.Page.otp.rawValue : AnalyticsSendMoney.Page.confirmation.rawValue ]
        analyticsService.logMayaEvents(AnalyticsSendMoney.success.appear(), attributes: keyAttributes)
    }

    func logViewReceiptButtonTap() {
        if isAppEventV2SendMoneyEnabled {
            logButtonEvent(attributes: setEventKeyAttributes(button: .viewReceipt, destinationPage: .receipt))
        } else {
            analyticsUtils.logMayaEvents(.tap(Analytics.SendMoneySuccess.receipt))
        }
    }

    func logDoneButtonTap() {
        if isAppEventV2SendMoneyEnabled {
            logButtonEvent(attributes: setEventKeyAttributes(button: .done, destinationPage: .dashboard))
        } else {
            analyticsUtils.logMayaEvents(.tap(Analytics.SendMoneySuccess.done))
        }
    }

    func logSaveToFavoritesButtonTap() {
        if isAppEventV2SendMoneyEnabled {
            logButtonEvent(attributes: setEventKeyAttributes(button: .saveToFavorites, destinationPage: .favorite))
        } else {
            analyticsUtils.logMayaEvents(.tap(Analytics.SendMoneySuccess.favorites))
        }
    }

    private func setEventKeyAttributes(button: AnalyticsSendMoney.Button, destinationPage: AnalyticsSendMoney.Page) -> [AnalyticsAttributeKey: Any] {
        return [
            .sourcePage: AnalyticsSendMoney.Page.confirmation.rawValue,
            .button: button.rawValue,
            .destinationPage: destinationPage.rawValue
        ]
    }

    private func logButtonEvent(attributes: [AnalyticsAttributeKey: Any]) {
        analyticsService.logMayaEvents(AnalyticsSendMoney.success.buttonTapped(), attributes: attributes)
    }
}
