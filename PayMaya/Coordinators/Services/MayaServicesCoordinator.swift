//
//  MayaServicesCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 2/8/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import ConfigurationService
import Injector
import UIKit

protocol MayaServicesCoordinatorDelegate: AnyObject {
    func didTapService(_ coordinator: MayaServicesCoordinator, service: Service)
    func didTapServicesAction(_ coordinator: MayaServicesCoordinator, url: URL?, title: String?)
}

class MayaServicesCoordinator: Coordinator, DeepLinkHandler {
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2

    weak var delegate: MayaServicesCoordinatorDelegate?

    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .services(let param):
            if case .servicesSection(let section) = param {
                scrollToSection(section)
            }
            return .opened(deepLink, nil)
        default:
            return .rejected(deepLink, .notHandled, identifier)
        }
    }

    override func start() {
        let viewController = StoryboardScene.Home.mayaServicesViewController.instantiate()
        viewController.routeActionDelegate = self
        viewController.delegate = self

        let viewModel = MayaServicesViewModel()
        viewController.bind(viewModel)

        setRootModule(viewController, animated: false)

        if configurationServiceV2.dashboardNavigationRefactorEnabled.value {
            presenter?.present(router, animated: true, completion: nil)
        } else {
            viewController.floatingTabBarItem = MayaFloatingTabBarItem(model: .init(mayaTab: .services))
            viewController.floatingTabBarItem?.accessibilityIdentifier = "pmios_tab_services"
        }
    }

    private func scrollToSection(_ section: Param.ServicesSection) {
        guard let servicesViewController = rootViewController as? MayaServicesViewController else { return }
        servicesViewController.scrollToSection(section)
    }
}

extension MayaServicesCoordinator: MayaServicesViewControllerDelegate {
    func didTapService(_ viewController: MayaServicesViewController, service: Service) {
        delegate?.didTapService(self, service: service)
    }

    func didTapMessages(_ viewController: MayaServicesViewController) {
        guard let url = Constants.DeepLinkPath.inbox.url else { return }

        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
    }

    func didTapHelp(_ viewController: MayaServicesViewController) {
        guard let viewModel = viewController.viewModel,
            let link = viewModel.helpUrl else {
            return
        }
        delegate?.didTapServicesAction(self, url: link, title: Constants.ScreenTitle.WebView.help.rawValue)
    }

    func didTapProfile(_ viewController: MayaServicesViewController) {
        guard let url = Constants.DeepLinkPath.profile.url else { return }

        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
    }

    func didTapSettings(_ viewController: MayaServicesViewController) {
        startSettingsFlow()
    }

    func didTapAboutMaya(_ viewController: MayaServicesViewController) {
        guard let link = Constants.WebView.about.url else { return }
        delegate?.didTapServicesAction(self, url: link, title: nil)
    }

    func didTapSecurityCenter(_ viewController: MayaServicesViewController) {
        guard let url = Constants.DeepLinkPath.securityCenter.url else { return }

        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
    }

    func didTapBack(_ viewController: MayaServicesViewController) {
        super.didTapBack(viewController)
    }

    func didTapAdBanner(url: URL, _ viewController: MayaServicesViewController) {
        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
    }
}

extension MayaServicesCoordinator {
    func startSettingsFlow() {
        let coordinator = MayaSettingsCoordinator(presenter: router)
        coordinator.presentationDirection = .right
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }
}

// MARK: MayaInboxSupportCoordinatorDelegate methods
extension MayaServicesCoordinator: MayaInboxSupportCoordinatorDelegate {
    func didDismiss(coordinator: MayaInboxSupportCoordinator) {
        self.removeChild(coordinator)
    }
}

// MARK: MayaSecurityCenterCoordinatorDelegate Methods
extension MayaServicesCoordinator: MayaSecurityCenterCoordinatorDelegate {
    func didDismiss(coordinator: MayaSecurityCenterCoordinator) {
        removeChild(coordinator)
    }

    func didSetSourceAttributes(coordinator: MayaSecurityCenterCoordinator) -> Array<String> {
        return [AnalyticsConstants.SourcePage.services.rawValue, AnalyticsConstants.Screen.SecurityCenter.Button.securityCenterButton.rawValue]
    }
}
