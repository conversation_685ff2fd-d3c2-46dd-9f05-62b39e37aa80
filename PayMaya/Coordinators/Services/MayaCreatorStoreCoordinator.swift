//
//  MayaCreatorStoreCoordinator.swift
//  PayMaya
//
//  Created by <PERSON> on 11/9/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation
import ReactiveSwift

class MayaCreatorStoreCoordinator: Coordinator {
    private var creatorStore: CreatorStore
    private var deeplinkMerchantId: String?
    private var viewModel: MayaCreatorStoreViewModelProtocol?

    // Used to prevent showing the first screen twice when coordinator is first initialized
    private var isFirstLaunch: Bool = false

    private var webViewConfiguration: WebViewConfiguration {
        return WebViewConfiguration(allowsLandscape: true)
    }

    private var shouldShowExternalLinkPrompt: Bool {
        creatorStore == .globalRemittance || creatorStore == .government
    }

    private var shouldShowInterstitial: Bool { creatorStore.shouldShowInterstitial }

    init(creatorStore: CreatorStore, router: RouterProtocol = Router(), presenter: RouterProtocol? = nil) {
        self.creatorStore = creatorStore
        super.init(router: router, presenter: presenter)
    }

    override func start(completion: (() -> Void)? = nil) {
        showFirstScreen()
        isFirstLaunch = true
        presenter?.present(router, animated: true, completion: completion)
    }

    private func showFirstScreen() {
        if shouldShowInterstitial {
            showInterstitialScreen()
        } else {
            showCreatorStoreScreen()
        }
    }

    private func showFirstScreenIfNeeded() {
        if !isFirstLaunch {
            showFirstScreen()
        } else {
            isFirstLaunch = false
        }
    }

    private func showInterstitialScreen() {
        var viewController: ViewController?

        if creatorStore.doesInterstitialHaveBanners,
           let viewModel = MayaBannerInterstitialViewModel(model: creatorStore) {
            let bannerInterstitialViewController = StoryboardScene.Home.mayaBannerInterstitialViewController.instantiate()
            bannerInterstitialViewController.delegate = self
            bannerInterstitialViewController.bind(viewModel)

            viewController = bannerInterstitialViewController
        } else if let viewModel = MayaInterstitialViewModel(model: creatorStore) {
            let interstitialViewController = StoryboardScene.Home.mayaInterstitialViewController.instantiate()
            interstitialViewController.delegate = self
            interstitialViewController.bind(viewModel)

            viewController = interstitialViewController
        }

        viewController?.backActionDelegate = self
        viewController?.routeActionDelegate = self

        guard let viewController else { return }
        setRootModule(viewController, animated: false)
    }

    private func showCreatorStoreScreen() {
        let viewModel = MayaCreatorStoreViewModel(creatorStore: creatorStore)
        self.viewModel = viewModel

        let viewController = StoryboardScene.Home.mayaCreatorStoreViewController.instantiate()
        viewController.backActionDelegate = self
        viewController.delegate = self
        viewController.routeActionDelegate = self
        viewController.bind(viewModel)
        setupGetMerchant()

        setRootModule(viewController, animated: true)

        if deeplinkMerchantId != nil {
            complete(deepLinkHandling: deepLinkHandling, error: nil)
        }
    }

    private func setupGetMerchant() {
        guard let viewModel else { return }

        viewModel.getMerchantAction.completed.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] in
                guard let self,
                    let merchantId = deeplinkMerchantId,
                    let merchant = viewModel.fetchMerchant(merchantId: merchantId) else {
                    return
                }

                switch merchant.type {
                case .internal:
                    if let merchantUrl = URL(string: merchant.link) {
                        showWebView(link: merchantUrl, title: merchant.name, willPresent: true, webViewConfiguration: webViewConfiguration, shouldShowExternalLinkPrompt: shouldShowExternalLinkPrompt)
                    }
                case .external:
                    if let merchantUrl = URL(string: merchant.link) {
                        showExternalURI(merchantUrl)
                    }
                case .miniapp:
                    startMiniAppFlow(code: merchant.code, title: merchant.name, category: merchant.category, isKYC1Required: merchant.isKYC1Required, webViewConfiguration: webViewConfiguration, shouldShowExternalLinkPrompt: shouldShowExternalLinkPrompt)
                case .flutter:
                    startFlutterFlow(code: merchant.code, isKYC1Required: merchant.isKYC1Required)
                default:
                    break
                }

                deeplinkMerchantId = nil
            }?.addToDisposeBag(disposeBag)

        viewModel.getMerchantAction.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                guard let self = self else { return }
                if isExecuting {
                    router.toPresentable().showMayaLoader()
                } else {
                    router.toPresentable().hideMayaLoader()
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.getMerchantAction.errors.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self = self else { return }
                deeplinkMerchantId = nil
            }?.addToDisposeBag(disposeBag)
    }

    private func startMiniAppFlow(code: String, title: String, category: String, isKYC1Required: Bool, webViewConfiguration: WebViewConfiguration, shouldShowExternalLinkPrompt: Bool) {
        var updatedWebViewConfiguration = webViewConfiguration
        updatedWebViewConfiguration.allowsInlineMediaPlayback = true
        let coordinator = WebViewCoordinator(presenter: router, webViewParametersRequiringModule: .miniapp(code: code, title: title, category: category), isKYC1Required: isKYC1Required, webViewConfiguration: updatedWebViewConfiguration)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.shouldShowExternalLinkPrompt = shouldShowExternalLinkPrompt
        addChild(coordinator)
        coordinator.start(completion: nil)
    }

    private func startFlutterFlow(code: String, isKYC1Required: Bool) {
        let coordinator: Coordinator
        switch code {
        case "seedbox":
            let fundsCoordinator = MayaFundsCoordinator(presenter: router)
            fundsCoordinator.delegate = self
            coordinator = fundsCoordinator
        default:
            return
        }

        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start(completion: nil)
    }
}

// MARK: - DeepLinkHandler Methods
extension MayaCreatorStoreCoordinator: DeepLinkHandler {
    func open(deepLink: DeepLink) -> DeepLinkHandling {
        guard case .dashboard(let dashboardRoute) = deepLink.route,
            let creatorStore = dashboardRoute.creatorStore else {
            return .rejected(deepLink, .notHandled, identifier)
        }

        self.creatorStore = creatorStore

        switch dashboardRoute {
        case .esims(let param),
            .food(let param),
            .funds(let param),
            .globalRemittance(let param),
            .globalStocks(let param),
            .government(let param),
            .insurance(let param),
            .luckyGames(let param),
            .merchantRewards(let param),
            .payLater(let param),
            .retail(let param),
            .sparkHackathon(let param),
            .stocks(let param),
            .stocksGame(let param),
            .stream(let param):

            if case .creatorStoreMerchantId(let merchantId) = param {
                let handling: DeepLinkHandling

                if deeplinkMerchantId == nil {
                    if shouldShowInterstitial {
                        handling = .delayed(deepLink, identifier) { [weak self] in
                            self?.showFirstScreenIfNeeded()
                        }
                    } else {
                        handling = .opened(deepLink) { [weak self] in
                            self?.showFirstScreenIfNeeded()
                            self?.viewModel?.getMerchantAction.apply(merchantId).start()
                        }
                    }
                } else {
                    handling = .opened(deepLink) { [weak self] in
                        self?.viewModel?.getMerchantAction.apply(merchantId).start()
                    }
                }

                deeplinkMerchantId = merchantId
                return handling
            } else {
                return .opened(deepLink) { [weak self] in
                    self?.showFirstScreenIfNeeded()
                }
            }
        default:
            return .rejected(deepLink, .notHandled, identifier)
        }
    }
}

// MARK: MayaCreatorStoreViewControllerDelegate functions
extension MayaCreatorStoreCoordinator: MayaCreatorStoreViewControllerDelegate {
    func didTapCheckMerchants(_ viewController: MayaCreatorStoreViewController) {
        guard let link = Constants.WebView.partnerMerchants.url else { return }
        showWebView(link: link, webViewConfiguration: webViewConfiguration)
    }

    func didTapOnlineMerchantItem(_ viewController: MayaCreatorStoreViewController, link: URL, title: String) {
        showWebView(link: link, title: title, willPresent: true, webViewConfiguration: webViewConfiguration)
    }

    func didTapMiniappMerchant(_ viewController: MayaCreatorStoreViewController, code: String, title: String, category: String, isKYC1Required: Bool) {
        startMiniAppFlow(code: code, title: title, category: category, isKYC1Required: isKYC1Required, webViewConfiguration: webViewConfiguration, shouldShowExternalLinkPrompt: shouldShowExternalLinkPrompt)
    }

    func didTapFlutterMerchant(_ viewController: MayaCreatorStoreViewController, code: String, isKYC1Required: Bool) {
        startFlutterFlow(code: code, isKYC1Required: isKYC1Required)
    }

    func didTapAdBanner(_ viewController: MayaCreatorStoreViewController, link: URL) {
        routeActionDelegate?.didRequestStartDeepLink(self, with: link, completion: { [weak self] success in
            if !success {
                self?.showWebView(link: link, willPresent: true)
            }
        })
    }
}

// MARK: MayaInterstitialViewControllerDelegate
extension MayaCreatorStoreCoordinator: MayaInterstitialViewControllerDelegate {
    func viewControllerDidTapContinue(_ viewController: MayaInterstitialViewController) {
        showCreatorStoreScreen()
    }

    func viewController(_ viewController: MayaInterstitialViewController, didTapURL url: URL) {
        showWebView(link: url)
    }
}

// MARK: MayaFundsCoordinatorDelegate Functions
extension MayaCreatorStoreCoordinator: MayaFundsCoordinatorDelegate {
    func didReceiveFundsError(error: PayMayaError, coordinator: MayaFundsCoordinator) {
        if case .sessionTimeout = error.type {
            // Do nothing
        } else if let errorViewModel = error.viewModel {
            showErrorAlert(errorViewModel)
        }
        removeChild(coordinator)
    }

    func didFinishFunds(coordinator: MayaFundsCoordinator) {
        removeChild(coordinator)
    }
}
