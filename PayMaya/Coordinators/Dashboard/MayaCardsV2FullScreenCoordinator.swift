//
//  MayaCardsV2FullScreenCoordinator.swift
//  PayMaya
//
//  Created by <PERSON> on 7/13/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import ConfigurationService
import Flutter
import FlutterPluginRegistrant
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider

protocol MayaCardsV2FullScreenCoordinatorDelegate: AnyObject {
    func didDismiss(coordinator: MayaCardsV2FullScreenCoordinator)
}

class MayaCardsV2FullScreenCoordinator: MayaCardBaseCoordinator {
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2

    weak var delegate: MayaCardsV2FullScreenCoordinatorDelegate?

    init(
        router: RouterProtocol = Router(),
        presenter: RouterProtocol? = nil,
        shouldPresentRouter: Bool = true
    ) {
        super.init(
            flutterModule: .cardsFull,
            router: router,
            presenter: presenter,
            animated: true,
            shouldPresentRouter: shouldPresentRouter
        )
    }

    override var flutterToggles: [FlutterToggles: CSConfig<Bool>] {
        return [
            FlutterToggles.landersCreditCard: configurationService.landersCreditCardEnabled,
            FlutterToggles.cardDetailsProtection: configurationService.cardDetailsProtectionEnabled,
            FlutterToggles.mayaBlackCreditCard: configurationService.loansCreditCardMayaBlackEnabled,
            FlutterToggles.voucherDetailsV2: configurationService.voucherDetailsV2Enabled,
            FlutterToggles.creditCardCashbackTile: configurationService.creditCardCashbackTileEnabled,
            FlutterToggles.loyaltyForceUpdate: configurationService.loyaltyForceUpdateEnabled
        ]
    }

    override var flutterTogglesV2: [FlutterToggles: CSConfigV2<Bool>] {
        return [
            FlutterToggles.mayaBlackIntro: configurationServiceV2.mayaBlackIntroEnabled,
            FlutterToggles.convertToInstallment: configurationServiceV2.convertToInstallmentEnabled
        ]
    }

    override func setCustomMethodHandler(method: FlutterMethod, call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch method {
        case .finishCurrentModuleForCard: finishCurrentModuleForCards(call: call, result: result)
        case .requestSetUsername: setUpSetUsernameFullScreen()
        case .requestCheckout: requestCheckout(call: call)
        case .requestCreditCard: startCreditCardFullScreen(args: call.arguments)
        case .requestCashIn: startCashIn()
        case .goToVoucherListScreen: startVoucherListScreen()
        case .goToVoucherDetailsScreen: startVoucherDetailFlow(call: call)
        case .reloadHome: reloadHome(method: method, call: call)
        default: result(nil)
        }
    }

    override func didDismiss(_ coordinator: MayaFlutterBaseCoordinator) {
        super.didDismiss(coordinator)
        self.delegate?.didDismiss(coordinator: self)
    }
}

// MARK: - MayaCardsCheckoutCoordinatorDelegate Methods
extension MayaCardsV2FullScreenCoordinator: MayaCardsCheckoutCoordinatorDelegate {
    func didDismiss(coordinator: MayaCardsCheckoutCoordinator) {
        removeChild(coordinator)
    }

    func setUpCheckoutFullScreen(completion: ((Coordinator) -> Void)?) {
        let coordinator = MayaCardsCheckoutCoordinator(presenter: router)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        completion?(coordinator)
    }

    func requestCheckout(call: FlutterMethodCall) {
        if let args = call.arguments as? Dictionary<String, Any>,
        let cardsPaymentId = args[Constants.Flutter.Checkout.paymentId] as? String {
            self.setUpCheckoutFullScreen(
                completion: {
                    coordinator in coordinator.start {
                        self.fullscreenViewController?.modalPresentationStyle = .fullScreen

                        let args = [Constants.Flutter.Checkout.route: Constants.Flutter.Checkout.buyLoadScreen,
                                    Constants.Flutter.Checkout.paymentId: cardsPaymentId]

                        self.flutterEngineManager.getChannel(with: .checkout)?.invokeMethod(FlutterMethod.requestFullscreen.rawValue, arguments: args)
                    }
                }
            )}
    }
}

// MARK: - MayaWalletSettingsCoordinatorDelegate Methods
extension MayaCardsV2FullScreenCoordinator: MayaWalletSettingsCoordinatorDelegate {
    func didShowStartSavings(_ coordinator: MayaWalletSettingsCoordinator) {
        guard let savingsDeepLink = Constants.DeepLinkPath.mayaStartSavings.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: savingsDeepLink, completion: nil)
    }

    func didDismissWalletSettings(_ coordinator: MayaWalletSettingsCoordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
        })
    }
}

// MARK: - Flutter Methods
private extension MayaCardsV2FullScreenCoordinator {
    func finishCurrentModuleForCards(call: FlutterMethodCall, result: @escaping FlutterResult) {
        self.dismiss {
            self.delegate?.didDismiss(coordinator: self)
            self.flutterEngineManager.getChannel(with: .cardsTab)?.invokeMethod("onModuleResult", arguments: call.arguments)
            result(true)
        }
    }

    func reloadHome(method: FlutterMethod, call: FlutterMethodCall) {
        self.flutterEngineManager.getChannel(with: .cardsTab)?.invokeMethod(FlutterMethod.reloadHome.rawValue, arguments: call.arguments)
    }

    func setUpSetUsernameFullScreen() {
        let coordinator = MayaCardsSetUsernameCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    func startCashIn() {
        let coordinator = MayaWalletSettingsCoordinator(presenter: router)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.presentationDirection = .right
        addChild(coordinator)
        coordinator.start()
    }

    func startVoucherListScreen() {
        let coordinator = MayaVoucherListCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.presentationDirection = .right
        addChild(coordinator)
        coordinator.start()
    }

    func startVoucherDetailFlow(call: FlutterMethodCall) {
        guard let methodArguments = call.arguments as? [String: Any] else { return }
        guard let voucherCode = methodArguments["voucherCode"] as? String else { return }

        if configurationService.voucherDetailsV2Enabled.value {
            let coordinator = MayaVoucherDetailsCoordinator(
                voucherCode: voucherCode,
                presenter: router)

            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            coordinator.presentationDirection = .right
            addChild(coordinator)
            coordinator.start()
            return
        }

        let coordinator = MayaVouchersCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.presentationDirection = .right
        addChild(coordinator)
        coordinator.start()
    }
}
