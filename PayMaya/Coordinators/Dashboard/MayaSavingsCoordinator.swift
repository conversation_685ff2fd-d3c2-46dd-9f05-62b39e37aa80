//
//  MayaSavingsCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 3/2/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import ConfigurationService
import Flutter
import FlutterPluginRegistrant
import Foundation
import Glimpse
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider

public enum SavingsFlow: String {
   case walletToSavings
   case savingsToWallet
   case startSavings
}

protocol MayaSavingsCoordinatorDelegate: AnyObject {
    func didDismissSavingsBank(_ coordinator: MayaSavingsCoordinator)
    func didFinishSavingsBank(_ coordinator: MayaSavingsCoordinator)
}

class MayaSavingsCoordinator: MayaEKYCBaseCoordinator, AnalyticsProtocol {
    weak var delegate: MayaSavingsCoordinatorDelegate?

    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .dashboard(.mayaSavingsToWallet):
            self.redirectToFullScreenFlow(flow: SavingsFlow.savingsToWallet)
            return .opened(deepLink, nil)
        case .dashboard(.mayaStartSavings):
            self.redirectToFullScreenFlow(flow: SavingsFlow.startSavings)
            return .opened(deepLink, nil)
        case .dashboard(.savings):
            return .opened(deepLink, nil)
        default: return .rejected(deepLink, .notHandled, identifier)
        }
    }
    var flow: SavingsFlow = SavingsFlow.savingsToWallet
    var loanApplicationId: String = ""
    var creditLimitMultiplier: String = ""
    private var mayaReKYCCoordinator: MayaReKYCCoordinator?

    public func redirectToFullScreenFlow(flow: SavingsFlow) {
        self.flow = flow
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.showPreKYCFlow()
        }
    }

    @Inject var storeProvider: StoreProvider
    @Inject private var flutterEngineManager: FlutterEngineManager
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2

    var sessionTimeoutResult: FlutterResult?

    override init(router: RouterProtocol = Router(), presenter: RouterProtocol? = nil) {
        super.init(router: router, presenter: presenter)
        let timeoutNotificationSignal = NotificationCenter.default.reactive.notifications(forName: NSNotification.Name(rawValue: sessionTimeoutSuccessNotification)).map { _ in }
        timeoutNotificationSignal.observe(on: UIScheduler()).observeValues { [weak self] in
            guard let self = self,
                  let databaseStore = self.storeProvider.target(DatabaseStore.self),
                  let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                  let token = user.token
            else {
                return
            }

            self.sessionTimeoutResult?(self.flutterEngineManager.responseGenerator.getSessionToken(token))
            self.sessionTimeoutResult = nil
        }?.addToDisposeBag(disposeBag)
    }

    override func start(completion: (() -> Void)? = nil) {
        flutterEngineManager.startEngine(with: .savingsTab)
        setMethodHandlerForTab()
        if let engine = flutterEngineManager.getEngine(with: .savingsTab) {
            let flutterViewController = FlutterViewController(engine: engine, nibName: nil, bundle: nil)
            let containerViewController = StoryboardScene.Others.flutterScrollViewControllerContainer.instantiate()
            containerViewController.view.setNeedsLayout() // Needed to load the view
            containerViewController.activeSignal.observe(on: UIScheduler()).startWithValues { [weak self] active in
                guard let self = self, active else { return }
                self.flutterEngineManager.getChannel(with: .savingsTab)?.invokeMethod(FlutterMethod.reloadHome.rawValue, arguments: nil)
            }.addToDisposeBag(disposeBag)
            flutterViewController.willMove(toParent: containerViewController)
            containerViewController.addChild(flutterViewController)
            containerViewController.contentView.addSubview(flutterViewController.view)
            containerViewController.scrollView.showsVerticalScrollIndicator = false
            flutterViewController.didMove(toParent: containerViewController)
            flutterViewController.view.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.enclose(view: flutterViewController.view, inside: containerViewController.contentView)
            router.navigationController?.setNavigationBarHidden(true, animated: false)
            setRootModule(containerViewController, animated: false)
        }
    }

    func getUserDetails() -> User? {
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
        else {
            return nil
        }
        return user
    }

    private func setMethodHandlerForTab() {
        flutterEngineManager.getChannel(with: .savingsTab)?.setMethodCallHandler { [weak self] call, result in
            guard let self = self,
                  let method = FlutterMethod(rawValue: call.method)
            else { return }

            self.handleFlutterMethod(method, arguments: call.arguments, result: result)
        }
    }

    private func handleFlutterMethod(_ method: FlutterMethod, arguments: Any?, result: @escaping FlutterResult) {
        switch method {
        case .getCustomerId:
            handleGetCustomerId(result: result)
        case .getMin:
            handleGetMin(result: result)
        case .getClientId:
            handleGetClientId(result: result)
        case .getSessionToken:
            handleGetSessionToken(result: result)
        case .getAccessToken:
            handleGetAccessToken(result: result)
        case .getShieldSessionId:
            handleGetShieldSessionId(result: result)
        case .onSessionTimeout:
            handleSessionTimeout(result: result)
        case .getPinningFingerprint:
            handleGetPinningFingerprint(result: result)
        case .logAnalyticsEvent:
            handleLogAnalyticsEvent(arguments: arguments)
        case .requestFullscreen:
            handleRequestFullscreen(arguments: arguments, result: result)
        case .showUpgradeScreen:
            showPreKYCFlow()
        case .getReKYCFlag:
            handleGetReKYCFlag(result: result)
        case .getReKYCEnabled:
            handleGetReKYCEnabled(result: result)
        case .showReKYCFlow:
            self.showReKYCFlow()
        case .showReKYCDialog:
            self.showReKYCDialog()
        case .sendToFullscreenEngine:
            handleSendToFullscreenEngine(arguments: arguments, result: result)
        case .openSavingsDormancyRekyc:
            handleOpenSavingsDormancyRekyc(arguments: arguments, result: result)
        case .openBankReKyc:
            handleOpenBankRekyc(arguments: arguments, result: result)
        case .isMayaBlackEnabled:
            handleIsMayaBlackEnabled(result: result)
        case .openSecurityDepositCreation:
            handleOpenSecurityDepositCreation(arguments: arguments, result: result)
        case .openMayaBlackDashboard:
            handleOpenMayaBlackDashboard()
        case .openLendingReprocess:
            handleOpenLendingReprocess(arguments: arguments)
        case .openCreditApplication:
            handleOpenCreditApplication()
        case .showSecurityDepositOverview:
            handleOpenSecurityDepositOverview(arguments: arguments)
        default: return
        }
    }

    private func handleGetCustomerId(result: @escaping FlutterResult) {
        guard let user = getUserDetails(),
              let customerAccount = user.customerAccount
        else { return }

        result(flutterEngineManager.responseGenerator.getCustomerId(customerAccount.walletId))
    }

    private func handleGetMin(result: @escaping FlutterResult) {
        guard let user = getUserDetails(),
              let msisdn = user.msisdn
        else { return }

        result(flutterEngineManager.responseGenerator.getMin(msisdn))
    }

    private func handleGetClientId(result: @escaping FlutterResult) {
        result(flutterEngineManager.responseGenerator.getGlimpseClientId(Glimpse.environment?.clientID ?? ""))
    }

    private func handleGetSessionToken(result: @escaping FlutterResult) {
        guard let user = getUserDetails(),
              let token = user.token
        else { return }

        result(flutterEngineManager.responseGenerator.getSessionToken(token))
    }

    private func handleGetAccessToken(result: @escaping FlutterResult) {
        guard let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
              let authorization = encryptedStore.read(EncryptedDefaultsStoreId.authorization, type: Authorization.self).value
        else { return }

        result(flutterEngineManager.responseGenerator.getAccessToken(authorization.accessToken))
    }

    private func handleGetShieldSessionId(result: @escaping FlutterResult) {
        result(flutterEngineManager.responseGenerator.getShieldSessionId(Shield.shared().sessionId))
    }

    private func handleSessionTimeout(result: @escaping FlutterResult) {
        sessionTimeoutResult = result
        NotificationCenter.default.post(name: NSNotification.Name(rawValue: sessionTimeoutNotification), object: nil)
    }

    private func handleGetPinningFingerprint(result: @escaping FlutterResult) {
        guard let propertyListStore = storeProvider.target(PropertyListStore.self),
              let pinningFingerprint = propertyListStore.read(PropertyListStoreId.pinningFingerprint).value as? String
        else { return }

        result(flutterEngineManager.responseGenerator.getPinningFingerprint(pinningFingerprint))
    }

    private func handleLogAnalyticsEvent(arguments: Any?) {
        guard let jsonString = arguments as? String,
              let jsonData = jsonString.data(using: .utf8),
              let dictionary = try? JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
              let name = dictionary["event_name"] as? String
        else { return }

        let attributes = dictionary["attributes"] as? [String: Any]
        analyticsUtils.logMayaEvents(name, attributes: attributes)
    }

    private func handleRequestFullscreen(arguments: Any?, result: @escaping FlutterResult) {
        guard let dictionary = arguments as? Dictionary<String, Any>
        else { return }

        let screenType = dictionary["screen_type"] as? String
        let animated = screenType != "dialog"

        setUpSavingsFullScreen { coordinator in
            self.flutterEngineManager.getChannel(with: .savingFull)?.invokeMethod(
                FlutterMethod.requestFullscreen.rawValue,
                arguments: arguments
            ) { _ in
                coordinator.animatePresentation = animated
                coordinator.start()
            }
        }
        result(true)
    }

    private func handleGetReKYCFlag(result: @escaping FlutterResult) {
        let user = getUserDetails()
        let reason = user?.reKycReason
        let status = user?.reKycStatus
        result(flutterEngineManager.responseGenerator.getReKYCFlag(status: status ?? "", reason: reason ?? ""))
    }

    private func handleGetReKYCEnabled(result: @escaping FlutterResult) {
        let configService = ContainerWrapper.shared.resolve(ConfigurationService.self)
        result(flutterEngineManager.responseGenerator.getReKYCEnabled(enabled: configService.reKYCEnabled.value))
    }

    private func setUpSavingsFullScreen(completion: ((MayaSavingsFullScreenCoordinator) -> Void)?) {
        let coordinator = MayaSavingsFullScreenCoordinator(presenter: router)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        completion?(coordinator)
    }

    private func showPreKYCLevelFlow() {
        let coordinator = MayaPreKYCLevelCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        coordinator.childDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.analyticsModule = Analytics.SavingsDashboard()
        addChild(coordinator)
        coordinator.start()
    }

    private func showPreKYCFlow() {
        let coordinator = MayaPreKYCCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        coordinator.routeActionDelegate = self
        coordinator.analyticsModule = Analytics.SavingsDashboard()
        addChild(coordinator)
        coordinator.start()
    }

    private func showSecurityDeposit() {
        let coordinator = MayaSecurityDepositCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        coordinator.routeActionDelegate = self

        addChild(coordinator)
        coordinator.start()
    }

    private func startCreditCardFullScreen(args: Any?) {
        if let arguments = args as? [String: Any],
           let route = arguments[Constants.Flutter.Cards.route] as? String,
           let params = arguments[Constants.Flutter.Cards.params] as? String {
            let coordinator = LoansCreditCardFullScreenCoordinator(route: route, params: params, presenter: router)
            coordinator.delegate = self
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            coordinator.presentationDirection = .right
            addChild(coordinator)
            coordinator.start()
        }
    }

    private func showLoansCreditCard() {
        startCreditCardFullScreen(args: [
            Constants.Flutter.LoansCreditCard.route: Constants.Flutter.LoansCreditCard.cardOverviewScreen,
            Constants.Flutter.Cards.params: ""
        ])
    }

    private func showLoansReprocessScreen(arguments: [String: String]) {
        let params = arguments.map({ "\($0)=\($1)" }).joined(separator: "&")
        startCreditCardFullScreen(args: [
            Constants.Flutter.LoansCreditCard.route: Constants.Flutter.LoansCreditCard.securityDepositReprocessScreen,
            Constants.Flutter.Cards.params: params
        ])
    }

    private func showSecurityDepositOverview(arguments: [String: String]) {
        let cardsParams = [Constants.Flutter.SecurityDeposit.loanApplicationId: arguments[Constants.Flutter.SecurityDeposit.loanApplicationId],
                           Constants.Flutter.Savings.productCode: Constants.Flutter.Cards.mayaBlackProductCode,
                           Constants.Flutter.Savings.productKey: Constants.Flutter.Cards.productOffering
        ]
        let params = cardsParams.map { key, value in "\(key)=\(value ?? "")" }.joined(separator: "&")
        startCreditCardFullScreen(args: [
            Constants.Flutter.LoansCreditCard.route: Constants.Flutter.LoansCreditCard.securityDepositScreen,
            Constants.Flutter.Cards.params: params
        ])
    }

    private func setUpCardsFullScreen(completion: ((Coordinator) -> Void)?) {
        let coordinator = MayaCardsV2FullScreenCoordinator(presenter: router, shouldPresentRouter: true)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.presentationDirection = .right
        addChild(coordinator)
        completion?(coordinator)
    }

    private func showCardsFullScreen(args: Any?) {
        setUpCardsFullScreen(completion: { [weak self] coordinator in
            guard let self else { return }
            coordinator.start {
                let getChannel = self.flutterEngineManager.getChannel(with: .cardsFull)
                getChannel?.invokeMethod(FlutterMethod.requestFullscreen.rawValue, arguments: args)
            }
        })
    }
    // MARK: - Redirects to ReKYC flow bypassing the ReKYC common native bottomsheet
    private func showReKYCFlow() {
        if mayaReKYCCoordinator == nil {
            mayaReKYCCoordinator = MayaReKYCCoordinator(presenter: router)
        }

        let coordinator = setupMayaReKYCCoordinator()
        coordinator.startUpgradeKYC()
    }

    private func showReKYCDialog() {
        if mayaReKYCCoordinator == nil {
            mayaReKYCCoordinator = MayaReKYCCoordinator(presenter: router)
        }

        let coordinator = setupMayaReKYCCoordinator()
        coordinator.start()
    }

    private func showReKYCDialogWithEntrypoint(entryPoint: String, reKycDetails: Dictionary<String, String>?) {
        if let reKycDetails = reKycDetails {
            self.updateReKYCStatusToTagged(reKycDetails: reKycDetails)
        }

        let coordinator = setupEKYCZolozCoordinator()
        coordinator.startWithEntryPoint(entryPoint: entryPoint)
    }

    private func setupEKYCZolozCoordinator() -> EKYCZolozCoordinator {
        let coordinator: EKYCZolozCoordinator
        coordinator = EKYCZolozCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        coordinator.routeActionDelegate = self
        coordinator.analyticsModule = Analytics.SavingsDashboard()

        addChild(coordinator)
        return coordinator
    }

    private func setupMayaReKYCCoordinator() -> MayaReKYCCoordinator {
        let coordinator: MayaReKYCCoordinator

        if let kycCoordinator = mayaReKYCCoordinator {
            coordinator = kycCoordinator
        } else {
            coordinator = MayaReKYCCoordinator(presenter: router)
            coordinator.backActionDelegate = self
            coordinator.delegate = self
            coordinator.routeActionDelegate = self
            coordinator.analyticsModule = Analytics.SavingsDashboard()
        }

        addChild(coordinator)
        return coordinator
    }

    private func updateReKYCStatusToTagged(reKycDetails: Dictionary<String, String>) {
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User else { return }
        if let reKycStatus = reKycDetails[Constants.Flutter.Savings.reKycStatusKey] {
            user.reKycStatus = reKycStatus
        }

        if let reKycReason = reKycDetails[Constants.Flutter.Savings.reKycReasonKey] {
            user.reKycReason = reKycReason
        }

        databaseStore.write(user, options: DatabaseStore.WritingOptions(DatabaseEntity.user))
    }

    private func handleSendToFullscreenEngine(arguments: Any?, result: @escaping FlutterResult) {
        guard let dictionary = arguments as? Dictionary<String, Any>
        else {
            result(FlutterError.init(code: "", message: Constants.Flutter.Savings.invalidArguments, details: nil))
            return
        }
        self.flutterEngineManager.getChannel(with: .savingFull)?.invokeMethod(FlutterMethod.sendToFullscreenEngine.rawValue, arguments: dictionary)
        result(true)
    }

    private func handleOpenSavingsDormancyRekyc(arguments: Any?, result: @escaping FlutterResult) {
        guard let dictionary = arguments as? Dictionary<String, Any>
        else {
            result(FlutterError.init(code: "", message: Constants.Flutter.Savings.invalidArguments, details: nil))
            return
        }
        let shouldUpdateLocalTag = dictionary["should_update_local_tag"] as? Bool
        var reKycData: [String: String] = [:]
        if shouldUpdateLocalTag ?? false {
            reKycData = self.flutterEngineManager.responseGenerator.getReKYCFlag(status: ReKYCReason.dormantSavings.rawValue, reason: ReKYCStatus.tagged.rawValue)
        }
        self.showReKYCDialogWithEntrypoint(entryPoint: Constants.Flutter.Savings.mayaSavingsLiftDormancyEntrypoint, reKycDetails: reKycData)
    }

    private func handleOpenBankRekyc(arguments: Any?, result: @escaping FlutterResult) {
        guard let dictionary = arguments as? Dictionary<String, Any>
        else {
            result(FlutterError.init(code: "", message: Constants.Flutter.Savings.invalidArguments, details: nil))
            return
        }
        let user = getUserDetails()
        let status = user?.reKycStatus
        let shouldUpdateLocalTag = status == "none"
        let rekycStatus = dictionary[Constants.Flutter.Savings.reKycStatusKey] as? String ?? ""
        let rekycReason = dictionary[Constants.Flutter.Savings.reKycReasonKey] as? String ?? ""
        if shouldUpdateLocalTag {
             let reKycData = self.flutterEngineManager.responseGenerator.getReKYCFlag(status: rekycStatus, reason: rekycReason)
            self.updateReKYCStatusToTagged(reKycDetails: reKycData)
        }

        if status == "submitted" {
            showAccountUnderReview()
        } else {
            showReKYCFlow()
        }
    }

    private func showAccountUnderReview() {
        let coordinator: Coordinator
        coordinator = EKYCZolozReviewCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }

    private func handleIsMayaBlackEnabled(result: @escaping FlutterResult) {
        result(configurationService.loansCreditCardMayaBlackEnabled.value)
    }

    private func handleOpenSecurityDepositCreation(arguments: Any?, result: FlutterResult?) {
        if !configurationService.loansCreditCardMayaBlackEnabled.value {
            return
        }
        guard let dictionary = arguments as? Dictionary<String, Any>
        else { return }
        self.loanApplicationId = dictionary["loan_application_id"] as? String ?? ""
        self.creditLimitMultiplier = dictionary["credit_limit_multiplier"] as? String ?? ""
        self.showSecurityDeposit()
        result?(nil)
    }

    private func handleOpenMayaBlackDashboard() {
        if !configurationService.loansCreditCardMayaBlackEnabled.value {
            return
        }
        self.showCardsFullScreen(args: [Constants.Flutter.Cards.route: Constants.Flutter.Cards.cardDashboard, Constants.Flutter.Cards.productOfferingKey: Constants.Flutter.Cards.productOffering])
    }

    private func handleOpenLendingReprocess(arguments: Any?) {
        if !configurationService.loansCreditCardMayaBlackEnabled.value {
            return
        }
        guard let dictionary = arguments as? Dictionary<String, String>
        else { return }
        self.showLoansReprocessScreen(arguments: dictionary)
    }

    private func handleOpenSecurityDepositOverview(arguments: Any?) {
        if !configurationService.loansCreditCardMayaBlackEnabled.value {
            return
        }
        guard let dictionary = arguments as? Dictionary<String, String>
        else { return }
        self.showSecurityDepositOverview(arguments: dictionary)
    }

    private func handleOpenCreditApplication() {
        if !configurationService.loansCreditCardMayaBlackEnabled.value {
            return
        }
        self.showLoansCreditCard()
    }
}

// MARK: - MayaCardsV2FullScreenCoordinatorDelegate Methods
extension MayaSavingsCoordinator: MayaCardsV2FullScreenCoordinatorDelegate {
    func didDismiss(coordinator: MayaCardsV2FullScreenCoordinator) {
        removeChild(coordinator)
    }
}

// MARK: - LoansCreditCardFullScreenCoordinatorDelegate - Methods
extension MayaSavingsCoordinator: LoansCreditCardFullScreenCoordinatorDelegate {
    func didRequestFullScreen(coordinator: LoansCreditCardFullScreenCoordinator) {
        // Do nothing
    }

    func didRequestFullScreen(coordinator: LoansCreditCardFullScreenCoordinator, route: String, args: [String: String], completion: (() -> Void)?) {
        // Do nothing
    }

    func didDismiss(coordinator: LoansCreditCardFullScreenCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
    }
}

// MARK: - MayaSecurityDepositCoordinatorDelegate - Methods
extension MayaSavingsCoordinator: MayaSecurityDepositCoordinatorDelegate {
    func didSuccessfullyCreateDeposit(coordinator: MayaSecurityDepositCoordinator, result: [String: String]) {
        self.showLoansReprocessScreen(arguments: result)
    }

    func didRequestInputData(coordinator: MayaSecurityDepositCoordinator) -> [String: String] {
        return ["loan_application_id": self.loanApplicationId, "credit_limit_multiplier": self.creditLimitMultiplier]
    }

    func didDismiss(coordinator: MayaSecurityDepositCoordinator) {
        removeChild(coordinator)
    }
}

// MARK: - MayaSavingsFullScreenCoordinatorDelegate Methods
extension MayaSavingsCoordinator: MayaSavingsFullScreenCoordinatorDelegate {
    func didDismiss(coordinator: MayaSavingsFullScreenCoordinator) {
        removeChild(coordinator)
    }

    func showReKYCFlow(coordinator: MayaSavingsFullScreenCoordinator) {
        self.showReKYCFlow()
    }
}

// MARK: - MayaPreKYCCoordinatorDelegate
extension MayaSavingsCoordinator: MayaPreKYCCoordinatorDelegate {
    func preKYCCoordinatorDidFinishKYC(_ coordinator: MayaPreKYCCoordinator, completion: (() -> Void)?) {
        let flow = self.flow.rawValue

        self.setUpSavingsFullScreen(completion: { [weak self] coordinator in
            self?.flutterEngineManager.getChannel(with: .savingFull)?.invokeMethod(FlutterMethod.requestFullscreenFromNative.rawValue, arguments: flow) { _ in
                coordinator.start()
            }
        })

        removeChild(coordinator)
    }
    func preKYCCoordinatorDidReceiveError(_ coordinator: MayaPreKYCCoordinator, errorViewModel: ErrorAlertViewModel) {
        removeChild(coordinator)
    }
    func preKYCCoordinatorDidTapBack(_ coordinator: MayaPreKYCCoordinator) {
        removeChild(coordinator)
    }
    func preKYCCoordinatorDidTapUpgrade(_ coordinator: MayaPreKYCCoordinator) {
        analyticsUtils.logMayaEvents(.tap(Analytics.CashInMayaSavings.upgradeNow))
    }
    func preKYCDidShowBottomSheet(_ coordinator: MayaPreKYCCoordinator) {
        analyticsUtils.logMayaEvents(.appear(Analytics.CashIn.mayaSavingsAccountUpgrade))
    }
}

extension MayaSavingsCoordinator: MayaPreKYCLevelCoordinatorDelegate {
    func preKYCCoordinatorDidFinishKYC(_ coordinator: MayaPreKYCLevelCoordinator, completion: (() -> Void)?) {
        let flow = self.flow.rawValue

        self.setUpSavingsFullScreen(completion: { [weak self] coordinator in
            self?.flutterEngineManager.getChannel(with: .savingFull)?.invokeMethod(FlutterMethod.requestFullscreenFromNative.rawValue, arguments: flow) { _ in
                coordinator.start()
            }
        })
        removeChild(coordinator)
    }
    func preKYCCoordinatorDidTapUpgrade(_ coordinator: MayaPreKYCLevelCoordinator) {
        analyticsUtils.logMayaEvents(.tap(Analytics.CashInMayaSavings.upgradeNow))
    }
}

// MARK: MayaReKYCCoordinatorDelegate Functions
extension MayaSavingsCoordinator: MayaReKYCCoordinatorDelegate {
    func reKYCCoordinatorDispose(_ coordinator: MayaReKYCCoordinator) {
        removeChild(coordinator)
        mayaReKYCCoordinator = nil
        self.flutterEngineManager.getChannel(with: .savingsTab)?.invokeMethod(FlutterMethod.reloadHome.rawValue, arguments: nil)
    }

    func reKYCCoordinatorDidFinishKYC(_ coordinator: MayaReKYCCoordinator, shouldProceedToProfile: Bool = false, completion: (() -> Void)?) {
        removeChild(coordinator)
        mayaReKYCCoordinator = nil
        self.flutterEngineManager.getChannel(with: .savingsTab)?.invokeMethod(FlutterMethod.reloadHome.rawValue, arguments: nil)
    }

    func reKYCCoordinatorDidTapBack(_ coordinator: MayaReKYCCoordinator) {
        removeChild(coordinator)
        if let rootCoordinator = backActionDelegate as? Coordinator {
            rootCoordinator.removeChild(self)
        }
        mayaReKYCCoordinator = nil

        // Force to show Re-KYC reminder bottom sheet
        guard let databaseStore = storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
               else { return }
        if user.reKYCStatusValue == .tagged || user.reKYCStatusValue == .rejected {
            showReKYCDialog()
        }
    }
}

// MARK: - EKYCZolozCoordinatorDelegate Methods
extension MayaSavingsCoordinator: EKYCZolozCoordinatorDelegate {
    func dismissCoordinator(_ coordinator: Coordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)
        })
    }

    func dismissAndPushProfileCoordinator(_ coordinator: Coordinator) {
        router.dismissModule(animated: true, completion: { [weak self] in
            guard let self = self else { return }
            self.removeChild(coordinator)

            guard let visibleViewController = router.visibleViewController,
                  !visibleViewController.isKind(of: MayaProfileViewController.self) else {
                return
            }

            self.startProfileFlow()
        })
    }

    func sessionTimeoutErrorOccured(_ coordinator: EKYCZolozCoordinator) {
        self.removeChild(coordinator)
    }
}
