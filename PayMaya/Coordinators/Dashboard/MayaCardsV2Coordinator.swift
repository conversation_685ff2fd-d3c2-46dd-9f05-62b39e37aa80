//
//  MayaCardsV2Coordinator.swift
//  PayMaya
//
//  Created by <PERSON> on 7/13/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import ConfigurationService
import Flutter
import FlutterPluginRegistrant
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider

private typealias FlutterConstants = Constants.Flutter.Cards

class MayaCardsV2Coordinator: MayaCardBaseCoordinator {
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2

    init(
        router: RouterProtocol = Router(),
        presenter: RouterProtocol? = nil
    ) {
        super.init(
            flutterModule: .cardsTab,
            flutterAvailabilities: [.cards],
            router: router,
            presenter: presenter,
            isFullScreen: false
        )
    }

    override func setCustomMethodHandler(method: FlutterMethod, call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch method {
        case .requestFullscreen: startCardsFullScreen(args: call.arguments)
        case .requestCreditCard: startCreditCardFullScreen(args: call.arguments)
        case .goToSavingsTab: showSavingsTab()
        case .reloadHome: reloadHome(call)
        default: result(nil)
        }
    }

    override var flutterToggles: [FlutterToggles: CSConfig<Bool>] {
        return [
            FlutterToggles.landersCreditCard: configurationService.landersCreditCardEnabled,
            FlutterToggles.cardDetailsProtection: configurationService.cardDetailsProtectionEnabled,
            FlutterToggles.mayaBlackCreditCard: configurationService.loansCreditCardMayaBlackEnabled
        ]
    }

    override var flutterTogglesV2: [FlutterToggles: CSConfigV2<Bool>] {
        return [
            FlutterToggles.mayaBlackIntro: configurationServiceV2.mayaBlackIntroEnabled,
            FlutterToggles.convertToInstallment: configurationServiceV2.convertToInstallmentEnabled
        ]
    }

    private func reloadHome(_ call: FlutterMethodCall) {
        self.flutterEngineManager.getChannel(with: .cardsTab)?.invokeMethod(FlutterMethod.reloadHome.rawValue, arguments: call.arguments)
    }

    private func showSavingsTab() {
        guard let savingsDeepLink = Constants.DeepLinkPath.savings.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: savingsDeepLink, completion: nil)
    }

    private func setUpCardsFullScreen(completion: ((Coordinator) -> Void)?) {
        let coordinator = MayaCardsV2FullScreenCoordinator(presenter: router, shouldPresentRouter: configurationService.landersCreditCardEnabled.value)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.presentationDirection = .right
        addChild(coordinator)
        completion?(coordinator)
    }

    private func startCardsFullScreen(args: Any?) {
        setUpCardsFullScreen(completion: { [weak self] coordinator in
            guard let self else { return }
            coordinator.start {
                let getChannel = self.flutterEngineManager.getChannel(with: .cardsFull)
                getChannel?.invokeMethod(FlutterMethod.requestFullscreen.rawValue, arguments: args)
            }
        })
    }
}

 // MARK: - MayaCardsV2FullScreenCoordinatorDelegate Methods
 extension MayaCardsV2Coordinator: MayaCardsV2FullScreenCoordinatorDelegate {
    func didDismiss(coordinator: MayaCardsV2FullScreenCoordinator) {
        removeChild(coordinator)
    }
 }

// MARK: - DeepLinkHandler Methods
extension MayaCardsV2Coordinator: DeepLinkHandler {
    func open(deepLink: DeepLink) -> DeepLinkHandling {
        guard case .dashboard(.myCards(let route)) = deepLink.route else {
            return .rejected(deepLink, .notHandled, identifier)
        }

        let args = getArguments(for: route, deepLink: deepLink)

        if let args {
            startCardsFullScreen(args: args)
        }
        return .opened(deepLink, nil)
    }

    private func getArguments(for route: Route.Dashboard.MyCards, deepLink: DeepLink) -> [String: Any]? {
        let isLandersCreditCardEnabled = configurationService.landersCreditCardEnabled.value
        let isMayaBlackCreditCardEnabled = configurationService.loansCreditCardMayaBlackEnabled.value

        guard isLandersCreditCardEnabled else { return nil }

        switch route {
        case .cardDashboardBenefits:
            return [FlutterConstants.route: FlutterConstants.cardDashboardBenefits]
        case .cardDashboard:
            if isMayaBlackCreditCardEnabled {
                return getCardArguments(from: deepLink, from: FlutterConstants.cardDashboard)
                ?? [FlutterConstants.route: FlutterConstants.cardDashboard]
            } else {
                return [FlutterConstants.route: FlutterConstants.cardDashboard]
            }
        case .activateCreditCard:
            return getCardArguments(from: deepLink, from: FlutterConstants.activateCreditCard)
        case .creditCardTransactionReceipt:
            return getCardArguments(from: deepLink, from: FlutterConstants.transactionReceipt)
        default:
            return nil
        }
    }

    private func getCardArguments(from deepLink: DeepLink, from screen: String) -> [String: Any]? {
        guard let urlComponents = URLComponents(string: deepLink.url.absoluteString),
              let params = urlComponents.query else { return nil }
        return [
            FlutterConstants.route: screen,
            FlutterConstants.params: params
        ]
    }
}
