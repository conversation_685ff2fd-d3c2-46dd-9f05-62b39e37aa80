//
//  MayaInboxNotifCoordinator.swift
//  PayMaya
//
//  Created by <PERSON> on 4/18/23.
//  Copyright © 2023 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
import ConfigurationService
import FirebaseMessaging
import Flutter
import FlutterPluginRegistrant
import Foundation
import Injector
import MayaCoreData
import Photos
import QuickLook
import ReactiveSwift
import ShieldFraud
import StoreProvider

protocol ********************************Delegate: AnyObject {
    func didDismiss(coordinator: ********************************)
}

class ********************************: Coordinator, DeepLinkHandler {
    @Inject var storeProvider: StoreProvider
    @Inject var flutterEngineManager: FlutterEngineManager
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2
    @Inject var apiProvider: APIProvider
    @Inject var analyticsService: AnalyticsService

    weak var delegate: ********************************Delegate?
    weak var dashboardMenuPagerViewController: MayaDashboardMenuPagerViewController?

    var isFullScreen = false
    var isLoginComplete = false
    var sessionTimeoutResult: FlutterResult?

    var inboxManageNotificationsEnabled: Bool {
        return configurationService.mayaInboxManageNotificationsEnabled.value
    }

    var inboxSupportEnabled: Bool {
        return configurationService.inboxSupportEnabled.value
    }

    private var filePath: String = ""
    private var documentPickerCompletion: ((URL?) -> Void)?

    var presentedScreen: PresentedScreen = .none

    enum PresentedScreen {
        case filePicker
        case webView
        case none
    }

    var topViewController: UIViewController? {
        var topViewController = UIApplication.shared.windows.first?.rootViewController
        while let presentedViewController = topViewController?.presentedViewController {
            topViewController = presentedViewController
        }

        return topViewController
    }

    private var databaseStore: DatabaseStore? {
       return storeProvider.target(DatabaseStore.self)
    }

    private var responseGenerator: FlutterResponseGenerator {
        return flutterEngineManager.responseGenerator
    }

    override init(router: RouterProtocol = Router(), presenter: RouterProtocol? = nil) {
        super.init(router: router, presenter: presenter)
        flutterEngineManager.startEngine(with: .inbox)

        setupSessionTimeoutObserver()
    }

    func setupSessionTimeoutObserver () {
        let timeoutNotificationSignal = NotificationCenter.default.reactive.notifications(forName: NSNotification.Name(rawValue: sessionTimeoutSuccessNotification)).map { _ in }
        timeoutNotificationSignal.observe(on: UIScheduler()).observeValues { [weak self] in
            guard let self = self,
                  let token = self.databaseStore?.fetchUserToken() else { return }

            self.sessionTimeoutResult?(self.responseGenerator.getSessionToken(token))
            self.sessionTimeoutResult = nil
        }?.addToDisposeBag(disposeBag)
    }

    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .inbox(.inboxRoute(let route)?, .inboxMessageTimestamp(let messageTimestamp)?, .inboxChannelUrl(let channelUrl)?):
            let arguments = ["route": route, "messageTimestamp": messageTimestamp, "channelUrl": channelUrl]
            return handleInboxDeeplink(arguments: arguments as [String: Any], deepLink: deepLink)
        default:
            return .rejected(deepLink, .notHandled, identifier)
        }
    }

    func setupFlutter(mayaDashboardViewController: MayaDashboardMenuPagerViewController) {
        setMethodHandlerForTab(module: .inbox)
        self.dashboardMenuPagerViewController = mayaDashboardViewController

        if let engine = flutterEngineManager.getEngine(with: .inbox) {
            let flutterViewController = FlutterViewController(engine: engine, nibName: nil, bundle: nil)

            // Flutter VC needs to be displayed for the full flutter code to run (limitation)
            flutterViewController.willMove(toParent: mayaDashboardViewController)
            mayaDashboardViewController.addChild(flutterViewController)
            flutterViewController.view.frame = CGRect(x: 0, y: 0, width: 0, height: 0)
            mayaDashboardViewController.view.addSubview(flutterViewController.view)
            flutterViewController.didMove(toParent: mayaDashboardViewController)
            flutterEngineManager.getChannel(with: .inbox)?.invokeMethod(FlutterMethod.requestFullscreen.rawValue, arguments: ["route": "fullscreenFlowEntrypoint"])
        }
    }

    func presentFullScreen(completion: (() -> Void)? = nil, arguments: Any?) {
        presentedScreen = .none
        if !isLoginComplete { return }
        if isFullScreen {
            flutterEngineManager.getChannel(with: .inbox)?.invokeMethod(FlutterMethod.dismiss.rawValue, arguments: nil)

            // Closes presented VCs. Used to handle edge cases where presentFullScreen is called when the inbox sdk is already presented
            _ = resetFlow(requested: String(describing: self), forceResetFlow: true)
        }
        if let engine = flutterEngineManager.getEngine(with: .inbox) {
            guard let oldFlutterViewController = engine.viewController else { return }

            oldFlutterViewController.willMove(toParent: nil)
            oldFlutterViewController.view.removeFromSuperview()
            oldFlutterViewController.removeFromParent()
            engine.viewController = nil

            let flutterViewController = BaseFlutterViewController(engine: engine, nibName: nil, bundle: nil)
            setRootModule(flutterViewController, animated: false)
            router.navigationController?.navigationBar.isHidden = true
            presenter?.present(router, animated: true, completion: completion)
            isFullScreen = true

            if arguments != nil {
                flutterEngineManager.getChannel(with: .inbox)?.invokeMethod(FlutterMethod.requestFullscreen.rawValue, arguments: arguments)
            } else {
                flutterEngineManager.getChannel(with: .inbox)?.invokeMethod(FlutterMethod.reloadHome.rawValue, arguments: nil)
            }
        }
    }

    func dismissMethodCall() {
        if isFullScreen {
            self.presenter?.dismissModule(animated: true) { [weak self] in
                guard let self = self, let dashboardMenuPagerViewController = self.dashboardMenuPagerViewController else { return }

                // Re-initializes the inbox engine each time dismissMethodCall is called. Needed to fix certain bugs
                self.flutterEngineManager.clearEngine(with: .inbox)
                self.flutterEngineManager.initializeEngine(with: .inbox)
                self.flutterEngineManager.startEngine(with: .inbox)

                self.delegate?.didDismiss(coordinator: self)
                self.setupFlutter(mayaDashboardViewController: dashboardMenuPagerViewController)
                self.isFullScreen = false
            }
        }
    }

    func updateNativeUnreadCountMethodCall(call: FlutterMethodCall) {
        guard let dashboardMenuPagerViewController else { return }
        if let json = call.arguments as? [String: Any], let unreadCount = json["unreadCount"] as? Int {
            checkForSupportUnreadCounter(json: json)
            dashboardMenuPagerViewController.setUnreadIndicator(unreadCount: unreadCount)
            if unreadCount == 0 {
                // Setting applicationIconBadgeNumber to 0 clears the app badge counter and all push notifications.
                // Setting it to -1 clears only the app badge counter
                UIApplication.shared.applicationIconBadgeNumber = -1
            } else {
                UIApplication.shared.applicationIconBadgeNumber = unreadCount // Sets the unread badge of the app icon to be the same as with the inbox sdk unreads
            }
        }
    }

    private func checkForSupportUnreadCounter(json: [String: Any]) {
        guard let userDefaultsStore = storeProvider.target(UserDefaultsStore.self),
              let hasActiveChat = json["hasActiveChat"] as? Bool,
              let shouldShowUnread = json["shouldShowUnread"] as? Bool else { return }

        // Default value is 1 as set by the UI/UX Team
        let activeChatUnreadCount = json["unreadCount"] as? Int ?? 1
        let defaultStore = InboxDefaultStore(userDefaultsStore)
        defaultStore.writeHasActiveChat(hasActiveChat)
        defaultStore.writeShouldShowUnread(shouldShowUnread)
        defaultStore.writeActiveChatUnreadCount(activeChatUnreadCount)
    }

    private func removeSupportPushNotifications(call: FlutterMethodCall) {
        guard let dictionary = call.arguments as? [String: Any],
        let supportGroupChannelUrl = dictionary["supportGroupChannelUrl"] as? String else { return }

        // Gets all delivered push notifications with the same threadIdentifier as supportGroupChannelUrl
        UNUserNotificationCenter.current().getDeliveredNotifications { notifications in
            let matchingIdentifiers = notifications
                .filter { $0.request.content.threadIdentifier == supportGroupChannelUrl }
                .map { $0.request.identifier }
            // Clears all delivered push notifications with the same threadIdentifier as supportGroupChannelUrl
            UNUserNotificationCenter.current().removeDeliveredNotifications(withIdentifiers: matchingIdentifiers)
        }
        return
    }

    private func showFilePicker(completion: @escaping (URL?) -> Void) {
        let documentPicker = UIDocumentPickerViewController(documentTypes: ["com.adobe.pdf", "public.png", "public.jpeg", "com.microsoft.word.doc", "org.openxmlformats.wordprocessingml.document", "public.heic"], in: .import)

        documentPicker.delegate = self
        documentPicker.allowsMultipleSelection = false
        documentPicker.modalPresentationStyle = .formSheet

        topViewController?.present(documentPicker, animated: true) {
            self.documentPickerCompletion = completion
            self.presentedScreen = .filePicker
        }
    }

    private func openFileExternally(call: FlutterMethodCall) {
        if let json = call.arguments as? [String: Any], let path = json["filePath"] as? String {
            filePath = path

            let previewController = QLPreviewController()

            previewController.delegate = self
            previewController.dataSource = self

            previewController.modalPresentationStyle = .formSheet
            presentedScreen = .filePicker
            topViewController?.present(previewController, animated: true)
        }
    }

    private func getGalleryPermission(result: @escaping FlutterResult) {
        PHPhotoLibrary.requestAuthorization { [weak self] status in
            guard let self = self else { return }
            if status == .authorized {
                self.dispatchToMain {
                    result(self.responseGenerator.getGalleryPermissions(true))
                }
            } else {
                self.dispatchToMain {
                    result(self.responseGenerator.getGalleryPermissions(false))
                }
            }
        }
    }

    private func getCameraPermission(result: @escaping FlutterResult) {
        let captureDeviceManager = AVCaptureDevice.self
        captureDeviceManager.requestAccess(for: .video) { isGranted in
            if isGranted {
                self.dispatchToMain {
                    result(self.responseGenerator.getCameraPermissions(true))
                }
            } else {
                self.dispatchToMain {
                    result(self.responseGenerator.getCameraPermissions(false))
                }
            }
        }
    }

    private func getAccessToken(result: FlutterResult) {
        guard let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
        let authorization = encryptedStore.read(EncryptedDefaultsStoreId.authorization, type: Authorization.self).value else { return }

        result(responseGenerator.getAccessToken(authorization.accessToken))
    }

    private func getSessionToken(result: FlutterResult) {
        guard let token = databaseStore?.fetchUserToken() else { return }

        result(responseGenerator.getSessionToken(token))
    }

    private func getApnsToken(result: FlutterResult) {
        guard let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
              let apnsToken = encryptedStore.read(EncryptedDefaultsStoreId.apnsToken, type: String.self).value else { return }

        result(responseGenerator.getApnsToken(apnsToken))
    }

    private func showWebViewMethodCall(call: FlutterMethodCall) {
        guard let dictionary = call.arguments as? Dictionary<String, AnyObject>,
              let urlString = dictionary["url"] as? String,
              let url = URL(string: urlString) else {return}

        showWebView(link: url, willPresent: true)
    }

    private func shouldNotPresentPushNotification(call: FlutterMethodCall) {
        guard let dictionary = call.arguments as? Dictionary<String, AnyObject>,
              let encryptedStore = self.storeProvider.target(EncryptedDefaultsStore.self) else { return }

        if let supportGroupChannelUrl = (dictionary["supportGroupChannelUrl"] as? String) {
            encryptedStore.write(supportGroupChannelUrl, options: EncryptedDefaultsStoreId.supportGroupChannelUrl)
        } else {
            encryptedStore.write("", options: EncryptedDefaultsStoreId.supportGroupChannelUrl)
        }
    }

    private func refreshAccessToken(result: @escaping FlutterResult) {
        guard let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
        let authorization = encryptedStore.read(EncryptedDefaultsStoreId.authorization, type: Authorization.self).value else { return }
        let accessToken = authorization.accessToken
        let accessTokenHeader = "Bearer \(accessToken)"

        self.apiProvider.reactive.refreshAccessToken(header: accessTokenHeader).startWithResult({ refreshAccessTokenResult in
            switch refreshAccessTokenResult {
            case .success:
                result(self.responseGenerator.refreshAccessToken("true"))
            case .failure:
                result(self.responseGenerator.refreshAccessToken("false"))
            }
        }).addToDisposeBag(self.disposeBag)
    }

    private func getPathFromFilePicker(result: @escaping FlutterResult) {
        showFilePicker { [weak self] selectedURL in
            guard let self = self else { return }
            if let url = selectedURL {
                result(self.responseGenerator.getFilePathFromFilePicker(url.path))
            } else {
                result(self.responseGenerator.getFilePathFromFilePicker(""))
            }
        }
    }

    private func setIsPresentingOverInboxFullScreen(call: FlutterMethodCall) {
        guard let arguments = call.arguments as? [String: Any],
              let value = arguments["value"] as? Bool else { return }
        presentedScreen = value ? .filePicker : .none
    }

    private func handleInboxDeeplink(arguments: [String: Any], deepLink: DeepLink) -> DeepLinkHandling {
        guard isLoginComplete else {
            return .delayed(deepLink, identifier, nil)
        }

        if !isFullScreen {
            presentFullScreen(arguments: arguments)
            return .opened(deepLink, nil)
        }

        switch presentedScreen {
        case .filePicker:
            presentedScreen = .none
            topViewController?.dismiss(animated: true)
        case .webView:
            presentedScreen = .none
            router.popModule(animated: true)
        case .none: break
        }

        flutterEngineManager.getChannel(with: .inbox)?.invokeMethod(FlutterMethod.requestForegroundDeeplink.rawValue, arguments: arguments)
        return .opened(deepLink, nil)
    }

    private func showSupportWebViewWithParams(call: FlutterMethodCall) {
        guard let arguments = call.arguments as? Dictionary<String, AnyObject>,
              let path = arguments["customPath"] as? String else { return }

        guard let helpCenterUrl = arguments["baseUrlOverride"] != nil
                ? Constants.WebView.mayaBankHelpWithProfileParamsURL(path)
                : Constants.WebView.mayaHelpWithProfilePassingParamsURL(path) else { return }

        presentedScreen = .webView
        showWebView(link: helpCenterUrl, willPresent: true)
    }

    private func handleGetUserInfo(_ result: FlutterResult) {
        guard let user = databaseStore?.fetchUser() else { return }

        let response = responseGenerator.generateUserInfoWithAdditionalParams(with: user)
            result(response)
        }

    private func redirectToStore() {
        guard let url = Constants.AppStoreLinks.store.url else { return }
        UIApplication.shared.open(url)
    }

    private func getAppVersion(result: FlutterResult) {
        let appVersion = Bundle.main.object(forInfoDictionaryKey: Constants.Keys.BuildVersion.version.rawValue) as? String ?? Constants.Defaults.Common.version.rawValue

        result(flutterEngineManager.responseGenerator.getAppVersion(appVersion))
    }

    func setMethodHandlerForTab(module: FlutterModule) {
        flutterEngineManager.getChannel(with: module)?.setMethodCallHandler { [weak self] call, result in
            guard let self = self,
                  let method = FlutterMethod(rawValue: call.method)
            else { return }
            switch method {
            case .logAnalyticsEvent:
                self.handleLogAnalyticsEvent(call)
            case .getAccessToken:
                self.getAccessToken(result: result)
            case .getSessionToken:
                self.getSessionToken(result: result)
            case .getApnsToken:
                self.getApnsToken(result: result)
            case .updateNativeUnreadCount:
                self.updateNativeUnreadCountMethodCall(call: call)
                result(nil)
            case .dismiss:
                self.dismissMethodCall()
            case .onLoginComplete:
                self.isLoginComplete = true
                self.complete(deepLinkHandling: self.deepLinkHandling, error: nil)
            case .showWebView:
                self.showWebViewMethodCall(call: call)
            case .getInboxToggles:
                let togglesS3 = [
                    FlutterToggles.mayaSupport: self.configurationService.inboxSupportEnabled,
                    FlutterToggles.manageNotifications: self.configurationService.mayaInboxManageNotificationsEnabled
                ]
                let togglesSplit = [
                    FlutterToggles.inboxTicketing: configurationServiceV2.inboxTicketingEnabled,
                    FlutterToggles.sendbirdCachingCollection: configurationServiceV2.sendbirdCachingCollectionEnabled
                ]
                let togglesV1 = self.responseGenerator.getToggles(from: togglesS3)
                let togglesV2 = self.responseGenerator.getTogglesV2(from: togglesSplit)

                let mergedToggles = togglesV1.merged(with: togglesV2)

                result(mergedToggles)
            case .removeSupportChannelPushNotifications:
                self.removeSupportPushNotifications(call: call)
            case .shouldNotPresentPushNotification:
                shouldNotPresentPushNotification(call: call)
            case .refreshAccessToken:
                self.refreshAccessToken(result: result)
            case .getFilePathFromFilePicker:
                self.getPathFromFilePicker(result: result)
            case .getGalleryPermissions:
                self.getGalleryPermission(result: result)
            case .getCameraPermissions:
                self.getCameraPermission(result: result)
            case .openFileAtPath:
                self.openFileExternally(call: call)
            case .navigateToSettings:
                showExternalURI(URL(string: UIApplication.openSettingsURLString))
            case .setIsPresentingOverInboxFullScreen:
                self.setIsPresentingOverInboxFullScreen(call: call)
            case .onSessionTimeout:
                self.sessionTimeoutResult = result
                NotificationCenter.default.post(name: NSNotification.Name(rawValue: sessionTimeoutNotification), object: nil)
            case .showSupportWebViewWithParams:
                self.showSupportWebViewWithParams(call: call)
            case .redirectToStore:
                self.redirectToStore()
            case .getUserInfo:
                self.handleGetUserInfo(result)
            case .getAppVersion:
                self.getAppVersion(result: result)

            default: return
            }
        }
    }
}

extension ********************************: QLPreviewControllerDelegate {}

extension ********************************: UIDocumentPickerDelegate {
    func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
        if let selectedURL = urls.first {
            presentedScreen = .filePicker
            documentPickerCompletion?(selectedURL)
        }
    }

    func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
        presentedScreen = .filePicker
        documentPickerCompletion?(nil)
    }
}

// MARK: - QLPreviewControllerDataSource
extension ********************************: QLPreviewControllerDataSource {
    func previewController(_ controller: QLPreviewController, previewItemAt index: Int) -> QLPreviewItem {
        return URL(fileURLWithPath: self.filePath) as QLPreviewItem
    }

    func previewControllerDidDismiss(_ controller: QLPreviewController) {
        presentedScreen = .filePicker
    }

    func numberOfPreviewItems(in controller: QLPreviewController) -> Int {
        return 1
    }
}

// MARK: - AnalyticsServiceProtocol Related Methods
extension ********************************: AnalyticsServiceProtocol {
    private func handleLogAnalyticsEvent(_ call: FlutterMethodCall) {
        guard let jsonString = call.arguments as? String,
              let jsonData = jsonString.data(using: .utf8),
              let dictionary = try? JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
              let name = dictionary[Constants.Flutter.Key.eventName] as? String
        else { return }

        let attributes = dictionary[Constants.Flutter.Key.attributes] as? [String: Any]
        analyticsService.logMayaEvents(name: name, attributes: attributes)
    }
}
