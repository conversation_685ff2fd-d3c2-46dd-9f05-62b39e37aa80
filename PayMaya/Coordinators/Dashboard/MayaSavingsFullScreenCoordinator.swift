//
//  MayaSavingsFullScreenCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 3/17/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import ConfigurationService
import Flutter
import FlutterPluginRegistrant
import Foundation
import Glimpse
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider

protocol MayaSavingsFullScreenCoordinatorDelegate: AnyObject {
    func didDismiss(coordinator: MayaSavingsFullScreenCoordinator)

    func showReKYCFlow(coordinator: MayaSavingsFullScreenCoordinator)
}

class MayaSavingsFullScreenCoordinator: Coordinator, AnalyticsProtocol {
    @Inject var storeProvider: StoreProvider
    @Inject private var flutterEngineManager: FlutterEngineManager
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2

    weak var delegate: MayaSavingsFullScreenCoordinatorDelegate?

    var sessionTimeoutResult: FlutterResult?
    var animatePresentation: Bool = true
    var loanApplicationId: String = ""
    var creditLimitMultiplier: String = ""

    // Strong reference to the router to prevent it from being deallocated
    private var savingsFullScreenRouter: MayaSavingsFullScreenRouter?

    override init(router: RouterProtocol = Router(), presenter: RouterProtocol? = nil) {
        super.init(router: router, presenter: presenter)
        flutterEngineManager.startEngine(with: .savingFull)
        setMethodHandlerForTab()
        savingsFullScreenRouter = MayaSavingsFullScreenRouter()
        let timeoutNotificationSignal = NotificationCenter.default.reactive.notifications(forName: NSNotification.Name(rawValue: sessionTimeoutSuccessNotification)).map { _ in }
        timeoutNotificationSignal.observe(on: UIScheduler()).observeValues { [weak self] in
            guard let self = self,
                  let databaseStore = self.storeProvider.target(DatabaseStore.self),
                  let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                  let token = user.token
            else {
                return
            }

            self.sessionTimeoutResult?(self.flutterEngineManager.responseGenerator.getSessionToken(token))
            self.sessionTimeoutResult = nil
        }?.addToDisposeBag(disposeBag)
    }

    override func start(completion: (() -> Void)? = nil) {
        if let engine = flutterEngineManager.getEngine(with: .savingFull) {
            let flutterViewController = FlutterViewController(engine: engine, nibName: nil, bundle: nil)
            flutterViewController.isViewOpaque = false
            flutterViewController.modalPresentationStyle = UIModalPresentationStyle.overFullScreen
            presenter?.present(flutterViewController, animated: animatePresentation, completion: completion)
        }
    }

    func getUserDetails() -> User? {
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
        else {
            return nil
        }
        return user
    }

    private func setMethodHandlerForTab() {
        flutterEngineManager.getChannel(with: .savingFull)?.setMethodCallHandler { [weak self] call, result in
            guard let self = self,
                  let method = FlutterMethod(rawValue: call.method)
            else { return }

            switch method {
            case .getCustomerId:
                guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
                      let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                      let customerAccount = user.customerAccount
                else {
                    return
                }
                result(self.flutterEngineManager.responseGenerator.getCustomerId(customerAccount.walletId))
            case .getMin:
                guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
                      let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                      let msisdn = user.msisdn
                else {
                    return
                }
                result(self.flutterEngineManager.responseGenerator.getMin(msisdn))
            case .getClientId:
                result(self.flutterEngineManager.responseGenerator.getGlimpseClientId(Glimpse.environment?.clientID ?? ""))
            case .getSessionToken:
                guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
                      let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                      let token = user.token
                else {
                    return
                }
                result(self.flutterEngineManager.responseGenerator.getSessionToken(token))
            case .getAccessToken:
                guard let encryptedStore = self.storeProvider.target(EncryptedDefaultsStore.self),
                      let authorization = encryptedStore.read(EncryptedDefaultsStoreId.authorization, type: Authorization.self).value
                else {
                    return
                }
                result(self.flutterEngineManager.responseGenerator.getAccessToken(authorization.accessToken))
            case .getShieldSessionId:
                result(self.flutterEngineManager.responseGenerator.getShieldSessionId(Shield.shared().sessionId))
            case .onSessionTimeout:
                self.sessionTimeoutResult = result
                NotificationCenter.default.post(name: NSNotification.Name(rawValue: sessionTimeoutNotification), object: nil)
            case .getPinningFingerprint:
                if let propertyListStore = self.storeProvider.target(PropertyListStore.self),
                   let pinningFingerprint = propertyListStore.read(PropertyListStoreId.pinningFingerprint).value as? String {
                    result(self.flutterEngineManager.responseGenerator.getPinningFingerprint(pinningFingerprint))
                }
            case .logAnalyticsEvent:
                guard let jsonString = call.arguments as? String,
                      let jsonData = jsonString.data(using: .utf8),
                      let dictionary = try? JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
                      let name = dictionary["event_name"] as? String
                else { return }

                let attributes = dictionary["attributes"] as? [String: Any]
                self.analyticsUtils.logMayaEvents(name, attributes: attributes)
            case .dismiss:
                self.presenter?.dismissModule(animated: self.animatePresentation) { [weak self] in
                    guard let self = self else { return }
                    self.flutterEngineManager.getChannel(with: .savingFull)?.setMethodCallHandler(nil)
                    self.flutterEngineManager.clearEngine(with: .savingFull)
                    self.flutterEngineManager.initializeEngine(with: .savingFull)
                    self.delegate?.didDismiss(coordinator: self)
                }
            case .reloadHome:
                self.flutterEngineManager.getChannel(with: .savingsTab)?.invokeMethod(FlutterMethod.reloadHome.rawValue, arguments: nil)
            case .getReKYCFlag:
                let user = self.getUserDetails()
                let reason = user?.reKycReason
                let status = user?.reKycStatus
                result(self.flutterEngineManager.responseGenerator.getReKYCFlag(status: status ?? "", reason: reason ?? ""))
            case .getReKYCEnabled:
                result(self.flutterEngineManager.responseGenerator.getReKYCEnabled(enabled: configurationService.reKYCEnabled.value))
            case .showReKYCFlow:
                self.delegate?.showReKYCFlow(coordinator: self)
            case .showReKYCDialog:
                self.showReKYCDialog()
            case .sendToPartialEngine:
                guard let dictionary = call.arguments as? Dictionary<String, Any>
                else {
                    result(FlutterError.init(code: "", message: "Invalid arguments passed", details: nil))
                    return
                }
                self.flutterEngineManager.getChannel(with: .savingsTab)?.invokeMethod(FlutterMethod.sendToPartialEngine.rawValue, arguments: dictionary)
                result(true)
            case .openSecurityDepositCreation:
                handleOpenSecurityDepositCreation(arguments: call.arguments, result: result)
            case .openMayaBlackDashboard:
                handleOpenMayaBlackDashboard()
            case .openLendingReprocess:
                handleOpenLendingReprocess(arguments: call.arguments)
            case .openCreditApplication:
                handleOpenCreditApplication()
            default: return
            }
        }
    }

    private func startCreditCardFullScreen(args: Any?) {
        if let arguments = args as? [String: Any],
           let route = arguments[Constants.Flutter.Cards.route] as? String,
           let params = arguments[Constants.Flutter.Cards.params] as? String {
            let coordinator = LoansCreditCardFullScreenCoordinator(route: route, params: params, presenter: savingsFullScreenRouter)
            coordinator.delegate = self
            coordinator.backActionDelegate = self
            coordinator.routeActionDelegate = self
            coordinator.presentationDirection = .right
            addChild(coordinator)
            coordinator.start()
        }
    }

    private func setUpCardsFullScreen(completion: ((Coordinator) -> Void)?) {
        let coordinator = MayaCardsV2FullScreenCoordinator(presenter: savingsFullScreenRouter, shouldPresentRouter: true)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.presentationDirection = .right
        addChild(coordinator)
        completion?(coordinator)
    }

    private func showSecurityDeposit() {
        let coordinator = MayaSecurityDepositCoordinator(presenter: savingsFullScreenRouter)
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        coordinator.routeActionDelegate = self

        addChild(coordinator)
        coordinator.start()
    }

    private func showCardsFullScreen(args: Any?) {
        setUpCardsFullScreen(completion: { [weak self] coordinator in
            guard let self else { return }
            coordinator.start {
                let getChannel = self.flutterEngineManager.getChannel(with: .cardsFull)
                getChannel?.invokeMethod(FlutterMethod.requestFullscreen.rawValue, arguments: args)
            }
        })
    }

    private func showLoansCreditCard() {
        startCreditCardFullScreen(args: [
            Constants.Flutter.LoansCreditCard.route: Constants.Flutter.LoansCreditCard.cardOverviewScreen,
            Constants.Flutter.Cards.params: ""
        ])
    }

    private func showLoansReprocessScreen(arguments: [String: String]) {
        let params = arguments.map({ "\($0)=\($1)" }).joined(separator: "&")
        startCreditCardFullScreen(args: [
            Constants.Flutter.LoansCreditCard.route: Constants.Flutter.LoansCreditCard.securityDepositReprocessScreen,
            Constants.Flutter.Cards.params: params
        ])
    }

    private func handleOpenLendingReprocess(arguments: Any?) {
        if !configurationService.loansCreditCardMayaBlackEnabled.value {
            return
        }
        guard let dictionary = arguments as? Dictionary<String, String>
        else { return }
        self.showLoansReprocessScreen(arguments: dictionary)
    }

    private func handleOpenMayaBlackDashboard() {
        if !configurationService.loansCreditCardMayaBlackEnabled.value {
            return
        }
        self.showCardsFullScreen(args: [Constants.Flutter.Cards.route: Constants.Flutter.Cards.cardDashboard, Constants.Flutter.Cards.productOfferingKey: Constants.Flutter.Cards.productOffering])
    }

    private func handleOpenCreditApplication() {
        if !configurationService.loansCreditCardMayaBlackEnabled.value {
            return
        }
        self.showLoansCreditCard()
    }

    private func handleOpenSecurityDepositCreation(arguments: Any?, result: FlutterResult?) {
        if !configurationService.loansCreditCardMayaBlackEnabled.value {
            return
        }
        guard let dictionary = arguments as? Dictionary<String, Any>
        else { return }
        self.loanApplicationId = dictionary["loan_application_id"] as? String ?? ""
        self.creditLimitMultiplier = dictionary["credit_limit_multiplier"] as? String ?? ""
        self.showSecurityDeposit()
        result?(nil)
    }

    private func showReKYCDialog() {
        let coordinator = MayaReKYCCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        coordinator.routeActionDelegate = self
        coordinator.analyticsModule = Analytics.SavingsDashboard()
        addChild(coordinator)
        coordinator.start()
    }
}

// MARK: MayaReKYCCoordinatorDelegate Functions
extension MayaSavingsFullScreenCoordinator: MayaReKYCCoordinatorDelegate {
    func reKYCCoordinatorDispose(_ coordinator: MayaReKYCCoordinator) {
        removeChild(coordinator)
    }

    func reKYCCoordinatorDidFinishKYC(_ coordinator: MayaReKYCCoordinator, shouldProceedToProfile: Bool = false, completion: (() -> Void)?) {
        removeChild(coordinator)
    }
}

// MARK: - MayaCardsV2FullScreenCoordinatorDelegate Methods
extension MayaSavingsFullScreenCoordinator: MayaCardsV2FullScreenCoordinatorDelegate {
    func didDismiss(coordinator: MayaCardsV2FullScreenCoordinator) {
        removeChild(coordinator)
    }
}

// MARK: - MayaSecurityDepositCoordinatorDelegate - Methods
extension MayaSavingsFullScreenCoordinator: MayaSecurityDepositCoordinatorDelegate {
    func didSuccessfullyCreateDeposit(coordinator: MayaSecurityDepositCoordinator, result: [String: String]) {
        self.showLoansReprocessScreen(arguments: result)
    }

    func didRequestInputData(coordinator: MayaSecurityDepositCoordinator) -> [String: String] {
        return ["loan_application_id": self.loanApplicationId, "credit_limit_multiplier": self.creditLimitMultiplier]
    }

    func didDismiss(coordinator: MayaSecurityDepositCoordinator) {
        removeChild(coordinator)
    }
}

// MARK: - LoansCreditCardFullScreenCoordinatorDelegate - Methods
extension MayaSavingsFullScreenCoordinator: LoansCreditCardFullScreenCoordinatorDelegate {
    func didRequestFullScreen(coordinator: LoansCreditCardFullScreenCoordinator) {
        // Do nothing
    }

    func didRequestFullScreen(coordinator: LoansCreditCardFullScreenCoordinator, route: String, args: [String: String], completion: (() -> Void)?) {
        // Do nothing
    }

    func didDismiss(coordinator: LoansCreditCardFullScreenCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
    }
}
