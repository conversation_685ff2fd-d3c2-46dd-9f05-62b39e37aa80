//
//  MayaLoansCoordinator.swift
//  PayMaya
//
//  Created by <PERSON> on 8/8/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
import ConfigurationService
import Flutter
import FlutterPluginRegistrant
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider

class MayaLoansCoordinator: Coordinator, AnalyticsProtocol {
    @Inject var storeProvider: StoreProvider
    @Inject private var flutterEngineManager: FlutterEngineManager
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2

    var sessionTimeoutResult: FlutterResult?

    override init(router: RouterProtocol = Router(), presenter: RouterProtocol? = nil) {
        super.init(router: router, presenter: presenter)
        let timeoutNotificationSignal = NotificationCenter.default.reactive.notifications(forName: NSNotification.Name(rawValue: sessionTimeoutSuccessNotification)).map { _ in }
        timeoutNotificationSignal.observe(on: UIScheduler()).observeValues { [weak self] in
            guard let self = self,
                  let databaseStore = self.storeProvider.target(DatabaseStore.self),
                  let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                  let token = user.token
            else {
                return
            }

            self.sessionTimeoutResult?(self.flutterEngineManager.responseGenerator.getSessionToken(token))
            self.sessionTimeoutResult = nil
        }?.addToDisposeBag(disposeBag)
    }

    override func start(completion: (() -> Void)? = nil) {
        flutterEngineManager.startEngine(with: .bnplTab)
        setMethodHandlerForTab()
        if let engine = flutterEngineManager.getEngine(with: .bnplTab) {
            let flutterViewController = FlutterViewController(engine: engine, nibName: nil, bundle: nil)
            let containerViewController = StoryboardScene.Others.flutterScrollViewControllerContainer.instantiate()
            containerViewController.view.setNeedsLayout() // Needed to load the view
            flutterViewController.willMove(toParent: containerViewController)
            containerViewController.addChild(flutterViewController)
            containerViewController.contentView.addSubview(flutterViewController.view)
            flutterViewController.didMove(toParent: containerViewController)
            flutterViewController.view.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.enclose(view: flutterViewController.view, inside: containerViewController.contentView)
            router.navigationController?.setNavigationBarHidden(true, animated: false)
            setRootModule(containerViewController, animated: false)
        }
    }

    private func setMethodHandlerForTab() {
        let channel = flutterEngineManager.getChannel(with: .bnplTab)

        channel?.setMethodCallHandler { [weak self] call, result in
            guard let self = self,
                  let method = FlutterMethod(rawValue: call.method)
            else { return }

            switch method {
            case .getSessionToken:
                guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
                      let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                      let token = user.token
                else {
                    return
                }
                result(self.flutterEngineManager.responseGenerator.getSessionToken(token))
            case .getAccessToken:
                guard let encryptedStore = self.storeProvider.target(EncryptedDefaultsStore.self),
                      let authorization = encryptedStore.read(EncryptedDefaultsStoreId.authorization, type: Authorization.self).value
                else {
                    return
                }
                result(self.flutterEngineManager.responseGenerator.getAccessToken(authorization.accessToken))
            case .getShieldSessionId:
                result(self.flutterEngineManager.responseGenerator.getShieldSessionId(Shield.shared().sessionId))
            case .onSessionTimeout:
                self.sessionTimeoutResult = result
                NotificationCenter.default.post(name: NSNotification.Name(rawValue: sessionTimeoutNotification), object: nil)
            case .getPinningFingerprint:
                if let propertyListStore = self.storeProvider.target(PropertyListStore.self),
                   let pinningFingerprint = propertyListStore.read(PropertyListStoreId.pinningFingerprint).value as? String {
                    result(self.flutterEngineManager.responseGenerator.getPinningFingerprint(pinningFingerprint))
                }
            case .logAnalyticsEvent:
                guard let jsonString = call.arguments as? String,
                      let jsonData = jsonString.data(using: .utf8),
                      let dictionary = try? JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
                      let name = dictionary["event_name"] as? String
                else { return }

                let attributes = dictionary["attributes"] as? [String: Any]
                self.analyticsUtils.logMayaEvents(name, attributes: attributes)
            case .requestFullscreen:
                self.startLoansFullScreen(args: call.arguments)
            case .reloadHome:
                self.flutterEngineManager.getChannel(with: .bnplTab)?.invokeMethod(FlutterMethod.reloadHome.rawValue, arguments: nil)
            case .showUpgradeScreen:
                self.showPreKYCFlow()
            case .getMin:
                guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
                      let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                      let msisdn = user.msisdn
                else {
                    return
                }
                result(self.flutterEngineManager.responseGenerator.getMin(msisdn))
            case .goToWallet:
                guard let dashboardURL = Constants.DeepLinkPath.dashboard.url else { return }
                self.routeActionDelegate?.didRequestStartDeepLink(self, with: dashboardURL, completion: nil)
            case .getToggle:
                let s3Toggles = [
                    FlutterToggles.simplifiedCheckout: configurationService.mayaLoansSimplifiedCheckoutEnabled,
                    FlutterToggles.loansV3: configurationService.mayaLoansV3Enabled,
                    FlutterToggles.loansICL: configurationService.mayaLoansICLEnabled,
                    FlutterToggles.loansULEReskin: configurationService.unifiedLendingExperienceReskin,
                    FlutterToggles.loansICLInitialEligibilityCheck: configurationService.iclInitialEligibilityCheckEnabled,
                    FlutterToggles.loansICLDropdownV2: configurationService.iclDropdownV2Enabled,
                    FlutterToggles.loansICLPhaseTwoV1: configurationService.iclPhaseTwoV1Enabled,
                    FlutterToggles.bnplDeepFreeze: configurationService.bnplDeepFreeze,
                    FlutterToggles.loansICLPhaseTwoV2: configurationService.iclPhaseTwoV2Enabled,
                    FlutterToggles.loansCreditCard: configurationService.loansCreditCardEnabled,
                    FlutterToggles.iclMothersMaidenName: configurationService.iclMothersMaidenNameV3Enabled,
                    FlutterToggles.iclToMambu: configurationService.iclToMambuEnabled,
                    FlutterToggles.iclDeviceScoringReferenceId: configurationService.iclDeviceScoringDataReferenceIdEnabled,
                    FlutterToggles.iclDbl: configurationService.iclDblEnabled
                ]

                let splitToggles = [
                    FlutterToggles.loansICLDeviceScoring: configurationServiceV2.iclDeviceScoringEnabled
                ]

                let togglesV1 = flutterEngineManager.responseGenerator.getToggles(from: s3Toggles)
                let togglesV2 = flutterEngineManager.responseGenerator.getTogglesV2(from: splitToggles)

                let mergedToggles = togglesV1.merged(with: togglesV2)

                self.analyticsUtils.logMayaEvents(Analytics.LendingToggles.loans.rawValue, attributes: mergedToggles)

                result(mergedToggles)
            case .uploadDocument:
                startIncomeDocumentation()
            default: return
            }
        }
    }

    private func startLoansFullScreen(args: Any?) {
        self.setUpLoansFullScreen(completion: { coordinator in
            self.flutterEngineManager.getChannel(with: .bnplFull)?.invokeMethod(FlutterMethod.requestFullscreen.rawValue, arguments: args) { _ in
                coordinator.start()
            }
        })
    }

    private func setUpLoansFullScreen(completion: ((Coordinator) -> Void)?) {
        let coordinator = MayaLoansFullScreenCoordinator(presenter: router)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        completion?(coordinator)
    }

    private func showPreKYCFlow() {
        let coordinator = MayaPreKYCCoordinator(presenter: router)
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        coordinator.routeActionDelegate = self
        coordinator.analyticsModule = Analytics.LoansDashboard()
        addChild(coordinator)
        coordinator.start()
    }

    private func startIncomeDocumentation(completion: (() -> Void)? = nil) {
        let coordinator = MayaIncomeDocumentationCoordinator(presenter: router)
        coordinator.routeActionDelegate = self
        coordinator.backActionDelegate = self
        coordinator.delegate = self
        addChild(coordinator)
        coordinator.start(completion: completion)
    }

    func handleDocumentSubmitSuccess(_ data: [String: Any]) {
        startLoansFullScreen(args: [Constants.Flutter.BNPL.route: Constants.Flutter.ICL.reviewingApplicationScreen])
    }
}

// MARK: - MayaLoansFullScreenCoordinatorDelegate Methods
extension MayaLoansCoordinator: MayaLoansFullScreenCoordinatorDelegate {
    func didDismissLoans(coordinator: MayaLoansFullScreenCoordinator) {
        removeChild(coordinator)
    }

    func didCompleteLoans(coordinator: MayaLoansFullScreenCoordinator) {
        // Do nothing
    }

    func didRequestDocumentUpload(coordinator: MayaLoansFullScreenCoordinator, completion: (() -> Void)?) {
        startIncomeDocumentation(completion: completion)
    }
}

// MARK: - MayaPreKYCCoordinatorDelegate
extension MayaLoansCoordinator: MayaPreKYCCoordinatorDelegate {
    func preKYCCoordinatorDidFinishKYC(_ coordinator: MayaPreKYCCoordinator, completion: (() -> Void)?) {
        removeChild(coordinator)
    }
}

// MARK: - DeepLinkHandler Methods
extension MayaLoansCoordinator: DeepLinkHandler {
    func open(deepLink: DeepLink) -> DeepLinkHandling {
        switch deepLink.route {
        case .dashboard(.loans(let route)):
            let isICLEnabled = configurationService.mayaLoansICLEnabled.value
            if .icl == route && isICLEnabled {
                startLoansFullScreen(args: [Constants.Flutter.BNPL.route: Constants.Flutter.BNPL.introScreen])
            }
            return .opened(deepLink, nil)
        default:
            return .rejected(deepLink, .notHandled, identifier)
        }
    }
}

// MARK: MayaIncomeDocumentationCoordinatorDelegate methods
extension MayaLoansCoordinator: MayaIncomeDocumentationCoordinatorDelegate {
    func didDismiss(coordinator: MayaIncomeDocumentationCoordinator) {
        removeChild(coordinator)
    }

    func didSetContentHeader(coordinator: MayaIncomeDocumentationCoordinator) -> Array<String> {
        return [L10n.Loans.Icl.Dbl.Upload.header, L10n.Loans.Icl.Dbl.Upload.subheader]
    }

    func didSubmitDocument(coordinator: MayaIncomeDocumentationCoordinator, data: [String: Any]) {
        handleDocumentSubmitSuccess(data)
    }
}
