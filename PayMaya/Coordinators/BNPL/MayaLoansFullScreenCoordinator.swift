//
//  MayaLoansFullScreenCoordinator.swift
//  PayMaya
//
//  Created by <PERSON> on 8/8/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
import ConfigurationService
import Flutter
import FlutterPluginRegistrant
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider

protocol MayaLoansFullScreenCoordinatorDelegate: AnyObject {
    func didDismissLoans(coordinator: MayaLoansFullScreenCoordinator)
    func didCompleteLoans(coordinator: MayaLoansFullScreenCoordinator)
    func didRequestDocumentUpload(coordinator: MayaLoansFullScreenCoordinator, completion: (() -> Void)?)
}

class MayaLoansFullScreenCoordinator: Coordinator, AnalyticsProtocol {
    @Inject var storeProvider: StoreProvider
    @Inject private var flutterEngineManager: FlutterEngineManager
    @Inject private var configurationService: ConfigurationService
    @Inject private var configurationServiceV2: ConfigurationServiceV2
    @Inject private var ravenWrapper: RavenWrapper

    private var credolabChannel: FlutterMethodChannel?
    weak var delegate: MayaLoansFullScreenCoordinatorDelegate?
    var sessionTimeoutResult: FlutterResult?
    private var mfaResult: FlutterResult?

    override init(router: RouterProtocol = Router(), presenter: RouterProtocol? = nil) {
        super.init(router: router, presenter: presenter)
        flutterEngineManager.startEngine(with: .bnplFull)
        setMethodHandlerForTab()

        let timeoutNotificationSignal = NotificationCenter.default.reactive.notifications(forName: NSNotification.Name(rawValue: sessionTimeoutSuccessNotification)).map { _ in }
        timeoutNotificationSignal.observe(on: UIScheduler()).observeValues { [weak self] in
            guard let self = self,
                  let databaseStore = self.storeProvider.target(DatabaseStore.self),
                  let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                  let token = user.token
            else {
                return
            }

            self.sessionTimeoutResult?(self.flutterEngineManager.responseGenerator.getSessionToken(token))
            self.sessionTimeoutResult = nil
        }?.addToDisposeBag(disposeBag)
    }

    override func start(completion: (() -> Void)? = nil) {
        if let engine = flutterEngineManager.getEngine(with: .bnplFull) {
            setCredolabMethodHandler(engine)

            let flutterViewController = FlutterViewController(engine: engine, nibName: nil, bundle: nil)
            router.navigationController?.setNavigationBarHidden(true, animated: false)
            setRootModule(flutterViewController, animated: true)
            presenter?.present(router, animated: true, completion: completion)
        }
    }

    private func handleRavenExtract() {
        if configurationServiceV2.lendingDataExtractionEnabled.value {
            guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
                  let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
            else {
                return
            }

            ravenWrapper.extract(customerID: user.walletId,
                                 transactionID: nil,
                                 triggeredBy: Constants.Trigger.lending,
                                 eventTrigger: Constants.EventTrigger.loans,
                                 completion: { res in
                let attributes = [
                    AnalyticsAttributeKey.triggeredBy: Constants.Trigger.lending,
                    AnalyticsAttributeKey.eventTrigger: Constants.EventTrigger.loans
                 ]

                let analyticsService = ContainerWrapper.shared.resolve(AnalyticsService.self) as AnalyticsService

                switch res {
                case .success(let _):
                    analyticsService.logMayaEvents(action: .success(Analytics.MayaDataExtraction.extract), keyAttributes: attributes)
                case .failure(let _):
                    analyticsService.logMayaEvents(action: .failure(Analytics.MayaDataExtraction.extract), keyAttributes: attributes)
                }
            })
        }
    }

    private func getGeolocation(completion: @escaping (String, String?) -> Void) {
        ravenWrapper.getCurrentLocation { location, error in
            if let location = location {
                completion(Constants.Flutter.ICL.Geolocation.success, nil)
            } else if let error = error {
                switch error {
                case .notDetermined:
                    completion(
                        Constants.Flutter.ICL.Geolocation.permissionNotDetermined,
                        Constants.Flutter.ICL.GeolocationErrorMessages.notDetermined
                    )
                case .denied, .restricted:
                    completion(
                        Constants.Flutter.ICL.Geolocation.permissionDenied,
                        Constants.Flutter.ICL.GeolocationErrorMessages.denied
                    )
                case .unknown:
                    completion(
                        Constants.Flutter.ICL.Geolocation.failed,
                        Constants.Flutter.ICL.GeolocationErrorMessages.unknown
                    )
                }
            } else {
                completion(
                    Constants.Flutter.ICL.Geolocation.failed,
                    Constants.Flutter.ICL.GeolocationErrorMessages.locationNotFound
                )
            }
        }
    }

    private func startDataExtract(completion: @escaping (String, String?) -> Void) {
        guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
              let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
        else {
            completion(
                Constants.Flutter.ICL.DataExtract.failed,
                Constants.Flutter.ICL.DataExtractErrorMessages.extractionFailed
            )
            return
        }

        ravenWrapper.extract(customerID: user.walletId,
                             transactionID: nil,
                             triggeredBy: Constants.Trigger.lending,
                             eventTrigger: Constants.EventTrigger.loans) { res in
            switch res {
            case .success:
                completion(Constants.Flutter.ICL.DataExtract.success, nil)
            case .failure:
                completion(
                    Constants.Flutter.ICL.DataExtract.failed,
                    Constants.Flutter.ICL.DataExtractErrorMessages.extractionFailed
                )
            }
        }
    }

    private func setCredolabMethodHandler(_ engine: FlutterEngine) {
        credolabChannel = FlutterMethodChannel(name: Constants.Flutter.CreditScoring.credolabChannel, binaryMessenger: engine.binaryMessenger)
        credolabChannel?.setMethodCallHandler({ [weak self] call, result in
            guard let method = FlutterMethod(rawValue: call.method),
                  let self = self
            else { return }

            if method == .collect {
                CredolabDataService().collect { res in
                    switch res {
                    case .success(let data):
                        result(data)
                    case .error(let errorCode, let errorMessage):
                        result(FlutterError(code: String(errorCode), message: errorMessage, details: nil))
                    @unknown default:
                        result(FlutterError(code: String(-1), message: nil, details: nil))
                    }
                }

                // trigger raven data extraction
                self.handleRavenExtract()
            }
        })
    }

    private func setMethodHandlerForTab() {
        flutterEngineManager.getChannel(with: .bnplFull)?.setMethodCallHandler { [weak self] call, result in
            guard let self = self,
                  let method = FlutterMethod(rawValue: call.method)
            else { return }

            switch method {
            case .getSessionToken:
                guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
                      let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                      let token = user.token
                else {
                    return
                }
                result(self.flutterEngineManager.responseGenerator.getSessionToken(token))
            case .getAccessToken:
                guard let encryptedStore = self.storeProvider.target(EncryptedDefaultsStore.self),
                      let authorization = encryptedStore.read(EncryptedDefaultsStoreId.authorization, type: Authorization.self).value
                else {
                    return
                }
                result(self.flutterEngineManager.responseGenerator.getAccessToken(authorization.accessToken))
            case .getShieldSessionId:
                result(self.flutterEngineManager.responseGenerator.getShieldSessionId(Shield.shared().sessionId))
            case .onSessionTimeout:
                self.sessionTimeoutResult = result
                NotificationCenter.default.post(name: NSNotification.Name(rawValue: sessionTimeoutNotification), object: nil)
            case .getPinningFingerprint:
                if let propertyListStore = self.storeProvider.target(PropertyListStore.self),
                   let pinningFingerprint = propertyListStore.read(PropertyListStoreId.pinningFingerprint).value as? String {
                    result(self.flutterEngineManager.responseGenerator.getPinningFingerprint(pinningFingerprint))
                }
            case .logAnalyticsEvent:
                guard let jsonString = call.arguments as? String,
                      let jsonData = jsonString.data(using: .utf8),
                      let dictionary = try? JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
                      let name = dictionary["event_name"] as? String
                else { return }

                let attributes = dictionary["attributes"] as? [String: Any]
                self.analyticsUtils.logMayaEvents(name, attributes: attributes)
            case .reloadHome:
                self.flutterEngineManager.getChannel(with: .bnplTab)?.invokeMethod(FlutterMethod.reloadHome.rawValue, arguments: nil)
            case .dismiss:
                handleDismiss()
            case .complete:
                handleDismiss()
                delegate?.didCompleteLoans(coordinator: self)
            case .getMin:
                guard let databaseStore = self.storeProvider.target(DatabaseStore.self),
                      let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                      let msisdn = user.msisdn
                else {
                    return
                }
                result(self.flutterEngineManager.responseGenerator.getMin(msisdn))
            case .goToWallet:
                guard let dashboardURL = Constants.DeepLinkPath.dashboard.url else { return }
                self.routeActionDelegate?.didRequestStartDeepLink(self, with: dashboardURL, completion: nil)
            case .getToggle:
                let s3Togggles = [
                    FlutterToggles.simplifiedCheckout: configurationService.mayaLoansSimplifiedCheckoutEnabled,
                    FlutterToggles.loansV3: configurationService.mayaLoansV3Enabled,
                    FlutterToggles.loansICL: configurationService.mayaLoansICLEnabled,
                    FlutterToggles.loansICLInitialEligibilityCheck: configurationService.iclInitialEligibilityCheckEnabled,
                    FlutterToggles.loansICLDropdownV2: configurationService.iclDropdownV2Enabled,
                    FlutterToggles.loansICLPhaseTwoV1: configurationService.iclPhaseTwoV1Enabled,
                    FlutterToggles.bnplDeepFreeze: configurationService.bnplDeepFreeze,
                    FlutterToggles.loansICLPhaseTwoV2: configurationService.iclPhaseTwoV2Enabled,
                    FlutterToggles.loansCreditCard: configurationService.loansCreditCardEnabled,
                    FlutterToggles.iclMothersMaidenName: configurationService.iclMothersMaidenNameV3Enabled,
                    FlutterToggles.iclToMambu: configurationService.iclToMambuEnabled,
                    FlutterToggles.iclDeviceScoringReferenceId: configurationService.iclDeviceScoringDataReferenceIdEnabled,
                    FlutterToggles.iclDbl: configurationService.iclDblEnabled
                ]

                let splitToggles = [
                    FlutterToggles.iclMfa: configurationServiceV2.iclMfaEnabled,
                    FlutterToggles.iclGeolocation: configurationServiceV2.iclGeolocationEnabled,
                    FlutterToggles.lendingDataExtraction: configurationServiceV2.lendingDataExtractionEnabled,
                    FlutterToggles.loansICLDeviceScoring: configurationServiceV2.iclDeviceScoringEnabled
                ]

                let togglesV1 = flutterEngineManager.responseGenerator.getToggles(from: s3Togggles)
                let togglesV2 = flutterEngineManager.responseGenerator.getTogglesV2(from: splitToggles)
                let mergedToggles = togglesV1.merged(with: togglesV2)

                self.analyticsUtils.logMayaEvents(Analytics.LendingToggles.loans.rawValue, attributes: mergedToggles)

                result(mergedToggles)
            case .requestFullscreen:
                guard let args = call.arguments as? Dictionary<String, Any> else { return }
                let billerKey = Constants.Flutter.ICL.Keys.biller
                let amountKey = Constants.Flutter.ICL.Keys.amount
                let accountNumberKey = Constants.Flutter.ICL.Keys.accountNumber

                let biller = args[billerKey] as? String ?? ""
                let amount = args[amountKey] as? String ?? ""
                let accountNumber = args[accountNumberKey] as? String ?? ""

                let billerItem = BillerItem(slug: biller, name: "", imageURL: nil, prefilledValues: [billerKey: biller, amountKey: amount, accountNumberKey: accountNumber])
                self.startPaymentFlow(billerItem)
            case .uploadDocument:
                let presenter = self.presenter ?? self.router
                let presentable = presenter.toPresentable()

                presentable.forceShowMayaLoader()
                handleDismiss(animated: false)

                delegate?.didRequestDocumentUpload(coordinator: self) {
                    presentable.hideMayaLoader()
                }
            case .processMfa:
                guard let challengeId = call.arguments as? String,
                      let databaseStore = self.storeProvider.target(DatabaseStore.self),
                      let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
                      let mobileNumber = user.msisdn,
                      let walletId = user.walletId
                else { return }

                let data: [MFAParameterKeys: Any] = [
                    .mfaType: MFAType.face,
                    .challengeId: challengeId,
                    .lifestyleId: walletId,
                    .transactionType: MFATransactionType.iclDisburse.rawValue
                ]
                mfaResult = result
                startMayaMFACoordinator(data: data)
            case .getGeolocation:
                getGeolocation { resultCode, errorMessage in
                    if resultCode == Constants.Flutter.ICL.Geolocation.success {
                        result(resultCode)
                    } else {
                        result(FlutterError(code: resultCode, message: errorMessage, details: nil))
                    }
                }
            case .startDataExtract:
                startDataExtract { resultCode, errorMessage in
                    if resultCode == Constants.Flutter.ICL.DataExtract.success {
                        result(resultCode)
                    } else {
                        result(FlutterError(code: resultCode, message: errorMessage, details: nil))
                    }
                }
            default: return
            }
        }
    }

    private func startMayaMFACoordinator(data: [MFAParameterKeys: Any]) {
        let coordinator = MayaMFACoordinator(data: data,
                                             router: router,
                                             presenter: presenter)
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        addChild(coordinator)
        coordinator.start()
    }
}

private extension MayaLoansFullScreenCoordinator {
    func startPaymentFlow(_ billerItem: BillerItem, favoriteItem: FavoriteItem? = nil) {
        let coordinator = MayaPayBillsPaymentCoordinator(presenter: router)
        let paymentViewModel = MayaPayBillsPaymentViewModel(with: favoriteItem, and: billerItem)
        coordinator.viewModel = paymentViewModel
        coordinator.delegate = self
        coordinator.backActionDelegate = self
        coordinator.routeActionDelegate = self
        coordinator.presentationDirection = .right
        addChild(coordinator)
        coordinator.start()
    }

    func handleDismiss(animated: Bool = true) {
        self.presenter?.dismissModule(animated: animated) { [weak self] in
            guard let self = self else { return }
            self.flutterEngineManager.getChannel(with: .bnplFull)?.setMethodCallHandler(nil)
            self.flutterEngineManager.clearEngine(with: .bnplFull)
            self.flutterEngineManager.initializeEngine(with: .bnplFull)
            self.delegate?.didDismissLoans(coordinator: self)
        }
    }
}

// MARK: - MayaPayBillsPaymentCoordinatorDelegate Methods
extension MayaLoansFullScreenCoordinator: MayaPayBillsPaymentCoordinatorDelegate {
    func didRequestRedirectToDashboard(_ coordinator: MayaPayBillsPaymentCoordinator) {
        removeChild(coordinator)
        guard let url = Constants.DeepLinkPath.dashboard.url else { return }
        router.dismissModule(animated: true) { [weak self] in
            guard let self = self else { return }
            self.routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
        }
    }

    func didTapBack(_ coordinator: MayaPayBillsPaymentCoordinator) {
        // do nothing
    }

    func didPaySuccessfully(_ coordinator: MayaPayBillsPaymentCoordinator) {
        router.popToRootViewController(animated: false)
        router.dismissModule(animated: true, completion: { [weak self, weak coordinator] in
            guard let self = self, let coordinator = coordinator else { return }
            self.removeChild(coordinator)
        })
    }

    func didReceiveError(_ coordinator: MayaPayBillsPaymentCoordinator, error: ErrorAlertViewModel?) {
        self.removeChild(coordinator)
        if let error = error {
            showErrorAlert(error, useMayaModal: true)
        }
    }

    func didRequestRedirectToPayBills(_ coordinator: MayaPayBillsPaymentCoordinator) {
        removeChild(coordinator)
        guard let url = Constants.DeepLinkPath.paybills.url else { return }
        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
    }
}

// MARK: MayaMFACoordinatorDelegate Methods
extension MayaLoansFullScreenCoordinator: MayaMFACoordinatorDelegate {
    func didCancelMFA(_ coordinator: MayaMFACoordinator, info: [MFAParameterKeys: Any]) {
        mfaResult?(Constants.Flutter.MFA.Result.cancel)
        mfaResult = nil
        removeChild(coordinator)
    }

    func didEncounterError(_ coordinator: MayaMFACoordinator, info: [MFAParameterKeys: Any], dismissCaller: Bool) {
        mfaResult?(Constants.Flutter.MFA.Result.error)
        mfaResult = nil
        removeChild(coordinator)
    }

    func didFailMFA(_ coordinator: MayaMFACoordinator, info: [MFAParameterKeys: Any]) {
        mfaResult?(Constants.Flutter.MFA.Result.maxAttempt)
        mfaResult = nil
        removeChild(coordinator)
    }

    func didSuccessfullyAuthenticate(_ coordinator: MayaMFACoordinator, info: [MFAParameterKeys: Any]) {
        mfaResult?(Constants.Flutter.MFA.Result.success)
        mfaResult = nil
        removeChild(coordinator)
    }

    func shouldPerformOTPFallback(_ coordinator: MayaMFACoordinator, info: [MFAParameterKeys: Any]) {
        mfaResult?(Constants.Flutter.MFA.Result.fallback)
        mfaResult = nil
        removeChild(coordinator)
    }
}
