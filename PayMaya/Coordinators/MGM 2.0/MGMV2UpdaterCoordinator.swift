//
//  MGMV2UpdaterCoordinator.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 7/4/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//
import AssetProvider

class MGMV2UpdaterCoordinator: Coordinator {
    override func start() {
        let configuration = FeatureUpdaterContentConfiguration(navigationBarTitle: L10n.Maya.MgmV2.Title.inviteAFriend)
        let viewModel = MayaFeatureUpdaterViewModel(configuration: configuration)

        let vc = MayaFeatureUpdaterViewController()
        vc.routeActionDelegate = self
        vc.delegate = self
        vc.bind(viewModel)

        rootViewController = vc
        router.push(vc, animated: true, popCompletion: nil)
    }
}

extension MGMV2UpdaterCoordinator: MayaFeatureUpdaterViewControllerDelegate {
    func viewControllerDidTapLeftBarButton(_ viewController: MayaFeatureUpdaterViewController) {
        backActionDelegate?.didTapBack(self, completion: nil)
    }
}
