//
//  Constants+QR.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 6/2/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation

struct QRFieldTag: Codable, Equatable {
    let rootTag: String
    let subTag: String
    let name: String

    init(rootTag: String, subTag: String = "", name: String) {
        self.rootTag = rootTag
        self.subTag = subTag
        self.name = name
    }
}

extension QRFieldTag {
    func fullTag() -> String {
        return subTag.isEmpty ? rootTag : "\(rootTag).\(subTag)"
    }
}

struct QRPHField {
    static let payloadFormatIndicator = QRFieldTag(rootTag: "00", name: "Payload Format Indicator")
    static let pointOfInitiation = QRFieldTag(rootTag: "01", name: "Point of Initiation Method")
    static let p2pMerchantAccountInformation = QRFieldTag(rootTag: "27", name: "Merchant Account Information")
    static let p2mMerchantAccountInformation = QRFieldTag(rootTag: "28", name: "Merchant Account Information")
    static let p2pAdditionalDataGloballyUniqueId = QRFieldTag(rootTag: "62", subTag: "00", name: "P2P Additional Data Globally Unique Id")
    static let p2mAdditionalDataGloballyUniqueId = QRFieldTag(rootTag: "62", subTag: "00", name: "P2M Additional Data Globally Unique Id")
    static let p2mGloballyUniqueId = QRFieldTag(rootTag: "28", subTag: "00", name: "P2M Globally Unique Id")
    static let p2pGloballyUniqueId = QRFieldTag(rootTag: "27", subTag: "00", name: "P2P Payment System Unique ID")
    static let p2pMerchantID = QRFieldTag(rootTag: "27", subTag: "03", name: "P2P Merchant ID")
    static let p2mMerchantID = QRFieldTag(rootTag: "28", subTag: "03", name: "P2M Merchant ID")
    static let p2pAcquirerID = QRFieldTag(rootTag: "27", subTag: "01", name: "P2P Acquirer ID")
    static let p2mAcquirerID = QRFieldTag(rootTag: "28", subTag: "01", name: "P2M Acquirer ID")
    static let paymentType = QRFieldTag(rootTag: "27", subTag: "02", name: "Payment Type")
    static let p2pMerchantCreditAccount = QRFieldTag(rootTag: "27", subTag: "04", name: "P2P Merchant Credit Account")
    static let p2mMerchantCreditAccount = QRFieldTag(rootTag: "28", subTag: "04", name: "P2M Merchant Credit Account")
    static let merchantMobileNumber = QRFieldTag(rootTag: "27", subTag: "05", name: "Merchant Mobile Number")
    static let p2mProxyNotifFlag = QRFieldTag(rootTag: "28", subTag: "05", name: "P2M Proxy Notif Flag")
    static let merchantCategoryCode = QRFieldTag(rootTag: "52", name: "Merchant Category Code")
    static let transactionCurrencyCode = QRFieldTag(rootTag: "53", name: "Transaction Currency Code")
    static let transactionAmount = QRFieldTag(rootTag: "54", name: "Transaction Amount")
    static let convenienceIndicator = QRFieldTag(rootTag: "55", name: "Convenience Indicator")
    static let convenienceFeeFixed = QRFieldTag(rootTag: "56", name: "Value of Convenience Fee Fixed")
    static let convenienceFeePercentage = QRFieldTag(rootTag: "57", name: "Value of Convenience Fee Percentage")
    static let countryCode = QRFieldTag(rootTag: "58", name: "Country Code")
    static let merchantName = QRFieldTag(rootTag: "59", name: "Merchant Name")
    static let merchantCity = QRFieldTag(rootTag: "60", name: "Merchant City")
    static let postalCode = QRFieldTag(rootTag: "61", name: "Postal Code")
    static let mobileNumber = QRFieldTag(rootTag: "62", subTag: "02", name: "Mobile Number")
    static let storeLabel = QRFieldTag(rootTag: "62", subTag: "03", name: "Store Label")
    static let loyaltyNumber = QRFieldTag(rootTag: "62", subTag: "04", name: "Loyalty Number")
    static let referenceLabel = QRFieldTag(rootTag: "62", subTag: "05", name: "Reference Label")
    static let customerLabel = QRFieldTag(rootTag: "62", subTag: "06", name: "Customer Label")
    static let terminalLabel = QRFieldTag(rootTag: "62", subTag: "07", name: "Terminal Label")
    static let purpose = QRFieldTag(rootTag: "62", subTag: "08", name: "Purpose of Transaction")
    static let additionalConsumerDataRequest = QRFieldTag(rootTag: "62", subTag: "09", name: "Additional Consumer Data Request")
    static let crc = QRFieldTag(rootTag: "63", name: "Cyclic Redundancy Check")
    static let languagePreference = QRFieldTag(rootTag: "64.00", name: "Language Preference")
    static let merchantNameAlternateLanguage = QRFieldTag(rootTag: "64", subTag: "01", name: "Merchant Name – Alternate Language")
    static let merchantCityAlternateLanguage = QRFieldTag(rootTag: "64", subTag: "02", name: "Merchant City – Alternate Language")
    static let acquirerRequiredInformation = QRFieldTag(rootTag: "88", subTag: "01", name: "Acquirer-Required Information")
    static let qrType  = QRFieldTag(rootTag: "unknown", name: "Merchant Account Information")
    static let qrTypeP2B = QRFieldTag(rootTag: "unknown", name: "QRPH Type P2B")
    static let unknown = QRFieldTag(rootTag: "unknown", name: "unknown")
}

enum QRPHConstants {
    static let invalidQR: Int = -400
    static let qrPHFormatIndicator = "000201"
    static let qrPHCrcFieldTag = "6304"
    static let mayaAcquirerID = "PAPHPHM1XXX"
    static let sendMoneyReceipientPattern = "^((\\+?(63)|0)?\\d{10})|\\d{16}$"
    static let payloadFormatIndicator = "01"
    static let convenienceFeeFixedIndicator = "02"
    static let convenienceFeePercentageIndicator = "03"
    static let validValue = "***"
    static let p2pGloballyUniqueID = "com.p2pqrpay"
    static let billersGloballyUniqueID = "ph.ppmi.p2b"
    static let microGloballyUniqueID = "ph.ppmi.p2micro"
    static let p2mGloballyUniqueID = "ph.ppmi.p2m"
    static let pointOfInitiationStatic = "11"
    static let pointOfInitiationDynamic = "12"
}

enum QRPHValidation {
    static let validPointsOfInitiation = [
        QRPHConstants.pointOfInitiationStatic,
        QRPHConstants.pointOfInitiationDynamic
    ]
    static let validPaymentTypes = ["99964403"]
    static let validMerchantCategoryCodes = ["6016"]
    static let validTransactionCurrencyCodes = ["608"]
    static let validCountryCodes = ["PH"]
    static let validConvenienceIndicators = ["01", "02", "03"]
    static let merchantMobileNumberRegex = "\\+\\d{1,3}-[0-9()+-]{1,30}"
    // STANDARD Regex for only allowing special characters within ISO20022
    static let specialCharacterRegex = "^[A-Za-z0-9_~`!@#$%^&*()\\-+={}\\[\\]\\\\|:;\"'<>,.?/ Ññ]+$"
}

struct QRPHFieldLength {
    static let minMaxRange = 1...99
    static let cityRange = 1...15
    static let postalCodeRange = 4...10
    static let referenceLabelRange = 1...25
    static let storeNameRange = 0...15
    static let globallyUniqueIDRange = 1...19
    static let additionalGloballyUniqueIDRange = 1...14
    static let p2pMerchantCreditAccountNumberRange = 1...19
    static let p2mMerchantCreditAccountNumberRange = 1...25
    static let merchantIdRange = 1...15
    static let merchantMobileNumberRange = 1...15
    static let idRange = 1...25
    static let nameRange = 1...25
    static let claimIDRange = 1...25
    static let loyaltyNumberRange = 3...25
    static let categoryCodeRange = 1...70
    static let acquirerIDLength = 11
    static let terminalIDLength = 8
    static let proxyNotifFlagLength = 3
    static let merchantCategoryCodeLength = 4
    static let countryCodeLength = 2
    static let languagePreferenceLength = 2
    static let convenienceIndicatorLength = 2
    static let convenienceFeeFixIndicatorLength = 0...13
    static let convenienceFeePercentageIndicatorLength = 0...5
    static let alternateLanguageRange = 1...25
    static let purposeLength = 3
    static let additionalConsumerDataRequestRange = 0...3
    static let crc = 8
}

enum QRPHType {
    case p2pSendMoney
    case p2pBankTransfer
    case p2mOffUs
    case p2mOnUs
    case p2b
}
