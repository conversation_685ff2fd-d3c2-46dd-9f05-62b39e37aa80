//
//  Constants+P2M.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 11/4/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation

enum CreateMerchantType: String, Codable {
    case onUsStaticQrPh = "QR_ON_US_STATIC_QRPH"
    case onUsDynamicQrPh = "QR_ON_US_DYNAMIC_QRPH"
    case offUsDynamicQrPh = "QR_OFF_US_DYNAMIC_QRPH"
    case offUsStaticQrPh = "QR_OFF_US_STATIC_QRPH"
}

extension CreateMerchantType {
    var analyticsMerchantType: AnalyticsConstants.Screen.Qr.MerchantType {
        switch self {
        case .onUsStaticQrPh, .onUsDynamicQrPh:
            return .onUs
        case .offUsStaticQrPh, .offUsDynamicQrPh:
            return .offUs
        }
    }
}

enum MerchantPaymentStatus: String, Codable {
    case failedPayment = "FAILED_PAYMENT"
    case failedFulfillment = "FAILED_FULFILLMENT"
    case fulfilled = "FULFILLED"
    case paid = "PAID"
    case pendingFulfillment = "PENDING_FULFILLMENT"
    case pendingPayment = "PENDING_PAYMENT"
    case refunded = "REFUNDED"
}

enum MerchantPaymentMethod: String, Codable {
    case mayaWallet
    case mayaCredit
    case unknown

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        let string = try container.decode(String.self)
        self = MerchantPaymentMethod(rawValue: string) ?? .unknown
    }
}
