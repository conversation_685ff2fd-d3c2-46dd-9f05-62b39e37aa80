//
//  Constants+WebView.swift
//  PayMaya
//
//  Created by <PERSON> on 07/11/2016.
//  Copyright © 2016 PayMaya Philippines, Inc. All rights reserved.
//

import ConfigurationService
import Foundation
import Injector
import MayaCoreData
import StoreProvider
import UIKit

extension Constants {
    enum WebView: String {
        case faq = "faq"
        case luckyGamesGovtRestriction = "s/article/Why-are-Lucky-Games-not-available-to-me-on-the-Maya-app"
        case topup = "topup-centers"
        case privacy = "privacy"
        case terms = "tos"
        case tnc = "terms-and-condition"
        case forgotPassword = "forgot-password"
        case forgotPasswordSuccess = "forgot-password/success"
        case promos = "deals"
        case howTo = "how-to"
        case parentalConsent = "downloads/ParentalConsentForm-KYCUpgrade.pdf"
        case partnerMerchants = "partner-merchants#feature"
        case support = "support"
        case accountLimits = "s/topic/0TO7F000000qB51WAE/account-limits"
        case mayaAccountLimits = "s/global-search/account%20limits"
        case duplicateAccount = "s/article/What-do-I-do-if-my-PayMaya-account-is-tagged-as-a-duplicate-one"
        case noAccessToMin = "s/article/I-lost-the-SIM-and-or-phone-with-the-number-that-s-registered-to-my-PayMaya-account"
        case guideToChangingMin = "s/article/How-do-I-change-the-registered-mobile-number-in-my-PayMaya-account"
        case salesForceQuery = "s/?q="
        case wayfinder = "find-a-smart-padala-agent"
        case quickGuide = "quick-guide#guides"
        case visitOTPHelpCenter = "s/article/What-is-a-One-Time-PIN-OTP"
        case transportMerchants = "partner-merchants/travel"
        case protect
        case visitEDDHelpCenter = "s/article/What-do-I-do-if-my-upgrade-application-was-put-on-hold-or-declined"
        case contactSupport = "s/article/Guide-for-your-Transaction-Concerns"
        case accountClosure = "s/article/Account-Closure"
        case bsp
        case mayaCreditHelpCenter = "s/topic/0TO2y000000TT54GAG/maya-credit-for-customers"
        case faqsMayaCredit = "loans/credit"
        case about = "about"
        case donate
        case mayaMall
        case bankAccountLinking = "s/article/My-bank-account-is-already-linked-to-another-Maya-account-Can-I-link-the-same-account-to-my-Maya-account"
        case mayaCreditPartnerMerchants = "credit-partner-merchants"
        case tinboRegistration = "?utm_source=maya&utm_campaign=mayaxsvn&utm_id=100044&utm_medium=app&utm_content=registration"
        case mayaBankPrivacy = "privacy/"
        case mayaBankTerms = "terms-conditions/"
        case serviceAdvisories
        case loginHistoryMain = "s/article/What-is-the-Maya-Log-in-History"
        case loginHistoryDetails = "s/article/What-can-I-do-with-the-Log-In-History"
        case baseHelpCenter = "s/"

        var description: String {
            switch self {
            case .faq, .support:
                return L10n.Profile.Menu.Title.help
            case .topup:
                return L10n.Profile.Menu.Title.topup
            case .privacy, .mayaBankPrivacy:
                return L10n.Settings.Menu.Title.privacy
            case .terms, .mayaBankTerms:
                return L10n.Settings.Menu.Title.terms
            case .tnc:
                return L10n.Settings.Menu.Title.tnc
            case .forgotPassword:
                return L10n.Forgot.Password.title
            case .partnerMerchants:
                return L10n.Profile.Menu.Title.partnerMerchants
            case .protect:
                return L10n.Dashboard.Service.Title.protect
            case .accountClosure:
                return L10n.Settings.Menu.Title.Account.closure
            case .donate:
                return L10n.Dashboard.Service.Title.donate
            default:
                return ""
            }
        }

        private var baseUrlString: String? {
            guard let propertyListStore = ContainerWrapper.shared.resolve(StoreProvider.self).target(PropertyListStore.self) else {
                return nil
            }

            switch self {
            case .forgotPassword:
                return propertyListStore.read(PropertyListStoreId.forgotPasswordUrl).value as? String
            case .forgotPasswordSuccess:
                return propertyListStore.read(PropertyListStoreId.forgotPasswordSuccessUrl).value as? String
            case .howTo:
                return propertyListStore.read(PropertyListStoreId.promotionsUrl).value as? String
            case .accountLimits, .duplicateAccount, .noAccessToMin, .guideToChangingMin, .visitEDDHelpCenter, .accountClosure:
                return propertyListStore.read(PropertyListStoreId.supportBaseUrl).value as? String
            case .contactSupport, .luckyGamesGovtRestriction, .mayaAccountLimits, .bankAccountLinking, .loginHistoryMain, .loginHistoryDetails, .baseHelpCenter:
                return propertyListStore.read(PropertyListStoreId.mayaSupportBaseUrl).value as? String
            case .wayfinder:
                return propertyListStore.read(PropertyListStoreId.smartPadalaBaseUrl).value as? String
            case .protect:
                return propertyListStore.read(PropertyListStoreId.protectServiceUrl).value as? String
            case .bsp:
                return propertyListStore.read(PropertyListStoreId.bspURL).value as? String
            case .mayaCreditHelpCenter:
                return propertyListStore.read(PropertyListStoreId.mayaBankBaseURL).value as? String
            case .donate:
                return propertyListStore.read(PropertyListStoreId.donateUrl).value as? String
            case .faqsMayaCredit, .mayaCreditPartnerMerchants, .mayaBankPrivacy, .mayaBankTerms:
                return propertyListStore.read(PropertyListStoreId.mayaBankWebViewBaseURL).value as? String
            case .mayaMall:
                return propertyListStore.read(PropertyListStoreId.mayaMallURL).value as? String
            case .tinboRegistration:
                return propertyListStore.read(PropertyListStoreId.tinboBaseURL).value as? String
            case .visitOTPHelpCenter:
                return propertyListStore.read(PropertyListStoreId.helpBaseUrl).value as? String
            case .serviceAdvisories:
                return propertyListStore.read(PropertyListStoreId.mayaServiceAdvisoriesURL.rawValue).value as? String
            default:
                return propertyListStore.read(PropertyListStoreId.webBaseUrl).value as? String
            }
        }

        var baseUrl: URL? {
            if let baseUrlString = baseUrlString {
                return URL(string: baseUrlString)
            }
            return nil
        }

        var url: URL? {
            if let baseUrlString = baseUrlString {
                return URL(string: "\(baseUrlString)\(self.rawValue)")
            } else {
                return URL(fileURLWithPath: self.rawValue)
            }
        }

        func urlWithAnchorTag(tag: String) -> URL? {
            if let baseUrlString = baseUrlString {
                return URL(string: "\(baseUrlString)\(self.rawValue)#\(tag)")
            } else {
                return URL(fileURLWithPath: self.rawValue)
            }
        }

        func urlWithPath(_ path: String) -> URL? {
            switch self {
            case .salesForceQuery:
                return URL(string: "s/" + path + "?q=")
            default:
                if let baseUrlString = baseUrlString {
                    return URL(string: "\(baseUrlString)\(self.rawValue)/\(path)")
                } else {
                    return URL(fileURLWithPath: self.rawValue)
                }
            }
        }

        static func accountLimitsURL() -> URL? {
            return Constants.WebView.mayaAccountLimits.url
        }

        static func statusURL() -> URL? {
            return URL(string: "https://maya.ph/status")
        }

        static func salesForceURLWithEncodedProfile(baseUrl: String, path: String = "") -> URL? {
            if let databaseStore = ContainerWrapper.shared.resolve(StoreProvider.self).target(DatabaseStore.self),
               let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
               let encodedParameter = WebView.getEncodedProfileData(of: user),
               let salesForcePath = WebView.salesForceQuery.urlWithPath(path) {
                return URL(string: "\(baseUrl)\(salesForcePath)\(encodedParameter)")
            }

            return URL(string: baseUrl)
        }

        static func mayaBankHelpWithProfileParamsURL(_ path: String = "") -> URL? {
            guard let propertyListStore = ContainerWrapper.shared.resolve(StoreProvider.self).target(PropertyListStore.self),
                  let baseUrl = propertyListStore.read(PropertyListStoreId.mayaBankBaseURL).value as? String else { return nil }

            return salesForceURLWithEncodedProfile(baseUrl: baseUrl, path: path)
        }

        static func mayaHelpWithProfilePassingParamsURL(_ path: String = "") -> URL? {
            guard let propertyListStore = ContainerWrapper.shared.resolve(StoreProvider.self).target(PropertyListStore.self),
                  let baseUrl = propertyListStore.read(PropertyListStoreId.helpBaseUrl).value as? String else { return nil }

            return salesForceURLWithEncodedProfile(baseUrl: baseUrl, path: path)
        }

        private static func getEncodedProfileData(of user: User) -> String? {
            guard let mobileNumber = user.msisdn else { return nil }
            let profileId = user.id
            let os = String(format: "%@(iOS %@)", UIDevice.current.model, UIDevice.current.systemVersion)
            let profileParameter = ProfileParameter(profileId: profileId, mobileNumber: mobileNumber, email: user.backupIdentity?.value, os: os)
            let encoder = JSONEncoder()
            if let encodedData = try? encoder.encode(profileParameter) {
                let encodedStringFromData = encodedData.base64EncodedString()
                return encodedStringFromData.encodedURLString()
            }
            return nil
        }

        static let internalDomains: [String] = [
            "paymaya.com",
            "maya.ph",
            "onevoyager--staging.sandbox.my.site.com",
            "mayabank.ph",
            "smartpadala.ph"
        ]
    }
}
