//
//  Constants+DeepLinkPath.swift
//  PayMaya
//
//  Created by <PERSON> on 8/5/21.
//  Copyright © 2021 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation

extension Constants {
    static let appScheme = "paymaya"

    enum DeepLinkPath: String {
        case accountLimits = "accountlimits"
        case accountrecovery = "accountrecovery"
        case addlPersonalDetails = "profile/addlpersonaldetails"
        case addmoney = "addmoney"
        case addmoneyCard = "addmoney/card"
        case approvalRequests = "approvalrequests"
        case banktransfer = "banktransfer"
        case beep = "transport/beep"
        case changeMobileNumber = "changemobilenumber"
        case chatWithUs = "chatwithus"
        case contactReference = "profile/contactreference"
        case crypto = "crypto"
        case cryptoV2 = "crypto_v2"
        case dashboard = "walletdashboard"
        case dataAndPersonalization = "datapersonalization"
        case deals = "deals"
        case donate = "donations"
        case empty = "" // Default path (i.e. paymaya:// or https://paymaya.com/app)
        case esims = "esims"
        case favorites = "favorites"
        case food = "food"
        case funds = "funds"
        case globalRemittance = "globalremittance"
        case globalStocks = "globalstocks"
        case government = "government"
        case help = "help"
        case inbox = "inbox"
        case insurance = "insurance"
        case loans = "loans"
        case loansICL = "loans/icl"
        case login = "login"
        case mayaAutoCashIn = "walletoptions/autocashin"
        case mayaCredit = "mayacredit"
        case load = "load"
        case loansCreditCard = "credit/card"
        case loansCreditCardPending = "credit/card/pending"
        case loansCreditCardAbout = "credit/card/about"
        case loansCreditCardRepayment = "credit/card/repayment"
        case loginHistory = "loginhistory"
        case mayaMall = "mall"
        case mayaSavingsToWallet = "mayaSavingsToWallet"
        case mayaStartSavings = "mayaStartSavings"
        case merchant = "merchant"
        case merchantRewards = "merchantrewards"
        case missions = "missions"
        case moneycard = "moneycard"
        case more = "more"
        case mycards = "mycards"
        case mycardsLink = "mycards/link"
        case mycardsStore = "cards/store"
        case mycardsVirtualView = "mycards/virtual/view"
        case cardDashboardBenefits = "mycards/carddashboard/benefits"
        case cardDashboard = "mycards/creditcarddashboard"
        case activateCreditCard = "mycards/activatecreditcard"
        case creditCardTransactionReceipt = "mycards/creditcard/transaction/receipt"
        case onboarding = "onboarding"
        case luckyGames = "lucky_games"
        case partnerMerchant = "partnermerchant"
        case pal = "philippineairlines"
        case payLater = "paylater"
        case paybills = "paybills"
        case paybillsReceipt = "paybills/receipt"
        case pbb
        case profile = "profile"
        case protect = "protect"
        case publicName = "publicname"
        case pullfunds = "pullfunds"
        case purchaseFinancing = "purchasefinancing"
        case pwp = "pwp"
        case quickguide = "quickguide"
        case rafflePromo = "rafflegame"
        case refer = "refer"
        case registration = "registration"
        case requestMoney = "requestmoney"
        case retail = "retail"
        case savings = "savings"
        case scanQr = "scanqr"
        case scheduler = "scheduler"
        case securityCenter = "securitycenter"
        case sendmoney = "sendmoney"
        case services = "services"
        case shop = "shop"
        case shopPurchaseHistory = "shop/history"
        case sparkHackathon = "sparkhackathon"
        case stocks = "stocks"
        case stocksGame = "stocksgame"
        case stream = "stream"
        case submitInviteCode = "submitinvitecode"
        case transport = "transport"
        case upgrade = "upgrade"
        case vouchers = "vouchers"
        case workDetails = "profile/workdetails"
        case blackpinkGiveaway = "giveaway"

        var url: URL? {
            return URL(string: Constants.appScheme + "://" + self.rawValue)
        }

        var appLinkPath: String {
            return "/app/" + self.rawValue
        }

        func urlWithRoute(_ route: Routes) -> URL? {
            var components = URLComponents()
            components.scheme = Constants.appScheme
            components.host = self.rawValue
            components.queryItems = [URLQueryItem(name: "route", value: route.rawValue)]
            return components.url
        }
    }

    enum Routes: String {
        case support
        case history
    }
}
