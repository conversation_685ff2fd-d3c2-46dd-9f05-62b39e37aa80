//
//  Constants+Types.swift
//  PayMaya
//
//  Created by <PERSON> on 23/01/2018.
//  Copyright © 2018 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import ConfigurationService
import Injector
import StoreProvider
import UIKit

extension Constants {
    struct Types {}
}

extension Constants.Types {
    enum TouchIdStatus: Int, Equatable {
        case touchIdNotSet
        case touchIdAllowed
        case touchIdDisallowed
    }
}

enum FundSourceType: String {
    case virtual = "virtual"
    case physical = "physical"
}

enum FundSourceBrand: String {
    case payMaya = "PAYMAYA"
    case smartPay = "SMARTPAY"
    case masterCard = "MASTERCARD"
}

enum FundSourceStatus: String, Equatable {
    case basic = "basic"
    case virtualCard = "virtual_card"
    case inactivePlus = "inactive_plus"
    case nonKyc = "instant_nonkyc"
    case plus = "plus"
}

enum FundSourceScheme: String, Equatable {
    case visa = "VISA"
    case masterCard = "MASTERCARD"
    case bancnet = "BANCNET"
}

enum KYCStatus: String {
    case submitted = "submitted"
    case rejected = "rejected"
    case approved = "kyc1"
    case forEdd = "for_edd"
    case none = ""
}

enum KYCLevel: String {
    case one = "1"
    case zeroPointFive = "0.5"
    case zero = "0"
}

enum AccountStatus: String {
    case active = "ACTIVE"
    case suspended = "SUSPENDED"
    case limited = "LIMITED"
    case closed = "CLOSED"
}

enum CreditApplicationStatus: String {
    case booked = "BOOKED"
    case approved = "APPROVED"
    case submitted = "SUBMITTED"
    case none = "NONE"
}

enum UserStatus: String {
    case unfunded = "unfunded"
}

enum ActivityColorClassification: String {
    case positive
    case negative
    case neutral

    static func classify(_ display: String?) -> ActivityColorClassification {
        return ActivityColorClassification(rawValue: display ?? "") ?? .neutral
    }

    var color: UIColor {
        switch self {
        case .positive:
            return CommonAsset.Colors.Constants.booger.color
        case .negative:
            return CommonAsset.Colors.Constants.rosyPink.color
        case .neutral:
            return CommonAsset.Colors.Constants.bluishGray.color
        }
    }

    var colorAsset: UIColor {
        switch self {
        case .positive:
            return CommonAsset.Colors.Success.success120S.color
        case .negative:
            return CommonAsset.Colors.Warning.warning100Base.color
        case .neutral:
            return CommonAsset.Colors.Gray.gray100Base.color
        }
    }

    var mayaColorAsset: UIColor {
        switch self {
        case .positive:
            return CommonAsset.MayaColors.Content.contentPrimaryGreen.color
        case .negative:
            return CommonAsset.MayaColors.Content.contentPrimaryBlack.color
        case .neutral:
            return CommonAsset.MayaColors.Content.contentGrey4.color
        }
    }
}

enum TransactionStatus: String, Equatable {
    case none
    case failed
    case processing
    case completed
    case `default`

    var statusMessage: String {
        switch self {
        case .processing:
            return L10n.Success.Status.Title.processing
        case .completed, .default:
            return L10n.Success.Status.Title.completed
        case .failed, .none:
            return String.empty
        }
    }

    var statusIcon: UIImage? {
        switch self {
        case .processing:
            return CommonAsset.Images.PayMaya.Common.iconProcessingStatus.image
        case .completed, .default:
            return CommonAsset.Images.PayMaya.Common.iconCheckedStatus.image
        case .failed, .none:
            return nil
        }
    }
}

enum ActivityStatus: String, Equatable {
    case authorized
    case posted
    case void
    case rejected

    var billerMessage: String {
        switch self {
        case .authorized:
            return L10n.Activity.Detail.Pay.Bills.authorized
        case .posted:
            return L10n.Activity.Detail.Pay.Bills.posted
        case .rejected, .void:
            return L10n.Activity.Detail.Pay.Bills.rejected
        }
    }

    var transactionMessage: String {
        switch self {
        case .authorized:
            return L10n.Activity.Detail.Transaction.Message.authorized
        case .posted:
            return L10n.Activity.Detail.Transaction.Message.completed
        case .rejected, .void:
            return L10n.Activity.Detail.Transaction.Message.cancelled
        }
    }

    var statusMessage: String {
        switch self {
        case .authorized:
            return L10n.Success.Status.Title.processing
        case .posted:
            return L10n.Success.Status.Title.completed
        case .rejected, .void:
            return L10n.Success.Status.Title.rejected
        }
    }

    var statusIcon: UIImage? {
        switch self {
        case .authorized:
            return CommonAsset.Images.PayMaya.Common.iconProcessingStatus.image
        case .posted:
            return CommonAsset.Images.PayMaya.Common.iconCheckedStatus.image
        case .rejected, .void:
            return nil
        }
    }

    var amountDetailTitle: String {
        switch self {
        case .authorized:
            return L10n.Activity.Detail.amount
        case .posted:
            return L10n.Maya.Activity.Detail.Amount.approved
        case .rejected, .void:
            return L10n.Maya.Activity.Detail.Amount.cancelled
        }
    }
}

enum BillsPaymentTransactionStatus: String, Equatable {
    case fulfilled = "FULFILLED"
    case failed = "FAILED"
    case postingFailed = "POSTING FAILED"
    case authorized = "AUTHORIZED"
    case new = "NEW"
    case posting = "POSTING"

    var statusMessage: String {
        switch self {
        /// Success state
        case .fulfilled:
            return CommonStrings.Maya.Receipt.Status.completed
        /// Failed state
        case .failed, .postingFailed:
            return CommonStrings.Maya.Receipt.Status.declined
        /// Processing state
        case .authorized, .new, .posting:
            return CommonStrings.Maya.Receipt.Status.processing
        }
    }

    var statusIcon: UIImage {
        switch self {
        /// Success state
        case .fulfilled:
            return CommonAsset.Images.Icons.iconSuccess.image
        /// Failed state
        case .failed, .postingFailed:
            return CommonAsset.Images.Icons.iconFailed.image
        /// Processing state
        case .authorized, .new, .posting:
            return CommonAsset.Images.Icons.iconProcessing.image        }
    }
}

enum PaymentMethod: String, Equatable {
    case wallet = "mayaWallet"
    case credit = "mayaCredit"

    var source: String {
        switch self {
        case .wallet:
            return CommonStrings.Maya.Receipt.Detail.Source.wallet
        case .credit:
            return CommonStrings.Maya.Receipt.Detail.Source.mayaCredit
        }
    }
}

extension ActivityStatus {
    init?(_ status: String?) {
        guard let status = status else {
            return nil
        }
        self.init(rawValue: status)
    }
}

enum ActivitySchemeType: String, Equatable {
    case purchase = "PURCHASE"
    case withdrawal = "WITHDRAWAL"
}

enum ActivityType: String, Equatable {
    case systemAlert = "System Alert"
    case purchase = "Purchase"
    case moneyIn = "Money In"
    case cashOut = "Cash Out"
    case transport = "Transport"
    case sendMoney = "Send Money"
    case padala = "Padala"
    case claim = "Claim"
    case payBills = "Pay Bills"

    func getBillerMessage(_ status: ActivityStatus?) -> String? {
        switch self {
        case .payBills:
            return status?.billerMessage
        default:
            return nil
        }
    }
}

enum CodeType {
    case any
    case sendMoney
    case qrphMerchant
    case qrphDynamicMerchant
    case instaPay
    case meralcoBarcode
}

enum QrCodeType {
    case invalid
    case sendMoney
    case merchant
}

enum CenterType: String {
    case normal
    case seven
    case moneyin
    case remittance = "WU"
    case barcode = "moneyin_barcode"
}

enum IdentityType: String {
    case msisdn = "msisdn"
    case email = "email"
}

enum PrivacyPolicyStatus: String {
    case accepted = "accepted"
    case notAccepted = "not_accepted"
    case error = "error"
}

enum PoliciesTypes: String {
    case disclosureStatement = "disclosureStatement"
    case termsAndConditions = "termsAndConditions"
    case truthAndTransparency = "truthAndTransparency"

    var index: Int {
        switch self {
        case .disclosureStatement: return 2
        case .termsAndConditions: return 0
        case .truthAndTransparency: return 1
        }
    }
}

enum CardCellType: Equatable {
    case virtualVisaCard
    case physicalVisaCard
    case virtualMasterCard
    case physicalMasterCard
    case linkPhysicalCard
    case activateVirtualCard
    case shopPayMayaCard
    case physicalBancnetCard

    static func getCellType(scheme: FundSourceScheme, type: FundSourceType) -> CardCellType {
        let schemeType = (type, scheme)
        switch schemeType {
        case (.virtual, .visa):
            return .virtualVisaCard
        case (.virtual, .masterCard):
            return .virtualMasterCard
        case (.physical, .visa):
            return .physicalVisaCard
        case (.physical, .masterCard):
            return .physicalMasterCard
        case (.physical, .bancnet):
            return .physicalBancnetCard
        default:
            return .virtualVisaCard
        }
    }
}

enum QuickTips {
    case security
    case reload
}

enum GovernmentIdType: String {
    case gsis = "GSIS"
    case sss = "SSS"
    case tin = "Tax Identification Number"
}

enum AccountCategory: String {
    case primary   = "primary"
    case secondary = "secondary"
    case additionalDocs = "additional_docs"
}

enum AccountClassification: String {
    case type1 /// Filipino legal age
    case type2 /// Filipino minor
    case type3 /// Foreign legal age
    case type4 /// Foreign minor
}

enum FieldType: String {
    case textfield
    case hidden
    case selectable
    case date
}

enum BIRFieldType: String {
    case branchCode = "branchCode"
    case rdoCode = "rdo"
    case formSeries = "formSeries"
    case formType = "formType"
    case taxType = "taxType"

    var priority: Int {
        switch self {
        case .branchCode: return 5
        case .rdoCode: return 4
        case .formSeries: return 3
        case .formType: return 2
        case .taxType: return 1
        }
    }
}

enum BillerHandling: String {
    case meralco = "MERALCO"
    case bir = "BIR"
    // Handling for non special billers
    case `default` = "DEFAULT"

    init(with slug: String) {
        self = BillerHandling(rawValue: slug.uppercased()) ?? .default
    }
}

enum LoadUpLimit: String {
    case notUpgraded = "PHP 50,000.00"
    case upgraded = "PHP 100,000.00"
}

enum MerchantIdentifierTag: String {
    case rootTag = "27"
    case acquirerId = "01"
    case paymentType = "02"
    case merchantId = "03"
    case creditAccount = "04"
    case mobileNumber = "05"
}

enum QRPHIdentifier {
    case rootTag
    case acquirerId
    case acquirerRequiredInformation
    case additionalGloballyUniqueIdentifier
    case globallyUniquePaymentSystemId
    case languagePreference
    case merchantAccountInformationTemplate

    // Same with MerchantIdentifierTag.creditAccount, should be removed here
    case merchantCreditAccountNumber

    case transactionAmount
    case merchantId
    case merchantName
    case merchantNameAlternateLanguage
    case merchantPostalCode
    case payloadFormatIndicator
    case pointOfInitiation
    // TODO: Remove usage of pointOfInitiationStatic and pointOfInitiationDynamic
    // Use QRPHConstants.pointOfInitiationStatic and QRPHConstants.pointOfInitiationDynamic instead
    case pointOfInitiationStatic
    case pointOfInitiationDynamic
    case proxyNotifFlag
    case language
    case convenienceIndicator
    case convenienceFeeFixed
    case convenienceFeePercentage

    var tag: String {
        switch self {
        case .globallyUniquePaymentSystemId,
             .additionalGloballyUniqueIdentifier,
             .languagePreference,
             .payloadFormatIndicator:
            return "00"
        case .acquirerId,
             .acquirerRequiredInformation,
             .merchantNameAlternateLanguage,
             .pointOfInitiation,
             .pointOfInitiationStatic,
             .pointOfInitiationDynamic:
            return "01"
        case .merchantId:
            return "03"
        case .merchantCreditAccountNumber:
            return "04"
        case .proxyNotifFlag:
            return "05"
        case .rootTag:
            return "28"
        case .transactionAmount:
            return "54"
        case .convenienceIndicator:
            return "55"
        case .convenienceFeeFixed:
            return "56"
        case .convenienceFeePercentage:
            return "57"
        case .merchantName:
            return "59"
        case .merchantPostalCode:
            return "61"
        case .language:
            return "64"
        case .merchantAccountInformationTemplate:
            return "88"
        }
    }

    var value: String {
        switch self {
        case .pointOfInitiationStatic: return "11"
        case .pointOfInitiationDynamic: return "12"
        default: return ""
        }
    }
}

enum QRPHGloballyUniqueIDs: String {
    case p2m = "ph.ppmi.p2m"
    case micro = "ph.ppmi.p2micro"
    case billers = "ph.ppmi.p2b"
}

enum QRPHP2PGloballyUniqueIDs: String {
    case p2pQrPay = "com.p2pqrpay"
}

enum UnionBankMerchant: String {
    case merchantId = "UBPHPHMMXXX"
    case code = "0041"
    case name = "Union Bank of the Philippines"
}

enum FavoriteType: String, Equatable, CaseIterable {
    case bank
    case moneysend
    case shop
    case billspay
    case unknown
    case all

    var title: String {
        switch self {
        case .bank: return CommonStrings.Favorites.BankTransfer.title
        case .moneysend: return CommonStrings.Favorites.SendMoney.title
        case .shop: return CommonStrings.Favorites.Shop.title
        case .billspay: return CommonStrings.Favorites.BillsPay.title
        case .unknown: return String.empty
        case .all: return CommonStrings.Favorites.All.title
        }
    }

    var deepLinkURL: URL? {
        return Constants.DeepLinkPath.favorites.url?.appendQueryItem(name: "type", value: self.rawValue)
    }

    var analyticsStringType: String {
        switch self {
        case .bank: return "bank transfer"
        case .moneysend: return "send money"
        case .billspay: return "bills"
        case .all: return "all"
        default: return ""
        }
    }
}

enum PreKYCMayaImage {
    case `default`
    case virtualCard

    var image: UIImage {
        switch self {
        case .default: return Asset.Images.More.Kyc.imageUpgrade.image
        case .virtualCard: return Asset.Images.More.MyCards.imageMayaVirtualCard.image
        }
    }

    var height: CGFloat {
        switch self {
        case .default: return 100
        case .virtualCard: return 206.7
        }
    }

    var width: CGFloat {
        switch self {
        case .default: return 156
        case .virtualCard: return 225
        }
    }
}

enum EKYCSubmissionType: String {
    case photo
    case video
}

enum EKYCProcessingResult: String {
    case blurred = "blurred"
    case noFace = "no_face"
    case multipleFaces = "multiple_face"
    case faceDetected = "face_detected"
}

/// Custom made status for determining profile type
enum KYCClientStatus: Equatable {
    case zero
    case pointFive
    case one
    case forEdd
    case verifiedSeller

    init(kycStatus: KYCStatus, isVerifiedSeller: Bool) {
        if isVerifiedSeller {
            self = .verifiedSeller
        } else {
            switch kycStatus {
            case .submitted:
                self = .pointFive
            case .approved:
                self = .one
            case .none, .rejected:
                self = .zero
            case .forEdd:
                self = .forEdd
            }
        }
    }

    static func == (lhs: KYCClientStatus, rhs: KYCClientStatus) -> Bool {
        switch (lhs, rhs) {
        case (.zero, .zero): return true
        case (.pointFive, .pointFive): return true
        case (.one, .one): return true
        case (.forEdd, .forEdd): return true
        case (.verifiedSeller, .verifiedSeller): return true
        default: return false
        }
    }

    var dashboardBadge: UIImage? {
        switch self {
        case .one:
            return Asset.Images.More.iconBadgeKYC1.image
        case .forEdd:
            if ContainerWrapper.shared.resolve(ConfigurationService.self).eddEnabled.value {
                return Asset.Images.More.iconBadgeForEdd.image
            }
            return Asset.Images.More.iconBadgeKYC0.image
        case .pointFive:
            if ContainerWrapper.shared.resolve(ConfigurationService.self).eddEnabled.value {
                return Asset.Images.More.iconBadgeForEdd.image
            }
            return Asset.Images.More.iconBadgeKYC0.image
        case .verifiedSeller:
            return Asset.Images.More.iconBadgeVerified.image
        default:
            return Asset.Images.More.iconBadgeKYC0.image
        }
    }

    var profileBadge: UIImage? {
        switch self {
        case .one:
            return Asset.Images.More.iconBadgeKYC1.image
        case .verifiedSeller:
            return Asset.Images.More.iconBadgeVerified.image
        default:
            return nil
        }
    }

    var badgeDescription: String {
        switch self {
        case .one:
            return "verified"
        case .forEdd:
            return "for_edd"
        case .verifiedSeller:
            return "verified_seller"
        default:
            return "basic"
        }
    }
}

enum EDDStatus: String {
    case forSubmission = "for_submission"
    case forApproval = "for_approval"
}

enum OperatorType: String {
    case null = "NULL"
    case bus = "BUS"
    case other = "OTHER"
    case ferry = "FERRY"
}

enum MerchantCategory: String {
    case food
    case retail
    case government
    case transport
    case invest
    case invest101 = "invest-101"

    var lastUpdatedKey: UserDefaultsStoreId {
        switch self {
        case .food: return .onlineMerchantsFoodLastUpdatedOn
        case .retail: return .onlineMerchantsRetailLastUpdatedOn
        case .government: return .onlineMerchantsGovernmentLastUpdatedOn
        case .transport: return .onlineMerchantsTransportLastUpdatedOn
        case .invest, .invest101: return .onlineMerchantsInvestLastUpdatedOn
        }
    }
}

enum QuickAction: String {
    case scanQr
    case requestMoney
}

enum DesignCardType: String {
    case gif
    case theme
}

enum SessionMFAChallengeMethod: String {
    case otp = "OTP"
    case face = "FACE"
}

enum MayaCreditTransactionType: String {
    case repayment = "REPAYMENT"
    case feeAdjusted = "FEE_ADJUSTED"
    case disbursementAdjustment = "DISBURSEMENT_ADJUSTMENT"
}

enum ChangeMinOTPType: Equatable {
    case oldNumber
    case newNumber(String)
}

enum ReKYCStatus: String, Equatable {
    case tagged
    case submitted
    case approved
    case rejected
    case none
}

enum ReKYCReason: String {
    case dormantSavings = "DORMANT_SAVINGS"
}
