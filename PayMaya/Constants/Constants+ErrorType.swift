//
//  Constants+ErrorType.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> Rabago on 9/20/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation

enum ErrorType: Equatable {
    /// Generic error
    case generic
    /// The device has no internet connection
    case noInternetConnection
    /// Scanning of barcode or qrcode failed
    case invalidScan
    /// <PERSON><PERSON> failed due to transaction is P2B
    case invalidScanP2B
    /// Importing a photo failed
    case invalidImport
    /// The maximum attempts of logging in has been reached
    case loginMaxAttempts
    /// The OTP challenge has expired
    case otpExpired
    /// The maximum attempts of otp is reached
    case otpMaxAttempts
    /// Response was a redirect response
    case redirect(String)
    /// The user's session has timed out
    case sessionTimeout(String)
    /// Returned when a string has failed the validation check
    case validation
    /// Return when the transaction cannot be completed because of insufficient balance
    case insufficientBalance(String)
    /// Return when the user has reached the max favorites limit
    case maxLimit
    /// Redirect response for redirect
    case verificationRedirect
    /// The online merchant is not found
    case contentNotFound
    /// Return when the response of the HEAD request is updated (meaning the cache is still the latest)
    case metadataUpdated
    /// Return when the specified voucher code is not found
    case voucherCodeNotFound
    /// Returned when the specified mission code is invalid
    case invalidMissionCode
    /// Returned when the specified voucher campaign is invalid
    case invalidVoucherCampaign
    /// Returned when the specified mission code has been used
    case usedMissionCode
    /// Returned when shop is taking a while to process a transaction
    case shopTimeout
    /// Returned when maya credit submission encounters an error other than session errors such as otp error
    case mayaCreditSubmissionError
    /// Returned when no contact reference is found
    case mayaCreditEmptyContactRef
    /// Returned when scanned QRPH qr code does not follow specs
    case qrphInvalidCode
    /// Returned when app calls the confirm payment in MM but OTP is not yet verified
    case qrphUnverifiedOTP
    /// Returned when lucky games user is still kyc 0 or kyc 0.5
    case luckyGamesUserNotUpgraded
    /// Return when lucky games user's age is less than 21
    case luckyGamesUserMinor
    /// Returns when lucky games user is a government employee (i.e. has GSIS ID)
    case luckyGamesUserGovernmentEmployee
    /// Missing or invalid parameters, such as when the page number already exceeded the data set
    case missingOrInvalidParameters
    /// Password given in the request is not identifical as in database
    case currentPasswordIncorrect

    /// MFA Challenge specific errors
    /// Returned when mfa otp challenge is needed to proceed. Happens in sessions v5 for now. Takes challengeId,  transactionType, and paramType as paramaters
    case mfaChallengeOTPRequired(challengeId: String, transactionType: String, paramType: String)
    /// Returned when mfa face challenge is needed to proceed. Happens in sessions v5 for now after changing min
    case mfaChallengeFaceRequired(challengeId: String, transactionType: String, lifestyleId: String)
    /// Returned when there is no face data found. Happens in sessions v5 for now after changing min
    case mfaChallengeNoFaceData
    /// Returned when OTP is invalid
    case mfaChallengeInvalidOTP
    /// Returned when resend OTP endpoint is called too early
    case mfaChallengeEarlyResendOTPCall
    /// Returned when start challenge endpoint is used more than once
    case mfaChallengeMultipleStart
    /// Returned when start challenge endpoint  has reached max attempts
    case mfaChallengeMaxAttempts
    /// Returned when challenge has already expired.
    case mfaChallengeExpired
    /// Returned when FACE challenge result is within the partial face match threshold
    case mfaChallengeFacePartialMatch
    /// Returned when FACE challenge result is below the partial face match threshold
    case mfaChallengeFaceMismatch
    /// Returned when FACE challenge result fails due to Tencent service is down
    case mfaChallengeFaceServiceDown
    /// Returned when FACE challenge max attempt has been reached
    case mfaChallengeFaceMaxAttempts
    /// Returned when FACE challenge max attempt has been reached, but will allow an (OTP) fallback
    case mfaChallengeFaceMaxAttemptsFallback

    /// Maya Credit
    /// Returned when user is ineligible to apply maya credit
    case mcIneligible

    /// Shield finger printing errors.
    case shieldStandardError
    case shieldSpecialError

    /// KYC errors.
    case preSubmissionIdExpired
    case kycMultipleSubmissionError

    /// Update Profile errors.
    case updateProfileSameNumberError

    /// Returned when the password is on the blacklist
    case passwordBlacklistedError

    /// Device Management errors
    case noDeviceLoadedError
}
