//
//  Constans+ZolozEKYC.swift
//  PayMaya
//
//  Created by <PERSON> on 13/10/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation

extension Constants {
    enum ZolozEKYC: String {
        case firstName
        case lastName
        case city
        case state
        case barangay
        case fullAddress
        case additionalAddress
        case zipCode
        case idNumber
        case companyName
        case workOthers
    }
}

extension Constants {
    struct EKYC {
        struct CreditCardOverView {
            static let analytics = "source_page=onboarding_qr"
        }

        struct Document {
            static let orderUndefined = 0   // index not found
            static let lastIndex = 100      // last index of an ordered list
            struct OtherID {
                static let key = "OTHER_ID"
                static let order = lastIndex
            }
            struct Philsys {
                static let key = "PH_NATIONAL_ID"
                static let digitalKey = "PHILSYS_DIGITAL"
                static let eGovLink = "eGovPH"
                static let digitalIDLink = "DigitalID"
                static let ePhilFrontKey = "EPHIL_ID"
                static let ePhilBackKey = "EPHIL_BACK"
                static let ePhilDescription = "ePhil ID (Paper ID)"
            }
            struct ForeignPassport {
                static let key = "FOREIGN_PASSPORT"
            }
            struct ACR {
                static let key = "ALIEN_CERTIFICATION"
            }
            static let liveness = "LIVENESS"

            struct ConfidenceScore {
                static let zeroPassingThreshold = "0"
            }
        }

        struct IDUploadOrder {
            static let secondaryID = 2
        }

        struct Order {
            static let first = "1"
            static let second = "2"
        }

        struct DSModelInput {
            static let height = 224
            static let width = 224
        }

        struct IDUpload {
            static let osTypeValueiOS = "MOBILE_IOS"
            static let exceedLimitErrorCode = "ERROR_UPLOAD_EXCEED_LIMIT"
            static let wrongTypeErrorCode = "ERROR_UPLOAD_WRONG_TYPE"
            static let cameraRoll = "Camera Roll"
            static let files = "Files"
            static let cancel = "Cancel"
            static let filePicker = "File picker"
            static let photoPicker = "Photo picker"
        }

        struct PersonalInfoFields {
            static let birthCountry = "birthPlace.country"
            static let nationality = "nationality"
            static let presentCountry = "presentAddress.country"
            static let permanentCountry = "permanentAddress.country"
        }

        struct ErrorReasons {
            static let nonHTTP = "Non-HTTP Error"
            static let presubmissionIDNotFound = "Presubmission ID Not Found"
            static let sessionTimeout = "Session Timeout"
            static let tencentUpload = "Tencent Upload Error"
            static let tencentFaceDetectionTimeout = "Tencent Face Detection Timed Out"
            static let tencentLocalRef = "TENCENT_ERROR_LOCAL_REF_FAILED"
            static let tencentUserStops = "TENCENT_ERROR_USER_STOPS"
            static let tencentInnerError = "TENCENT_ERROR_INNER_ERROR_CODE"
            static let tencentAppSwitch = "TENCENT_ERROR_APP_SWITCH"
            static let tencentCameraPermission = "TENCENT_ERROR_CAMERA_PERMISSION"
            static let tencentInitSDK = "TENCENT_ERROR_INIT_SDK"
            static let tencentPermissionCheck = "TENCENT_ERROR_PERMISSION_CHECK"
            static let tencentAppStop = "TENCENT_ERROR_APP_STOP"
            static let tencentLiveData = "TENCENT_ERROR_LIVE_DATA"
            static let tencentLocalTimeout = "TENCENT_ERROR_LOCAL_TIME_OUT"
            static let tencentPrepareTimeout = "TENCENT_ERROR_PREPARE_TIME_OUT"
            static let tencentCheckPermission = "TENCENT_ERROR_CHECK_PERMISSION"
            static let invalidRequest = "Invalid Request"
            static let resourceNotFound = "Resource not found"
            static let internalServerError = "Internal Server Error"
            static let defaultError = ""
        }

        struct SplitKeys {
            struct WelcomeScreen {
                static let verifyNow = "verify_now"
                static let upgradeForFree = "upgrade_for_free"
            }

            struct Events {
                static let welcomeButtonTapped = "KYC_WELCOME_BUTTON_TAPPED"
            }
        }

        static let defaultImageQualityMaxRetries: Int = 2

        static let bottomsheetAnalytics = "bottomsheet"
        static let learnMoreAnalytics = "About registering your name"
        static let tofuFlowAnalytics = "TOFU 1.0"
    }
}

extension Constants {
    struct ReKYC {
        // MARK: RKYCIntroScreen Appearance duration in **Minutes** from when it was presented last
        static let reKYCIntroScreenDuration: Int = 0
        // TO DO: It is associated with testing toggle and should be removed before moving to prod
        static let reKYCIntroScreenDurationTest: Int = 10
    }
}
