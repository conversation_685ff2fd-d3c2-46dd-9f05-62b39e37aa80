//
//  Constants+Flutter.swift
//  PayMaya
//
//  Created by <PERSON> on 11/18/21.
//  Copyright © 2021 PayMaya Philippines, Inc. All rights reserved.
//

import ConfigurationService
import Foundation
import Injector
import MayaCoreData

extension Constants {
    struct Flutter {
        struct Key {
            static let eventName = "event_name"
            static let attributes = "attributes"
            static let success = "success"
            static let biller = "biller"
            static let amount = "amount"

            /// For Credit Scoring
            static let isEligible = "isEligible"
            static let customerId = "customerId"
            static let dataReferenceId = "dataReferenceId"
            static let data = "data"

            /// For Credit Card
            static let otherFields = "other_fields"
            static let accountNumber = "accountNumber"
            static let cardId = "cardId"

            // For Security Center
            static let freezeStatus = "freeze_status"
        }

        struct Crypto {
            static let engineName = "CRYPTO_FLUTTER"
            static let entryPoint = "runPaymayaCrypto"
            static let channel = "com.paymaya.crypto"
            static let libraryURI = "package:paymaya_module/main.dart"
            static let module = "crypto"
        }

        struct CryptoV2 {
            static let engineName = "CRYPTO_FLUTTER_V2"
            static let entryPoint = "runMayaCryptoV2"
            static let channel = "com.maya.crypto-v2"
            static let libraryURI = "package:paymaya_module/main.dart"
            static let module = "crypto-v2"
        }

        struct Savings {
            static let tabEngineName = "SAVINGS_TAB_ENGINE"
            static let fullScreenEngineName = "SAVINGS_FULL_ENGINE"
            static let channel = "packages/paymaya_consumer_savings"
            static let entryPoint = "runConsumerSavings"
            static let fullScreenEntryPointInitialRoute = "fullscreenFlowEntrypoint"
            static let startIntroSavings = "AUTO_CASH_IN_CREATE_MAYA_SAVINGS"
            static let mayaSavingsLiftDormancyEntrypoint = "MAYA_SAVINGS_LIFT_DORMANCY"
            static let reKycReasonKey = "rekyc_reason"
            static let reKycStatusKey = "rekyc_status"
            static let productCode = "product_code"
            static let productKey = "product_key"
            static let invalidArguments = "Invalid arguments passed"
        }

        struct SecurityDeposit {
            static let engineName = "securityDepositEngine"
            static let entryPoint = "runSecurityDeposit"
            static let channel = "packages/security_deposit"
            static let loanApplicationId = "loan_application_id"
            static let creditLimitMultiplier = "credit_limit_multiplier"
        }

        struct SetUsername {
            static let entryPoint = "runSetUsername"
            static let fullScreenEngineName = "setUsernameFullscreen"
            static let channel = "packages/paymaya_set_username"
        }

        struct Checkout {
            static let engineName = "CHECKOUT_FLUTTER"
            static let entryPoint = "runCheckout"
            static let fullScreenEntryPointInitialRoute = "fullscreenFlowEntrypoint"
            static let channel = "packages/paymaya_checkout"
            static let libraryURI = "package:paymaya_module/main.dart"
            static let route = "route"
            static let buyLoadScreen = "buyLoadScreen"
            static let paymentId = "paymentId"
            static let paymentType = "paymentType"
            static let billsPaymentType = "bills"
        }

        struct Funds {
            static let engineName = "funds"
            static let entryPoint = "runMayaFunds"
            static let channel = "packages/maya_funds"
            static let module = "funds"
        }

        struct BNPL {
            static let tabEngineName = "LOANS_TAB_ENGINE"
            static let fullScreenEngineName = "LOANS_FULL_ENGINE"
            static let channel = "packages/paymaya_bnpl"
            static let entryPoint = "runBnplHome"
            static let route = "route"
            static let introScreen = "introScreen"
            static let tabScreenEntryPointInitialRoute = "homeScreen"
            static let fullScreenEntryPointInitialRoute = "fullscreenFlowEntrypoint"
            static let motherMaidenName = "motherMaidenName"
        }

        struct LoansCreditCard {
            static let engineName = "loansCreditCard"
            static let fullScreenEngineName = "loansCreditCard"
            static let route = "route"
            static let channel = "packages/loans_credit_card"
            static let entryPoint = "runLoansCreditCard"
            static let fullScreenEntryPointInitialRoute = "fullscreenFlowEntrypoint"
            static let cardOverviewScreen = "creditCardOverviewScreen"
            static let applicationPendingScreen = "applicationPendingScreen"
            static let aboutYourCardScreen = "aboutYourCardScreen"
            static let repaymentScreen = "repaymentScreen"
            static let ccReviewingApplicationScreen = "ccReviewingApplicationScreen"
            static let securityDepositReprocessScreen = "securityDepositReprocessScreen"
            static let securityDepositScreen = "securityDepositScreen"
            static let selectTransactionScreen = "selectTransactionScreen"
            static let installmentPlanScreen = "installmentPlanScreen"
            static let dblSecuritySelectionScreen = "dblSecuritySelectionScreen"
            static let loanApplicationId = "loan_application_id"
            static let sdAccountId = "sd_account_id"
            static let sdAccountNumber = "sd_account_number"
            static let sdAccountName = "sd_account_name"
            static let entrypoint = "entrypoint"
            static let sourceScreen = "source_screen"
        }

        struct ICL {
            struct Keys {
                static let biller = "biller"
                static let amount = "amount"
                static let accountNumber = "account_number"
            }

            static let reviewingApplicationScreen = "iclReviewingApplicationScreen"

            struct Geolocation {
                static let success = "1000"
                static let failed = "1001"
                static let permissionNotDetermined = "1100"
                static let permissionDenied = "1101"
            }

            struct GeolocationErrorMessages {
                static let notDetermined = "Permission not determined"
                static let denied = "Permission denied"
                static let unknown = "Unknown error"
                static let locationNotFound = "Location not found"
            }

            struct DataExtract {
                static let success = "1000"
                static let failed = "1001"
            }

            struct DataExtractErrorMessages {
                static let extractionFailed = "Extract failed"
            }
        }

        struct PurchaseFinancing {
            static let engineName = "purchaseFinancing"
            static let fullScreenEngineName = "purchaseFinancing"
            static let route = "route"
            static let channel = "packages/purchase_financing_sdk"
            static let entryPoint = "runPurchaseFinancing"
            static let fullScreenEntryPointInitialRoute = "pfIntro"
            static let sourceScreen = "source_screen"
        }

        struct MFA {
            struct Result {
                static let success = 1011
                static let cancel = 1012
                static let error = 1013
                static let maxAttempt = 1014
                static let fallback = 1015
            }
        }

        struct CreditScoring {
            static let engineName = "creditScoring"
            static let channel = "packages/paymaya_credit_scoring"
            static let credolabChannel = "credolab"
            static let entryPoint = "runCreditScoring"
            static let fullScreenEntryPointInitialRoute = "home?riskLevel="
            static let enableDataReferenceId = "&enableDataReferenceId="
        }

        struct Cards {
            static let entryPoint = "runCard"
            static let tabEngineName = "CARDS_TAB_ENGINE"
            static let fullScreenEngineName = "CARDS_FULL_ENGINE"
            static let channel = "packages/paymaya_card"
            static let fullScreenEntryPointInitialRoute = "fullscreenFlowEntrypoint"
            static let route = "route"
            static let params = "params"
            static let cardDashboardBenefits = "cardDashboard/benefits"
            static let cardDashboard = "cardDashboard"
            static let activateCreditCard = "activateCreditCard"
            static let transactionReceipt = "transactionReceipt"
            static let productOfferingKey = "productOffering"
            static let productOffering = "MAYA_BLACK_CREDIT_CARD_CONSUMER_LOAN"
            static let mayaBlackProductCode = "MAYA_VISA_CREDIT_CARD_BLACK"
        }

        struct AutoCashIn {
            static let entryPoint = "runAutoCashIn"
            static let channel = "packages/paymaya_auto_cash_in"
            static let fullScreenEngineName = "AUTO_CASH_IN_ENGINE"
            static let fullScreenEntryPointInitialRoute = "requestFullscreen"
            static let reloadHome = "reloadHome"
        }

        struct Inbox {
            static let entryPoint = "runInboxNotification"
            static let channel = "packages/paymaya_inbox_notification"
            static let manageNotificationsEngineName = "INBOX_MANAGE_NOTIFICATIONS"
            static let fullScreenEngineName = "INBOX_FULL_ENGINE"
            static let fullScreenEntryPointInitialRoute = "fullscreenFlowEntrypoint"
            static let fullScreenEntryPoint = "runInboxNotificationFullScreen"
            static let reloadHome = "reloadHome"
        }

        struct Load {
            static let entryPoint = "runLoad"
            static let channel = "packages/maya_load"
            static let shopFullScreenEngineName = "LOAD_SHOP_FULL_ENGINE"
            static let fullScreenEntryPointInitialRoute = "home"
        }

        struct RestrictedLogin {
            static let engineName = "restrictedLoginEngine"
            static let entryPoint = "runRestrictedLoginFlow"
            static let channel = "packages/restricted_login_flow"
        }

        struct IncomeDocumentation {
            static let engineName = "incomeDocumentationEngine"
            static let entryPoint = "runIncomeDocumentation"
            static let channel = "packages/income_documentation"
        }

        struct CardPull {
            static let entryPoint = "runCardPull"
            static let channel = "packages/card_pull"
            static let fullScreenEngineName = "CARD_PULL_FULL_ENGINE"
            static let fullScreenEntryPointInitialRoute = "home"
        }

        struct SecurityCenter {
            static let channel = "packages/security_center_tech"
            static let engineName = "SECURITY_CENTER_ENGINE"
            static let entryPoint = "runSecurityCenterTech"
        }

        struct FlutterInterstitial {
            static let channel = "packages/flutter_interstitial"
            static let engineName = "FLUTTER_INTERSTIAL_ENGINE"
            static let entryPoint = "runFlutterInterstitial"
        }

        struct AutoDebitArrangement {
            static let entryPoint = "runAutoDebitArrangement"
            static let channel = "packages/maya_auto_debit_arrangement"
            static let engineName = "AUTO_DEBIT_ARRANGEMENT_FULL_ENGINE"
            static let fullScreenEntryPointInitialRoute = "home"
        }
    }
}

enum FlutterToggles: String {
    case simplifiedCheckout = "simplified_checkout"
    case loansV3 = "loans_v3_enabled"
    case loansICL = "icl_enabled"
    case loansULEReskin = "unified_lending_experience_reskin"
    case loansICLInitialEligibilityCheck = "initial_eligibility_check"
    case loansICLDropdownV2 = "icl_drop_down_v2"
    case loansICLPhaseTwoV1 = "icl_phase_two_v1"
    case bnplDeepFreeze = "bnpl_deep_freeze"
    case loansICLPhaseTwoV2 = "icl_phase_two_v2"
    case landersCreditCard = "landers_credit_card"
    case cardDetailsProtection = "card_details_protection"
    case loansCreditCard = "loans_credit_card"
    case loansICLDeviceScoring = "icl_device_scoring"
    case manageNotifications = "manage_notifs_toggle"
    case mayaSupport = "maya_support_toggle"
    case inboxTicketing = "ticket_notification_toggle"
    case sendbirdCachingCollection = "sendbird_caching_collection"
    case iclMothersMaidenName = "icl_mothers_maiden_name"
    case iclToMambu = "icl_to_mambu"
    case iclDeviceScoringReferenceId = "icl_device_scoring_data_reference_id"
    case iclDbl = "icl_dbl"
    case iclMfa = "icl_mfa"
    case iclGeolocation = "icl_geolocation"
    case lendingDataExtraction = "lending_data_extraction"
    case loansCreditCardDetailsV2 = "loans_credit_card_details_v2"
    case loansCreditCardMayaBlack = "loans_credit_card_maya_black"
    case ccDBL = "cc_dbl"
    case ccSkipCreditScoring = "loans_credit_card_credolab_skip_permission"
    case loadV2Favorites = "load_favorites"
    case loadV2Recommender = "load_recommender"
    case loadV2MinDetection = "load_min_detection"
    case voucherDetailsV2 = "voucher_details_v2"
    case creditCardCashbackTile = "landers_credit_card_cashback_tile"
    case mayaBlackCreditCard = "maya_black_credit_card"
    case loyaltyForceUpdate = "loyalty_force_update"
    case mayaBlackIntro = "maya_black_intro"
    case ccCreditScoring = "loans_credit_card_credit_scoring"
    case convertToInstallment = "convert_to_installment"
    case purchaseFinancing = "purchase_financing"
}

enum FlutterAvailability: String {
    case load
    case cards
    case addMoneyViaCard = "add_money_via_card_service"

    var serviceAvailability: AppConfig.Maintenance.ServiceAvailability.ServiceMaintenance? {
        let configurationService = ContainerWrapper.shared.resolve(ConfigurationService.self)
        switch self {
        case .load:
            return configurationService.maintenance?.serviceAvailability.load
        case .cards:
            return configurationService.maintenance?.serviceAvailability.cards
        case .addMoneyViaCard:
            return configurationService.maintenance?.serviceAvailability.addMoneyViaCard
        default:
            return nil
        }
    }
}

enum FlutterMethod: String {
    /// Common
    case dismiss
    case dismissWithChanges
    case logAnalyticsEvent
    case getAvailability
    case goToWallet

    /// For Crypto
    case getAccountLimits
    case getBalance
    case getBalanceAndLimits
    case getCustomerAccessToken
    case getProfile
    case getProfileAndToken

    /// For Savings
    case getCustomerId
    case getSessionToken
    case getMin
    case getAccessToken
    case getShieldSessionId
    case getPinningFingerprint
    case onSessionTimeout
    case requestFullscreen
    case requestFullscreenFromNative
    case showUpdateProfileScreen
    case showWorkDetailsScreen
    case showContactReferenceScreen
    case reloadHome
    case showUpgradeScreen
    case showAccountUnderReviewScreen
    case getReKYCFlag
    case getReKYCEnabled
    case showReKYCDialog
    case showReKYCFlow
    case openSavingsDormancyRekyc
    case openBankReKyc
    case sendToPartialEngine
    case sendToFullscreenEngine
    case isMayaBlackEnabled
    case openSecurityDepositCreation
    case openMayaBlackDashboard
    case openLendingReprocess
    case openCreditApplication
    case showSecurityDepositOverview

    /// For Security Deposit
    case getInputData
    case onSuccessfullyCreated
    case getClientId
    case openCreateSavings
    case openSecurityDepositAccount

    /// For Checkout
    case finishCurrentModuleForCard
    case complete
    case getToggle
    case getKycStatus
    case getKycLevel

    /// For Cards 2.0
    case goToSavingsTab
    case requestSetUsername
    case requestCheckout
    case requestCreditCard
    case requestCashIn

    /// For Cards 2.0 - Benefits
    case goToVoucherListScreen
    case goToVoucherDetailsScreen

    /// For Auto Cash In
    case runAutoCashIn

    /// For Inbox
    case getApnsToken
    case updateNativeUnreadCount
    case showHelp
    case handleDeeplink
    case showWebView
    case onLoginComplete
    case getInboxToggles
    case requestForegroundDeeplink
    case shouldNotPresentPushNotification
    case removeSupportChannelPushNotifications
    case refreshAccessToken
    case getFilePathFromFilePicker
    case openFileAtPath
    case getGalleryPermissions
    case getCameraPermissions
    case navigateToSettings
    case setIsPresentingOverInboxFullScreen
    case showSupportWebViewWithParams
    case redirectToStore
    case getAppVersion

    /// For Loans Credit Card
    case getUserInfo
    case requestBillsPay
    case completeKyc

    /// Restricted Login
    case logout
    case getRestrictionCode
    case toKycFlowFromRestrictedLogin
    case getName

    /// For credit scoring
    case collect
    case creditScoringComplete
    case uploadDocument

    /// For Loans
    case processMfa
    case getGeolocation
    case startDataExtract

    /// For Income Documentation
    case getIncomeDocsContentHeader
    case getCMSAccessToken
    case onSubmitSuccess

    /// Security Center
    case changePassword
    case changeMobileNumber
    case changeEmailAddress
    case manageCards
    case getLoginHistoryToggle
    case callLoginHistory
    case setFrozenStatus
    case getSourceAttributes
    case getFreezeCardSpiel
    case getUnfreezeCardSpiel
    case getFreezeConfirmationSpiel
    case getUnfreezeConfirmationSpiel
    case getFreezeSuccessSpiel
    case getUnfreezeSuccessSpiel

    /// Flutter Interstitial
    case onContinue

    /// Card Pull
    case requestAccountLimits
}

enum FlutterArgument: String {
    /// Checkout
    case showReceipt
}

struct FlutterResponseGenerator {
    // MARK: Crypto related generators
    func generateAccountLimits(_ accountLimits: AccountLimitList) -> String? {
        guard let json = JSONEncoder().encodeToJSONDictionary(accountLimits),
              let data = try? JSONSerialization.data(withJSONObject: json, options: []),
              let jsonString = String(data: data, encoding: .ascii) else { return nil }

        return """
        { "limits": \(jsonString) }
        """
    }

    func generateBalance(amount: AvailableAmount) -> String {
        let value = amount.value ?? 0.00 as NSNumber
        return """
        { "balance": {"value": "\(value)", "currency": "PHP"} }
        """
    }

    func generateBalanceAndLimits(amount: AvailableAmount, accountLimits: AccountLimitList) -> String? {
        guard let json = JSONEncoder().encodeToJSONDictionary(accountLimits),
              let data = try? JSONSerialization.data(withJSONObject: json, options: []),
              let jsonString = String(data: data, encoding: .ascii) else { return nil }

        let value = amount.value ?? 0.00 as NSNumber
        return """
        {
            "balance": {"value": "\(value)", "currency": "PHP"},
            "limits": \(jsonString)
        }
        """
    }

    func generateCustomerAccessToken(token: String) -> String {
        return """
        { "customer_access_token": "\(token)" }
        """
    }

    func generateProfile(msisdn: String, verified: Bool, kyc: String) -> String {
        return """
        { "identities": [{ "type": "msisdn", "value": "\(msisdn)", "verified": "\(verified ? "true" : "false")" }], "kyc": "\(kyc)" }
        """
    }

    func generateUserInfoFromProfile(_ profile: Profile?) -> [String: String] {
        return generateUserInfo(
            firstName: profile?.firstName,
            lastName: profile?.lastName,
            middleName: profile?.middleName
        )
    }

    func generateUserInfo(firstName: String?, lastName: String?, middleName: String?) -> [String: String] {
        let firstNameValue = firstName ?? ""
        let lastNameValue = lastName ?? ""
        let middleNameValue = middleName ?? ""
        return ["first_name": firstNameValue, "last_name": lastNameValue, "middle_name": middleNameValue]
    }

    func generateUserInfoWithAdditionalParams(with user: User) -> [String: String] {
        let nameInfo = generateUserInfo(
           firstName: user.profile?.firstName,
           lastName: user.profile?.lastName,
           middleName: user.profile?.middleName
        )
        let otherInfo = [
           "msisdn": user.msisdn ?? "",
           "kyc_level": user.kycLevel ?? "",
           "email": user.email ?? ""
        ]

        return nameInfo.merging(otherInfo) { current, _ in current }
    }

    func generateProfileAndToken(msisdn: String, verified: Bool, kyc: String, token: String) -> String {
        return """
        {
            "customer_access_token": "\(token)",
            "profile": {
                "identities": [{
                    "type": "msisdn",
                    "value": "\(msisdn)",
                    "verified": "\(verified ? "true" : "false")"
                }],
                "kyc": "\(kyc)"
            }
        }
        """
    }

    // MARK: Savings related generators
    func getCustomerId(_ customerId: String) -> [String: Any] {
        return ["customer_id": customerId]
    }

    func getSessionToken(_ sessionToken: String) -> [String: Any] {
        return ["session_token": sessionToken]
    }

    func getMin(_ min: String) -> [String: Any] {
        return ["min": min]
    }

    func getAccessToken(_ accessToken: String) -> [String: Any] {
        return ["access_token": accessToken]
    }

    func getShieldSessionId(_ sessionId: String) -> [String: Any] {
        return ["shield_session_id": sessionId]
    }

    func getPinningFingerprint(_ pinningFingerprint: String) -> [String: Any] {
        return ["pinning_fingerprint": pinningFingerprint]
    }

    func getReKYCFlag(status: String, reason: String) -> [String: String] {
        return [Constants.Flutter.Savings.reKycReasonKey: reason, Constants.Flutter.Savings.reKycStatusKey: status]
    }

    func getReKYCEnabled(enabled: Bool) -> [String: Any] {
        return ["rekyc_enabled": enabled]
    }

    func getToggles(from rawToggles: [FlutterToggles: CSConfig<Bool>]) -> [String: Bool] {
        var toggles: [String: Bool] = [:]

        for toggleKey in rawToggles.keys {
            toggles[toggleKey.rawValue] = rawToggles[toggleKey]?.value
        }

        return toggles
    }

    func getTogglesV2(from rawToggles: [FlutterToggles: CSConfigV2<Bool>]) -> [String: Bool] {
        var toggles: [String: Bool] = [:]

        for toggleKey in rawToggles.keys {
            toggles[toggleKey.rawValue] = rawToggles[toggleKey]?.value
        }

        return toggles
    }

    func getAvailability(
        from rawAvailability: [FlutterAvailability]
    ) -> [String: Bool] {
        let availabilities: [String: Bool] = Dictionary(
            uniqueKeysWithValues: rawAvailability.map {
                ($0.rawValue, !($0.serviceAvailability?.isUnderMaintenance ?? false))
            }
        )

        return availabilities
    }

    func getKycStatus(kycStatus: String) -> [String: String] {
        let responseKey = "kyc_status"

        return [responseKey: kycStatus]
    }

    func getKycLevel(kycLevel: String) -> [String: String] {
        return ["kyc_level": kycLevel]
    }

    func getApnsToken(_ apnsToken: String) -> [String: Any] {
        return ["apns_token": apnsToken]
    }

    func refreshAccessToken(_ refreshAccessTokenResponse: String) -> [String: Any] {
        return ["refresh_access_token_response": refreshAccessTokenResponse]
    }

    func getFilePathFromFilePicker(_ filePath: String) -> [String: Any] {
        return ["file_path": filePath]
    }

    func getGalleryPermissions(_ galleryPermissions: Bool) -> [String: Any] {
        return ["granted": galleryPermissions]
    }

    func getCameraPermissions(_ cameraPermissions: Bool) -> [String: Any] {
        return ["granted": cameraPermissions]
    }

    func getAppVersion(_ appVersion: String) -> [String: Any] {
        return ["app_version": appVersion]
    }

    // MARK: Security deposit related generators
    func getSecurityDepositInputData(loanApplicationId: String, creditLimitMultiplier: String) -> [String: Any] {
        return ["loan_application_id": loanApplicationId, "credit_limit_multiplier": creditLimitMultiplier]
    }

    func getGlimpseClientId(_ clientId: String) -> [String: Any] {
        return ["client_id": clientId]
    }

    // MARK: Restricted login related generators
    func getRestrictionCode(_ restrictionCode: String) -> [String: Any] {
        return ["restriction_code": restrictionCode]
    }

    func getName(_ name: String) -> [String: Any] {
        return ["name": name]
    }

    // MARK: Income documentation related generators
    func getIncomeDocsContentHeader(title: String, subtext: String) -> [String: Any] {
        return ["data": ([ "title": title, "subtitle": subtext])]
    }

    // MARK: Security center related generators
    func getSourceAttributes(sourcePage: String, sourceButton: String) -> [String: Any] {
        return ["attributes": ["source_page": sourcePage, "source_button": sourceButton]]
    }
}
