//
//  AppDelegate+Split.swift
//  PayMaya
//
//  Created by <PERSON> on 7/3/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import ConfigurationService
import Injector
import ReactiveSwift

extension AppDelegate {
    func setupSplit() {
        setupSplitAnalytics()
        setupSplitPerformanceMonitoring()
        setupSplitLogs()
    }

    private func setupSplitAnalytics() {
        #if !UNIT_TEST_TARGET
        let analyticsService = ContainerWrapper.shared.resolve(AnalyticsService.self)
        let configurationService = ContainerWrapper.shared.resolve(ConfigurationServiceV2.self)

        let provider = AnalyticsProvider { (eventName: String, eventAttributes: [String: Any]?, additionalInfoForExperiment: [String: Any]?) in
            guard
                let additionalInfo = additionalInfoForExperiment,
                let experiment = additionalInfo[MayaExperimentInfoKey.experiment.rawValue] as? MayaExperiment else {
                return
            }

            let treatment = additionalInfo[<PERSON>E<PERSON><PERSON>imentInfoKey.treatment.rawValue] as? String ?? ""

            var attributes: [String: Any] = eventAttributes ?? [String: Any]()
            attributes[MayaExperimentAttributedKey.treatment.rawValue] = treatment
            attributes[MayaExperimentAttributedKey.experimentName.rawValue] = experiment.splitKey

            let result = configurationService.track(for: experiment.mayaToggle, eventName: eventName, attributes: attributes)
            #if DEBUG
            let resultMessage = result ? "Successfully tracked " : "Failed to track "
            ConfigurationServiceLogger.log(message: "\(resultMessage) treatment \(treatment) for \(experiment.splitKey)")
            #endif
        }

        analyticsService.addProvider(provider)
        #endif
    }

    private func setupSplitPerformanceMonitoring() {
        func setupMonitoring(trafficType: MayaToggle.TrafficType) {
            let configurationService = ContainerWrapper.shared.resolve(ConfigurationServiceV2.self)
            let performanceMonitoringService = ContainerWrapper.shared.resolve(PerformanceMonitoringServiceProtocol.self)

            configurationService.getEventProperty(trafficType: trafficType).producer
                .observe(on: UIScheduler())
                .startWithValues { event in
                    switch event {
                    case .created:
                        let attributes = [AnalyticsAttributeKey.trafficType.rawValue: trafficType.rawValue]
                        performanceMonitoringService.startCustomTrace(with: SplitClientEvent.readyFromCache.traceName, attributes: attributes)
                        performanceMonitoringService.startCustomTrace(with: SplitClientEvent.ready.traceName, attributes: attributes)
                    case .readyFromCache:
                        performanceMonitoringService.endCustomTrace(with: SplitClientEvent.readyFromCache.traceName)
                    case .ready:
                        performanceMonitoringService.endCustomTrace(with: SplitClientEvent.ready.traceName)
                    default:
                        break
                    }
                }.addToDisposeBag(disposeBag)
        }

        setupMonitoring(trafficType: .anonymous)
        setupMonitoring(trafficType: .user)
    }

    private func setupSplitLogs() {
        #if !UNIT_TEST_TARGET && !PROD_TARGET && !SANDBOX_TARGET
        let userDefaults = UserDefaults.standard
        let isEnabled = userDefaults.bool(forKey: UserDefaultsStoreId.configurationServiceLogsEnabled.rawValue)
        ConfigurationServiceLogger.isEnabled = isEnabled
        #endif
    }
}
