//
//  AppDelegate.swift
//  PayMaya
//
//  Created by <PERSON> on 26/10/2016.
//  Copyright © 2016 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider_Reactive
import AppsFlyerLib
import AssetProvider
import CleverTapSDK
import ConfigurationService
import Firebase
import FirebaseMessaging
import GoogleMobileAds
import Injector
import MayaCoreData
import ReactiveSwift
import ShieldFraud
import StoreProvider
import UIKit
import VoyagerGenericEncryption

#if !UNIT_TEST_TARGET
#if !PROD_TARGET && !SANDBOX_TARGET
import DebugSwift
#endif
#endif

class AppDelegate: UIResponder, UIApplicationDelegate {
    var window: UIWindow?
    var dependencyManager: DependencyManager?
    var dependencySetupHelper: DependencySetupHelper? {
        return dependencyManager?.dependencySetupHelper
    }

    lazy var appCoordinator: AppCoordinator = AppCoordinator()

    // View to put on top of other views when app goes on background
    lazy var appSwitcherView: UIView = {
        return createAppSwitcherView()
    }()

    var didSetBuildNumber: Bool = false

    let disposeBag = CompositeDisposable()

    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        return dependencyManager?.orientationController.orientation ?? UIInterfaceOrientationMask.portrait
    }

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        window = AppWindow()

        // Register all custom fonts from AssetProvider package
        CommonFontFamily.registerAllCustomFonts()

        // Setup default key-value for important entries in plist in case retrieving fails
        VGEncryptor.shared.setUp(with: VGDefaults.keys, key: GE_TEMP_KEY)

        // Setup debugging tool
        setupDebugSwift()

        // Setup all dependencies needed by the app upon launch
        dependencyManager = DependencyManager(application: application, launchOptions: launchOptions, window: window)
        setupAdditionalDependencies()

        // Additional setup with relation to UIApplicationDelegate
        setupApplication(application, launchOptions: launchOptions)

        window?.rootViewController = appCoordinator.router.toPresentable()
        window?.makeKeyAndVisible()

        // Start the first coordinator as entry point
        appCoordinator.start()

        // Handle deeplink upon needed
        if let url = launchOptions?[.url] as? URL {
            _ = appCoordinator.startDeepLinkFlow(url: url)
        }

        // Handle shortcut upon needed
        if let shortcutItem = launchOptions?[.shortcutItem] as? UIApplicationShortcutItem {
            openShortcutItem(shortcutItem: shortcutItem)
        }

        MobileAds.shared.start(completionHandler: nil)

        return true
    }

    func applicationDidBecomeActive(_ application: UIApplication) {
        setApplicationState(application)
        dependencySetupHelper?.applicationDidBecomeActive(application)
    }

    func applicationWillEnterForeground(_ application: UIApplication) {
        dependencySetupHelper?.applicationWillEnterForeground(application)
        appSwitcherView.removeFromSuperview()
    }

    func applicationDidEnterBackground(_ application: UIApplication) {
        setApplicationState(application)
        dependencySetupHelper?.applicationDidEnterBackground(application)
        dependencyManager?.storeProvider.target(DatabaseStore.self)?.persistToDatabase()
        if let window = window {
            window.addSubview(appSwitcherView)
            NSLayoutConstraint.enclose(view: appSwitcherView, inside: window)
        }
    }

    func applicationWillTerminate(_ application: UIApplication) {
        dependencyManager?.storeProvider.target(DatabaseStore.self)?.persistToDatabase()
    }

    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey: Any] = [:]) -> Bool {
        if let dynamicLink = DynamicLinks.dynamicLinks().dynamicLink(fromCustomSchemeURL: url),
           let url = dynamicLink.url {
            setDeepLinkSource(url: url, source: .external)
            _ = appCoordinator.startDeepLinkFlow(url: url)
            return true
        }

        setDeepLinkSource(url: url, source: .external)
        _ = appCoordinator.startDeepLinkFlow(url: url)
        return dependencySetupHelper?.application(app, open: url, options: options) ?? true
    }

    func application(_ application: UIApplication, performActionFor shortcutItem: UIApplicationShortcutItem, completionHandler: @escaping (Bool) -> Void) {
        openShortcutItem(shortcutItem: shortcutItem)
    }

    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        dependencySetupHelper?.application(application, didRegisterForRemoteNotificationsWithDeviceToken: deviceToken)
        storeApnsToken(deviceToken: deviceToken)
    }

    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Swift.Error) {
        registerForFirebaseRemoteNotification(application)
    }

    func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
        if let url = userActivity.webpageURL {
            let handled = DynamicLinks.dynamicLinks().handleUniversalLink(url, completion: { dynamicLink, _ in
                if let dynamicLinkURL = dynamicLink?.url {
                    self.setDeepLinkSource(url: dynamicLinkURL, source: .external)
                    _ = self.appCoordinator.startDeepLinkFlow(url: dynamicLinkURL)
                }
            })

            if !handled {
                setDeepLinkSource(url: url, source: .external)
                _ = appCoordinator.startDeepLinkFlow(url: url)
            } else {
                return handled
            }
        }

        return dependencySetupHelper?.application(application, continue: userActivity, restorationHandler: restorationHandler) ?? true
    }

    func application(_ application: UIApplication, open url: URL, sourceApplication: String?, annotation: Any) -> Bool {
        if let dynamicLink = DynamicLinks.dynamicLinks().dynamicLink(fromCustomSchemeURL: url),
           let url = dynamicLink.url {
            setDeepLinkSource(url: url, source: .external)
            _ = appCoordinator.startDeepLinkFlow(url: url)
            return true
        }

        return dependencySetupHelper?.application(application, open: url, sourceApplication: sourceApplication, annotation: annotation) ?? true
    }

    func applicationProtectedDataDidBecomeAvailable(_ application: UIApplication) {
        setProtectedDataAvailabilityStatus(application)
    }

    func applicationProtectedDataWillBecomeUnavailable(_ application: UIApplication) {
        setProtectedDataAvailabilityStatus(application)
    }
}

// MARK: Private setup methods
fileprivate extension AppDelegate {
    func setupApplication(_ application: UIApplication, launchOptions: [UIApplication.LaunchOptionsKey: Any]?) {
        registerForFirebaseRemoteNotification(application)

        #if !UNIT_TEST_TARGET
        // Users are opted in to CleverTap analytics and communication preferences enabled upon app launch
        if let cleverTapInstance = dependencySetupHelper?.cleverTapInstance {
            cleverTapInstance.setInAppNotificationDelegate(self)
            cleverTapInstance.setOptOut(false)
        }

        if let profileTrackingManager = dependencyManager?.profileTrackingManager {
            profileTrackingManager.setUserDataSendingConsentOptOut(false)
        }
        #endif

        setApplicationState(application)
        setProtectedDataAvailabilityStatus(application)

        AppsFlyerLib.shared().deepLinkDelegate = self
    }

    func registerForFirebaseRemoteNotification(_ application: UIApplication) {
        #if !targetEnvironment(simulator)
        UNUserNotificationCenter.current().delegate = self
        setupCleverTapNotification()
        let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound]
        UNUserNotificationCenter.current().requestAuthorization(options: authOptions, completionHandler: { _, _ in })
        Messaging.messaging().delegate = self
        application.registerForRemoteNotifications()
        #endif
    }

    func setupCleverTapNotification() {
        #if !targetEnvironment(simulator)
        let backAction = UNNotificationAction(identifier: Constants.CleverTapNotificationAction.back.key, title: Constants.CleverTapNotificationAction.back.title, options: [])
        let nextAction = UNNotificationAction(identifier: Constants.CleverTapNotificationAction.next.key, title: Constants.CleverTapNotificationAction.next.title, options: [])
        let learnMoreAction = UNNotificationAction(identifier: Constants.CleverTapNotificationAction.learnMore.key, title: Constants.CleverTapNotificationAction.learnMore.title, options: [])

        let category = UNNotificationCategory(identifier: "CTNotification", actions: [backAction, nextAction, learnMoreAction], intentIdentifiers: [], options: [])
        UNUserNotificationCenter.current().setNotificationCategories([category])
        #endif
    }

    func setProtectedDataAvailabilityStatus(_ application: UIApplication) {
        let storeProvider = dependencyManager?.storeProvider
        let isProtectedDataAvailable = application.isProtectedDataAvailable

        if let encryptedDefaultsStore = storeProvider?.target(EncryptedDefaultsStore.self) {
            encryptedDefaultsStore.isProtectedDataAvailableProperty.value = isProtectedDataAvailable
        }

        if let userDefaultsStore = storeProvider?.target(UserDefaultsStore.self) {
            userDefaultsStore.isProtectedDataAvailableProperty.value = isProtectedDataAvailable

            if !didSetBuildNumber {
                userDefaultsStore.setBuildNumber()
                didSetBuildNumber = true
            }
        }

        if let databaseStore = storeProvider?.target(DatabaseStore.self) {
            databaseStore.isProtectedDataAvailableProperty.value = isProtectedDataAvailable
        }
    }

    func setApplicationState(_ application: UIApplication) {
        let storeProvider = dependencyManager?.storeProvider
        let isInBackground = application.applicationState == .background

        if let databaseStore = storeProvider?.target(DatabaseStore.self) {
            databaseStore.isInBackgroundProperty.value = isInBackground
        }
    }

    func setupAdditionalDependencies() {
        setupSplit()
    }

    func setDeepLinkSource(url: URL, source: AnalyticsAttributionSource) {
        if let analyticsAction = AnalyticsHelper.getAnalyticsAction(for: url) {
            dependencyManager?.analyticsHelper.setAttribution(source: source, for: analyticsAction)
            dependencyManager?.analyticsService.setAppearAttributionSource(source.rawValue)
        }
    }

    func handleDeepLink(userInfo: [AnyHashable: Any], willUseDefaultTitle: Bool = false, source: AnalyticsAttributionSource? = nil) {
        if let externalURIString = userInfo[Constants.Notification.externalURI.rawValue] as? String,
            let externalURI = URL(string: externalURIString) {
            showExternalURI(externalURI)
        }

        if let applicationURI = userInfo[Constants.Notification.applicationURI.rawValue] as? String,
           let url = URL(string: applicationURI) {
            if let source = source {
                setDeepLinkSource(url: url, source: source)
            }

            dependencyManager?.analyticsHelper.setConversionAttributes(for: url, payload: userInfo)

            if !appCoordinator.startDeepLinkFlow(url: url) {
                appCoordinator.getTopCoordinator().showWebView(link: url, willPresent: true, willUseDefaultTitle: willUseDefaultTitle)
            }
        }

        // checks and handles push notifications coming from sendbird
        checkIfSendbirdPushNotification(userInfo: userInfo)
    }

    func openShortcutItem(shortcutItem: UIApplicationShortcutItem) {
        if let quickAction = QuickAction(rawValue: shortcutItem.type) {
            switch quickAction {
            case .scanQr, .requestMoney:
                if let url = shortcutItem.userInfo?["url"] as? String {
                    handleDeepLink(userInfo: [Constants.Notification.applicationURI.rawValue: url])
                }
            }
        }
    }

    func createAppSwitcherView() -> UIView {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        let imageView = UIImageView(image: CommonAsset.Images.MayaLogos.iconMayaLogoPlainGreen.image)
        imageView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(imageView)
        view.backgroundColor = CommonAsset.MayaColors.Background.backgroundPrimaryBlack.color
        NSLayoutConstraint.activate([imageView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
                                     imageView.centerYAnchor.constraint(equalTo: view.centerYAnchor)])
        return view
    }

    func storeApnsToken(deviceToken: Data) {
        let token = deviceToken.map { String(format: "%02.2hhx", $0) }.joined()
        if let encryptedStore = dependencyManager?.storeProvider.target(EncryptedDefaultsStore.self) {
            encryptedStore.write(token, options: EncryptedDefaultsStoreId.apnsToken)
        }
    }
}

// MARK: UNUserNotificationCenterDelegate and MessagingDelegate
extension AppDelegate: UNUserNotificationCenterDelegate, MessagingDelegate {
    func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
        #if !RELEASE && !PROD_TARGET
        UserDefaults.standard.set(fcmToken, forKey: "FCMToken")
        #endif
        dependencyManager?.notificationService.pushToken = fcmToken
        if let databaseStore = dependencyManager?.storeProvider.target(DatabaseStore.self),
           let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
           user.token != nil {
            dependencyManager?.notificationService.sendPushTokenAction.apply().start()
        }
    }

    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable: Any]) {
        Messaging.messaging().appDidReceiveMessage(userInfo)
        dependencySetupHelper?.cleverTapInstance?.handleNotification(withData: userInfo)
        handleDeepLink(userInfo: userInfo, source: .push)
    }

    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable: Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        Messaging.messaging().appDidReceiveMessage(userInfo)
        dependencyManager?.notificationService.didReceiveRemoteNotificationPayload(userInfo)
        dependencySetupHelper?.cleverTapInstance?.handleNotification(withData: userInfo)
        handleDeepLink(userInfo: userInfo, source: .push)
        completionHandler(UIBackgroundFetchResult.newData)
    }

    public func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        let userInfo = notification.request.content.userInfo
        Messaging.messaging().appDidReceiveMessage(userInfo)

        // Will not a push notification based on the received push notification if the thread identifier of the push notification is the same as the supportGroupChannelUrl set by the Inbox SDK
        guard let encryptedStore = dependencyManager?.storeProvider.target(EncryptedDefaultsStore.self),
              let supportGroupChannelUrl = encryptedStore.read(EncryptedDefaultsStoreId.supportGroupChannelUrl, type: String.self).value else { completionHandler(UNNotificationPresentationOptions.alert); return }

        if supportGroupChannelUrl != notification.request.content.threadIdentifier {
            completionHandler(UNNotificationPresentationOptions.alert)
        }
    }

    public func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo
        dependencySetupHelper?.cleverTapInstance?.handleNotification(withData: userInfo, openDeepLinksInForeground: true)
        handleDeepLink(userInfo: userInfo, source: .push)
        completionHandler()
    }
}

// MARK: CleverTapInAppNotificationDelegate
extension AppDelegate: CleverTapInAppNotificationDelegate {
    func inAppNotificationButtonTapped(withCustomExtras customExtras: [AnyHashable: Any]!) {
        handleDeepLink(userInfo: customExtras, willUseDefaultTitle: true, source: .ctInterstitial)
    }
}

// MARK: Appsflyer's DeepLinkDelegate
extension AppDelegate: DeepLinkDelegate {
    func didResolveDeepLink(_ result: DeepLinkResult) {
        switch result.status {
        case .found:
            if let deepLink = result.deepLink,
               deepLink.isDeferred,
               let deepLinkValue = deepLink.deeplinkValue,
               let url = URL(string: deepLinkValue) {
                setDeepLinkSource(url: url, source: .external)
                _ = appCoordinator.startDeepLinkFlow(url: url)
            }
        default: break
        }
    }
}

// MARK: Sendbird Push Notification related methods
extension AppDelegate {
    private func checkIfSendbirdPushNotification(userInfo: [AnyHashable: Any]) {
        guard let sendbirdPushNotif = SendbirdPushNotification(userInfo: userInfo),
              let deeplink = sendbirdPushNotif.createDeepLinkUrl() else { return }

        dependencyManager?.analyticsService.logMayaEvents(name: Analytics.Inbox.pushNotificationTapped.rawValue, attributes: sendbirdPushNotif.analyticsAttributes)
        startDeeplinkFlow(url: deeplink)
    }

    func startDeeplinkFlow (url: URL) {
        if !appCoordinator.startDeepLinkFlow(url: url) {
            appCoordinator.complete(deepLinkHandling: appCoordinator.deepLinkHandling, error: nil)
        }
    }
}

// MARK: - DebugSwift debugging tool
private extension AppDelegate {
    func setupDebugSwift() {
    #if !UNIT_TEST_TARGET
    #if !PROD_TARGET && !SANDBOX_TARGET
        /*
         Location Spoofing interferes with `CLLocationManager`
         and causes a freshly-installed app to hang after location authorization.
         A similar feature exists in the settings bundle anyway.
         Leaks Detection can be handled by `LifetimeTracker` alone.
        */

        let userDefaults = UserDefaults.standard
        let debugSwiftNetworkEnabled = userDefaults.bool(forKey: UserDefaultsStoreId.debugSwiftNetworkEnabled.rawValue)

        if debugSwiftNetworkEnabled {
            DebugSwift.setup(disable: [.location, .leaksDetector])
        } else {
            DebugSwift.setup(hideFeatures: [.network], disable: [.network, .location, .leaksDetector])
        }
    #endif
    #endif
    }
}

#if !UNIT_TEST_TARGET
#if !PROD_TARGET && !SANDBOX_TARGET
extension UIWindow {
    override open func motionEnded(_ motion: UIEvent.EventSubtype, with event: UIEvent?) {
        super.motionEnded(motion, with: event)
        if motion == .motionShake {
            DebugSwift.toggle()
        }
    }
}
#endif
#endif
