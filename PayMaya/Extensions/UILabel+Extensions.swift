//
//  UILabel+Extensions.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 28/04/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import Foundation
import UIKit

extension UILabel {
    func setTextSpacingBy(value: Double) {
        if let textString = self.text {
            let attributedString = NSMutableAttributedString(string: textString)
            attributedString.addAttribute(.kern, value: value, range: NSRange(location: 0, length: attributedString.length - 1))
            attributedText = attributedString
        }
    }

    func setParagraphLineSpacing(
        _ spacing: CGFloat,
        foregroundColor: UIColor = CommonAsset.MayaColors.Content.contentGrey6.color,
        font: UIFont = CommonFontFamily.CerebriSansPro.book.font(size: 14)
    ) {
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = spacing
        let text = text ?? ""
        let attributedString = NSMutableAttributedString(
            string: text,
            attributes: [
                .foregroundColor: textColor ?? foregroundColor,
                .font: font,
                .paragraphStyle: paragraphStyle
            ]
        )
        attributedText = attributedString
    }

    func setBulletList(_ items: [String]) {
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.headIndent = 18
        // Spacing before the bullet
        paragraphStyle.firstLineHeadIndent = 6
        paragraphStyle.paragraphSpacingBefore = 0
        // character spacing between bullet and text
        let bulletSpacing = 2

        let bullet = "•"
        // Define bullet with spacing
        let bulletWithSpacing = "\(bullet)".padding(toLength: Int(bulletSpacing), withPad: " ", startingAt: 0)

        let bulletAttributes: [NSAttributedString.Key: Any] = [.paragraphStyle: paragraphStyle]

        let bulletList = items.map { item -> NSAttributedString in
            let formattedItem = "\(bulletWithSpacing) \(item)"
            return NSAttributedString(string: formattedItem, attributes: bulletAttributes)
        }

        let fullAttributedString = NSMutableAttributedString()
        for (index, item) in bulletList.enumerated() {
            if index > 0 {
                fullAttributedString.append(NSAttributedString(string: "\n"))
            }
            fullAttributedString.append(item)
        }

        self.attributedText = fullAttributedString
    }

    func createColoredString(fullString: String, parameter: String, color: UIColor) {
        // Split the string into parts
        let parts = fullString.components(separatedBy: "_")

        // Ensure the full string has exactly one placeholder
        guard parts.count == 2 else {
            attributedText = NSAttributedString(string: fullString)
            return
        }

        // Create an attributed string for the first part
        let attributedString = NSMutableAttributedString(string: parts[0])

        // Create an attributed string for the parameter with the desired color
        let coloredParameter = NSAttributedString(string: parameter, attributes: [NSAttributedString.Key.foregroundColor: color])

        // Append the colored parameter to the attributed string
        attributedString.append(coloredParameter)

        // Append the remaining part of the string
        attributedString.append(NSAttributedString(string: parts[1]))

        attributedText = attributedString
    }

    func setNumberedBulletsWithColor(bullets: String, color: UIColor, font: UIFont) {
        let attributedString = NSMutableAttributedString(string: bullets)
        let bulletSize = CGSize(width: 20, height: 20)

        // Define paragraph style for line spacing
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.firstLineHeadIndent = 0
        let bulletWidth: CGFloat = bulletSize.width + 8 // Adjust this width based on the bullet image size 20 + 8px(padding)
        paragraphStyle.headIndent = bulletWidth
        paragraphStyle.paragraphSpacingBefore = 0
        paragraphStyle.paragraphSpacing = 5

        // Regular expression to match numbered bullets (e.g., "1 ", "2 ", etc.)
        let pattern = #"(\d+)\ \s"#
        let regex = try? NSRegularExpression(pattern: pattern, options: [])

        // Find matches
        let matches = regex?.matches(in: bullets, options: [], range: NSRange(location: 0, length: bullets.utf16.count)) ?? []

        for match in matches.reversed() {
            // Capture the range of the number and space (excluding the period)
            let numberRange = match.range(at: 1)
            let fullRange = match.range

            // Create the bullet string with attributes
            let numberText = (bullets as NSString).substring(with: numberRange)
            let bulletText = (bullets as NSString).substring(with: fullRange)

            // Create a combined image for the background and the number
            let combinedImage = createCombinedImage(
                number: numberText,
                size: bulletSize,
                color: color,
                font: font
            )
            let attachment = NSTextAttachment()
            attachment.image = combinedImage
            attachment.bounds = CGRect(x: 0, y: -5, width: combinedImage.size.width, height: combinedImage.size.height)

            // Insert the combined image
            let attributedNumber = NSAttributedString(attachment: attachment)

            // Combine the number with the background and the rest of the bullet text
            let combinedAttributedString = NSMutableAttributedString()
            combinedAttributedString.append(attributedNumber)
            combinedAttributedString.append(NSAttributedString(string: "  ")) // Space between image and text

            // Replace the original bullet text with the combined attributed string
            attributedString.replaceCharacters(in: fullRange, with: combinedAttributedString)
        }

        // Apply paragraph style to the entire attributed string
        attributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: attributedString.length))

        self.attributedText = attributedString
    }

    private func createCombinedImage(number: String, size: CGSize, color: UIColor, font: UIFont) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { _ in
            // Draw the background
            let rect = CGRect(origin: .zero, size: size)
            let path = UIBezierPath(roundedRect: rect, cornerRadius: size.height / 2)
            color.setFill()
            path.fill()

            // Draw the number
            let attributes: [NSAttributedString.Key: Any] = [
                .font: font,
                .foregroundColor: UIColor.black
            ]
            let textSize = (number as NSString).size(withAttributes: attributes)
            let textRect = CGRect(
                x: (size.width - textSize.width) / 2,
                y: (size.height - textSize.height) / 2,
                width: textSize.width,
                height: textSize.height
            )
            (number as NSString).draw(in: textRect, withAttributes: attributes)
        }
    }

    func setStyle(_ style: LabelStyle) {
        if let font = style.font {
            self.font = font
        }

        if let textColor = style.textColor {
            self.textColor = textColor
        }
    }
}
