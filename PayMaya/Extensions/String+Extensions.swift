//
//  String+Extensions.swift
//  PayMaya
//
//  Created by <PERSON> on 11/11/2016.
//  Copyright © 2016 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import CommonCrypto
import Foundation
import PhoneNumberKit
import UIKit
import UtilityExtension

private let iso20022Format = "+##-###-#######"
private let nameMaxLength = 40

extension String {
    fileprivate static let phoneNumberKit = PhoneNumberKit()

    /// Returns `""`
    static var empty: String {
        return ""
    }

    /// Returns a new string that has the whitespace and newlines removed from this string
    func trim() -> String {
        return self.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    /// Trim leading, trailing, and multiple consecutive space in between 2 strings
    func smartTrim() -> String {
        return self.trim().replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
    }

    /// Obtains the character at the specified index of this string
    func stringCharacterAtIndex (_ i: Int) -> String {
        guard i < self.count else {
            return ""
        }
        let index = self.index(self.startIndex, offsetBy: i)
        return String(self[index])
    }

    /// Returns true if count is greater than 0
    func isValidPassword() -> Bool {
        return self.count > 0
    }

    /// Returns true if string has at least 1 uppercase letter
    func hasOneUppercase() -> Bool {
        return self.rangeOfCharacter(from: .uppercaseLetters) != nil
    }

    /// Returns true if string has at least 1 lowercase letter
    func hasOneLowercase() -> Bool {
        return self.rangeOfCharacter(from: .lowercaseLetters) != nil
    }

    /// Returns true if string has at least 1 number character
    func hasOneNumber() -> Bool {
        return self.rangeOfCharacter(from: .decimalDigits) != nil
    }

    /// Returns true if count is greater than 7
    func hasValidLength() -> Bool {
        return self.count > 7
    }

    /// Returns true if string has no whitespace
    func hasNoWhitespace() -> Bool {
        return self.rangeOfCharacter(from: .whitespaces) == nil
    }

    /// Returns a new string wherein only leading spaces are removed
    func removeLeadingSpaces() -> String {
        guard let index = firstIndex(where: { !CharacterSet(charactersIn: String($0)).isSubset(of: .whitespaces) }) else {
            return ""
        }
        return String(self[index...])
    }

    /// Formats string to an unordered list item
    func formatToUnorderedListItem() -> String {
        return "<ul>\(self)</ul>"
    }

    /// Returns true if string format is that of smart padala account numbers
    func isAccountNumber() -> Bool {
        guard self.count == 16 else { return false }
        let nonNumericalRange = self.rangeOfCharacter(from: CharacterSet.decimalDigits.inverted)
        return nonNumericalRange == nil
    }

    /// Returns true if string format is Maya username
    func isUsername() -> Bool {
        guard self.count > 1 else { return false }
        return self.first == "@"
    }

    /// Returns a string that has the normal PH phone number prefixes removed
    func getMobileNumberSearchString() -> String {
        var searchString = self.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines).trimmingCharacters(in: CharacterSet.controlCharacters)
        if searchString.hasPrefix("0") && !searchString.hasPrefix("00") && searchString.count > 1 {
            searchString.removeFirst()
        }
        if searchString.hasPrefix("+") && searchString.count > 1 {
            searchString.removeFirst()
        }
        if searchString.hasPrefix("639") && searchString.count > 2 {
            searchString.removeFirst(2)
        }

        return searchString
    }

    /// Capitalizing First Letter
    func capitalizingFirstLetter() -> String {
        return prefix(1).capitalized + dropFirst()
    }

    mutating func capitalizeFirstLetter() {
        self = self.capitalizingFirstLetter()
    }

    /// Returns true if this string matches the supplied regex
    func matchRegex(_ regex: String) -> Bool {
        return self.range(of: regex, options: .regularExpression, range: nil, locale: nil) != nil
    }

    /// Returns a new string. Masks the email string (at most the last 4 characters of the username part of the emai, and if username is shorter than 4, half of that)
    func maskEmail() -> String {
        var unmaskedCharCount = 4
        let emailComponents = self.components(separatedBy: "@")

        guard
            emailComponents.count == 2,
            let username = emailComponents.first,
            username.count > 1
        else {
            return self
        }

        if username.count < unmaskedCharCount {
            unmaskedCharCount = username.count / 2
        }

        let unMaskedIndex = username.index(username.startIndex, offsetBy: unmaskedCharCount)
        let maskedCount = username.count - unmaskedCharCount
        let maskedString = String.empty.padding(toLength: maskedCount, withPad: "*", startingAt: 0)

        return self.replacingCharacters(in: unMaskedIndex..<username.endIndex, with: maskedString)
    }

    /// Returns the string itself if it is not empty, otherwise returns nil
    var nonEmptyValue: String? {
        return !isEmpty ? self : nil
    }

    /// Returns the string itself if it is not nil, and an empty string if it is
    static func unwrappedValue(_ value: String?) -> String {
        return value ?? String.empty
    }

    /// If `self` is a json string, returns a json dictionary
    func toDictionary() -> [String: Any]? {
        guard
            let data = self.data(using: .utf8),
            let json = try? JSONSerialization.jsonObject(with: data, options: .allowFragments),
            let dictionary = json as? [String: Any]
        else { return nil }
        return dictionary
    }

    /// If `self` is a json string, returns a dictionary
    func toDictionaryArray() -> [[String: Any]]? {
        guard
            let data = self.data(using: .utf8),
            let json = try? JSONSerialization.jsonObject(with: data, options: .allowFragments),
            let array = json as? [[String: Any]]
            else { return nil }
        return array
    }

    /// decode base64 `self` to given type that conforms to Decodable
    func decodeData<T: Decodable>(type: T.Type) -> T? {
        let decoder = JSONDecoder()
        guard
            let decodedData = Data(base64Encoded: self),
            let decodable = try? decoder.decode(type, from: decodedData)
        else { return nil }
        return decodable
    }

    /// Format `text` as a PH phone number
    static func formatToPhilippinesMSISDN(_ text: String, _ withPrefix: Bool = true) -> String {
        let msisdnNumber = text.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        if let phoneNumber = try? String.phoneNumberKit.parse(msisdnNumber, withRegion: Constants.Defaults.Common.countryCode.rawValue, ignoreType: true) {
            let formattedMsisdn = String.phoneNumberKit.format(phoneNumber, toType: .e164, withPrefix: withPrefix)
            return formattedMsisdn
        } else {
            return msisdnNumber
        }
    }

    /// Formats `msisdn` to a string using the `format` supplied
    /// - parameter msisdn: A valid mobile number string in the E. 164 format
    /// - parameter format: A format to which the msisdn will be converted to. Use # to signify numbers (e.g. A format of +##(###)-####### will convert +639191234567 to +63(919)-1234567)
    /// - returns: A new msisdn string formatted according to `format`. Will return the original string if formatting failed (e.g. wrong number of #)
    static func formatE164MSISDN(_ msisdn: String, with format: String) -> String {
        var numberString = msisdn.components(separatedBy: .decimalDigits.inverted).joined()
        let formatHashes = format.replacingOccurrences(of: "[^#]", with: "", options: .regularExpression)

        guard numberString.count == formatHashes.count else { return msisdn }

        var formattedString = ""

        for character in format {
            let formatted = character == "#" ? numberString.removeFirst() : character
            formattedString.append(formatted)
        }

        return formattedString
    }

    /// Format `msisdn` to ISO20022 format (e.g. +63-919-1234567)
    static func formatToISO20022Format(msisdn: String) -> String {
        return String.formatE164MSISDN(msisdn, with: iso20022Format)
    }

    /// Returns true if supplied string is a valid philippine msisdn
    static func isValidMSISDN(_ msisdn: String) -> Bool {
        guard let _ = try? String.phoneNumberKit.parse(msisdn, withRegion: Constants.Defaults.Common.countryCode.rawValue, ignoreType: true) else { return false }
        return true
    }

    /// Returns true if `self` has matching quotes as prefix and suffix
    func hasMatchingQuotes() -> Bool {
        return (hasPrefix("“") && hasSuffix("”")) || (hasPrefix("\"") && hasSuffix("\"")) || (hasPrefix("&#34;") && hasSuffix("&#34;"))
    }

    /// Used to format a gift message in the shop payment
    func printableString() -> String {
        var dataString = self
        let senderFrom = L10n.Sendmoney.Spiel.from
        if dataString.contains(senderFrom),
            let signatureRange = dataString.range(of: "\n\(senderFrom)") {
            let message = String(dataString[dataString.startIndex...dataString.index(before: signatureRange.lowerBound)])
            let signature = String(dataString[signatureRange.lowerBound...index(before: dataString.endIndex)])
            dataString = String(format: "”%@“%@", message, signature)
        } else if !dataString.starts(with: senderFrom) && !hasMatchingQuotes() {
            dataString = String(format: "”%@“", dataString)
        }

        if dataString.contains("\n") {
           dataString = dataString.replacingOccurrences(of: "\n", with: "<br />")
        }

        guard
            let data = dataString.data(using: .utf8),
            let attributedString = try? NSAttributedString(data: data, options: [NSAttributedString.DocumentReadingOptionKey.documentType: NSAttributedString.DocumentType.html, NSAttributedString.DocumentReadingOptionKey.characterEncoding: Encoding.utf8.rawValue], documentAttributes: nil)
        else {
            return String.empty
        }

        return attributedString.string
    }

    /// Returns true if `self` is a valid URL
    func isValidURL() -> Bool {
        if let detector = try? NSDataDetector(types: NSTextCheckingResult.CheckingType.link.rawValue),
           let match = detector.firstMatch(in: self, options: [], range: NSRange(location: 0, length: self.utf16.count)) {
            return match.range.length == self.utf16.count
        } else {
            return false
        }
    }

    /// Returns an attributed string with attributes based on this html string
    var htmlAttributedString: NSAttributedString? {
        guard let data = data(using: .utf8) else { return NSAttributedString() }
        do {
            return try NSAttributedString(data: data, options: [.documentType: NSAttributedString.DocumentType.html, .characterEncoding: String.Encoding.utf8.rawValue], documentAttributes: nil)
        } catch {
            return NSAttributedString()
        }
    }

    /// Returns an attributed string with the specified font and color as hex string
    func getHTMLAttributedString(with font: UIFont, color: UIColor, shouldUseSystemFont: Bool = true) -> NSAttributedString? {
        var formattedHTML = self.replacingOccurrences(of: "&lt;", with: "<")
        formattedHTML = formattedHTML.replacingOccurrences(of: "&gt;", with: ">")
        formattedHTML = self.replacingOccurrences(of: "\n", with: "<br>")
        let rawHex = color.rawHexString
        let systemFont = shouldUseSystemFont ? "'-apple-system', " : ""
        let styledHTML = String(format: "<span style=\"font-family: \(systemFont)'\(font.fontName)'; font-size: \(font.pointSize); color: #\(rawHex)\">%@</span>", formattedHTML)
        return styledHTML.htmlAttributedString
    }

    /// Encodes this string with percent encoding
    func encodedURLString() -> String {
        let unreserved = "-._~/?"
        var allowedCharacterSet = CharacterSet.alphanumerics
        allowedCharacterSet.insert(charactersIn: "\(unreserved)")
        return self.addingPercentEncoding(withAllowedCharacters: allowedCharacterSet) ?? self
    }

    /// Returns true if `self` contains an emoji unicode
    var containsEmoji: Bool {
        for scalar in unicodeScalars {
            switch scalar.value {
            case 0x3030, 0x00AE, 0x00A9,
            0x1D000...0x1F77F,
            0x2100...0x27BF,
            0xFE00...0xFE0F,
            0x1F900...0x1F9FF:
                return true
            default:
                continue
            }
        }
        return false
    }

    /**
     Formats the string to the semantic version scheme (major.minor.patch). If the string has missing components,
     it will append "0" to each of them. For example, "18" will return as "18.0.0" and "18.1" will return as "18.1.0"
    */
    var semanticVersionString: String? {
        var versionComponents = split(separator: ".").compactMap { return Int($0) }
        let versionLength = versionComponents.count

        guard versionLength >= 1, versionLength <= 3 else {
            return nil
        }

        if versionLength == 1 {
            versionComponents.append(0) // Minor version
        }

        if versionLength == 2 {
            versionComponents.append(0) // Patch version
        }

        return versionComponents
            .map { "\($0)" }
            .joined(separator: ".")
    }

    /**
     Compares the calling string with the current version number of the app.
     
     The string must be in SemVer notation.
     */
    func compareToCurrentAppVersion() -> ComparisonResult? {
        guard let currentVersion = Bundle.init(for: MayaSettingsViewModel.self).object(forInfoDictionaryKey: Constants.Keys.BuildVersion.version.rawValue) as? String else { return nil }

        let versionArray = split(separator: ".").compactMap { return Int($0) }
        let currentVersionArray = currentVersion.split(separator: ".").compactMap { return Int($0) }

        guard versionArray.count == 3 && currentVersionArray.count == 3 else { return nil }

        var result: ComparisonResult?
        for index in 0...2 {
            if versionArray[index] > currentVersionArray[index] {
                result = .orderedDescending
                break
            } else if versionArray[index] == currentVersionArray[index] {
                result = .orderedSame
            } else if versionArray[index] < currentVersionArray[index] {
                result = .orderedAscending
                break
            }
        }

        return result
    }

    /**
     Converts string to simple NSAttributedString.
     */
    func convertToAttributedString(font: UIFont, fontColor: UIColor, alignment: NSTextAlignment = .center, lineBreakMode: NSLineBreakMode = .byWordWrapping) -> NSAttributedString {
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 3
        paragraphStyle.alignment = alignment
        paragraphStyle.lineBreakMode = lineBreakMode
        let attributes = [NSAttributedString.Key.font: font, NSAttributedString.Key.foregroundColor: fontColor, NSAttributedString.Key.paragraphStyle: paragraphStyle]
        return NSAttributedString(string: self, attributes: attributes)
    }

    /**
     Converts a string to a NSAttributedString with attributes applied for the passed dictionary of links and URLs
     */
    func setLink(
        links: [String: URL?],
        textColor: UIColor = CommonAsset.Colors.Constants.dark.color,
        textFont: UIFont = UIFont.systemFont(ofSize: 14.0, weight: .regular),
        paragraphSpacing: CGFloat = 0.0,
        lineHeightMultiple: CGFloat = 0.0,
        textAlignment: NSTextAlignment? = nil
    ) -> NSAttributedString {
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = paragraphSpacing
        paragraphStyle.lineHeightMultiple = lineHeightMultiple
        if let textAlignment {
            paragraphStyle.alignment = textAlignment
        }

        let attributedString = NSMutableAttributedString(
            string: self,
            attributes: [
                .foregroundColor: textColor,
                .font: textFont,
                .paragraphStyle: paragraphStyle
        ])

        for (link, url) in links {
            guard let range = range(of: link),
                let stringUrl = url?.absoluteString else {
                continue
            }

            attributedString.addAttribute(.link, value: stringUrl, range: NSRange(range, in: self))
        }

        return attributedString
    }

    /**
     Returns  the character in the given index. Returns as String datatype.
     */
    subscript(i: Int) -> String {
        guard i < self.count else { return "" }
        return String(self[index(startIndex, offsetBy: i)])
    }

    /**
     Returns extracted substrings from a string that match a regex pattern
     */
    func substringsThatMatch(regex: String) -> [String] {
        do {
            let regex = try NSRegularExpression(pattern: regex, options: [])
            let nsString = self as NSString
            let results = regex.matches(in: self, options: [], range: NSMakeRange(0, nsString.length))
            return results.map { nsString.substring(with: $0.range) }
        } catch {
            return []
        }
    }

    func removeAllParentheses() -> String {
        return self.replacingOccurrences(of: "(", with: "").replacingOccurrences(of: ")", with: "")
    }

    /// Returns an attributed string with formatted bold substring
    func formatSubstringToBold(substring: String, fontSize: CGFloat) -> NSAttributedString? {
        let range = (self as NSString).range(of: substring)
        guard range.location != NSNotFound else { return nil }
        let attributedString = NSMutableAttributedString(string: self)
        attributedString.addAttributes([.font: UIFont.boldSystemFont(ofSize: fontSize)], range: range)
        return attributedString
    }

    static func assembleAttributedString(with image: UIImage, text: String?, font: UIFont) -> NSMutableAttributedString? {
        guard let text = text else { return nil }
        return assembleAttributedString(with: image, text: NSAttributedString(string: text), font: font)
    }

    static func assembleAttributedString(with image: UIImage, text: NSAttributedString?, font: UIFont) -> NSMutableAttributedString? {
        guard let text = text else { return nil }
        let range = (text.string as NSString).range(of: "~")
        guard range.location != NSNotFound else { return nil }
        let attributedString = NSMutableAttributedString(attributedString: text)
        let imageAttachment = NSTextAttachment()
        imageAttachment.bounds = CGRect(x: 0, y: (font.capHeight - image.size.height) / 2, width: image.size.width, height: image.size.height)
        imageAttachment.image = image
        let attributedAttachment = NSAttributedString(attachment: imageAttachment)
        attributedString.replaceCharacters(in: range, with: attributedAttachment)
        return attributedString
    }

    /// Returns a string  in "+63 XXX XXX XXXX" format
    func applyMayaMobileNumberFormat() -> String {
        let mobileNumber = String.formatToPhilippinesMSISDN(self)
        let pattern: String = "+XX XXX XXX XXXX"
        let replacementCharacter: Character = "X"
        var pureNumber = mobileNumber.replacingOccurrences( of: "[^0-9]", with: "", options: .regularExpression)
        for index in 0 ..< pattern.count {
            guard index < pureNumber.count else { return pureNumber }
            let stringIndex = String.Index(utf16Offset: index, in: pattern)
            let patternCharacter = pattern[stringIndex]
            guard patternCharacter != replacementCharacter else { continue }
            pureNumber.insert(patternCharacter, at: stringIndex)
        }
        return pureNumber
    }

    /// Returns true if `self` is a valid name containing alphabetical characters, hyphen (-), period (.), and space and not more than charCount
    func isValidName(with charCount: Int = nameMaxLength) -> Bool {
        return self.matchRegex("[A-Za-z .\\-]+$") && self.count <= charCount
    }

    /// Returns a string  in "0XXXXXXXXXX" format
    func formatMobileNumberToLocal() -> String {
        guard String.isValidMSISDN(self) else { return self }
        return "0\(String.formatToPhilippinesMSISDN(self, false))".removeAllWhitespaces()
    }

    func removeAllWhitespaces() -> String {
        return self.replacingOccurrences(of: " ", with: String.empty)
    }

    func removeAllPlusChar() -> String {
        return self.replacingOccurrences(of: "+", with: String.empty)
    }

    func formatMobileNumber() -> String {
        var newString = self

        if newString.hasPrefix("+63") {
            newString = String(newString.dropFirst(3))
        } else if newString.hasPrefix("63") {
            newString = String(newString.dropFirst(2))
        } else if newString.hasPrefix("+") {
            newString = String(newString.dropFirst(1))
        } else if newString.hasPrefix("0") {
            newString = String(newString.dropFirst(1))
        }

        return newString
    }

    var rangesForOrdinalSuffix: [NSRange] {
        let textRange = self.startIndex..<self.endIndex
        let range = NSRange(textRange, in: self)
        if let regex = try? NSRegularExpression(pattern: "(?<=\\d)(st|nd|rd|th)(?![a-zA-Z])") {
            let matches = regex.matches(in: self, range: range)
            return matches.map({ $0.range(at: 1) })
        }
        return [NSMakeRange(0, 0)]
    }

    // Helper function to automatically add "-" after every three characters in a textfield and returns a string in "123-343-343-343" format
    func formatTinNumber(every groupSize: String.IndexDistance, with separator: Character) -> String {
        let text = replacingOccurrences(of: String(separator), with: "")
        return String(text.enumerated().map {
            $0.offset % groupSize == 0 ? [separator, $0.element] : [$0.element]
        }.joined().dropFirst())
    }
}

// Card Helper functions
extension String {
    /// Formats a first and last name as normally seen in credit cards
    static func formatCardDisplayName(firstName: String, lastName: String) -> String {
        let cardNameLength = 25
        let spaceLength = 1
        var displayName = String.empty

        if lastName.count >= cardNameLength {
            let indexLimit = lastName.index(lastName.startIndex, offsetBy: cardNameLength - spaceLength)
            displayName = String(lastName[..<indexLimit])
        } else {
            let offset = (cardNameLength - lastName.count - spaceLength)
            let actualOffset = offset > firstName.count ? firstName.count : offset
            let indexLimit = firstName.index(firstName.startIndex, offsetBy: actualOffset)
            displayName = "\(firstName[..<indexLimit]) \(lastName)"
        }
        return displayName
    }

    /// Formats the expiry as the one seen in credit cards (e.g. 01/21)
    static func formatCardExpiry(month: String, year: String) -> String {
        return "\(month)/\(year.suffix(2))"
    }

    /// Formats a string as the card number normally seen in credit cards (spaces every 4 numbers)
    static func formatCardNumber(_ number: String) -> String {
        var cardNumber = String.empty
        for i in 0..<number.count / 4 {
            let startIndex = number.index(number.startIndex, offsetBy: i * 4)
            let endIndex = number.index(startIndex, offsetBy: 3)
            let fourDigitNumber = String(number[startIndex...endIndex])
            cardNumber.append(fourDigitNumber)
            if i < 3 {
                let spaceChar = Character(" ")
                cardNumber.append(spaceChar)
            }
        }
        return cardNumber
    }

    /// Creates a date from string value based on the given format.
    func getDate(format: String) -> Date? {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = format
        return dateFormatter.date(from: self)
    }

    /// Creates a date from string value using Gregorian calendar based on the given format.
    func getGregorianDate(format: String) -> Date? {
        let dateFormatter = DateFormatter()
        dateFormatter.calendar = Calendar(identifier: .gregorian)
        dateFormatter.dateFormat = format
        return dateFormatter.date(from: self)
    }

    /// Formats and concatenates several address details as normally seen in credit cards
    static func formatAddress(line1: String, line2: String?, locality: String, city: String, state: String, zipcode: String, country: String) -> String {
        var secondaryAddress = String.empty
        if let line2Value = line2, line2Value.count > 0 {
            secondaryAddress = "\(line2Value), "
        }
        let localityAddress = locality.count > 0 ? "\(locality), " : String.empty
        let cityAddress = city.count > 0 ? "\(city), " : String.empty
        return "\(line1), \(secondaryAddress)\(localityAddress)\(cityAddress)\(state), \(zipcode), \(country)"
    }

    func toBase64() -> String {
        return Data(self.utf8).base64EncodedString()
    }

    func tosha256Base64Encode() -> String {
        guard let data = self.data(using: .utf8) else {
            return ""
        }

        var hash = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))
        data.withUnsafeBytes {
            _ = CC_SHA256($0.baseAddress, CC_LONG(data.count), &hash)
        }

        let hashData = Data(hash)
        return hashData.base64EncodedString()
    }

    func lowercasingFirstLetter() -> String {
        return prefix(1).lowercased() + self.dropFirst()
    }

    func extractAllPhonePadCharacters() -> String? {
        return extractStringFromCharacterSet(set: CharacterSet(charactersIn: "0123456789+#*"))
    }

    func extractAllNumberPadChacters() -> String? {
        return extractStringFromCharacterSet(set: CharacterSet.decimalDigits)
    }

    fileprivate func extractStringFromCharacterSet(set: CharacterSet) -> String? {
        let filtered = self.components(separatedBy: set.inverted).joined()
        return filtered.isEmpty ? nil : filtered
    }

    func slice(from bottomString: String? = nil, to upperString: String? = nil) -> String? {
        var resultString = self

        if let bottomString = bottomString {
            guard let bottomStringRange = resultString.range(of: bottomString) else { return nil }
            let substringStartIndex = bottomStringRange.upperBound
            let substringEndIndex = resultString.endIndex
            let substring = resultString[substringStartIndex..<substringEndIndex]
            resultString = .init(substring)
        }

        if let upperString = upperString {
            guard let upperStringRange = resultString.range(of: upperString) else { return nil }
            let substringStartIndex = resultString.startIndex
            let substringEndIndex = upperStringRange.lowerBound
            let substring = resultString[substringStartIndex..<substringEndIndex]
            resultString = .init(substring)
        }
        return resultString
    }

    func component(withLength length: Int) -> [String] {
       return stride(from: 0, to: self.count, by: length).map {
           let start = self.index(self.startIndex, offsetBy: $0)
           let end = self.index(start, offsetBy: length, limitedBy: self.endIndex) ?? self.endIndex
           return String(self[start..<end])
       }
    }

    func removeAlphabet() -> String {
        let pattern = "[a-zA-Z]"
        let regex = try? NSRegularExpression(pattern: pattern, options: [])
        let range = NSRange(location: 0, length: self.utf16.count)

        let result = regex?.stringByReplacingMatches(in: self, options: [], range: range, withTemplate: "") ?? self
        return result
    }
}

// Voucher Helper methods
extension String {
    /// Extracts the voucher code from the voucherId String
    ///
    /// voucherId Pattern:
    /// 
    ///  <campaign_name>.<voucher_code>.<start_date_in_utc)>.<end_date_in_utc>
    ///
    /// ```
    /// let voucherId = "₱500.00 Landers Superstore.MAYAgdcL6kKDApTSLNDR.2024-04-17T16:00:00.000Z.2024-11-29T05:02:51.177Z"
    /// let voucherCode = voucherId.extractVoucherCode()
    /// print(voucherCode) // "MAYAgdcL6kKDApTSLNDR"
    /// ```
    ///  - Returns: the voucher code from the voucherId string
    ///
    func extractVoucherCode() -> String? {
        let pattern = "[^.]+\\.([^.]+)\\.\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{3}Z\\.\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{3}Z"
        guard let regex = try? NSRegularExpression(pattern: pattern) else {
            return nil
        }

        let nsString = self as NSString
        let range = NSRange(location: 0, length: nsString.length)
        let results = regex.matches(in: self, range: range)

        guard let match = results.first else {
            return nil
        }

        let codeRange = match.range(at: 1)
        let code = nsString.substring(with: codeRange)
        return code
    }
}

extension String {
    /// Masks all but the last 'count' characters of the string with asterisks.
    /// ex.`987654321` will be  `*****4321`
    func maskStringExceptLastCharacters(count: Int = 4) -> String {
        guard self.count > count else { return self }

        return String(repeating: "*", count: self.count - count) + self.suffix(count)
    }

    // TODO: Add unit tests for this method
    func replaceAmountWithPlaceholder(placeholder: String = "₱X.XX") -> String {
        let pattern = "₱[0-9,]+(?:\\.[0-9]{1,2})?"

        return replacingOccurrences(of: pattern, with: placeholder, options: .regularExpression)
    }

    func replaceCurlyQuotes() -> String {
        self
            .replacingOccurrences(of: "“", with: "\"")
            .replacingOccurrences(of: "”", with: "\"")
            .replacingOccurrences(of: "‘", with: "'")
            .replacingOccurrences(of: "’", with: "'")
    }
}
