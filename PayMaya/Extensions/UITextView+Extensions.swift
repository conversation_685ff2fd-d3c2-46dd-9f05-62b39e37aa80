//
//  UITextView+Extensions.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 6/17/21
//  Copyright © 2021 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import UIKit

extension UITextView {
    /// Removes the top and bottom paddings made by `textContainerInset` and the width padding made by `textContainer.lineFragmentPadding`
    func removePaddings() {
        textContainerInset = .zero
        textContainer.lineFragmentPadding = 0
    }

    func setLink(
        text: String,
        link: String,
        url: URL? = nil,
        textColor: UIColor = CommonAsset.Colors.Constants.dark.color,
        linkColor: UIColor = CommonAsset.Colors.Constants.darkSkyBlue.color,
        textFont: UIFont = UIFont.systemFont(ofSize: 14.0, weight: .regular),
        linkFont: UIFont = UIFont.systemFont(ofSize: 14.0, weight: .bold),
        addUnderlineToLink: Bool = false,
        paragraphSpacing: CGFloat = 0.0,
        lineHeightMultiple: CGFloat = 0.0,
        textAlignment: NSTextAlignment? = nil
    ) {
        setLink(text: text, links: [link: url], textColor: textColor, linkColor: linkColor, textFont: textFont, linkFont: linkFont, addUnderlineToLink: addUnderlineToLink, paragraphSpacing: paragraphSpacing, lineHeightMultiple: lineHeightMultiple, textAlignment: textAlignment)
    }

    func setLink(
        text: String,
        links: [String: URL?],
        textColor: UIColor = CommonAsset.Colors.Constants.dark.color,
        linkColor: UIColor = CommonAsset.Colors.Constants.darkSkyBlue.color,
        textFont: UIFont = UIFont.systemFont(ofSize: 14.0, weight: .regular),
        linkFont: UIFont = UIFont.systemFont(ofSize: 14.0, weight: .bold),
        addUnderlineToLink: Bool = false,
        paragraphSpacing: CGFloat = 0.0,
        lineHeightMultiple: CGFloat = 0.0,
        textAlignment: NSTextAlignment? = nil
    ) {
        let attributedText = text.setLink(links: links, textColor: textColor, textFont: textFont, paragraphSpacing: paragraphSpacing, lineHeightMultiple: lineHeightMultiple, textAlignment: textAlignment)
        setLink(attributedText: attributedText, linkColor: linkColor, linkFont: linkFont, addUnderlineToLink: addUnderlineToLink)
    }

    func setLink(
        attributedText: NSAttributedString,
        linkColor: UIColor = CommonAsset.Colors.Constants.darkSkyBlue.color,
        linkFont: UIFont = UIFont.systemFont(ofSize: 14.0, weight: .bold),
        addUnderlineToLink: Bool = false
    ) {
        var linkAttributes: [NSAttributedString.Key: Any] = [.foregroundColor: linkColor, .font: linkFont]
        if addUnderlineToLink {
            linkAttributes[.underlineStyle] = NSUnderlineStyle.single.rawValue
        }

        self.linkTextAttributes = linkAttributes
        self.attributedText = attributedText
    }
}
