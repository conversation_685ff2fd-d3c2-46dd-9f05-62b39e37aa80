//
//  MayaDashboardViewController+Extensions.swift
//  PayMaya
//
//  Created by Augment Agent on 12/19/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import UIKit

/// Extension to add Google Interstitial Ads functionality to the main dashboard
/// This is completely separate from the existing CleverTap ad carousel functionality
extension MayaDashboardViewController {
    
    // MARK: - Google Interstitial Ads Integration
    
    /// Setup Google Interstitial Ads for the dashboard
    /// Call this method in viewDidLoad() to enable interstitial ads
    func setupGoogleInterstitialAds() {
        #if DEBUG
        // Create and configure the interstitial ad manager
        let interstitialAdManager = GoogleInterstitialAdManager()
        
        // Configure for dashboard usage
        interstitialAdManager.configure(
            adUnitId: "ca-app-pub-3940256099942544/1033173712", // Google test ad unit
            showInterval: 60.0, // Show every 60 seconds on dashboard (less frequent than test)
            maxAdsPerSession: 2 // Limit to 2 ads per session on main dashboard
        )
        
        // Initialize with consent request
        interstitialAdManager.initialize(from: self)
        
        // Store reference (you'll need to add this property to MayaDashboardViewController)
        // self.googleInterstitialAdManager = interstitialAdManager
        
        print("[MayaDashboardViewController] Google Interstitial Ads setup completed")
        #endif
    }
    
    /// Start showing interstitial ads when dashboard becomes visible
    /// Call this in viewWillAppear()
    func startGoogleInterstitialAds() {
        #if DEBUG
        // self.googleInterstitialAdManager?.startAutomaticAds()
        print("[MayaDashboardViewController] Google Interstitial Ads started")
        #endif
    }
    
    /// Stop showing interstitial ads when dashboard becomes hidden
    /// Call this in viewDidDisappear()
    func stopGoogleInterstitialAds() {
        #if DEBUG
        // self.googleInterstitialAdManager?.stopAutomaticAds()
        print("[MayaDashboardViewController] Google Interstitial Ads stopped")
        #endif
    }
    
    /// Add a test button to manually trigger interstitial ads (DEBUG only)
    /// This creates a floating button for easy testing
    func addGoogleInterstitialAdTestButton() {
        #if DEBUG
        let testButton = UIButton(type: .system)
        testButton.setTitle("Test Interstitial", for: .normal)
        testButton.backgroundColor = .systemBlue
        testButton.setTitleColor(.white, for: .normal)
        testButton.layer.cornerRadius = 25
        testButton.titleLabel?.font = .boldSystemFont(ofSize: 12)
        
        testButton.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(testButton)
        
        NSLayoutConstraint.activate([
            testButton.widthAnchor.constraint(equalToConstant: 100),
            testButton.heightAnchor.constraint(equalToConstant: 50),
            testButton.trailingAnchor.constraint(equalTo: view.safeAreaLayoutGuide.trailingAnchor, constant: -20),
            testButton.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -100)
        ])
        
        testButton.addTarget(self, action: #selector(testInterstitialAdButtonTapped), for: .touchUpInside)
        
        print("[MayaDashboardViewController] Test button added for interstitial ads")
        #endif
    }
    
    @objc private func testInterstitialAdButtonTapped() {
        #if DEBUG
        // self.googleInterstitialAdManager?.showInterstitialAdNow(from: self)
        
        // For now, show the dedicated test view controller
        let testViewController = GoogleInterstitialAdTestViewConKtroller.create()
        let navigationController = UINavigationController(rootViewController: testViewController)
        present(navigationController, animated: true)
        #endif
    }
}

// MARK: - Integration Instructions
/*
 
 To integrate Google Interstitial Ads into MayaDashboardViewController:
 
 1. Add this property to MayaDashboardViewController:
    private var googleInterstitialAdManager: GoogleInterstitialAdManager?
 
 2. In viewDidLoad(), add:
    setupGoogleInterstitialAds()
    addGoogleInterstitialAdTestButton() // Optional: for testing
 
 3. In viewWillAppear(), add:
    startGoogleInterstitialAds()
 
 4. In viewDidDisappear(), add:
    stopGoogleInterstitialAds()
 
 5. Uncomment the lines in the extension methods above
 
 Example integration:
 
 class MayaDashboardViewController: ViewController {
     private var googleInterstitialAdManager: GoogleInterstitialAdManager?
     
     override func viewDidLoad() {
         super.viewDidLoad()
         // ... existing code ...
         setupGoogleInterstitialAds()
         addGoogleInterstitialAdTestButton() // Optional
     }
     
     override func viewWillAppear(_ animated: Bool) {
         super.viewWillAppear(animated)
         // ... existing code ...
         startGoogleInterstitialAds()
     }
     
     override func viewDidDisappear(_ animated: Bool) {
         super.viewDidDisappear(animated)
         // ... existing code ...
         stopGoogleInterstitialAds()
     }
 }
 
 */
