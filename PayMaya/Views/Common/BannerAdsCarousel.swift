//
//  BannerAdsCarousel.swift
//  PayMaya
//
//  Created by <PERSON> on 06/09/25.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import UIKit
import GoogleMobileAds
import ReactiveSwift

/// A custom view that displays multiple banner ads in a carousel format
class BannerAdsCarousel: UIView {
    
    // MARK: - Constants
    private enum Constants {
        static let defaultScrollInterval: TimeInterval = 5.0
        static let animationDuration: TimeInterval = 0.25
    }
    
    // MARK: - Protocols
    protocol CarouselStateListener: AnyObject {
        func isFragmentVisible() -> Bool
    }
    
    protocol CarouselLoadedListener: AnyObject {
        func onLoaded()
    }
    
    // MARK: - Properties
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.translatesAutoresizingMaskIntoConstraints = false
        cv.delegate = self
        cv.dataSource = self
        cv.isPagingEnabled = true
        cv.showsHorizontalScrollIndicator = false
        cv.backgroundColor = .clear
        cv.register(BannerAdCollectionViewCell.self, forCellWithReuseIdentifier: BannerAdCollectionViewCell.identifier)
        return cv
    }()
    
    private var bannerViews: [GADBannerView] = []
    private var currentItem = 0
    private var autoScrollTimer: Timer?
    
    // Configuration properties
    private var autoScrollDelayMs: TimeInterval = Constants.defaultScrollInterval
    private var isAutoScrollEnabled = true
    private var endlessScrolling = true
    
    // Listeners
    weak var carouselStateListener: CarouselStateListener?
    weak var carouselLoadedListener: CarouselLoadedListener?
    private var adListener: GADBannerViewDelegate?
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        addSubview(collectionView)
        NSLayoutConstraint.activate([
            collectionView.topAnchor.constraint(equalTo: topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
        
        setupGestureHandling()
    }
    
    private func setupGestureHandling() {
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePanGesture(_:)))
        collectionView.addGestureRecognizer(panGesture)
    }
    
    @objc private func handlePanGesture(_ gesture: UIPanGestureRecognizer) {
        switch gesture.state {
        case .began:
            stopAutoScroll()
            superview?.superview?.isScrollEnabled = false
        case .ended, .cancelled:
            superview?.superview?.isScrollEnabled = true
            resetAutoScroll()
        default:
            break
        }
    }
    
    // MARK: - Public Methods
    
    /// Load multiple banner ads into the carousel
    /// - Parameters:
    ///   - adUnitIds: List of ad unit IDs to display in the carousel
    ///   - adSize: The size of the banner ads (default: GADAdSizeBanner)
    ///   - autoScrollDelay: Delay in seconds between auto-scrolling (default 5.0)
    ///   - listener: Optional listener for ad events
    func loadAds(
        adUnitIds: [String],
        adSize: GADAdSize = GADAdSizeBanner,
        autoScrollDelay: TimeInterval = Constants.defaultScrollInterval,
        listener: GADBannerViewDelegate? = nil
    ) {
        // Clear existing ads
        bannerViews.removeAll()
        autoScrollDelayMs = autoScrollDelay
        self.adListener = listener
        
        var loadedCount = 0
        
        // Create banner views for each ad unit ID
        for adUnitId in adUnitIds {
            let bannerView = GADBannerView(adSize: adSize)
            bannerView.adUnitID = adUnitId
            bannerView.delegate = BannerAdDelegate(
                originalDelegate: listener,
                onAdLoaded: { [weak self] in
                    loadedCount += 1
                    if loadedCount == adUnitIds.count {
                        DispatchQueue.main.async {
                            self?.carouselLoadedListener?.onLoaded()
                        }
                    }
                }
            )
            
            // Find the root view controller to set as the root view controller
            if let rootViewController = findViewController() {
                bannerView.rootViewController = rootViewController
            }
            
            bannerViews.append(bannerView)
            
            // Load the ad
            let request = GADRequest()
            bannerView.load(request)
        }
        
        // Reload collection view
        DispatchQueue.main.async { [weak self] in
            self?.collectionView.reloadData()
            self?.setupInfiniteScrolling()
            self?.startAutoScrollIfNeeded()
        }
    }
    
    private func setupInfiniteScrolling() {
        guard endlessScrolling && bannerViews.count > 1 else { return }
        
        // Start from a position that allows infinite scrolling both ways
        let middleIndex = (Int.max / 2 / bannerViews.count) * bannerViews.count
        currentItem = middleIndex
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            let indexPath = IndexPath(item: self.currentItem, section: 0)
            self.collectionView.scrollToItem(at: indexPath, at: .centeredHorizontally, animated: false)
        }
    }
    
    private func startAutoScrollIfNeeded() {
        guard isAutoScrollEnabled && bannerViews.count > 1 else { return }
        startAutoScroll()
    }
    
    private func startAutoScroll() {
        stopAutoScroll()
        
        autoScrollTimer = Timer.scheduledTimer(withTimeInterval: autoScrollDelayMs, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            if self.carouselStateListener?.isFragmentVisible() != false {
                self.scrollToNextItem()
            }
        }
    }
    
    private func stopAutoScroll() {
        autoScrollTimer?.invalidate()
        autoScrollTimer = nil
    }
    
    private func resetAutoScroll() {
        if isAutoScrollEnabled {
            startAutoScroll()
        }
    }
    
    private func scrollToNextItem() {
        guard bannerViews.count > 1 else { return }
        
        currentItem += 1
        let indexPath = IndexPath(item: currentItem, section: 0)
        
        DispatchQueue.main.async { [weak self] in
            self?.collectionView.scrollToItem(at: indexPath, at: .centeredHorizontally, animated: true)
        }
    }
    
    /// Animate hiding the carousel
    func animateHideCarousel() {
        let startingHeight = frame.height
        
        UIView.animate(
            withDuration: Constants.animationDuration,
            animations: {
                self.transform = CGAffineTransform(scaleX: 1, y: 0)
                self.alpha = 0
            },
            completion: { _ in
                self.isHidden = true
                self.adListener?.bannerViewDidDismissScreen?(self.bannerViews.first)
            }
        )
    }
    
    /// Enable or disable auto-scrolling
    func setAutoScrollEnabled(_ enabled: Bool) {
        isAutoScrollEnabled = enabled
        if enabled {
            startAutoScroll()
        } else {
            stopAutoScroll()
        }
    }
    
    /// Set the delay between auto-scrolling transitions
    func setAutoScrollDelay(_ delaySeconds: TimeInterval) {
        autoScrollDelayMs = delaySeconds
        resetAutoScroll()
    }
    
    /// Enable or disable endless scrolling
    func setEndlessScrolling(_ enabled: Bool) {
        if endlessScrolling != enabled {
            endlessScrolling = enabled
            collectionView.reloadData()
        }
    }
    
    /// Get the number of banner ads
    func getBannerCount() -> Int {
        return bannerViews.count
    }
    
    // MARK: - Lifecycle
    deinit {
        stopAutoScroll()
    }
}

// MARK: - UICollectionViewDataSource
extension BannerAdsCarousel: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if endlessScrolling && bannerViews.count > 1 {
            return Int.max
        } else {
            return bannerViews.count
        }
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: BannerAdCollectionViewCell.identifier, for: indexPath) as! BannerAdCollectionViewCell

        let realPosition = endlessScrolling ? indexPath.item % bannerViews.count : indexPath.item

        if realPosition < bannerViews.count {
            let bannerView = bannerViews[realPosition]
            cell.configure(with: bannerView)
        }

        return cell
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension BannerAdsCarousel: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return collectionView.bounds.size
    }
}

// MARK: - UIScrollViewDelegate
extension BannerAdsCarousel: UIScrollViewDelegate {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        let pageWidth = scrollView.frame.width
        let currentPage = Int((scrollView.contentOffset.x + pageWidth / 2) / pageWidth)

        if currentPage != currentItem {
            currentItem = currentPage
        }
    }

    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        stopAutoScroll()
    }

    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if !decelerate {
            resetAutoScroll()
        }
    }

    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        resetAutoScroll()
    }
}

// MARK: - Helper Extensions
private extension UIView {
    func findViewController() -> UIViewController? {
        if let nextResponder = self.next as? UIViewController {
            return nextResponder
        } else if let nextResponder = self.next as? UIView {
            return nextResponder.findViewController()
        } else {
            return nil
        }
    }
}

// MARK: - BannerAdCollectionViewCell
private class BannerAdCollectionViewCell: UICollectionViewCell {
    static let identifier = "BannerAdCollectionViewCell"

    private var bannerView: GADBannerView?

    override func prepareForReuse() {
        super.prepareForReuse()
        bannerView?.removeFromSuperview()
        bannerView = nil
    }

    func configure(with bannerView: GADBannerView) {
        // Remove from previous parent if necessary
        bannerView.removeFromSuperview()

        self.bannerView = bannerView
        contentView.addSubview(bannerView)

        bannerView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            bannerView.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            bannerView.centerYAnchor.constraint(equalTo: contentView.centerYAnchor),
            bannerView.widthAnchor.constraint(lessThanOrEqualTo: contentView.widthAnchor),
            bannerView.heightAnchor.constraint(lessThanOrEqualTo: contentView.heightAnchor)
        ])
    }
}

// MARK: - BannerAdDelegate
private class BannerAdDelegate: NSObject, GADBannerViewDelegate {
    private weak var originalDelegate: GADBannerViewDelegate?
    private let onAdLoaded: () -> Void

    init(originalDelegate: GADBannerViewDelegate?, onAdLoaded: @escaping () -> Void) {
        self.originalDelegate = originalDelegate
        self.onAdLoaded = onAdLoaded
        super.init()
    }

    func bannerViewDidReceiveAd(_ bannerView: GADBannerView) {
        originalDelegate?.bannerViewDidReceiveAd?(bannerView)
        onAdLoaded()
    }

    func bannerView(_ bannerView: GADBannerView, didFailToReceiveAdWithError error: Error) {
        originalDelegate?.bannerView?(bannerView, didFailToReceiveAdWithError: error)
    }

    func bannerViewDidRecordImpression(_ bannerView: GADBannerView) {
        originalDelegate?.bannerViewDidRecordImpression?(bannerView)
    }

    func bannerViewWillPresentScreen(_ bannerView: GADBannerView) {
        originalDelegate?.bannerViewWillPresentScreen?(bannerView)
    }

    func bannerViewWillDismissScreen(_ bannerView: GADBannerView) {
        originalDelegate?.bannerViewWillDismissScreen?(bannerView)
    }

    func bannerViewDidDismissScreen(_ bannerView: GADBannerView) {
        originalDelegate?.bannerViewDidDismissScreen?(bannerView)
    }
}
