//
//  MayaButton.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 2/10/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import Foundation
import UIKit

/// Custom Button
/// IMPORTANT! ! To get the desired UI behavior of this custom button, set the button's `Style` to `default` from the storyboard.
/// `mayaThemeButtonColor`s value by default is set to 0, for primary color. You may also change this from the storyboard
/// `mayaThemeButtonSize`s value by default is set to 0, for large size. You may also change this from the storyboard

/// Distance between image and text
private let padding: CGFloat = 8

@IBDesignable
class MayaButton: UIButton {
    // MARK: - Interface Builder Inspectables
    @IBInspectable var borderWidth: Float = 0.0 {
        didSet {
            self.layer.borderWidth = CGFloat(borderWidth)
        }
    }

    @IBInspectable var borderRadius: Float = 16.0 {
        didSet {
            self.layer.cornerRadius = CGFloat(borderRadius)
        }
    }

    @IBInspectable var borderColor: UIColor = UIColor.clear {
        didSet {
            self.layer.borderColor = borderColor.cgColor
        }
    }

    @IBInspectable var isRoundedCorners: Bool = false

    /// The left image on the button
    @IBInspectable var leftHandImage: UIImage? {
        didSet {
            leftImageView?.image = leftHandImage
        }
    }

    /// The right image on the button
    @IBInspectable var rightHandImage: UIImage? {
        didSet {
            rightImageView?.image = rightHandImage
        }
    }

    /// Pre-defined button colors in Figma (Maya 2.0)
    /// Values are as follows:
    /// 0 - primary
    /// 1 - secondary
    /// 2 - grey
    /// 3 - black
    /// 4 - white
    /// any other value: primary
    @IBInspectable var mayaThemeButtonColor: Int = 0 {
        didSet {
            switch mayaThemeButtonColor {
            case 0: self.buttonColor = MayaTheme.ButtonColor.primary
            case 1: self.buttonColor = MayaTheme.ButtonColor.secondary
            case 2: self.buttonColor = MayaTheme.ButtonColor.grey
            case 3: self.buttonColor = MayaTheme.ButtonColor.black
            case 4: self.buttonColor = MayaTheme.ButtonColor.white
            case 5: self.buttonColor = MayaTheme.ButtonColor.mintGreen
            case 6: self.buttonColor = MayaTheme.ButtonColor.charcoalBlack
            default: self.buttonColor = MayaTheme.ButtonColor.primary
            }
            setupMayaTheme()
        }
    }

    /// Pre-defined button sizes in Figma (Maya 2.0)
    /// Values are as follows:
    /// 0 - large
    /// 1 - medium
    /// 2 - small
    /// any other value: large
    /// Note that this will only change the button's titleLabel' font size. You will still need to set the frame or constraints.
    @IBInspectable var mayaThemeButtonSize: Int = 0 {
        didSet {
            switch mayaThemeButtonSize {
            case 0: self.buttonSize = MayaTheme.ButtonSize.large
            case 1: self.buttonSize = MayaTheme.ButtonSize.medium
            case 2: self.buttonSize = MayaTheme.ButtonSize.small
            case 3: self.buttonSize = MayaTheme.ButtonSize.custom(font: CommonFontFamily.CerebriSansPro.bold.font(size: 16))
            default: self.buttonSize = MayaTheme.ButtonSize.large
            }
            setupMayaTheme()
        }
    }

    private weak var leftImageView: UIImageView?
    private weak var rightImageView: UIImageView?
    private let highlightDuration: TimeInterval = 0.25
    var buttonColor: MayaTheme.ButtonColor = .primary

    var buttonSize: MayaTheme.ButtonSize = .large {
        didSet {
            setupMayaTheme()
        }
    }

    private var originalBackgroundColor: UIColor?

    override var isHighlighted: Bool {
        didSet {
            if !oldValue && isHighlighted {
                highlight()
            } else if oldValue && !isHighlighted {
                unHighlight()
            }
        }
    }

    override var isEnabled: Bool {
        didSet {
            guard let theme = self.mayaTheme?.theme.getButtonTheme(forMayaThemeButtonColor: buttonColor) as? MayaThemeButtonBaseProtocol else { return }
            if !oldValue && isEnabled {
                self.backgroundColor = theme.buttonPrimaryBackgroundColor
                self.alpha = 1.0
            } else if oldValue && !isEnabled {
                updateDisplayForDisabledState()
            }
        }
    }

    override func awakeFromNib() {
        super.awakeFromNib()
        setupMayaButton()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupMayaButton()
    }
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupMayaButton()
    }

    private func setupMayaButton() {
        setupImages()
        setupMayaTheme()
        setupRoundedCorners()
        updateDisplayForDisabledState()
    }

    func setupMayaTheme() {
        mayaTheme?.applyTheme(to: self, color: buttonColor, size: buttonSize)
    }

    private func setupImages() {
        if let leftImage = leftHandImage,
            let titleLabel = titleLabel {
            let leftImageView = UIImageView(image: leftImage)
            leftImageView.translatesAutoresizingMaskIntoConstraints = false

            addSubview(leftImageView)
            NSLayoutConstraint.activate([leftImageView.centerYAnchor.constraint(equalTo: titleLabel.centerYAnchor),
                                         titleLabel.leadingAnchor.constraint(equalTo: leftImageView.trailingAnchor, constant: padding)])
            self.leftImageView = leftImageView
        }

        if let rightImage = rightHandImage,
           let titleLabel = titleLabel {
            let rightImageView = UIImageView(image: rightImage)
            rightImageView.translatesAutoresizingMaskIntoConstraints = false

            addSubview(rightImageView)
            NSLayoutConstraint.activate([rightImageView.centerYAnchor.constraint(equalTo: titleLabel.centerYAnchor),
                                         rightImageView.leadingAnchor.constraint(equalTo: titleLabel.trailingAnchor, constant: padding)])
            self.rightImageView = rightImageView
        }

        if let leftImage = leftHandImage, rightHandImage == nil {
            let originalTitleInsets = titleEdgeInsets
            let offset = (leftImage.size.width + padding) / 2
            titleEdgeInsets = UIEdgeInsets(top: originalTitleInsets.top, left: originalTitleInsets.left + offset, bottom: originalTitleInsets.bottom, right: originalTitleInsets.right - offset)
        } else if let rightImage = rightHandImage, leftHandImage == nil {
            let originalTitleInsets = titleEdgeInsets
            let offset = (rightImage.size.width + padding) / 2
            titleEdgeInsets = UIEdgeInsets(top: originalTitleInsets.top, left: originalTitleInsets.left - offset, bottom: originalTitleInsets.bottom, right: originalTitleInsets.right + offset)
        }
    }

    private func setupRoundedCorners() {
        guard isRoundedCorners else { return }
        layer.cornerRadius = frame.height / 2
        layer.masksToBounds = true
    }

    private func highlight() {
        let alpha = 0.50
        self.originalBackgroundColor = self.backgroundColor
        var highlightColor: UIColor? = self.originalBackgroundColor?.withAlphaComponent(0.88)
        if let theme = self.mayaTheme?.theme.getButtonTheme(forMayaThemeButtonColor: buttonColor) as? MayaThemeButtonBaseProtocol {
            highlightColor = theme.buttonBackgroundHightlightColor
        }
        UIView.animate(withDuration: highlightDuration) {
            self.backgroundColor = highlightColor
            self.leftImageView?.alpha = alpha
            self.rightImageView?.alpha = alpha
            self.titleLabel?.textColor = .white
        }
    }

    private func unHighlight() {
        let alpha: CGFloat = 1
        UIView.animate(withDuration: highlightDuration) {
            self.backgroundColor = self.originalBackgroundColor
            self.leftImageView?.alpha = alpha
            self.rightImageView?.alpha = alpha
            self.titleLabel?.textColor = .white
        }
    }

    private func updateDisplayForDisabledState() {
        guard let theme = self.mayaTheme?.theme.getButtonTheme(forMayaThemeButtonColor: buttonColor) as? MayaThemeButtonBaseProtocol, !isEnabled else { return }
        self.backgroundColor = theme.buttonBackgroundDisabledColor
        if let alpha = theme.buttonBackgroundDisabledAlpha {
            self.alpha = alpha
        }
    }
}

extension MayaButton: MayaThemeable {
    var mayaTheme: MayaTheme? { return .maya }
}

class AdaptableSizeMayaButton: MayaButton {
    override var intrinsicContentSize: CGSize {
        let labelSize = titleLabel?.sizeThatFits(CGSize(width: frame.size.width, height: CGFloat.greatestFiniteMagnitude)) ?? .zero
        let leftImageSize: CGFloat = leftHandImage != nil ? leftHandImage!.size.width + padding : 0
        let rightImageSize: CGFloat = rightHandImage != nil ? rightHandImage!.size.width + padding : 0
        let desiredButtonSize = CGSize(width: labelSize.width + titleEdgeInsets.left + titleEdgeInsets.right + leftImageSize + rightImageSize + (padding * 2), height: labelSize.height + titleEdgeInsets.top + titleEdgeInsets.bottom)

        return desiredButtonSize
    }
}
