//
//  MayaHelpCenterCardView.swift
//  PayMaya
//
//  Created by <PERSON> on 7/15/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import Foundation
import Lottie
import UIKit

protocol MayaHelpCenterCardViewDelegate: AnyObject {
    func didRequestWebView(_ helpCenterCardView: MayaHelpCenterCardView, with url: URL)
}

class MayaHelpCenterCardView: View, NibLoadable {
    @IBOutlet weak var contentView: UIView!
    @IBOutlet weak var emojiLabel: UILabel!
    @IBOutlet weak var helpCenterSpielTextView: UITextView!

    var web: Constants.WebView?
    weak var delegate: MayaHelpCenterCardViewDelegate?

    init() {
        super.init(frame: .zero)
        setup()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setup()
    }

    func setup() {
        loadFromNib()
        setupView()
    }

    func setupView() {
        addSubview(contentView)
        contentView.frame = self.bounds
        contentView.autoresizingMask = [.flexibleHeight, .flexibleWidth]
    }

    func setProperties(web: Constants.WebView, with delegate: MayaHelpCenterCardViewDelegate?) {
        self.web = web
        self.delegate = delegate

        let visitHelpCenterSpiel = L10n.Maya.HelpCenter.ReachHelpCenter.spiel
        let helpCenterSpiel = L10n.Authenticate.Help.Center.spiel
        let helpCenterRange = (visitHelpCenterSpiel as NSString).range(of: helpCenterSpiel)
        let attributedString: NSMutableAttributedString

        attributedString = NSMutableAttributedString(string: visitHelpCenterSpiel, attributes: [
            .font: CommonFontFamily.CerebriSansPro.regular.font(size: 14)!,
            .foregroundColor: CommonAsset.MayaColors.Content.contentGrey5.color
        ])
        attributedString.addAttribute(.foregroundColor, value: CommonAsset.MayaColors.Content.contentPrimaryGreen.color, range: helpCenterRange)
        helpCenterSpielTextView.attributedText = attributedString

        let tapGestureRecognizer = UITapGestureRecognizer(target: self, action: #selector(didTapHelpCenterCard(_:)))
        addGestureRecognizer(tapGestureRecognizer)
    }
}

extension MayaHelpCenterCardView {
    @objc func didTapHelpCenterCard(_ sender: Any) {
        if let url = web?.url, url.absoluteString == web?.url?.absoluteString {
            delegate?.didRequestWebView(self, with: url)
        }
    }
}
