//
//  MayaAdBannerCollectionViewCell.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 3/23/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation

class MayaAdBannerCollectionViewCell: UICollectionViewCell {
    @IBOutlet weak var bannerImageView: UIImageView!
    @IBOutlet weak var bannerView: View!
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var messageLabel: UILabel!
    @IBOutlet weak var textContentView: UIView!

    var errorImage: UIImage?

    override func awakeFromNib() {
        super.awakeFromNib()
        isAccessibilityElement = false
        accessibilityElements = [titleLabel!, messageLabel!]
        titleLabel.isAccessibilityElement = true
        messageLabel.isAccessibilityElement = true
        titleLabel.accessibilityIdentifier = "pmios_banner_title_label"
        messageLabel.accessibilityIdentifier = "pmios_banner_message_label"
    }

    override func prepareForReuse() {
        super.prepareForReuse()

        errorImage = nil
    }

    func bind(_ model: AdBanner) {
        if let imageURLString = model.imageURLString {
            textContentView.isHidden = true
            messageLabel.isHidden = true
            bannerImageView.image = nil
            contentView.showAnimatedGradientSkeleton()
            bannerImageView.sd_setImage(with: URL(string: imageURLString), completed: { [weak self] _, error, _, _ in
                guard let self else { return }
                contentView.hideSkeleton()
                if let text = model.text, !text.trim().isEmpty {
                    textContentView.isHidden = false
                    titleLabel.text = text
                } else {
                    textContentView.isHidden = true
                }

                if let message = model.message, !message.trim().isEmpty {
                    messageLabel.isHidden = false
                    messageLabel.text = message
                }
                if let errorImage, error != nil {
                    bannerImageView.image = errorImage
                }
            })
        }
    }
}
