//
//  MayaServiceBannerTableViewCell.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 6/17/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

class MayaServiceBannerTableViewCell: UITableViewCell {
    @IBOutlet weak var containerView: UIView!

    weak var adCarouselView: UIView?

    func addAdCarouselView(adCarouselView: UIView) {
        guard adCarouselView != self.adCarouselView else { return }

        self.adCarouselView = adCarouselView
        containerView.addSubview(adCarouselView)
        adCarouselView.translatesAutoresizingMaskIntoConstraints = false
        let constraints = [
            adCarouselView.topAnchor.constraint(equalTo: containerView.topAnchor),
            adCarouselView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor),
            adCarouselView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            adCarouselView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor)
        ]
        NSLayoutConstraint.activate(constraints)
    }
}
