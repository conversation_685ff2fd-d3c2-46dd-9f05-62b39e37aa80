//
//  MayaServiceCategoryTableViewCell.swift
//  PayMaya
//
//  Created by <PERSON> on 3/7/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import ConfigurationService
import Injector
import ReactiveCocoa
import ReactiveSwift
import UIKit

protocol MayaServiceCategoryTableViewCellDelegate: AnyObject {
    func didTapService(_ tableViewCell: MayaServiceCategoryTableViewCell, service: Service)
    func additionalAnalyticsAttributes(_ tableViewCell: MayaServiceCategoryTableViewCell) -> [AnalyticsAttributeKey: Any]
}

private typealias CellConstants = MayaServiceCellConstants.Category

class MayaServiceCategoryTableViewCell: UITableViewCell, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    @IBOutlet weak var categoryBackgroundView: View!
    @IBOutlet weak var categoryTitleLabel: UILabel!
    @IBOutlet weak var servicesCollectionView: UICollectionView!
    @IBOutlet weak var collectionViewHeightConstraint: NSLayoutConstraint!

    private var viewModel: MayaServiceCategoryViewModel?
    weak var delegate: MayaServiceCategoryTableViewCellDelegate?

    override func awakeFromNib() {
        super.awakeFromNib()
        servicesCollectionView.registerCell(of: MayaServiceCollectionViewCell.self)
        servicesCollectionView.layer.masksToBounds = false
    }

    func bind(_ viewModel: MayaServiceCategoryViewModel) {
        if let viewModel = self.viewModel {
            servicesCollectionView.reactive.reloadData <~ viewModel.servicesProperty.map { _ in }
        }

        categoryTitleLabel.text = viewModel.title
        categoryBackgroundView.backgroundColor = viewModel.backgroundColor

        guard self.viewModel != viewModel else { return }
        self.viewModel = viewModel
        self.contentView.layoutIfNeeded()
        servicesCollectionView.reloadData()
    }

    func updateHeight(_ height: CGFloat) {
        guard collectionViewHeightConstraint.constant != height else { return }

        collectionViewHeightConstraint.constant = height
        servicesCollectionView.collectionViewLayout.invalidateLayout()
    }
}

// MARK: - UICollectionViewDelegate, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout Methods
extension MayaServiceCategoryTableViewCell: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        guard let viewModel = viewModel else { return 0 }
        return viewModel.servicesProperty.value.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let viewModel = viewModel,
            let service = viewModel.servicesProperty.value[safeIndex: indexPath.row] else {
            return UICollectionViewCell()
        }
        let cell = collectionView.dequeueCell(of: MayaServiceCollectionViewCell.self, for: indexPath)

        let dataModel = MayaServiceCellDataModel(service: service, style: .category)
        cell.bind(dataModel)

        return cell
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        guard let viewModel = viewModel,
            let service = viewModel.servicesProperty.value[safeIndex: indexPath.row] else {
            return
        }

        // position and positionWidget both start at index 1
        var attributes: [AnalyticsAttributeKey: Any] = [
            .tile: service.id,
            .module: AnalyticsConstants.Module.servicesDashboard.rawValue,
            .category: viewModel.categoryID,
            .position: indexPath.row + 1,
            .positionWidget: viewModel.categoryRow + 1
        ]
        if let additionalAttributes = delegate?.additionalAnalyticsAttributes(self) {
            attributes.merge(with: additionalAttributes)
        }

        attributes[.status] = service.dynamicStatusType.analyticsValue
        analyticsService.logMayaEvents(action: .tap(Analytics.Services.tile), keyAttributes: attributes)
        delegate?.didTapService(self, service: service)
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CellConstants.collectionViewProperties.computedSize(maximumWidth: CellConstants.width, targetHeight: CellConstants.height)
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return CellConstants.collectionViewProperties.minimumSpacing
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return CellConstants.collectionViewProperties.interItemSpacing(maximumWidth: CellConstants.width)
    }
}
