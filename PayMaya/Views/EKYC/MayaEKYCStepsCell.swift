//
//  MayaEKYCStepsCell.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 6/3/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

//
//  MayaBenefitCell.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 18/07/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import SDWebImage
import UIKit

class MayaEKYCStepsCell: UICollectionViewCell {
    @IBOutlet weak var iconImageView: UIImageView!
    @IBOutlet weak var stepNumberView: UIView!
    @IBOutlet weak var stepNumberLabel: UILabel!
    @IBOutlet weak var stepDescription: UITextView!

    func configure(with model: KYCData.SampleImage, step: Int) {
        // MARK: TODO - Uncomment when images are uploaded to S3
//         let placeholderIcon = Asset.MayaImages.Ekyc.ekycStep1
//        iconImageView.sd_setImage(with: URL(string: model.iconUrl), placeholderImage: placeholderIcon)
        stepNumberView.layer.cornerRadius = 10
        stepNumberView.clipsToBounds = true
        stepNumberView.backgroundColor = CommonAsset.Colors.Constants._00A651.color
        stepNumberLabel.font = CommonFontFamily.CerebriSansPro.regular.font(size: 13)
        stepNumberLabel.textColor = CommonAsset.MayaColors.Primary.primaryWhite.color
        stepNumberLabel.text = String(step + 1)
        stepDescription.textContainer.lineFragmentPadding = 0
        stepDescription.textContainerInset = .zero
        stepDescription.contentInset = .zero
        stepDescription.linkTextAttributes = [.underlineStyle: 0,
                                              .underlineColor: UIColor.clear,
                                              .font: CommonFontFamily.CerebriSansPro.regular.font(size: 10)!,
                                              .foregroundColor: CommonAsset.Colors.Constants._00A651.color]
        stepDescription.attributedText = model.instruction?.getHTMLAttributedString(with: CommonFontFamily.CerebriSansPro.book.font(size: 12)!, color: CommonAsset.MayaColors.Primary.primaryBlack.color, shouldUseSystemFont: false)

        // MARK: TODO - Remove when images are uploaded to S3
        if step == 0 {
            iconImageView.image = Asset.MayaImages.Ekyc.ekycStep1.image
        } else if step == 1 {
            iconImageView.image = Asset.MayaImages.Ekyc.ekycStep2.image
        } else {
            iconImageView.image = Asset.MayaImages.Ekyc.ekycStep3.image
        }
    }
}
