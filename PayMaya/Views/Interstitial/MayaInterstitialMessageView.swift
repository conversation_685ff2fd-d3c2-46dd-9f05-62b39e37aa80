//
//  MayaInterstitialMessageView.swift
//  PayMaya
//
//  Created by <PERSON> on 7/7/23.
//  Copyright © 2023 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import UIKit

protocol MayaInterstitialMessageViewDelegate: AnyObject {
    func messageView(_ view: MayaInterstitialMessageView, didTapURL url: URL)
}

class MayaInterstitialMessageView: UIView, NibLoadable {
    @IBOutlet private weak var contentView: UIView!
    @IBOutlet private weak var stackView: UIStackView!
    @IBOutlet private weak var emojiLabel: UILabel!
    @IBOutlet private weak var messageTextView: UITextView!

    weak var delegate: MayaInterstitialMessageViewDelegate?

    init() {
        super.init(frame: .zero)
        setup()
    }

    required init(coder: NSCoder) {
        super.init(coder: coder)!
        setup()
    }

    func bind(message introMessage: MayaInterstitialMessage) {
        let text = introMessage.text
        let config = introMessage.config

        stackView.spacing = introMessage.config.spacing

        emojiLabel.text = text.emoji
        emojiLabel.font = CommonFontFamily.CerebriSansPro.book.font(size: config.textSize)

        if let message = text.message {
            bind(message: message, config: config)
        } else if let attributedMessage = text.attributedMessage {
            bind(attributedMessage: attributedMessage, config: config)
        }
    }
}

private extension MayaInterstitialMessageView {
    func bind(message: String, config: MayaInterstitialMessage.Config) {
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineHeightMultiple = config.lineHeightMultiple

        let attributes: [NSAttributedString.Key: Any] = [
            .foregroundColor: CommonAsset.MayaColors.Content.contentGrey6.color,
            .font: CommonFontFamily.CerebriSansPro.book.font(size: config.textSize)!
        ]

        let attributedMessage = NSMutableAttributedString(string: message, attributes: attributes)
        attributedMessage.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: message.count))

        messageTextView.attributedText = attributedMessage
    }

    func bind(attributedMessage: NSAttributedString, config: MayaInterstitialMessage.Config) {
        messageTextView.linkTextAttributes = [
            .foregroundColor: CommonAsset.MayaColors.Content.contentPrimaryGreen.color,
            .font: CommonFontFamily.CerebriSansPro.book.font(size: config.textSize)!
        ]
        messageTextView.attributedText = attributedMessage
    }
}

extension MayaInterstitialMessageView: UITextViewDelegate {
    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        delegate?.messageView(self, didTapURL: URL)
        return false
    }
}

private extension MayaInterstitialMessageView {
    func setup() {
        loadFromNib()
        setupView()
    }

    func setupView() {
        addSubview(contentView)
        contentView.frame = self.bounds
        contentView.autoresizingMask = [.flexibleHeight, .flexibleWidth]

        messageTextView.textContainerInset = .zero
        messageTextView.delegate = self
    }
}
