//
//  MayaProfileValidationFieldView.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 23/01/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import ReactiveSwift
import UIKit

/*
 Use this protocol for customizing validation field view upon change of input state
 */
protocol MayaProfileValidationFieldViewAccessoryViewDelegate: AnyObject {
    func didChangeInputState(_ view: MayaProfileValidationFieldView, state: MayaInputViewState)
}

extension MayaProfileValidationFieldViewAccessoryViewDelegate {
    /// Use this default delegate extension in case you want to reuse accessory view as error view
    func didChangeInputState(_ view: MayaProfileValidationFieldView, state: MayaInputViewState) {
        view.errorLabelContainerView.safeSetHidden(true)

        let toggleSubviews = {
            guard let accessoryView = view.accessoryView else { return }
            for view in accessoryView.subviews {
                if let label = view as? UILabel {
                    label.textColor = state.accessoryColor
                }
            }
        }

        UIView.animate(withDuration: 0.25) {
            view.mainView.borderColor = state.borderColor
            switch state {
            case .active:
                view.accessoryView?.safeSetHidden(false)
            case .inactive:
                view.accessoryView?.safeSetHidden(view.isAccessoryViewHiddenInitially)
            case .error:
                view.accessoryView?.safeSetHidden(false)
            default: break
            }
            toggleSubviews()
            view.layoutIfNeeded()
        }
    }
}

/**
 Almost the same usage as ValidationFieldView, but for now no different types. Use functions showPrefix() to show +63 or showChevron() for dropdowns.
 Recommended usage: insert this in a stack view and do not set height constraints. Let autolayout do the work
 */
@IBDesignable
class MayaProfileValidationFieldView: UIView, NibLoadable {
    private let disposeBag = CompositeDisposable()
    private(set) var isActionTriggeredOnce = false
    var isInitialState = true

    /// Set a view that appears at the bottom when `inputStateProperty` becomes `.active`
    /// Recommended way is to add a top-level view on the view controller scene, no hard height or width constraints
    /// Make sure to set vertical compression resistance priority of a variable height view such as a UILabel to Required(1000) to let autolayout magic do the work
    @IBOutlet weak var accessoryView: UIView? {
        didSet {
            guard let accessoryView = accessoryView else { return }
            bigStackView.addArrangedSubview(accessoryView)
            bigStackView.layoutIfNeeded()
            if !hasPersistentAccessoryView {
                accessoryView.isHidden = true
            }
        }
    }

    @IBOutlet weak var bigStackView: UIStackView!

    @IBOutlet weak var mainView: View!

    @IBOutlet weak var titleLabel: LabelWithHighlight!
    @IBOutlet weak var subtitleLabel: UILabel!
    @IBOutlet weak var flagImageView: UIImageView!
    @IBOutlet weak var prefixLabel: UILabel!
    @IBOutlet weak var bottomErrorIconImageView: UIImageView!
    @IBOutlet weak var firstActionButton: UIButton!
    @IBOutlet weak var secondActionButton: UIButton!
    @IBOutlet weak var rightActionButtons: UIStackView!
    @IBOutlet weak var textField: TextField!

    @IBOutlet weak var textFieldDelegate: UITextFieldDelegate?

    @IBOutlet weak var errorLabelContainerView: UIStackView!
    @IBOutlet weak var errorLabel: UILabel!

    @IBInspectable var placeholderText: String? {
        didSet {
            textField.placeholder = placeholderText
            if let text = placeholderText {
                setAccessibilityIdentifiers(placeholder: text)
            }
        }
    }

    /// Set to true to use an error icon under the text field insted inside of it.
    @IBInspectable var useBottomErrorIconImageView: Bool = false {
        didSet {
            bottomErrorIconImageView.isHidden = !useBottomErrorIconImageView
        }
    }

    /// Set to true if accessory view only appears when text field is active
    @IBInspectable var isAccessoryViewHiddenInitially: Bool = false

    /// Set to true if accessory view should always show
    @IBInspectable var hasPersistentAccessoryView: Bool = false

    /// Set to true if text field should run validation upon end editing event
    @IBInspectable var shouldValidateOnEndEditing: Bool = false

    /// Set to true if text field is always valid on initial state
    @IBInspectable var isValidOnInitialState: Bool = false

    /// Set to false if text field should not validate on initial state
    @IBInspectable var shouldValidateOnInitialState: Bool = true

    /// Set to true if  copy paste is enabled on the textfield
    @IBInspectable var isCopyPasteEnabled: Bool = false {
        didSet {
            textField.copyPasteEnabled = isCopyPasteEnabled
        }
    }

    /// Set this delegate if you want to customize changing of states (i.e. reuse accessory view for error view)
    weak var accessoryViewDelegate: MayaProfileValidationFieldViewAccessoryViewDelegate?

    var fieldProperty: FieldPropertyProtocol?
    var isInlineErrorIconHidden: Bool?
    var isProgrammaticInput: Bool = false
    var passwordToggleActionHandler: (() -> Void)?

    let inputStateProperty: MutableProperty<MayaInputViewState> = MutableProperty(.inactive)
    private var isPasswordField = false
    private let tinFormatMaxLength = 15
    private let contactNumberMaxLength = 12
    private let monthlyIncomeMaxLength = 14
    private let mobileNumberMaxDigitLength = 10

    enum FieldType {
        case normal
        case tin
        case contactNumber
        case fullContactNumber
        case monthlyIncome
    }

    var fieldType: FieldType = .normal

    init() {
        super.init(frame: .zero)
        setup()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setup()
    }

    func setup() {
        loadFromNib()
        setupView()
        setupObservables()
    }

    fileprivate func setupView() {
        addSubview(bigStackView)
        bigStackView.translatesAutoresizingMaskIntoConstraints = false
        translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([bigStackView.widthAnchor.constraint(equalTo: widthAnchor),
                                     bigStackView.heightAnchor.constraint(equalTo: heightAnchor),
                                     bigStackView.centerXAnchor.constraint(equalTo: centerXAnchor),
                                     bigStackView.centerYAnchor.constraint(equalTo: centerYAnchor)])
        layoutIfNeeded()

        let tapGestureRecognizer = UITapGestureRecognizer.init(target: self, action: #selector(selectTextField))
        mainView.addGestureRecognizer(tapGestureRecognizer)

        mainView.borderColor = MayaInputViewState.inactive.borderColor
        if hasPersistentAccessoryView {
            accessoryView?.safeSetHidden(false)
        } else {
            accessoryView?.safeSetHidden(isAccessoryViewHiddenInitially)
        }
        errorLabelContainerView.safeSetHidden(true)
        flagImageView.safeSetHidden(true)
    }

    private func setupObservables() {
        inputStateProperty.producer
            .skipRepeats()
            .throttle(0.25, on: QueueScheduler.main)
            .startWithValues { [weak self] state in
                guard let self = self else { return }
                guard self.accessoryViewDelegate == nil else {
                    self.accessoryViewDelegate?.didChangeInputState(self, state: state)
                    return
                }

                UIView.animate(withDuration: 0.25) {
                    self.mainView.borderColor = state.borderColor

                        switch state {
                        case .active:
                            if !self.hasPersistentAccessoryView {
                                self.accessoryView?.safeSetHidden(false)
                            }
                            self.errorLabelContainerView.safeSetHidden(true)
                        case .inactive:
                            if !self.hasPersistentAccessoryView {
                                self.accessoryView?.safeSetHidden(self.isAccessoryViewHiddenInitially)
                            }
                            self.errorLabelContainerView.safeSetHidden(true)
                        case .error(let isActive):
                            if !self.isUserInteractionEnabled {
                                self.hideError()
                                self.layoutIfNeeded()
                                return
                            }
                            if !self.errorLabel.text!.isEmpty {
                                self.errorLabelContainerView.safeSetHidden(false)
                            }
                            if !self.hasPersistentAccessoryView {
                                self.accessoryView?.safeSetHidden(!isActive)
                            }
                        default: break
                        }
                        self.layoutIfNeeded()
                }
            }.addToDisposeBag(disposeBag)

        textField.reactive
            .mapControlEvents([.editingDidBegin]) { $0.text }
            .observeValues { [weak self] _ in
                guard let self, needsValidation() else { return }

                if case .error = self.inputStateProperty.value {
                    self.inputStateProperty.value = .error(isActive: true)
                } else {
                    self.inputStateProperty.value = .active
                }
            }?.addToDisposeBag(disposeBag)

        textField.reactive
            .mapControlEvents([.editingDidEnd]) { $0.text }
            .observeValues { [weak self] _ in
                guard let self = self else { return }

                if case .error = self.inputStateProperty.value {
                    self.inputStateProperty.value = .error(isActive: false)
                } else {
                    self.inputStateProperty.value = .inactive
                }
            }?.addToDisposeBag(disposeBag)
    }

    @IBAction private func didTapFirstActionButton(_ sender: UIButton) {
        // TODO: Add handling here
    }

    @IBAction private func didTapSecondActionButton(_ sender: UIButton) {
        if isPasswordField {
            textField.isSecureTextEntry.toggle()
            let image = textField.isSecureTextEntry ? CommonAsset.Images.Icons.iconEyeClosed.image : CommonAsset.Images.Icons.iconEyeOpen.image
            secondActionButton.setImage(image, for: .normal)
            passwordToggleActionHandler?()
        }
    }

    @objc private func selectTextField() {
        textField.becomeFirstResponder()
    }

    /// Execute validation of current `fieldProperty` value, then display error based on `validation` result value
    func actionTriggered() {
        guard let fieldProperty = fieldProperty else { return }

        if fieldProperty.validation.result.value.isInvalid {
            errorLabel.text = fieldProperty.validationMessage
            inputStateProperty.value = .error(isActive: self.textField.isEditing)
        } else {
            inputStateProperty.value = self.textField.isEditing ? .active : .inactive
        }

        isActionTriggeredOnce = true

        if let fieldProperty = fieldProperty as? NewFieldProperty, fieldProperty.inlineValidation == nil {
            fieldProperty.mutableProperty.signal
                .observe(on: UIScheduler())
                .observeValues { [weak self] _ in
                    guard let self = self else { return }
                    if self.isActionTriggeredOnce, fieldProperty.validation.result.value.isInvalid {
                        if !fieldProperty.inlineValidationMessage.isEmpty {
                            self.errorLabel.text = fieldProperty.inlineValidationMessage
                        }
                        if !fieldProperty.validationMessage.isEmpty {
                            self.errorLabel.text = fieldProperty.validationMessage
                        }
                        self.inputStateProperty.value = .error(isActive: self.textField.isEditing)
                    } else {
                        self.inputStateProperty.value = self.textField.isEditing ? .active : .inactive
                    }
                }?.addToDisposeBag(disposeBag)
        }
    }

    /// Setting accessibility identifiers for UI elements within the view for automation purposes
    func setAccessibilityIdentifiers(placeholder: String) {
        let placeholderArray = placeholder.components(separatedBy: CharacterSet.whitespaces)
        errorLabel.accessibilityIdentifier = "pmios_\(placeholderArray.joined(separator: "_").lowercased())_validation_field_message_label"
        prefixLabel.accessibilityIdentifier = "pmios_\(placeholderArray.joined(separator: "_").lowercased())_validation_field_prefix_label"
        textField.accessibilityIdentifier = "pmios_\(placeholderArray.joined(separator: "_").lowercased())_validation_text_field"
    }

    /// observe `fieldProperty` signal for value change during editing, then show Error message base on `inlineValidation`  result value
    /// - Parameter fieldProperty: FieldPropertyProtocol that values will be observed for inline validation
    func bindFieldProperty(_ fieldProperty: FieldPropertyProtocol) {
        self.fieldProperty = fieldProperty
        guard let fieldProperty = fieldProperty as? NewFieldProperty else { return }

        if let inlineValidation = fieldProperty.inlineValidation {
            fieldProperty.mutableProperty.signal
                .observe(on: UIScheduler())
                .observeValues { [weak self] text in
                    guard let self = self else { return }
                    if text.count > 0 {
                        self.isInitialState = false
                    }

                    if self.isValidOnInitialState, self.isInitialState {
                        self.isValidOnInitialState = false
                        self.inputStateProperty.value = self.textField.isEditing ? .active : .inactive
                        return
                    }

                    if inlineValidation.result.value.isInvalid && needsValidation() {
                        if !self.isUserInteractionEnabled {
                            self.hideError()
                            self.layoutIfNeeded()
                            return
                        }
                        if !fieldProperty.inlineValidationMessage.isEmpty {
                            self.errorLabel.text = fieldProperty.inlineValidationMessage
                        }
                        self.inputStateProperty.value = .error(isActive: self.textField.isEditing)
                    } else if self.isActionTriggeredOnce, fieldProperty.validation.result.value.isInvalid {
                        if !fieldProperty.validationMessage.isEmpty {
                            self.errorLabel.text = fieldProperty.validationMessage
                        }
                        self.inputStateProperty.value = .error(isActive: self.textField.isEditing)
                    } else {
                        self.inputStateProperty.value = self.textField.isEditing ? .active : .inactive
                    }
                }?.addToDisposeBag(disposeBag)
        }
    }

    /// Assigns text value and cursor location offset with respect to initial position provided that the field is an amount type field
    /// - Parameter text: the formatted amount text value to be displayed
    /// - Parameter offset: cursor location offset from initial position
    func setFormattedAmount(with text: String, offset: Int?) {
        textField.willChangeValue(forKey: "text")
        textField.text = text
        textField.didChangeValue(forKey: "text")

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            if let offset = offset,
               let newPosition = self.textField.position(from: self.textField.beginningOfDocument, offset: offset) {
                self.textField.selectedTextRange = self.textField.textRange(from: newPosition, to: newPosition)
            }
        }
    }

    /// Setting of textfield properties from main view
    /// - Parameter keyboardType: type of keyboard that will show when it becomes active
    /// - Parameter capitalizationType: type of capitalization during typing
    /// - Parameter isSecureTextEntry: setting this true makes typed characters masked during typing
    /// - Parameter copyPasteEnabled: allows the user to copy values from the field and paste values from clipboard
    func setTextFieldProperties(keyboardType: UIKeyboardType = .default, capitalizationType: UITextAutocapitalizationType = .none, isSecureTextEntry: Bool = false, copyPasteEnabled: Bool? = nil, smartQuotesType: UITextSmartQuotesType = .default, type: FieldType = .normal) {
        textField.keyboardType = keyboardType
        textField.autocapitalizationType = capitalizationType
        textField.isSecureTextEntry = isSecureTextEntry
        textField.copyPasteEnabled = copyPasteEnabled ?? isCopyPasteEnabled
        textField.smartQuotesType = smartQuotesType
        fieldType = type
    }

    func setPasswordFieldView() {
        isPasswordField = true
        textField.isSecureTextEntry = true
        rightActionButtons.isHidden = false
        secondActionButton.isHidden = false
        secondActionButton.setImage(CommonAsset.Images.Icons.iconEyeClosed.image, for: .normal)
    }

    func setPhoneNumberFieldView(showFlag: Bool = false) {
        prefixLabel.safeSetHidden(false)
        flagImageView.safeSetHidden(!showFlag)
    }

    func setupSubtitle(_ text: String) {
        subtitleLabel.isHidden = false
        subtitleLabel.text = text
    }

    func disableValidationSignal() {
        isActionTriggeredOnce = false
    }

    func hasValidationError() -> Bool {
        guard let fieldProperty = fieldProperty else { return false }

        return fieldProperty.validation.result.value.isInvalid
    }

    private func needsValidation() -> Bool {
        if shouldValidateOnInitialState {
            return true
        } else {
            return !isInitialState
        }
    }
}

// MARK: Update MayaProfileValidationFieldView
extension MayaProfileValidationFieldView {
    /// Sets the given value on fields view field property.
    /// - Parameter value: A value which will be set on a form field.
    func setValue(_ value: String?) {
        guard let value = value else { return }
        textField.text = value
        fieldProperty?.mutableProperty.value = value
    }

    func resetField() {
        isValidOnInitialState = true
        isInitialState = true
        setValue("")
    }

    func readOnly(_ readOnly: Bool) {
        shouldValidateOnEndEditing = !readOnly
        isUserInteractionEnabled = !readOnly
        textField.isUserInteractionEnabled = !readOnly
    }

    /// Show Custom inline error
    func showCustomError(message: String) {
        inputStateProperty.value = .error(isActive: textField.isEditing)
        errorLabel.text = message
        UIView.animate(withDuration: 0.25) {
            self.errorLabelContainerView.safeSetHidden(false)
        }
    }

    /// Hide Custom inline error
    func hideError() {
        UIView.animate(withDuration: 0.25) {
            self.errorLabelContainerView.safeSetHidden(true)
        }
    }

    /// Trim leading, trailing, and multiple consecutive space in between 2 strings
    func smartTrim() {
        // Trim leading and trailing spaces
        let trimmedText = textField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""

        // Replace multiple consecutive spaces with a single space
        let cleanedText = trimmedText.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)

        // Update the text field with the cleaned text
        setValue(cleanedText)
    }
}

// MARK: - UITextFieldDelegate Methods
extension MayaProfileValidationFieldView: UITextFieldDelegate {
    func textFieldShouldBeginEditing(_ textField: UITextField) -> Bool {
        return textFieldDelegate?.textFieldShouldBeginEditing?(textField) ?? true
    }

    func textFieldDidBeginEditing(_ textField: UITextField) {
        textFieldDelegate?.textFieldDidBeginEditing?(textField)
    }

    func textFieldShouldEndEditing(_ textField: UITextField) -> Bool {
        return textFieldDelegate?.textFieldShouldEndEditing?(textField) ?? true
    }

    func textFieldDidEndEditing(_ textField: UITextField) {
        smartTrim()

        if shouldValidateOnEndEditing {
            actionTriggered()
        }
        // Revert back isProgrammaticInput to false if user has already updated field to desired length
        if fieldType == .fullContactNumber && textField.text?.count ?? 0 < contactNumberMaxLength {
            isProgrammaticInput = false
        }

        textFieldDelegate?.textFieldDidEndEditing?(textField)
    }

    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        if textField.smartQuotesType == .no {
            let invalidCharacters = CharacterSet(charactersIn: "`‘’")
            if string.rangeOfCharacter(from: invalidCharacters) != nil {
                return false
            }
        }

        if self.textField.isPastingContent &&
            UIPasteboard.general.hasStrings {
            var newString: String?
            switch textField.keyboardType {
            case .phonePad:
                newString = UIPasteboard.general.string?.extractAllPhonePadCharacters()
            case .numberPad, .decimalPad:
                newString = UIPasteboard.general.string?.extractAllNumberPadChacters()
            default: break
            }

            if let newString = newString, !newString.isEmpty {
                if let textFieldDelegate = textFieldDelegate {
                    let shouldChangeCharacter = textFieldDelegate.textField?(textField, shouldChangeCharactersIn: range, replacementString: newString) ?? false
                    self.textField.isPastingContent = false
                    return shouldChangeCharacter
                } else {
                    textField.text = newString
                    self.textField.isPastingContent = false
                    return false
                }
            }
        }

        switch fieldType {
        case .tin:
            if range.location < tinFormatMaxLength {
                let textFieldText = textField.text as NSString?
                let value = textFieldText?.replacingCharacters(in: range, with: string) ?? ""
                textField.setText(to: value.formatTinNumber(every: 3, with: "-"), preservingCursor: true)
                fieldProperty?.mutableProperty.value = textField.text ?? String.empty
                return false
            }
            return range.location < tinFormatMaxLength
        case .contactNumber:
            return range.location < contactNumberMaxLength
        case .fullContactNumber:
            // if user enters contact number manually then restict to enter more than contactNumberMaxLength
            if !isProgrammaticInput {
                return range.location < mobileNumberMaxDigitLength
            }
            return true
        case .monthlyIncome:
            if range.location < monthlyIncomeMaxLength {
                return textFieldDelegate?.textField?(textField, shouldChangeCharactersIn: range, replacementString: string) ?? true
            }
            return range.location < monthlyIncomeMaxLength
        default:
            return textFieldDelegate?.textField?(textField, shouldChangeCharactersIn: range, replacementString: string) ?? true
        }
    }

    func textFieldShouldClear(_ textField: UITextField) -> Bool {
        return textFieldDelegate?.textFieldShouldClear?(textField) ?? true
    }

    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        return textFieldDelegate?.textFieldShouldReturn?(textField) ?? true
    }
}

extension UITextField {
    func setText(to newText: String, preservingCursor: Bool) {
        if preservingCursor {
            let cursorPosition = offset(from: beginningOfDocument, to: selectedTextRange!.start) + newText.count - (text?.count ?? 0)
            text = newText

            if let newPosition = self.position(from: beginningOfDocument, offset: cursorPosition) {
                selectedTextRange = textRange(from: newPosition, to: newPosition)
            }
        } else {
            text = newText
        }
    }
}
