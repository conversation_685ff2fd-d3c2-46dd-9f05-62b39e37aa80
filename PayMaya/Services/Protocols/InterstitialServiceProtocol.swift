//
//  InterstitialServiceProtocol.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 4/25/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//
protocol InterstitialServiceProtocol {
    /// Suspends interstitial notifications for a specific module
    /// - Parameter flow: The flow for which to suspend interstitial notifications
    func suspend(for flow: ModuleFlow)

    /// Resumes interstitial notifications for a specific module
    /// - Parameter flow: The flow for which to resume interstitial notifications
    /// - Note: Interstitials will only fully resume when all active flows have been completed
    func resume(for flow: ModuleFlow)
}
