//
//  PerformanceMonitoringServiceProtocol.swift
//  PayMaya
//
//  Created by <PERSON> on 3/13/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation

protocol PerformanceMonitoringServiceProtocol {
    func timeToDisplayStart(_ screenName: String)
    func timeToDisplayStop(_ screenName: String)
    func timeToFullyDisplayStart(_ screenName: String)
    func timeToFullyDisplayStop(_ screenName: String)
    func startCustomTrace(with traceName: String, attributes: [String: String])
    func endCustomTrace(with traceName: String)
    func set(value: Int64, for metric: String, under traceName: String)
}

extension PerformanceMonitoringServiceProtocol {
    func startCustomTrace(with traceName: String, attributes: [String: String] = [:]) {
        startCustomTrace(with: traceName, attributes: attributes)
    }
}
