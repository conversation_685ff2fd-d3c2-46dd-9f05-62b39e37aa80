//
//  FlutterEngineManager.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 3/16/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Flutter
import FlutterPluginRegistrant
import Foundation

enum FlutterModule: Equatable, CaseIterable, Hashable {
    case crypto
    case cryptoV2
    case savingsTab
    case savingFull
    case securityDeposit
    case setUsername
    case checkout
    case funds
    case bnplTab
    case bnplFull
    case cardsTab
    case cardsFull
    case autoCashIn
    case inbox
    case inboxManage
    case loansCreditCard
    case loadShop
    case restrictedLogin
    case creditScoring
    case incomeDocumentation
    case cardPull
    case securityCenter
    case purchaseFinancing
    case flutterInterstitial
    case autoDebitArrangement

    var engineName: String {
        switch self {
        case .crypto: return Constants.Flutter.Crypto.engineName
        case .cryptoV2: return Constants.Flutter.CryptoV2.engineName
        case .savingsTab: return Constants.Flutter.Savings.tabEngineName
        case .savingFull: return Constants.Flutter.Savings.fullScreenEngineName
        case .securityDeposit: return Constants.Flutter.SecurityDeposit.engineName
        case .setUsername: return Constants.Flutter.SetUsername.fullScreenEngineName
        case .checkout: return Constants.Flutter.Checkout.engineName
        case .funds: return Constants.Flutter.Funds.engineName
        case .bnplTab: return Constants.Flutter.BNPL.tabEngineName
        case .bnplFull: return Constants.Flutter.BNPL.fullScreenEngineName
        case .cardsTab: return Constants.Flutter.Cards.tabEngineName
        case .cardsFull: return Constants.Flutter.Cards.fullScreenEngineName
        case .autoCashIn: return Constants.Flutter.AutoCashIn.fullScreenEngineName
        case .inbox: return Constants.Flutter.Inbox.fullScreenEngineName
        case .inboxManage: return Constants.Flutter.Inbox.manageNotificationsEngineName
        case .loansCreditCard: return Constants.Flutter.LoansCreditCard.engineName
        case .loadShop: return Constants.Flutter.Load.shopFullScreenEngineName
        case .restrictedLogin: return Constants.Flutter.RestrictedLogin.engineName
        case .creditScoring: return Constants.Flutter.CreditScoring.engineName
        case .incomeDocumentation: return Constants.Flutter.IncomeDocumentation.engineName
        case .cardPull: return Constants.Flutter.CardPull.fullScreenEngineName
        case .securityCenter: return Constants.Flutter.SecurityCenter.engineName
        case .purchaseFinancing: return Constants.Flutter.PurchaseFinancing.engineName
        case .flutterInterstitial: return Constants.Flutter.FlutterInterstitial.engineName
        case .autoDebitArrangement: return Constants.Flutter.AutoDebitArrangement.engineName
        }
    }

    var methodChannel: String {
        switch self {
        case .autoCashIn: return Constants.Flutter.AutoCashIn.channel
        case .crypto: return Constants.Flutter.Crypto.channel
        case .cryptoV2: return Constants.Flutter.CryptoV2.channel
        case .savingsTab, .savingFull: return Constants.Flutter.Savings.channel
        case .securityDeposit: return Constants.Flutter.SecurityDeposit.channel
        case .setUsername: return Constants.Flutter.SetUsername.channel
        case .checkout: return Constants.Flutter.Checkout.channel
        case .funds: return Constants.Flutter.Funds.channel
        case .bnplTab, .bnplFull: return Constants.Flutter.BNPL.channel
        case .cardsTab, .cardsFull: return  Constants.Flutter.Cards.channel
        case .inbox, .inboxManage: return Constants.Flutter.Inbox.channel
        case .loansCreditCard: return Constants.Flutter.LoansCreditCard.channel
        case .loadShop: return Constants.Flutter.Load.channel
        case .restrictedLogin: return Constants.Flutter.RestrictedLogin.channel
        case .creditScoring: return Constants.Flutter.CreditScoring.channel
        case .incomeDocumentation: return Constants.Flutter.IncomeDocumentation.channel
        case .cardPull: return Constants.Flutter.CardPull.channel
        case .securityCenter: return Constants.Flutter.SecurityCenter.channel
        case .purchaseFinancing: return Constants.Flutter.PurchaseFinancing.channel
        case .flutterInterstitial: return Constants.Flutter.FlutterInterstitial.channel
        case .autoDebitArrangement: return Constants.Flutter.AutoDebitArrangement.channel
        }
    }

    var entryPoint: String {
        switch self {
        case .autoCashIn: return Constants.Flutter.AutoCashIn.entryPoint
        case .crypto: return Constants.Flutter.Crypto.entryPoint
        case .cryptoV2: return Constants.Flutter.CryptoV2.entryPoint
        case .savingsTab, .savingFull: return Constants.Flutter.Savings.entryPoint
        case .securityDeposit: return Constants.Flutter.SecurityDeposit.entryPoint
        case .setUsername: return Constants.Flutter.SetUsername.entryPoint
        case .checkout: return Constants.Flutter.Checkout.entryPoint
        case .funds: return Constants.Flutter.Funds.entryPoint
        case .bnplTab, .bnplFull: return Constants.Flutter.BNPL.entryPoint
        case .cardsTab, .cardsFull: return Constants.Flutter.Cards.entryPoint
        case .inbox, .inboxManage: return Constants.Flutter.Inbox.fullScreenEntryPoint
        case .loansCreditCard: return Constants.Flutter.LoansCreditCard.entryPoint
        case .loadShop: return Constants.Flutter.Load.entryPoint
        case .restrictedLogin: return Constants.Flutter.RestrictedLogin.entryPoint
        case .creditScoring: return Constants.Flutter.CreditScoring.entryPoint
        case .incomeDocumentation: return Constants.Flutter.IncomeDocumentation.entryPoint
        case .cardPull: return Constants.Flutter.CardPull.entryPoint
        case .securityCenter: return Constants.Flutter.SecurityCenter.entryPoint
        case .purchaseFinancing: return Constants.Flutter.PurchaseFinancing.entryPoint
        case .flutterInterstitial: return Constants.Flutter.FlutterInterstitial.entryPoint
        case .autoDebitArrangement: return Constants.Flutter.AutoDebitArrangement.entryPoint
        }
    }

    var initialRoute: String? {
        switch self {
        case .autoCashIn: return Constants.Flutter.AutoCashIn.fullScreenEntryPointInitialRoute
        case .savingFull: return Constants.Flutter.Savings.fullScreenEntryPointInitialRoute
        case .checkout: return Constants.Flutter.Checkout.fullScreenEntryPointInitialRoute
        case .bnplTab: return Constants.Flutter.BNPL.tabScreenEntryPointInitialRoute
        case .bnplFull: return Constants.Flutter.BNPL.fullScreenEntryPointInitialRoute
        case .cardsFull: return Constants.Flutter.Cards.fullScreenEntryPointInitialRoute
        case .loansCreditCard: return Constants.Flutter.LoansCreditCard.fullScreenEntryPointInitialRoute
        case .loadShop: return Constants.Flutter.Load.fullScreenEntryPointInitialRoute
        case .cardPull: return Constants.Flutter.CardPull.fullScreenEntryPointInitialRoute
        case .autoDebitArrangement: return Constants.Flutter.CardPull.fullScreenEntryPointInitialRoute
        case .purchaseFinancing: return Constants.Flutter.PurchaseFinancing.fullScreenEntryPointInitialRoute
        default: return nil
        }
    }

    var libraryURI: String? {
        switch self {
        case .crypto: return Constants.Flutter.Crypto.libraryURI
        case .cryptoV2: return Constants.Flutter.CryptoV2.libraryURI
        case .checkout: return Constants.Flutter.Checkout.libraryURI
        default: return nil
        }
    }
}

/// Manages flutter engines used within the app
class FlutterEngineManager {
    private var engines: [FlutterModule: FlutterEngine] = [FlutterModule: FlutterEngine]()
    private var channels: [FlutterModule: FlutterMethodChannel] = [FlutterModule: FlutterMethodChannel]()
    private var isStarted: [FlutterModule: Bool] = [FlutterModule: Bool]()

    let responseGenerator = FlutterResponseGenerator()

    /// Initializes specified module's engine
    func initializeEngine(with module: FlutterModule) {
        guard engines[module] == nil else { return }
        engines[module] = FlutterEngine(name: module.engineName)
    }

    /// Utility method to initialize all engines specified (i.e. user logs in)
    func initializeEngines(with modules: [FlutterModule] = FlutterModule.allCases) {
        for module in modules {
            initializeEngine(with: module)
        }
    }

    /// Call to start speicifed engine before FlutterViewController will be presented
    func startEngine(with module: FlutterModule, customRoute: String? = nil) {
        if !(isStarted[module] ?? false) {
            isStarted[module] = true
            if let engine = engines[module] {
                let initialRoute = customRoute ?? module.initialRoute
                if initialRoute != nil {
                    engine.run(withEntrypoint: module.entryPoint, initialRoute: initialRoute)
                } else if let libraryURI = module.libraryURI {
                    engine.run(withEntrypoint: module.entryPoint, libraryURI: libraryURI)
                } else {
                    engine.run(withEntrypoint: module.entryPoint)
                }
                GeneratedPluginRegistrant.register(with: engine)
                channels[module] = FlutterMethodChannel(name: module.methodChannel, binaryMessenger: engine.binaryMessenger)
            }
        }
    }

    /// Gets corresponding engine of module
    func getEngine(with module: FlutterModule) -> FlutterEngine? {
        return engines[module]
    }

    /// Gets corresponding channel of module
    func getChannel(with module: FlutterModule) -> FlutterMethodChannel? {
        return channels[module]
    }

    /// Frees-up specified module's engine and channel
    func clearEngine(with module: FlutterModule) {
        engines[module] = nil
        channels[module] = nil
        isStarted[module] = nil
    }

    /// Utility method to free-up loaded engines specified (i.e. user signs out)
    func clearEngines(with modules: [FlutterModule] = FlutterModule.allCases) {
        for module in modules {
            clearEngine(with: module)
        }
    }
}
