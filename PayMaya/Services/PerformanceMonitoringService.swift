//
//  PerformanceMonitoringService.swift
//  PayMaya
//
//  Created by <PERSON> on 2/22/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import FirebasePerformance
import Foundation

class PerformanceMonitoringService: PerformanceMonitoringServiceProtocol {
    private var traces: [String: Trace] = [:]

    func timeToDisplayStart(_ screenName: String) {
        let customTraceName = getTraceName(of: screenName, key: "ttd")
        startCustomTrace(with: customTraceName)
    }

    func timeToDisplayStop(_ screenName: String) {
        let customTraceName = getTraceName(of: screenName, key: "ttd")
        endCustomTrace(with: customTraceName)
    }

    func timeToFullyDisplayStart(_ screenName: String) {
        let customTraceName = getTraceName(of: screenName, key: "ttfd")
        startCustomTrace(with: customTraceName)
    }

    func timeToFullyDisplayStop(_ screenName: String) {
        let customTraceName = getTraceName(of: screenName, key: "ttfd")
        endCustomTrace(with: customTraceName)
    }

    func startCustomTrace(with traceName: String, attributes: [String: String]) {
        let trace = Performance.startTrace(name: traceName)
        for (key, value) in attributes {
            trace?.setValue(value, forAttribute: key)
        }
        traces[traceName] = trace
    }

    func endCustomTrace(with traceName: String) {
        guard let trace = traces[traceName] else { return }
        trace.stop()
        traces.removeValue(forKey: traceName)
    }

    func set(value: Int64, for metric: String, under traceName: String) {
        guard let trace = traces[traceName] else { return }
        trace.setValue(value, forMetric: metric)
    }
}

extension PerformanceMonitoringService {
    private func getTraceName(of className: String, key: String) -> String {
        return className + "_" + key
    }
}
