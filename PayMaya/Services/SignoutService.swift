//
//  SignoutService.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 8/8/18.
//  Copyright © 2018 PayMaya Philippines, Inc. All rights reserved.
//

import APIProvider
import Combine
import ConfigurationService
import Error
import FirebaseAnalytics
import Foundation
import Injector
import MayaCoreData
import ReactiveSwift
import StoreProvider
import UIKit

private let timeoutDuration: TimeInterval = 5

class SignoutService {
    @Inject private var apiProvider: APIProvider
    @Inject private var storeProvider: StoreProvider
    @Inject private var authenticationProvider: AuthenticationProviderManager
    @Inject private var flutterEngineManager: FlutterEngineManager
    @Inject private var profileTrackingManager: ProfileTrackingManager
    @Inject private var configurationService: ConfigurationServiceV2

    // MARK: - Signal Producers
    func signout(msisdn: String? = nil) -> SignalProducer<Void, Error> {
        return deleteApnsTokenRequest()
            .flatMap(.latest) { _ in self.signoutRequest(msisdn: msisdn) }
            .flatMapError { _ in self.signoutRequest(msisdn: msisdn) }
    }

    func deleteApnsTokenRequest() -> SignalProducer<Void, Error> {
        guard let request = getApnsTokenRequest() else {
            return SignalProducer(value: ())
        }

        return apiProvider.reactive.request(request)
            .timeout(after: timeoutDuration, raising: Error(), on: QueueScheduler())
            .map { _ in }
    }

    func signoutRequest(msisdn: String?) -> SignalProducer<Void, Error> {
        return apiProvider.reactive.request(API.Session.signout).on(event: { [weak self] event in
            switch event {
            case .completed, .failed, .interrupted:
                self?.handleSignoutRequest(msisdn: msisdn)
            default:
                break
            }
        }).map { _ in }
    }

    // MARK: - Publishers
    func signoutPublisher(msisdn: String? = nil) -> AnyPublisher<Void, Error> {
        return deleteApnsTokenRequestPublisher()
            .flatMap { _ in self.signoutRequestPublisher(msisdn: msisdn) }
            .eraseToAnyPublisher()
    }

    func deleteApnsTokenRequestPublisher() -> AnyPublisher<Void, Error> {
        guard let request = getApnsTokenRequest() else {
            return Just(())
                .setFailureType(to: Error.self)
                .eraseToAnyPublisher()
        }

        return apiProvider.reactive.requestPublisher(request)
            .map { _ in () }
            .timeout(.seconds(timeoutDuration), scheduler: DispatchQueue.main)
            .eraseToAnyPublisher()
    }

    func signoutRequestPublisher(msisdn: String?) -> AnyPublisher<Void, Error> {
        return apiProvider.reactive.requestPublisher(API.Session.signout)
            .map { _ in () }
            .handleEvents(receiveCompletion: { [weak self] _ in
                self?.handleSignoutRequest(msisdn: msisdn)
            })
            .eraseToAnyPublisher()
    }
}

// MARK: - Private Methods
private extension SignoutService {
    func getApnsTokenRequest() -> APITargetType? {
        guard
            let encryptedDefaultsStore = storeProvider.target(EncryptedDefaultsStore.self),
            let apnsToken = encryptedDefaultsStore.read(EncryptedDefaultsStoreId.apnsToken, type: String.self).value
        else {
            return nil
        }

        return API.InboxNotification.deleteApnsToken(apnsToken)
    }

    func handleSignoutRequest(msisdn: String?) {
        guard
            let dbStore = storeProvider.target(DatabaseStore.self),
            let userDefaultsStore = storeProvider.target(UserDefaultsStore.self),
            let encryptedDefaultsStore = storeProvider.target(EncryptedDefaultsStore.self)
        else {
            return
        }

        savePreviouslyLoggedIn(msisdn: msisdn)
        profileTrackingManager.setUserProfile(nil, source: .signout)

        DispatchQueue.main.async {
            UIApplication.shared.unregisterForRemoteNotifications()
        }

        userDefaultsStore.resetTransientStore()
        userDefaultsStore.remove(UserDefaultsStoreId.restriction, completion: nil)
        encryptedDefaultsStore.remove(EncryptedDefaultsStoreId.tinboPromptShown, completion: nil)
        encryptedDefaultsStore.remove(EncryptedDefaultsStoreId.touchID, completion: nil)
        encryptedDefaultsStore.remove(EncryptedDefaultsStoreId.recentPurchase, completion: nil)
        encryptedDefaultsStore.remove(EncryptedDefaultsStoreId.recentlyPaidBillers, completion: nil)
        dbStore.resetTransientStore()
        dbStore.remove(DatabaseStore.ReadingOptions(DatabaseEntity.restriction), completion: nil)

        clearSession()
        clearWalletIdFromFirebaseAnalytics()
    }

    func savePreviouslyLoggedIn(msisdn: String? = nil) {
        guard
            let databaseStore = storeProvider.target(DatabaseStore.self),
            let encryptedStore = storeProvider.target(EncryptedDefaultsStore.self),
            let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User,
            let msisdn = msisdn ?? user.msisdn
        else {
            return
        }

        let previouslyLoggedInAccount = PreviousAccount(msisdn: msisdn)
        encryptedStore.write(previouslyLoggedInAccount, options: EncryptedDefaultsStoreId.previousAccount)
    }

    func clearSession() {
        authenticationProvider.removeCredential()
        saveTouchIdStatus(status: .touchIdNotSet)
        configurationService.destroyUserClient()
        #if !UNIT_TEST_TARGET
        analyticsHelper.clearGlimpseUserPayload()
        flutterEngineManager.clearEngines()
        #endif
    }

    func saveTouchIdStatus(status: Constants.Types.TouchIdStatus) {
        authenticationProvider.saveTouchIdStatus(status: status)
    }

    func clearWalletIdFromFirebaseAnalytics() {
        FirebaseAnalytics.Analytics.setUserID(nil)
    }
}

#if !UNIT_TEST_TARGET
extension SignoutService: AnalyticsProtocol {}
#endif
