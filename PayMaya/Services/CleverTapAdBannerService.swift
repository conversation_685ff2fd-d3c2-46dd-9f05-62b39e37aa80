//
//  CleverTapAdBannerService.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> Pa<PERSON> Barroga on 6/17/21.
//  Copyright © 2021 PayMaya Philippines, Inc. All rights reserved.
//

import ConfigurationService
import Foundation
import Injector
import ReactiveSwift

#if !UNIT_TEST_TARGET
import CleverTapSDK
#endif

class CleverTapAdBannerService: NSObject, AdBannerServiceProtocol {
    @Inject private var dependencySetupHelper: DependencySetupHelper

    var adBannersProperty: MutableProperty<[AdBanner]> = MutableProperty([AdBanner]())
    var rotationIntervalProperty = MutableProperty(Constants.CleverTapRotationIntervalDefault)

    var displayUnitID: String?

    func setDisplayUnitDelegate() {
        #if !UNIT_TEST_TARGET
        dependencySetupHelper.cleverTapInstance?.setDisplayUnitDelegate(self)
        #endif
    }

    func sendAnalyticsForClickedEvent() {
        guard let displayUnitID = displayUnitID else { return }
        #if !UNIT_TEST_TARGET
        dependencySetupHelper.cleverTapInstance?.recordDisplayUnitClickedEvent(forID: displayUnitID)
        #endif
    }
}

// MARK: - CleverTapDisplayUnitDelegate methods
#if !UNIT_TEST_TARGET
extension CleverTapAdBannerService: CleverTapDisplayUnitDelegate {
    func displayUnitsUpdated(_ displayUnits: [CleverTapDisplayUnit]) {
        guard !displayUnits.isEmpty, let displayUnit = displayUnits[safeIndex: 0], let contents = displayUnit.contents else {
            displayUnitID = nil
            adBannersProperty.value = []
            return
        }

        let screenName = displayUnit.customExtras?[Constants.CleverTapResponseKeys.screen.rawValue] as? String
        var banners: [AdBanner] = []

        if let displayUnitId = displayUnit.unitID {
            displayUnitID = displayUnitId
            dependencySetupHelper.cleverTapInstance?.recordDisplayUnitViewedEvent(forID: displayUnitId)
        }

        if ContainerWrapper.shared.resolve(ConfigurationServiceV2.self).cleverTapUnliBannersEnabled.value,
            let isUnliBannersString = displayUnit.customExtras?[Constants.CleverTapResponseKeys.unliBanners.rawValue] as? String,
            let isUnliBanners = Bool(isUnliBannersString),
            isUnliBanners {
            let duration = displayUnit.customExtras?[Constants.CleverTapResponseKeys.duration.rawValue]
            if let number = duration as? NSNumber {
                rotationIntervalProperty.value = TimeInterval(number.doubleValue)
            } else if let string = duration as? String, let intFromString = Int(string) {
                rotationIntervalProperty.value = TimeInterval(intFromString)
            }

            if let dictionary = displayUnit.customExtras as? [String: Any] {
                let parsedBanners = parseDictionaryToCleverTapUnliBannerResponseArray(dictionary: dictionary)
                let sortedBanners = parsedBanners.sorted()
                banners = sortedBanners.enumerated().map { index, banner in
                    let tag = index + 1
                    return AdBanner(imageURLString: banner.imageURL, text: banner.title ?? "", message: banner.message ?? "", actionURLString: banner.deeplink, tag: tag, screen: screenName, priority: banner.priority, name: banner.name, active: banner.active, bannerKey: banner.key)
                }
            }
        } else {
            for i in 0..<contents.count {
                let content = contents[i]
                if content.mediaIsImage {
                    let adBanner = AdBanner(imageURLString: content.mediaUrl, text: content.title, message: content.message, actionURLString: content.actionUrl, tag: i + 1, screen: screenName)
                    banners.append(adBanner)
                }
            }
        }

        adBannersProperty.value = banners
    }
}
#endif

private extension CleverTapAdBannerService {
    func parseDictionaryToCleverTapUnliBannerResponseArray(dictionary: [String: Any]) -> [CleverTapUnliBannerResponse] {
        let filtered = dictionary.filter { key, _ in
            if let keyString = key as? String {
                return keyString.hasPrefix(Constants.CleverTapResponseKeys.bannerKeyword.rawValue)
            }
            return false
        }

        var temporaryBanners: [CleverTapUnliBannerResponse] = []

        for (key, value) in filtered {
            if let jsonString = value as? String,
               let jsonData = jsonString.data(using: .utf8),
               var cleverTapBanner = try? JSONDecoder().decode(CleverTapUnliBannerResponse.self, from: jsonData),
               let imageURL = cleverTapBanner.imageURL,
               cleverTapBanner.active == true, !imageURL.isEmpty {
                cleverTapBanner.key = key as? String
                temporaryBanners.append(cleverTapBanner)
            }
        }
        return temporaryBanners
    }
}
