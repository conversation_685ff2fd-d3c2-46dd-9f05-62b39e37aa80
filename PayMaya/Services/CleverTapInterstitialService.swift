//
//  CleverTapInterstitialService.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 4/25/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//
#if !UNIT_TEST_TARGET
import CleverTapSDK
#endif
import ConfigurationService
import Injector

public class CleverTapInsterstitialService: InterstitialServiceProtocol {
    @Inject private var dependencySetupHelper: DependencySetupHelper
    @Inject private var configurationService: ConfigurationServiceV2

    private var activeFlows = Set<ModuleFlow>()

    #if !UNIT_TEST_TARGET
    private var cleverTapInstance: CleverTap {
        return dependencySetupHelper.cleverTapInstance
    }
    #endif

    private var isServiceEnabled: Bool {
        return configurationService.interstitialManualConfigServiceEnabled.value
    }

    func suspend(for flow: ModuleFlow) {
        guard isServiceEnabled else { return }
        activeFlows.insert(flow)
        #if !UNIT_TEST_TARGET
        cleverTapInstance.suspendInAppNotifications()
        #endif
    }

    func resume(for flow: ModuleFlow) {
        guard isServiceEnabled else { return }
        activeFlows.remove(flow)
        #if !UNIT_TEST_TARGET
        if activeFlows.isEmpty {
            cleverTapInstance.resumeInAppNotifications()
        }
        #endif
    }
}
