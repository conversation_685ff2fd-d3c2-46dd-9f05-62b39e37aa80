#!/usr/bin/env ruby

# References
# Settings bundle: https://developer.apple.com/library/archive/documentation/Cocoa/Conceptual/UserDefaults/Preferences/Preferences.html#//apple_ref/doc/uid/10000059i-CH6-SW5
# Settings plist values: https://developer.apple.com/library/archive/documentation/PreferenceSettings/Conceptual/SettingsApplicationSchemaReference/Articles/PSChildPaneSpecifier.html#//apple_ref/doc/uid/**********-SW1

require 'xcodeproj'

def add_firebase_debugview_toggle()
    dict = {}
    dict['Type'] = 'PSToggleSwitchSpecifier'
    dict['Title'] = 'Firebase DebugView'
    dict['Key'] = 'firebase_debug_view_enabled'
    dict['DefaultValue'] = false
    return dict
end

def add_debugswift_network_toggle()
    dict = {}
    dict['Type'] = 'PSToggleSwitchSpecifier'
    dict['Title'] = 'DebugSwift Network'
    dict['Key'] = 'debug_swift_network_enabled'
    dict['DefaultValue'] = false
    return dict
end

def add_mock_location_child()
    dict = {}
    dict['Type'] = 'PSChildPaneSpecifier'
    dict['Title'] = 'Mock Location'
    dict['File'] = 'MockLocation'
    return dict
end

def add_feature_flags_header()
    dict = {}
    dict['Type'] = 'PSGroupSpecifier'
    dict['Title'] = 'Feature Flags'
    return dict
end

def add_feature_flag_logs_toggle()
    dict = {}
    dict['Type'] = 'PSToggleSwitchSpecifier'
    dict['Title'] = 'Feature Flag Logs'
    dict['Key'] = 'configuration_service_logs_enabled'
    dict['DefaultValue'] = false
    return dict
end

def add_maintenance_toggles_child()
    dict = {}
    dict['Type'] = 'PSChildPaneSpecifier'
    dict['Title'] = 'Maintenance'
    dict['File'] = 'Maintenance'
    return dict
end

def add_feature_flags_v1_child()
    dict = {}
    dict['Type'] = 'PSChildPaneSpecifier'
    dict['Title'] = 'v1 (S3 + Remote Config)'
    dict['File'] = 'Toggles'
    return dict
end

def add_feature_flags_v2_child()
    dict = {}
    dict['Type'] = 'PSChildPaneSpecifier'
    dict['Title'] = 'v2 (Split)'
    dict['File'] = 'SplitToggles'
    return dict
end

def generate_main_dictionary(array)
    dict = {}
    dict['StringsTable'] = 'Root'
    dict['PreferenceSpecifiers'] = array
    return dict
end

# First and only argument is output file (for now Root.plist inside Settings.bundle)
def main
    array = []
    
    array << add_firebase_debugview_toggle()
    array << add_debugswift_network_toggle()
    array << add_mock_location_child()
    array << add_feature_flags_header()
    array << add_feature_flag_logs_toggle()
    array << add_maintenance_toggles_child()
    array << add_feature_flags_v1_child()
    array << add_feature_flags_v2_child()
    
    dict = generate_main_dictionary(array)
    Xcodeproj::Plist.write_to_path(dict, ARGV[0])
end

main

