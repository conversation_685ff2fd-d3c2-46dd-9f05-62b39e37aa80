#!/usr/bin/env ruby

require 'xcodeproj'

def add_header(team)
    dict = {}
    dict['Type'] = 'PSGroupSpecifier'
    dict['Title'] = team
    return dict
end

def add_bool_toggle(key, title)
    dict = {}
    dict['Type'] = 'PSToggleSwitchSpecifier'
    dict['Title'] = title
    dict['Key'] = key
    dict['DefaultValue'] = false
    return dict
end

def add_string_toggle(key, title)
    dict = {}
    dict['Type'] = 'PSTextFieldSpecifier'
    dict['Title'] = title
    dict['Key'] = key
    return dict
end

def generate_main_dictionary(array)
    dict = {}
    dict['StringsTable'] = 'Root'
    dict['PreferenceSpecifiers'] = array
    return dict
end

# First and only argument is output file (for now SplitToggles.plist inside Settings.bundle)
def main
    prefix = 'non_prod_'

    array = []
    previous_team = ''
    
    File.foreach(ARGV[0]) do |line|
        temp_line = line.strip
        split_line = temp_line.split(',')

        key = split_line[0]
        type = split_line[1]
        team = split_line[2]
        title = split_line[3]

        if team != previous_team
            array << add_header(team)
            previous_team = team
        end

        case type
        when 'string', 'integer', 'double', 'dictionary', 'object'
            array << add_string_toggle(key, title)
        when 'bool'
            array << add_bool_toggle(key, title)
        end
    end
    
    dict = generate_main_dictionary(array)
    Xcodeproj::Plist.write_to_path(dict, ARGV[1])
end

main

