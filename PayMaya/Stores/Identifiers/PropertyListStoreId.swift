//
//  PropertyListStoreIdentifier.swift
//  PayMaya
//
//  Created by <PERSON>az<PERSON>oga on 5/12/21.
//  Copyright © 2021 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation
import StoreProvider

enum PropertyListStoreId: String {
    case accountVerificationCustomerSupportURL = "Account Verification Customer Support URL"
    case appsFlyerAppID = "AppsFlyer App ID"
    case appsFlyerDevKey = "AppsFlyer Dev Key"
    case autoCashInURL = "Base Auto Cash In URL"
    case billspayUrl = "Base Pay Bills URL"
    case bspURL = "BSP URL"
    case cardUrl = "Base Card URL"
    case chatbotUrl = "Chatbot URL"
    case cleverTapAccountID = "CleverTap Account ID"
    case cleverTapRegion = "CleverTap Region"
    case cleverTapToken = "CleverTap Token"
    case clientId = "Client ID"
    case clientSecret = "Client Secret"
    case clientUrl = "Base Client URL"
    case configurationUrl = "Config URL"
    case deploymentId = "Salesforce Deployment ID"
    case donateUrl = "Donate URL"
    case fileClientAuthKey = "File API Auth Key"
    case fileClientUrl = "File API Client URL"
    case forgotPasswordSuccessUrl = "Forgot Password Success Base URL"
    case forgotPasswordUrl = "Forgot Password Base URL"
    case forgotPasswordV2Url = "Forgot Password V2 Base URL"
    case giphyAPIKey = "Giphy API Key"
    case glimpseClientID = "Glimpse Client ID"
    case glimpseClientSecret = "Glimpse Client Secret"
    case glimpseTrackingID = "Glimpse Tracking ID"
    case helpBaseUrl = "Help Base URL"
    case identityKey = "Identity"
    case insightsURL = "Insights URL"
    case issuingDomain = "Issuing Domain"
    case kountMerchantID = "Kount Merchant ID"
    case liveAgentPod = "Salesforce Live Agent Pod"
    case lookupUrl = "Lookup URL"
    case mayaBankBaseURL = "Maya Bank Base URL"
    case mayaBankWebViewBaseURL = "Mayabank Web View Base URL"
    case mayaCreditURL = "Base Maya Credit URL"
    case mayaMallURL = "Maya Mall URL"
    case mayaServiceAdvisoriesURL = "Maya Service Advisories URL"
    case mayaSupportBaseUrl = "Maya Support Base URL"
    case merchantAcquirerID = "Merchant Acquirer ID"
    case merchantGlobalIdentifier = "Merchant Global Identifier"
    case merchantPaymentType = "Merchant Payment Type"
    case merchantPaymentUrl = "Base Merchant Payment URL"
    case mfaBaseURL = "MFA Base URL"
    case mfaFaceAuthFormURL = "MFA Face Auth Form URL"
    case organizationId = "Salesforce Org ID"
    case palTPID = "PAL TPID"
    case pbbTPID = "PBB TPID"
    case pinningFingerprint = "Pinning Fingerprint"
    case profileServiceUrl = "Profile Service URL"
    case promotionsUrl = "Promotions URL"
    case protectServiceUrl = "Protect URL"
    case pushApprovalServiceUrl = "Push Approval Service URL"
    case rafflePromoTPID = "Raffle Promo TPID"
    case requestTokenSecret = "Request Token Secret"
    case securityCenterBaseURL = "Security Center Base URL"
    case shieldSecretKey = "Shield Secret Key"
    case shieldSiteID = "Shield Site ID"
    case shopUrl = "Base Shop URL"
    case shortenedKycToken = "Shortened KYC Token"
    case smartPadalaBaseUrl = "Smart Padala Base URL"
    case spki = "SPKI"
    case splitSDKKey = "Split SDK Key"
    case supportBaseUrl = "Support Base URL"
    case tinboBaseURL = "Tinbo Base URL"
    case tokenUrl = "Apigee Access Token URL"
    case updateProfileCMSKey = "Update Profile CMS Key"
    case updateProfileToken = "Update Profile Token"
    case updateProfileUrl = "Update Profile URL"
    case updaterUrl = "Updater URL"
    case userProfilesApiKey = "User Profiles API Key"
    case userProfilesUrl = "User Profiles URL"
    case webBaseUrl = "Web View Base URL"
    case zolozKycServiceUrl = "ZolozKYC Service URL"
    case zolozKycToken = "Zoloz KYC Token"
    case zolozKycUrl = "Base ZolozKYC Client URL"
    case inboxNotificationBaseUrl = "Inbox SDK Base URL"
    case blackpinkGiveawayTPID = "Blackpink Giveaway TPID"
}

extension PropertyListStoreId: StorableIdentifier {
    func value() -> String {
        return self.rawValue
    }
}
