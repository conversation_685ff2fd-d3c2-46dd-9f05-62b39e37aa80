//
//  UserDefaultsStoreIdentifier.swift
//  PayMaya
//
//  Created by <PERSON>az<PERSON>oga on 5/12/21.
//  Copyright © 2021 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation
import StoreProvider

/// Identifiers of the objects stored in `UserDefaultsStore`.
///
/// - Warning: Whenever a new ID is added, always remember to return the correct values for `storageMode` and `clearOnMigration`.
enum UserDefaultsStoreId: String, CaseIterable {
    case accountTypesEkycLastUpdatedOn = "account_types_last_updated_on"
    case accountUsagesLastUpdatedOn = "account_usages_last_updated_on"
    case actionCardsLastUpdatedOn = "action_cards_last_updated_on"
    case appConfigurationSettingsLastUpdatedOn = "application_configuration_settings_last_updated_on"
    case appStoreLink = "app_store_link"
    case banksLastUpdatedOn = "banks_last_updated_on"
    case barcodeScanInstructionShownForUser = "barcode_scan_instruction_shown"
    case blockReasonsLastUpdatedOn = "block_card_reasons_last_updated_on"
    case canLinkCard = "can_link_card"
    case categoriesLastUpdatedOn = "categories_last_updated_on"
    case configurationServiceLogsEnabled = "configuration_service_logs_enabled"
    case consents = "policy_consents"
    case countriesLastUpdatedOn = "countries_last_updated_on"
    case currentShopVersion = "current_shop_version"
    case debugSwiftNetworkEnabled = "debug_swift_network_enabled"
    case decorationsLastUpdatedOn = "decorations_last_updated_on"
    case dedupAlertShownForUser = "dedup_alert_shown"
    case eddSettingsLastUpdatedOn = "edd_settings_last_updated_on"
    case expandedReKYCAction = "expanded_rekyc_action"
    case fieldInformationLastUpdatedOn = "field_information_last_updated_on"
    case financialDocumentsLastUpdatedOn = "financial_documents_last_updated_on"
    case firebaseDebugViewEnabled = "firebase_debug_view_enabled"
    case formSeriesLastUpdatedOn = "form_series_last_updated_on"
    case fundsCreatorStoreInterstitialShown = "funds_creator_store_intro_shown"
    case globalRemittanceCreatorStoreInterstitialShown = "global_remittance_creator_store_intro_shown"
    case globalStocksCreatorStoreInterstitialShown = "global_stocks_creator_store_intro_shown"
    case hideBalanceOn = "hide_balance_on"
    case hideCreditBalanceOn = "hide_credit_balance_on"
    case identificationTypesLastUpdatedOn = "identification_types_last_updated_on"
    case imageQualityRetryCount = "imageQualityRetryCount"
    case incomeSourcesEkycLastUpdatedOn = "income_sources_ekyc_last_updated_on"
    case instructionalOverlayShownForUser = "instructional_overlay_shown"
    case insuranceCreatorStoreInterstitialShown = "insurance_creator_store_intro_shown"
    case kycEntryPoint = "kyc_entry_point"
    case latestReferralCodeToUse = "latest_referral_code_to_use"
    case loadUpCentersLastUpdatedOn = "load_up_centers_last_updated_on"
    case mayaIntroductionScreensShown = "maya_introduction_screens_shown"
    case nationalitiesLastUpdatedOn = "nationalities_last_updated_on"
    case onboardingBranchCode = "onboarding_branch_code"
    case onboardingEntryPoint = "onboarding_entry_point"
    case onboardingPartner = "onboarding_partner"
    case onlineMerchantsESimsLastUpdatedOn = "online_merchants_esims_last_updated_on"
    case onlineMerchantsFoodLastUpdatedOn = "online_merchants_food_last_updated_on"
    case onlineMerchantsFundsLastUpdatedOn = "online_merchants_funds_last_updated_on"
    case onlineMerchantsGlobalRemittanceLastUpdatedOn = "online_merchants_global_remittance_last_updated_on"
    case onlineMerchantsGlobalStocksLastUpdatedOn = "online_merchants_global_stocks_last_updated_on"
    case onlineMerchantsGovernmentLastUpdatedOn = "online_merchants_government_last_updated_on"
    case onlineMerchantsInsuranceLastUpdatedOn = "online_merchants_insurance_last_updated_on"
    case onlineMerchantsInvestLastUpdatedOn = "online_merchants_invest_last_updated_on"
    case onlineMerchantsLuckyGamesLastUpdatedOn = "online_merchants_lucky_games_last_updated_on"
    case onlineMerchantsPayLaterLastUpdatedOn = "online_merchants_paylater_last_updated_on"
    case onlineMerchantsRetailLastUpdatedOn = "online_merchants_retail_last_updated_on"
    case onlineMerchantsRewardsLastUpdatedOn = "online_merchants_rewards_last_updated_on"
    case onlineMerchantsSparkHackathonLastUpdatedOn = "online_merchants_spark_hackathon_last_updated_on"
    case onlineMerchantsStocksGameLastUpdatedOn = "online_merchants_stocks_game_last_updated_on"
    case onlineMerchantsStocksLastUpdatedOn = "online_merchants_stocks_last_updated_on"
    case onlineMerchantsStreamLastUpdatedOn = "online_merchants_stream_last_updated_on"
    case onlineMerchantsTransportLastUpdatedOn = "online_merchants_transport_last_updated_on"
    case philippineAirlinesMMAInterstitialShown = "philippine_airlines_mma_intro_shown"
    case policy = "privacy_policy"
    case previousBuildNumber = "previous_build_number"
    case privacyPolicyDisplayed = "privacy_policy_displayed"
    case providersLastUpdatedOn = "providers_last_updated_on"
    case providersPerCategoryLastUpdatedOn = "providers_per_category_last_updated_on"
    case reKYCInitialDateTime = "rekyc_initial_dateTime"
    case restriction = "restriction"
    case reviewLastDate = "review_last_date"
    case reviewVersionNumber = "review_version_number"
    case savingsAutoProvisionOptIn = "savings_auto_provision_opt_in"
    case savingsAutoProvisionOptInSent = "savings_auto_provision_opt_in_sent"
    case sendMoneyWizardShownForUser = "send_money_wizard_shown"
    case shopBuyAgainProductsLastUpdatedOn = "shop_buy_again_products_last_updated_on"
    case shopProductsLastUpdatedOn = "shop_products_last_updated_on"
    case shopPurchaseHistoryLastUpdatedOn = "shop_purchase_history_last_updated_on"
    case shopPurchaseHistoryShown = "shop_purchase_history_shown"
    case shouldOnboardingEntryPoint = "should_onboarding_entry_point"
    case shouldPrefillReferralCode = "should_prefill_referral_code"
    case shouldShowInputInviteCode = "should_show_input_invite_code"
    case shouldShowReKYCNudge = "should_show_rekyc_nudge"
    case shouldShowUpgradeAccountUponRegistration = "should_show_upgrade_account_upon_registration"
    case sparkHackathonCreatorStoreInterstitialShown = "spark_hackathon_creator_store_intro_shown"
    case stocksCreatorStoreInterstitialShown = "stocks_creator_store_intro_shown"
    case stocksGameCreatorStoreInterstitialShown = "stocks_game_creator_store_intro_shown"
    case streamCreatorStoreInterstitialShown = "stream_creator_store_intro_shown"
    case supportHasActiveChat = "support_has_active_chat"
    case supportShouldShowUnread = "support_should_show_unread"
    case supportActiveChatUnreadCount = "support_active_chat_unread_count"
    case travelTicketLastTransactionDate = "travel_ticket_last_transaction_date"
    case whitelistLastUpdatedOn = "whitelist_last_updated_on"
    case workNaturesEkycLastUpdatedOn = "work_natures_ekyc_last_updated_on"
}

extension UserDefaultsStoreId {
    /// Describes how to write the user defaults object.
    var writingOptions: UserDefaultsStore.WritingOptions {
        return .init(id: self, storageMode: storageMode)
    }

    /// Determines which `UserDefaultsStore` storage the user defaults object will be stored in.
    ///
    /// - Warning: Whenever an ID associated to transient data is added, make sure to return `.transient` here
    /// so that it gets stored in the appropriate storage in `UserDefaultsStore`.
    var storageMode: UserDefaultsStore.WritingOptions.StorageMode {
        switch self {
        case .actionCardsLastUpdatedOn,
            .dedupAlertShownForUser,
            .fundsCreatorStoreInterstitialShown,
            .globalRemittanceCreatorStoreInterstitialShown,
            .globalStocksCreatorStoreInterstitialShown,
            .hideBalanceOn,
            .insuranceCreatorStoreInterstitialShown,
            .philippineAirlinesMMAInterstitialShown,
            .sparkHackathonCreatorStoreInterstitialShown,
            .stocksCreatorStoreInterstitialShown,
            .stocksGameCreatorStoreInterstitialShown,
            .streamCreatorStoreInterstitialShown,
            .supportHasActiveChat,
            .supportShouldShowUnread,
            .supportActiveChatUnreadCount:
            return .transient
        case .accountTypesEkycLastUpdatedOn,
            .accountUsagesLastUpdatedOn,
            .appConfigurationSettingsLastUpdatedOn,
            .appStoreLink,
            .banksLastUpdatedOn,
            .barcodeScanInstructionShownForUser,
            .blockReasonsLastUpdatedOn,
            .canLinkCard,
            .categoriesLastUpdatedOn,
            .configurationServiceLogsEnabled,
            .consents,
            .countriesLastUpdatedOn,
            .currentShopVersion,
            .debugSwiftNetworkEnabled,
            .decorationsLastUpdatedOn,
            .eddSettingsLastUpdatedOn,
            .expandedReKYCAction,
            .fieldInformationLastUpdatedOn,
            .financialDocumentsLastUpdatedOn,
            .firebaseDebugViewEnabled,
            .formSeriesLastUpdatedOn,
            .hideCreditBalanceOn,
            .identificationTypesLastUpdatedOn,
            .imageQualityRetryCount,
            .incomeSourcesEkycLastUpdatedOn,
            .instructionalOverlayShownForUser,
            .kycEntryPoint,
            .latestReferralCodeToUse,
            .loadUpCentersLastUpdatedOn,
            .mayaIntroductionScreensShown,
            .nationalitiesLastUpdatedOn,
            .onboardingBranchCode,
            .onboardingEntryPoint,
            .onboardingPartner,
            .onlineMerchantsESimsLastUpdatedOn,
            .onlineMerchantsFoodLastUpdatedOn,
            .onlineMerchantsFundsLastUpdatedOn,
            .onlineMerchantsGlobalRemittanceLastUpdatedOn,
            .onlineMerchantsGlobalStocksLastUpdatedOn,
            .onlineMerchantsGovernmentLastUpdatedOn,
            .onlineMerchantsInsuranceLastUpdatedOn,
            .onlineMerchantsInvestLastUpdatedOn,
            .onlineMerchantsLuckyGamesLastUpdatedOn,
            .onlineMerchantsPayLaterLastUpdatedOn,
            .onlineMerchantsRetailLastUpdatedOn,
            .onlineMerchantsRewardsLastUpdatedOn,
            .onlineMerchantsSparkHackathonLastUpdatedOn,
            .onlineMerchantsStocksGameLastUpdatedOn,
            .onlineMerchantsStocksLastUpdatedOn,
            .onlineMerchantsStreamLastUpdatedOn,
            .onlineMerchantsTransportLastUpdatedOn,
            .policy,
            .previousBuildNumber,
            .privacyPolicyDisplayed,
            .providersLastUpdatedOn,
            .providersPerCategoryLastUpdatedOn,
            .reKYCInitialDateTime,
            .restriction,
            .reviewLastDate,
            .reviewVersionNumber,
            .savingsAutoProvisionOptIn,
            .savingsAutoProvisionOptInSent,
            .sendMoneyWizardShownForUser,
            .shopBuyAgainProductsLastUpdatedOn,
            .shopProductsLastUpdatedOn,
            .shopPurchaseHistoryLastUpdatedOn,
            .shopPurchaseHistoryShown,
            .shouldOnboardingEntryPoint,
            .shouldPrefillReferralCode,
            .shouldShowInputInviteCode,
            .shouldShowReKYCNudge,
            .shouldShowUpgradeAccountUponRegistration,
            .travelTicketLastTransactionDate,
            .whitelistLastUpdatedOn,
            .workNaturesEkycLastUpdatedOn:
            return .standard
        }
    }
}

// MARK: - Helper for UserDefaultsStoreMigrator
extension UserDefaultsStoreId {
    /// Determines if user defaults object should be reset in `UserDefaultsStoreMigrator`.
    ///
    /// - Warning: Whenever an ID associated to metadata (`HEAD` request) is added, make sure to return `true` here
    /// so that it gets reset in `UserDefaultsStoreMigrator` during every app update.
    var clearOnMigration: Bool {
        switch self {
        case .accountTypesEkycLastUpdatedOn,
            .accountUsagesLastUpdatedOn,
            .actionCardsLastUpdatedOn,
            .appConfigurationSettingsLastUpdatedOn,
            .banksLastUpdatedOn,
            .blockReasonsLastUpdatedOn,
            .categoriesLastUpdatedOn,
            .countriesLastUpdatedOn,
            .decorationsLastUpdatedOn,
            .eddSettingsLastUpdatedOn,
            .expandedReKYCAction,
            .fieldInformationLastUpdatedOn,
            .financialDocumentsLastUpdatedOn,
            .formSeriesLastUpdatedOn,
            .identificationTypesLastUpdatedOn,
            .incomeSourcesEkycLastUpdatedOn,
            .kycEntryPoint,
            .loadUpCentersLastUpdatedOn,
            .nationalitiesLastUpdatedOn,
            .onboardingBranchCode,
            .onboardingEntryPoint,
            .onboardingPartner,
            .onlineMerchantsESimsLastUpdatedOn,
            .onlineMerchantsFoodLastUpdatedOn,
            .onlineMerchantsGovernmentLastUpdatedOn,
            .onlineMerchantsInsuranceLastUpdatedOn,
            .onlineMerchantsLuckyGamesLastUpdatedOn,
            .onlineMerchantsPayLaterLastUpdatedOn,
            .onlineMerchantsRetailLastUpdatedOn,
            .onlineMerchantsRewardsLastUpdatedOn,
            .onlineMerchantsSparkHackathonLastUpdatedOn,
            .onlineMerchantsStocksLastUpdatedOn,
            .onlineMerchantsStreamLastUpdatedOn,
            .onlineMerchantsTransportLastUpdatedOn,
            .providersLastUpdatedOn,
            .providersPerCategoryLastUpdatedOn,
            .shopBuyAgainProductsLastUpdatedOn,
            .shopProductsLastUpdatedOn,
            .shopPurchaseHistoryLastUpdatedOn,
            .shopPurchaseHistoryShown,
            .shouldOnboardingEntryPoint,
            .shouldShowReKYCNudge,
            .supportHasActiveChat,
            .supportShouldShowUnread,
            .supportActiveChatUnreadCount,
            .whitelistLastUpdatedOn,
            .workNaturesEkycLastUpdatedOn:
            return true
        case .appStoreLink,
            .barcodeScanInstructionShownForUser,
            .canLinkCard,
            .configurationServiceLogsEnabled,
            .consents,
            .currentShopVersion,
            .debugSwiftNetworkEnabled,
            .dedupAlertShownForUser,
            .firebaseDebugViewEnabled,
            .fundsCreatorStoreInterstitialShown,
            .globalRemittanceCreatorStoreInterstitialShown,
            .globalStocksCreatorStoreInterstitialShown,
            .hideBalanceOn,
            .hideCreditBalanceOn,
            .imageQualityRetryCount,
            .instructionalOverlayShownForUser,
            .insuranceCreatorStoreInterstitialShown,
            .latestReferralCodeToUse,
            .mayaIntroductionScreensShown,
            .onlineMerchantsFundsLastUpdatedOn,
            .onlineMerchantsGlobalRemittanceLastUpdatedOn,
            .onlineMerchantsGlobalStocksLastUpdatedOn,
            .onlineMerchantsInvestLastUpdatedOn,
            .onlineMerchantsStocksGameLastUpdatedOn,
            .philippineAirlinesMMAInterstitialShown,
            .policy,
            .previousBuildNumber,
            .privacyPolicyDisplayed,
            .reKYCInitialDateTime,
            .restriction,
            .reviewLastDate,
            .reviewVersionNumber,
            .savingsAutoProvisionOptIn,
            .savingsAutoProvisionOptInSent,
            .sendMoneyWizardShownForUser,
            .shouldPrefillReferralCode,
            .shouldShowInputInviteCode,
            .shouldShowUpgradeAccountUponRegistration,
            .sparkHackathonCreatorStoreInterstitialShown,
            .stocksCreatorStoreInterstitialShown,
            .stocksGameCreatorStoreInterstitialShown,
            .streamCreatorStoreInterstitialShown,
            .travelTicketLastTransactionDate:
            return false
        }
    }
}

extension UserDefaultsStoreId: StorableIdentifier {
    func value() -> String {
        return self.rawValue
    }
}
