//
//  CreatorStore+Analytics.swift
//  PayMaya
//
//  Created by <PERSON> on 4/8/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import Foundation

// MARK: List
extension CreatorStore {
    var emptyScreen: AnalyticsModule {
        switch self {
        case .esims: return Analytics.ESimsCreatorStore.empty
        case .food: return Analytics.FoodCreatorStore.empty
        case .funds: return Analytics.FundsCreatorStore.empty
        case .globalRemittance: return Analytics.RemittanceCreatorStore.empty
        case .globalStocks: return Analytics.StocksCreatorStore.empty
        case .government: return Analytics.GovernmentOnlinePayment.emptyScreen
        case .insurance: return Analytics.InsuranceCreatorStore.empty
        case .luckyGames: return Analytics.LuckyGamesCreatorStore.empty
        case .merchantRewards: return Analytics.MerchantRewardsCreatorStore.empty
        case .payLater: return Analytics.PayLaterCreatorStore.empty
        case .retail: return Analytics.RetailOnlinePayment.emptyScreen
        case .sparkHackathon: return Analytics.SparkHackathonCreatorStore.empty
        case .stocks: return Analytics.StocksCreatorStore.empty
        case .stocksGame: return Analytics.StocksGameCreatorStore.empty
        case .stream: return Analytics.StreamCreatorStore.empty
        }
    }

    var screen: AnalyticsModule {
        switch self {
        case .esims: return Analytics.ESimsCreatorStore()
        case .food: return Analytics.FoodCreatorStore()
        case .funds: return Analytics.FundsCreatorStore()
        case .globalRemittance: return Analytics.RemittanceCreatorStore()
        case .globalStocks: return Analytics.StocksCreatorStore()
        case .government: return Analytics.GovernmentOnlinePayment()
        case .insurance: return Analytics.InsuranceCreatorStore()
        case .luckyGames: return Analytics.LuckyGamesCreatorStore()
        case .merchantRewards: return Analytics.MerchantRewardsCreatorStore()
        case .payLater: return Analytics.PayLaterCreatorStore()
        case .retail: return Analytics.RetailOnlinePayment()
        case .sparkHackathon: return Analytics.SparkHackathonCreatorStore()
        case .stocks: return Analytics.StocksCreatorStore()
        case .stocksGame: return Analytics.StocksGameCreatorStore()
        case .stream: return Analytics.StreamCreatorStore()
        }
    }

    var merchantModule: AnalyticsModule {
        switch self {
        case .esims: return Analytics.ESimsCreatorStore.merchant
        case .food: return Analytics.FoodCreatorStore.merchant
        case .funds: return Analytics.FundsCreatorStore.merchant
        case .globalRemittance: return Analytics.RemittanceCreatorStore.merchant
        case .globalStocks: return Analytics.StocksCreatorStore.merchant
        case .government: return Analytics.GovernmentOnlinePayment.tapMerchants
        case .insurance: return Analytics.InsuranceCreatorStore.merchant
        case .luckyGames: return Analytics.LuckyGamesCreatorStore.merchant
        case .merchantRewards: return Analytics.MerchantRewardsCreatorStore.merchant
        case .payLater: return Analytics.PayLaterCreatorStore.merchant
        case .retail: return Analytics.RetailOnlinePayment.tapMerchants
        case .sparkHackathon: return Analytics.SparkHackathonCreatorStore.merchant
        case .stocks: return Analytics.StocksCreatorStore.merchant
        case .stocksGame: return Analytics.StocksGameCreatorStore.merchant
        case .stream: return Analytics.StreamCreatorStore.merchant
        }
    }

    var analyticsAttributes: [AnalyticsAttributeKey: Any] {
        switch self {
        case .globalStocks: return [.type: "global"]
        case .stocks: return [.type: "local"]
        default: return [:]
        }
    }
}

// MARK: Interstitial
extension CreatorStore {
    var interstitialScreenModule: AnalyticsModule? {
        switch self {
        case .funds: Analytics.FundsStoreInterstitial()
        case .globalRemittance: Analytics.RemittanceCreatorStoreInterstitial()
        case .globalStocks, .stocks: Analytics.StocksCreatorStoreInterstitial()
        case .insurance: Analytics.InsuranceCreatorStoreInterstitial()
        case .luckyGames: Analytics.LuckyGamesStoreInterstitial()
        case .sparkHackathon: Analytics.SparkHackathonStoreInterstitial()
        case .stocksGame: Analytics.StocksGameStoreInterstitial()
        case .stream: Analytics.StreamStoreInterstitial()
        default: nil
        }
    }

    var tapIntestitialContinueModule: AnalyticsModule? {
        switch self {
        case .funds: Analytics.FundsStoreInterstitial.getStarted
        case .globalRemittance: Analytics.RemittanceCreatorStoreInterstitial.getStarted
        case .globalStocks, .stocks: Analytics.StocksCreatorStoreInterstitial.getStarted
        case .insurance: Analytics.InsuranceCreatorStoreInterstitial.getStarted
        case .luckyGames: Analytics.LuckyGamesStoreInterstitial.getStarted
        case .sparkHackathon: Analytics.SparkHackathonStoreInterstitial.getStarted
        case .stocksGame: Analytics.StocksGameStoreInterstitial.getStarted
        case .stream: Analytics.StreamStoreInterstitial.getStarted
        default: nil
        }
    }

    var tapInterstitialBackModule: AnalyticsModule? {
        switch self {
        case .funds: Analytics.FundsStoreInterstitial.back
        case .globalRemittance: Analytics.RemittanceCreatorStoreInterstitial.back
        case .globalStocks, .stocks: Analytics.StocksCreatorStoreInterstitial.back
        case .insurance: Analytics.InsuranceCreatorStoreInterstitial.back
        case .luckyGames: Analytics.LuckyGamesStoreInterstitial.back
        case .sparkHackathon: Analytics.SparkHackathonStoreInterstitial.back
        case .stocksGame: Analytics.StocksGameStoreInterstitial.back
        case .stream: Analytics.StreamStoreInterstitial.back
        default: nil
        }
    }

    var tapInterstitialURLModules: [URL: AnalyticsModule] {
        switch self {
        case .luckyGames:
            var modules: [URL: AnalyticsModule] = [:]
            if let termsURL = Constants.WebView.tnc.url {
                modules[termsURL] = Analytics.LuckyGamesStoreInterstitial.terms
            }
            return modules
        default:
            return [:]
        }
    }
}

// MARK: Banner interstitial
extension CreatorStore {
    var appearAnalyticsAttributes: [AnalyticsAttributeKey: Any] {
        switch self {
        case .luckyGames: [.category: category]
        default: [:]
        }
    }

    var bannerTapIntestitialContinueModule: AnalyticsModule? {
        switch self {
        case .luckyGames: Analytics.CreatorStoreInterstitial.continue
        default: nil
        }
    }
}
