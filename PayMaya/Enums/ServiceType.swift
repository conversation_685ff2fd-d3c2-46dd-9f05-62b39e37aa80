//
//  ServiceType.swift
//  PayMaya
//
//  Created by <PERSON> on 8/5/21.
//  Copyright © 2021 PayMaya Philippines, Inc. All rights reserved.
//

import ConfigurationService
import Foundation
import Injector

enum ServiceTypeConstants {
    static let categoryKey = "category"
    static let serviceType = "servicetype"
    static let telco = "telco"
}

/// This will serve as the list of supported Secondary Service
enum ServiceType: String, CaseIterable {
    case bankTransfer = "banktransfer"
    case bills
    case blackpinkGiveaway = "giveaway"
    case cashIn = "cashin"
    case creditCard = "creditcard"
    case crypto = "invest"
    case cryptoV2 = "cryptov2"
    case deals
    case donate
    case esims
    case food
    case funds
    case globalRemittance = "globalremittance"
    case globalStocks = "globalstocks"
    case government
    case insurance
    case inviteAFriend = "inviteafriend"
    case load
    case loadV2 = "loadsdk"
    case loans
    case lrt1
    case luckyGames = "luckygames"
    case mayaCredit = "mayacredit"
    case mayaMall
    case merchantRewards = "merchantrewards"
    case merchants
    case missions
    case more
    case myCards = "mycards"
    case pal = "pal"
    case payLater = "paylater"
    case pbb
    case personalGoals = "personalgoals"
    case protect
    case purchaseFinancing = "purchasefinancing"
    case rafflePromo = "rafflegame"
    case receiveMoney = "receivemoney"
    case retail
    case savings
    case savingsToWallet
    case scanqr
    case scheduler
    case sendMoney = "sendmoney"
    case shop
    case sparkHackathon = "sparkhackathon"
    case stocks
    case stocksGame = "stocksgame"
    case stream
    case timeDeposit = "timedepositplus"
    case travel
    case voucher

    var title: String {
        switch self {
        case .bankTransfer: return L10n.Dashboard.Service.Title.bankTransfer
        case .bills: return L10n.Dashboard.Service.Title.bills
        case .blackpinkGiveaway: return L10n.Dashboard.Service.Title.blackPinkGiveaway
        case .cashIn: return L10n.Dashboard.Service.Title.cashIn
        case .creditCard: return L10n.Dashboard.Service.Title.creditCards
        case .crypto: return L10n.Dashboard.Service.Title.crypto
        case .cryptoV2: return L10n.Dashboard.Service.Title.crypto
        case .deals: return L10n.Dashboard.Service.Title.deals
        case .donate: return L10n.Dashboard.Service.Title.donate
        case .esims: return L10n.Dashboard.Service.Title.esims
        case .food: return L10n.Dashboard.Service.Title.food
        case .funds: return L10n.Dashboard.Service.Title.funds
        case .globalRemittance: return L10n.Dashboard.Service.Title.globalRemittance
        case .globalStocks: return L10n.Dashboard.Service.Title.globalStocks
        case .government: return L10n.Dashboard.Service.Title.government
        case .insurance: return L10n.Dashboard.Service.Title.insurance
        case .inviteAFriend: return L10n.Dashboard.Service.Title.inviteAFriend
        case .load, .loadV2: return L10n.Dashboard.Service.Title.load
        case .loans: return L10n.Dashboard.Service.Title.loans
        case .lrt1: return L10n.Dashboard.Service.Title.lrt1
        case .luckyGames: return L10n.Dashboard.Service.Title.luckyGames
        case .mayaCredit: return L10n.Dashboard.Service.Title.mayaCredit
        case .mayaMall: return L10n.Dashboard.Service.Title.mayaMall
        case .merchantRewards: return L10n.Dashboard.Service.Title.merchantRewards
        case .merchants: return L10n.Dashboard.Service.Title.merchants
        case .missions: return L10n.Dashboard.Service.Title.missions
        case .more: return L10n.Dashboard.Service.Title.more
        case .myCards: return L10n.Dashboard.Service.Title.myCards
        case .pal: return L10n.Dashboard.Service.Title.pal
        case .payLater: return L10n.Dashboard.Service.Title.payLater
        case .pbb: return L10n.Dashboard.Service.Title.pbb
        case .personalGoals: return L10n.Dashboard.Service.Title.personalGoals
        case .protect: return L10n.Dashboard.Service.Title.protect
        case .purchaseFinancing: return L10n.Dashboard.Service.Title.purchaseFinancing
        case .rafflePromo: return L10n.Dashboard.Service.Title.rafflePromo
        case .receiveMoney: return L10n.Dashboard.Service.Title.myQR
        case .retail: return L10n.Dashboard.Service.Title.retail
        case .savings: return L10n.Dashboard.Service.Title.savings
        case .savingsToWallet: return L10n.Dashboard.Service.Title.savingsToWallet
        case .scanqr: return L10n.Dashboard.Service.Title.scanqr
        case .scheduler: return L10n.Dashboard.Service.Title.scheduler
        case .sendMoney: return L10n.Dashboard.Service.Title.sendMoney
        case .shop: return L10n.Dashboard.Service.Title.shop
        case .sparkHackathon: return L10n.Dashboard.Service.Title.sparkHackathon
        case .stocks: return L10n.Dashboard.Service.Title.stocks
        case .stocksGame: return L10n.Dashboard.Service.Title.stocksGame
        case .stream: return L10n.Dashboard.Service.Title.stream
        case .timeDeposit: return L10n.Dashboard.Service.Title.timeDeposit
        case .travel: return L10n.Dashboard.Service.Title.travel
        case .voucher: return L10n.Dashboard.Service.Title.voucher
        }
    }

    var imageAsset: ImageAsset {
        switch self {
        case .bankTransfer: return Asset.MayaImages.Services.iconServiceBankTransfer
        case .bills: return Asset.MayaImages.Services.iconServiceBills
        case .blackpinkGiveaway: return Asset.MayaImages.Services.iconServiceBlackpinkGiveaway
        case .cashIn: return Asset.MayaImages.Services.iconServiceCashIn
        case .creditCard: return Asset.MayaImages.Services.iconServiceCreditCard
        case .crypto: return Asset.MayaImages.Services.iconServiceCrypto
        case .cryptoV2: return Asset.MayaImages.Services.iconServiceCrypto
        case .deals: return Asset.MayaImages.Services.iconServiceDeals
        case .donate: return Asset.MayaImages.Services.iconServiceDonations
        case .esims: return Asset.MayaImages.Services.iconServiceESims
        case .food: return Asset.MayaImages.Services.iconServiceFood
        case .funds: return Asset.MayaImages.Services.iconServiceFunds
        case .globalRemittance: return Asset.MayaImages.Services.iconServiceGlobalRemittance
        case .globalStocks: return Asset.MayaImages.Services.iconServiceGlobalStocks
        case .government: return Asset.MayaImages.Services.iconServiceGovernment
        case .insurance: return Asset.MayaImages.Services.iconServiceInsurance
        case .inviteAFriend: return Asset.MayaImages.Services.iconServiceInviteAFriend
        case .load, .loadV2: return Asset.MayaImages.Services.iconServiceLoad
        case .loans: return Asset.MayaImages.Services.iconServiceLoans
        case .lrt1: return Asset.MayaImages.Services.iconServiceTrain
        case .luckyGames: return Asset.MayaImages.Services.iconServiceLuckyGames
        case .mayaCredit: return Asset.MayaImages.Services.iconServiceCredit
        case .mayaMall: return Asset.MayaImages.Services.iconServiceMayaMall
        case .merchantRewards: return Asset.MayaImages.Services.iconServiceGift
        case .merchants: return Asset.MayaImages.Services.iconServicePartners
        case .missions: return Asset.MayaImages.Services.iconServiceMissions
        case .more: return Asset.MayaImages.Dashboard.moreIcon // No new asset
        case .myCards: return Asset.MayaImages.Services.iconServiceCards
        case .pal: return Asset.MayaImages.Services.iconServicePAL
        case .payLater: return Asset.MayaImages.Services.iconServicePayLater
        case .pbb: return Asset.MayaImages.Services.iconServicePBB
        case .personalGoals: return Asset.MayaImages.Services.iconServicePersonalGoals
        case .protect: return Asset.MayaImages.Services.iconServiceInsurance
        case .purchaseFinancing: return Asset.MayaImages.Services.iconServicePurchaseFinancing
        case .rafflePromo: return Asset.MayaImages.Services.iconServiceRafflePromo
        case .receiveMoney: return ContainerWrapper.shared.resolve(ConfigurationService.self).requestMoneyTileUpdatesEnabled.value ? Asset.MayaImages.Services.iconServiceMyQRv2 : Asset.MayaImages.Services.iconServiceMyQR
        case .retail: return Asset.MayaImages.Services.iconServiceRetail
        case .savings: return Asset.MayaImages.Services.iconServiceSavings
        case .savingsToWallet: return Asset.MayaImages.Services.iconServiceSavings
        case .scanqr: return Asset.MayaImages.Services.iconServiceScanQR
        case .scheduler: return Asset.MayaImages.Services.iconServiceScheduler
        case .sendMoney: return Asset.MayaImages.Services.iconServiceSendMoney
        case .shop: return Asset.MayaImages.Services.iconServiceShop
        case .sparkHackathon: return Asset.MayaImages.Services.iconServiceSparkHackathon
        case .stocks: return Asset.MayaImages.Services.iconServiceStocks
        case .stocksGame: return Asset.MayaImages.Services.iconServiceStocksGame
        case .stream: return Asset.MayaImages.Services.iconServiceStream
        case .timeDeposit: return Asset.MayaImages.Services.iconServiceTimeDeposit
        case .travel: return Asset.MayaImages.Services.iconServiceTravel
        case .voucher: return Asset.MayaImages.Services.iconServiceVouchers
        }
    }

    var deepLinkURL: String? {
        let url: URL?

        switch self {
        case .bankTransfer: url = Constants.DeepLinkPath.banktransfer.url
        case .bills: url = Constants.DeepLinkPath.paybills.url
        case .blackpinkGiveaway: url = Constants.DeepLinkPath.blackpinkGiveaway.url
        case .cashIn: url = Constants.DeepLinkPath.addmoney.url
        case .creditCard: url = Constants.DeepLinkPath.mycards.url
        case .crypto: url = Constants.DeepLinkPath.crypto.url
        case .cryptoV2: url = Constants.DeepLinkPath.cryptoV2.url
        case .deals: url = Constants.DeepLinkPath.deals.url
        case .donate: url = Constants.DeepLinkPath.donate.url
        case .esims: url = Constants.DeepLinkPath.esims.url
        case .food: url = Constants.DeepLinkPath.food.url
        case .funds: url = Constants.DeepLinkPath.funds.url
        case .globalRemittance: url = Constants.DeepLinkPath.globalRemittance.url
        case .globalStocks: url = Constants.DeepLinkPath.globalStocks.url
        case .government: url = Constants.DeepLinkPath.government.url
        case .insurance: url = Constants.DeepLinkPath.insurance.url
        case .inviteAFriend: url = Constants.DeepLinkPath.refer.url
        case .load: url = shopLoadURL
        case .loadV2: url = Constants.DeepLinkPath.load.url
        case .loans: url = Constants.DeepLinkPath.loans.url
        case .lrt1: url = Constants.DeepLinkPath.beep.url
        case .luckyGames: url = Constants.DeepLinkPath.luckyGames.url
        case .mayaCredit: url = Constants.DeepLinkPath.mayaCredit.url
        case .mayaMall: url = Constants.DeepLinkPath.mayaMall.url
        case .merchantRewards: url = Constants.DeepLinkPath.merchantRewards.url
        case .merchants: url = Constants.DeepLinkPath.partnerMerchant.url
        case .missions: url = Constants.DeepLinkPath.missions.url
        case .more: url = Constants.DeepLinkPath.more.url
        case .myCards: url = Constants.DeepLinkPath.mycards.url
        case .pal: url = Constants.DeepLinkPath.pal.url
        case .payLater: url = Constants.DeepLinkPath.payLater.url
        case .pbb: url = Constants.DeepLinkPath.pbb.url
        case .personalGoals: url = Constants.DeepLinkPath.savings.url
        case .protect: url = Constants.DeepLinkPath.protect.url
        case .purchaseFinancing: url = Constants.DeepLinkPath.mayaCredit.url
        case .rafflePromo: url = Constants.DeepLinkPath.rafflePromo.url
        case .receiveMoney: url = Constants.DeepLinkPath.requestMoney.url
        case .retail: url = Constants.DeepLinkPath.retail.url
        case .savings: url = Constants.DeepLinkPath.savings.url
        case .savingsToWallet: url = Constants.DeepLinkPath.mayaSavingsToWallet.url
        case .scanqr: url = Constants.DeepLinkPath.scanQr.url
        case .scheduler: url = Constants.DeepLinkPath.scheduler.url
        case .sendMoney: url = Constants.DeepLinkPath.sendmoney.url
        case .shop: url = Constants.DeepLinkPath.shop.url
        case .sparkHackathon: url = Constants.DeepLinkPath.sparkHackathon.url
        case .stocks: url = Constants.DeepLinkPath.stocks.url
        case .stocksGame: url = Constants.DeepLinkPath.stocksGame.url
        case .stream: url = Constants.DeepLinkPath.stream.url
        case .timeDeposit: url = Constants.DeepLinkPath.savings.url
        case .travel: url = Constants.DeepLinkPath.transport.url
        case .voucher: url = Constants.DeepLinkPath.vouchers.url
        }

        return url?.absoluteString
    }

    private var shopLoadURL: URL? {
        guard let shopURL = Constants.DeepLinkPath.shop.url,
            let p2pCategoryUrl = shopURL.appendQueryItem(name: ServiceTypeConstants.categoryKey, value: ServiceTypeConstants.telco) else {
            return nil
        }
        return p2pCategoryUrl
    }
}
