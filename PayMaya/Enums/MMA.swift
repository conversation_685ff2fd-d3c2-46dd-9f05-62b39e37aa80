//
//  MMA.swift
//  PayMaya
//
//  Created by <PERSON> on 5/2/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Foundation
import Injector
import StoreProvider

enum MMA {
    case pbb
    case philippineAirlines
    case rafflePromo
    case blackpinkGiveaway

    var tpid: String {
        let storeId: PropertyListStoreId = {
            switch self {
            case .pbb: .pbbTPID
            case .philippineAirlines: .palTPID
            case .rafflePromo: .rafflePromoTPID
            case .blackpinkGiveaway: .blackpinkGiveawayTPID
            }
        }()

        guard let plistStore = ContainerWrapper.shared.resolve(StoreProvider.self).target(PropertyListStore.self),
            let tpid = plistStore.read(storeId).value as? String else {
            return ""
        }

        return tpid
    }

    var title: String {
        switch self {
        case .pbb: L10n.Dashboard.Service.Title.pbb
        case .philippineAirlines: L10n.Dashboard.Service.Title.pal
        case .rafflePromo: L10n.Dashboard.Service.Title.rafflePromo
        case .blackpinkGiveaway: ""
        }
    }
}

// MARK: Interstitial
extension MMA: InterstitialModel {
    var hasInterstitial: Bool {
        switch self {
        case .philippineAirlines: true
        default: false
        }
    }

    var interstitialShownStoreID: UserDefaultsStoreId? {
        switch self {
        case .philippineAirlines: .philippineAirlinesMMAInterstitialShown
        default: nil
        }
    }

    var interstitialNavigationTitle: String? { nil }

    var interstitialImage: ImageAsset? {
        switch self {
        case .philippineAirlines: Asset.MayaImages.Mma.iconPhilippineAirlinesMMAInterstitial
        default: nil
        }
    }

    var interstitialTitle: String {
        switch self {
        case .philippineAirlines: L10n.PhilippineAirlines.Interstitial.title
        default: ""
        }
    }

    var interstitialMessages: [MayaInterstitialMessage] {
        switch self {
        case .philippineAirlines:
            return [
                ("🔗", L10n.PhilippineAirlines.Interstitial.message1),
                ("📲", L10n.PhilippineAirlines.Interstitial.message2),
                ("😎", L10n.PhilippineAirlines.Interstitial.message3)
            ].map { MayaInterstitialMessage(emoji: $0.0, message: $0.1) }
        default:
            return []
        }
    }

    var interstitialButtonText: String { CommonStrings.Common.continue }
    var isInterstitialButtonSticky: Bool { true }
}

// MARK: Analytics
extension MMA {
    var interstitialScreenModule: AnalyticsModule? {
        switch self {
        case .philippineAirlines: Analytics.PhilippineAirlinesInterstitial()
        default: nil
        }
    }

    var tapIntestitialContinueModule: AnalyticsModule? {
        switch self {
        case .philippineAirlines: Analytics.PhilippineAirlinesInterstitial.getStarted
        default: nil
        }
    }

    var tapInterstitialBackModule: AnalyticsModule? {
        switch self {
        case .philippineAirlines: Analytics.PhilippineAirlinesInterstitial.back
        default: nil
        }
    }

    var tapInterstitialURLModules: [URL: AnalyticsModule] { [:] }
    var analyticsAttributes: [AnalyticsAttributeKey: Any] { [:] }
}

// MARK: WebView Configuration
extension MMA {
    var webviewConfiguration: WebViewConfiguration {
        switch self {
        case .blackpinkGiveaway:
            return getBlackPinkGiveawayWebConfig()
        default:
            return WebViewConfiguration()
        }
    }

    fileprivate func getBlackPinkGiveawayWebConfig() -> WebViewConfiguration {
        if #available(iOS 18.0, *) {
            return WebViewConfiguration(
                appBarConfiguration: appBarConfiguration,
                backgroundColor: .clear,
                statusBarStyle: .darkContent
            )
        }

        return WebViewConfiguration(
            appBarConfiguration: appBarConfiguration,
            backgroundColor: CommonAsset.MayaColors.Grey.grey12.color
        )
    }
}

// MARK: AppBar Configuration
extension MMA {
    var appBarConfiguration: AppBarConfiguration? {
        switch self {
        case .blackpinkGiveaway:
            return AppBarConfiguration(
                color: CommonAsset.MayaColors.Grey.grey12.color,
                iconColor: CommonAsset.MayaColors.Button.buttonPrimaryWhite.color
            )

        default:
            return nil
        }
    }
}
