//
//  CreatorStore+Interstitial.swift
//  PayMaya
//
//  Created by <PERSON> on 5/2/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import ConfigurationService
import Foundation
import Injector
import UIKit

extension CreatorStore: BannerInterstitialModel {
    var interstitialNavigationTitle: String? { title }

    var hasInterstitial: Bool {
        /*
         For cases returning true, please group them into five per line:
         case .csOne, .csTwo, .csThree, .csFour, .csFive: true
         case .csSix, .csSeven, .csEight, .csNine, .csTen: true
        */
        switch self {
        case .funds, .globalRemittance, .globalStocks, .insurance, .luckyGames: true
        case .sparkHackathon, .stocks, .stocksGame, .stream: true
        default: false
        }
    }

    var interstitialShownStoreID: UserDefaultsStoreId? {
        switch self {
        case .funds: .fundsCreatorStoreInterstitialShown
        case .globalRemittance: .globalRemittanceCreatorStoreInterstitialShown
        case .globalStocks: .globalStocksCreatorStoreInterstitialShown
        case .insurance: .insuranceCreatorStoreInterstitialShown
        case .sparkHackathon: .sparkHackathonCreatorStoreInterstitialShown
        case .stocks: .stocksCreatorStoreInterstitialShown
        case .stocksGame: .stocksGameCreatorStoreInterstitialShown
        case .stream: .streamCreatorStoreInterstitialShown
        default: nil
        }
    }

    var interstitialImage: ImageAsset? {
        switch self {
        case .funds: Asset.MayaImages.CreatorStore.iconFundsCreatorStoreInterstitial
        case .globalRemittance: Asset.MayaImages.CreatorStore.iconGlobalRemittanceCreatorStoreInterstitial
        case .globalStocks: Asset.MayaImages.CreatorStore.iconGlobalStocksCreatorStoreInterstitial
        case .insurance: Asset.MayaImages.CreatorStore.iconInsuranceCreatorStoreInterstitial
        case .luckyGames: Asset.MayaImages.CreatorStore.iconLuckyGamesCreatorStoreInterstitial
        case .sparkHackathon: Asset.MayaImages.CreatorStore.iconSparkHackathonCreatorStoreInterstitial
        case .stocks: Asset.MayaImages.CreatorStore.iconStocksCreatorStoreInterstitial
        case .stocksGame: Asset.MayaImages.CreatorStore.iconStocksGameCreatorStoreInterstitial
        case .stream: Asset.MayaImages.CreatorStore.iconStreamCreatorStoreInterstitial
        default: nil
        }
    }

    var interstitialTitle: String {
        switch self {
        case .funds: L10n.Funds.Interstitial.title
        case .globalRemittance: L10n.GlobalRemittance.Interstitial.title
        case .globalStocks: L10n.GlobalStocks.Interstitial.title
        case .insurance: L10n.Insurance.Interstitial.title
        case .luckyGames: L10n.LuckyGames.Interstitial.title
        case .sparkHackathon: L10n.SparkHackathon.Interstitial.title
        case .stocks: L10n.Stocks.Interstitial.title
        case .stocksGame: L10n.StocksGame.Interstitial.title
        case .stream: L10n.Stream.Interstitial.title
        default: ""
        }
    }

    var interstitialMessages: [MayaInterstitialMessage] {
        switch self {
        case .funds:
            return [
                ("📊", L10n.Funds.Interstitial.message1),
                ("👌", L10n.Funds.Interstitial.message2),
                ("💚", L10n.Funds.Interstitial.message3)
            ].map { MayaInterstitialMessage(emoji: $0.0, message: $0.1) }
        case .globalRemittance:
            return [
                ("🌎", L10n.GlobalRemittance.Interstitial.message1),
                ("👌", L10n.GlobalRemittance.Interstitial.message2),
                ("💚", L10n.GlobalRemittance.Interstitial.message3)
            ].map { MayaInterstitialMessage(emoji: $0.0, message: $0.1) }
        case .globalStocks:
            return [
                ("🌎", L10n.GlobalStocks.Interstitial.message1),
                ("⚡️", L10n.GlobalStocks.Interstitial.message2),
                ("💚", L10n.GlobalStocks.Interstitial.message3)
            ].map { MayaInterstitialMessage(emoji: $0.0, message: $0.1) }
        case .insurance:
            return [
                ("✨", L10n.Insurance.Interstitial.message1),
                ("👌", L10n.Insurance.Interstitial.message2),
                ("💚", L10n.Insurance.Interstitial.message3)
            ].map { MayaInterstitialMessage(emoji: $0.0, message: $0.1) }
        case .luckyGames:
            let textSize: CGFloat = 14
            let lineHeightMultiple: CGFloat = 1.2
            let config = MayaInterstitialMessage.Config(textSize: textSize, lineHeightMultiple: lineHeightMultiple)

            var messages = [
                ("✅", L10n.LuckyGames.Interstitial.message1),
                ("✅", L10n.LuckyGames.Interstitial.message2),
                ("✅", L10n.LuckyGames.Interstitial.message3)
            ].map { MayaInterstitialMessage(emoji: $0.0, message: $0.1, config: config) }

            let links = [L10n.LuckyGames.Interstitial.TermsAndConditions.link: Constants.WebView.tnc.url]

            let attributedMessage4 = L10n.LuckyGames.Interstitial.termsAndConditions.setLink(
                links: links,
                textColor: CommonAsset.MayaColors.Content.contentGrey6.color,
                textFont: CommonFontFamily.CerebriSansPro.book.font(size: textSize)!,
                lineHeightMultiple: lineHeightMultiple
            )

            messages.append(MayaInterstitialMessage(emoji: "✅", attributedMessage: attributedMessage4, config: config))

            return messages
        case .sparkHackathon:
            return [
                ("💡", L10n.SparkHackathon.Interstitial.message1),
                ("🤖", L10n.SparkHackathon.Interstitial.message2),
                ("🧑‍💻", L10n.SparkHackathon.Interstitial.message3)
            ].map { MayaInterstitialMessage(emoji: $0.0, message: $0.1) }
        case .stocks:
            return [
                ("📊", L10n.Stocks.Interstitial.message1),
                ("👌", L10n.Stocks.Interstitial.message2),
                ("💚", L10n.Stocks.Interstitial.message3)
            ].map { MayaInterstitialMessage(emoji: $0.0, message: $0.1) }
        case .stocksGame:
            return [
                ("💡", L10n.StocksGame.Interstitial.message1),
                ("👌", L10n.StocksGame.Interstitial.message2),
                ("🌎", L10n.StocksGame.Interstitial.message3)
            ].map { MayaInterstitialMessage(emoji: $0.0, message: $0.1) }
        case .stream:
            return [
                ("📱", L10n.Stream.Interstitial.message1),
                ("🍿", L10n.Stream.Interstitial.message2),
                ("💚", L10n.Stream.Interstitial.message3)
            ].map { MayaInterstitialMessage(emoji: $0.0, message: $0.1) }
        default:
            return []
        }
    }

    var interstitialButtonText: String {
        switch self {
        case .luckyGames: CommonStrings.Common.continue
        default: L10n.CreatorStore.Interstitial.Button.title
        }
    }

    var isInterstitialButtonSticky: Bool {
        switch self {
        case .funds, .luckyGames: false
        default: true
        }
    }
}

// MARK: Banner interstitial
extension CreatorStore {
    var doesInterstitialHaveBanners: Bool {
        guard ContainerWrapper.shared.resolve(ConfigurationServiceV2.self).creatorStoreInterstitialBannersEnabled.value else {
            return false
        }

        switch self {
        case .luckyGames: return true
        default: return false
        }
    }

    var bannerInterstitialTitle: String {
        switch self {
        case .luckyGames: L10n.LuckyGames.Interstitial.Banner.title
        default: ""
        }
    }

    var bannerInterstitialMessages: [MayaInterstitialMessage] {
        switch self {
        case .luckyGames:
            let config = MayaInterstitialMessage.Config(textSize: 12, lineHeightMultiple: 1, spacing: -6)
            return [
                ("•", L10n.LuckyGames.Interstitial.Banner.message1),
                ("•", L10n.LuckyGames.Interstitial.Banner.message2),
                ("•", L10n.LuckyGames.Interstitial.Banner.message3)
            ].map { MayaInterstitialMessage(emoji: $0.0, message: $0.1, config: config) }
        default:
            return []
        }
    }

    var isBannerInterstitialButtonSticky: Bool {
        switch self {
        case .luckyGames: true
        default: false
        }
    }

    var footerText: NSAttributedString? {
        switch self {
        case .luckyGames:
            let text = L10n.LuckyGames.Interstitial.Banner.termsAndConditions
            let links = [L10n.LuckyGames.Interstitial.TermsAndConditions.link: Constants.WebView.tnc.url]
            return text.setLink(
                links: links,
                textColor: CommonAsset.MayaColors.Content.contentGrey6.color,
                textFont: CommonFontFamily.CerebriSansPro.book.font(size: 12)!,
                textAlignment: .center
            )
        default:
            return nil
        }
    }
}
