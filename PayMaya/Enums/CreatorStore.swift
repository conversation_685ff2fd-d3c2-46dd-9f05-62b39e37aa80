//
//  CreatorStore.swift
//  PayMaya
//
//  Created by <PERSON> on 11/14/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import Foundation

enum CreatorStore {
    case esims
    case food
    case funds
    case globalRemittance
    case globalStocks
    case government
    case insurance
    case luckyGames
    case merchantRewards
    case payLater
    case retail
    case sparkHackathon
    case stocks
    case stocksGame
    case stream

    var category: String {
        switch self {
        case .esims: return "esims"
        case .food: return "food"
        case .funds: return "mayafunds"
        case .globalRemittance: return "globalremittance"
        case .globalStocks: return "globalstocks"
        case .government: return "government"
        case .insurance: return "insurance"
        case .luckyGames: return "luckygames"
        case .merchantRewards: return "merchantrewards"
        case .payLater: return "paylater"
        case .retail: return "retail"
        case .sparkHackathon: return "sparkhackathon"
        case .stocks: return "stocks"
        case .stocksGame: return "stocksgame"
        case .stream: return "stream"
        }
    }

    var lastUpdatedKey: UserDefaultsStoreId {
        switch self {
        case .esims: return .onlineMerchantsESimsLastUpdatedOn
        case .food: return .onlineMerchantsFoodLastUpdatedOn
        case .funds: return .onlineMerchantsFundsLastUpdatedOn
        case .globalRemittance: return .onlineMerchantsGlobalRemittanceLastUpdatedOn
        case .globalStocks: return .onlineMerchantsGlobalStocksLastUpdatedOn
        case .government: return .onlineMerchantsGovernmentLastUpdatedOn
        case .insurance: return .onlineMerchantsInsuranceLastUpdatedOn
        case .luckyGames: return .onlineMerchantsLuckyGamesLastUpdatedOn
        case .merchantRewards: return .onlineMerchantsRewardsLastUpdatedOn
        case .payLater: return .onlineMerchantsPayLaterLastUpdatedOn
        case .retail: return .onlineMerchantsRetailLastUpdatedOn
        case .sparkHackathon: return .onlineMerchantsSparkHackathonLastUpdatedOn
        case .stocks: return .onlineMerchantsStocksLastUpdatedOn
        case .stocksGame: return .onlineMerchantsStocksGameLastUpdatedOn
        case .stream: return .onlineMerchantsStreamLastUpdatedOn
        }
    }

    var title: String {
        switch self {
        case .esims: return L10n.CreatorStore.ESims.title
        case .food: return L10n.CreatorStore.Food.title
        case .funds: return L10n.CreatorStore.Funds.title
        case .globalRemittance: return L10n.CreatorStore.GlobalRemittance.title
        case .globalStocks: return L10n.CreatorStore.GlobalStocks.title
        case .government: return L10n.CreatorStore.Government.title
        case .insurance: return L10n.CreatorStore.Insurance.title
        case .luckyGames: return L10n.CreatorStore.Games.title
        case .merchantRewards: return L10n.CreatorStore.MerchantRewards.title
        case .payLater: return L10n.CreatorStore.PayLater.title
        case .retail: return L10n.CreatorStore.Retail.title
        case .sparkHackathon: return L10n.CreatorStore.SparkHackathon.title
        case .stocks: return L10n.CreatorStore.Stocks.title
        case .stocksGame: return L10n.CreatorStore.StocksGame.title
        case .stream: return L10n.CreatorStore.Stream.title
        }
    }
}
