//
//  GoogleMobileAdsConsentManager.swift
//  PayMaya
//
//  Created by <PERSON> on 06/09/25.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import ConsentOptOutService
import Foundation
import GoogleMobileAds
import Injector
import ReactiveSwift
import UIKit
import UserMessagingPlatform

/// Manager class for handling Google Mobile Ads User Messaging Platform (UMP) consent
class GoogleMobileAdsConsentManager {
    // MARK: - Constants
    private enum Constants {
        static let tag = "GoogleMobileAdsConsentManager"
        static let domain = "GoogleMobileAdsConsentManager"
    }

    @Inject private var consentOptOutService: ConsentOptOutService

    // MARK: - Properties
    var consentStatusProperty = MutableProperty<ConsentStatus>(.unknown)
    var isConsentFormAvailableProperty = MutableProperty<Bool>(false)

    private var consentForm: ConsentForm?
    private var consentInformation: ConsentInformation {
        return ConsentInformation.shared
    }
    private let disposeBag = CompositeDisposable()

    // MARK: - Initialization
    init() {
        setupConsentStatusObserver()
    }

    deinit {
        disposeBag.dispose()
    }

    // MARK: - Private Methods
    private func setupConsentStatusObserver() {
        // Update consent status based on current UMP consent information
        updateConsentStatus()

        // Observe changes in the existing consent system
        consentOptOutService.getUserDataSendingConsentOptOutAction.values
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                self?.updateConsentStatus()
            }?.addToDisposeBag(disposeBag)
    }

    private func updateConsentStatus() {
        let currentStatus = consentInformation.consentStatus
        consentStatusProperty.value = currentStatus
        isConsentFormAvailableProperty.value = consentInformation.formStatus == .available
    }

    private func log(_ message: String) {
        #if !PROD_TARGET && !SANDBOX_TARGET
        print("[\(Constants.tag)] \(message)")
        #endif
    }

    // MARK: - Public Methods

    /// Create debug settings for testing
    /// - Parameters:
    ///   - debugGeography: Debug geography setting (default: EEA)
    ///   - testDeviceHashedIds: List of test device hashed IDs
    /// - Returns: UMPDebugSettings configured for testing
    func createDebugSettings(
        debugGeography: DebugGeography = .EEA,
        testDeviceHashedIds: [String] = []
    ) -> DebugSettings {
        let debugSettings = DebugSettings()
        debugSettings.geography = debugGeography
        debugSettings.testDeviceIdentifiers = testDeviceHashedIds
        return debugSettings
    }

    /// Gather user consent
    /// - Parameters:
    ///   - viewController: The view controller to present consent form from
    ///   - debugSettings: Debug settings for testing (optional)
    ///   - forceShow: Force showing the consent form even if not required
    ///   - completion: Completion handler called when consent form is dismissed
    func gatherConsent(
        from viewController: UIViewController,
        debugSettings: DebugSettings? = nil,
        forceShow: Bool = false,
        completion: @escaping (Error?) -> Void
    ) {
        // In debug builds and when not forcing consent, skip consent
        #if DEBUG
        if !forceShow {
            log("Debug mode: Skipping consent")
            completion(nil)
            return
        }
        #endif

        let parameters = RequestParameters()
        parameters.isTaggedForUnderAgeOfConsent = false

        #if DEBUG
        if let debugSettings = debugSettings {
            parameters.debugSettings = debugSettings
        }
        #endif

        consentInformation.requestConsentInfoUpdate(with: parameters) { [weak self] error in
            guard let self = self else { return }

            if let error = error {
                self.log("Error requesting consent info update: \(error.localizedDescription)")
                completion(error)
                return
            }

            if self.consentInformation.formStatus == .available {
                ConsentForm.load { [weak self] form, error in
                    guard let self = self else { return }

                    if let error = error {
                        self.log("Error loading consent form: \(error.localizedDescription)")
                        completion(error)
                        return
                    }

                    guard let form = form else {
                        let error = NSError(domain: Constants.domain, code: -2, userInfo: [NSLocalizedDescriptionKey: "Consent form is nil"])
                        completion(error)
                        return
                    }

                    self.consentForm = form

                    if forceShow || !self.consentInformation.canRequestAds {
                        form.present(from: viewController) { [weak self] error in
                            self?.updateConsentStatus()
                            completion(error)
                        }
                    } else {
                        #if DEBUG
                        if !forceShow {
                            self.log("Debug mode: Setting default consent status")
                            self.log("Debug mode: Skipping consent")
                            completion(nil)
                        }
                        #endif
                        completion(nil)
                    }
                }
            } else {
                completion(nil)
            }
        }
    }

    /// Show privacy options form
    /// - Parameters:
    ///   - viewController: The view controller to present privacy options form from
    ///   - completion: Completion handler called when privacy options form is dismissed
    func showPrivacyOptionsForm(
        from viewController: UIViewController,
        completion: @escaping (Error?) -> Void
    ) {
        if let consentForm = consentForm {
            consentForm.present(from: viewController) { [weak self] error in
                self?.updateConsentStatus()
                completion(error)
            }
        } else {
            gatherConsent(from: viewController, completion: completion)
        }
    }

    /// Check if ads can be requested based on consent status
    /// - Returns: True if ads can be requested, false otherwise
    func canRequestAds() -> Bool {
        #if DEBUG
        return true
        #else
        return consentInformation.canRequestAds
        #endif
    }

    /// Reset consent information (for testing purposes)
    func resetConsentInformation() {
        consentInformation.reset()
        updateConsentStatus()
    }
}
