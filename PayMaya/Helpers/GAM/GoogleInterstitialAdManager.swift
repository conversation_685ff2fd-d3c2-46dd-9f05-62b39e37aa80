//
//  GoogleInterstitialAdManager.swift
//  PayMaya
//
//  Created by Augment Agent on 12/19/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import UIKit
import GoogleMobileAds
import Injector
import ReactiveSwift

/// Manager class specifically for handling Google Mobile Ads interstitial ads
/// This is separate from the existing CleverTap ad carousel functionality
class GoogleInterstitialAdManager {
    
    // MARK: - Constants
    private enum Constants {
        static let tag = "GoogleInterstitialAdManager"
        static let testInterstitialAdUnitId = "ca-app-pub-3940256099942544/1033173712"
        static let defaultShowInterval: TimeInterval = 30.0
        static let maxAdsPerSession = 3
    }
    
    // MARK: - Properties
    @Inject private var googleAdsHelper: GoogleAdsHelper
    
    private var interstitialAdTimer: Timer?
    private var interstitialAdShownCount = 0
    private var isInitialized = false
    
    // Configuration
    private var showInterval: TimeInterval = Constants.defaultShowInterval
    private var maxAdsPerSession = Constants.maxAdsPerSession
    private var adUnitId: String = Constants.testInterstitialAdUnitId
    
    // State tracking
    private var isViewVisible = false
    private var hasRequestedConsent = false
    
    // MARK: - Initialization
    init() {
        // Dependency injection will automatically inject googleAdsHelper
    }
    
    deinit {
        stopInterstitialAdTimer()
    }
    
    // MARK: - Private Methods
    private func log(_ message: String) {
        #if !PROD_TARGET && !SANDBOX_TARGET
        print("[\(Constants.tag)] \(message)")
        #endif
    }
    
    // MARK: - Public Configuration Methods
    
    /// Configure the interstitial ad manager
    /// - Parameters:
    ///   - adUnitId: The ad unit ID to use (defaults to test ad unit)
    ///   - showInterval: Time interval between automatic ad displays (default: 30 seconds)
    ///   - maxAdsPerSession: Maximum ads to show per session (default: 3)
    func configure(
        adUnitId: String = Constants.testInterstitialAdUnitId,
        showInterval: TimeInterval = Constants.defaultShowInterval,
        maxAdsPerSession: Int = Constants.maxAdsPerSession
    ) {
        self.adUnitId = adUnitId
        self.showInterval = showInterval
        self.maxAdsPerSession = maxAdsPerSession
        
        log("Configured with adUnitId: \(adUnitId), interval: \(showInterval)s, maxAds: \(maxAdsPerSession)")
    }
    
    /// Initialize the interstitial ad manager
    /// This should be called once during app startup or when the feature is first needed
    /// - Parameter viewController: The view controller to request consent from
    func initialize(from viewController: UIViewController) {
        guard !isInitialized else {
            log("Already initialized")
            return
        }
        
        log("Initializing Google Interstitial Ad Manager")
        
        // Initialize the Google Ads SDK
        googleAdsHelper.initialize()
        
        // Request consent
        requestConsent(from: viewController)
        
        isInitialized = true
    }
    
    /// Start showing interstitial ads automatically
    /// Call this when the view becomes visible
    func startAutomaticAds() {
        #if DEBUG
        guard isInitialized else {
            log("Manager not initialized. Call initialize() first.")
            return
        }
        
        isViewVisible = true
        
        guard hasRequestedConsent else {
            log("Consent not yet requested. Automatic ads will start after consent.")
            return
        }
        
        guard googleAdsHelper.canRequestAds() else {
            log("Cannot show ads - consent not granted")
            return
        }
        
        startInterstitialAdTimer()
        #endif
    }
    
    /// Stop showing interstitial ads automatically
    /// Call this when the view becomes hidden
    func stopAutomaticAds() {
        isViewVisible = false
        stopInterstitialAdTimer()
        log("Stopped automatic ads")
    }
    
    /// Manually trigger an interstitial ad for testing
    /// - Parameter viewController: The view controller to present the ad from
    func showInterstitialAdNow(from viewController: UIViewController) {
        #if DEBUG
        log("Manual interstitial ad trigger requested")
        
        guard googleAdsHelper.canRequestAds() else {
            log("Cannot show ad - consent not granted")
            return
        }
        
        stopInterstitialAdTimer() // Stop automatic timer
        showInterstitialAd(from: viewController)
        #endif
    }
    
    /// Reset the session ad count (useful for testing)
    func resetSessionCount() {
        interstitialAdShownCount = 0
        log("Session ad count reset")
    }
    
    // MARK: - Private Implementation Methods
    
    private func requestConsent(from viewController: UIViewController) {
        log("Requesting consent for interstitial ads")
        
        googleAdsHelper.requestConsent(from: viewController) { [weak self] canShowAds in
            guard let self = self else { return }
            
            self.hasRequestedConsent = true
            
            if canShowAds {
                self.log("Consent granted - interstitial ads can be shown")
                
                // Start automatic ads if view is visible
                if self.isViewVisible {
                    self.startInterstitialAdTimer()
                }
            } else {
                self.log("Consent not granted - interstitial ads will not be shown")
            }
        }
    }
    
    private func startInterstitialAdTimer() {
        #if DEBUG
        guard interstitialAdShownCount < maxAdsPerSession else {
            log("Maximum interstitial ads (\(maxAdsPerSession)) shown for this session")
            return
        }
        
        stopInterstitialAdTimer()
        
        interstitialAdTimer = Timer.scheduledTimer(withTimeInterval: showInterval, repeats: false) { [weak self] _ in
            guard let self = self else { return }
            
            // Find the current top view controller
            if let topViewController = self.findTopViewController() {
                self.showInterstitialAd(from: topViewController)
            } else {
                self.log("Could not find top view controller for interstitial ad")
            }
        }
        
        log("Interstitial ad timer started - will show in \(showInterval) seconds")
        #endif
    }
    
    private func stopInterstitialAdTimer() {
        interstitialAdTimer?.invalidate()
        interstitialAdTimer = nil
    }
    
    private func showInterstitialAd(from viewController: UIViewController) {
        #if DEBUG
        guard googleAdsHelper.canRequestAds() else {
            log("Cannot show interstitial ad - consent not granted")
            scheduleNextAd() // Retry later
            return
        }
        
        log("Attempting to show interstitial ad")
        
        googleAdsHelper.loadAndShowInterstitialAd(
            from: viewController,
            adUnitId: adUnitId
        ) { [weak self] success in
            guard let self = self else { return }
            
            if success {
                self.interstitialAdShownCount += 1
                self.log("Interstitial ad shown successfully (count: \(self.interstitialAdShownCount)/\(self.maxAdsPerSession))")
            } else {
                self.log("Failed to show interstitial ad")
            }
            
            // Schedule next ad if we haven't reached the limit and view is still visible
            self.scheduleNextAd()
        }
        #endif
    }
    
    private func scheduleNextAd() {
        #if DEBUG
        guard isViewVisible else {
            log("View not visible - not scheduling next ad")
            return
        }
        
        guard interstitialAdShownCount < maxAdsPerSession else {
            log("Session limit reached - not scheduling next ad")
            return
        }
        
        // Wait a bit before scheduling the next ad
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            self?.startInterstitialAdTimer()
        }
        #endif
    }
    
    private func findTopViewController() -> UIViewController? {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return nil
        }
        
        return window.rootViewController?.topMostViewController()
    }
}

// MARK: - UIViewController Extension
private extension UIViewController {
    func topMostViewController() -> UIViewController {
        if let presented = presentedViewController {
            return presented.topMostViewController()
        }
        
        if let navigationController = self as? UINavigationController {
            return navigationController.visibleViewController?.topMostViewController() ?? self
        }
        
        if let tabBarController = self as? UITabBarController {
            return tabBarController.selectedViewController?.topMostViewController() ?? self
        }
        
        return self
    }
}
