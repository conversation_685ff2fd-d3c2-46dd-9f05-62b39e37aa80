//
//  GoogleInterstitialAdManager.swift
//  PayMaya
//
//  Created by <PERSON> on 12/19/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import GoogleMobileAds
import Injector
import ReactiveSwift
import UIKit

/// Manager class specifically for handling Google Mobile Ads interstitial ads
class GoogleInterstitialAdManager {
    // MARK: - Constants
    private enum Constants {
        static let testInterstitialAdUnitId = "/23303781892/App_Interstitial_Home_OutOfPage_iOS_320x480"
        static let defaultShowInterval: TimeInterval = 30.0
    }

    // MARK: - Properties
    @Inject private var googleAdsHelper: GoogleAdsHelper

    private var interstitialAdTimer: Timer?
    private var isInitialized = false

    // Configuration
    private var showInterval: TimeInterval = Constants.defaultShowInterval
    private var adUnitId: String = Constants.testInterstitialAdUnitId

    // State tracking
    private var isViewVisible = false

    // MARK: - Initialization

    deinit {
        stopInterstitialAdTimer()
    }

    // MARK: - Public Configuration Methods

    /// Configure the interstitial ad manager
    /// - Parameters:
    ///   - adUnitId: The ad unit ID to use (defaults to test ad unit)
    ///   - showInterval: Time interval between automatic ad displays (default: 30 seconds)
    func setup(
        viewController: UIViewController,
        adUnitId: String = Constants.testInterstitialAdUnitId,
        showInterval: TimeInterval = Constants.defaultShowInterval
    ) {
        self.adUnitId = adUnitId
        self.showInterval = showInterval

        guard !isInitialized else {
            return
        }

        // Initialize the Google Ads SDK
        googleAdsHelper.initialize()

        isInitialized = true
    }

    /// Start showing interstitial ads automatically
    /// Call this when the view becomes visible
    func startAutomaticAds() {
        #if DEBUG
        guard isInitialized else {
            return
        }

        isViewVisible = true

        startInterstitialAdTimer()
        #endif
    }

    /// Stop showing interstitial ads automatically
    /// Call this when the view becomes hidden
    func stopAutomaticAds() {
        isViewVisible = false
        stopInterstitialAdTimer()
    }

    // MARK: - Private Implementation Methods

    private func startInterstitialAdTimer() {
        stopInterstitialAdTimer()

        interstitialAdTimer = Timer.scheduledTimer(withTimeInterval: showInterval, repeats: false) { [weak self] _ in
            guard let self = self else { return }

            // Find the current top view controller
            if let topViewController = findTopViewController() {
                showInterstitialAd(from: topViewController)
            }
        }
    }

    private func stopInterstitialAdTimer() {
        interstitialAdTimer?.invalidate()
        interstitialAdTimer = nil
    }

    private func showInterstitialAd(from viewController: UIViewController) {
        googleAdsHelper.loadAndShowInterstitialAd(
            from: viewController,
            adUnitId: adUnitId
        ) { [weak self] _ in
            guard let self = self else { return }

            // Schedule next ad if we haven't reached the limit and view is still visible
            scheduleNextAd()
        }
    }

    private func scheduleNextAd() {
        #if DEBUG
        guard isViewVisible else {
            return
        }

        // Wait a bit before scheduling the next ad
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            self?.startInterstitialAdTimer()
        }
        #endif
    }

    private func findTopViewController() -> UIViewController? {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return nil
        }

        return window.rootViewController?.topMostViewController()
    }
}

// MARK: - UIViewController Extension
private extension UIViewController {
    func topMostViewController() -> UIViewController {
        if let presented = presentedViewController {
            return presented.topMostViewController()
        }

        if let navigationController = self as? UINavigationController {
            return navigationController.visibleViewController?.topMostViewController() ?? self
        }

        if let tabBarController = self as? UITabBarController {
            return tabBarController.selectedViewController?.topMostViewController() ?? self
        }

        return self
    }
}
