//
//  GoogleAdsHelper.swift
//  PayMaya
//
//  Created by <PERSON> on 06/09/25.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import UIKit
import GoogleMobileAds
import Injector
import ReactiveSwift

/// Helper class to handle loading, initializing, and showing of ads.
/// This class provides methods for banner ads, interstitial ads, and rewarded ads.
class GoogleAdsHelper {
    
    // MARK: - Constants
    private enum Constants {
        static let tag = "GoogleAdsHelper"
        static let testDeviceId = "4CE16389D4357FABD6041DD93D33B183"
    }
    
    // MARK: - Properties
    @Inject private var consentManager: GoogleMobileAdsConsentManager
    
    private var interstitialAd: GADInterstitialAd?
    private var isInitialized = false
    
    // MARK: - Initialization
    init() {
        // Dependency injection will automatically inject consentManager
    }
    
    // MARK: - Private Methods
    private func log(_ message: String) {
        #if !PROD_TARGET && !SANDBOX_TARGET
        print("[\(Constants.tag)] \(message)")
        #endif
    }
    
    // MARK: - Public Methods
    
    /// Initialize the Mobile Ads SDK
    /// This should be called once, preferably in the Application class or during app startup
    func initialize() {
        guard !isInitialized else { return }
        
        GADMobileAds.sharedInstance().start { [weak self] initializationStatus in
            let adapterStatuses = initializationStatus.adapterStatusesByClassName
            self?.log("Ads initialization complete: \(adapterStatuses)")
            self?.isInitialized = true
        }
    }
    
    /// Requests user consent for personalized ads
    /// - Parameters:
    ///   - viewController: The view controller to present consent form from
    ///   - forceShow: Force showing the consent form even if not required (for testing)
    ///   - onConsentComplete: Callback when consent gathering is complete
    func requestConsent(
        from viewController: UIViewController,
        forceShow: Bool = false,
        onConsentComplete: @escaping (Bool) -> Void
    ) {
        // When forceShow is true, always show the consent form regardless of build type
        if forceShow {
            log("Force showing consent form for testing")
            
            // Create debug settings with force testing enabled
            let debugSettings = consentManager.createDebugSettings(
                debugGeography: .EEA,
                testDeviceHashedIds: [Constants.testDeviceId]
            )
            
            // Request consent with debug settings
            consentManager.gatherConsent(
                from: viewController,
                debugSettings: debugSettings,
                forceShow: true
            ) { [weak self] error in
                self?.handleConsentResult(error: error, onConsentComplete: onConsentComplete)
            }
            return
        }
        
        // For normal operation (not forcing consent)
        // In debug builds, we can skip consent for easier testing
        // In release builds, we always show consent as required by regulations
        #if DEBUG
        let shouldShowConsent = false
        #else
        let shouldShowConsent = true
        #endif
        
        if !shouldShowConsent {
            log("Skipping consent in debug mode. Proceeding as if consent was given.")
            onConsentComplete(true)
            return
        }
        
        // Create debug settings for testing in debug builds
        #if DEBUG
        let debugSettings = consentManager.createDebugSettings(
            debugGeography: .EEA,
            testDeviceHashedIds: []
        )
        #else
        let debugSettings: UMPDebugSettings? = nil
        #endif
        
        #if DEBUG
        log("Requesting consent with buildType=debug")
        #else
        log("Requesting consent with buildType=release")
        #endif
        
        consentManager.gatherConsent(
            from: viewController,
            debugSettings: debugSettings,
            forceShow: false
        ) { [weak self] error in
            self?.handleConsentResult(error: error, onConsentComplete: onConsentComplete)
        }
    }
    
    /// Helper method to handle consent result
    private func handleConsentResult(error: Error?, onConsentComplete: @escaping (Bool) -> Void) {
        if let error = error {
            log("Consent form error: \(error.localizedDescription)")
        } else {
            log("Consent form dismissed successfully")
        }
        
        let canRequestAds = consentManager.canRequestAds()
        log("Can request ads: \(canRequestAds)")
        onConsentComplete(canRequestAds)
    }
    
    /// Creates a GADRequest object for loading ads
    /// - Returns: GADRequest object
    func createAdRequest() -> GADRequest {
        return GADRequest()
    }
    
    /// Shows privacy options to the user
    /// - Parameters:
    ///   - viewController: The view controller to present privacy options from
    ///   - onComplete: Callback when privacy options form is dismissed
    func showPrivacyOptions(
        from viewController: UIViewController,
        onComplete: @escaping () -> Void = {}
    ) {
        consentManager.showPrivacyOptionsForm(from: viewController) { [weak self] error in
            if let error = error {
                self?.log("Privacy options error: \(error.localizedDescription)")
            }
            onComplete()
        }
    }
    
    /// Checks if ads can be requested based on user consent
    /// - Returns: true if ads can be requested, false otherwise
    func canRequestAds() -> Bool {
        // In debug builds, we can always show ads for easier testing
        // In release builds, we respect the actual consent status
        #if DEBUG
        return true
        #else
        return consentManager.canRequestAds()
        #endif
    }

    /// Load and display a banner ad in the provided container
    /// - Parameters:
    ///   - adContainer: UIView where the ad will be displayed
    ///   - adUnitId: The ad unit ID for the banner ad
    ///   - adSize: The size of the banner ad (default is GADAdSizeBanner)
    ///   - listener: Optional listener for ad events
    /// - Returns: The created GADBannerView or nil if consent is not given
    @discardableResult
    func loadBannerAd(
        in adContainer: UIView,
        adUnitId: String,
        adSize: GADAdSize = GADAdSizeBanner,
        listener: GADBannerViewDelegate? = nil
    ) -> GADBannerView? {
        #if !DEBUG
        guard consentManager.canRequestAds() else {
            log("Cannot load banner ad: User consent not given")
            return nil
        }
        #endif

        let bannerView = GADBannerView(adSize: adSize)
        bannerView.adUnitID = adUnitId
        bannerView.delegate = listener

        // Find the root view controller
        if let rootViewController = adContainer.findViewController() {
            bannerView.rootViewController = rootViewController
        }

        adContainer.addSubview(bannerView)
        bannerView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            bannerView.centerXAnchor.constraint(equalTo: adContainer.centerXAnchor),
            bannerView.centerYAnchor.constraint(equalTo: adContainer.centerYAnchor)
        ])

        bannerView.load(createAdRequest())
        return bannerView
    }

    /// Load an interstitial ad
    /// - Parameters:
    ///   - adUnitId: The ad unit ID for the interstitial ad
    ///   - onComplete: Callback when ad is loaded or failed to load
    func loadInterstitialAd(
        adUnitId: String,
        onComplete: @escaping (Bool) -> Void
    ) {
        #if !DEBUG
        guard consentManager.canRequestAds() else {
            log("Cannot load interstitial ad: User consent not given")
            onComplete(false)
            return
        }
        #endif

        GADInterstitialAd.load(
            withAdUnitID: adUnitId,
            request: createAdRequest()
        ) { [weak self] ad, error in
            if let error = error {
                self?.log("Interstitial ad failed to load: \(error.localizedDescription)")
                self?.interstitialAd = nil
                onComplete(false)
            } else {
                self?.log("Interstitial ad loaded")
                self?.interstitialAd = ad
                onComplete(true)
            }
        }
    }

    /// Load and show an interstitial ad
    /// - Parameters:
    ///   - viewController: The view controller to present the ad from
    ///   - adUnitId: The ad unit ID for the interstitial ad
    ///   - onComplete: Callback when ad is shown or failed
    func loadAndShowInterstitialAd(
        from viewController: UIViewController,
        adUnitId: String,
        onComplete: @escaping (Bool) -> Void
    ) {
        log("Loading interstitial ad with ID: \(adUnitId)")

        loadInterstitialAd(adUnitId: adUnitId) { [weak self] success in
            self?.log("Ad load result: \(success)")

            if success {
                self?.log("Showing interstitial ad")

                guard let ad = self?.interstitialAd else {
                    self?.log("Failed to show ad: interstitialAd is nil")
                    onComplete(false)
                    return
                }

                ad.fullScreenContentDelegate = InterstitialAdDelegate(onComplete: onComplete)
                ad.present(fromRootViewController: viewController)
            } else {
                self?.log("Failed to load ad")
                onComplete(false)
            }
        }
    }
}

// MARK: - Helper Extensions
private extension UIView {
    func findViewController() -> UIViewController? {
        if let nextResponder = self.next as? UIViewController {
            return nextResponder
        } else if let nextResponder = self.next as? UIView {
            return nextResponder.findViewController()
        } else {
            return nil
        }
    }
}

// MARK: - InterstitialAdDelegate
private class InterstitialAdDelegate: NSObject, GADFullScreenContentDelegate {
    private let onComplete: (Bool) -> Void

    init(onComplete: @escaping (Bool) -> Void) {
        self.onComplete = onComplete
        super.init()
    }

    func adDidDismissFullScreenContent(_ ad: GADFullScreenPresentingAd) {
        print("[GoogleAdsHelper] Ad dismissed")
        onComplete(true)
    }

    func ad(_ ad: GADFullScreenPresentingAd, didFailToPresentFullScreenContentWithError error: Error) {
        print("[GoogleAdsHelper] Ad failed to show: \(error.localizedDescription)")
        onComplete(false)
    }
}
