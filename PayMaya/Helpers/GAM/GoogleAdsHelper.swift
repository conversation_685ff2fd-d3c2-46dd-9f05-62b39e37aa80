//
//  GoogleAdsHelper.swift
//  PayMaya
//
//  Created by <PERSON> on 06/09/25.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import GoogleMobileAds
import Injector
import ReactiveSwift
import UIKit

/// Helper class to handle loading, initializing, and showing of ads.
/// This class provides methods for banner ads, interstitial ads, and rewarded ads.
class GoogleAdsHelper {
    // MARK: - Constants
    private enum Constants {
        static let tag = "GoogleAdsHelper"
        static let testDeviceId = "4CE16389D4357FABD6041DD93D33B183"
    }

    // MARK: - Properties

    private var interstitialAd: InterstitialAd?
    private var interstitialDelegate: InterstitialAdDelegate?
    private var isInitialized = false

    // MARK: - Private Methods
    private func log(_ message: String) {
        #if !PROD_TARGET && !SANDBOX_TARGET
        print("[\(Constants.tag)] \(message)")
        #endif
    }

    // MARK: - Public Methods

    /// Initialize the Mobile Ads SDK
    /// This should be called once, preferably in the Application class or during app startup
    func initialize() {
        guard !isInitialized else { return }

        MobileAds.shared.start { [weak self] initializationStatus in
            let adapterStatuses = initializationStatus.adapterStatusesByClassName
            self?.log("Ads initialization complete: \(adapterStatuses)")
            self?.isInitialized = true
        }
    }

    /// Creates a GADRequest object for loading ads
    /// - Returns: GADRequest object
    func createAdRequest() -> Request {
        return Request()
    }

    /// Load an interstitial ad
    /// - Parameters:
    ///   - adUnitId: The ad unit ID for the interstitial ad
    ///   - onComplete: Callback when ad is loaded or failed to load
    func loadInterstitialAd(
        adUnitId: String,
        onComplete: @escaping (Bool) -> Void
    ) {
        InterstitialAd.load(
            with: adUnitId,
            request: createAdRequest()
        ) { [weak self] ad, error in
            if let error = error {
                self?.log("Interstitial ad failed to load: \(error.localizedDescription)")
                self?.interstitialAd = nil
                onComplete(false)
            } else {
                self?.log("Interstitial ad loaded")
                self?.interstitialAd = ad
                onComplete(true)
            }
        }
    }

    /// Load and show an interstitial ad
    /// - Parameters:
    ///   - viewController: The view controller to present the ad from
    ///   - adUnitId: The ad unit ID for the interstitial ad
    ///   - onComplete: Callback when ad is shown or failed
    func loadAndShowInterstitialAd(
        from viewController: UIViewController,
        adUnitId: String,
        onComplete: @escaping (Bool) -> Void
    ) {
        log("Loading interstitial ad with ID: \(adUnitId)")

        loadInterstitialAd(adUnitId: adUnitId) { [weak self] success in
            self?.log("Ad load result: \(success)")

            if success {
                self?.log("Showing interstitial ad")

                guard let ad = self?.interstitialAd else {
                    self?.log("Failed to show ad: interstitialAd is nil")
                    onComplete(false)
                    return
                }

                self?.interstitialDelegate = InterstitialAdDelegate(onComplete: onComplete)
                ad.fullScreenContentDelegate = self?.interstitialDelegate
                ad.present(from: viewController)
            } else {
                self?.log("Failed to load ad")
                onComplete(false)
            }
        }
    }
}

// MARK: - Helper Extensions
private extension UIView {
    func findViewController() -> UIViewController? {
        if let nextResponder = self.next as? UIViewController {
            return nextResponder
        } else if let nextResponder = self.next as? UIView {
            return nextResponder.findViewController()
        } else {
            return nil
        }
    }
}

// MARK: - InterstitialAdDelegate
private class InterstitialAdDelegate: NSObject, FullScreenContentDelegate {
    private let onComplete: (Bool) -> Void

    init(onComplete: @escaping (Bool) -> Void) {
        self.onComplete = onComplete
        super.init()
    }

    func adDidDismissFullScreenContent(_ ad: FullScreenPresentingAd) {
        print("[GoogleAdsHelper] Ad dismissed")
        onComplete(true)
    }

    func ad(_ ad: FullScreenPresentingAd, didFailToPresentFullScreenContentWithError error: Error) {
        print("[GoogleAdsHelper] Ad failed to show: \(error.localizedDescription)")
        onComplete(false)
    }
}
