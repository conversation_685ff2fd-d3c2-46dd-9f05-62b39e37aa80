//
//  DataProvider.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 07/06/2017.
//  Copyright © 2017 PayMaya Philippines, Inc. All rights reserved.
//

import Alamofire
import Analytics
import APIProvider
import ConfigurationService
import ConsentOptOutService
import Error
import Foundation
import Injector
import Logger
import MayaCoreData
import MayaFormatterManager
import Moya
import Raven
import RavenCalendar
import RavenContacts
import RavenLocation
import RavenPhotos
import ShieldFraud
import StoreProvider
import TrustKit

#if !UNIT_TEST_TARGET
import MayaBrightnessManager
#endif

/// Manages all dependencies needed by the app and put them in the appropriate container for later injection
struct DependencyManager {
    var apiProvider: APIProvider!
    var storeProvider: StoreProvider!
    var configurationService: ConfigurationService!
    var configurationServiceV2: ConfigurationServiceV2!
    var notificationService: NotificationService!
    var dependencySetupHelper: DependencySetupHelper!
    var orientationController: OrientationController!
    var analyticsHelper: AnalyticsHelper!
    var profileTrackingManager: ProfileTrackingManager!

    var analyticsService: AnalyticsService {
        return analyticsHelper.analyticsService
    }

    init(application: UIApplication, launchOptions: [UIApplication.LaunchOptionsKey: Any]?, window: UIWindow?) {
        ContainerWrapper.initialize()
        setContainerScopedDependencies(application: application, launchOptions: launchOptions, window: window)
        setGraphScopedDependencies(application: application, launchOptions: launchOptions, window: window)
        ContainerWrapper.shared.synchronize()

        /// Utility properties for easy access
        storeProvider = ContainerWrapper.shared.resolve(StoreProvider.self)
        apiProvider = ContainerWrapper.shared.resolve(APIProvider.self)
        configurationService = ContainerWrapper.shared.resolve(ConfigurationService.self)
        configurationServiceV2 = ContainerWrapper.shared.resolve(ConfigurationServiceV2.self)
        notificationService = ContainerWrapper.shared.resolve(NotificationService.self)
        dependencySetupHelper = ContainerWrapper.shared.resolve(DependencySetupHelper.self)
        orientationController = ContainerWrapper.shared.resolve(OrientationController.self)
        analyticsHelper = ContainerWrapper.shared.resolve(AnalyticsHelper.self)
        profileTrackingManager = ContainerWrapper.shared.resolve(ProfileTrackingManager.self)
    }

    /// Dependencies have global scope and live for the entire app lifetime
    fileprivate func setContainerScopedDependencies(application: UIApplication, launchOptions: [UIApplication.LaunchOptionsKey: Any]?, window: UIWindow?) {
        ContainerWrapper.shared.register(type: CrashlyticsLogger.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return CrashlyticsLogger()
        })

        ContainerWrapper.shared.register(type: ErrorLogger.self, scope: ContainerWrapper.getContainerScope(), { resolver in
            let crashlyticsLogger = resolver.resolve(CrashlyticsLogger.self)!
            return ErrorLogger(with: { error in
                crashlyticsLogger.logNonFatalError(error)
            })
        })

        ContainerWrapper.shared.register(type: StoreProvider.self, scope: ContainerWrapper.getContainerScope(), { _ in
            let previousBuildNumber = UserDefaultsStore.getBuildNumber()

            let userDefaultsStore = UserDefaultsStore(configuration: StoreConfiguration(previousBuildNumber: previousBuildNumber,
                                                                                        migrator: UserDefaultsStoreMigrator()))

            let encryptedDefaultsStore = EncryptedDefaultsStore(configuration:
                                                                 StoreConfiguration(
                                                                    previousBuildNumber: previousBuildNumber,
                                                                    migrator: EncryptedDefaultsStoreMigrator()),
                                                                storeName: "Paymaya-Defaults")

            let propertyListStore = PropertyListStore(configuration:
                                                       StoreConfiguration(previousBuildNumber: previousBuildNumber,
                                                                          migrator: PropertyListStoreMigrator()))

            let databaseStore = DatabaseStore(configuration:
                                                  StoreConfiguration(
                                                    previousBuildNumber: previousBuildNumber, migrator: DatabaseStoreMigrator()),
                                              dataStack: MayaCoreDataStack())

            let fileStore = FileStore(configuration:
                                        StoreConfiguration(previousBuildNumber: previousBuildNumber,
                                                           migrator: FileStoreMigrator()))

            return StoreProvider([userDefaultsStore, encryptedDefaultsStore, propertyListStore, databaseStore, fileStore])
        })

        ContainerWrapper.shared.register(type: APIProvider.self, scope: ContainerWrapper.getContainerScope(), { resolver in
            let storeProvider = resolver.resolve(StoreProvider.self)!
            #if !UNIT_TEST_TARGET && PINNING_ENABLED
            let delegate = CustomSessionDelegate()
            #else
            let delegate = SessionDelegate()
            #endif
            let session = MoyaAPIProvider.getSession(delegate: delegate)

            let identifier = APIProviderStorableIdentifier(
                clientId: PropertyListStoreId.clientId.rawValue,
                clientSecret: PropertyListStoreId.clientSecret.rawValue,
                tokenUrl: PropertyListStoreId.tokenUrl.rawValue,
                clientUrl: PropertyListStoreId.clientUrl.rawValue,
                cardUrl: PropertyListStoreId.cardUrl.rawValue,
                payBillsUrl: PropertyListStoreId.billspayUrl.rawValue,
                configurationUrl: PropertyListStoreId.configurationUrl.rawValue,
                securityCenterUrl: PropertyListStoreId.securityCenterBaseURL.rawValue,
                authorization: EncryptedDefaultsStoreId.authorization.rawValue
            )

            let plugins: [PluginType] = [
                APIBasePlugin(),
                APIAuthorizationPlugin(store: storeProvider),
                APIDCRNPlugin(),
                APIDDPSessionIdPlugin(),
                APIAuthenticationPlugin(store: storeProvider),
                APIPersistencePlugin(store: storeProvider),
                APICachePolicyPlugin(),
                APIRequestTokenAndBannerCorrelationPlugin(store: storeProvider),
                APIRequestReferencePlugin(),
                APIShieldPlugin(),
                APIAppsFlyerPlugin(),
                APIAuthorizationZolozKYCPlugin(store: storeProvider),
                APIPushApprovalPlugin(store: storeProvider),
                APIAuthorizationShortenedPlugin(store: storeProvider),
                APIAuthorizationUpdateProfilePlugin(store: storeProvider),
                APIDeviceIndentifierPlugin(),
                APICachedSessionTokenPlugin(),
                APIBrandNamePlugin(),
                APIDeviceModelPlugin(),
                APISecurityCenterPlugin(store: storeProvider)
            ]

            return MoyaAPIProvider(plugins, session: session, identifier: identifier)
        })

        ContainerWrapper.shared.register(type: ConfigurationService.self, scope: ContainerWrapper.getContainerScope(), { _ in
            let identifier = ConfigurationServiceStorableIdentifier(appConfig: EncryptedDefaultsStoreId.appConfig.rawValue,
                                                                    remoteConfig: EncryptedDefaultsStoreId.remoteConfig.rawValue,
                                                                    appConfigurationSettingsLastUpdatedOn:
                                                                        UserDefaultsStoreId.appConfigurationSettingsLastUpdatedOn.rawValue)
            return ConfigurationService(identifier: identifier,
                                        metadataDateFormatter: DateFormatters.getDateFormatter(for: .lastModifiedHeader),
                                        completion: { configurationService in
                if let application = UIApplication.shared as? TimerUIApplication {
                    application.setTimeout(timeoutInMinutes: configurationService.appLocalTimeoutDuration)
                }
            })
        })

        ContainerWrapper.shared.register(type: ConfigurationServiceV2.self, scope: ContainerWrapper.getContainerScope()) { _ in
            let bundleID = Bundle.main.object(forInfoDictionaryKey: Constants.Keys.BuildInfo.bundleID.rawValue) as? String ?? ""
            let versionNumber = Bundle.main.object(forInfoDictionaryKey: Constants.Keys.BuildVersion.version.rawValue) as? String ?? ""
            let buildNumber = Bundle.main.object(forInfoDictionaryKey: Constants.Keys.BuildVersion.build.rawValue) as? Int ?? 0
            let osVersion = UIDevice.current.semanticSystemVersion

            let attributes = [
                SplitConstants.Attributes.Key.osName: SplitConstants.Attributes.Value.ios,
                SplitConstants.Attributes.Key.osVersion: osVersion,
                SplitConstants.Attributes.Key.appID: bundleID,
                SplitConstants.Attributes.Key.versionNumber: versionNumber,
                SplitConstants.Attributes.Key.buildNumber: buildNumber
            ]

            let service = ConfigurationServiceV2()
            service.setClientAttributes(attributes)

            return service
        }

        ContainerWrapper.shared.register(type: NotificationService.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return NotificationService()
        })

        ContainerWrapper.shared.register(type: ReviewService.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return ReviewService()
        })

        ContainerWrapper.shared.register(type: AppThemeService.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return AppThemeService()
        })

        ContainerWrapper.shared.register(type: OrientationController.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return OrientationController()
        })

        ContainerWrapper.shared.register(type: ResumeSessionService.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return ResumeSessionService()
        })

        ContainerWrapper.shared.register(
            type: LocationAuthorizerServiceProtocol.self,
            scope: ContainerWrapper.getContainerScope()
        ) { _ in
            return LocationAuthorizerService()
        }

        ContainerWrapper.shared.register(
            type: LocationCheckerServiceProtocol.self,
            scope: ContainerWrapper.getContainerScope()
        ) { _ in
            return LocationCheckerService()
        }

        ContainerWrapper.shared.register(type: ShieldDataCollectorProtocol.self, scope: ContainerWrapper.getContainerScope(), { resolver in
            let storeProvider = resolver.resolve(StoreProvider.self)!
            let propertyListStore = storeProvider.target(PropertyListStore.self)!

            guard let shieldSiteId = propertyListStore.read(PropertyListStoreId.shieldSiteID).value as? String,
            let shieldSecretKey = propertyListStore.read(PropertyListStoreId.shieldSecretKey).value as? String
            else {
                return ShieldDataCollector()
            }

            return ShieldDataCollector(siteId: shieldSiteId, secretKey: shieldSecretKey)
        })

        ContainerWrapper.shared.register(type: ToggleBalanceShownService.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return ToggleBalanceShownService()
        })

        ContainerWrapper.shared.register(type: FlutterEngineManager.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return FlutterEngineManager()
        })

        ContainerWrapper.shared.register(type: MayaFormatterManager.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MayaFormatterManager()
        })

        ContainerWrapper.shared.register(type: DependencySetupHelper.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return DependencySetupHelper(window: window)
        })

        ContainerWrapper.shared.register(type: AnalyticsService.self, scope: ContainerWrapper.getContainerScope(), { resolver in
            let helper = resolver.resolve(DependencySetupHelper.self)!
            return AnalyticsService(providers: helper.analyticsProviders)
        })

        ContainerWrapper.shared.register(type: AnalyticsHelper.self, scope: ContainerWrapper.getContainerScope(), { _ in
            AnalyticsUtils.initialize()
            return AnalyticsHelper(analyticsUtils: AnalyticsUtils.shared)
        })

        ContainerWrapper.shared.register(type: ProfileTrackingManager.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return ProfileTrackingManager()
        })

        #if !UNIT_TEST_TARGET
        ContainerWrapper.shared.register(type: MayaBrightnessManager.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return MayaBrightnessManager()
        })
        #endif

        ContainerWrapper.shared.register(type: PerformanceMonitoringServiceProtocol.self, scope: ContainerWrapper.getContainerScope(), { _ in
            return PerformanceMonitoringService()
        })

        ContainerWrapper.shared.register(type: RavenWrapper.self, scope: ContainerWrapper.getContainerScope(), { _ in
            Raven.shared.setup()
            Raven.shared.load([RavenCalendar(), RavenContacts(), RavenLocation(), RavenPhotos()])
            return RavenWrapper()
        })
    }

    /// Dependencies are initialized when needed and is tied to the lifetime of the injecting object
    fileprivate func setGraphScopedDependencies(application: UIApplication, launchOptions: [UIApplication.LaunchOptionsKey: Any]?, window: UIWindow?) {
        ContainerWrapper.shared.register(type: TouchIDManager.self, { _ in
            return TouchIDManager()
        })

        ContainerWrapper.shared.register(type: CredentialsManager.self, { _ in
            return CredentialsManager()
        })

        ContainerWrapper.shared.register(type: AuthenticationProviderManager.self, { _ in
            return AuthenticationProviderManager()
        })

        ContainerWrapper.shared.register(type: SignoutService.self, { _ in
            return SignoutService()
        })

        ContainerWrapper.shared.register(type: ContactsImporter.self, { _ in
            return ContactsImporter()
        })

        ContainerWrapper.shared.register(type: BackgroundSessionManager.self, { _ in
            return BackgroundSessionManager()
        })

        ContainerWrapper.shared.register(type: FlutterSessionManager.self, { _ in
            return FlutterSessionManager()
        })

        ContainerWrapper.shared.register(type: PushApprovalProvider.self) { _ in
            return PushApprovalProvider()
        }

        ContainerWrapper.shared.register(type: KountDataCollectorProtocol.self, { resolver in
            let storeProvider = resolver.resolve(StoreProvider.self)!
            let propertyListStore = storeProvider.target(PropertyListStore.self)!
            let merchantID = Int(propertyListStore.read(PropertyListStoreId.kountMerchantID).value as? String ?? "")!

            #if !UNIT_TEST_TARGET
            return KountDataCollector(merchantID: merchantID)
            #else
            return MockKountDataCollectorProtocol()
            #endif
        })

        ContainerWrapper.shared.register(type: AdBannerServiceProtocol.self) { _ in
           return CleverTapAdBannerService()
        }

        // MARK: - Google Ads Dependencies
        ContainerWrapper.shared.register(type: GoogleMobileAdsConsentManager.self) { _ in
            return GoogleMobileAdsConsentManager()
        }

        ContainerWrapper.shared.register(type: GoogleAdsHelper.self) { _ in
            return GoogleAdsHelper()
        }

        ContainerWrapper.shared.register(type: GoogleInterstitialAdManager.self) { _ in
            return GoogleInterstitialAdManager()
        }

        ContainerWrapper.shared.register(type: GoogleInterstitialAdsHelper.self) { _ in
            return GoogleInterstitialAdsHelper()
        }

        ContainerWrapper.shared.register(type: GoogleBannerAdsCarouselViewController.self) { _ in
            return GoogleBannerAdsCarouselViewController()
        }

        ContainerWrapper.shared.register(type: GoogleBannerAdsCarouselViewModel.self) { _ in
            return GoogleBannerAdsCarouselViewModel()
        }

        ContainerWrapper.shared.register(type: QRGeneratorUtilityProtocol.self) { _ in
            #if !UNIT_TEST_TARGET
            return QRGeneratorUtility()
            #else
            return MockQRGeneratorUtilityProtocol()
            #endif
        }

        ContainerWrapper.shared.register(type: QRParserUtilityProtocol.self) { _ in
            #if !UNIT_TEST_TARGET
            return QRParserUtility()
            #else
            return MockQRParserUtilityProtocol()
            #endif
        }

        ContainerWrapper.shared.register(type: DataLoaderProtocol.self) { _ in
            return DataLoader()
        }

        ContainerWrapper.shared.register(type: InboxManagerProtocol.self, { _ in
            #if AUTOMATION_TARGET
            let apiProvider = ContainerWrapper.shared.resolve(APIProvider.self)
            StubInboxProvider.shared = StubInboxProvider(apiProvider: apiProvider, with: { inboxMessage -> Bool in
                return InboxTag(rawValue: inboxMessage.tag ?? "") != nil
            })
            #endif

            #if AUTOMATION_TARGET || UNIT_TEST_TARGET
                return InboxManager(StubInboxProvider.shared!)
            #else
                return InboxManager(CleverTapInboxProvider(with: { inboxMessage -> Bool in
                    return InboxTag(rawValue: inboxMessage.tag ?? "") != nil
                }))
            #endif
        })

        ContainerWrapper.shared.register(type: ConsentOptOutService.self, { _ in
            return ConsentOptOutService()
        })

        ContainerWrapper.shared.register(type: AnalyticsDurationManager.self, { _ in
            let analyticsService = ContainerWrapper.shared.resolve(AnalyticsService.self)
            return AnalyticsDurationManager(analyticsService: analyticsService)
        })
    }
}
