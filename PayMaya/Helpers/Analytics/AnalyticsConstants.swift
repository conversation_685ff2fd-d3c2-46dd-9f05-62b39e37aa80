//
//  AnalyticsConstants.swift
//  PayMaya
//
//  Created by <PERSON> on 3/14/23.
//  Copyright © 2023 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation

enum AnalyticsConstants {}

enum GlimpseParameter: String {
    case applicationID = "aid"
    case eventName = "ea"
    case userID = "uid"
}

enum ClevertapProfile: String {
    case identity = "Identity"
    case phone = "Phone"
    case email = "Email"
}

enum FirebaseUserProperty: String {
    case accountType = "account_type"
    case buildNumber = "build_number"
}

enum SendProfileModule: String {
    case login = "LOGIN"
    case loginOTP = "LOGIN_OTP"
    case registrationOTP = "REGISTRATION_OTP"
    case sessionTimeout = "SESSION_TIMEOUT"
    case faceChallenge = "FACE_CHALLENGE"
    case signout = "SIGNOUT"
}

extension AnalyticsConstants {
    enum Registration: String {
        case startAccount = "start_account"
        case setupLogin = "setup_login"
        case dataPrivacy = "data_privacy"
        case otp = "one_time_pin"
        case secondaryConsents = "secondary_consents"
        case registration = "registration page"
    }

    enum Module: String {
        case registration
        case servicesDashboard = "services_dashboard"
        case shop
    }

    enum Services: String {
        case moreClicked = "more_clicked"
        case navigationTapped = "nav_tap"
    }

    enum BankPullFunds: String {
        case confirmation
        case form
    }

    enum EKYC: String {
        case consumerKyc1 = "CONSUMER_KYC1"
        case consumerRekyc = "CONSUMER_REKYC"
    }

    enum Wallet {
        enum EasyCreditBanner: String {
            case noApplication = "No Application"
        }
        enum Item: String {
            case autoCashIn = "auto_cash_in"
            case bsp
            case cashIn = "cash_in"
            case easyCredit = "easy_credit"
            case helpCenter = "help_center"
            case missions
            case rewards
            case seeAllTransaction = "see_all_transactions"
            case sendMoney = "send_money"
            case serviceAdvisories = "service_advisories"
            case showBalance = "show_balance"
            case transactionReceipt = "transaction_receipt"

            enum ButtonType: String {
                case button
                case link
            }

            enum Category: String {
                case walletFunctions = "wallet_functions"
                case footer = "footer"
            }

            var buttonType: ButtonType {
                switch self {
                case .bsp, .helpCenter, .serviceAdvisories: .link
                default: .button
                }
            }

            var category: Category {
                switch self {
                case .bsp, .helpCenter, .serviceAdvisories: .footer
                default: .walletFunctions
                }
            }

            var attributes: [AnalyticsAttributeKey: String] {
                [
                    .button: rawValue,
                    .buttonType: buttonType.rawValue,
                    .category: category.rawValue
                ]
            }
        }
    }

    enum DashboardNavigation: String {
        case helpCenter = "help_center"
        case profile = "profile_menu"
        case inbox

        var attributes: [AnalyticsAttributeKey: String] { [.button: rawValue] }
    }
}

extension AnalyticsConstants {
    struct ActionFunction {
        func expanded(_ status: Bool) -> String {
            return status ? "expand" : "not expand"
        }

        func checked(_ status: Bool) -> String {
            return status ? "check" : "uncheck"
        }

        func shown(_ status: Bool) -> String {
            return status ? "show" : "hide"
        }

        func status(_ status: Bool) -> String {
            return status ? "1" : "0"
        }

        func autofill(_ status: Bool) -> String {
            return status ? "autofill by OS" : "manual"
        }

        func boolean(_ value: Bool) -> String {
            return value ? "T" : "F"
        }
    }

    struct BottomSheet {
        static let verifyIdentity = "Please verify your identity (Bottom sheet)"
        static let passwordInfo = "Tips for a strong password (Bottom sheet)"
        static let tinboSIM = "I don’t have a Philippine SIM (Bottom sheet)"
        static let registrationSuccess = "You're in! Welcome to Maya (Bottom sheet)"
        static let registrationV2Success = "Registration Success (Bottom sheet)"
        static let inviteCodeSuccess = "Code looks good! (Bottom sheet)"
        static let locationPersmission = "Location Permission (Bottom sheet)"
        static let passwordTips = "Password Tips (Bottom sheet)"
        static let mayaPHTermsAndConditions = "Maya PH Terms and Conditions (Bottom sheet)"
        static let mayaPHPrivacyPolicy = "Maya PH Privacy Policy (Bottom sheet)"
        static let mayaBankTermsAndConditions = "Maya Bank Terms and Conditions (Bottom sheet)"
        static let mayaBankPrivacyPolicy = "Maya Bank Privacy Policy (Bottom sheet)"
        static let prominentDisclosure = "Prominent Disclosure (Bottom sheet)"
    }

    struct ButtonText {
        static let clear = "Clear"
        static let hideConfirmPassword = "Hide confirm password"
        static let hideNewPassword = "Hide new password"
        static let showPassword = "Show password"
        static let hidePassword = "Hide password"
        static let passwordInfo = "Password tips"
        static let privacyPolicy = "Privacy policy"
        static let privacyPolicyBank = "Privacy policy Maya Bank"
        static let seeAll = "See all"
        static let termsAndConditions = "Terms and conditions"
        static let termsAndConditionsBank = "Terms and conditions Maya Bank"
        static let tinboSIM = "I don’t have a Philippine SIM"
        static let enableAllConsent = "Enable all data personalization"
        static let noLegalMiddleName = "I have no legal middle name"
        static let helpCenter = "Help center"
        static let savings = "Savings"
        static let switchAccount = "Switch account"
        static let inviteCode = "Invite code"
        static let allow = "Allow"
        static let deny = "Deny"
        static let registerNow = "Register now"
        static let login = "Login"
        static let back = "Back"
        static let verify = "Verify"
        static let resendCode = "Resend Code"
        static let goToHome = "Go to home"
        static let upgradeYourAccount = "Upgrade your account"
        static let `continue` = "Continue"
        static let noMiddleName = "I have no legal middle name"
        static let showPasswordTips = "Show password tips"
        static let allowData = "Allow data personalization"
    }

    struct Dialog {
        static let mfaNoFaceData = "No face data - OTP fallback (Dialog)"
        static let mfaFaceMismatch = "Face authentication failed (Dialog)"
        static let mfaFacePartialMatch = "You’re almost there! (Dialog)"
        static let mfaTencentError = "Tencent Error (Dialog)"
        static let mfaTencentRetry = "Let’s try another take (Dialog)"
        static let mfaOtpChallengeExpired = "Authentication expired (Dialog)"
        static let mfaOtpMaxAttempts = "Max OTP attempts reached (Dialog)"
        static let mfaOtpMaxResend = "Max resend attempts reached (Dialog)"
        static let locationPermission = "Location Permission (Dialog)"
        static let regPasswordError = "Reg Password Error (Dialog)"
        static let registrationOTPError = "Reg OTP Error (Dialog)"
        static let registrationError = "Registration Error (Dialog)"
        static let regSubmitError = "Reg Submit Error (Dialog)"
    }

    struct MfaFailureReason {
        static let userCancel = "User cancelled"
        static let misMatch = "Face mismatch"
        static let invalidOtp = "Invalid OTP"
        static let maxAttempt = "Max OTP attempts reached"
        static let challengeIdExpired = "Challenge ID expired"
    }

    struct Webview {
        static let mfaCustSupport = "Customer support (Webview)"
        static let mfaSubmitTicket = "Submit ticket (Webview)"
    }

    struct UploadIDButton {
        static let addFileFront = "add a file front"
        static let addFileBack = "add a file back"
    }

    struct ScreenName {
        // Registration (User Profile) Screens
        static let mayaStartPage = "Reg Maya Start Page"
        static let locationOS = "Location Permission OS"
        static let startAccount = "Start an account"
        static let setLoginDetails = "Set your login details"
        static let loginPage = "Login Page"
        static let dataPrivacy = "Data privacy"
        static let dataPrivacyAutoProv = "Product Opt In"
        static let dataPersonalization = "Data personalization"
        static let registrationOTP = "Reg Verification Page (OTP)"
        static let termsAndConditions = "Terms and conditions"
        static let privacyPolicy = "Privacy policy"
        static let helpCenter = "Help center"
        static let dashboard = "Dashboard"
        static let forgotPassword = "Enter your number"
        static let setNewPassword = "Set a new password"
        static let passwordChanged = "Password changed"
        static let submitInviteCode = "Submit invite code"
        static let profilePage = "Profile Page"
        // Registration V2
        static let initial = "Initialize"
        static let login = "Login"
        static let landing = "Landing"
        static let accountCreation = "Account Creation"
        static let accountProvisioning = "Account Provisioning"
        static let registrationCode = "Registration OTP"
        static let referral = "Referral"
        static let kycUpgrade = "KYC Upgrade"
        // MFA Screens
        static let tencentFaceAuth = "Tencent Face Auth Screen"
        static let mfaOTP = "Enter the OTP"
        // Login
        static let loginOTP = "Login Verification Page (OTP)"
        // KYC Screens
        static let loader = "KYC Initial Loading"
        static let intro = "KYC Intro"
        static let selectId = "Select ID"
        static let idGuide = "ID Type Guide"
        static let uploadID = "Upload ID"
        static let uploadIDMenu = "ID Upload Menu"
        static let idTips = "ID tips"
        static let idCapture = "ID capture"
        static let idCaptureResult = "ID capture result"
        static let livenessTips = "Liveness tips"
        static let livenessCapture = "Liveness Capture"
        static let selectSecondId = "Select Second ID"
        static let captureSecondId = "Capture Second ID"
        static let personalInfo = "Personal Info"
        static let confirmSubmission = "Confirm Submission"
        static let docsMinor = "Docs minors"
        static let captureDocsMinors = "Capture docs minors"
        static let resultDocsMinors = "Result docs minors"
        static let kycSubmitted = "KYC Submitted"
        static let reKYCSubmitted = "ReKYC Submitted"
        static let kycStatus = "KYC Status"
        static let fatca = "FATCA"
        static let dosri = "DOSRI"
        // Intro Screens
        static let acceptedIds = "Accepted IDs"
        static let learnMore = "Learn More"
        static let exampleWhenSendingMoney = "Example when sending money"
    }

    struct ScreenType {
        static let mainLogin = "Main Login"
        static let sessionTimeoutLogin = "Session Timeout Login"
    }

    struct UserAction {
        static let button = "Button"
        static let swipe = "Swipe"
        static let kycBack = "EKYC_V6_BACK_TAPPED"
        static let tapToFocus = "Tap to focus"
        static let takeIDPhoto = "Take ID Photo"
        static let takeBirthCertificatePhoto = "Tap to take a photo birth certificate"
        static let takeParentIDPhoto = "Tap to take a photo parent id"
        static let takeConsentFormPhoto = "Tap to take a photo consent form"
        static let downloadConsentForm = "Download consent form"
    }

    struct Vendors {
        static let inHouse = "in-house"
        static let tencent = "Tencent"
    }

    enum SourcePage: String {
        case allBrands = "All Brands"
        case cashInCode = "Cash In Code"
        case cashInLanding = "Cash In Landing"
        case cashInPartner = "Cash In Partner"
        case category = "Category"
        case dashboard = "Dashboard"
        case deepLink = "Deep Link"
        case favorites = "Favorites"
        case home = "Home"
        case load = "Load"
        case payBills = "Pay Bills"
        case productDetails = "Product Details"
        case profile = "Profile"
        case prominentDisclosure = "Prominent Disclosure (Bottom sheet)"
        case providerCategory = "Provider Category"
        case requestMoney = "Request Money"
        case search = "Search"
        case services = "Services"
    }
}

extension AnalyticsConstants {
    struct Screen {
        enum Login: String {
            case main = "LOGIN_SCREEN"
            case forgotPassword = "FORGOT_PASSWORD_SCREEN"

            func inputted() -> String {
                return self.rawValue + "_INPUTTED"
            }
            func tapped() -> String {
                return self.rawValue + "_TAPPED"
            }
        }
        enum Registration: String {
            case onboarding = "REG_ONBOARDING_SCREEN"
            case location = "REG_LOC_PERMISSION"
            case locationOS = "REG_LOC_PERMISSION_OS"
            case mayaStartPage = "REG_MAYA_START_PAGE"
            case mainFlow = "REG_SCREEN"
            case mainFlowError = "REG_SCREEN_ERROR"
            case dataPrivacy = "REG_DATA_PRIVACY"
            case drawDown = "REG_DRAWDOWN"
            case dataPersonalization = "REG_DATA_PERSONAL"
            case otp = "REG_OTP"
            case otpError = "REG_OTP_ERROR"
            case text = "REG_TEXT"

            func inputted() -> String {
                return self.rawValue + "_INPUTTED"
            }
            func tapped() -> String {
                return self.rawValue + "_TAPPED"
            }
            func viewed() -> String {
                return self.rawValue + "_VIEWED"
            }
            func success() -> String {
                return self.rawValue + "_SUCCESS"
            }
        }

        enum RegistrationV2: String {
            case mainFlow = "REGV2"
            case prominentDisclosure = "REGV2_DISCLOSURE"
            case landingPage = "REGV2_LANDING_PAGE"
            case accountCreation = "REGV2_ACCOUNT_CREATION"
            case accountProvisioning = "REGV2_ACCOUNT_PROVISION"
            case locationPermission = "REGV2_LOC_PERMISSION"
            case mayaPHTermsAndConditions = "REGV2_MAYA_PH_TC"
            case mayaPHPrivacyPolicy = "REGV2_MAYA_PH_PRIVACY_POLICY"
            case mayaBankTermsAndConditions = "REGV2_MAYA_BANK_TC"
            case mayaBankPrivacyPolicy = "REGV2_MAYA_BANK_PRIVACY_POLICY"
            case successBottomSheet = "REGV2_SUCCESS_BOTTOM_SHEET"
            case error = "REGV2_ERROR"
            case otp = "REGV2_OTP"

            func inputted() -> String {
                return self.rawValue + "_INPUTTED"
            }
            func tapped() -> String {
                return self.rawValue + "_TAPPED"
            }
            func viewed() -> String {
                return self.rawValue + "_VIEWED"
            }
            func success() -> String {
                return self.rawValue + "_SUCCESS"
            }
            func autoProvisionLinkName() -> String {
                switch self {
                case .mayaPHPrivacyPolicy: AnalyticsConstants.BottomSheet.mayaPHPrivacyPolicy
                case .mayaPHTermsAndConditions: AnalyticsConstants.BottomSheet.mayaPHTermsAndConditions
                case .mayaBankPrivacyPolicy: AnalyticsConstants.BottomSheet.mayaBankPrivacyPolicy
                case .mayaBankTermsAndConditions: AnalyticsConstants.BottomSheet.mayaBankTermsAndConditions
                default: AnalyticsConstants.BottomSheet.mayaBankPrivacyPolicy
                }
            }
        }

        enum Mfa: String {
            case tencent = "MFA_TENCENT"
            case tencentScreen = "MFA_FACE_TENCENT_SCREEN"
            case face = "MFA_FACE"
            case faceChallengeExpired = "MFA_FACE_SCREEN_EXPIRED"
            case faceNoFaceData = "MFA_FACE_SCREEN_NO_FACE_DATA"
            case faceTencentError = "MFA_FACE_SCREEN_TENCENT_ERROR"
            case faceVerifyPrompt = "MFA_FACE_VERIFY_SCREEN"
            case faceRetry = "MFA_FACE_SCREEN_RETRY"
            case faceMismatch = "MFA_FACE_SCREEN_MISMATCH"
            case faceSupport = "MFA_FACE_SCREEN_SUPPORT"
            case facePartialMatch = "MFA_FACE_SCREEN_PARTIAL_MATCH"
            case otp = "MFA_OTP"
            case otpScreen = "MFA_OTP_SCREEN"
            case otpMaxAttempts = "MFA_OTP_SCREEN_MAX_ATTEMPTS"
            case otpMaxResend = "MFA_OTP_SCREEN_MAX_RESEND"
            case otpChallengeExpired = "MFA_OTP_SCREEN_EXPIRED"

            func tapped() -> String {
                return self.rawValue + "_TAPPED"
            }
            func viewed() -> String {
                return self.rawValue + "_VIEWED"
            }
            func success() -> String {
                return self.rawValue + "_SUCCESS"
            }
            func failure() -> String {
                return self.rawValue + "_FAILURE"
            }
        }

        enum Ekyc: String {
            case idCaptureResult = "EKYC_V6_ID_CAPTURE_RESULT"
            case selectIDSecondary = "EKYC_V6_SELECT_ID_SECONDARY"
            case personalInfo = "EKYC_V6_PERSONAL_INFORMATION"
            case intro = "EKYC_V6_INTRO"
            case selectID = "EKYC_V6_SELECT_ID"
            case idTypeGuide = "EKYC_V6_ID_TYPE_GUIDE"
            case idTips = "EKYC_V6_ID_TIPS"
            case simpleCaptureFront = "EKYC_V6_SIMPLE_CAPTURE_FRONT"
            case simpleCaptureBack = "EKYC_V6_SIMPLE_CAPTURE_BACK"
            case uploadID = "EKYC_V6_UPLOAD_ID"
            case livenessTips = "EKYC_V6_ID_LIVENESS_TIPS"
            case liveness = "EKYC_V6_LIVENESS"
            case additionalDocs = "EKYC_V6_MINORS_ADD_DOC"
            case additionalDocsCapture = "EKYC_V6_MINORS_ADD_DOC_CAPTURE"
            case additionalDocsCaptureResults = "EKYC_V6_MINORS_ADD_DOC_CAPTURE_RESULTS"
            case changeYourMind = "EKYC_V6_ID_CHANGE_YOUR_MIND"
            case confirmSubmission = "EKYC_V6_CONFIRM_SUBMISSION"
            case idSubmitted = "EKYC_V6_SUBMITTED"

            func appear() -> String {
                return self.rawValue + "_APPEAR"
            }

            func tapped() -> String {
                return self.rawValue + "_TAPPED"
            }

            func error() -> String {
                return self.rawValue + "_ERROR"
            }

            func result() -> String {
                return self.rawValue + "_RESULT"
            }
        }

        enum Fatca: String {
            case usPerson = "YES"
            case notUsPerson = "NO"
            case none = "FATCA"

            func appear() -> String {
                return self.rawValue + "_APPEAR"
            }

            func tapped() -> String {
                return self.rawValue + "_TAPPED"
            }
        }

        enum Dosri: String {
            case none = "DOSRI"

            func appear() -> String {
                return self.rawValue + "_APPEAR"
            }

            func tapped() -> String {
                return self.rawValue + "_TAPPED"
            }

            func error() -> String {
                return self.rawValue + "_ERROR"
            }
        }

        enum Qr: String {
            case action = "QR_ACTION"
            case code = "QR_CODE"
            case payment = "QR_PAYMENT"
            case paymentButton = "QR_PAYMENT_BUTTON"
            case scan = "QR_SCAN"
            case scanPage = "QR_SCAN_PAGE"

            enum Page: String {
                case qrScan = "Pay with QR"
                case payment = "Payment"
            }

            enum QrClass: String {
                case p2m = "P2M"
                case instapay = "Instapay"
                case p2p = "P2P"
            }

            enum QrType: String {
                case `static` = "Static"
                case dynamic = "Dynamic"
            }

            enum MerchantType: String {
                case onUs = "On us"
                case offUs = "Off us"
            }

            func scanned() -> String {
                return self.rawValue + "_SCANNED"
            }

            func tapped() -> String {
                return self.rawValue + "_TAPPED"
            }

            func viewed() -> String {
                return self.rawValue + "_VIEWED"
            }

            func errorScreen() -> String {
                return self.rawValue + "_ERROR_SCREEN"
            }
        }

        enum Shop: String {
            case ad = "SHOP_AD"
            case brand = "SHOP_BRAND"
            case brandList = "SHOP_BRAND_LIST"
            case category = "SHOP_CATEGORY"
            case contactName = "SHOP_CONTACT_NAME"
            case contacts = "SHOP_CONTACTS"
            case errorScreen = "SHOP_ERROR_SCREEN"
            case history = "SHOP_HISTORY"
            case historyCta = "SHOP_HISTORY_CTA"
            case historyItem = "SHOP_HISTORY_ITEM"
            case home = "SHOP_HOME"
            case homeCta = "SHOP_HOME_CTA"
            case product = "SHOP_PRODUCT"
            case productDetails = "SHOP_PRODUCT_DETAILS"
            case productList = "SHOP_PRODUCT_LIST"
            case productOption = "SHOP_PRODUCT_OPTION"
            case search = "SHOP_SEARCH"
            case searchResults = "SHOP_SEARCH_RESULTS"
            case subcategory = "SHOP_SUBCATEGORY"
            case subcategoryPage = "SHOP_SUBCATEGORY_PAGE"

            enum Page: String {
                case home = "Home"
                case search = "Search"
            }

            enum Button: String {
                case airtimeBanner = "Airtime Banner"
                case contacts = "Contacts"
                case `continue` = "Continue"
                case seeAll = "See All"
                case shopNow = "Shop Now"
            }

            enum Section: String {
                case brandList = "Brand List"
                case buyAgain = "Buy Again"
                case catalog = "Catalog"
                case global = "Global"
                case history = "History"
                case home = "Home"
                case mostPopular = "Most Popular"
                case productDetails = "Product Details"
                case search = "Search"
            }

            func tapped() -> String {
                return self.rawValue + "_TAPPED"
            }

            func viewed() -> String {
                return self.rawValue + "_VIEWED"
            }
        }

        enum BillsPay: String {
            case ad = "BILLS_AD"
            case biller = "BILLS_BILLER"
            case category = "BILLS_CATEGORY"
            case categoryPage = "BILLS_CATEGORY_PAGE"
            case errorScreen = "BILLS_PAY_ERROR_SCREEN"
            case favorites = "BILLS_FAVORITES"
            case page = "BILLS_PAGE"
            case pageCta = "BILLS_PAGE_CTA"
            case paymentFieldError = "BILLS_PAYMENT_FIELD_ERROR"
            case paymentFavorites = "BILLS_PAYMENT_FAVORITES"
            case paymentPage = "BILLS_PAYMENT_PAGE"
            case receiptPage = "BILLS_RECEIPT_PAGE"
            case search = "BILLS_SEARCH"
            case searchCta = "BILLS_SEARCH_CTA"
            case searchLoadPage = "BILLS_SEARCH_LOAD_PAGE"
            case searchResult = "BILLS_SEARCH_RESULT"

            enum Page: String {
                case billerForm = "Biller Form"
                case category = "Category"
                case home = "Home"
                case search = "Search"
            }

            enum Section: String {
                case categories = "Categories"
                case favorites = "Favorites"
            }

            func appear() -> String {
                return self.rawValue + "_APPEAR"
            }

            func confirmed() -> String {
                return self.rawValue + "_CONFIRMED"
            }

            func tapped() -> String {
                return self.rawValue + "_TAPPED"
            }

            func viewed() -> String {
                return self.rawValue + "_VIEWED"
            }
        }

        enum Error: String {
            case ekycV6 = "EKYC_V6_ERROR"

            func appear() -> String {
                return self.rawValue + "_APPEAR"
            }
        }

        enum BankTransfer: String {
            case bankList = "BANK_TRANSFER_BANK_LIST"
            case screen = "BANK_TRANSFER_SCREEN"
            case processing = "BANK_TRANSFER_PROCESSING"
            case confirmation = "BANK_TRANSFER_CONFIRMATION"
            case mfaSetup = "BANK_TRANSFER_MFA_SETUP"

            enum Button: String {
                case bankName = "Bank Name"
                case seeAll = "See all"
                case favoriteBank = "Favorite Bank"
                case back = "Back"
                case continueButton = "Continue"
                case saveToFavorites = "Save details to favorites"
                case done = "Done"
                case viewReceipt = "View receipt"
                case transferNow = "Transfer now"
            }

            enum Page: String {
                case dashboard = "Dashboard"
                case bankList = "Bank Transfer Bank List Screen"
                case favorite = "Bank Transfer Favorite Screen"
                case form = "Bank Transfer Form Screen"
                case qr = "QR Scanner Screen"
                case confirmation = "Bank Transfer Confirmation Screen"
                case receipt = "Bank Transfer Transaction Receipt"
                case otp = "OTP Screen"
            }

            enum Input: String {
                case amount
                case purpose
                case accountNumber = "Account Number"
                case accountName = "Account Name"
            }

            func appear() -> String {
                return self.rawValue + "_APPEAR"
            }

            func buttonTapped() -> String {
                return self.rawValue + "_BUTTON_TAPPED"
            }

            func input() -> String {
                return self.rawValue + "_INPUT"
            }

            func failed() -> String {
                return self.rawValue + "_FAILED"
            }
        }

        enum SendMoney: String {
            case screen = "SEND_MONEY_SCREEN"
            case success = "SEND_MONEY_SUCCESS"
            case confirmation = "SEND_MONEY_CONFIRMATION"

            enum Button: String {
                case seeAll = "See all"
                case favorite = "Favorite"
                case back = "Back"
                case continueButton = "Continue"
                case saveToFavorites = "Save details to favorites"
                case done = "Done"
                case viewReceipt = "View receipt"
                case contacts = "Contact List"
                case preview = "Preview"
                case theme = "Theme"
                case sendNow = "Send now"
            }

            enum Page: String {
                case dashboard = "Dashboard"
                case favorite = "Send Money Favorite Screen"
                case form = "Send Money Form Screen"
                case qr = "QR Scanner Screen"
                case confirmation = "Send Money Confirmation Screen"
                case receipt = "Send Money Transaction Receipt"
                case contacts = "Contact List Screen"
                case preview = "Preview Screen"
                case otp = "OTP Screen"
            }

            enum Input: String {
                case amount
                case recipient
                case note
            }

            func appear() -> String {
                return self.rawValue + "_APPEAR"
            }

            func buttonTapped() -> String {
                return self.rawValue + "_BUTTON_TAPPED"
            }

            func input() -> String {
                return self.rawValue + "_INPUT"
            }

            func failed() -> String {
                return self.rawValue + "_FAILED"
            }
        }

        enum DeviceManagement: String {
            case back
            case getHelp = "get_help"
            case changePassword = "change_password"
            case updateEmail = "update_email"
        }

        enum CashIn: String {
            case accountUpgradePopUp = "CASH_IN_ACCOUNT_UPGRADE_POP_UP"
            case codeAbandonPopUp = "CASH_IN_CODE_ABANDON_POP_UP"
            case codeGenerationFailure = "CASH_IN_CODE_GENERATION_FAILURE"
            case codePage = "CASH_IN_CODE_PAGE"
            case banner = "CASH_IN_BANNER"
            case page = "CASH_IN_PAGE"
            case pageButton = "CASH_IN_PAGE_BUTTON"
            case partnerPage = "CASH_IN_PARTNER_PAGE"

            enum Button: String {
                case accountLimits = "Account Limits"
                case bankAccount = "Bank Account"
                case debitCreditCard = "Debit or Credit Card"
                case editAmount = "Edit Amount"
                case mayaCenter = "Maya Center"
                case mayaSavings = "Maya Savings"
                case viewAccountLimits = "View Account Limits"
            }

            enum CodeType: String {
                case barcode = "bar code"
                case sevenDigitCode = "7 digit code"
            }

            func tapped() -> String {
                return self.rawValue + "_TAPPED"
            }

            func viewed() -> String {
                return self.rawValue + "_VIEWED"
            }
        }

        enum SecurityCenter: String {
            case page = "SECURITY_CENTER_PAGE"

            enum Button: String {
                case freezeButton = "Freeze Button"
                case securityCenterButton = "Security Center Button"
            }
        }

        enum MEC: String {
            case product = "mec"
        }

        enum MECApplication: String {
            case page = "Confirm Details Screen"

            enum Button: String {
                case confirm = "Confirm"
                case deny = "Deny"
                case allow = "Allow"
                case gotIt = "Got it"
                case goToSettings = "Go to Settings"
            }

            enum Prompt: String {
                case location = "Location Permission Prompt"
                case deviceAccess = "Device Access Request Prompt"
            }
        }

        enum MECDrawdown: String {
            case page = "Transfer To Wallet Screen"

            enum Button: String {
                case `continue` = "Continue"
                case deny = "Deny"
                case allow = "Allow"
            }

            enum Prompt: String {
                case location = "Location Permission Prompt"
            }
        }
    }
}
