//
//  MayaExperimentInfoKey.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 11/27/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation

enum MayaExperimentInfoKey: String {
    case experiment = "experiment"
    case treatment = "treatment"
}

enum MayaExperimentAttributedKey: String {
    case experimentName = "experiment_name"
    case treatment = "treatment"
    case osPlatform = "os_platform"
}
