//
//  AnalyticsAttributeKey.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 6/5/19.
//  Copyright © 2019 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics

// In case of additional attribute key, add here
public enum AnalyticsAttributeKey: String, AnalyticsAttributeKeyProtocol {
    case accountNumber = "account_number"
    case accountType = "account_type"
    case action
    case actionCard = "action_card"
    case actionFunction = "action_function"
    case advertising
    case amount
    case appsFlyerId = "appsflyer_id"
    case assignmentId = "assignment_id"
    case attribution = "attribution"
    case autoCashIn
    case bank
    case bankCode = "bank_code"
    case bankStatus = "bank_status"
    case banners
    case billAmount = "bill_amount"
    case biller
    case billerName = "biller_name"
    case billerSlug = "biller_slug"
    case blockReason = "block_reason"
    case blurScore = "blur_score"
    case blurrinessScore = "blurriness_score"
    case branch
    case brand
    case buildNumber = "build_number"
    case button
    case buttonType = "button_type"
    case caller
    case campaignName = "campaign_name"
    case carouselName = "carousel_name"
    case cashInMethod = "cash_in_method"
    case catego
    case category
    case categoryName = "category_name"
    case categorySlug = "category_slug"
    case challengeId
    case checkbox
    case code
    case codeType = "code_type"
    case confidenceScore = "confidence_score"
    case consentedAdvertising = "consented_advertising"
    case consentedProfileSharing = "consented_profile_sharing"
    case consentedProfiling = "consented_profiling"
    case convenienceFee = "convenience_fee"
    case count
    case cpmId = "cpm_id"
    case creditScoring = "credit_scoring"
    case cta_source = "cta_source"
    case day
    case deeplink
    case design
    case destination
    case destinationPage = "destination_page"
    case detectedId = "detected_id"
    case deviceType = "device_type"
    case disabled
    case document
    case documentType = "document_type"
    case duration
    case easyCreditStatus = "easy_credit_status"
    case enabled
    case error
    case errorCode = "error_code"
    case errorMessage = "error_message"
    case errorReason = "error_reason"
    case errorUID = "error_uid"
    case eventTrigger = "eventTrigger"
    case eventTrigger2 = "event_trigger"
    case extractor
    case favorite = "favorite"
    case favoriteId = "favoriteId"
    case favoritePartnerName = "partner_name"
    case favoritesCount = "favorites_count"
    case fee
    case field
    case fieldError = "field_error"
    case gifId = "gif_id"
    case gifted
    case grantedPermission = "granted_permission"
    case hasFavorites = "has_favorites"
    case hasSignature = "has_signature"
    case idLevel = "ID_level"
    case idOrder = "ID_order"
    case idPass = "pass"
    case idSide = "ID_side"
    case idTypeDetected = "ID_type_detected"
    case idTypeGuide = "id_type_guide"
    case idTypingResult = "id_typing_result"
    case idTypingScore = "id_typing_score"
    case idTypingVersion = "id_typing_version"
    case idValModelVersion = "idval_model_version"
    case id_position = "ID_position"
    case id_type = "ID_type"
    case idtype1
    case idtype2
    case imageQModelVersion = "imageq_model_version"
    case imageQResult = "imageq_result"
    case imageQScore = "imageq_score"
    case inputLabel = "input_label"
    case inputType = "input_type"
    case invalidReason = "invalid_reason"
    case isAccountNameFilled = "is_account_name_filled"
    case isAccountNumberFilled = "is_account_number_filled"
    case isAmountFilled = "is_amount_filled"
    case isBlurrinessCheckPass = "is_blurriness_check_pass"
    case isDedup = "is_dedup"
    case isDisabled = "is_disabled"
    case isIdTypingPass = "is_id_typing_pass"
    case isImageQPass = "is_imageq_pass"
    case isInvalid = "is_invalid"
    case isNoteFilled = "is_note_filled"
    case isPrefillCurrentAddress = "is_prefill_current_address"
    case isPrefillDob = "is_prefill_dob"
    case isPrefillIdDetails = "is_prefill_id_details"
    case isPrefillName = "is_prefill_name"
    case isPrefillNationality = "is_prefill_nationality"
    case isPrefillPermanentAddress = "is_prefill_permanent_address"
    case isPrefillPob = "is_prefill_pob"
    case isPrefillWork = "is_prefill_work"
    case isPurposeFilled = "is_purpose_filled"
    case isRecipientFilled = "is_recipient_filled"
    case isSuccess = "is_success"
    case isSuspectedMinor = "is_suspected_minor"
    case itemNumber = "item_number"
    case key
    case keyword
    case kycAction = "kyc_action"
    case kycLevel = "kyc_level"
    case kycPresubmissionId = "kycpresubmission_id"
    case kycStatus = "kyc_status"
    case labelName = "label_name"
    case lifestyleID = "lifestyle_id"
    case link
    case location
    case merchant
    case merchantName = "merchant_name"
    case merchantType = "merchant_type"
    case missionName = "mission_name"
    case missionTitle = "mission_title"
    case module
    case name
    case noOfResults = "no_of_results"
    case note
    case notificationId = "notificationId"
    case numberOfBanners = "no_of_banners"
    case numberOfFavorites = "number_of_favorites"
    case option
    case optionLabel = "option_label"
    case origin
    case os
    case page
    case partner
    case path
    case paymentMethod = "payment_method"
    case position
    case positionWidget = "position_widget"
    case price
    case priority
    case product
    case productCode = "product_code"
    case productName = "product_name"
    case profileSharing = "profile_sharing"
    case profiling
    case progress
    case promo
    case provider
    case providerCode = "provider_code"
    case providerId = "provider_id"
    case providerName = "provider_name"
    case purpose
    case qrClass = "qr_class"
    case qrErrorCode = "qr_error_code"
    case qrErrorReason = "qr_error_reason"
    case qrFieldName = "qr_field_name"
    case qrFieldTag = "qr_field_tag"
    case qrFieldValue = "qr_field_value"
    case qrType = "qr_type"
    case qualityScore = "quality_score"
    case reKYCReason = "rekyc_reason"
    case reKYCStatus = "rekyc_status"
    case reason
    case receiptType = "receipt_type"
    case receiverBIC = "receiver_bic"
    case recipientType = "recipient_type"
    case referrer
    case reminder
    case retryCount = "retry_count"
    case screen
    case screenName = "screen_name"
    case screenType = "screen_type"
    case sdkVersion = "sdk_version"
    case section
    case selectedId = "selected_id"
    case service
    case serviceTile = "service_tile"
    case sessionId = "session_id"
    case setting
    case source
    case sourceAccount = "source_account"
    case sourceButton = "source_button"
    case sourceClick = "source_click"
    case sourcePage = "source_page"
    case sourceTab = "source_tab"
    case state
    case status
    case stepNumber = "step_no"
    case subcategories = "subcategories"
    case subcategory = "subcategory"
    case subcategoryCode = "subcategory_code"
    case subcategoryCount = "subcategory_count"
    case subcategoryPosition = "subcategory_position"
    case tab = "tab"
    case tagName = "tag_name"
    case targetBank = "target_bank"
    case themeId = "theme_id"
    case thirdPartyAdvertising = "third_party_advertising"
    case tile
    case totalAmount = "total_amount"
    case trafficType = "traffic_type"
    case triggeredBy = "triggeredBy"
    case type
    case unreadMessages = "unread_messages"
    case url
    case userAction = "user_action"
    case uuid
    case vendor
    case version = "version"
    case viewType = "view_type"
    case widgetName = "widget_name"
    case widgetPosition = "widget_position"
    case zolozServiceError = "zoloz_service_error"
}

public extension Dictionary where Key == AnalyticsAttributeKey {
    var rawValue: [String: Value] {
        self.mapKeys { $0.rawValue }
    }
}
