//
//  AnalyticsModule.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 5/5/23.
//  Copyright © 2023 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics

// swiftlint:disable type_body_length file_length
enum Analytics {
    enum AppStart: String, AnalyticsModule {
        case launch = ""

        var name: String {
            return "MAYA_APP_LAUNCH"
        }
    }
    enum FaceAuth: String, AnalyticsModule {
        case prompt = "PROMPT"
        case promptContinue = "CONTINUE"
        case promptLater = "LATER"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            switch self {
            case .promptContinue,
                    .promptLater:
                return "FACE_AUTH_PROMPT"
            default:
                return "FACE_AUTH"
            }
        }
    }

    enum WizardV2: String, AnalyticsModule {
        case getStarted = "GET_STARTED"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "WIZARD_V2"
        }
    }

    enum NewWizard: String, AnalyticsModule {
        case a = "A"
        case b = "B"
        case c = "C"
        case getStarted = "GETSTARTED"
        case next = "NEXT"
        case previous = "PREVIOUS"
        case skip = "SKIP"
        case back = "BACK"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "NEW_WIZARD"
        }
    }

    enum Login: String, AnalyticsModule {
        case login = "LOGIN"
        case forgotPassword = "FORGOT_PASSWORD"
        case acceptConfirmCredential = "ACCEPT_CONFIRM_CREDENTIAL"
        case rejectConfirmCredential = "REJECT_CONFIRM_CREDENTIAL"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "LOGIN"
        }
    }

    enum SessionTimeout: String, AnalyticsModule {
        case faceID = "FACEID"
        case forgotPassword = "FORGOT_PASSWORD"
        case login = "LOGIN"
        case switchAccount = "SWITCH_ACCOUNT"
        case touchID = "TOUCHID"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "SESSION_TIMEOUT"
        }
    }

    enum Otp: AnalyticsModule {
        case button(String?, String?)
        case proceed(String?)
        case resend(String?)
        case helpCenter(String?)
        case changeMin(String?)
        case none(String?)
        case verify(String?)
        init(_ module: String?) {
            self = .none(module)
        }

        var name: String {
            switch self {
            case .button(let module, _):
                return module ?? "OTP"
            case .proceed(let module):
                return module ?? "OTP"
            case .resend(let module):
                return module ?? "OTP"
            case .helpCenter(let module):
                return module ?? "OTP"
            case .changeMin(let module):
                return module ?? "OTP"
            case .none(let module):
                return module ?? "OTP"
            case .verify(let module):
                return module ?? "OTP"
            }
        }

        var object: String {
            switch self {
            case .button( _, let title):
                return title?.uppercased() ?? "PROCEED"
            case .proceed:
                return "PROCEED"
            case .resend:
                return "RESEND"
            case .helpCenter:
                return "HELP_CENTER"
            case .changeMin:
                return "CHANGE_MIN"
            case .none:
                return ""
            case .verify:
                return "VERIFY"
            }
        }
    }

    enum AppsFlyer: String, AnalyticsModule {
        case customerID = "CUSTOMER_ID"
    }

    enum Tinbo: String, AnalyticsModule {
        case none = ""
        case openTinbo = "OPEN_TINBO"
        case close = "CLOSE"

        init() {
            self = .none
        }
    }

    enum Registration: String, AnalyticsModule {
        case back = "BACK"
        case `continue` = "CONTINUE"
        case login = "LOGIN"
        case save = "SAVE"
        case secondaryConsents = "SECONDARY_CONSENTS"
        case tinbo = "TINBO"
        case tinboContinue = "TINBO_CONTINUE"
        case tinboClose = "TINBO_CLOSE"
        case validatePassword = "VALIDATE_PASSWORD"
        case whatsThis = "WHATS_THIS"
        case none = ""

        init() {
            self = .none
        }
    }

    enum RegV2: String, AnalyticsModule {
        case welcome = "LANDING_PAGE"
        case accountCreation = "ACCOUNT_CREATION"
        case accountProvisioning = "ACCOUNT_PROVISION"
        case prominentDisclosure = "DISCLOSURE"
        case otp = "OTP"
        case none = ""

        init() {
            self = .none
        }
    }

    enum RegistrationSuccess: String, AnalyticsModule {
        case none = ""
        case upgrade = "UPGRADE"
        case home = "HOME"
        case submitCode = "SUBMIT_CODE"

        init() {
            self = .none
        }

        var name: String {
            return "REGISTRATION_SUCCESS"
        }
    }

    enum RegistrationValidatePassword: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "REGISTRATION_VALIDATE_PASSWORD"
        }
    }

    enum DataPrivacy: String, AnalyticsModule {
        case agree = "AGREE"
        case disagree = "DISAGREE"
        case secondaryConsents = "SECONDARY_CONSENTS"
        case privacyPolicy = "VIEW_PRIVACY_POLICY"
        case termsAndConditions = "VIEW_TERMS"
        case enableAll = "ENABLE_ALL"
        case save = "SAVE"
        case saveChanges = "SAVE_CHANGES"
        case dontSaveChanges = "DONT_SAVE_CHANGES"
        case updateConsents = "UPDATE_CONSENTS"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "DATA_PRIVACY"
        }
    }

    enum CardsV2Dashboard: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CARDS_V2_DASHBOARD"
        }
    }

    enum ChangePassword: String, AnalyticsModule {
        case none = ""
        case whatsThis = "WHATS_THIS"

        init() {
            self = .none
        }

        var name: String {
            return "CHANGE_PASSWORD"
        }
    }

    enum AccountRecovery: String, AnalyticsModule {
        case emailNomination = "EMAIL_NOMINATION"
        case verify = "VERIFY"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "ACCOUNT_RECOVERY"
        }
    }

    enum AddMoney: String, AnalyticsModule {
        case partner = "PARTNER"
        case learnMore = "LEARN_MORE"
        case banner = "BANNER"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "ADD_MONEY"
        }
    }

    enum InviteCode: String, AnalyticsModule {
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "INVITE_CODE"
        }
    }

    enum MayaSettings: String, AnalyticsModule {
        case manageNotifications = "MANAGE_NOTIFICATIONS"
        case showName = "SHOW_NAME"
        case recovery = "ACCOUNT_RECOVERY"
        case changeMobile = "CHANGE_MOBILE"
        case dataPersonalization = "DATA_PERSONALIZATION"
        case closeAccount = "CLOSE_ACCOUNT"
        case changePassword = "CHANGE_PASSWORD"
        case biometryOn = "FINGERPRINTPATTERN_ON"
        case biometryOff = "FINGERPRINTPATTERN_OFF"
        case privacy = "PRIVACY_POLICY"
        case terms = "TERMS"
        case licenses = "LICENSES"
        case upgrade = "UPDATE_NOW"

        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "SETTINGS"
        }
    }

    enum InviteFriends: String, AnalyticsModule {
        case copy = "COPY"
        case learnMore = "LEARN_MORE"
        case share = "SHARE"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "INVITE_FRIENDS"
        }
    }

    enum WesternUnion: String, AnalyticsModule {
        case `continue` = "CONTINUE"
        case whatsMtcn = "WHATS_MTCN"
        case receive = "RECEIVE"
        case create = "CREATE"
        case confirmation = "CONFIRMATION"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "WESTERN_UNION"
        }
    }

    enum MoneyIn: String, AnalyticsModule {
        case code = "CODE"
        case generateCode = "GENERATE_NEW_CODE"
        case location = "LOCATION"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "MONEY_IN"
        }
    }

    enum SevenConnect: String, AnalyticsModule {
        case barCode = "BARCODE"
        case generateCode = "GENERATE_NEW_CODE"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "SEVEN_CONNECT"
        }
    }

    enum AddMoneyEnterAmount: String, AnalyticsModule {
        case code = "CODE"
        case generateCode = "GENERATE_NEW_CODE"
        case location = "LOCATION"
        case accountLimits = "LIMITS"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "ADD_MONEY_ENTER_AMOUNT"
        }
    }

    enum PersonalQR: String, AnalyticsModule {
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "PERSONAL_QR"
        }
    }

    enum SendMoney: String, AnalyticsModule {
        case `continue` = "CONTINUE"
        case send = "SEND"
        case create = "CREATE"
        case confirmation = "CONFIRMATION"
        case splash = "SPLASH"
        case theme = "THEME"
        case gif = "GIF"
        case preview = "PREVIEW"
        case personalizationOn = "PERSONALIZE_ON"
        case personalizationOff = "PERSONALIZE_OFF"
        case gifTab = "ADD_GIF"
        case themeTab = "ADD_THEME"
        case selectGif = "SELECT_GIF"
        case changeGif = "CHANGE_GIF"
        case verifiedSellerBadge = "VERIFIED_SELLER_BADGE"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "SEND_MONEY"
        }
    }

    enum SendMoneySuccess: String, AnalyticsModule {
        case none = ""
        case done = "DONE"
        case receipt = "RECEIPT"
        case favorites = "FAVORITES"
        init() {
            self = .none
        }

        var name: String {
            return "SEND_MONEY_SUCCESS"
        }
    }

    enum Shop: AnalyticsModule {
        case product
        case `continue`
        case buy
        case search
        case purchase
        case purchaseCreate
        case purchaseHistory
        case brands
        case allProviders
        case providers
        case provider
        case favorites
        case deals
        case home
        case banner
        case popular
        case buyAgain
        case homeCategories(String)
        case category
        case loadV2Banner

        var object: String {
            switch self {
            case .product: return "PRODUCT"
            case .continue: return "CONTINUE"
            case .buy: return "BUY"
            case .search: return "SEARCH"
            case .purchase: return "PURCHASE"
            case .purchaseCreate: return "PURCHASE_CREATE"
            case .purchaseHistory: return "PURCHASE_HISTORY"
            case .popular: return "POPULAR"
            case .brands: return "BRANDS"
            case .allProviders: return "ALL_PROVIDERS"
            case .providers: return "PROVIDERS"
            case .deals: return "DEALS"
            case .favorites: return "FAVORITES"
            case .home: return "HOME"
            case .provider: return "PROVIDER"
            case .banner: return "BANNER"
            case .buyAgain: return "PRODUCT"
            case .homeCategories(let category): return "\(category.uppercased())"
            case .category: return "CATEGORY"
            case .loadV2Banner: return "LOAD_V2_BANNER"
            }
        }
    }

    enum ShopSuccess: String, AnalyticsModule {
        case none = ""
        case viewReceipt = "VIEW_RECEIPT"
        case finish = "FINISH"

        init() {
            self = .none
        }
    }

    enum ShopPayment: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "SHOP_PAYMENT"
        }
    }

    enum ShopProviders: String, AnalyticsModule {
        case none = ""
        case brand = "BRAND"

        init() {
            self = .none
        }

        var name: String {
            return "SHOP_PROVIDERS"
        }
    }

    enum ShopProvider: String, AnalyticsModule {
        case none = ""
        case subcategory = "SUBCATEGORY"

        init() {
            self = .none
        }

        var name: String {
            return "SHOP_PROVIDER"
        }
    }

    enum ShopSearch: AnalyticsModule {
        case none
        case productDisabled

        init() {
            self = .none
        }

        var name: String {
            return "SHOP_SEARCH"
        }

        var object: String {
            switch self {
            case .productDisabled: return "PRODUCT_DISABLED"
            default: return ""
            }
        }
    }

    enum ShopPurchaseHistory: String, AnalyticsModule {
        case none = ""
        case toolTip
        case product

        init() {
            self = .none
        }

        var name: String {
            return "SHOP_PURCHASE_HISTORY"
        }

        var object: String {
            switch self {
            case .toolTip: return "TOOLTIP"
            case .product: return "PRODUCT"
            default: return ""
            }
        }
    }

    enum Verification: String, AnalyticsModule {
        case verify = "VERIFY"
        case resend = "RESEND"
        case help = "HELP"
        case back = "BACK"
        case none = ""

        init() {
            self = .none
        }
        var name: String {
            return "VERIFICATION"
        }
    }

    enum Welcome: String, AnalyticsModule {
        case register = "REGISTER"
        case viewIntro = "VIEW_INTRO"
        case login = "LOGIN"
        case none = ""
        init() {
            self = .none
        }
    }

    enum Activity: String, AnalyticsModule {
        case shareBillsPayment = "SHARE_BILLS_PAYMENT"
        case none = ""
        init() {
            self = .none
        }
    }

    enum ActivityList: String, AnalyticsModule {
        case share = "SHARE"
        case getHelp = "GET_HELP"
        case entry = "ENTRY"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "ACTIVITY_LIST"
        }
    }

    enum ActivityListDetails: String, AnalyticsModule {
        case shareOrSave = "SHARE_OR_SAVE"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "ACTIVITY_LIST_DETAILS"
        }
    }

    enum ActivityListDecoration: String, AnalyticsModule {
        case sendMoney
        case receiveMoney
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            switch self {
            case .sendMoney: return "ACTIVITY_LIST_SEND_MONEY"
            case .receiveMoney: return "ACTIVITY_LIST_RECEIVE_MONEY"
            default: return ""
            }
        }

        var object: String {
            switch self {
            case .sendMoney, .receiveMoney: return "PREVIEW"
            default: return ""
            }
        }
    }

    enum SendMoneyCard: String, AnalyticsModule {
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "SEND_MONEY_CARD"
        }
    }

    enum SendMoneyCardPreview: String, AnalyticsModule {
        case theme = "THEME"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "SEND_MONEY_CARD_PREVIEW"
        }
    }

    enum Dashboard: String, AnalyticsModule {
        case addMoney = "ADD_MONEY"
        case addMoneyCTA = "ADD_MONEY_CTA"
        case bankTransfer = "BANK_TRANSFER"
        case cardsV2 = "CARDS_V2"
        case credit = "CREDIT"
        case creditSettings = "CREDIT_SETTINGS"
        case drawer = "DRAWER"
        case food = "FOOD"
        case government = "GOVERNMENT"
        case hideBalance = "HIDE_BALANCE"
        case home = "HOME"
        case inbox = "INBOX"
        case invest = "INVEST"
        case load = "LOAD"
        case loans = "LOANS"
        case mayaCreditActivateNow = "MAYA_CREDIT_ACTIVATE_NOW"
        case mayaCreditJoinNow = "MAYA_CREDIT_JOIN_NOW"
        case mayaCreditTab = "MAYA_CREDIT_TAB"
        case mayaMall = "MALL"
        case missions = "MISSIONS"
        case more = "MORE_SERVICES"
        case moreTooltip = "MORE_SERVICES_TOOLTIP"
        case myCards = "MY_CARDS"
        case none = ""
        case payBills = "PAY_BILLS"
        case payCreditBill = "PAY_CREDIT_BILL"
        case protect = "PROTECT"
        case receiveMoney = "RECEIVE_MONEY"
        case retail = "RETAIL"
        case savings = "SAVINGS"
        case scan = "SCAN"
        case scanToPay = "SCAN_TO_PAY"
        case securityCenter = "SECURITY_CENTER"
        case sendMoney = "SEND_MONEY"
        case shop = "SHOP"
        case tab = "TAB"
        case transferCredit = "TRANSFER_CREDIT"
        case transport = "TRANSPORT"
        case unhideBalance = "UNHIDE_BALANCE"
        case viewAll = "VIEW_ALL"
        case viewCard = "VIEW_CARD"
        case viewCreditSummary = "VIEW_CREDIT_SUMMARY"
        case voucher = "VOUCHER"
        case wallet = "WALLET"

        init() {
            self = .none
        }
    }

    enum DashboardNavigation: String, AnalyticsModule {
        case item = "ITEM"

        var name: String { "SHARED_ROW" }
    }

    enum Inbox: String, AnalyticsModule {
        case option = "OPTION"
        case pushNotificationTapped = "PUSH_NOTIFICATION_SENDBIRD_TAPPED"
        case messageId = "MESSAGE_ID"
    }

    enum KYC: String, AnalyticsModule {
        case personal = "PERSONAL"
        case address = "ADDRESS"
        case work = "WORK"
        case review = "REVIEW"
        case submitReview = "SUBMIT_REVIEW"
        case submit = "SUBMIT"
    }

    enum EKYC: String, AnalyticsModule {
        case benefits = "BENEFITS"
        case reminder = "REMINDER"
        case idList = "ID_LIST"
        case personal = "PERSONAL"
        case address = "ADDRESS"
        case identification = "IDENTIFICATION"
        case livenessCapture = "LIVENESS_CAPTURE"
        case review = "REVIEW"
        case submitReview = "SUBMIT_REVIEW"
        case submitSuccess = "SUBMIT_SUCCESS"
        case submit = "SUBMIT"
        case use = "USE"
        case retake = "RETAKE"
    }

    enum EKYCV5: String, AnalyticsModule {
        case reminder = "REMINDER"
        case idList = "ID_LIST"
        case profileSelection = "PROFILE_SELECTION"
        case identification = "IDENTIFICATION"
        case faceVerification = "FACE_VERIFICATION"
        case personal = "PERSONAL"
        case livenessCapture = "LIVENESS_CAPTURE"
        case tencentLiveness = "TENCENT_LIVENESS"
        case review = "REVIEW"
        case submitReview = "SUBMIT_REVIEW"
        case submitSuccess = "SUBMIT_SUCCESS"
        case submit = "SUBMIT"
        case use = "USE"
        case retake = "RETAKE"

        var name: String {
            return "EKYC_V5"
        }
    }

    enum EKYCV6: String, AnalyticsModule {
        case none = ""
        case intro = "INTRO"
        case selectId = "SELECT_ID"
        case idTips = "ID_TIPS"
        case livenessTips = "LIVENESS_TIPS"
        case zolozStart = "ZOLOZ_START"
        case personalInformation = "PERSONAL_INFORMATION"
        case minorsAdditionalDocuments = "MINORS_ADD_DOCS"
        case minorsBirthCert = "MINORS_BIRTH_CERT"
        case minorsParentId = "MINORS_PARENT_ID"
        case minorsConsentForm = "MINORS_CONSENT_FORM"
        case submitted = "SUBMITTED"
        case reKYCCheckStatus = "REKYC_CHECK_STATUS"
        case reKYCProfile = "REKYC_PROFILE"
        case eligibleId = "ELIGIBLE_ID"
        case personalInformationNameRules = "PERSONAL_INFORMATION_NAME_RULES"
        case accountUnderReview = "ACCOUNT_UNDER_REVIEW"
        case tencent = "TENCENT_LIVENESS"
        case simpleCaptureFront = "SIMPLE_CAPTURE_FRONT"
        case simpleCaptureBack = "SIMPLE_CAPTURE_BACK"
        case idValResult = "IDVAL_RESULT"
        case idVal = "IDVAL_MATCH"
        case idImageQ = "IDVAL_IMAGEQ"
        case preSubmissionId = "PRESUBMISSION_ID"
        case kycMultipleSubmission = "MULTIPLE_KYC_SUBMISSION"

        enum ZolozEnd: String, AnalyticsModule {
            case completed = "COMPLETED"
            case interrupted = "INTERRUPTED"
            case zolozFailed = "ZOLOZFAILED"
            case updateRequired = "UPDATEREQUIRED"
            case failed = "FAILED"
            case none = ""

            var name: String {
                return "EKYC_V6_ZOLOZ"
            }

            init() {
                self = .none
            }
        }

        enum IdValResult: String, AnalyticsModule {
            case takeNewPhoto = "TAKE_NEW_PHOTO"
            case useThisPhoto = "USE_THIS_PHOTO"
            case chooseDifferentID = "CHOOSE_DIFF_ID"
            case retake = "RETAKE"
            case none = ""

            var name: String {
                return "EKYC_V6_IDVAL_RESULT"
            }

            init() {
                self = .none
            }
        }

        enum ReKYCNudge: String, AnalyticsModule {
            case updateNow = "UPDATE_NOW"
            case doLater = "DO_LATER"
            case none = ""

            var name: String {
                return "EKYC_V6_REKYC_NUDGE"
            }

            init() {
                self = .none
            }
        }

        enum ReKYCSubmitted: String, AnalyticsModule {
            case logout = "LOGOUT"
            case none = ""

            var name: String {
                return "EKYC_V6_REKYC_SUBMITTED"
            }

            init() {
                self = .none
            }
        }

        enum ReKYCUIBlock: String, AnalyticsModule {
            case updateNow = "UPDATE_NOW"
            case none = ""

            var name: String {
                return "EKYC_V6_REKYC_UI_BLOCK"
            }

            init() {
                self = .none
            }
        }

        var name: String {
            return "EKYC_V6"
        }

        init() {
            self = .none
        }
    }

    enum EKYCIdPhotoMatching: String, AnalyticsModule {
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "EKYC_ID_PHOTO_MATCHING"
        }
    }

    enum EDD: String, AnalyticsModule {
        case primaryUsage = "PRIMARY_USAGE"
        case alternativeSourcesOfIncome = "ALTERNATIVE_SOURCES_OF_INCOME"
        case relatedCompanies = "RELATED_COMPANIES"
        case bankInformation = "BANK_INFORMATION"
        case financialDocuments = "FINACIAL_DOCUMENTS"
        case submit = "SUBMIT"
    }

    enum EDDPrompt: String, AnalyticsModule {
        case none = ""
        case proceed = "PROCEED"
        case later = "LATER"
        case help = "HELP"

        init() {
            self = .none
        }

        var name: String {
            return "EDD_PROMPT"
        }
    }

    enum Support: String, AnalyticsModule {
        case messagePayMayaCares = "MESSAGE_PAYMAYACARES"
        case none = ""
        init() {
            self = .none
        }
    }

    enum Menu: String, AnalyticsModule {
        case profileAvatar = "PROFILE_AVATAR"
        case deals = "DEALS"
        case getHelp = "GET_HELP"
        case inviteFriends = "INVITE_FRIENDS"
        case receiveMoney = "RECEIVE_MONEY"
        case signout = "SIGNOUT"
        case logout = "LOGOUT"
        case chatbot = "CHATBOT"
        case partnerMerchants = "PARTNER_MERCHANTS"
        case accountLimits = "ACCOUNT_LIMITS"
        case myFavorites = "MY_FAVORITES"
        case missions = "MISSIONS"
        case vouchers = "VOUCHERS"
        case submitInviteCode = "SUBMIT_INVITE_CODE"
        case settings = "SETTINGS"
        case quickGuide = "QUICK_GUIDE"
        case securityCenter = "SECURITY_CENTER"
        case checkApplicationStatus = "CHECK_APPLICATION_STATUS"
        case verifiedSellerBadge = "VERIFIED_SELLER_BADGE"
        case qr = "QR"
        case verifiedSeller = "VERIFIED_SELLER"
        case username = "SET_USERNAME"
        case auth = "AUTHORIZE_REQUESTS"
        case updateProfile = "UPDATE_PROFILE"
        case rateThisApp = "RATE_THIS_APP"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "MENU"
        }
    }

    enum PayBills: String, AnalyticsModule {
        case list = "LIST"
        case search = "SEARCH"
        case biller = "BILLER"
        case next = "NEXT"
        case create = "CREATE"
        case pay = "PAY"
        case category = "CATEGORY"
        case confirmation = "CONFIRMATION"
        case share = "SHARE"
        case meralcoScan = "MERALCO_SCAN"
        case mcclBanner = "MCCLBANNER"
        case mcclModalActivate = "MCCLMODAL_ACTIVATE"
        case mcclModalClose = "MCCLMODAL_CLOSE"
        case receipt = "RECEIPT"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "PAY_BILLS"
        }
    }

    enum PayBillsProcessing: String, AnalyticsModule {
        case none = ""
        case viewReceipt = "VIEW_RECEIPT"
        case done = "DONE"
        case favorites = "FAVORITES"

        init() {
            self = .none
        }

        var name: String {
            return "PAY_BILLS_PROCESSING"
        }
    }

    enum AddMoneyViaCard: String, AnalyticsModule {
        case _3ds = "3DS"
        case confirmation = "CONFIRMATION"
        case `continue` = "CONTINUE"
        case create = "CREATE"
        case execute = "EXECUTE"
        case submit = "SUBMIT"
        case form = "FORM"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "ADD_MONEY_VIA_CARD"
        }
    }

    enum VirtualCard: String, AnalyticsModule { // TODO: Cleanup?
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "VIRTUAL_CARD"
        }
    }

    enum P2M: String, AnalyticsModule {
        case `continue` = "CONTINUE"
        case create = "CREATE"
        case pay = "PAY"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "P2M"
        }
    }

    enum P2MSuccess: String, AnalyticsModule {
        case none = ""
        case backToMerchant = "BACK_TO_MERCHANT"
        case viewReceipt = "VIEW_RECEIPT"
        case finish = "FINISH"

        init() {
            self = .none
        }

        var name: String {
            return "P2M_SUCCESS"
        }
    }

    enum DynamicP2M: String, AnalyticsModule {
        case `continue` = "CONTINUE"
        case create = "CREATE"
        case pay = "PAY"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "DYNAMIC_P2M"
        }
    }

    enum BankTransfer: String, AnalyticsModule {
        case `continue` = "CONTINUE"
        case create = "CREATE"
        case send = "SEND"
        case confirmation = "CONFIRMATION"
        case maintenance = "MAINTENANCE"
        case shop = "MAINTENANCE_SHOP"
        case createOTP = "CREATE_OTP"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "BANK_TRANSFER"
        }
    }

    enum BankTransferSelection: String, AnalyticsModule {
        case favorite = "FAVORITE"
        case bank = "BANK"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "BANK_TRANSFER_BANK_SELECTION"
        }
    }

    enum BankTransferAddToFavorites: String, AnalyticsModule {
        case add = "ADD"
        case notNow = "NOT_NOW"
        case retry = "RETRY"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "BANK_TRANSFER_ADD_TO_FAVORITE"
        }
    }

    enum AddToFavorites: String, AnalyticsModule {
        case add = "ADD"
        case back = "BACK"
        case sampleReminder = "SAMPLE_REMINDER"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "ADD_TO_FAVORITES"
        }
    }

    enum AddToFavoritesReminder: String, AnalyticsModule {
        case save = "SAVE"
        case cancel = "CANCEL"
        case sample = "SAMPLE"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "ADD_TO_FAVORITES_REMINDER"
        }
    }

    enum Missions: String, AnalyticsModule {
        case none = ""
        case category = "CATEGORY"
        case mission = "MISSION"
        case missionItem = "MISSION_ITEM"
        case failure = "FAILURE"
        case cta = "CTA"
        case enterCodeButton = "BUTTON"
        case enterCode = "ENTER_CODE"

        init() {
            self = .none
        }

        var name: String {
            switch self {
            case .cta: return "MISSIONS_MISSION"
            case .enterCodeButton: return "MISSIONS_ENTER_CODE"
            default: return "MISSIONS"
            }
        }
    }

    enum DeepLink: String, AnalyticsModule {
        case link = "LINK"
        case none = ""
        init() {
            self = .none
        }

        var name: String {
            return "DEEP_LINK"
        }
    }

    enum RequestMoney: String, AnalyticsModule {
        case sendRequest = "SEND_REQUEST"
        case guide = "GUIDE"
        case customize = "CUSTOMIZE"
        case share = "SHARE"
        case verifiedSellerBadge = "VERIFIED_SELLER_BADGE"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "REQUEST_MONEY"
        }
    }

    enum RequestMoneyCustomize: String, AnalyticsModule {
        case accountLimits = "ACCOUNT_LIMITS"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "REQUEST_MONEY_CUSTOMIZE"
        }
    }

    enum RequestMoneyForm: String, AnalyticsModule {
        case `continue` = "CONTINUE"
        case share = "SHARE"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "REQUEST_MONEY_FORM"
        }
    }

    enum RequestMoneySuccess: String, AnalyticsModule {
        case share = "SHARE"
        case home = "BACK_TO_HOME"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "REQUEST_MONEY_SUCCESS"
        }
    }

    enum BPIPullFunds: String, AnalyticsModule {
        case form = "FORM"
        case create = "CREATE"
        case confirmation = "CONFIRMATION"
        case execute = "EXECUTE"
        case _3ds = "3DS"

        var name: String {
            return "BPI"
        }
    }

    enum BankPull: String, AnalyticsModule {
        case none = ""
        case linkedAccount = "LINKED_ACCOUNT"
        case bank = "BANK"
        case create = "CREATE"
        case confirmation = "CONFIRMATION"
        case execute = "EXECUTE"
        case _3ds = "3DS"
        case unlink = "UNLINK"
        case form = "FORM"
        case accountLimits = "ACCOUNT_LIMITS"
        case linkNewAccount = "LINK_NEW_ACCOUNT"
        case link = "LINK"
        case `continue` = "CONTINUE"
        case remove = "REMOVE"
        case confirm = "CONFIRM"

        init() {
            self = .none
        }

        var name: String {
            switch self {
            case .accountLimits, .link:
                return "BANK_PULL_FORM"
            case .remove, .confirm:
                return "BANK_PULL_UNLINK"
            default: return "BANK_PULL"
            }
        }
    }

    enum AccountLimits: String, AnalyticsModule {
        case addMoney = "ADDMONEY"
        case learn = "LEARN"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "ACCOUNT_LIMITS"
        }
    }

    enum ESimsCreatorStore: String, AnalyticsModule {
        case empty = "EMPTY"
        case merchant = "MERCHANT"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "ESIMS"
        }
    }

    enum FoodCreatorStore: String, AnalyticsModule {
        case empty = "EMPTY"
        case merchant = "MERCHANT"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "FOOD"
        }
    }

    enum FundsCreatorStore: String, AnalyticsModule {
        case empty = "EMPTY"
        case merchant = "MERCHANT"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "FUNDS"
        }
    }

    enum RetailOnlinePayment: String, AnalyticsModule {
        case emptyScreen = "EMPTY"
        case tapMerchants = "MERCHANT"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "RETAIL"
        }
    }

    enum SparkHackathonCreatorStore: String, AnalyticsModule {
        case empty = "EMPTY"
        case merchant = "MERCHANT"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "SPARK_HACKATHON"
        }
    }

    enum GovernmentOnlinePayment: String, AnalyticsModule {
        case emptyScreen = "EMPTY"
        case checkMerchants = "PARTNERMERCHANTS"
        case tapMerchants = "MERCHANT"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            switch self {
            case .checkMerchants: return "GOVERNMENT_EMPTY_SCREEN"
            default: return "GOVERNMENT"
            }
        }
    }

    enum InsuranceCreatorStore: String, AnalyticsModule {
        case empty = "EMPTY"
        case merchant = "MERCHANT"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "INSURANCE"
        }
    }

    enum LuckyGamesCreatorStore: String, AnalyticsModule {
        case empty = "EMPTY"
        case merchant = "MERCHANT"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "LUCKY_GAMES"
        }
    }

    enum MerchantRewardsCreatorStore: String, AnalyticsModule {
        case empty = "EMPTY"
        case merchant = "MERCHANT"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "MERCHANT_REWARDS"
        }
    }

    enum PayLaterCreatorStore: String, AnalyticsModule {
        case empty = "EMPTY"
        case merchant = "MERCHANT"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "PAYLATER"
        }
    }

    enum StocksCreatorStore: String, AnalyticsModule {
        case empty = "EMPTY"
        case merchant = "MERCHANT"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "STOCKS"
        }
    }

    enum RemittanceCreatorStore: String, AnalyticsModule {
        case empty = "EMPTY"
        case merchant = "MERCHANT"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "REMITTANCE"
        }
    }

    enum StocksGameCreatorStore: String, AnalyticsModule {
        case empty = "EMPTY"
        case merchant = "MERCHANT"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "STOCKS_GAME"
        }
    }

    enum StreamCreatorStore: String, AnalyticsModule {
        case empty = "EMPTY"
        case merchant = "MERCHANT"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "STREAM"
        }
    }

    enum CreatorStoreInterstitial: String, AnalyticsModule {
        case none = ""
        case `continue` = "CONTINUE"

        init() {
            self = .none
        }

        var name: String { "CREATOR_STORE_INTERSTITIAL" }
    }

    enum InsuranceCreatorStoreInterstitial: String, AnalyticsModule {
        case none = ""
        case getStarted = "GET_STARTED"
        case back = "BACK"

        init() {
            self = .none
        }

        var name: String { "INSURANCE_INTRO" }
    }

    enum StocksCreatorStoreInterstitial: String, AnalyticsModule {
        case none = ""
        case getStarted = "GET_STARTED"
        case back = "BACK"

        init() {
            self = .none
        }

        var name: String { "STOCKS_INTRO" }
    }

    enum RemittanceCreatorStoreInterstitial: String, AnalyticsModule {
        case none = ""
        case getStarted = "GET_STARTED"
        case back = "BACK"

        init() {
            self = .none
        }

        var name: String { "REMITTANCE_INTRO" }
    }

    enum StocksGameStoreInterstitial: String, AnalyticsModule {
        case none = ""
        case getStarted = "GET_STARTED"
        case back = "BACK"

        init() {
            self = .none
        }

        var name: String { "STOCKS_GAME_INTRO" }
    }

    enum StreamStoreInterstitial: String, AnalyticsModule {
        case none = ""
        case getStarted = "GET_STARTED"
        case back = "BACK"

        init() {
            self = .none
        }

        var name: String { "STREAM_INTRO" }
    }

    enum LuckyGamesStoreInterstitial: String, AnalyticsModule {
        case none = ""
        case getStarted = "GET_STARTED"
        case back = "BACK"
        case terms = "TERMS"

        init() {
            self = .none
        }

        var name: String { "LUCKY_GAMES_INTRO" }
    }

    enum SparkHackathonStoreInterstitial: String, AnalyticsModule {
        case none = ""
        case getStarted = "GET_STARTED"
        case back = "BACK"

        init() {
            self = .none
        }

        var name: String { "SPARK_HACKATHON_INTRO" }
    }

    enum FundsStoreInterstitial: String, AnalyticsModule {
        case none = ""
        case getStarted = "GET_STARTED"
        case back = "BACK"

        init() {
            self = .none
        }

        var name: String { "FUNDS_INTRO" }
    }

    enum PhilippineAirlinesInterstitial: String, AnalyticsModule {
        case none = ""
        case getStarted = "GET_STARTED"
        case back = "BACK"

        init() {
            self = .none
        }

        var name: String { "PAL_INTRO" }
    }

    enum Voucher: String, AnalyticsModule {
        case splash = "SPLASH"
        case splashGotIt = "GOTIT"
        case tooltip = "TOOLTIP"
        case notif = "NOTIF"
        case emptyDeals = "DEALS"
        case confirm = "CONFIRM"
        case execute = "EXECUTE"
        case reminder = "REMINDER"
        case voucher = "VOUCHER"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            switch self {
            case .splashGotIt: return "VOUCHER_SPLASH_SCREEN"
            case .notif: return "VOUCHER_REMINDER"
            case .emptyDeals: return "VOUCHER_EMPTYAVAILABLETAB"
            default: return "VOUCHER"
            }
        }
    }

    enum VoucherCalendarReminder: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "VOUCHER_REMINDER"
        }
    }

    enum MyFavorites: String, AnalyticsModule {
        case select = "SELECT"
        case delete = "DELETE"
        case empty = "EMPTY"
        case maintenance = "MAINTENANCE"
        case splash = "SPLASH"
        case all = "ALL"
        case billsPay = "BILLS_PAY"
        case banks = "BANKS"
        case sendMoney = "SEND_MONEY"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "MY_FAVORITES"
        }
    }

    enum EnterNewMobileNumber: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "ENTER_NEW_MOBILE_NUMBER"
        }
    }

    enum AccountRecoveryScreen: String, AnalyticsModule {
        case verify = "VERIFY"

        var name: String {
            return "ACCOUNT_RECOVERY"
        }
    }

    enum MobileNumber: String, AnalyticsModule {
        case changeMobile = "CHANGE_MOBILE"
        case noAccess = "NO_ACCESS"
        case guide = "GUIDE"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "MOBILE_NUMBER"
        }
    }

    enum MobileNumberReminders: String, AnalyticsModule {
        case cancel = "CANCEL"
        case noAccess = "NO_ACCESS"
        case continueAction = "CONTINUE"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "MOBILE_NUMBER_REMINDERS"
        }
    }

    // Do not add cases here for 2.0 design, use MayaReceipt instead
    enum Receipt: String, AnalyticsModule {
        case backToHome = "BACK_TO_HOME"
        case addToFavorites = "ADD_TO_FAVORITES"
        case share = "SHARE"
        case viewQR = "VIEW_QR"
        case addToContacts = "ADD_TO_CONTACTS"
        case verifiedSellerBadge = "VERIFIED_SELLER_BADGE"
        case reportIssue = "REPORT_ISSUE"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "RECEIPT"
        }
    }

    enum Scan: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }
    }

    enum ScanQr: String, AnalyticsModule {
        case gallery = "GALLERY"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "SCAN_QR"
        }
    }

    enum NewScanQr: String, AnalyticsModule {
        case upload = "UPLOAD_QR"
        case show = "SHOW_QR"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "SCAN"
        }
    }

    enum ScanBarcode: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "BARCODE_SCAN"
        }
    }

    enum PayBillsSearch: String, AnalyticsModule {
        case none = ""
        case biller = "BILLER"

        init() {
            self = .none
        }

        var name: String {
            return "PAY_BILLS_SEARCH"
        }
    }

    enum PayBillsCategory: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "PAY_BILLS_CATEGORY"
        }
    }

    enum Transport: AnalyticsModule {
        case none
        case operators
        case routes
        case stops
        case next(String?)
        case buy(String?)
        case accessPhotos(String?)
        case confirmation
        case create

        init() {
            self = .none
        }

        var name: String {
            switch self {
            case .next(let module):
                return module ?? "TRANSPORT"
            case .buy(let module):
                return module ?? "TRANSPORT"
            case .accessPhotos(let module):
                return module ?? "TRANSPORT"
            case .operators:
                return "TRANSPORT_OPERATORS"
            case .routes:
                return "TRANSPORT_ROUTES"
            case .stops:
                return "TRANSPORT_STOPS"
            case .confirmation:
                return "TRANSPORT_CONFIRMATION"
            case .create:
                return "TRANSPORT_CREATE"
            default: return "TRANSPORT"
            }
        }

        var object: String {
            switch self {
            case .none:
                return ""
            case .operators, .routes, .stops, .confirmation, .create:
                return ""
            case .next:
                return "NEXT"
            case .buy:
                return "BUY"
            case .accessPhotos:
                return "ACCESS_PHOTOS"
            }
        }
    }

    enum SetPublicName: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "SET_PUBLIC_NAME"
        }
    }

    enum WebView: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "WEBVIEW"
        }
    }

    enum YearEndReview: String, AnalyticsModule {
        case none = ""
        case share = "SHARE"
        case donate = "DONATE"
        case backToHome = "BACK_TO_HOME"

        init() {
            self = .none
        }

        var name: String {
            return "YIR"
        }
    }

    enum Travel: String, AnalyticsModule {
        case none = ""
        case beep = "BEEP"
        case train = "TRAIN"
        case airfare = "AIRFARE"
        case toll = "TOLL"
        case merchants = "MERCHANTS"

        init() {
            self = .none
        }

        var name: String {
            return "TRAVEL"
        }
    }

    enum Disclosure: String, AnalyticsModule {
        case none = ""
        case agree = "AGREE"

        init() {
            self = .none
        }

        var name: String {
            return "DISCLOSURE"
        }
    }

    enum LocationPermissionTimeout: String, AnalyticsModule {
        case settings = "SETTINGS"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "LOCATION_PERMISSION_TIMEOUT"
        }
    }

    enum LocationPermissionPrompt: String, AnalyticsModule {
        case allow = "ALLOW"
        case deny = "DENY"

        var name: String {
            return "LOCATION_PERMISSION_PROMPT"
        }
    }

    enum LocationPermission: String, AnalyticsModule {
        case none = ""
        case allow = "ALLOW"
        case `continue` = "CONTINUE"
        case skip = "SKIP"

        var name: String {
            return "LOCATION_PERMISSION"
        }

        init() {
            self = .none
        }
    }

    enum QRPH: String, AnalyticsModule {
        case none = ""
        case upgrade = "UPGRADE"
        case checkStatus = "CHECK_STATUS"

        init() {
            self = .none
        }

        var name: String {
            return "QRPH_UPGRADE"
        }
    }

    enum MayaCreditSummary: String, AnalyticsModule {
        case none = ""
        case transfer = "TRANSFER"
        case payBill = "PAY_BILL"
        case viewBill = "VIEW_BILL"

        init() {
            self = .none
        }

        var name: String {
            return "MAYA_CREDIT_SUMMARY"
        }
    }

    enum MayaCreditSettings: String, AnalyticsModule {
        case view = "VIEW"
        case updateEmail = "UPDATE_EMAIL"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "MAYA_CREDIT_SETTINGS"
        }
    }

    enum MayaCreditTransferCredit: String, AnalyticsModule {
        case next = "CONTINUE"
        case helpCenter = "HELP_CENTER"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "MAYA_CREDIT_TRANSFER_CREDIT"
        }
    }

    enum MayaCreditOptIn: String, AnalyticsModule {
        case agree = "AGREE"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "MAYA_CREDIT_OPT_IN"
        }
    }

    enum NewMayaCreditOptIn: String, AnalyticsModule {
        case signUp = "SIGN_UP"
        case faq = "FAQS"
        case dataPrivacy = "DATA_PRIVACY"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_OPT_IN"
        }
    }

    enum NewMayaCreditOptInDataPrivacy: String, AnalyticsModule {
        case agree = "AGREE"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_OPT_IN_DATA_PRIVACY"
        }
    }

    enum MayaCreditActivation: String, AnalyticsModule {
        case none = ""
        case setEndDate = "SET_END_DATE"
        case serviceFee = "SERVICE_FEE"
        case agreement = "AGREEMENT"
        case updateEmail = "UPDATE_EMAIL"
        case activate = "ACTIVATE"

        init() {
            self = .none
        }

        var name: String {
            return "MAYA_CREDIT_ACTIVATION"
        }
    }

    enum MayaCreditApplication: String, AnalyticsModule {
        case none = ""
        case geolocation = "GEOLOC"
        case access = "ACCESS"
        case deviceSettings = "DEVSETTINGS"

        init() {
            self = .none
        }

        var name: String {
            return "MEC_APPLICATION"
        }
    }

    enum MayaCreditDrawdown: String, AnalyticsModule {
        case none = ""
        case geolocation = "GEOLOC"

        init() {
            self = .none
        }

        var name: String {
            return "MEC_DRAWDOWN"
        }
    }

    enum LendingToggles: String, AnalyticsModule {
        case none = ""
        case creditCard = "CREDIT_CARD"
        case loans = "LOANS"
        case mecDeviceScoring = "MEC_DEVICE_SCORING"

        init() {
            self = .none
        }

        var name: String {
            return "LENDING_TOGGLES"
        }
    }

    enum NewMayaCreditActivation: String, AnalyticsModule {
        case none = ""
        case setEndDate = "CHANGE_DATE"
        case learnMore = "LEARN_MORE"
        case agreement = "AGREEMENT"
        case updateEmail = "UPDATE_EMAIL"
        case activate = "ACTIVATE"

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_ACTIVATION"
        }
    }

    enum MayaCreditInitialEligibility: String, AnalyticsModule {
        case none = ""
        case dashboardApply = "DASHBOARD_APPLY"
        case introContinue = "INTRO_CONTINUE"
        case offerView = "OFFER_VIEW"
        case offerAccept = "OFFER_ACCEPT"
        case initialEligibilityIneligible = "INITIAL_CHECKS_INELIGIBLE"
        case fullEligibilityIneligible = "FULL_CHECKS_INELIGIBLE"

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT"
        }
    }

    enum MayaCreditScoringExtract: String, AnalyticsModule {
        case none = ""
        case extract = "EXTRACT"

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_SCORING"
        }
    }

    enum NewMayaCreditSelectBillEndDate: String, AnalyticsModule {
        case none = ""
        case set = "SET"

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_SELECT_BILL_END_DATE"
        }
    }

    enum MayaCreditConfirmationChangeBillingDate: String, AnalyticsModule {
        case none = ""
        case change = "CHANGE"
        case confirm = "CONFIRM"

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_CONFIRM_BILL_END_DATE"
        }
    }

    enum NewMayaCreditServiceFee: String, AnalyticsModule {
        case none = ""
        case gotIt = "GOT_IT"

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_SERVICE_FEE"
        }
    }

    enum NewMayaCreditSummary: String, AnalyticsModule {
        case none = ""
        case accountSummary = "ACCOUNT_SUMMARY"
        case transferToWallet = "TRANSFER_TO_WALLET"
        case statement = "BILL_STATEMENT"
        case payBillNow = "BILL_PAY_NOW"
        case payNow = "PAY_NOW"

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_SUMMARY"
        }
    }

    enum MayaCreditBillingStatement: String, AnalyticsModule {
        case none = ""
        case statement = "BILL_STATEMENT_VIEW"
        case payBillNow = "BILL_PAY_NOW"
        case payNow = "OUTSTANDING_BALANCE_PAY_NOW"
        case billToolTip = "BILL_TOOLTIP_VIEW"

        init() {
            self = .none
        }

        var name: String {
            return "MAYA_CREDIT_BILLING_STATEMENT"
        }
    }

    enum MayaCreditAccountSummary: String, AnalyticsModule {
        case none = ""
        case updateEmail = "UPDATE_EMAIL"
        case terms = "TERMS"
        case truth = "TRUTH"
        case disclosure = "DISCLOSURE"
        case howItWorks = "HOWITWORKS"
        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_ACCOUNT_SUMMARY"
        }
    }

    enum NewMayaCreditTransferCredit: String, AnalyticsModule {
        case next = "CONTINUE"
        case viewSample = "VIEW_SAMPLE"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_TRANSFER"
        }
    }

    enum MayaCreditTransferCreditFeeSheet: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_TRANSFER_SAMPLE_CALCULATION"
        }
    }

    enum NewMayaCreditTransferCreditConfirmation: String, AnalyticsModule {
        case next = "CONFIRM"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_TRANSFER"
        }
    }

    enum MayaCreditSuccessTransfer: String, AnalyticsModule {
        case next = "CONTINUE"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_TRANSFER_PROCESSING"
        }
    }

    enum NewMayaCreditAgreement: String, AnalyticsModule {
        case none = ""
        case agree = "AGREE"

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_AGREEMENT"
        }
    }

    enum MayaCreditApprovedReview: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_REVIEW"
        }
    }

    enum MayaCreditAgreement: String, AnalyticsModule {
        case none = ""
        case agree = "AGREE"

        init() {
            self = .none
        }

        var name: String {
            return "MAYA_CREDIT_AGREEMENT"
        }
    }

    enum MayaCreditServiceFee: String, AnalyticsModule {
        case none = ""
        case gotIt = "GOT_IT"

        init() {
            self = .none
        }

        var name: String {
            return "MAYA_CREDIT_SERVICE_FEE"
        }
    }

    enum MayaCreditSelectBillEndDate: String, AnalyticsModule {
        case none = ""
        case set = "SET"

        init() {
            self = .none
        }

        var name: String {
            return "MAYA_CREDIT_SELECT_BILL_END_DATE"
        }
    }

    enum MayaDataExtraction: String, AnalyticsModule {
        case none = ""
        case extract = "EXTRACT"

        init() {
            self = .none
        }

        var name: String {
            return "MAYA_DATA_EXTRACTION"
        }
    }

    enum CreditOptIn: String, AnalyticsModule {
        case getStarted = "GET_STARTED"
        case kyc0 = "KYC0"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_OPT_IN"
        }
    }

    enum SavingsDashboard: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "SAVINGS_DASHBOARD"
        }
    }

    enum LoansDashboard: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "LOANS_DASHBOARD"
        }
    }

    enum CreditOptInKYC0: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_OPT_IN_KYC0"
        }
    }

    enum CreditNotEligible: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_NOT_ELIGIBLE"
        }
    }

    enum CreditActivate: String, AnalyticsModule {
        case activate = "ACTIVATE"
        case help = "HELP"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_ACTIVATE"
        }
    }

    enum MayaCreditActivate: String, AnalyticsModule {
        case activate = "ACTIVATE"
        case help = "HELP"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_ACTIVATE"
        }
    }

    enum MayaCreditRiskChallenge: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_RISK"
        }
    }

    enum MayaCreditMfaCallback: String, AnalyticsModule {
        case none = ""
        case maxAttempt = "MAX_ATTEMPT"
        case fallback = "FALLBACK"

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_MFA"
        }
    }

    enum MayaCreditMfaUserCallback: String, AnalyticsModule {
        case none = ""
        case cancel = "CANCEL"

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_MFA_USER"
        }
    }

    enum CreditApprovedReview: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_APPROVED_REVIEW"
        }
    }

    enum CreditSubmittedReview: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_SUBMITTED_REVIEW"
        }
    }

    enum CreditSummary: String, AnalyticsModule {
        case none = ""
        case accountSummary = "ACCOUNT_SUMMARY"
        case transferToWallet = "TRANSFER_TO_WALLET"
        case settings = "SETTINGS"
        case payBill = "PAY_BILL"

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_SUMMARY"
        }
    }

    enum Credit: String, AnalyticsModule {
        case bsp = "BSP"
        case support = "SUPPORT"
        case chatWithUs = "CHAT_WITH_US"
        case servieAdvisories = "SERVICE_ADVISORIES"
    }

    enum PreKYC: AnalyticsModule {
        case upgradeNow(String?)

        var name: String {
            switch self {
            case .upgradeNow(let module):
                return module ?? "KYC0"
            }
        }

        var object: String {
            switch self {
            case .upgradeNow:
                return "UPGRADE_NOW"
            }
        }
    }

    enum ReKYC: AnalyticsModule {
        case intro(String?)
        case inReview(String?)
        case otherId

        var name: String {
            switch self {
            case .intro(let module), .inReview(let module):
                return module ?? "EKYC_V6"
            case .otherId:
                return "EKYC_V6"
            }
        }

        var object: String {
            switch self {
            case .intro:
                return "REKYC_INTRO"
            case .inReview:
                return "REKYC_EXIS_SUB"
            case .otherId:
                return "REKYC_OTHER_ID"
            }
        }
    }

    enum SlideToConfirm: AnalyticsModule {
        case cancel(String)

        var name: String {
            switch self {
            case .cancel(let module):
                return module
            }
        }

        var object: String {
            switch self {
            case .cancel:
                return "CANCEL"
            }
        }
    }

    enum Wallet: String, AnalyticsModule {
        case banner = "BANNER"
        case exit = "EXIT"
        case item = "ITEM"
        case none = ""
        case optionsScreen = "OPTIONS_SCREEN"
        case tile = "TILE"

        init() {
            self = .none
        }

        var name: String { "WALLET" }
    }

    enum FloatingNavigation: String, AnalyticsModule {
        case item = "ITEM"

        var name: String { "FLOATING_NAVIGATION" }
    }

    enum Services: String, AnalyticsModule {
        case none = ""
        case tile = "TILE"
        case actions = "MORE_ACTIONS"
        case help = "GET_HELP"
        case settings = "SETTINGS"
        case about = "ABOUT_MAYA"
        case exit = "EXIT"
        case item = "ITEM"

        init() {
            self = .none
        }

        var name: String {
            return "SERVICES"
        }
    }

    enum ServicesBanner: String, AnalyticsModule {
        case none = ""

        var name: String {
            return "SERVICES_BANNER"
        }
    }

    enum MenuBanner: String, AnalyticsModule {
        case none = ""

        var name: String {
            return "MENU_BANNER"
        }
    }

    enum AutoCashIn: String, AnalyticsModule {
        case uAccountUpgrade = "U_ACCOUNT_UPGRADE"
        case accountUpgrade = "ACCOUNT_UPGRADE"
        case screen = "SCREEN"
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "AUTO_CASH_IN"
        }
    }

    enum CashInMayaSavings: String, AnalyticsModule {
        case upgradeNow = "UPGRADE_NOW"
        var name: String {
            return "CASH_IN_MAYA_SAVINGS"
        }
    }

    enum CashIn: String, AnalyticsModule {
        case none = ""
        case mayaSavings = "MAYA_SAVINGS"
        case mayaSavingsAccountUpgrade = "MAYA_SAVINGS_ACCOUNT_UPGRADE"

        init() {
            self = .none
        }

        var name: String {
            return "CASH_IN"
        }
    }

    enum BankTransferProcessing: String, AnalyticsModule {
        case none = ""
        case done = "DONE"
        case receipt = "RECEIPT"
        case favorites = "FAVORITES"

        init() {
            self = .none
        }

        var name: String {
            return "BANK_TRANSFER_PROCESSING"
        }
    }

    enum PushApprovalRequestDenied: String, AnalyticsModule {
        case none = ""
        case done = "DONE"
        case report = "REPORT"

        init() {
            self = .none
        }

        var name: String {
            return "PUSH_APPROVAL_REQUEST_DENIED"
        }
    }

    enum MayaCreditTransactions: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "CREDIT_TRANSACTIONS"
        }
    }

    enum PushApprovalList: String, AnalyticsModule {
        case none = ""
        case `return` = "RETURN"
        case refresh = "REFRESH"
        case request = "REQUEST"

        init() {
            self = .none
        }

        var name: String {
            return "PUSH_APPROVAL_LIST"
        }
    }

    enum PushApprovalReviewRequest: String, AnalyticsModule {
        case none = ""
        case approve = "APPROVE"
        case deny = "DENY"

        init() {
            self = .none
        }

        var name: String {
            return "PUSH_APPROVAL_REQUEST"
        }
    }

    enum PushApprovalReviewRequestApproved: String, AnalyticsModule {
        case none = ""
        case done = "DONE"

        init() {
            self = .none
        }

        var name: String {
            return "PUSH_APPROVAL_REQUEST_APPROVED"
        }
    }

    enum PushApprovalReviewRequestExpired: String, AnalyticsModule {
        case none = ""
        case done = "DONE"

        init() {
            self = .none
        }

        var name: String {
            return "PUSH_APPROVAL_REQUEST_EXPIRED"
        }
    }

    enum DynamicServices: AnalyticsModule {
        case module(String)

        var name: String {
            switch self {
            case .module(let eventName): return eventName
            }
        }

        var object: String {
            switch self {
            case .module: return ""
            }
        }
    }

    enum MayaReceipt: AnalyticsModule {
        case none(String?)
        case banner(String?)
        case close(String?)
        case share(String?)
        case reportIssue(String?)
        case addToFavorites
        case viewQR
        case addToContacts
        case verifiedSellerBadge

        init(_ module: String? = nil) {
            self = .none(module)
        }

        var name: String {
            switch self {
            case .none(let module),
                    .banner(let module),
                    .close(let module),
                    .share(let module),
                    .reportIssue(let module):
                return module ?? "RECEIPT"
            default: return "RECEIPT"
            }
        }

        var object: String {
            switch self {
            case .none: return ""
            case .banner: return "BANNER"
            case .close: return "CLOSE"
            case .share: return "SHARE"
            case .reportIssue: return "REPORT"
            case .addToFavorites: return "ADD_TO_FAVORITES"
            case .viewQR: return "VIEW_QR"
            case .addToContacts: return "ADD_TO_CONTACTS"
            case .verifiedSellerBadge: return "VERIFIED_SELLER_BADGE"
            }
        }
    }

    enum CreatorStoreRestriction: String, AnalyticsModule {
        case none = ""
        case back = "BACK"

        init() {
            self = .none
        }

        var name: String {
            return "CREATOR_STORE_RESTRICTION"
        }
    }

    enum Error: String, AnalyticsModule {
        case link = "LINK"
    }

    enum Maintenance: String, AnalyticsModule {
        case none = ""
        case link = "LINK"
        case help = "HELP"

        init() {
            self = .none
        }

        var name: String {
            return "MAINTENANCE"
        }
    }

    enum AgeRestricted: String, AnalyticsModule {
        case none = ""
        case gotIt = "GOT_IT"

        init() {
            self = .none
        }

        var name: String {
            return "AGE_RESTRICTED"
        }
    }

    enum LoansCreditCard: AnalyticsModule {
        case none(String?)
        case introOverview(String?)
        case repayment
        case kyc
        case updateProfile

        init(_ module: String? = nil) {
            self = .none(module)
        }

        var name: String {
            switch self {
            case .none(let module),
                    .introOverview(let module):
                return module ?? "CC"
            default: return "CC"
            }
        }

        var object: String {
            switch self {
            case .none: return ""
            case .introOverview: return "INTRO_OVERVIEW"
            case .repayment: return "REPAYMENT"
            case .kyc: return "KYC"
            case .updateProfile: return "UPDATE_PROFILE"
            }
        }
    }

    enum InAppReviewPrompt: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "IN_APP_REVIEW_PROMPT"
        }
    }

    enum DeviceExtraction: String, AnalyticsModule {
        case callback = "CALLBACK"
        case send = "SEND"

        var name: String {
            return "DEVICE_EXTRACTION"
        }
    }

    enum UpdateProfile: AnalyticsModule {
        case creditDetails
        case workDetails
        case contactRef

        var name: String {
            return "PROFILE"
        }

        var object: String {
            switch self {
            case .creditDetails:
                return "CREDIT_DETAILS"
            case .workDetails:
                return "WORK_DETAILS"
            case .contactRef:
                return "CONTACT_REF"
            }
        }

        enum CreditDetails: String, AnalyticsModule {
            case back = "BACK"
            case next = "NEXT"
            case none = ""

            var name: String {
                return "PROFILE_CREDIT_DETAILS"
            }

            init() {
                self = .none
            }
        }

        enum WorkDetails: String, AnalyticsModule {
            case back = "BACK"
            case next = "NEXT"
            case none = ""

            var name: String {
                return "PROFILE_WORK_DETAILS"
            }

            init() {
                self = .none
            }
        }

        enum ContactReference: String, AnalyticsModule {
            case back = "BACK"
            case next = "NEXT"
            case none = ""

            var name: String {
                return "PROFILE_CONTACT_REF"
            }

            init() {
                self = .none
            }
        }
    }

    enum ActionCard: String, AnalyticsModule {
        case none = ""

        var name: String {
            return "ACTION_CARD"
        }
    }

    enum CreatorStoreBanner: String, AnalyticsModule {
        case none = ""

        var name: String {
            return "CREATOR_STORE_BANNER"
        }
    }

    enum CreatorStoreBannerV2: String, AnalyticsModule {
        case none = ""

        var name: String {
            return "CREATOR_STORE_BANNER_V2"
        }
    }

    enum FATCA: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "FATCA"
        }
    }

    enum DOSRI: String, AnalyticsModule {
        case none = ""

        init() {
            self = .none
        }

        var name: String {
            return "DOSRI"
        }
    }

    enum RefereeMGM: AnalyticsModule {
        case inputCodeSuccessSheet(InputCodeSuccessSheet)
        case welcomeSheet(WelcomeSheet)
        case inputCodeScreen

        var name: String {
            return "REFEREE_MGM"
        }

        var object: String {
            switch self {
            case .inputCodeSuccessSheet(let target):
                return target.object
            case .welcomeSheet(let target):
                return target.object
            case .inputCodeScreen:
                return "INPUT_CODE"
            }
        }

        enum WelcomeSheet: String, AnalyticsModule {
            case inputCode = "INPUT_CODE"
            case upgradeNow = "UPGRADE"
            case goToHome = "HOME"
            case none = ""

            var name: String {
                return "WELCOME"
            }

            var object: String {
                switch self {
                case .none:
                    return name
                default:
                    return "\(name)_\(rawValue)"
                }
            }
        }

        enum InputCodeSuccessSheet: String, AnalyticsModule {
            case upgradeButton = "UPGRADE"
            case homeButton = "HOME"

            var name: String {
                return "INPUT_CODE"
            }

            var object: String {
                return "\(name)_\(rawValue)"
            }
        }
    }

    enum DeviceManagement {
        enum List: String, AnalyticsModule {
            case none = ""
            case exit
            case item
            case device

            init() {
                self = .none
            }

            var name: String {
                return "MANAGE_DEVICES"
            }

            var object: String {
                return rawValue.uppercased()
            }
        }

        enum Details: String, AnalyticsModule {
            case none = ""
            case item
            case exit

            init() {
                self = .none
            }

            var name: String {
                return "MANAGE_DEVICES_DEVICE"
            }

            var object: String {
                return rawValue.uppercased()
            }
        }
    }
}
