//
//  MayaTheme.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 11/22/21.
//  Copyright © 2021 PayMaya Philippines, Inc. All rights reserved.
//

import ConfigurationService
import Foundation
import Injector
import UIKit

enum MayaTheme {
    case maya // New Maya 2.0 theme

    enum ButtonType { // Can be identified in Zeplin. Specific to PayMaya 1.5
        case primary(MayaTheme.ButtonSize)
        case secondary(MayaTheme.ButtonSize)
        case tertiary
        case destructive
    }

    enum ButtonColor { // Can be identified in Zeplin. Used in Maya 2.0
        case primary
        case secondary
        case grey
        case black
        case white
        case mintGreen
        case charcoalBlack
    }

    enum ButtonSize { // Can be identified in Zeplin
        case xsmall // used in paymaya 1.5
        case small
        case `default` // used in paymaya 1.5
        case large
        case medium
        case custom(font: UIFont)
    }

    var theme: MayaThemeBaseProtocol {
        switch self {
        case .maya: return MayaThemed()
        }
    }
}
