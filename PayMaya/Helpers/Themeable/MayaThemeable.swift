//
//  MayaThemeable.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 2/10/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import Foundation
import UIKit

protocol MayaThemeable {
    var mayaTheme: MayaTheme? { get }
}

protocol MayaThemeBaseProtocol {
    func getButtonTheme(forMayaThemeButtonColor color: MayaTheme.ButtonColor) -> (ButtonThemeBaseProtocol & ButtonThemePrimaryProtocol)?
}

// MARK: - ButtonThemeable
protocol ButtonThemeBaseProtocol {
    var buttonFontForLarge: UIFont { get }
    var buttonFontForMedium: UIFont? { get }
    var buttonFontForSmall: UIFont { get }
    var buttonFontForExtraSmall: UIFont? { get }
}

protocol ButtonThemePrimaryProtocol {
    var buttonPrimaryBackgroundColor: UIColor { get }
    var buttonPrimaryTextColor: UIColor { get }
    var buttonPrimaryCornerRadi<PERSON>: Float { get }
    var buttonPrimaryBorderWidth: Float? { get }
    var buttonPrimaryBorderColor: UIColor? { get }
    var buttonPrimaryDisabledBackgroundColor: UIColor { get }
    var buttonPrimaryDisabledTextColor: UIColor { get }
}

class MayaThemed: MayaThemeProtocol {
    func getButtonTheme(forMayaThemeButtonColor color: MayaTheme.ButtonColor) -> (ButtonThemeBaseProtocol & ButtonThemePrimaryProtocol)? {
        switch color {
        case .primary: return MayaThemeButtonPrimary()
        case .secondary: return MayaThemeButtonSecondary()
        case .grey: return MayaThemeButtonGrey()
        case .black: return MayaThemeButtonBlack()
        case .white: return MayaThemeButtonWhite()
        case .mintGreen: return MayaThemeButtonMintGreen()
        case .charcoalBlack: return MayaThemeButtonCharcoalBlack()
        }
    }
}

protocol MayaThemeProtocol: MayaThemeBaseProtocol {}
protocol MayaThemeButtonBaseProtocol: ButtonThemeBaseProtocol, ButtonThemePrimaryProtocol, MayaThemeButtonStateBackgroundProcol {}

protocol MayaThemeButtonStateBackgroundProcol {
    var buttonBackgroundHightlightColor: UIColor? { get }
    var buttonBackgroundDisabledColor: UIColor? { get }
    var buttonBackgroundDisabledAlpha: CGFloat? { get }
}

private class MayaThemeButton {
    var buttonFontForLarge: UIFont { return CommonFontFamily.Jeko.bold.font(size: 16) }
    var buttonFontForMedium: UIFont? { return CommonFontFamily.Jeko.bold.font(size: 16) }
    var buttonFontForSmall: UIFont { return CommonFontFamily.Jeko.bold.font(size: 14) }
    var buttonFontForExtraSmall: UIFont? { return nil }
}

private class MayaThemeButtonPrimary: MayaThemeButton, MayaThemeButtonBaseProtocol {
    var buttonPrimaryBackgroundColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryGreen.color }
    var buttonPrimaryTextColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryWhite.color }
    var buttonPrimaryCornerRadius: Float { return 16 }
    var buttonPrimaryBorderWidth: Float? { return nil }
    var buttonPrimaryBorderColor: UIColor? { return nil }
    var buttonPrimaryDisabledBackgroundColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryGreen.color }
    var buttonPrimaryDisabledTextColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryWhite.color }
    var buttonBackgroundHightlightColor: UIColor? { return CommonAsset.MayaColors.Button.buttonFocusedPrimaryGreen.color }
    var buttonBackgroundDisabledColor: UIColor? { return CommonAsset.MayaColors.Button.buttonPrimaryGreen.color }
    var buttonBackgroundDisabledAlpha: CGFloat? { return 0.40 }
}

private class MayaThemeButtonSecondary: MayaThemeButton, MayaThemeButtonBaseProtocol {
    var buttonPrimaryBackgroundColor: UIColor { return CommonAsset.MayaColors.Button.buttonSecondaryLightGreen.color }
    var buttonPrimaryTextColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryGreen.color }
    var buttonPrimaryCornerRadius: Float { return 16 }
    var buttonPrimaryBorderWidth: Float? { return nil }
    var buttonPrimaryBorderColor: UIColor? { return nil }
    var buttonPrimaryDisabledBackgroundColor: UIColor { return CommonAsset.MayaColors.Button.buttonSecondaryLightGreen.color }
    var buttonPrimaryDisabledTextColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryGreen.color }
    var buttonBackgroundHightlightColor: UIColor? { return CommonAsset.MayaColors.Button.buttonFocusedSecondaryLightGreen.color }
    var buttonBackgroundDisabledColor: UIColor? { return CommonAsset.MayaColors.Button.buttonSecondaryLightGreen.color }
    var buttonBackgroundDisabledAlpha: CGFloat? { return 0.40 }
}

private class MayaThemeButtonGrey: MayaThemeButton, MayaThemeButtonBaseProtocol {
    var buttonBackgroundDisabledColor: UIColor? { return CommonAsset.MayaColors.Button.buttonGrey3.color }
    var buttonPrimaryBackgroundColor: UIColor { return CommonAsset.MayaColors.Button.buttonGrey3.color }
    var buttonPrimaryTextColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryBlack.color }
    var buttonPrimaryCornerRadius: Float { return 16 }
    var buttonPrimaryBorderWidth: Float? { return nil }
    var buttonPrimaryBorderColor: UIColor? { return nil }
    var buttonPrimaryDisabledBackgroundColor: UIColor { return CommonAsset.MayaColors.Button.buttonGrey3.color }
    var buttonPrimaryDisabledTextColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryBlack.color }
    var buttonBackgroundHightlightColor: UIColor? { return CommonAsset.MayaColors.Button.buttonFocusedGrey.color }
    var buttonBackgroundDisabledAlpha: CGFloat? { return 0.40 }
}

private class MayaThemeButtonBlack: MayaThemeButton, MayaThemeButtonBaseProtocol {
    var buttonPrimaryBackgroundColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryBlack.color }
    var buttonPrimaryTextColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryWhite.color }
    var buttonPrimaryCornerRadius: Float { return 16 }
    var buttonPrimaryBorderWidth: Float? { return nil }
    var buttonPrimaryBorderColor: UIColor? { return nil }
    var buttonPrimaryDisabledBackgroundColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryBlack.color }
    var buttonPrimaryDisabledTextColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryWhite.color }
    var buttonBackgroundHightlightColor: UIColor? { return CommonAsset.MayaColors.Button.buttonFocusedBlack.color }
    var buttonBackgroundDisabledColor: UIColor? { return CommonAsset.MayaColors.Button.buttonPrimaryBlack.color }
    var buttonBackgroundDisabledAlpha: CGFloat? { return 0.40 }
}

private class MayaThemeButtonWhite: MayaThemeButton, MayaThemeButtonBaseProtocol {
    var buttonPrimaryBackgroundColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryWhite.color }
    var buttonPrimaryTextColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryGreen.color }
    var buttonPrimaryCornerRadius: Float { return 16 }
    var buttonPrimaryBorderWidth: Float? { return nil }
    var buttonPrimaryBorderColor: UIColor? { return nil }
    var buttonPrimaryDisabledBackgroundColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryWhite.color }
    var buttonPrimaryDisabledTextColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryGreen.color }
    var buttonBackgroundHightlightColor: UIColor? { return CommonAsset.MayaColors.Button.buttonPrimaryWhite.color }
    var buttonBackgroundDisabledColor: UIColor? { return CommonAsset.MayaColors.Button.buttonPrimaryWhite.color }
    var buttonBackgroundDisabledAlpha: CGFloat? { return 0.40 }
}

private class MayaThemeButtonMintGreen: MayaThemeButton, MayaThemeButtonBaseProtocol {
    var buttonPrimaryBackgroundColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryMintGreen.color }
    var buttonPrimaryTextColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryBlack.color }
    var buttonPrimaryCornerRadius: Float { return 16 }
    var buttonPrimaryBorderWidth: Float? { return nil }
    var buttonPrimaryBorderColor: UIColor? { return nil }
    var buttonPrimaryDisabledBackgroundColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryMintGreen.color }
    var buttonPrimaryDisabledTextColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryBlack.color }
    var buttonBackgroundHightlightColor: UIColor? { return CommonAsset.MayaColors.Button.buttonPrimaryMintGreen.color }
    var buttonBackgroundDisabledColor: UIColor? { return CommonAsset.MayaColors.Button.buttonPrimaryMintGreen.color }
    var buttonBackgroundDisabledAlpha: CGFloat? { return 0.40 }
}

private class MayaThemeButtonCharcoalBlack: MayaThemeButton, MayaThemeButtonBaseProtocol {
    var buttonPrimaryBackgroundColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryCharcoalBlack.color }
    var buttonPrimaryTextColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryWhite.color }
    var buttonPrimaryCornerRadius: Float { return 16 }
    var buttonPrimaryBorderWidth: Float? { return nil }
    var buttonPrimaryBorderColor: UIColor? { return nil }
    var buttonPrimaryDisabledBackgroundColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryCharcoalBlack.color }
    var buttonPrimaryDisabledTextColor: UIColor { return CommonAsset.MayaColors.Button.buttonPrimaryWhite.color }
    var buttonBackgroundHightlightColor: UIColor? { return CommonAsset.MayaColors.Button.buttonPrimaryCharcoalBlack.color }
    var buttonBackgroundDisabledColor: UIColor? { return CommonAsset.MayaColors.Button.buttonPrimaryCharcoalBlack.color }
    var buttonBackgroundDisabledAlpha: CGFloat? { return 0.40 }
}
