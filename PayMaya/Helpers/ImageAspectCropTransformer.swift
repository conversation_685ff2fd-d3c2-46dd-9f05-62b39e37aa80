//
//  ImageAspectCropTransformer.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 5/19/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import SDWebImage

class ImageAspectCropTransformer: NSObject, SDImageTransformer {
    let widthRatio: CGFloat
    let heightRatio: CGFloat

    init(widthRatio: CGFloat, heightRatio: CGFloat) {
        self.widthRatio = widthRatio
        self.heightRatio = heightRatio
    }

    func transformedImage(with image: UIImage, forKey key: String) -> UIImage? {
        return image.croppedToAspectRatio(widthRatio: widthRatio, heightRatio: heightRatio)
    }

    var transformerKey: String {
        return "ImageAspectCropTransformer(\(widthRatio):\(heightRatio))"
    }
}
