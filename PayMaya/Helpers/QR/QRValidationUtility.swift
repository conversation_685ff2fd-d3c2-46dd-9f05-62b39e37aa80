//
//  QRValidationUtility.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 6/2/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation
import MPQRCoreSDK

class QRValidationUtility: QRValidationUtilityProtocol {
    func checkQRPHFormatIndicator(qrCode: String) throws -> Bool {
        guard qrCode.contains(QRPHConstants.qrPHFormatIndicator) else {
            throw QRError.parsingError(field: QRPHField.payloadFormatIndicator)
        }

        return true
    }

    func checkQRPHCrc(qrCode: String) throws -> Bool {
        guard qrCode.count >= QRPHFieldLength.crc else {
            throw QRError.parsingError(field: QRPHField.crc)
        }

        let crcSegment = qrCode.suffix(QRPHFieldLength.crc)

        guard crcSegment.hasPrefix(QRPHConstants.qrPHCrcFieldTag) else {
            throw QRError.missingRequiredField(field: QRPHField.crc, bic: "")
        }

        return true
    }

    func validateQRPHCrc(qrCode: String, bic: String = "") throws -> Bool {
        guard ChecksumUtility.isValidCrc16(qrCode) else {
            throw QRError.tamperedQR(bic: bic)
        }

        return true
    }

    func parseQRPH(qrCode: String) throws -> QRDetails {
        guard let pushData = try? MPQRParser.parseWithoutTagValidationAndCRC(qrCode) else {
            throw QRError.parsingError(field: QRPHField.unknown)
        }

        var qrDetails = QRDetails()

        // QRPH Field 00
        qrDetails.payloadFormatIndicator = fetchTagValue(from: pushData, tagString: QRPHField.payloadFormatIndicator.rootTag)

        // QRPH Field 01
        qrDetails.pointOfInitiation = fetchTagValue(from: pushData, tagString: QRPHField.pointOfInitiation.rootTag)

        // QRPH Field 27 - P2P Merchant Account Information
        if let p2pMaiData = try? pushData.getMAIData(forTagString: QRPHField.p2pMerchantAccountInformation.rootTag) {
            // QRPH Field 27
            qrDetails.p2pMerchantAccountInformation = p2pMaiData
            // QRPH Field 27.00
            qrDetails.globalUniqueIdentifier = fetchTagValue(from: p2pMaiData, tagString: QRPHField.p2pGloballyUniqueId.subTag) as String?
            // QRPH Field 27.01
            qrDetails.acquirerId = fetchPaymentNetworkSpecific(from: p2pMaiData, tag: QRPHField.p2pAcquirerID.subTag)
            // QRPH Field 27.02
            qrDetails.paymentType = fetchPaymentNetworkSpecific(from: p2pMaiData, tag: QRPHField.paymentType.subTag)
            // QRPH Field 27.03
            qrDetails.merchantId = fetchPaymentNetworkSpecific(from: p2pMaiData, tag: QRPHField.p2pMerchantID.subTag)
            // QRPH Field 27.04
            qrDetails.creditAccountNumber = fetchPaymentNetworkSpecific(from: p2pMaiData, tag: QRPHField.p2pMerchantCreditAccount.subTag)
            // QRPH Field 27.05
            qrDetails.mobileNumber = fetchPaymentNetworkSpecific(from: p2pMaiData, tag: QRPHField.merchantMobileNumber.subTag)
        }

        // QRPH Field 28 - P2M Merchant Account Information
        if let p2mMaiData = try? pushData.getMAIData(forTagString: QRPHField.p2mMerchantAccountInformation.rootTag) {
            // QRPH Field 28
            qrDetails.p2mMerchantAccountInformation = p2mMaiData
            // QRPH Field 28.00
            qrDetails.globalUniqueIdentifier = fetchTagValue(from: p2mMaiData, tagString: QRPHField.p2mGloballyUniqueId.subTag)
            // QRPH Field 28.01
            qrDetails.acquirerId = fetchPaymentNetworkSpecific(from: p2mMaiData, tag: QRPHField.p2mAcquirerID.subTag)
            // QRPH Field 28.03
            qrDetails.merchantId = fetchPaymentNetworkSpecific(from: p2mMaiData, tag: QRPHField.p2mMerchantID.subTag)
            // QRPH Field 28.04
            qrDetails.merchantCreditAccountNumber = fetchPaymentNetworkSpecific(from: p2mMaiData, tag: QRPHField.p2mMerchantCreditAccount.subTag)
            // QRPH Field 28.05
            qrDetails.proxyNotifFlag = fetchPaymentNetworkSpecific(from: p2mMaiData, tag: QRPHField.p2mProxyNotifFlag.subTag)
        }

        // QRPH Field 52
        qrDetails.merchantCategoryCode = pushData.merchantCategoryCode
        // QRPH Field 53
        qrDetails.transactionCurrencyCode = pushData.transactionCurrencyCode
        // QRPH Field 54
        qrDetails.transactionAmount = pushData.transactionAmount
        // QRPH Field 55
        qrDetails.convenienceIndicator = pushData.tipOrConvenienceIndicator
        // QRPH Field 56
        qrDetails.convenienceFeeFixed = pushData.valueOfConvenienceFeeFixed
        // QRPH Field 57
        qrDetails.convenienceFeeFixed = pushData.valueOfConvenienceFeePercentage
        // QRPH Field 58
        qrDetails.merchantCountryCode = pushData.countryCode
        // QRPH Field 59
        qrDetails.merchantName = pushData.merchantName
        // QRPH Field 60
        qrDetails.merchantCity = pushData.merchantCity

        let merchantPostalCode: String? = fetchTagValue(from: pushData, tagString: QRPHField.postalCode.rootTag)

        // QRPH Field 61
        qrDetails.merchantPostalCode = merchantPostalCode

        let additionalData = pushData.additionalData

        // QRPH Field 62
        qrDetails.additionalDataPayload = additionalData
        qrDetails.additionalData = QRAdditionalData(
            // QRPH Field 62.00
            // p2mAdditionalDataGloballyUniqueId.subTag is the same as p2pAdditionalDataGloballyUniqueId.subTag
            globalUniqueIdentifier: fetchTagValue(from: additionalData, tagString: QRPHField.p2mAdditionalDataGloballyUniqueId.subTag) as String?,
            // QRPH Field 62.01
            billNumber: additionalData?.billNumber,
            // QRPH Field 62.02
            additionalMobileNumber: additionalData?.mobileNumber,
            // QRPH Field 62.03
            storeName: additionalData?.storeId,
            merchantPostalCode: merchantPostalCode,
            // QRPH Field 62.04
            loyaltyNumber: additionalData?.loyaltyNumber,
            // QRPH Field 62.05
            referenceId: additionalData?.referenceId,
            // QRPH Field 62.06
            customerLabel: additionalData?.consumerId,
            // QRPH Field 62.07
            terminalId: additionalData?.terminalId,
            // QRPH Field 62.08
            purpose: additionalData?.purpose,
            // QRPH Field 62.09
            additionalRequestData: additionalData?.additionalConsumerDataRequest
        )

        // QRPH Field 63
        qrDetails.merchantCrc = pushData.crc

        // QRPH Field 64 (includes 64.00, 64.01, 64.02)
        qrDetails.languageData = pushData.languageData

        // QRPH Field 88.01
        if let acquirerRequiredInfo = try? pushData.getUnreservedData(forTagString: QRPHField.acquirerRequiredInformation.rootTag).getTagInfoValue(forTagString: QRPHField.acquirerRequiredInformation.subTag) as? String {
            qrDetails.acquirerRequiredInfo = acquirerRequiredInfo
        }

        return qrDetails
    }

    func getQRPHType(qrDetails: QRDetails) throws -> QRPHType {
        let p2p = qrDetails.p2pMerchantAccountInformation
        let p2m = qrDetails.p2mMerchantAccountInformation
        let bic = qrDetails.acquirerId
        let globallyUniqueID = qrDetails.globalUniqueIdentifier

        switch (p2p, p2m) {
        // QR Should have field 27/28
        case (nil, nil):
            throw QRError.missingRequiredField(field: QRPHField.qrType, bic: bic ?? "")
        // QR Should not contain both field 27/28
        case (.some, .some):
            throw QRError.invalidValue(value: "", field: QRPHField.qrType, bic: bic ?? "")
        // P2P
        case (.some, nil):
            guard let bic = bic else {
                throw QRError.missingRequiredField(field: QRPHField.p2pAcquirerID, bic: "")
            }

            guard let globallyUniqueID = globallyUniqueID else {
                throw QRError.missingRequiredField(field: QRPHField.p2pGloballyUniqueId, bic: bic)
            }

            switch (globallyUniqueID, bic) {
            case (QRPHConstants.p2pGloballyUniqueID, QRPHConstants.mayaAcquirerID):
                return .p2pSendMoney
            case (QRPHConstants.p2pGloballyUniqueID, _):
                return .p2pBankTransfer

            default:
                throw QRError.invalidValue(value: globallyUniqueID, field: QRPHField.p2pGloballyUniqueId, bic: bic)
            }

        // P2M
        case (nil, .some):
            guard let bic = bic else {
                throw QRError.missingRequiredField(field: QRPHField.p2mAcquirerID, bic: "")
            }

            guard let globallyUniqueID = globallyUniqueID else {
                throw QRError.missingRequiredField(field: QRPHField.p2mGloballyUniqueId, bic: bic)
            }

            switch (globallyUniqueID, bic) {
            case (QRPHConstants.p2mGloballyUniqueID, QRPHConstants.mayaAcquirerID),
                (QRPHConstants.microGloballyUniqueID, QRPHConstants.mayaAcquirerID):
                return .p2mOnUs
            case (QRPHConstants.p2mGloballyUniqueID, _),
                (QRPHConstants.microGloballyUniqueID, _):
                return .p2mOffUs
            case (QRPHConstants.billersGloballyUniqueID, _):
                return .p2b
            default:
                throw QRError.invalidValue(value: globallyUniqueID, field: QRPHField.p2mGloballyUniqueId, bic: bic)
            }
        }
    }

    func validateQRPHP2p(qrDetails: QRDetails) throws {
        // QRPH Field 27.01 gets checked first if it has value, this is needed info for other validations
        let bic = try validateQRFieldAndReturnValue(qrDetails.acquirerId,
                        tag: QRPHField.p2pAcquirerID,
                        exactLength: QRPHFieldLength.acquirerIDLength,
                        bic: "")

        // QRPH Field 00
        try validateQRField(qrDetails.payloadFormatIndicator,
                            tag: QRPHField.payloadFormatIndicator,
                            isMandatory: true,
                            allowedValues: [QRPHConstants.payloadFormatIndicator],
                            bic: bic)

        // QRPH Field 01
        let pointOfInitiation = try validateQRFieldAndReturnValue(qrDetails.pointOfInitiation,
                                        tag: QRPHField.pointOfInitiation,
                                        allowedValues: QRPHValidation.validPointsOfInitiation,
                                        bic: bic)

        // QRPH Field 27.00
        try validateQRField(qrDetails.globalUniqueIdentifier,
                            tag: QRPHField.p2pGloballyUniqueId,
                            isMandatory: true,
                            range: QRPHFieldLength.globallyUniqueIDRange,
                            bic: bic)

        // QRPH Field 27.02
        try validateQRField(qrDetails.paymentType,
                            tag: QRPHField.paymentType,
                            isMandatory: true,
                            allowedValues: QRPHValidation.validPaymentTypes,
                            bic: bic)
        // QRPH Field 27.03
        try validateQRField(qrDetails.merchantId,
                            tag: QRPHField.p2pMerchantID,
                            isMandatory: false,
                            range: QRPHFieldLength.merchantIdRange,
                            bic: bic)
        // QRPH Field 27.04
        try validateQRField(qrDetails.creditAccountNumber,
                            tag: QRPHField.p2pMerchantCreditAccount,
                            isMandatory: true,
                            range: QRPHFieldLength.p2pMerchantCreditAccountNumberRange,
                            bic: bic)
        // QRPH Field 27.05
        try validateQRField(qrDetails.mobileNumber,
                            tag: QRPHField.merchantMobileNumber,
                            isMandatory: false,
                            validationRegex: QRPHValidation.merchantMobileNumberRegex,
                            range: QRPHFieldLength.merchantMobileNumberRange,
                            bic: bic)
        // QRPH Field 52
        try validateQRField(qrDetails.merchantCategoryCode,
                            tag: QRPHField.merchantCategoryCode,
                            isMandatory: true,
                            exactLength: QRPHFieldLength.merchantCategoryCodeLength,
                            allowedValues: QRPHValidation.validMerchantCategoryCodes,
                            bic: bic)
        // QRPH Field 53
        try validateQRField(qrDetails.transactionCurrencyCode,
                            tag: QRPHField.transactionCurrencyCode,
                            isMandatory: true,
                            allowedValues: QRPHValidation.validTransactionCurrencyCodes,
                            bic: bic)
        // QRPH Field 54
        let transactionAmount = qrDetails.transactionAmount
        // QRPH Dynamic QR
        if pointOfInitiation == QRPHConstants.pointOfInitiationDynamic
            && transactionAmount == nil {
            throw QRError.missingRequiredField(field: QRPHField.transactionAmount, bic: bic)
        }
        // QRPH Static QR
        if pointOfInitiation == QRPHConstants.pointOfInitiationStatic,
           let transactionAmount = transactionAmount,
             Double(transactionAmount) != 0.0 {
            throw QRError.invalidValue(value: transactionAmount, field: QRPHField.transactionAmount, bic: bic)
        }

        // QRPH Field 55
        if let convenienceIndicator = qrDetails.convenienceIndicator {
            try validateQRField(convenienceIndicator,
                                tag: QRPHField.convenienceIndicator,
                                isMandatory: false,
                                exactLength: QRPHFieldLength.convenienceIndicatorLength,
                                allowedValues: QRPHValidation.validConvenienceIndicators,
                                bic: bic)
            // QRPH Field 56
            if convenienceIndicator == QRPHConstants.convenienceFeeFixedIndicator {
                try validateQRField(qrDetails.convenienceFeeFixed,
                                    tag: QRPHField.convenienceFeeFixed,
                                    isMandatory: true,
                                    range: QRPHFieldLength.convenienceFeeFixIndicatorLength,
                                    bic: bic)
            // QRPH Field 57
            } else if convenienceIndicator == QRPHConstants.convenienceFeePercentageIndicator {
                try validateQRField(qrDetails.convenienceFeePercentage,
                                    tag: QRPHField.convenienceFeePercentage,
                                    isMandatory: true,
                                    range: QRPHFieldLength.convenienceFeePercentageIndicatorLength,
                                    bic: bic)
            }
        }

        // QRPH Field 58
        try validateQRField(qrDetails.merchantCountryCode,
                            tag: QRPHField.countryCode,
                            isMandatory: true,
                            exactLength: QRPHFieldLength.countryCodeLength,
                            allowedValues: QRPHValidation.validCountryCodes,
                            bic: bic)
        // QRPH Field 59
        try validateQRField(qrDetails.merchantName,
                            tag: QRPHField.merchantName,
                            isMandatory: true,
                            validationRegex: QRPHValidation.specialCharacterRegex,
                            range: QRPHFieldLength.nameRange,
                            bic: bic)
        // QRPH Field 60
        try validateQRField(qrDetails.merchantCity,
                            tag: QRPHField.merchantCity,
                            isMandatory: true,
                            validationRegex: QRPHValidation.specialCharacterRegex,
                            range: QRPHFieldLength.cityRange,
                            bic: bic)
        // QRPH Field 61
        try validateQRField(qrDetails.merchantPostalCode,
                            tag: QRPHField.postalCode,
                            isMandatory: false,
                            range: QRPHFieldLength.postalCodeRange,
                            bic: bic)
        // QRPH Field 62
        // Check if additionalDataPayload is not null, if it is null, QRAdditionalData/additionalData has null values.
        if qrDetails.additionalDataPayload != nil,
            let additionalData = qrDetails.additionalData {
            // QRPH Field 62.00
            try validateQRField(additionalData.globalUniqueIdentifier,
                                tag: QRPHField.p2pAdditionalDataGloballyUniqueId,
                                isMandatory: true,
                                range: QRPHFieldLength.additionalGloballyUniqueIDRange,
                                bic: bic)
            // QRPH Field 62.02
            try validateQRField(additionalData.additionalMobileNumber,
                                tag: QRPHField.mobileNumber,
                                isMandatory: false,
                                validationRegex: QRPHValidation.merchantMobileNumberRegex,
                                allowedPlaceholderValue: QRPHConstants.validValue,
                                bic: bic)
            // QRPH Field 62.03
            try validateQRField(additionalData.storeName,
                                tag: QRPHField.storeLabel,
                                isMandatory: false,
                                validationRegex: QRPHValidation.specialCharacterRegex,
                                range: QRPHFieldLength.storeNameRange,
                                bic: bic)
            // QRPH Field 62.04
            try validateQRField(additionalData.loyaltyNumber,
                                tag: QRPHField.loyaltyNumber,
                                isMandatory: false,
                                range: QRPHFieldLength.minMaxRange,
                                bic: bic)

            // QRPH Field 62.05
            try validateQRField(additionalData.referenceId,
                                tag: QRPHField.referenceLabel,
                                isMandatory: false,
                                validationRegex: QRPHValidation.specialCharacterRegex,
                                range: QRPHFieldLength.referenceLabelRange,
                                bic: bic)
            // QRPH Field 62.06
            try validateQRField(additionalData.customerLabel,
                                tag: QRPHField.customerLabel,
                                isMandatory: false,
                                validationRegex: QRPHValidation.specialCharacterRegex,
                                range: QRPHFieldLength.minMaxRange,
                                allowedPlaceholderValue: QRPHConstants.validValue,
                                bic: bic)
            // QRPH Field 62.07
            try validateQRField(additionalData.terminalId,
                                tag: QRPHField.terminalLabel,
                                isMandatory: true,
                                validationRegex: QRPHValidation.specialCharacterRegex,
                                exactLength: QRPHFieldLength.terminalIDLength,
                                bic: bic)
        }

        // QRPH Field 63
        try validateCrc(crc: qrDetails.merchantCrc, bic: bic)

        // QRPH Field 64
        if let languageData = qrDetails.languageData {
            // QRPH Field 64.00
            try validateQRField(languageData.languagePreference,
                                tag: QRPHField.languagePreference,
                                isMandatory: false,
                                exactLength: QRPHFieldLength.languagePreferenceLength,
                                bic: bic)
            // QRPH Field 64.01
            try validateQRField(languageData.alternateMerchantName,
                                tag: QRPHField.merchantNameAlternateLanguage,
                                isMandatory: false,
                                range: QRPHFieldLength.alternateLanguageRange,
                                bic: bic)
            // QRPH Field 64.02
            try validateQRField(languageData.alternateMerchantCity,
                                tag: QRPHField.merchantCityAlternateLanguage,
                                isMandatory: false,
                                range: QRPHFieldLength.alternateLanguageRange,
                                bic: bic)
        }
    }

    func validateQRPHP2m(qrDetails: QRDetails) throws {
        // QRPH Field 28.01 gets checked first if it has value, this is needed info for other validations
        let bic = try validateQRFieldAndReturnValue(qrDetails.acquirerId,
                                                    tag: QRPHField.p2mAcquirerID,
                                                    exactLength: QRPHFieldLength.acquirerIDLength,
                                                    bic: "")
        // QRPH Field 00
        try validateQRField(qrDetails.payloadFormatIndicator,
                            tag: QRPHField.payloadFormatIndicator,
                            isMandatory: true,
                            allowedValues: [QRPHConstants.payloadFormatIndicator],
                            bic: bic)

        // QRPH Field 01
        let pointOfInitiation = try validateQRFieldAndReturnValue(qrDetails.pointOfInitiation,
                                        tag: QRPHField.pointOfInitiation,
                                        allowedValues: QRPHValidation.validPointsOfInitiation,
                                        bic: bic)

        // QRPH Field 28.00
        try validateQRField(qrDetails.globalUniqueIdentifier,
                            tag: QRPHField.p2mGloballyUniqueId,
                            isMandatory: true,
                            range: QRPHFieldLength.globallyUniqueIDRange,
                            bic: bic)

        // QRPH Field 28.03
        try validateQRField(qrDetails.merchantId,
                            tag: QRPHField.p2mMerchantID,
                            isMandatory: true,
                            range: QRPHFieldLength.idRange,
                            bic: bic)

        // QRPH Field 28.04
        try validateQRField(qrDetails.merchantCreditAccountNumber,
                            tag: QRPHField.p2mMerchantCreditAccount,
                            isMandatory: false,
                            range: QRPHFieldLength.p2mMerchantCreditAccountNumberRange,
                            bic: bic)

        // QRPH Field 28.05
        try validateQRField(qrDetails.proxyNotifFlag,
                            tag: QRPHField.p2mProxyNotifFlag,
                            isMandatory: true,
                            exactLength: QRPHFieldLength.proxyNotifFlagLength,
                            bic: bic)
        // QRPH Field 52
        try validateQRField(qrDetails.merchantCategoryCode,
                            tag: QRPHField.merchantCategoryCode,
                            isMandatory: true,
                            exactLength: QRPHFieldLength.merchantCategoryCodeLength,
                            bic: bic)

        // QRPH Field 53
        try validateQRField(qrDetails.transactionCurrencyCode,
                            tag: QRPHField.transactionCurrencyCode,
                            isMandatory: true,
                            allowedValues: QRPHValidation.validTransactionCurrencyCodes,
                            bic: bic)
        // QRPH Field 54
        let transactionAmount = qrDetails.transactionAmount
        // QRPH Dynamic QR
        if pointOfInitiation == QRPHConstants.pointOfInitiationDynamic
            && transactionAmount == nil {
            throw QRError.missingRequiredField(field: QRPHField.transactionAmount, bic: bic)
        }

        // QRPH Static QR
        if pointOfInitiation == QRPHConstants.pointOfInitiationStatic,
           let transactionAmount = transactionAmount,
             Double(transactionAmount) != 0.0 {
            throw QRError.invalidValue(value: transactionAmount, field: QRPHField.transactionAmount, bic: bic)
        }

        // QRPH Field 55
        if let convenienceIndicator = qrDetails.convenienceIndicator {
            try validateQRField(convenienceIndicator,
                                tag: QRPHField.convenienceIndicator,
                                isMandatory: false,
                                exactLength: QRPHFieldLength.convenienceIndicatorLength,
                                allowedValues: QRPHValidation.validConvenienceIndicators,
                                bic: bic)
            // QRPH Field 56
            if convenienceIndicator == QRPHConstants.convenienceFeeFixedIndicator {
                try validateQRField(qrDetails.convenienceFeeFixed,
                                    tag: QRPHField.convenienceFeeFixed,
                                    isMandatory: true,
                                    range: QRPHFieldLength.convenienceFeeFixIndicatorLength,
                                    bic: bic)
            // QRPH Field 57
            } else if convenienceIndicator == QRPHConstants.convenienceFeePercentageIndicator {
                try validateQRField(qrDetails.convenienceFeePercentage,
                                    tag: QRPHField.convenienceFeePercentage,
                                    isMandatory: true,
                                    range: QRPHFieldLength.convenienceFeePercentageIndicatorLength,
                                    bic: bic)
            }
        }

        // QRPH Field 58
        try validateQRField(qrDetails.merchantCountryCode,
                            tag: QRPHField.countryCode,
                            isMandatory: true,
                            exactLength: QRPHFieldLength.countryCodeLength,
                            allowedValues: QRPHValidation.validCountryCodes,
                            bic: bic)
        // QRPH Field 59
        try validateQRField(qrDetails.merchantName,
                            tag: QRPHField.merchantName,
                            isMandatory: true,
                            validationRegex: QRPHValidation.specialCharacterRegex,
                            range: QRPHFieldLength.nameRange,
                            bic: bic)
        // QRPH Field 60
        try validateQRField(qrDetails.merchantCity,
                            tag: QRPHField.merchantCity,
                            isMandatory: true,
                            validationRegex: QRPHValidation.specialCharacterRegex,
                            range: QRPHFieldLength.cityRange,
                            bic: bic)
        // QRPH Field 61
        try validateQRField(qrDetails.merchantPostalCode,
                            tag: QRPHField.postalCode,
                            isMandatory: false,
                            range: QRPHFieldLength.postalCodeRange,
                            bic: bic)

        // QRPH Field 62
        // Check if additionalDataPayload is not null,
        // If additionalDataPayload is null, QRAdditionalData/additionalData has null values.
        if qrDetails.additionalDataPayload != nil,
            let additionalData = qrDetails.additionalData {
            // QRPH Field 62.00
            try validateQRField(additionalData.globalUniqueIdentifier,
                                tag: QRPHField.p2mAdditionalDataGloballyUniqueId,
                                isMandatory: true,
                                range: QRPHFieldLength.additionalGloballyUniqueIDRange,
                                bic: bic)
            // QRPH Field 62.02
            try validateQRField(additionalData.additionalMobileNumber,
                                tag: QRPHField.mobileNumber,
                                isMandatory: false,
                                validationRegex: QRPHValidation.merchantMobileNumberRegex,
                                allowedPlaceholderValue: QRPHConstants.validValue,
                                bic: bic)
            // QRPH Field 62.03
            try validateQRField(additionalData.storeName,
                                tag: QRPHField.storeLabel,
                                isMandatory: false,
                                validationRegex: QRPHValidation.specialCharacterRegex,
                                range: QRPHFieldLength.storeNameRange,
                                bic: bic)
            // QRPH Field 62.04
            try validateQRField(additionalData.loyaltyNumber,
                                tag: QRPHField.loyaltyNumber,
                                isMandatory: false,
                                range: QRPHFieldLength.minMaxRange,
                                bic: bic)

            // QRPH Field 62.05
            try validateQRField(additionalData.referenceId,
                                tag: QRPHField.referenceLabel,
                                isMandatory: false,
                                validationRegex: QRPHValidation.specialCharacterRegex,
                                range: QRPHFieldLength.referenceLabelRange,
                                bic: bic)
            // QRPH Field 62.06
            try validateQRField(additionalData.customerLabel,
                                tag: QRPHField.customerLabel,
                                isMandatory: false,
                                range: QRPHFieldLength.minMaxRange,
                                bic: bic)
            // QRPH Field 62.07
            try validateQRField(additionalData.terminalId,
                                tag: QRPHField.terminalLabel,
                                isMandatory: false,
                                validationRegex: QRPHValidation.specialCharacterRegex,
                                exactLength: QRPHFieldLength.terminalIDLength,
                                bic: bic)
            // QRPH Field 62.08
            try validateQRField(additionalData.purpose,
                                tag: QRPHField.purpose,
                                isMandatory: false,
                                exactLength: QRPHFieldLength.purposeLength,
                                bic: bic)

            // QRPH Field 62.09
            try validateQRField(additionalData.additionalRequestData,
                                tag: QRPHField.additionalConsumerDataRequest,
                                isMandatory: false,
                                range: QRPHFieldLength.additionalConsumerDataRequestRange,
                                bic: bic)
        }

        // QRPH Field 63
        try validateCrc(crc: qrDetails.merchantCrc, bic: bic)

        // QRPH Field 64
        if let languageData = qrDetails.languageData {
            // QRPH Field 64.00
            try validateQRField(languageData.languagePreference,
                                tag: QRPHField.languagePreference,
                                isMandatory: true,
                                exactLength: QRPHFieldLength.languagePreferenceLength,
                                bic: bic)
            // QRPH Field 64.01
            try validateQRField(languageData.alternateMerchantName,
                                tag: QRPHField.merchantNameAlternateLanguage,
                                isMandatory: true,
                                range: QRPHFieldLength.alternateLanguageRange,
                                bic: bic)
            // QRPH Field 64.02
            try validateQRField(languageData.alternateMerchantCity,
                                tag: QRPHField.merchantCityAlternateLanguage,
                                isMandatory: false,
                                range: QRPHFieldLength.alternateLanguageRange,
                                bic: bic)
        }
    }

    func validateQRFieldAndReturnValue(_ value: String?,
                                        tag: QRFieldTag,
                                        validationRegex: String? = nil,
                                        range: ClosedRange<Int>? = nil,
                                        exactLength: Int? = nil,
                                        allowedValues: [String]? = nil,
                                        allowedPlaceholderValue: String? = nil,
                                        bic: String = "") throws -> String {
        try self.validateQRField(
            value,
            tag: tag,
            isMandatory: true,
            validationRegex: validationRegex,
            range: range,
            exactLength: exactLength,
            allowedValues: allowedValues,
            allowedPlaceholderValue: allowedPlaceholderValue,
            bic: bic
        )

        // Force unwrap – validateQRField should have thrown an error if value is nil at this point
        return value!
    }

    func validateQRField(
        _ value: String?,
        tag: QRFieldTag,
        isMandatory: Bool = false,
        validationRegex: String? = nil,
        range: ClosedRange<Int>? = nil,
        exactLength: Int? = nil,
        allowedValues: [String]? = nil,
        allowedPlaceholderValue: String? = nil,
        bic: String = ""
    ) throws {
        guard let value = value, !value.isEmpty else {
            if isMandatory {
                throw QRError.missingRequiredField(field: tag, bic: bic)
            }
            return
        }

        // Allowed values validation
        if let allowed = allowedValues, !allowed.contains(value) {
            throw QRError.invalidValue(value: value, field: tag, bic: bic)
        }

        // Length range validation
        // if the value does not comply within the range, throw error
        if let range = range, !range.contains(value.count) {
            throw QRError.invalidLength(value: value, field: tag, bic: bic)
        }

        // Exact length validation
        // if the value does not comply with the exact value length, throw error
        if let exact = exactLength, value.count != exact {
            throw QRError.invalidLength(value: value, field: tag, bic: bic)
        }

        // Regex validation
        // if allowPlaceholderValue null or is not equal to value if not null, continue checking regex
        if allowedPlaceholderValue == nil || value != allowedPlaceholderValue {
            // If the value does not comply with the pattern, throw error
            if let pattern = validationRegex,
               let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive),
               regex.matches(in: value, range: NSRange(location: 0, length: value.utf16.count)).isEmpty {
                throw QRError.invalidValue(value: value, field: tag, bic: bic)
            }
        }
    }

    func validateCrc(crc: String?, bic: String) throws {
        guard let crc = crc else {
            throw QRError.missingRequiredField(field: QRPHField.crc, bic: bic)
        }

        if crc.contains(where: { $0.isLowercase }) {
            throw QRError.invalidValue(value: crc, field: QRPHField.crc, bic: bic)
        }
    }

    func fetchTagValue<T>(from data: MAIData, tagString: String) -> T? {
        return try? data.getTagInfoValue(forTagString: tagString) as? T
    }

    func fetchTagValue<T>(from data: PushPaymentData, tagString: String) -> T? {
        return try? data.getTagInfoValue(forTagString: tagString) as? T
    }

    func fetchTagValue<T>(from data: AdditionalData?, tagString: String) -> T? {
        guard let data = data else { return nil }
        return try? data.getTagInfoValue(forTagString: tagString) as? T
    }

    func fetchPaymentNetworkSpecific(from maiData: MAIData, tag: String) -> String? {
        return try? maiData.getPaymentNetworkSpecific(forTag: tag)
    }
}
