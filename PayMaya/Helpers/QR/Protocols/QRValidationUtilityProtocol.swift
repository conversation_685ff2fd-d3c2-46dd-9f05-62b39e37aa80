//
//  QRValidationUtilityProtocol.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 6/2/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Foundation

protocol QRValidationUtilityProtocol {
    func checkQRPHFormatIndicator(qrCode: String) throws -> Bool
    func checkQRPHCrc(qrCode: String) throws -> Bool
    func validateQRPHCrc(qrCode: String, bic: String) throws -> Bool
    func parseQRPH(qrCode: String) throws -> QRDetails
    func getQRPHType(qrDetails: QRDetails) throws -> QRPHType
    func validateQRPHP2p(qrDetails: QRDetails) throws
    func validateQRPHP2m(qrDetails: QRDetails) throws
}

enum QRError: Error, Codable, Equatable {
    case invalidLength(value: String, field: QRFieldTag, bic: String)
    case missingRequiredField(field: QRFieldTag, bic: String)
    case invalidValue(value: String, field: QRFieldTag, bic: String)
    case parsingError(field: QRFieldTag)
    case tamperedQR(bic: String)

    enum CodingKeys: String, CodingKey {
        case type, value, field, bic
    }

    public var caseName: String {
        switch self {
        case .invalidLength: return "invalidLength"
        case .missingRequiredField: return "missingRequiredField"
        case .invalidValue: return "invalidValue"
        case .parsingError: return "parsingError"
        case .tamperedQR: return "tamperedQR"
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(caseName, forKey: .type)

        switch self {
        case .invalidLength(let value, let field, let bic),
             .invalidValue(let value, let field, let bic):
            try container.encode(value, forKey: .value)
            try container.encode(field, forKey: .field)
            try container.encode(bic, forKey: .bic)

        case .missingRequiredField(let field, let bic):
            try container.encode(field, forKey: .field)
            try container.encode(bic, forKey: .bic)

        case .parsingError(let field):
            try container.encode(field, forKey: .field)

        case .tamperedQR(let bic):
            try container.encode(bic, forKey: .bic)
        }
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let decodedCaseName = try container.decode(String.self, forKey: .type)

        switch decodedCaseName {
        case "invalidLength":
            let value = try container.decode(String.self, forKey: .value)
            let field = try container.decode(QRFieldTag.self, forKey: .field)
            let bic = try container.decode(String.self, forKey: .bic)
            self = .invalidLength(value: value, field: field, bic: bic)

        case "invalidValue":
            let value = try container.decode(String.self, forKey: .value)
            let field = try container.decode(QRFieldTag.self, forKey: .field)
            let bic = try container.decode(String.self, forKey: .bic)
            self = .invalidValue(value: value, field: field, bic: bic)

        case "missingRequiredField":
            let field = try container.decode(QRFieldTag.self, forKey: .field)
            let bic = try container.decode(String.self, forKey: .bic)
            self = .missingRequiredField(field: field, bic: bic)

        case "parsingError":
            let field = try container.decode(QRFieldTag.self, forKey: .field)
            self = .parsingError(field: field)

        case "tamperedQR":
            let bic = try container.decode(String.self, forKey: .bic)
            self = .tamperedQR(bic: bic)

        default:
            throw DecodingError.dataCorruptedError(
                forKey: .type,
                in: container,
                debugDescription: "Unknown case name: \(decodedCaseName)"
            )
        }
    }
}
