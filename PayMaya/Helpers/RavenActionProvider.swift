//
//  RavenActionProvider.swift
//  PayMaya
//
//  Created by <PERSON> on 5/27/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Injector
import MayaCoreData
import RavenLocation
import ReactiveSwift
import StoreProvider

class RavenActionProvider {
    @Inject private var ravenWrapper: RavenWrapper
    @Inject private var storeProvider: StoreProvider

    func makeGetGeolocationAction() -> Action<Void, RavenLocation.Location?, RavenLocation.Error> {
        return Action { [weak self] in
            guard let self else {
                return SignalProducer(error: .unknown)
            }
            return SignalProducer { observer, _ in
                self.ravenWrapper.getCurrentLocation { loc, err in
                    if let loc {
                        observer.send(value: loc)
                        observer.sendCompleted()
                    } else if let err {
                        observer.send(error: err)
                    }
                }
            }
        }
    }

    func makeExtractAction() -> Action<Void, Void, PayMayaError> {
        return Action { [weak self] in
            guard let self else {
                return SignalProducer(error: PayMayaError())
            }
            guard let databaseStore = storeProvider.target(DatabaseStore.self),
                  let user = databaseStore.read(DatabaseStore.ReadingOptions(DatabaseEntity.user)).value as? User
            else {
                return SignalProducer(error: PayMayaError())
            }

            return SignalProducer { observer, _ in
                self.ravenWrapper.extract(customerID: user.walletId,
                                          transactionID: nil,
                                          triggeredBy: Constants.Trigger.lending,
                                          eventTrigger: Constants.EventTrigger.credit) { result in
                    switch result {
                    case .success:
                        observer.sendCompleted()
                    case .failure:
                        observer.send(error: PayMayaError())
                    }
                }
            }
        }
    }
}
