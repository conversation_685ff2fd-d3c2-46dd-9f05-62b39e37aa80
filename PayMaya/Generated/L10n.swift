// swiftlint:disable all
// Generated using SwiftGen — https://github.com/SwiftGen/SwiftGen

import Foundation

// swiftlint:disable superfluous_disable_command file_length implicit_return prefer_self_in_static_references

// MARK: - Strings

// swiftlint:disable explicit_type_interface function_parameter_count identifier_name line_length
// swiftlint:disable nesting type_body_length type_name vertical_whitespace_opening_braces
internal enum L10n {
  internal enum AccountLimits {
    /// %@ %@ used (max: %@ %@)
    internal static func amountRange(_ p1: Any, _ p2: Any, _ p3: Any, _ p4: Any) -> String {
      return L10n.tr("Localizable", "accountLimits.amountRange", String(describing: p1), String(describing: p2), String(describing: p3), String(describing: p4), fallback: "%@ %@ used (max: %@ %@)")
    }
    /// %@ %@ remaining
    internal static func amountRemaining(_ p1: Any, _ p2: Any) -> String {
      return L10n.tr("Localizable", "accountLimits.amountRemaining", String(describing: p1), String(describing: p2), fallback: "%@ %@ remaining")
    }
    /// Learn more about account limits.
    internal static let learnMore = L10n.tr("Localizable", "accountLimits.learnMore", fallback: "Learn more about account limits.")
    /// Account Limits
    internal static let noDailyLimit = L10n.tr("Localizable", "accountLimits.noDailyLimit", fallback: "No daily amount limit")
    /// No monthly amount limit
    internal static let noMonthlyLimit = L10n.tr("Localizable", "accountLimits.noMonthlyLimit", fallback: "No monthly amount limit")
    internal enum CountLimit {
      /// %@ transactions left (max: %@)
      internal static func plural(_ p1: Any, _ p2: Any) -> String {
        return L10n.tr("Localizable", "accountLimits.countLimit.plural", String(describing: p1), String(describing: p2), fallback: "%@ transactions left (max: %@)")
      }
      /// %@ transaction left (max: %@)
      internal static func singular(_ p1: Any, _ p2: Any) -> String {
        return L10n.tr("Localizable", "accountLimits.countLimit.singular", String(describing: p1), String(describing: p2), fallback: "%@ transaction left (max: %@)")
      }
    }
    internal enum LearnMore {
      /// account limits
      internal static let link = L10n.tr("Localizable", "accountLimits.learnMore.link", fallback: "account limits")
    }
  }
  internal enum Activities {
    internal enum Error {
      internal enum Spiel {
        /// Activities
        internal static let fetchNextPage = L10n.tr("Localizable", "activities.error.spiel.fetchNextPage", fallback: "Failed to load more transactions.\nSwipe up to try again.")
      }
    }
  }
  internal enum Activity {
    internal enum Detail {
      /// Amount
      internal static let amount = L10n.tr("Localizable", "activity.detail.amount", fallback: "Amount")
      /// Reason
      internal static let reason = L10n.tr("Localizable", "activity.detail.reason", fallback: "Reason")
      /// Recipient
      internal static let recipient = L10n.tr("Localizable", "activity.detail.recipient", fallback: "Recipient")
      internal enum Account {
        /// Activity Details
        internal static let number = L10n.tr("Localizable", "activity.detail.account.number", fallback: "Account Number")
      }
      internal enum Amount {
        /// Amount Paid
        internal static let paid = L10n.tr("Localizable", "activity.detail.amount.paid", fallback: "Amount Paid")
      }
      internal enum Fund {
        /// Fund Source
        internal static let source = L10n.tr("Localizable", "activity.detail.fund.source", fallback: "Fund Source")
      }
      internal enum Merchant {
        /// Merchant ID
        internal static let id = L10n.tr("Localizable", "activity.detail.merchant.id", fallback: "Merchant ID")
      }
      internal enum Pay {
        internal enum Bills {
          /// Amount has been reserved for payment to biller.
          internal static let authorized = L10n.tr("Localizable", "activity.detail.pay.bills.authorized", fallback: "Amount has been reserved for payment to biller.")
          /// Amount has been sent to the biller.
          internal static let posted = L10n.tr("Localizable", "activity.detail.pay.bills.posted", fallback: "Amount has been sent to the biller.")
          /// Payment to this biller has failed. The bill amount and Biller Convenience Fee has been credited back to your account.
          internal static let rejected = L10n.tr("Localizable", "activity.detail.pay.bills.rejected", fallback: "Payment to this biller has failed. The bill amount and Biller Convenience Fee has been credited back to your account.")
        }
      }
      internal enum Payment {
        /// Payment ID
        internal static let id = L10n.tr("Localizable", "activity.detail.payment.id", fallback: "Payment ID")
      }
      internal enum Receipt {
        /// Receipt No.
        internal static let number = L10n.tr("Localizable", "activity.detail.receipt.number", fallback: "Receipt No.")
      }
      internal enum Reference {
        /// Reference ID
        internal static let id = L10n.tr("Localizable", "activity.detail.reference.id", fallback: "Reference ID")
        internal enum Merchant {
          /// Card Transaction ID
          internal static let number = L10n.tr("Localizable", "activity.detail.reference.merchant.number", fallback: "Card Transaction ID")
        }
      }
      internal enum Transaction {
        /// Transaction Fee
        internal static let fee = L10n.tr("Localizable", "activity.detail.transaction.fee", fallback: "Transaction Fee")
        internal enum Instapay {
          /// You may confirm the status of your transaction with your recipient.
          internal static let message = L10n.tr("Localizable", "activity.detail.transaction.instapay.message", fallback: "You may confirm the status of your transaction with your recipient.")
        }
        internal enum Message {
          /// The reserved authorization amount may differ from the final amount once successfully settled with the merchant.
          internal static let authorized = L10n.tr("Localizable", "activity.detail.transaction.message.authorized", fallback: "The reserved authorization amount may differ from the final amount once successfully settled with the merchant.")
          /// This purchase has been cancelled and the amount has been credited back to your account.
          internal static let cancelled = L10n.tr("Localizable", "activity.detail.transaction.message.cancelled", fallback: "This purchase has been cancelled and the amount has been credited back to your account.")
          /// The final amount has been sent to the merchant.
          internal static let completed = L10n.tr("Localizable", "activity.detail.transaction.message.completed", fallback: "The final amount has been sent to the merchant.")
        }
      }
    }
  }
  internal enum AddFavorites {
    internal enum Alias {
      /// Label (e.g. %@)
      internal static func label(_ p1: Any) -> String {
        return L10n.tr("Localizable", "addFavorites.alias.label", String(describing: p1), fallback: "Label (e.g. %@)")
      }
      internal enum Enter {
        /// Enter Label (e.g. %@)
        internal static func label(_ p1: Any) -> String {
          return L10n.tr("Localizable", "addFavorites.alias.enter.label", String(describing: p1), fallback: "Enter Label (e.g. %@)")
        }
      }
    }
    internal enum Success {
      internal enum Title {
        /// Added to Favorites
        internal static let spiel = L10n.tr("Localizable", "addFavorites.success.title.spiel", fallback: "Added to Favorites")
      }
    }
  }
  internal enum AddToFavorites {
    internal enum Error {
      /// We could not add to your favorites at the moment. Please try again.
      internal static let message = L10n.tr("Localizable", "addToFavorites.error.message", fallback: "We could not add to your favorites at the moment. Please try again.")
      /// Add To Favorites
      internal static let title = L10n.tr("Localizable", "addToFavorites.error.title", fallback: "Add To Favorites Error")
      internal enum TryAgain {
        /// Try Again
        internal static let spiel = L10n.tr("Localizable", "addToFavorites.error.tryAgain.spiel", fallback: "Try Again")
      }
    }
  }
  internal enum Authenticate {
    /// Authenticate (OTP) screen
    internal static func instruction(_ p1: Any) -> String {
      return L10n.tr("Localizable", "authenticate.instruction", String(describing: p1), fallback: "Please enter the One-Time PIN (OTP) we sent to %@")
    }
    /// tel://(+632)88457788
    internal static let support = L10n.tr("Localizable", "authenticate.support", fallback: "tel://(+632)88457788")
    internal enum Help {
      internal enum Center {
        /// Help Center
        internal static let spiel = L10n.tr("Localizable", "authenticate.help.center.spiel", fallback: "Help Center")
      }
    }
    internal enum New {
      /// Please enter the One-Time PIN (OTP) that we sent to %@
      internal static func instruction(_ p1: Any) -> String {
        return L10n.tr("Localizable", "authenticate.new.instruction", String(describing: p1), fallback: "Please enter the One-Time PIN (OTP) that we sent to %@")
      }
    }
    internal enum Newmin {
      /// Please enter the One-Time PIN we sent to your new number: %@.
      internal static func instruction(_ p1: Any) -> String {
        return L10n.tr("Localizable", "authenticate.newmin.instruction", String(describing: p1), fallback: "Please enter the One-Time PIN we sent to your new number: %@.")
      }
    }
    internal enum Oldmin {
      /// Please enter the One-Time PIN we sent to your current number: %@.
      internal static func instruction(_ p1: Any) -> String {
        return L10n.tr("Localizable", "authenticate.oldmin.instruction", String(describing: p1), fallback: "Please enter the One-Time PIN we sent to your current number: %@.")
      }
    }
    internal enum Otp {
      internal enum Cta {
        /// Proceed
        internal static let title = L10n.tr("Localizable", "authenticate.otp.cta.title", fallback: "Proceed")
      }
      internal enum Instructions {
        /// Please enter the one-time PIN (OTP) that we sent to %@
        internal static func spiel(_ p1: Any) -> String {
          return L10n.tr("Localizable", "authenticate.otp.instructions.spiel", String(describing: p1), fallback: "Please enter the one-time PIN (OTP) that we sent to %@")
        }
      }
    }
    internal enum Registration {
      internal enum Otp {
        internal enum Cta {
          /// Verify
          internal static let title = L10n.tr("Localizable", "authenticate.registration.otp.cta.title", fallback: "Verify")
        }
        internal enum Screen {
          /// One-time PIN
          internal static let title = L10n.tr("Localizable", "authenticate.registration.otp.screen.title", fallback: "One-time PIN")
        }
        internal enum Success {
          /// You now own a basic account. Upgrade for free now and enjoy everything Maya has to offer.
          internal static let message = L10n.tr("Localizable", "authenticate.registration.otp.success.message", fallback: "You now own a basic account. Upgrade for free now and enjoy everything Maya has to offer.")
          /// You’re in! Welcome to Maya.
          internal static let title = L10n.tr("Localizable", "authenticate.registration.otp.success.title", fallback: "You’re in! Welcome to Maya.")
        }
      }
      internal enum SubmitInviteCode {
        /// invite code
        internal static let inviteCode = L10n.tr("Localizable", "authenticate.registration.submitInviteCode.inviteCode", fallback: "invite code")
        /// Submit your friend’s invite code
        internal static let subtitle = L10n.tr("Localizable", "authenticate.registration.submitInviteCode.subtitle", fallback: "Submit your friend’s invite code")
        /// Got an invite code?
        internal static let title = L10n.tr("Localizable", "authenticate.registration.submitInviteCode.title", fallback: "Got an invite code?")
      }
    }
    internal enum Resendcode {
      /// Resend Code in %ld %@...
      internal static func spiel(_ p1: Int, _ p2: Any) -> String {
        return L10n.tr("Localizable", "authenticate.resendcode.spiel", p1, String(describing: p2), fallback: "Resend Code in %ld %@...")
      }
      internal enum New {
        /// Resend code in %ld %@...
        internal static func spiel(_ p1: Int, _ p2: Any) -> String {
          return L10n.tr("Localizable", "authenticate.resendcode.new.spiel", p1, String(describing: p2), fallback: "Resend code in %ld %@...")
        }
      }
    }
    internal enum Visit {
      internal enum Help {
        internal enum Center {
          /// Visit our Help Center to learn more
          internal static let spiel = L10n.tr("Localizable", "authenticate.visit.help.center.spiel", fallback: "Visit our Help Center to learn more")
        }
      }
    }
  }
  internal enum Banktransfer {
    internal enum Bank {
      internal enum Not {
        internal enum Supported {
          /// Sorry, this bank is not yet supported.
          internal static let spiel = L10n.tr("Localizable", "banktransfer.bank.not.supported.spiel", fallback: "Sorry, this bank is not yet supported.")
        }
      }
    }
    internal enum Confirmation {
      /// correct
      internal static let correct = L10n.tr("Localizable", "banktransfer.confirmation.correct", fallback: "correct")
      /// Destination
      internal static let destination = L10n.tr("Localizable", "banktransfer.confirmation.destination", fallback: "Destination")
      /// My Wallet
      internal static let myWallet = L10n.tr("Localizable", "banktransfer.confirmation.myWallet", fallback: "My Wallet")
      /// Purpose
      internal static let purpose = L10n.tr("Localizable", "banktransfer.confirmation.purpose", fallback: "Purpose")
      /// Is the receiver someone you know and trust? Once sent, Maya can no longer refund your money. Always check if the recipient, account details, and amount are entered correctly before continuing.
      internal static let spiel = L10n.tr("Localizable", "banktransfer.confirmation.spiel", fallback: "Is the receiver someone you know and trust? Once sent, Maya can no longer refund your money. Always check if the recipient, account details, and amount are entered correctly before continuing.")
      /// Make sure this transaction is safe ✋
      internal static let spielHeader = L10n.tr("Localizable", "banktransfer.confirmation.spielHeader", fallback: "Make sure this transaction is safe ✋")
      /// Transfer amount
      internal static let transferAmount = L10n.tr("Localizable", "banktransfer.confirmation.transferAmount", fallback: "Transfer amount")
      /// Transfer now
      internal static let transferNow = L10n.tr("Localizable", "banktransfer.confirmation.transferNow", fallback: "Transfer now")
    }
    internal enum Favorites {
      internal enum Details {
        /// Bank transfer details
        internal static let spiel = L10n.tr("Localizable", "banktransfer.favorites.details.spiel", fallback: "Bank transfer details")
      }
      internal enum Save {
        /// Save details to favorites
        internal static let spiel = L10n.tr("Localizable", "banktransfer.favorites.save.spiel", fallback: "Save details to favorites")
      }
      internal enum Success {
        internal enum Message {
          /// You can now use Favorites for quick and easy bank transfers
          internal static let spiel = L10n.tr("Localizable", "banktransfer.favorites.success.message.spiel", fallback: "You can now use Favorites for quick and easy bank transfers")
        }
      }
      internal enum Update {
        internal enum Failed {
          /// Favorite's details failed to saved
          internal static let spiel = L10n.tr("Localizable", "banktransfer.favorites.update.failed.spiel", fallback: "Favorite's details failed to saved")
        }
        internal enum Success {
          /// Favorite's details have been saved
          internal static let spiel = L10n.tr("Localizable", "banktransfer.favorites.update.success.spiel", fallback: "Favorite's details have been saved")
        }
      }
    }
    internal enum Form {
      /// Bank Transfer
      internal static let accountName = L10n.tr("Localizable", "banktransfer.form.accountName", fallback: "Account Name")
      /// %@ has invalid characters
      internal static func hasInvalidCharacters(_ p1: Any) -> String {
        return L10n.tr("Localizable", "banktransfer.form.hasInvalidCharacters", String(describing: p1), fallback: "%@ has invalid characters")
      }
      /// Purpose
      internal static let purpose = L10n.tr("Localizable", "banktransfer.form.purpose", fallback: "Purpose")
      internal enum AccountName {
        /// Account name is required
        internal static let `required` = L10n.tr("Localizable", "banktransfer.form.accountName.required", fallback: "Account name is required")
      }
      internal enum AccountNumber {
        /// Account number is required
        internal static let `required` = L10n.tr("Localizable", "banktransfer.form.accountNumber.required", fallback: "Account number is required")
      }
      internal enum Amount {
        /// ₱0
        internal static let `default` = L10n.tr("Localizable", "banktransfer.form.amount.default", fallback: "₱0")
        /// ₱%@ transaction fee applies.
        internal static func feeApply(_ p1: Any) -> String {
          return L10n.tr("Localizable", "banktransfer.form.amount.feeApply", String(describing: p1), fallback: "₱%@ transaction fee applies.")
        }
        /// Transaction fee may apply.
        internal static let fees = L10n.tr("Localizable", "banktransfer.form.amount.fees", fallback: "Transaction fee may apply.")
        /// You have %@ in your wallet.
        internal static func info(_ p1: Any) -> String {
          return L10n.tr("Localizable", "banktransfer.form.amount.info", String(describing: p1), fallback: "You have %@ in your wallet.")
        }
        /// You only have %@ in your wallet.
        internal static func insufficient(_ p1: Any) -> String {
          return L10n.tr("Localizable", "banktransfer.form.amount.insufficient", String(describing: p1), fallback: "You only have %@ in your wallet.")
        }
        /// Amount is required. You have %@ in your wallet.
        internal static func `required`(_ p1: Any) -> String {
          return L10n.tr("Localizable", "banktransfer.form.amount.required", String(describing: p1), fallback: "Amount is required. You have %@ in your wallet.")
        }
      }
      internal enum Purpose {
        /// Please enter up to %@ characters only
        internal static func limit(_ p1: Any) -> String {
          return L10n.tr("Localizable", "banktransfer.form.purpose.limit", String(describing: p1), fallback: "Please enter up to %@ characters only")
        }
        /// Purpose is required
        internal static let `required` = L10n.tr("Localizable", "banktransfer.form.purpose.required", fallback: "Purpose is required")
      }
    }
    internal enum Success {
      /// We’re now processing this bank transfer to bank account ending %@.
      internal static func message(_ p1: Any) -> String {
        return L10n.tr("Localizable", "banktransfer.success.message", String(describing: p1), fallback: "We’re now processing this bank transfer to bank account ending %@.")
      }
      /// You’re almost there!
      internal static let title = L10n.tr("Localizable", "banktransfer.success.title", fallback: "You’re almost there!")
    }
  }
  internal enum Barcode {
    internal enum Detection {
      internal enum Failed {
        /// Not Supported
        internal static let reason = L10n.tr("Localizable", "barcode.detection.failed.reason", fallback: "Not Supported")
      }
    }
    internal enum Instructions {
      /// Scanner
      internal static let `default` = L10n.tr("Localizable", "barcode.instructions.default", fallback: "Align Barcode to scan.")
      /// Barcode Invalid. 
      /// Please try again.
      internal static let invalid = L10n.tr("Localizable", "barcode.instructions.invalid", fallback: "Barcode Invalid. \nPlease try again.")
    }
  }
  internal enum Bsp {
    internal enum HelpCenter {
      /// Help Center
      internal static let text = L10n.tr("Localizable", "bsp.helpCenter.text", fallback: "Help Center")
    }
    internal enum Website {
      /// BSP Footer
      internal static let text = L10n.tr("Localizable", "bsp.website.text", fallback: "bsp.gov.ph")
    }
  }
  internal enum Cashin {
    internal enum _3ds {
      internal enum Leave {
        internal enum Screen {
          /// Your progress will be lost
          internal static let message = L10n.tr("Localizable", "cashin.3ds.leave.screen.message", fallback: "Your progress will be lost")
          /// Are you sure you want to leave?
          internal static let title = L10n.tr("Localizable", "cashin.3ds.leave.screen.title", fallback: "Are you sure you want to leave?")
        }
      }
    }
    internal enum Account {
      internal enum Limits {
        /// Account Limits
        internal static let text = L10n.tr("Localizable", "cashin.account.limits.text", fallback: "Account Limits")
        internal enum New {
          /// account limits
          internal static let text = L10n.tr("Localizable", "cashin.account.limits.new.text", fallback: "account limits")
        }
      }
    }
    internal enum AccountLimit {
      internal enum LearnMore {
        /// Limits will reset on the first day of the next month. Learn more about Account Limits
        internal static let spiel = L10n.tr("Localizable", "cashin.accountLimit.learnMore.spiel", fallback: "Limits will reset on the first day of the next month. Learn more about Account Limits")
        internal enum New {
          /// Limits will reset on the first day of the next month. Learn more about account limits.
          internal static let spiel = L10n.tr("Localizable", "cashin.accountLimit.learnMore.new.spiel", fallback: "Limits will reset on the first day of the next month. Learn more about account limits.")
        }
      }
      internal enum Remaining {
        /// %@ remaining for cash in
        internal static func spiel(_ p1: Any) -> String {
          return L10n.tr("Localizable", "cashin.accountLimit.remaining.spiel", String(describing: p1), fallback: "%@ remaining for cash in")
        }
      }
    }
    internal enum Amount {
      /// Amount
      internal static let text = L10n.tr("Localizable", "cashin.amount.text", fallback: "Amount")
      /// Cash in amount
      internal static let title = L10n.tr("Localizable", "cashin.amount.title", fallback: "Cash in amount")
    }
    internal enum Cancel {
      internal enum Code {
        /// Are you done using this Cash In Code?
        internal static let title = L10n.tr("Localizable", "cashin.cancel.code.title", fallback: "Are you done using this Cash In Code?")
      }
    }
    internal enum Center {
      internal enum Instructions {
        /// <body bgcolor="%@"> <div style="font-family:-apple-system;color:#445157;margin-left:4px;margin-right:4px"> %@ </div> </body>
        internal static func html(_ p1: Any, _ p2: Any) -> String {
          return L10n.tr("Localizable", "cashin.center.instructions.html", String(describing: p1), String(describing: p2), fallback: "<body bgcolor=\"%@\"> <div style=\"font-family:-apple-system;color:#445157;margin-left:4px;margin-right:4px\"> %@ </div> </body>")
        }
      }
    }
    internal enum Code {
      internal enum Expired {
        /// Do you want to generate new code?
        internal static let message = L10n.tr("Localizable", "cashin.code.expired.message", fallback: "Do you want to generate new code?")
        /// Cash In Code has expired
        internal static let title = L10n.tr("Localizable", "cashin.code.expired.title", fallback: "Cash In Code has expired")
      }
    }
    internal enum Confirm {
      internal enum Transaction {
        /// We’re now processing this cash in transaction from your %@ ending %@.
        internal static func message(_ p1: Any, _ p2: Any) -> String {
          return L10n.tr("Localizable", "cashin.confirm.transaction.message", String(describing: p1), String(describing: p2), fallback: "We’re now processing this cash in transaction from your %@ ending %@.")
        }
        /// You’re almost there!
        internal static let title = L10n.tr("Localizable", "cashin.confirm.transaction.title", fallback: "You’re almost there!")
      }
    }
    internal enum Confirmation {
      /// Confirm transaction
      internal static let title = L10n.tr("Localizable", "cashin.confirmation.title", fallback: "Confirm transaction")
    }
    internal enum Error {
      /// There was a problem with the card you provided. Please contact your card issuer for further assistance.
      internal static let defaultMessage = L10n.tr("Localizable", "cashin.error.defaultMessage", fallback: "There was a problem with the card you provided. Please contact your card issuer for further assistance.")
    }
    internal enum Failed {
      internal enum Transaction {
        /// Cash in transaction failed
        internal static let title = L10n.tr("Localizable", "cashin.failed.transaction.title", fallback: "Cash in transaction failed")
      }
    }
    internal enum From {
      internal enum Loadup {
        /// Cash in from %@
        internal static func partner(_ p1: Any) -> String {
          return L10n.tr("Localizable", "cashin.from.loadup.partner", String(describing: p1), fallback: "Cash in from %@")
        }
      }
    }
    internal enum Generate {
      internal enum New {
        /// Generate New Code
        internal static let code = L10n.tr("Localizable", "cashin.generate.new.code", fallback: "Generate New Code")
      }
    }
    internal enum Header {
      /// Cash In
      internal static let title = L10n.tr("Localizable", "cashin.header.title", fallback: "Cash In")
    }
    internal enum Maximum {
      internal enum Limit {
        internal enum Reached {
          /// You can only use up to %d cards to cash in. Please delete an existing card to use a new card
          internal static func message(_ p1: Int) -> String {
            return L10n.tr("Localizable", "cashin.maximum.limit.reached.message", p1, fallback: "You can only use up to %d cards to cash in. Please delete an existing card to use a new card")
          }
          /// Maximum limit reached
          internal static let title = L10n.tr("Localizable", "cashin.maximum.limit.reached.title", fallback: "Maximum limit reached")
        }
      }
    }
    internal enum Maya {
      internal enum RecommendedMethods {
        internal enum BankAccount {
          /// Bank account
          internal static let title = L10n.tr("Localizable", "cashin.maya.recommendedMethods.bankAccount.title", fallback: "Bank account")
        }
        internal enum Card {
          /// Debit or credit card
          internal static let title = L10n.tr("Localizable", "cashin.maya.recommendedMethods.card.title", fallback: "Debit or credit card")
        }
        internal enum Maya {
          internal enum Center {
            /// Maya Center
            internal static let title = L10n.tr("Localizable", "cashin.maya.recommendedMethods.maya.center.title", fallback: "Maya Center")
          }
        }
        internal enum Padala {
          /// Padala agents
          internal static let title = L10n.tr("Localizable", "cashin.maya.recommendedMethods.padala.title", fallback: "Padala agents")
        }
      }
      internal enum WalletSettingsMethods {
        /// Wallet options
        internal static let title = L10n.tr("Localizable", "cashin.maya.walletSettingsMethods.title", fallback: "Wallet options")
        internal enum AutoCashin {
          /// Auto cash in
          internal static let title = L10n.tr("Localizable", "cashin.maya.walletSettingsMethods.autoCashin.title", fallback: "Auto cash in")
        }
        internal enum InstaFill {
          /// Insta fill
          internal static let title = L10n.tr("Localizable", "cashin.maya.walletSettingsMethods.instaFill.title", fallback: "Insta fill")
        }
        internal enum WalletTransactionLimit {
          /// Wallet transaction limit
          internal static let title = L10n.tr("Localizable", "cashin.maya.walletSettingsMethods.walletTransactionLimit.title", fallback: "Wallet transaction limit")
        }
      }
    }
    internal enum RecommendedMethods {
      internal enum BankAccount {
        /// Transfer funds directly from your bank account
        internal static let subtitle = L10n.tr("Localizable", "cashin.recommendedMethods.bankAccount.subtitle", fallback: "Transfer funds directly from your bank account")
      }
      internal enum Card {
        /// Cash In conveniently using your debit or credit card
        internal static let subtitle = L10n.tr("Localizable", "cashin.recommendedMethods.card.subtitle", fallback: "Cash In conveniently using your debit or credit card")
      }
      internal enum MayaSavings {
        /// Maya Savings
        internal static let title = L10n.tr("Localizable", "cashin.recommendedMethods.mayaSavings.title", fallback: "Maya Savings")
      }
      internal enum Padala {
        /// Cash In at your nearest Smart Padala agent
        internal static let subtitle = L10n.tr("Localizable", "cashin.recommendedMethods.padala.subtitle", fallback: "Cash In at your nearest Smart Padala agent")
      }
    }
    internal enum Remove {
      internal enum Card {
        internal enum Prompt {
          /// Card number ending in %@
          internal static func message(_ p1: Any) -> String {
            return L10n.tr("Localizable", "cashin.remove.card.prompt.message", String(describing: p1), fallback: "Card number ending in %@")
          }
          /// Are you sure you want to remove your card?
          internal static let title = L10n.tr("Localizable", "cashin.remove.card.prompt.title", fallback: "Are you sure you want to remove your card?")
        }
        internal enum Success {
          /// You have successfully deleted your %@ card from Maya.
          internal static func message(_ p1: Any) -> String {
            return L10n.tr("Localizable", "cashin.remove.card.success.message", String(describing: p1), fallback: "You have successfully deleted your %@ card from Maya.")
          }
          /// Card successfully removed
          internal static let title = L10n.tr("Localizable", "cashin.remove.card.success.title", fallback: "Card successfully removed")
        }
      }
    }
    internal enum Screen {
      /// Cash In
      internal static let title = L10n.tr("Localizable", "cashin.screen.title", fallback: "Cash In")
      internal enum Title {
        /// Cash in
        internal static let lowercase = L10n.tr("Localizable", "cashin.screen.title.lowercase", fallback: "Cash in")
      }
    }
    internal enum Seven {
      internal enum Amount {
        /// Provide an amount between %@ up to %@. Convenience fee may apply.
        internal static func message(_ p1: Any, _ p2: Any) -> String {
          return L10n.tr("Localizable", "cashin.seven.amount.message", String(describing: p1), String(describing: p2), fallback: "Provide an amount between %@ up to %@. Convenience fee may apply.")
        }
        internal enum Error {
          /// Type a number between %@ up to %@
          internal static func message(_ p1: Any, _ p2: Any) -> String {
            return L10n.tr("Localizable", "cashin.seven.amount.error.message", String(describing: p1), String(describing: p2), fallback: "Type a number between %@ up to %@")
          }
        }
        internal enum Message {
          /// From %@ up to %@ only
          internal static func old(_ p1: Any, _ p2: Any) -> String {
            return L10n.tr("Localizable", "cashin.seven.amount.message.old", String(describing: p1), String(describing: p2), fallback: "From %@ up to %@ only")
          }
        }
      }
    }
    internal enum Spiel {
      /// Are you done using cash in code?
      internal static let done = L10n.tr("Localizable", "cashin.spiel.done", fallback: "Are you done using cash in code?")
      /// Maya’s Terms & Conditions
      internal static let mayaTerms = L10n.tr("Localizable", "cashin.spiel.mayaTerms", fallback: "Maya’s Terms & Conditions")
      /// Western Union
      internal static let westernUnion = L10n.tr("Localizable", "cashin.spiel.westernUnion", fallback: "Western Union")
      /// Western Union’s Terms and Conditions
      internal static let westernUnionTerms = L10n.tr("Localizable", "cashin.spiel.westernUnionTerms", fallback: "Western Union’s Terms and Conditions")
      internal enum Back {
        internal enum To {
          /// Back to cash in
          internal static let cashin = L10n.tr("Localizable", "cashin.spiel.back.to.cashin", fallback: "Back to cash in")
        }
      }
      internal enum From {
        internal enum Partner {
          internal enum To {
            /// From our partner to your wallet
            internal static let wallet = L10n.tr("Localizable", "cashin.spiel.from.partner.to.wallet", fallback: "From our partner to your wallet")
          }
        }
      }
      internal enum Generate {
        /// Generate Code
        internal static let code = L10n.tr("Localizable", "cashin.spiel.generate.code", fallback: "Generate Code")
      }
      internal enum Something {
        internal enum Went {
          /// Something went wrong. Please try again in a bit.
          internal static let wrong = L10n.tr("Localizable", "cashin.spiel.something.went.wrong", fallback: "Something went wrong. Please try again in a bit.")
        }
      }
      internal enum Successfully {
        /// You’ve successfully received %@
        internal static func received(_ p1: Any) -> String {
          return L10n.tr("Localizable", "cashin.spiel.successfully.received", String(describing: p1), fallback: "You’ve successfully received %@")
        }
      }
      internal enum Transaction {
        /// Cash in transaction failed
        internal static let failed = L10n.tr("Localizable", "cashin.spiel.transaction.failed", fallback: "Cash in transaction failed")
      }
      internal enum Wayfinder {
        /// Find the nearest Padala agent around your area​
        internal static let padala = L10n.tr("Localizable", "cashin.spiel.wayfinder.padala", fallback: "Find the nearest Padala agent around your area​")
        internal enum Maya {
          /// Find the nearest Maya Centers around your area​
          internal static let center = L10n.tr("Localizable", "cashin.spiel.wayfinder.maya.center", fallback: "Find the nearest Maya Centers around your area​")
        }
      }
      internal enum Western {
        internal enum Union {
          internal enum Confirmation {
            /// By proceeding, you are accepting Maya’s Terms & Conditions and Privacy Policy and Western Union’s Terms and Conditions for the use of this service
            internal static let policy = L10n.tr("Localizable", "cashin.spiel.western.union.confirmation.policy", fallback: "By proceeding, you are accepting Maya’s Terms & Conditions and Privacy Policy and Western Union’s Terms and Conditions for the use of this service")
          }
        }
      }
    }
    internal enum Success {
      /// Cash In successful!
      internal static let title = L10n.tr("Localizable", "cashin.success.title", fallback: "Cash In successful!")
      internal enum Detail {
        /// MTCN
        internal static let mtcn = L10n.tr("Localizable", "cashin.success.detail.mtcn", fallback: "MTCN")
        /// Sender
        internal static let sender = L10n.tr("Localizable", "cashin.success.detail.sender", fallback: "Sender")
      }
      internal enum Maya {
        internal enum Bottomsheet {
          /// You’ve successfully cashed in %@
          internal static func title(_ p1: Any) -> String {
            return L10n.tr("Localizable", "cashin.success.maya.bottomsheet.title", String(describing: p1), fallback: "You’ve successfully cashed in %@")
          }
        }
      }
      internal enum TopUp {
        /// Receive Money Successful!
        internal static let title = L10n.tr("Localizable", "cashin.success.topUp.title", fallback: "Receive Money Successful!")
      }
    }
    internal enum Vaulted {
      internal enum Card {
        internal enum Disabled {
          /// Sorry, temporarily we cannot proceed with your cash in transaction using this card.
          internal static let spiel = L10n.tr("Localizable", "cashin.vaulted.card.disabled.spiel", fallback: "Sorry, temporarily we cannot proceed with your cash in transaction using this card.")
        }
      }
    }
    internal enum Via {
      internal enum Loadup {
        /// Cash in via %@
        internal static func partner(_ p1: Any) -> String {
          return L10n.tr("Localizable", "cashin.via.loadup.partner", String(describing: p1), fallback: "Cash in via %@")
        }
      }
      internal enum New {
        /// Cash in via new card
        internal static let card = L10n.tr("Localizable", "cashin.via.new.card", fallback: "Cash in via new card")
      }
      internal enum Western {
        /// via Western Union
        internal static let union = L10n.tr("Localizable", "cashin.via.western.union", fallback: "via Western Union")
      }
    }
    internal enum Viacard {
      /// Terms & Conditions
      internal static let termsAndConditions = L10n.tr("Localizable", "cashin.viacard.termsAndConditions", fallback: "Terms & Conditions")
      internal enum Confirmation {
        /// Cash in now
        internal static let button = L10n.tr("Localizable", "cashin.viacard.confirmation.button", fallback: "Cash in now")
        /// Please contact your issuing bank or provider for any additional charges you may incur.
        internal static let info = L10n.tr("Localizable", "cashin.viacard.confirmation.info", fallback: "Please contact your issuing bank or provider for any additional charges you may incur.")
        internal enum Detail {
          /// Convenience fee
          internal static let `convenience` = L10n.tr("Localizable", "cashin.viacard.confirmation.detail.convenience", fallback: "Convenience fee")
          /// Source
          internal static let source = L10n.tr("Localizable", "cashin.viacard.confirmation.detail.source", fallback: "Source")
          /// Total amount to be deducted
          internal static let totalAmount = L10n.tr("Localizable", "cashin.viacard.confirmation.detail.totalAmount", fallback: "Total amount to be deducted")
        }
        internal enum Dontsave {
          /// You have opted not to save this card
          internal static let description = L10n.tr("Localizable", "cashin.viacard.confirmation.dontsave.description", fallback: "You have opted not to save this card")
          /// Don’t save this Card
          internal static let header = L10n.tr("Localizable", "cashin.viacard.confirmation.dontsave.header", fallback: "Don’t save this Card")
        }
      }
      internal enum Default {
        internal enum Amount {
          /// Convenience fee may apply
          internal static let note = L10n.tr("Localizable", "cashin.viacard.default.amount.note", fallback: "Convenience fee may apply")
        }
      }
      internal enum Form {
        /// Choose from the recommended amounts below.
        internal static let hint = L10n.tr("Localizable", "cashin.viacard.form.hint", fallback: "Choose from the recommended amounts below.")
      }
      internal enum Required {
        /// Card Number is required
        internal static let cardNumber = L10n.tr("Localizable", "cashin.viacard.required.cardNumber", fallback: "Card Number is required")
        /// CVV/CSC is required
        internal static let cvv = L10n.tr("Localizable", "cashin.viacard.required.cvv", fallback: "CVV/CSC is required")
        /// Date is required
        internal static let expiry = L10n.tr("Localizable", "cashin.viacard.required.expiry", fallback: "Date is required")
        internal enum CardNumber {
          /// Card number is required
          internal static let new = L10n.tr("Localizable", "cashin.viacard.required.cardNumber.new", fallback: "Card number is required")
        }
        internal enum Cvv {
          /// CVV security code is required
          internal static let new = L10n.tr("Localizable", "cashin.viacard.required.cvv.new", fallback: "CVV security code is required")
        }
        internal enum Expiry {
          /// Month is required
          internal static let month = L10n.tr("Localizable", "cashin.viacard.required.expiry.month", fallback: "Month is required")
          /// Year is required
          internal static let year = L10n.tr("Localizable", "cashin.viacard.required.expiry.year", fallback: "Year is required")
        }
      }
      internal enum Select {
        /// Select month
        internal static let month = L10n.tr("Localizable", "cashin.viacard.select.month", fallback: "Select month")
        /// Select year
        internal static let year = L10n.tr("Localizable", "cashin.viacard.select.year", fallback: "Select year")
      }
      internal enum Success {
        /// Fund Source
        internal static let fundsource = L10n.tr("Localizable", "cashin.viacard.success.fundsource", fallback: "Fund Source")
      }
    }
    internal enum WesternUnion {
      /// https://www.westernunion.com/us/en/what-is-mtcn.html
      internal static let faq = L10n.tr("Localizable", "cashin.westernUnion.faq", fallback: "https://www.westernunion.com/us/en/what-is-mtcn.html")
      /// https://www.westernunion.com/PH/en/terms-conditions.html
      internal static let terms = L10n.tr("Localizable", "cashin.westernUnion.terms", fallback: "https://www.westernunion.com/PH/en/terms-conditions.html")
      internal enum Maya {
        /// Remittance could not be credited to your account. You need to have a government ID linked to your Maya account. Please call (02) 845-7788 for more details.
        internal static let noGovID = L10n.tr("Localizable", "cashin.westernUnion.maya.noGovID", fallback: "Remittance could not be credited to your account. You need to have a government ID linked to your Maya account. Please call (02) 845-7788 for more details.")
      }
    }
  }
  internal enum ChangeMin {
    internal enum Leave {
      internal enum Screen {
        /// Your progress will be lost
        internal static let message = L10n.tr("Localizable", "changeMin.leave.screen.message", fallback: "Your progress will be lost")
        /// Are you sure you want to leave?
        internal static let title = L10n.tr("Localizable", "changeMin.leave.screen.title", fallback: "Are you sure you want to leave?")
      }
    }
    internal enum Maya {
      internal enum Confirm {
        internal enum Back {
          /// You'll lose your progress if you leave.
          internal static let message = L10n.tr("Localizable", "changeMin.maya.confirm.back.message", fallback: "You'll lose your progress if you leave.")
          /// Changed your mind?
          internal static let title = L10n.tr("Localizable", "changeMin.maya.confirm.back.title", fallback: "Changed your mind?")
        }
        internal enum Password {
          /// Please enter your password to confirm that you want to change your registered mobile number.
          internal static let spiel = L10n.tr("Localizable", "changeMin.maya.confirm.password.spiel", fallback: "Please enter your password to confirm that you want to change your registered mobile number.")
        }
      }
      internal enum Kyc {
        internal enum Prompt {
          /// Get access to this feature and a bunch more when you upgrade your account. It’s free and takes just a few minutes!
          internal static let message = L10n.tr("Localizable", "changeMin.maya.kyc.prompt.message", fallback: "Get access to this feature and a bunch more when you upgrade your account. It’s free and takes just a few minutes!")
          /// Upgrade your account to change your mobile number.
          internal static let title = L10n.tr("Localizable", "changeMin.maya.kyc.prompt.title", fallback: "Upgrade your account to change your mobile number.")
        }
      }
      internal enum Reminders {
        /// both your OLD and NEW
        internal static let highlightedText = L10n.tr("Localizable", "changeMin.maya.reminders.highlightedText", fallback: "both your OLD and NEW")
        internal enum Tap {
          /// tap here
          internal static let here = L10n.tr("Localizable", "changeMin.maya.reminders.tap.here", fallback: "tap here")
        }
      }
      internal enum Spiel {
        internal enum Same {
          /// Oops, that's your current number! Please enter a different mobile number.
          internal static let mobileNumber = L10n.tr("Localizable", "changeMin.maya.spiel.same.mobileNumber", fallback: "Oops, that's your current number! Please enter a different mobile number.")
        }
      }
      internal enum Success {
        /// You will now be logged out. Please log in again using your new mobile number.
        internal static let message = L10n.tr("Localizable", "changeMin.maya.success.message", fallback: "You will now be logged out. Please log in again using your new mobile number.")
        /// New Maya number saved!
        internal static let title = L10n.tr("Localizable", "changeMin.maya.success.title", fallback: "New Maya number saved!")
      }
    }
    internal enum Spiel {
      internal enum Invalid {
        /// Please provide a valid mobile number
        internal static let mobileNumber = L10n.tr("Localizable", "changeMin.spiel.invalid.mobileNumber", fallback: "Please provide a valid mobile number")
      }
      internal enum Required {
        /// Change Min
        internal static let mobileNumber = L10n.tr("Localizable", "changeMin.spiel.required.mobileNumber", fallback: "Mobile number is required")
      }
      internal enum Success {
        /// We will now redirect you to the login screen. You may use this number to login to your PayMaya account.
        internal static let message = L10n.tr("Localizable", "changeMin.spiel.success.message", fallback: "We will now redirect you to the login screen. You may use this number to login to your PayMaya account.")
        /// Got it! Your new PayMaya account number has been updated to %@.
        internal static func title(_ p1: Any) -> String {
          return L10n.tr("Localizable", "changeMin.spiel.success.title", String(describing: p1), fallback: "Got it! Your new PayMaya account number has been updated to %@.")
        }
      }
    }
  }
  internal enum Clevertap {
    internal enum Action {
      internal enum Back {
        /// CleverTap Notification
        internal static let title = L10n.tr("Localizable", "clevertap.action.back.title", fallback: "Back")
      }
      internal enum LearnMore {
        /// Learn More
        internal static let title = L10n.tr("Localizable", "clevertap.action.learnMore.title", fallback: "Learn More")
      }
      internal enum Next {
        /// Next
        internal static let title = L10n.tr("Localizable", "clevertap.action.next.title", fallback: "Next")
      }
    }
  }
  internal enum Compliance {
    internal enum Dosri {
      internal enum RelativeInformation {
        /// Compliance
        internal static let title = L10n.tr("Localizable", "compliance.dosri.relativeInformation.title", fallback: "Relative's information")
        internal enum AddAnotherRelative {
          /// + Add another relative
          internal static let title = L10n.tr("Localizable", "compliance.dosri.relativeInformation.addAnotherRelative.title", fallback: "+ Add another relative")
        }
        internal enum FirstName {
          /// First name is required
          internal static let error = L10n.tr("Localizable", "compliance.dosri.relativeInformation.firstName.error", fallback: "First name is required")
          /// Enter first name
          internal static let placeholder = L10n.tr("Localizable", "compliance.dosri.relativeInformation.firstName.placeholder", fallback: "Enter first name")
          /// First name
          internal static let title = L10n.tr("Localizable", "compliance.dosri.relativeInformation.firstName.title", fallback: "First name")
        }
        internal enum LastName {
          /// Last name is required
          internal static let error = L10n.tr("Localizable", "compliance.dosri.relativeInformation.lastName.error", fallback: "Last name is required")
          /// Enter last name
          internal static let placeholder = L10n.tr("Localizable", "compliance.dosri.relativeInformation.lastName.placeholder", fallback: "Enter last name")
          /// Last name
          internal static let title = L10n.tr("Localizable", "compliance.dosri.relativeInformation.lastName.title", fallback: "Last name")
        }
        internal enum MiddleName {
          /// Middle name is required
          internal static let error = L10n.tr("Localizable", "compliance.dosri.relativeInformation.middleName.error", fallback: "Middle name is required")
          /// Enter middle name
          internal static let placeholder = L10n.tr("Localizable", "compliance.dosri.relativeInformation.middleName.placeholder", fallback: "Enter middle name")
          /// Middle name
          internal static let title = L10n.tr("Localizable", "compliance.dosri.relativeInformation.middleName.title", fallback: "Middle name")
        }
        internal enum Relationship {
          /// Relationship is required
          internal static let error = L10n.tr("Localizable", "compliance.dosri.relativeInformation.relationship.error", fallback: "Relationship is required")
          /// Select your relationship
          internal static let placeholder = L10n.tr("Localizable", "compliance.dosri.relativeInformation.relationship.placeholder", fallback: "Select your relationship")
          /// Relationship
          internal static let title = L10n.tr("Localizable", "compliance.dosri.relativeInformation.relationship.title", fallback: "Relationship")
          internal enum Cancel {
            /// Cancel
            internal static let title = L10n.tr("Localizable", "compliance.dosri.relativeInformation.relationship.cancel.title", fallback: "Cancel")
          }
          internal enum SameRelative {
            /// Cannot declare the same person as a relative more than once
            internal static let error = L10n.tr("Localizable", "compliance.dosri.relativeInformation.relationship.sameRelative.error", fallback: "Cannot declare the same person as a relative more than once")
          }
          internal enum SameUser {
            /// You must name another person as your relative
            internal static let error = L10n.tr("Localizable", "compliance.dosri.relativeInformation.relationship.sameUser.error", fallback: "You must name another person as your relative")
          }
        }
      }
    }
  }
  internal enum CreatorStore {
    internal enum ESims {
      /// Creator Store
      internal static let title = L10n.tr("Localizable", "creatorStore.eSims.title", fallback: "eSIMs")
    }
    internal enum Error {
      internal enum Restricted {
        /// Help Center
        internal static let helpCenter = L10n.tr("Localizable", "creatorStore.error.restricted.helpCenter", fallback: "Help Center")
        /// %@ may not be available to everyone.
        /// Visit our Help Center to learn how this feature can be enabled for you.
        internal static func message(_ p1: Any) -> String {
          return L10n.tr("Localizable", "creatorStore.error.restricted.message", String(describing: p1), fallback: "%@ may not be available to everyone.\nVisit our Help Center to learn how this feature can be enabled for you.")
        }
        /// This feature is unavailable to you
        internal static let title = L10n.tr("Localizable", "creatorStore.error.restricted.title", fallback: "This feature is unavailable to you")
      }
    }
    internal enum Food {
      /// Food
      internal static let title = L10n.tr("Localizable", "creatorStore.food.title", fallback: "Food")
    }
    internal enum Funds {
      /// Funds
      internal static let title = L10n.tr("Localizable", "creatorStore.funds.title", fallback: "Funds")
    }
    internal enum Games {
      /// Games
      internal static let title = L10n.tr("Localizable", "creatorStore.games.title", fallback: "Games")
    }
    internal enum GlobalRemittance {
      /// Global remittance
      internal static let title = L10n.tr("Localizable", "creatorStore.globalRemittance.title", fallback: "Global remittance")
    }
    internal enum GlobalStocks {
      /// Global Stocks
      internal static let title = L10n.tr("Localizable", "creatorStore.globalStocks.title", fallback: "Global Stocks")
    }
    internal enum Government {
      /// Government
      internal static let title = L10n.tr("Localizable", "creatorStore.government.title", fallback: "Government")
    }
    internal enum Insurance {
      /// Insurance
      internal static let title = L10n.tr("Localizable", "creatorStore.insurance.title", fallback: "Insurance")
    }
    internal enum Interstitial {
      internal enum Button {
        /// Get started
        internal static let title = L10n.tr("Localizable", "creatorStore.interstitial.button.title", fallback: "Get started")
      }
    }
    internal enum LuckyGames {
      /// Lucky Games
      internal static let title = L10n.tr("Localizable", "creatorStore.luckyGames.title", fallback: "Lucky Games")
    }
    internal enum MerchantRewards {
      /// Merchant Rewards
      internal static let title = L10n.tr("Localizable", "creatorStore.merchantRewards.title", fallback: "Merchant Rewards")
    }
    internal enum PayLater {
      /// Maya Bank
      internal static let title = L10n.tr("Localizable", "creatorStore.payLater.title", fallback: "Maya Bank")
    }
    internal enum Retail {
      /// Retail
      internal static let title = L10n.tr("Localizable", "creatorStore.retail.title", fallback: "Retail")
    }
    internal enum SparkHackathon {
      /// Spark Hackathon
      internal static let title = L10n.tr("Localizable", "creatorStore.sparkHackathon.title", fallback: "Spark Hackathon")
    }
    internal enum Stocks {
      /// Stocks
      internal static let title = L10n.tr("Localizable", "creatorStore.stocks.title", fallback: "Stocks")
    }
    internal enum StocksGame {
      /// Stocks game
      internal static let title = L10n.tr("Localizable", "creatorStore.stocksGame.title", fallback: "Stocks game")
    }
    internal enum Stream {
      /// Stream
      internal static let title = L10n.tr("Localizable", "creatorStore.stream.title", fallback: "Stream")
    }
  }
  internal enum Credential {
    internal enum Authentication {
      /// Do you want to use %@ for user authentication?
      internal static func confirmation(_ p1: Any) -> String {
        return L10n.tr("Localizable", "credential.authentication.confirmation", String(describing: p1), fallback: "Do you want to use %@ for user authentication?")
      }
      /// Confirm Credentials
      internal static func reason(_ p1: Any) -> String {
        return L10n.tr("Localizable", "credential.authentication.reason", String(describing: p1), fallback: "Login with your %@")
      }
    }
  }
  internal enum Dashboard {
    internal enum ErrorSpiel {
      /// Dashboard
      internal static let activity = L10n.tr("Localizable", "dashboard.errorSpiel.activity", fallback: "Recent activities can’t be updated right now. Please try again later.")
      /// Available balance can’t be updated right now. Please try again later.
      internal static let balance = L10n.tr("Localizable", "dashboard.errorSpiel.balance", fallback: "Available balance can’t be updated right now. Please try again later.")
      /// Available balance and recent activities can’t be updated right now. Please try again later.
      internal static let balanceAndActivity = L10n.tr("Localizable", "dashboard.errorSpiel.balanceAndActivity", fallback: "Available balance and recent activities can’t be updated right now. Please try again later.")
    }
    internal enum Footer {
      /// Maya Philippines, Inc. is regulated by the Bangko Sentral ng Pilipinas – bsp.gov.ph.
      /// Visit our Help Center or check our service advisories for any concerns.
      internal static let message = L10n.tr("Localizable", "dashboard.footer.message", fallback: "Maya Philippines, Inc. is regulated by the Bangko Sentral ng Pilipinas – bsp.gov.ph.\nVisit our Help Center or check our service advisories for any concerns.")
      internal enum Service {
        /// service advisories
        internal static let advisories = L10n.tr("Localizable", "dashboard.footer.service.advisories", fallback: "service advisories")
      }
    }
    internal enum Latest {
      internal enum Activities {
        /// Transactions
        internal static let title = L10n.tr("Localizable", "dashboard.latest.activities.title", fallback: "Transactions")
      }
    }
    internal enum Menu {
      internal enum Title {
        /// Cards
        internal static let cards = L10n.tr("Localizable", "dashboard.menu.title.cards", fallback: "Cards")
        /// Credit
        internal static let credit = L10n.tr("Localizable", "dashboard.menu.title.credit", fallback: "Credit")
        /// Loans
        internal static let loans = L10n.tr("Localizable", "dashboard.menu.title.loans", fallback: "Loans")
        /// Savings
        internal static let savings = L10n.tr("Localizable", "dashboard.menu.title.savings", fallback: "Savings")
        /// Wallet
        internal static let wallet = L10n.tr("Localizable", "dashboard.menu.title.wallet", fallback: "Wallet")
      }
    }
    internal enum Rewards {
      internal enum Missions {
        /// Earn rewards for
        /// completing tasks
        internal static let subtitle = L10n.tr("Localizable", "dashboard.rewards.missions.subtitle", fallback: "Earn rewards for\ncompleting tasks")
        /// Missions
        internal static let title = L10n.tr("Localizable", "dashboard.rewards.missions.title", fallback: "Missions")
      }
      internal enum Vouchers {
        /// Go claim them
        /// before they’re gone
        internal static let subtitle = L10n.tr("Localizable", "dashboard.rewards.vouchers.subtitle", fallback: "Go claim them\nbefore they’re gone")
        /// Vouchers
        internal static let title = L10n.tr("Localizable", "dashboard.rewards.vouchers.title", fallback: "Vouchers")
      }
    }
    internal enum Service {
      internal enum Title {
        /// Bank
        /// transfer
        internal static let bankTransfer = L10n.tr("Localizable", "dashboard.service.title.bankTransfer", fallback: "Bank\ntransfer")
        /// Bills
        internal static let bills = L10n.tr("Localizable", "dashboard.service.title.bills", fallback: "Bills")
        /// Deadline
        /// Tickets
        internal static let blackPinkGiveaway = L10n.tr("Localizable", "dashboard.service.title.blackPinkGiveaway", fallback: "Deadline\nTickets")
        /// Cards
        internal static let cards = L10n.tr("Localizable", "dashboard.service.title.cards", fallback: "Cards")
        /// Cash in
        internal static let cashIn = L10n.tr("Localizable", "dashboard.service.title.cashIn", fallback: "Cash in")
        /// Credit Cards
        internal static let creditCards = L10n.tr("Localizable", "dashboard.service.title.creditCards", fallback: "Credit Cards")
        /// Crypto
        internal static let crypto = L10n.tr("Localizable", "dashboard.service.title.crypto", fallback: "Crypto")
        /// Deals
        internal static let deals = L10n.tr("Localizable", "dashboard.service.title.deals", fallback: "Deals")
        /// Donate
        internal static let donate = L10n.tr("Localizable", "dashboard.service.title.donate", fallback: "Donate")
        /// eSIMs
        internal static let esims = L10n.tr("Localizable", "dashboard.service.title.esims", fallback: "eSIMs")
        /// Food
        internal static let food = L10n.tr("Localizable", "dashboard.service.title.food", fallback: "Food")
        /// Funds
        internal static let funds = L10n.tr("Localizable", "dashboard.service.title.funds", fallback: "Funds")
        /// Global remittance
        internal static let globalRemittance = L10n.tr("Localizable", "dashboard.service.title.globalRemittance", fallback: "Global remittance")
        /// Global Stocks
        internal static let globalStocks = L10n.tr("Localizable", "dashboard.service.title.globalStocks", fallback: "Global Stocks")
        /// Government
        internal static let government = L10n.tr("Localizable", "dashboard.service.title.government", fallback: "Government")
        /// Insurance
        internal static let insurance = L10n.tr("Localizable", "dashboard.service.title.insurance", fallback: "Insurance")
        /// Invite a friend
        internal static let inviteAFriend = L10n.tr("Localizable", "dashboard.service.title.inviteAFriend", fallback: "Invite a friend")
        /// Load
        internal static let load = L10n.tr("Localizable", "dashboard.service.title.load", fallback: "Load")
        /// Personal Loans
        internal static let loans = L10n.tr("Localizable", "dashboard.service.title.loans", fallback: "Personal Loans")
        /// LRT-1
        internal static let lrt1 = L10n.tr("Localizable", "dashboard.service.title.lrt1", fallback: "LRT-1")
        /// Lucky Games
        internal static let luckyGames = L10n.tr("Localizable", "dashboard.service.title.luckyGames", fallback: "Lucky Games")
        /// Credit
        internal static let mayaCredit = L10n.tr("Localizable", "dashboard.service.title.mayaCredit", fallback: "Credit")
        /// Maya Mall
        internal static let mayaMall = L10n.tr("Localizable", "dashboard.service.title.mayaMall", fallback: "Maya Mall")
        /// Merchant Rewards
        internal static let merchantRewards = L10n.tr("Localizable", "dashboard.service.title.merchantRewards", fallback: "Merchant Rewards")
        /// Merchants
        internal static let merchants = L10n.tr("Localizable", "dashboard.service.title.merchants", fallback: "Merchants")
        /// Missions
        internal static let missions = L10n.tr("Localizable", "dashboard.service.title.missions", fallback: "Missions")
        /// More
        internal static let more = L10n.tr("Localizable", "dashboard.service.title.more", fallback: "More")
        /// Cards
        internal static let myCards = L10n.tr("Localizable", "dashboard.service.title.myCards", fallback: "Cards")
        /// My QR
        internal static let myQR = L10n.tr("Localizable", "dashboard.service.title.myQR", fallback: "My QR")
        /// PAL
        internal static let pal = L10n.tr("Localizable", "dashboard.service.title.pal", fallback: "PAL")
        /// PayLater
        internal static let payLater = L10n.tr("Localizable", "dashboard.service.title.payLater", fallback: "PayLater")
        /// PBB
        internal static let pbb = L10n.tr("Localizable", "dashboard.service.title.pbb", fallback: "PBB")
        /// Personal Goals
        internal static let personalGoals = L10n.tr("Localizable", "dashboard.service.title.personalGoals", fallback: "Personal Goals")
        /// Protect
        internal static let protect = L10n.tr("Localizable", "dashboard.service.title.protect", fallback: "Protect")
        /// Purchase Financing
        internal static let purchaseFinancing = L10n.tr("Localizable", "dashboard.service.title.purchaseFinancing", fallback: "Purchase Financing")
        /// Raffle Promo
        internal static let rafflePromo = L10n.tr("Localizable", "dashboard.service.title.rafflePromo", fallback: "Raffle Promo")
        /// Retail
        internal static let retail = L10n.tr("Localizable", "dashboard.service.title.retail", fallback: "Retail")
        /// Savings
        internal static let savings = L10n.tr("Localizable", "dashboard.service.title.savings", fallback: "Savings")
        /// Savings To Wallet
        internal static let savingsToWallet = L10n.tr("Localizable", "dashboard.service.title.savingsToWallet", fallback: "Savings To Wallet")
        /// Pay with QR
        internal static let scanqr = L10n.tr("Localizable", "dashboard.service.title.scanqr", fallback: "Pay with QR")
        /// Schedules
        internal static let scheduler = L10n.tr("Localizable", "dashboard.service.title.scheduler", fallback: "Schedules")
        /// Security center
        internal static let securityCenter = L10n.tr("Localizable", "dashboard.service.title.securityCenter", fallback: "Security center")
        /// Send
        /// money
        internal static let sendMoney = L10n.tr("Localizable", "dashboard.service.title.sendMoney", fallback: "Send\nmoney")
        /// Shop
        internal static let shop = L10n.tr("Localizable", "dashboard.service.title.shop", fallback: "Shop")
        /// Spark Hackathon
        internal static let sparkHackathon = L10n.tr("Localizable", "dashboard.service.title.sparkHackathon", fallback: "Spark Hackathon")
        /// Stocks
        internal static let stocks = L10n.tr("Localizable", "dashboard.service.title.stocks", fallback: "Stocks")
        /// Stocks game
        internal static let stocksGame = L10n.tr("Localizable", "dashboard.service.title.stocksGame", fallback: "Stocks game")
        /// Stream
        internal static let stream = L10n.tr("Localizable", "dashboard.service.title.stream", fallback: "Stream")
        /// Time Deposit Plus
        internal static let timeDeposit = L10n.tr("Localizable", "dashboard.service.title.timeDeposit", fallback: "Time Deposit Plus")
        /// Travel
        internal static let travel = L10n.tr("Localizable", "dashboard.service.title.travel", fallback: "Travel")
        /// Vouchers
        internal static let voucher = L10n.tr("Localizable", "dashboard.service.title.voucher", fallback: "Vouchers")
      }
    }
  }
  internal enum Disclosure {
    internal enum Spiel {
      internal enum AgreeAndContinue {
        /// “Agree and Continue”
        internal static let title = L10n.tr("Localizable", "disclosure.spiel.agreeAndContinue.title", fallback: "“Agree and Continue”")
      }
      internal enum DataFromDevice {
        /// Data from device settings:
        internal static let title = L10n.tr("Localizable", "disclosure.spiel.dataFromDevice.title", fallback: "Data from device settings:")
      }
      internal enum DataFromNetwork {
        /// Data from network connections:
        internal static let title = L10n.tr("Localizable", "disclosure.spiel.dataFromNetwork.title", fallback: "Data from network connections:")
      }
      internal enum DataPrivacy {
        /// Data Privacy
        internal static let title = L10n.tr("Localizable", "disclosure.spiel.dataPrivacy.title", fallback: "Data Privacy")
      }
      internal enum DeviceAttributes {
        /// Device attributes:
        internal static let title = L10n.tr("Localizable", "disclosure.spiel.deviceAttributes.title", fallback: "Device attributes:")
      }
      internal enum DeviceIdentifiers {
        /// Device identifiers:
        internal static let title = L10n.tr("Localizable", "disclosure.spiel.deviceIdentifiers.title", fallback: "Device identifiers:")
      }
      internal enum DeviceSignals {
        /// Device signals:
        internal static let title = L10n.tr("Localizable", "disclosure.spiel.deviceSignals.title", fallback: "Device signals:")
      }
      internal enum InstalledApplications {
        /// Installed applications
        internal static let title = L10n.tr("Localizable", "disclosure.spiel.installedApplications.title", fallback: "Installed applications")
      }
      internal enum KeyHighlights {
        /// Prominent Disclosure
        internal static let title = L10n.tr("Localizable", "disclosure.spiel.keyHighlights.title", fallback: "Key Highlights")
      }
      internal enum ShareInformation {
        /// Who we share your information with
        internal static let title = L10n.tr("Localizable", "disclosure.spiel.shareInformation.title", fallback: "Who we share your information with")
      }
    }
  }
  internal enum Document {
    internal enum Capture {
      /// Take a photo of the document
      internal static let bottomSpiel = L10n.tr("Localizable", "document.capture.bottomSpiel", fallback: "Take a photo of the document")
    }
  }
  internal enum Edd {
    internal enum Banks {
      internal enum Others {
        /// Provide Bank Name
        internal static let placeholder = L10n.tr("Localizable", "edd.banks.others.placeholder", fallback: "Provide Bank Name")
      }
    }
    internal enum Field {
      /// This field is required.
      internal static let `required` = L10n.tr("Localizable", "edd.field.required", fallback: "This field is required.")
    }
    internal enum FinancialDocs {
      /// Pro Tip: Make sure the photo is clear and all important information is captured in the photo.
      internal static let proTip = L10n.tr("Localizable", "edd.financialDocs.proTip", fallback: "Pro Tip: Make sure the photo is clear and all important information is captured in the photo.")
      internal enum Alert {
        /// This action is irreversible.
        internal static let message = L10n.tr("Localizable", "edd.financialDocs.alert.message", fallback: "This action is irreversible.")
        /// Are you sure you want to delete?
        internal static let title = L10n.tr("Localizable", "edd.financialDocs.alert.title", fallback: "Are you sure you want to delete?")
      }
      internal enum Change {
        internal enum Alert {
          /// Changing your selection will delete all the information you added below. Do you want to continue?
          internal static let message = L10n.tr("Localizable", "edd.financialDocs.change.alert.message", fallback: "Changing your selection will delete all the information you added below. Do you want to continue?")
          /// Change
          internal static let option = L10n.tr("Localizable", "edd.financialDocs.change.alert.option", fallback: "Change")
          /// Are you sure you want to change?
          internal static let title = L10n.tr("Localizable", "edd.financialDocs.change.alert.title", fallback: "Are you sure you want to change?")
        }
      }
      internal enum Not {
        internal enum Applicable {
          /// NOT_APPLICABLE
          internal static let type = L10n.tr("Localizable", "edd.financialDocs.not.applicable.type", fallback: "NOT_APPLICABLE")
        }
      }
      internal enum ProTip {
        /// Pro Tip:
        internal static let bold = L10n.tr("Localizable", "edd.financialDocs.proTip.bold", fallback: "Pro Tip:")
      }
    }
    internal enum Form {
      internal enum Back {
        internal enum Confirmation {
          /// Your progress will be lost
          internal static let message = L10n.tr("Localizable", "edd.form.back.confirmation.message", fallback: "Your progress will be lost")
          /// EDD
          internal static let title = L10n.tr("Localizable", "edd.form.back.confirmation.title", fallback: "Are you sure you want to leave?")
        }
      }
      internal enum Options {
        /// N/A (Not Applicable)
        internal static let notApplicable = L10n.tr("Localizable", "edd.form.options.notApplicable", fallback: "N/A (Not Applicable)")
        /// Yes
        internal static let yes = L10n.tr("Localizable", "edd.form.options.yes", fallback: "Yes")
        internal enum Alert {
          /// Changing your selection will delete all the information you added below. Do you want to continue?
          internal static let message = L10n.tr("Localizable", "edd.form.options.alert.message", fallback: "Changing your selection will delete all the information you added below. Do you want to continue?")
          /// Are you sure you want to change?
          internal static let title = L10n.tr("Localizable", "edd.form.options.alert.title", fallback: "Are you sure you want to change?")
        }
      }
    }
    internal enum IncomeSources {
      internal enum Others {
        /// e.g. Freelance Works
        internal static let placeholder = L10n.tr("Localizable", "edd.incomeSources.others.placeholder", fallback: "e.g. Freelance Works")
      }
    }
    internal enum Related {
      internal enum Companies {
        internal enum Others {
          /// e.g. PayMaya Phillippines Inc.
          internal static let placeholder = L10n.tr("Localizable", "edd.related.companies.others.placeholder", fallback: "e.g. PayMaya Phillippines Inc.")
        }
      }
    }
    internal enum Review {
      internal enum Not {
        internal enum Applicable {
          /// N/A (Not Applicable)
          internal static let spiel = L10n.tr("Localizable", "edd.review.not.applicable.spiel", fallback: "N/A (Not Applicable)")
        }
      }
    }
    internal enum Spiel {
      internal enum Selection {
        /// Other/s, please specify
        internal static let others = L10n.tr("Localizable", "edd.spiel.selection.others", fallback: "Other/s, please specify")
      }
    }
    internal enum Splash {
      /// Hello, %@!
      internal static func header(_ p1: Any) -> String {
        return L10n.tr("Localizable", "edd.splash.header", String(describing: p1), fallback: "Hello, %@!")
      }
      /// Upgrade Application Status
      internal static let title = L10n.tr("Localizable", "edd.splash.title", fallback: "Upgrade Application Status")
      internal enum Alert {
        /// You will need to provide the required additional information before you can access the app again.
        /// 
        /// You will be redirected back to the login screen if you exit this step.
        internal static let message = L10n.tr("Localizable", "edd.splash.alert.message", fallback: "You will need to provide the required additional information before you can access the app again.\n\nYou will be redirected back to the login screen if you exit this step.")
        /// Are you sure you want to do this later?
        internal static let title = L10n.tr("Localizable", "edd.splash.alert.title", fallback: "Are you sure you want to do this later?")
      }
    }
    internal enum Title {
      internal enum Bank {
        /// Bank Information
        internal static let information = L10n.tr("Localizable", "edd.title.bank.information", fallback: "Bank Information")
      }
      internal enum Financial {
        /// Financial Documents
        internal static let documents = L10n.tr("Localizable", "edd.title.financial.documents", fallback: "Financial Documents")
      }
      internal enum Primary {
        internal enum Account {
          /// Primary Account Usage
          internal static let usage = L10n.tr("Localizable", "edd.title.primary.account.usage", fallback: "Primary Account Usage")
        }
      }
      internal enum Related {
        /// Related Companies
        internal static let companies = L10n.tr("Localizable", "edd.title.related.companies", fallback: "Related Companies")
      }
      internal enum Source {
        internal enum Of {
          /// Source of Income
          internal static let income = L10n.tr("Localizable", "edd.title.source.of.income", fallback: "Source of Income")
        }
      }
    }
  }
  internal enum Ekyc {
    internal enum Address {
      /// Address
      internal static let title = L10n.tr("Localizable", "ekyc.address.title", fallback: "Address")
      internal enum Navigation {
        /// Address
        internal static let title = L10n.tr("Localizable", "ekyc.address.navigation.title", fallback: "Address")
      }
    }
    internal enum BirthCity {
      /// Place of Birth (City) is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.birthCity.required", fallback: "Place of Birth (City) is required")
    }
    internal enum BirthCountry {
      /// Country/Region of Birth is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.birthCountry.required", fallback: "Country/Region of Birth is required")
    }
    internal enum City {
      /// City is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.city.required", fallback: "City is required")
    }
    internal enum Country {
      /// Country/Region is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.country.required", fallback: "Country/Region is required")
    }
    internal enum DateOfBirth {
      /// Date of Birth is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.dateOfBirth.required", fallback: "Date of Birth is required")
    }
    internal enum FaceVerification {
      /// Face Verification
      internal static let title = L10n.tr("Localizable", "ekyc.faceVerification.title", fallback: "Face Verification")
    }
    internal enum Field {
      /// This field is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.field.required", fallback: "This field is required")
    }
    internal enum Firstname {
      /// First Name is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.firstname.required", fallback: "First Name is required")
    }
    internal enum Id {
      internal enum Back {
        /// Take a photo of the BACK of the ID
        internal static let spiel = L10n.tr("Localizable", "ekyc.id.back.spiel", fallback: "Take a photo of the BACK of the ID")
      }
      internal enum Cancel {
        /// If you close this screen the image won’t be saved. Are you sure you want to cancel?
        internal static let message = L10n.tr("Localizable", "ekyc.id.cancel.message", fallback: "If you close this screen the image won’t be saved. Are you sure you want to cancel?")
        /// Cancel Photo Capture
        internal static let title = L10n.tr("Localizable", "ekyc.id.cancel.title", fallback: "Cancel Photo Capture")
        /// Yes, Cancel
        internal static let yes = L10n.tr("Localizable", "ekyc.id.cancel.yes", fallback: "Yes, Cancel")
      }
      internal enum Classification {
        internal enum LowConfidence {
          /// To make your upgrade process better, make sure the photo you'll submit matches with the ID you selected in the previous screen.
          internal static let spiel = L10n.tr("Localizable", "ekyc.id.classification.lowConfidence.spiel", fallback: "To make your upgrade process better, make sure the photo you'll submit matches with the ID you selected in the previous screen.")
        }
        internal enum Mismatch {
          /// This photo does not match with your selected ID. Please take a photo of your “%@”.
          internal static func spiel(_ p1: Any) -> String {
            return L10n.tr("Localizable", "ekyc.id.classification.mismatch.spiel", String(describing: p1), fallback: "This photo does not match with your selected ID. Please take a photo of your “%@”.")
          }
        }
      }
      internal enum Clearance {
        /// Take a photo of your NBI Clearance
        internal static let spiel = L10n.tr("Localizable", "ekyc.id.clearance.spiel", fallback: "Take a photo of your NBI Clearance")
      }
      internal enum Front {
        /// Take a photo of the FRONT of the ID
        internal static let spiel = L10n.tr("Localizable", "ekyc.id.front.spiel", fallback: "Take a photo of the FRONT of the ID")
      }
      internal enum MultipleFacesDetected {
        /// It looks like there are multiple faces included in your photo. Tip: Make sure to upload a photo containing your face only for faster upgrade approval.
        internal static let spiel = L10n.tr("Localizable", "ekyc.id.multipleFacesDetected.spiel", fallback: "It looks like there are multiple faces included in your photo. Tip: Make sure to upload a photo containing your face only for faster upgrade approval.")
      }
      internal enum NoFaceDetected {
        /// Sorry, we are unable to detect your face from your photo. Tip: Take a photo in good lighting and avoid glare on the ID.
        internal static let spiel = L10n.tr("Localizable", "ekyc.id.noFaceDetected.spiel", fallback: "Sorry, we are unable to detect your face from your photo. Tip: Take a photo in good lighting and avoid glare on the ID.")
      }
      internal enum Passport {
        /// Take a photo of your Passport
        internal static let spiel = L10n.tr("Localizable", "ekyc.id.passport.spiel", fallback: "Take a photo of your Passport")
      }
      internal enum Protip {
        /// <b>Pro Tip:</b><br><br><ul>Make sure it’s clear and the whole card is seen in the shot. Don’t forget to check its expiry date!</ul><br><ul>Make sure you’re in a well-lit room and place the ID on a plain dark surface.</ul>
        internal static let spiel = L10n.tr("Localizable", "ekyc.id.protip.spiel", fallback: "<b>Pro Tip:</b><br><br><ul>Make sure it’s clear and the whole card is seen in the shot. Don’t forget to check its expiry date!</ul><br><ul>Make sure you’re in a well-lit room and place the ID on a plain dark surface.</ul>")
      }
      internal enum SingleFaceDetected {
        /// Before you submit, make sure your ID image is clear and the information is the same as the information you entered.
        internal static let spiel = L10n.tr("Localizable", "ekyc.id.singleFaceDetected.spiel", fallback: "Before you submit, make sure your ID image is clear and the information is the same as the information you entered.")
      }
    }
    internal enum IdNumber {
      /// ID Number is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.idNumber.required", fallback: "ID Number is required")
    }
    internal enum IdType {
      /// ID Type is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.idType.required", fallback: "ID Type is required")
    }
    internal enum IdVerification {
      /// ID Verification
      internal static let title = L10n.tr("Localizable", "ekyc.idVerification.title", fallback: "ID Verification")
    }
    internal enum Identification {
      /// Identification
      internal static let title = L10n.tr("Localizable", "ekyc.identification.title", fallback: "Identification")
      internal enum Ids {
        /// ID Type
        internal static let title = L10n.tr("Localizable", "ekyc.identification.ids.title", fallback: "ID Type")
      }
      internal enum Navigation {
        /// Identification
        internal static let title = L10n.tr("Localizable", "ekyc.identification.navigation.title", fallback: "Identification")
      }
    }
    internal enum Lastname {
      /// Last Name is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.lastname.required", fallback: "Last Name is required")
    }
    internal enum Liveness {
      internal enum Spiel {
        /// Nod your head up and down.
        internal static let nod = L10n.tr("Localizable", "ekyc.liveness.spiel.nod", fallback: "Nod your head up and down.")
        /// Preparing...
        internal static let preparing = L10n.tr("Localizable", "ekyc.liveness.spiel.preparing", fallback: "Preparing...")
        /// Recording...
        internal static let recording = L10n.tr("Localizable", "ekyc.liveness.spiel.recording", fallback: "Recording...")
        /// Turn your head sideways.
        internal static let sideways = L10n.tr("Localizable", "ekyc.liveness.spiel.sideways", fallback: "Turn your head sideways.")
        internal enum FaceDetection {
          /// It looks like there are multiple faces in your video. Tip: Make sure to upload a video containing your face only for faster upgrade approval.
          internal static let multiple = L10n.tr("Localizable", "ekyc.liveness.spiel.faceDetection.multiple", fallback: "It looks like there are multiple faces in your video. Tip: Make sure to upload a video containing your face only for faster upgrade approval.")
          /// Sorry, we are unable to detect your face from the video. We recommend retaking it for faster upgrade approval.
          internal static let `none` = L10n.tr("Localizable", "ekyc.liveness.spiel.faceDetection.none", fallback: "Sorry, we are unable to detect your face from the video. We recommend retaking it for faster upgrade approval.")
          /// Before you submit, make sure the video is clear and you followed the instructions.
          internal static let single = L10n.tr("Localizable", "ekyc.liveness.spiel.faceDetection.single", fallback: "Before you submit, make sure the video is clear and you followed the instructions.")
        }
      }
    }
    internal enum Locality {
      /// Barangay is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.locality.required", fallback: "Barangay is required")
    }
    internal enum MiddleName {
      internal enum Mandatory {
        /// Middle Name
        internal static let placeholder = L10n.tr("Localizable", "ekyc.middleName.mandatory.placeholder", fallback: "Middle Name")
      }
      internal enum Optional {
        /// Middle Name (Optional)
        internal static let placeholder = L10n.tr("Localizable", "ekyc.middleName.optional.placeholder", fallback: "Middle Name (Optional)")
      }
    }
    internal enum Middlename {
      /// Middle Name is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.middlename.required", fallback: "Middle Name is required")
    }
    internal enum NameOfCompany {
      /// Name of Company/Business Name is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.nameOfCompany.required", fallback: "Name of Company/Business Name is required")
    }
    internal enum Nationality {
      /// Nationality is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.nationality.required", fallback: "Nationality is required")
    }
    internal enum NatureOfWork {
      /// Nature of Work is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.natureOfWork.required", fallback: "Nature of Work is required")
    }
    internal enum Personal {
      /// Personal
      internal static let title = L10n.tr("Localizable", "ekyc.personal.title", fallback: "Personal")
      internal enum Navigation {
        /// Personal Information
        internal static let title = L10n.tr("Localizable", "ekyc.personal.navigation.title", fallback: "Personal Information")
      }
    }
    internal enum PersonalInfo {
      /// Personal Info
      internal static let title = L10n.tr("Localizable", "ekyc.personalInfo.title", fallback: "Personal Info")
    }
    internal enum Review {
      internal enum AlmostDone {
        /// You’re almost done! Before you submit, make sure that the information below is correct.
        internal static let spiel = L10n.tr("Localizable", "ekyc.review.almostDone.spiel", fallback: "You’re almost done! Before you submit, make sure that the information below is correct.")
      }
      internal enum UnderReview {
        /// We’ve got your application, and currently reviewing it. Kindly give us a maximum of 24 hours to review and evaluate your application.
        internal static let spiel = L10n.tr("Localizable", "ekyc.review.underReview.spiel", fallback: "We’ve got your application, and currently reviewing it. Kindly give us a maximum of 24 hours to review and evaluate your application.")
      }
    }
    internal enum SourceOfIncome {
      /// Source of Income is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.sourceOfIncome.required", fallback: "Source of Income is required")
    }
    internal enum Spiel {
      internal enum Required {
        ///  Don't forget to upload a photo of the back of your ID to complete your upgrade.
        internal static let backOfIdPhoto = L10n.tr("Localizable", "ekyc.spiel.required.backOfIdPhoto", fallback: " Don't forget to upload a photo of the back of your ID to complete your upgrade.")
        /// Don't forget to upload a photo of your Birth Certificate to complete your upgrade.
        internal static let birthCertificate = L10n.tr("Localizable", "ekyc.spiel.required.birthCertificate", fallback: "Don't forget to upload a photo of your Birth Certificate to complete your upgrade.")
        /// Don't forget to provide your ID number to complete your upgrade.
        internal static let idNumber = L10n.tr("Localizable", "ekyc.spiel.required.idNumber", fallback: "Don't forget to provide your ID number to complete your upgrade.")
        ///  Don't forget to upload a photo of your ID to complete your upgrade.
        internal static let idPhoto = L10n.tr("Localizable", "ekyc.spiel.required.idPhoto", fallback: " Don't forget to upload a photo of your ID to complete your upgrade.")
        ///  Don't forget to select an ID to submit in order to complete your upgrade.
        internal static let idType = L10n.tr("Localizable", "ekyc.spiel.required.idType", fallback: " Don't forget to select an ID to submit in order to complete your upgrade.")
        /// Don't forget to record and upload a liveness clip to complete your upgrade.
        internal static let livenessClip = L10n.tr("Localizable", "ekyc.spiel.required.livenessClip", fallback: "Don't forget to record and upload a liveness clip to complete your upgrade.")
        /// Don't forget to upload a photo of your Parental Consent to complete your upgrade.
        internal static let parentalConsent = L10n.tr("Localizable", "ekyc.spiel.required.parentalConsent", fallback: "Don't forget to upload a photo of your Parental Consent to complete your upgrade.")
        /// Don't forget to upload a photo of your Parent’s/Guardian’s Primary ID to complete your upgrade.
        internal static let parentId = L10n.tr("Localizable", "ekyc.spiel.required.parentId", fallback: "Don't forget to upload a photo of your Parent’s/Guardian’s Primary ID to complete your upgrade.")
      }
    }
    internal enum State {
      /// State/Province is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.state.required", fallback: "State/Province is required")
    }
    internal enum Submit {
      /// We encountered an issue with your submission. Please resubmit after securing a stable internet connection.
      internal static let error = L10n.tr("Localizable", "ekyc.submit.error", fallback: "We encountered an issue with your submission. Please resubmit after securing a stable internet connection.")
    }
    internal enum Success {
      internal enum Receipt {
        internal enum ValidationCode {
          /// Validation Code Copied
          internal static let copied = L10n.tr("Localizable", "ekyc.success.receipt.validationCode.copied", fallback: "Validation Code Copied")
        }
      }
    }
    internal enum Upgrade {
      /// EKYC
      internal static let title = L10n.tr("Localizable", "ekyc.upgrade.title", fallback: "Upgrade")
    }
    internal enum UpgradeAccount {
      /// Upgrade my account
      internal static let title = L10n.tr("Localizable", "ekyc.upgradeAccount.title", fallback: "Upgrade my account")
    }
    internal enum ZipCode {
      /// Zip Code is required
      internal static let `required` = L10n.tr("Localizable", "ekyc.zipCode.required", fallback: "Zip Code is required")
    }
  }
  internal enum EkycReminders {
    internal enum List {
      /// Be ready with any of their valid IDs from this list.
      internal static let beReady = L10n.tr("Localizable", "ekycReminders.list.beReady", fallback: "Be ready with any of their valid IDs from this list.")
      /// Download and have your parents sign the consent form. Download form here.
      internal static let downloadForm = L10n.tr("Localizable", "ekycReminders.list.downloadForm", fallback: "Download and have your parents sign the consent form. Download form here.")
      /// Prepare your IDs. Check the complete list here.
      internal static let prepareIDs = L10n.tr("Localizable", "ekycReminders.list.prepareIDs", fallback: "Prepare your IDs. Check the complete list here.")
      internal enum BeReady {
        /// list
        internal static let link = L10n.tr("Localizable", "ekycReminders.list.beReady.link", fallback: "list")
      }
      internal enum DownloadForm {
        /// Download form here.
        internal static let link = L10n.tr("Localizable", "ekycReminders.list.downloadForm.link", fallback: "Download form here.")
      }
      internal enum Ids {
        /// Primary
        internal static let primary = L10n.tr("Localizable", "ekycReminders.list.ids.primary", fallback: "Primary")
        /// Secondary
        internal static let secondary = L10n.tr("Localizable", "ekycReminders.list.ids.secondary", fallback: "Secondary")
        /// EKYC Reminders
        internal static let title = L10n.tr("Localizable", "ekycReminders.list.ids.title", fallback: "ID List")
      }
      internal enum PrepareIDs {
        /// Check the complete list here.
        internal static let link = L10n.tr("Localizable", "ekycReminders.list.prepareIDs.link", fallback: "Check the complete list here.")
      }
    }
  }
  internal enum EkycZoloz {
    internal enum AcceptId {
      /// Accepted IDs
      internal static let title = L10n.tr("Localizable", "ekycZoloz.acceptId.title", fallback: "Accepted IDs")
      internal enum IdListHeader {
        /// Recommended Primary IDs
        internal static let recommendedIds = L10n.tr("Localizable", "ekycZoloz.acceptId.idListHeader.recommendedIds", fallback: "Recommended Primary IDs")
      }
    }
    internal enum AccountReview {
      internal enum Sections {
        internal enum AdditionalDocuments {
          /// Additional Requirements
          internal static let title = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.additionalDocuments.title", fallback: "Additional Requirements")
        }
        internal enum Address {
          /// Permanent address
          internal static let permanent = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.address.permanent", fallback: "Permanent address")
          /// Present address
          internal static let present = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.address.present", fallback: "Present address")
          /// Address
          internal static let title = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.address.title", fallback: "Address")
        }
        internal enum Identification {
          /// Expiration date
          internal static let expirationDate = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.identification.expirationDate", fallback: "Expiration date")
          /// ID number
          internal static let idNumber = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.identification.idNumber", fallback: "ID number")
          /// ID photo
          internal static let idPhoto = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.identification.idPhoto", fallback: "ID photo")
          /// Identification
          internal static let title = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.identification.title", fallback: "Identification")
          /// ID type
          internal static let type = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.identification.type", fallback: "ID type")
        }
        internal enum PersonalInformation {
          /// Date of birth
          internal static let dateOfBirth = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.personalInformation.dateOfBirth", fallback: "Date of birth")
          /// Full name
          internal static let fullName = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.personalInformation.fullName", fallback: "Full name")
          /// Nationality
          internal static let nationality = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.personalInformation.nationality", fallback: "Nationality")
          /// Place of birth
          internal static let placeOfBirth = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.personalInformation.placeOfBirth", fallback: "Place of birth")
          /// Personal information
          internal static let title = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.personalInformation.title", fallback: "Personal information")
        }
        internal enum WorkInformation {
          /// Name of company / business
          internal static let nameOfCompany = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.workInformation.nameOfCompany", fallback: "Name of company / business")
          /// Nature of work
          internal static let natureOfWork = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.workInformation.natureOfWork", fallback: "Nature of work")
          /// Source of income
          internal static let sourceOfIncome = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.workInformation.sourceOfIncome", fallback: "Source of income")
          /// Work information
          internal static let title = L10n.tr("Localizable", "ekycZoloz.accountReview.sections.workInformation.title", fallback: "Work information")
        }
      }
      internal enum ValidationCode {
        /// Validation code copied
        internal static let copied = L10n.tr("Localizable", "ekycZoloz.accountReview.validationCode.copied", fallback: "Validation code copied")
        /// Validation code sent
        internal static let sent = L10n.tr("Localizable", "ekycZoloz.accountReview.validationCode.sent", fallback: "Validation code sent")
      }
    }
    internal enum AdditionalDocuments {
      internal enum TakePhoto {
        ///  a photo
        internal static let photo = L10n.tr("Localizable", "ekycZoloz.additionalDocuments.takePhoto.photo", fallback: " a photo")
        /// take
        internal static let take = L10n.tr("Localizable", "ekycZoloz.additionalDocuments.takePhoto.take", fallback: "take")
        /// Tap to 
        internal static let tap = L10n.tr("Localizable", "ekycZoloz.additionalDocuments.takePhoto.tap", fallback: "Tap to ")
      }
    }
    internal enum AllGoods {
      internal enum Alert {
        /// Please make sure everything's OK to keep the approval process buttery smooth.
        internal static let message = L10n.tr("Localizable", "ekycZoloz.allGoods.alert.message", fallback: "Please make sure everything's OK to keep the approval process buttery smooth.")
        /// All good?
        internal static let title = L10n.tr("Localizable", "ekycZoloz.allGoods.alert.title", fallback: "All good?")
        internal enum AdditionalInfo {
          /// You can only have one Maya account at a time. By submitting, you allow us to restrict other accounts in your name.
          internal static let message = L10n.tr("Localizable", "ekycZoloz.allGoods.alert.additionalInfo.message", fallback: "You can only have one Maya account at a time. By submitting, you allow us to restrict other accounts in your name.")
        }
        internal enum CheckAgainButton {
          /// Check again
          internal static let title = L10n.tr("Localizable", "ekycZoloz.allGoods.alert.checkAgainButton.title", fallback: "Check again")
        }
        internal enum ContinueButton {
          /// Continue
          internal static let title = L10n.tr("Localizable", "ekycZoloz.allGoods.alert.continueButton.title", fallback: "Continue")
        }
      }
    }
    internal enum BenefitsAndSteps {
      internal enum DataPrivacy {
        /// To protect your information and safety on Maya, we conduct periodic updates to ensure that your data is up to date. Your personal data will always remain secured and processed with the set guidelines under the Data Privacy Act of 2012. Visit Maya’s Privacy Statement to learn more about how we process your data.
        internal static let description = L10n.tr("Localizable", "ekycZoloz.benefitsAndSteps.dataPrivacy.description", fallback: "To protect your information and safety on Maya, we conduct periodic updates to ensure that your data is up to date. Your personal data will always remain secured and processed with the set guidelines under the Data Privacy Act of 2012. Visit Maya’s Privacy Statement to learn more about how we process your data.")
        /// Privacy Statement
        internal static let highlightPhrase = L10n.tr("Localizable", "ekycZoloz.benefitsAndSteps.dataPrivacy.highlightPhrase", fallback: "Privacy Statement")
        /// Data Privacy
        internal static let title = L10n.tr("Localizable", "ekycZoloz.benefitsAndSteps.dataPrivacy.title", fallback: "Data Privacy")
      }
      internal enum ShowBenefitsToggle {
        /// EKYC-Zoloz
        internal static let colapsedTitle = L10n.tr("Localizable", "ekycZoloz.benefitsAndSteps.showBenefitsToggle.colapsedTitle", fallback: "See all benefits")
        /// Hide benefits
        internal static let expandedTitle = L10n.tr("Localizable", "ekycZoloz.benefitsAndSteps.showBenefitsToggle.expandedTitle", fallback: "Hide benefits")
      }
    }
    internal enum ChangedYourMind {
      internal enum Alert {
        /// This will make you resubmit your ID.
        /// Are you sure you want to go back?
        internal static let message = L10n.tr("Localizable", "ekycZoloz.changedYourMind.alert.message", fallback: "This will make you resubmit your ID.\nAre you sure you want to go back?")
        /// Changed your mind?
        internal static let title = L10n.tr("Localizable", "ekycZoloz.changedYourMind.alert.title", fallback: "Changed your mind?")
        internal enum CheckAgainButton {
          /// Cancel
          internal static let title = L10n.tr("Localizable", "ekycZoloz.changedYourMind.alert.checkAgainButton.title", fallback: "Cancel")
        }
        internal enum SubmitButton {
          /// Confirm
          internal static let title = L10n.tr("Localizable", "ekycZoloz.changedYourMind.alert.submitButton.title", fallback: "Confirm")
        }
      }
    }
    internal enum CompletingPersonalInformation {
      /// Select %@
      internal static func selectorTitle(_ p1: Any) -> String {
        return L10n.tr("Localizable", "ekycZoloz.completingPersonalInformation.selectorTitle", String(describing: p1), fallback: "Select %@")
      }
      internal enum FormValidationErrorMessage {
        /// You need to be at least 7 years old to proceed
        internal static let ageToLow = L10n.tr("Localizable", "ekycZoloz.completingPersonalInformation.formValidationErrorMessage.ageToLow", fallback: "You need to be at least 7 years old to proceed")
        /// Please enter up to %@ characters only
        internal static func charactersLimit(_ p1: Any) -> String {
          return L10n.tr("Localizable", "ekycZoloz.completingPersonalInformation.formValidationErrorMessage.charactersLimit", String(describing: p1), fallback: "Please enter up to %@ characters only")
        }
        /// MM/DD/YYYY
        internal static let dateFormat = L10n.tr("Localizable", "ekycZoloz.completingPersonalInformation.formValidationErrorMessage.dateFormat", fallback: "MM/DD/YYYY")
        /// %@ is required
        internal static func fieldNameIsRequired(_ p1: Any) -> String {
          return L10n.tr("Localizable", "ekycZoloz.completingPersonalInformation.formValidationErrorMessage.fieldNameIsRequired", String(describing: p1), fallback: "%@ is required")
        }
        /// Please use a Valid ID
        internal static let invalidId = L10n.tr("Localizable", "ekycZoloz.completingPersonalInformation.formValidationErrorMessage.invalidId", fallback: "Please use a Valid ID")
        /// Required
        internal static let isRequired = L10n.tr("Localizable", "ekycZoloz.completingPersonalInformation.formValidationErrorMessage.isRequired", fallback: "Required")
        /// You cannot declare a PH province for a non PH country
        internal static let nonPHProvince = L10n.tr("Localizable", "ekycZoloz.completingPersonalInformation.formValidationErrorMessage.nonPHProvince", fallback: "You cannot declare a PH province for a non PH country")
        /// Please enter numbers only
        internal static let numbersOnly = L10n.tr("Localizable", "ekycZoloz.completingPersonalInformation.formValidationErrorMessage.numbersOnly", fallback: "Please enter numbers only")
        /// Character repeated 3 or more times consecutively
        internal static let threeLetters = L10n.tr("Localizable", "ekycZoloz.completingPersonalInformation.formValidationErrorMessage.threeLetters", fallback: "Character repeated 3 or more times consecutively")
      }
      internal enum NameRulesInformationText {
        /// You are only allowed to use your legal name during registration. Learn more
        internal static let description = L10n.tr("Localizable", "ekycZoloz.completingPersonalInformation.nameRulesInformationText.description", fallback: "You are only allowed to use your legal name during registration. Learn more")
        /// Learn more
        internal static let highlighted = L10n.tr("Localizable", "ekycZoloz.completingPersonalInformation.nameRulesInformationText.highlighted", fallback: "Learn more")
        /// ekycZoloz/nameRules/learnMore
        internal static let link = L10n.tr("Localizable", "ekycZoloz.completingPersonalInformation.nameRulesInformationText.link", fallback: "ekycZoloz/nameRules/learnMore")
      }
    }
    internal enum ErrorView {
      /// Got it
      internal static let actionTitle = L10n.tr("Localizable", "ekycZoloz.errorView.actionTitle", fallback: "Got it")
    }
    internal enum FlowProgressBar {
      /// %@/3
      internal static func progress(_ p1: Any) -> String {
        return L10n.tr("Localizable", "ekycZoloz.flowProgressBar.progress", String(describing: p1), fallback: "%@/3")
      }
    }
    internal enum LivenessTips {
      /// We'll turn your camera on for a quick identity check. This will only be used to verify your ID.
      internal static let subtitle = L10n.tr("Localizable", "ekycZoloz.livenessTips.subtitle", fallback: "We'll turn your camera on for a quick identity check. This will only be used to verify your ID.")
      /// Tips for taking a selfie
      internal static let tipListHeader = L10n.tr("Localizable", "ekycZoloz.livenessTips.tipListHeader", fallback: "Tips for taking a selfie")
      /// Take a video selfie
      internal static let title = L10n.tr("Localizable", "ekycZoloz.livenessTips.title", fallback: "Take a video selfie")
    }
    internal enum OtherId {
      /// Other ID
      internal static let title = L10n.tr("Localizable", "ekycZoloz.otherId.title", fallback: "Other ID")
      internal enum Note {
        /// NBI Clearance, ACR-I card, Government office (GOCC), Student ID, TIN ID, Voter’s ID, Senior Citizens card, Police Clearance, PhilHealh ID, GSIS e-card ePhil ID (Printed Phil ID) and OFW e-card (OWWA).
        internal static let message = L10n.tr("Localizable", "ekycZoloz.otherId.note.message", fallback: "NBI Clearance, ACR-I card, Government office (GOCC), Student ID, TIN ID, Voter’s ID, Senior Citizens card, Police Clearance, PhilHealh ID, GSIS e-card ePhil ID (Printed Phil ID) and OFW e-card (OWWA).")
        /// Select “Other ID” if you have the following:
        internal static let title = L10n.tr("Localizable", "ekycZoloz.otherId.note.title", fallback: "Select “Other ID” if you have the following:")
      }
    }
    internal enum PhotoTips {
      /// Your ID helps us confirm your identity.
      internal static let subtitle = L10n.tr("Localizable", "ekycZoloz.photoTips.subtitle", fallback: "Your ID helps us confirm your identity.")
      /// Tips for taking an ID photo
      internal static let tipListHeader = L10n.tr("Localizable", "ekycZoloz.photoTips.tipListHeader", fallback: "Tips for taking an ID photo")
      /// Take an ID photo
      internal static let title = L10n.tr("Localizable", "ekycZoloz.photoTips.title", fallback: "Take an ID photo")
    }
    internal enum SubmitAnId {
      /// Your ID helps us confirm your identity. Please choose a valid ID to submit from the list below:
      internal static let subtitle = L10n.tr("Localizable", "ekycZoloz.submitAnId.subtitle", fallback: "Your ID helps us confirm your identity. Please choose a valid ID to submit from the list below:")
      /// Submit an ID
      internal static let title = L10n.tr("Localizable", "ekycZoloz.submitAnId.title", fallback: "Submit an ID")
      internal enum IdListHeader {
        /// ACCEPTED IDS
        internal static let acceptedIds = L10n.tr("Localizable", "ekycZoloz.submitAnId.idListHeader.acceptedIds", fallback: "ACCEPTED IDS")
        /// Primary IDs
        internal static let primaryIds = L10n.tr("Localizable", "ekycZoloz.submitAnId.idListHeader.primaryIds", fallback: "Primary IDs")
        /// RECOMMENDED PRIMARY ID
        internal static let recommendedIds = L10n.tr("Localizable", "ekycZoloz.submitAnId.idListHeader.recommendedIds", fallback: "RECOMMENDED PRIMARY ID")
        /// Secondary IDs
        internal static let secondaryIds = L10n.tr("Localizable", "ekycZoloz.submitAnId.idListHeader.secondaryIds", fallback: "Secondary IDs")
      }
      internal enum IdListSubHeader {
        /// Two IDs are required
        internal static let secondaryIds = L10n.tr("Localizable", "ekycZoloz.submitAnId.idListSubHeader.secondaryIds", fallback: "Two IDs are required")
      }
      internal enum ShowMoreIds {
        /// Show more IDs
        internal static let collapsedTitle = L10n.tr("Localizable", "ekycZoloz.submitAnId.showMoreIds.collapsedTitle", fallback: "Show more IDs")
        /// Hide other IDs
        internal static let expandedTitle = L10n.tr("Localizable", "ekycZoloz.submitAnId.showMoreIds.expandedTitle", fallback: "Hide other IDs")
      }
    }
    internal enum ZolozError {
      internal enum DeviceUnsupported {
        /// Sorry, it appears that your device is not compatible with our identity verification services.
        internal static let message = L10n.tr("Localizable", "ekycZoloz.zolozError.deviceUnsupported.message", fallback: "Sorry, it appears that your device is not compatible with our identity verification services.")
        /// Device unsupported
        internal static let title = L10n.tr("Localizable", "ekycZoloz.zolozError.deviceUnsupported.title", fallback: "Device unsupported")
      }
      internal enum SystemError {
        /// Back to home
        internal static let action = L10n.tr("Localizable", "ekycZoloz.zolozError.systemError.action", fallback: "Back to home")
        /// Sorry about that! It looks like our systems are busy right now but you can try again later.
        internal static let message = L10n.tr("Localizable", "ekycZoloz.zolozError.systemError.message", fallback: "Sorry about that! It looks like our systems are busy right now but you can try again later.")
        /// Error encountered
        internal static let title = L10n.tr("Localizable", "ekycZoloz.zolozError.systemError.title", fallback: "Error encountered")
      }
      internal enum UpdateRequired {
        /// Update now
        internal static let action = L10n.tr("Localizable", "ekycZoloz.zolozError.updateRequired.action", fallback: "Update now")
        /// Please update your app in order to proceed with your video selfie.
        internal static let message = L10n.tr("Localizable", "ekycZoloz.zolozError.updateRequired.message", fallback: "Please update your app in order to proceed with your video selfie.")
        /// Update required
        internal static let title = L10n.tr("Localizable", "ekycZoloz.zolozError.updateRequired.title", fallback: "Update required")
      }
    }
  }
  internal enum Error {
    internal enum AccountLimits {
      /// Account Limits Error
      internal static let title = L10n.tr("Localizable", "error.accountLimits.title", fallback: "Account Limits Error")
    }
    internal enum Activate {
      internal enum Card {
        /// Activate Virtual Card Error
        internal static let title = L10n.tr("Localizable", "error.activate.card.title", fallback: "Activate Virtual Card Error")
      }
    }
    internal enum Authenticate {
      /// Authentication Error
      internal static let title = L10n.tr("Localizable", "error.authenticate.title", fallback: "Authentication Error")
    }
    internal enum Cardrenewal {
      /// Card Renewal Error
      internal static let title = L10n.tr("Localizable", "error.cardrenewal.title", fallback: "Card Renewal Error")
    }
    internal enum Cashin {
      /// Cash In Error
      internal static let title = L10n.tr("Localizable", "error.cashin.title", fallback: "Cash In Error")
    }
    internal enum Change {
      internal enum Password {
        /// Change Password Error
        internal static let title = L10n.tr("Localizable", "error.change.password.title", fallback: "Change Password Error")
      }
    }
    internal enum Code {
      internal enum Generation {
        /// Code Generation Error
        internal static let title = L10n.tr("Localizable", "error.code.generation.title", fallback: "Code Generation Error")
      }
    }
    internal enum Database {
      /// Error in writing to database.
      internal static let saving = L10n.tr("Localizable", "error.database.saving", fallback: "Error in writing to database.")
    }
    internal enum Did {
      internal enum Not {
        internal enum Load {
          /// That didn't load right.
          internal static let title = L10n.tr("Localizable", "error.did.not.load.title", fallback: "That didn't load right.")
        }
      }
    }
    internal enum FaceVerification {
      /// Face Verification Error
      internal static let title = L10n.tr("Localizable", "error.faceVerification.title", fallback: "Face Verification Error")
    }
    internal enum Invalid {
      /// Cash in amount should be from %@ up to %@ only.
      internal static func amount(_ p1: Any, _ p2: Any) -> String {
        return L10n.tr("Localizable", "error.invalid.amount", String(describing: p1), String(describing: p2), fallback: "Cash in amount should be from %@ up to %@ only.")
      }
    }
    internal enum InviteCode {
      /// Invite Code Error
      internal static let title = L10n.tr("Localizable", "error.inviteCode.title", fallback: "Invite Code Error")
    }
    internal enum Join {
      internal enum Maya {
        internal enum Credit {
          /// Join Maya Easy Credit Error
          internal static let title = L10n.tr("Localizable", "error.join.maya.credit.title", fallback: "Join Maya Easy Credit Error")
        }
      }
    }
    internal enum Login {
      /// Login Error
      internal static let title = L10n.tr("Localizable", "error.login.title", fallback: "Login Error")
    }
    internal enum Maya {
      internal enum Credit {
        /// Maya Easy Credit Error
        internal static let title = L10n.tr("Localizable", "error.maya.credit.title", fallback: "Maya Easy Credit Error")
        internal enum Close {
          internal enum Button {
            /// Try again
            internal static let title = L10n.tr("Localizable", "error.maya.credit.close.button.title", fallback: "Try again")
          }
        }
        internal enum Optin {
          /// Join Maya Easy Credit Error
          internal static let title = L10n.tr("Localizable", "error.maya.credit.optin.title", fallback: "Join Maya Easy Credit Error")
        }
      }
    }
    internal enum Merchant {
      internal enum Payment {
        /// Merchant Payment Error
        internal static let title = L10n.tr("Localizable", "error.merchant.payment.title", fallback: "Merchant Payment Error")
      }
    }
    internal enum Missions {
      /// We did not get a response from the server. 
      /// Please try again in a bit.
      internal static let message = L10n.tr("Localizable", "error.missions.message", fallback: "We did not get a response from the server. \nPlease try again in a bit.")
      /// Missions Error
      internal static let title = L10n.tr("Localizable", "error.missions.title", fallback: "Missions Error")
    }
    internal enum New {
      internal enum Maya {
        internal enum Credit {
          /// That didn't work
          internal static let title = L10n.tr("Localizable", "error.new.maya.credit.title", fallback: "That didn't work")
        }
      }
    }
    internal enum Password {
      /// Error Modal Title
      internal static let title = L10n.tr("Localizable", "error.password.title", fallback: "Password")
    }
    internal enum Paybills {
      /// Pay Bills Error
      internal static let title = L10n.tr("Localizable", "error.paybills.title", fallback: "Pay Bills Error")
    }
    internal enum Privacy {
      /// Data Privacy Error
      internal static let title = L10n.tr("Localizable", "error.privacy.title", fallback: "Data Privacy Error")
    }
    internal enum PurchaseHistory {
      /// Purchase History
      internal static let title = L10n.tr("Localizable", "error.purchaseHistory.title", fallback: "Purchase History")
    }
    internal enum Receipt {
      /// Receipt Error
      internal static let title = L10n.tr("Localizable", "error.receipt.title", fallback: "Receipt Error")
    }
    internal enum Registration {
      /// Registration Error
      internal static let title = L10n.tr("Localizable", "error.registration.title", fallback: "Registration Error")
    }
    internal enum Request {
      internal enum Money {
        /// Request Money Error
        internal static let title = L10n.tr("Localizable", "error.request.money.title", fallback: "Request Money Error")
      }
    }
    internal enum ResendReferenceId {
      /// Resend Reference ID Error
      internal static let title = L10n.tr("Localizable", "error.resendReferenceId.title", fallback: "Resend Reference ID Error")
    }
    internal enum Sendmoney {
      /// Send Money Error
      internal static let title = L10n.tr("Localizable", "error.sendmoney.title", fallback: "Send Money Error")
      internal enum Bank {
        /// Bank Transfer Error
        internal static let title = L10n.tr("Localizable", "error.sendmoney.bank.title", fallback: "Bank Transfer Error")
      }
    }
    internal enum Set {
      internal enum Public {
        internal enum Name {
          /// Set Public Name Error
          internal static let title = L10n.tr("Localizable", "error.set.public.name.title", fallback: "Set Public Name Error")
        }
      }
    }
    internal enum Settings {
      internal enum Biometrics {
        /// Change Settings Error
        internal static let title = L10n.tr("Localizable", "error.settings.biometrics.title", fallback: "Change Settings Error")
      }
    }
    internal enum Shield {
      internal enum Finger {
        internal enum Printing {
          /// We couldn't log you in.
          internal static let title = L10n.tr("Localizable", "error.shield.finger.printing.title", fallback: "We couldn't log you in.")
        }
      }
    }
    internal enum Shop {
      /// Shop Error
      internal static let title = L10n.tr("Localizable", "error.shop.title", fallback: "Shop Error")
      internal enum Timeout {
        internal enum Action {
          /// Go back to home
          internal static let title = L10n.tr("Localizable", "error.shop.timeout.action.title", fallback: "Go back to home")
        }
      }
    }
    internal enum Spiel {
      /// Please re-enter your password. It must be 8 characters long with at least one uppercase, one lowercase, one number, and has no spaces.
      internal static let password = L10n.tr("Localizable", "error.spiel.password", fallback: "Please re-enter your password. It must be 8 characters long with at least one uppercase, one lowercase, one number, and has no spaces.")
      /// PayMaya is taking too long to respond to your request, please try again later.
      internal static let timeout = L10n.tr("Localizable", "error.spiel.timeout", fallback: "PayMaya is taking too long to respond to your request, please try again later.")
      internal enum Activation {
        /// Don't forget to fill in your address! Your details must be complete to activate your card.
        internal static let address = L10n.tr("Localizable", "error.spiel.activation.address", fallback: "Don't forget to fill in your address! Your details must be complete to activate your card.")
        /// Don't forget to fill in your barangay! Your details must be complete to activate your card.
        internal static let barangay = L10n.tr("Localizable", "error.spiel.activation.barangay", fallback: "Don't forget to fill in your barangay! Your details must be complete to activate your card.")
        /// Error Field Validation Spiels
        internal static let birthdate = L10n.tr("Localizable", "error.spiel.activation.birthdate", fallback: "Don't forget to fill in your birthday! Your details must be complete to activate your card.")
        /// Don't forget to fill in your city address! Your details must be complete to activate your card.
        internal static let city = L10n.tr("Localizable", "error.spiel.activation.city", fallback: "Don't forget to fill in your city address! Your details must be complete to activate your card.")
        /// Don't forget to fill in your state/province! Your details must be complete to activate your card.
        internal static let state = L10n.tr("Localizable", "error.spiel.activation.state", fallback: "Don't forget to fill in your state/province! Your details must be complete to activate your card.")
        /// Don't forget to fill in your zip code! Your details must be complete to activate your card.
        internal static let zipcode = L10n.tr("Localizable", "error.spiel.activation.zipcode", fallback: "Don't forget to fill in your zip code! Your details must be complete to activate your card.")
      }
      internal enum Amount {
        /// Insufficient funds in your PayMaya account
        internal static let insufficient = L10n.tr("Localizable", "error.spiel.amount.insufficient", fallback: "Insufficient funds in your PayMaya account")
      }
      internal enum Confirmation {
        internal enum Its {
          internal enum Us {
            ///  It's not you, it's us! We're experiencing an internal service issue. Please try again later.  
            internal static let message = L10n.tr("Localizable", "error.spiel.confirmation.its.us.message", fallback: " It's not you, it's us! We're experiencing an internal service issue. Please try again later.  ")
          }
        }
      }
      internal enum Email {
        /// Email addresses do not match.
        internal static let notMatch = L10n.tr("Localizable", "error.spiel.email.notMatch", fallback: "Email addresses do not match.")
      }
      internal enum FaceVerification {
        /// Please follow the on-screen instructions to complete your Face Verification.
        internal static let message = L10n.tr("Localizable", "error.spiel.faceVerification.message", fallback: "Please follow the on-screen instructions to complete your Face Verification.")
      }
      internal enum Invalid {
        /// The link is currently unavailable.
        internal static let link = L10n.tr("Localizable", "error.spiel.invalid.link", fallback: "The link is currently unavailable.")
        /// Please enter a valid 16-Digit account number.
        internal static let smartPadalaNumber = L10n.tr("Localizable", "error.spiel.invalid.smartPadalaNumber", fallback: "Please enter a valid 16-Digit account number.")
        internal enum Maya {
          /// You might have entered the wrong 10-digit  MTCN
          internal static let mtcn = L10n.tr("Localizable", "error.spiel.invalid.maya.mtcn", fallback: "You might have entered the wrong 10-digit  MTCN")
        }
      }
      internal enum InviteCode {
        /// Invite code is required.
        /// Please try again.
        internal static let `required` = L10n.tr("Localizable", "error.spiel.inviteCode.required", fallback: "Invite code is required.\nPlease try again.")
      }
      internal enum Login {
        /// Forgot your password?
        internal static let forgotPassword = L10n.tr("Localizable", "error.spiel.login.forgotPassword", fallback: "Forgot your password?")
        /// Reset
        internal static let reset = L10n.tr("Localizable", "error.spiel.login.reset", fallback: "Reset")
      }
      internal enum New {
        internal enum Password {
          /// Please re-enter your password. It must be 8 characters long and has no spaces.
          internal static let validation = L10n.tr("Localizable", "error.spiel.new.password.validation", fallback: "Please re-enter your password. It must be 8 characters long and has no spaces.")
        }
      }
      internal enum Otp {
        /// OTP Code is required.
        /// Please try again.
        internal static let `required` = L10n.tr("Localizable", "error.spiel.otp.required", fallback: "OTP Code is required.\nPlease try again.")
      }
      internal enum Retyped {
        internal enum Address {
          /// Re-typed Recovery Email Address is required.
          /// Please try again.
          internal static let `required` = L10n.tr("Localizable", "error.spiel.retyped.address.required", fallback: "Re-typed Recovery Email Address is required.\nPlease try again.")
        }
      }
      internal enum Server {
        internal enum Shop {
          internal enum Home {
            internal enum BuyAgain {
              /// We cannot load your recent purchase at the moment.
              internal static let message = L10n.tr("Localizable", "error.spiel.server.shop.home.buyAgain.message", fallback: "We cannot load your recent purchase at the moment.")
            }
          }
        }
      }
      internal enum Shield {
        internal enum FingerPrinting {
          internal enum Special {
            /// Visit our Help Center to get assistance or contact our support hotline.
            internal static let message = L10n.tr("Localizable", "error.spiel.shield.fingerPrinting.special.message", fallback: "Visit our Help Center to get assistance or contact our support hotline.")
          }
          internal enum Standard {
            /// Visit our Help Center below for assistance.
            internal static let message = L10n.tr("Localizable", "error.spiel.shield.fingerPrinting.standard.message", fallback: "Visit our Help Center below for assistance.")
          }
        }
      }
      internal enum Shop {
        internal enum Provider {
          internal enum Not {
            internal enum Found {
              /// This brand does not exist in Shop yet. Please feel free to explore other brands in Shop.
              internal static let message = L10n.tr("Localizable", "error.spiel.shop.provider.not.found.message", fallback: "This brand does not exist in Shop yet. Please feel free to explore other brands in Shop.")
            }
          }
        }
      }
      internal enum Unable {
        internal enum To {
          internal enum Load {
            /// Sorry, we are unable to load the page. Please try again.
            internal static let page = L10n.tr("Localizable", "error.spiel.unable.to.load.page", fallback: "Sorry, we are unable to load the page. Please try again.")
          }
        }
      }
      internal enum Validation {
        /// Error Spiels
        internal static func message(_ p1: Any) -> String {
          return L10n.tr("Localizable", "error.spiel.validation.message", String(describing: p1), fallback: "%@ is required. \nPlease try again.")
        }
      }
      internal enum Voucher {
        internal enum Not {
          internal enum Found {
            /// Voucher is unavailable.
            internal static let message = L10n.tr("Localizable", "error.spiel.voucher.not.found.message", fallback: "Voucher is unavailable.")
          }
        }
      }
    }
    internal enum Transport {
      /// Purchase Error
      internal static let title = L10n.tr("Localizable", "error.transport.title", fallback: "Purchase Error")
    }
    internal enum Upgrade {
      internal enum Account {
        /// PayMaya Upgrade Error
        internal static let title = L10n.tr("Localizable", "error.upgrade.account.title", fallback: "PayMaya Upgrade Error")
      }
      internal enum Camera {
        /// Device has no camera.
        internal static let unavailable = L10n.tr("Localizable", "error.upgrade.camera.unavailable", fallback: "Device has no camera.")
      }
      internal enum Id {
        internal enum Image {
          /// Any Government Issued ID is required.
          /// Please try again.
          internal static let `required` = L10n.tr("Localizable", "error.upgrade.id.image.required", fallback: "Any Government Issued ID is required.\nPlease try again.")
        }
      }
    }
    internal enum Verification {
      /// Verification Code Error
      internal static let title = L10n.tr("Localizable", "error.verification.title", fallback: "Verification Code Error")
      internal enum Code {
        /// Verification Code is required.
        /// Please try again.
        internal static let `required` = L10n.tr("Localizable", "error.verification.code.required", fallback: "Verification Code is required.\nPlease try again.")
      }
    }
    internal enum VoucherClaim {
      /// Voucher Claim Error
      internal static let title = L10n.tr("Localizable", "error.voucherClaim.title", fallback: "Voucher Claim Error")
    }
    internal enum Vouchers {
      /// Vouchers Error
      internal static let title = L10n.tr("Localizable", "error.vouchers.title", fallback: "Vouchers Error")
    }
    internal enum VouchersClaim {
      /// Vouchers Claim Error
      internal static let title = L10n.tr("Localizable", "error.vouchersClaim.title", fallback: "Vouchers Claim Error")
    }
    internal enum Webview {
      /// The page did not load properly. Refresh Now
      internal static let title = L10n.tr("Localizable", "error.webview.title", fallback: "The page did not load properly. Refresh Now")
      internal enum Title {
        /// Refresh Now
        internal static let actionTrigger = L10n.tr("Localizable", "error.webview.title.actionTrigger", fallback: "Refresh Now")
      }
    }
    internal enum WesternUnion {
      /// Western Union Error
      internal static let title = L10n.tr("Localizable", "error.westernUnion.title", fallback: "Western Union Error")
    }
  }
  internal enum Field {
    internal enum Error {
      /// %@ is required
      internal static func `required`(_ p1: Any) -> String {
        return L10n.tr("Localizable", "field.error.required", String(describing: p1), fallback: "%@ is required")
      }
    }
    internal enum Name {
      /// Amount
      internal static let amount = L10n.tr("Localizable", "field.name.amount", fallback: "Amount")
      /// Current Password
      internal static let currentPassword = L10n.tr("Localizable", "field.name.currentPassword", fallback: "Current Password")
      /// CVV
      internal static let cvv = L10n.tr("Localizable", "field.name.cvv", fallback: "CVV")
      /// Email address
      internal static let email = L10n.tr("Localizable", "field.name.email", fallback: "Email address")
      /// First Name
      internal static let firstName = L10n.tr("Localizable", "field.name.firstName", fallback: "First Name")
      /// Last Name
      internal static let lastName = L10n.tr("Localizable", "field.name.lastName", fallback: "Last Name")
      /// Text Field Name
      internal static let mobileNumber = L10n.tr("Localizable", "field.name.mobileNumber", fallback: "Mobile Number")
      /// New Password
      internal static let newPassword = L10n.tr("Localizable", "field.name.newPassword", fallback: "New Password")
      /// Password
      internal static let password = L10n.tr("Localizable", "field.name.password", fallback: "Password")
      /// Valid Thru (MM/YY)
      internal static let validity = L10n.tr("Localizable", "field.name.validity", fallback: "Valid Thru (MM/YY)")
    }
  }
  internal enum Forgot {
    internal enum Password {
      /// Password successfully changed.
      internal static let success = L10n.tr("Localizable", "forgot.password.success", fallback: "Password successfully changed.")
      /// Forgot Password
      internal static let title = L10n.tr("Localizable", "forgot.password.title", fallback: "Forgot Password")
    }
  }
  internal enum ForgotPassword {
    /// Please use the number registered to your Maya account
    internal static let subtitle = L10n.tr("Localizable", "forgotPassword.subtitle", fallback: "Please use the number registered to your Maya account")
    /// Forgot Password Screen
    internal static let title = L10n.tr("Localizable", "forgotPassword.title", fallback: "Enter your number")
    internal enum Alert {
      internal enum Button {
        /// Back to Login
        internal static let backToLogin = L10n.tr("Localizable", "forgotPassword.alert.button.backToLogin", fallback: "Back to Login")
        /// Retry
        internal static let retry = L10n.tr("Localizable", "forgotPassword.alert.button.retry", fallback: "Retry")
      }
    }
    internal enum InlineValidation {
      internal enum Number {
        /// Please enter a valid mobile number
        internal static let invalid = L10n.tr("Localizable", "forgotPassword.inlineValidation.number.invalid", fallback: "Please enter a valid mobile number")
        /// Mobile Number is required
        internal static let `required` = L10n.tr("Localizable", "forgotPassword.inlineValidation.number.required", fallback: "Mobile Number is required")
      }
    }
    internal enum Min {
      internal enum Verification {
        internal enum Error {
          /// You have entered an Incorrect Number. Please try again.
          internal static let description = L10n.tr("Localizable", "forgotPassword.min.verification.error.description", fallback: "You have entered an Incorrect Number. Please try again.")
        }
      }
    }
    internal enum MobileNumber {
      /// 9123456789
      internal static let placeholder = L10n.tr("Localizable", "forgotPassword.mobileNumber.placeholder", fallback: "9123456789")
    }
    internal enum Next {
      internal enum Button {
        /// Next
        internal static let title = L10n.tr("Localizable", "forgotPassword.next.button.title", fallback: "Next")
      }
    }
  }
  internal enum Funds {
    internal enum Interstitial {
      /// Set up your risk profile and get recommendations for funds
      internal static let message1 = L10n.tr("Localizable", "funds.interstitial.message1", fallback: "Set up your risk profile and get recommendations for funds")
      /// Invest in funds for as low as ₱50
      internal static let message2 = L10n.tr("Localizable", "funds.interstitial.message2", fallback: "Invest in funds for as low as ₱50")
      /// Buy orders and receive your earnings through your Maya Wallet
      internal static let message3 = L10n.tr("Localizable", "funds.interstitial.message3", fallback: "Buy orders and receive your earnings through your Maya Wallet")
      /// Funds
      internal static let title = L10n.tr("Localizable", "funds.interstitial.title", fallback: "Start investing with Maya Funds")
    }
  }
  internal enum GlobalRemittance {
    internal enum Interstitial {
      /// Conveniently claim your money from around the world using Maya
      internal static let message1 = L10n.tr("Localizable", "globalRemittance.interstitial.message1", fallback: "Conveniently claim your money from around the world using Maya")
      /// Select from a range of partners like Western Union and more
      internal static let message2 = L10n.tr("Localizable", "globalRemittance.interstitial.message2", fallback: "Select from a range of partners like Western Union and more")
      /// Receive the money in your Maya Wallet
      internal static let message3 = L10n.tr("Localizable", "globalRemittance.interstitial.message3", fallback: "Receive the money in your Maya Wallet")
      /// Global Remittance
      internal static let title = L10n.tr("Localizable", "globalRemittance.interstitial.title", fallback: "Global remittance at your fingertips")
    }
  }
  internal enum GlobalStocks {
    internal enum Interstitial {
      /// Invest in the world’s largest companies like Google, Facebook, Tesla
      internal static let message1 = L10n.tr("Localizable", "globalStocks.interstitial.message1", fallback: "Invest in the world’s largest companies like Google, Facebook, Tesla")
      /// Trade global stocks seamlessly with Maya's premier trading providers
      internal static let message2 = L10n.tr("Localizable", "globalStocks.interstitial.message2", fallback: "Trade global stocks seamlessly with Maya's premier trading providers")
      /// Use your Maya Wallet to fund your USD wallet with each partner
      internal static let message3 = L10n.tr("Localizable", "globalStocks.interstitial.message3", fallback: "Use your Maya Wallet to fund your USD wallet with each partner")
      /// Global Stocks
      internal static let title = L10n.tr("Localizable", "globalStocks.interstitial.title", fallback: "Unlock the world of global stock trading")
    }
  }
  internal enum Import {
    internal enum Detection {
      internal enum Failed {
        /// Import Qr
        internal static let reason = L10n.tr("Localizable", "import.detection.failed.reason", fallback: "No qr detected")
      }
    }
  }
  internal enum Inbox {
    /// Inbox
    internal static let all = L10n.tr("Localizable", "inbox.all", fallback: "All")
    /// Earlier
    internal static let earlier = L10n.tr("Localizable", "inbox.earlier", fallback: "Earlier")
    /// Promos
    internal static let promos = L10n.tr("Localizable", "inbox.promos", fallback: "Promos")
    /// Recent
    internal static let recent = L10n.tr("Localizable", "inbox.recent", fallback: "Recent")
    /// Updates
    internal static let updates = L10n.tr("Localizable", "inbox.updates", fallback: "Updates")
    internal enum EmptyView {
      /// All of your %@ will be shown here.
      internal static func message(_ p1: Any) -> String {
        return L10n.tr("Localizable", "inbox.emptyView.message", String(describing: p1), fallback: "All of your %@ will be shown here.")
      }
      /// You currently have no %@
      internal static func title(_ p1: Any) -> String {
        return L10n.tr("Localizable", "inbox.emptyView.title", String(describing: p1), fallback: "You currently have no %@")
      }
    }
    internal enum Maintenance {
      /// Inbox is currently under maintenance. Please check again later.
      internal static let message = L10n.tr("Localizable", "inbox.maintenance.message", fallback: "Inbox is currently under maintenance. Please check again later.")
      /// We’ll be back shortly.
      internal static let title = L10n.tr("Localizable", "inbox.maintenance.title", fallback: "We’ll be back shortly.")
    }
    internal enum Option {
      /// Mark all as read
      internal static let markAllAsRead = L10n.tr("Localizable", "inbox.option.markAllAsRead", fallback: "Mark all as read")
    }
    internal enum Paymaya {
      /// PayMaya Deals
      internal static let deals = L10n.tr("Localizable", "inbox.paymaya.deals", fallback: "PayMaya Deals")
    }
    internal enum ResumeChat {
      /// Resume chat
      internal static let button = L10n.tr("Localizable", "inbox.resumeChat.button", fallback: "Resume chat")
    }
  }
  internal enum Instructional {
    internal enum Overlay {
      internal enum Custom {
        /// Hi %@! We added new exciting PayMaya services just for you and you can access all these by tapping on the more icon.
        internal static func message(_ p1: Any) -> String {
          return L10n.tr("Localizable", "instructional.overlay.custom.message", String(describing: p1), fallback: "Hi %@! We added new exciting PayMaya services just for you and you can access all these by tapping on the more icon.")
        }
      }
      internal enum Default {
        /// Hi! We added new exciting PayMaya services just for you and you can access all these by tapping on the more icon.
        internal static let message = L10n.tr("Localizable", "instructional.overlay.default.message", fallback: "Hi! We added new exciting PayMaya services just for you and you can access all these by tapping on the more icon.")
        /// Instructional Overlay Spiels
        internal static let title = L10n.tr("Localizable", "instructional.overlay.default.title", fallback: "What’s New!")
      }
    }
  }
  internal enum Insurance {
    internal enum Interstitial {
      /// Choose among leading insurance providers in the Philippines
      internal static let message1 = L10n.tr("Localizable", "insurance.interstitial.message1", fallback: "Choose among leading insurance providers in the Philippines")
      /// Explore insurance products from travel to health – and more
      internal static let message2 = L10n.tr("Localizable", "insurance.interstitial.message2", fallback: "Explore insurance products from travel to health – and more")
      /// Use your Maya Wallet to pay insurance premiums from each partner
      internal static let message3 = L10n.tr("Localizable", "insurance.interstitial.message3", fallback: "Use your Maya Wallet to pay insurance premiums from each partner")
      /// Insurance
      internal static let title = L10n.tr("Localizable", "insurance.interstitial.title", fallback: "Stay covered in times of need")
    }
  }
  internal enum Introduction {
    internal enum Borrow {
      /// Get cash quick for emergencies or fund your next big life move.​
      internal static let description = L10n.tr("Localizable", "introduction.borrow.description", fallback: "Get cash quick for emergencies or fund your next big life move.​")
      /// Borrow
      internal static let title = L10n.tr("Localizable", "introduction.borrow.title", fallback: "Borrow")
    }
    internal enum Grow {
      /// Explore your money’s potential with crypto. Stocks and funds coming soon!​
      internal static let description = L10n.tr("Localizable", "introduction.grow.description", fallback: "Explore your money’s potential with crypto. Stocks and funds coming soon!​")
      /// Grow
      internal static let title = L10n.tr("Localizable", "introduction.grow.title", fallback: "Grow")
    }
    internal enum Page1 {
      /// We’ve gone beyond payments. Savings, wallet, crypto, credit, and more — welcome to your new all-in-one money app.
      /// 
      /// ​Enjoy this exclusive early access to Maya while we roll out the full experience. Let’s go!
      internal static let description = L10n.tr("Localizable", "introduction.page1.description", fallback: "We’ve gone beyond payments. Savings, wallet, crypto, credit, and more — welcome to your new all-in-one money app.\n\n​Enjoy this exclusive early access to Maya while we roll out the full experience. Let’s go!")
      /// Introduction screen
      internal static let title = L10n.tr("Localizable", "introduction.page1.title", fallback: "PayMaya is \nnow Maya.")
      internal enum Accent {
        /// Maya.
        internal static let text = L10n.tr("Localizable", "introduction.page1.accent.text", fallback: "Maya.")
      }
    }
    internal enum Page2 {
      /// Start a secure savings account and earn some of the highest interest rates out there.
      /// 
      /// Then you can pay your bills, send money, and shop cashless online or in real life. All that and then some.
      internal static let description = L10n.tr("Localizable", "introduction.page2.description", fallback: "Start a secure savings account and earn some of the highest interest rates out there.\n\nThen you can pay your bills, send money, and shop cashless online or in real life. All that and then some.")
      /// Maya is a wallet.
      /// And a bank!
      internal static let title = L10n.tr("Localizable", "introduction.page2.title", fallback: "Maya is a wallet.\nAnd a bank!")
      internal enum Accent {
        /// bank!
        internal static let text = L10n.tr("Localizable", "introduction.page2.accent.text", fallback: "bank!")
      }
    }
    internal enum Page3 {
      /// Dive into crypto. Get credit in a flash. Insure the things that matter. Everything you need to master your money.
      internal static let description = L10n.tr("Localizable", "introduction.page3.description", fallback: "Dive into crypto. Get credit in a flash. Insure the things that matter. Everything you need to master your money.")
      /// Maya puts your money to work.
      /// Fast.
      internal static let title = L10n.tr("Localizable", "introduction.page3.title", fallback: "Maya puts your money to work.\nFast.")
      internal enum Accent {
        /// Fast.
        internal static let text = L10n.tr("Localizable", "introduction.page3.accent.text", fallback: "Fast.")
      }
    }
    internal enum Pay {
      /// Pay your bills, buy load, and scan a QR code to pay, including from other apps!​
      internal static let description = L10n.tr("Localizable", "introduction.pay.description", fallback: "Pay your bills, buy load, and scan a QR code to pay, including from other apps!​")
      /// Pay
      internal static let title = L10n.tr("Localizable", "introduction.pay.title", fallback: "Pay")
    }
    internal enum Save {
      /// Earn high interest daily from PDIC-insured deposits in a BSP-licensed bank.​
      internal static let description = L10n.tr("Localizable", "introduction.save.description", fallback: "Earn high interest daily from PDIC-insured deposits in a BSP-licensed bank.​")
      /// Save
      internal static let title = L10n.tr("Localizable", "introduction.save.title", fallback: "Save")
    }
    internal enum Send {
      /// Forget phone numbers and send money with a @username. Put it on your Maya card, too!​
      internal static let description = L10n.tr("Localizable", "introduction.send.description", fallback: "Forget phone numbers and send money with a @username. Put it on your Maya card, too!​")
      /// Send
      internal static let title = L10n.tr("Localizable", "introduction.send.title", fallback: "Send")
    }
  }
  internal enum Kyc {
    internal enum Address {
      /// Address
      internal static let title = L10n.tr("Localizable", "kyc.address.title", fallback: "Address")
    }
    internal enum Field {
      internal enum Name {
        /// Additional info (optional)
        internal static let additionalInfo = L10n.tr("Localizable", "kyc.field.name.additionalInfo", fallback: "Additional info (optional)")
        /// Barangay
        internal static let barangay = L10n.tr("Localizable", "kyc.field.name.barangay", fallback: "Barangay")
        /// City
        internal static let city = L10n.tr("Localizable", "kyc.field.name.city", fallback: "City")
        /// City of birth
        internal static let cityOfBirth = L10n.tr("Localizable", "kyc.field.name.cityOfBirth", fallback: "City of birth")
        /// Country
        internal static let country = L10n.tr("Localizable", "kyc.field.name.country", fallback: "Country")
        /// Country of birth
        internal static let countryOfBirth = L10n.tr("Localizable", "kyc.field.name.countryOfBirth", fallback: "Country of birth")
        /// Date of Birth
        internal static let dateOfBirth = L10n.tr("Localizable", "kyc.field.name.dateOfBirth", fallback: "Date of Birth")
        /// Expiration date
        internal static let expirationDate = L10n.tr("Localizable", "kyc.field.name.expirationDate", fallback: "Expiration date")
        /// First name
        internal static let firstName = L10n.tr("Localizable", "kyc.field.name.firstName", fallback: "First name")
        /// ID number
        internal static let idNumber = L10n.tr("Localizable", "kyc.field.name.idNumber", fallback: "ID number")
        /// Last name
        internal static let lastName = L10n.tr("Localizable", "kyc.field.name.lastName", fallback: "Last name")
        /// Middle name
        internal static let middleName = L10n.tr("Localizable", "kyc.field.name.middleName", fallback: "Middle name")
        /// Name of company / business
        internal static let nameOfCompanyBusiness = L10n.tr("Localizable", "kyc.field.name.nameOfCompanyBusiness", fallback: "Name of company / business")
        /// Nationality
        internal static let nationality = L10n.tr("Localizable", "kyc.field.name.nationality", fallback: "Nationality")
        /// KYC Text Field Name
        internal static let natureOfWork = L10n.tr("Localizable", "kyc.field.name.natureOfWork", fallback: "Nature of work")
        /// Nature of work - Others
        internal static let natureOfWorkOthers = L10n.tr("Localizable", "kyc.field.name.natureOfWorkOthers", fallback: "Nature of work - Others")
        /// Source of income
        internal static let sourceOfIncome = L10n.tr("Localizable", "kyc.field.name.sourceOfIncome", fallback: "Source of income")
        /// State / Province
        internal static let stateProvince = L10n.tr("Localizable", "kyc.field.name.stateProvince", fallback: "State / Province")
        /// State / Province of birth
        internal static let stateProvinceOfBirth = L10n.tr("Localizable", "kyc.field.name.stateProvinceOfBirth", fallback: "State / Province of birth")
        /// House / Unit Number, Building Name, Street Name
        internal static let streetName = L10n.tr("Localizable", "kyc.field.name.streetName", fallback: "House / Unit Number, Building Name, Street Name")
        /// Zip code
        internal static let zipCode = L10n.tr("Localizable", "kyc.field.name.zipCode", fallback: "Zip code")
      }
    }
    internal enum Id {
      internal enum Card {
        /// Identification Card (Optional)
        internal static let `optional` = L10n.tr("Localizable", "kyc.id.card.optional", fallback: "Identification Card (Optional)")
        /// Identification Card
        internal static let `required` = L10n.tr("Localizable", "kyc.id.card.required", fallback: "Identification Card")
      }
    }
    internal enum Personal {
      /// Personal
      internal static let title = L10n.tr("Localizable", "kyc.personal.title", fallback: "Personal")
    }
    internal enum PersonalInfo {
      internal enum Dropdown {
        /// Select barangay
        internal static let barangayPlaceholder = L10n.tr("Localizable", "kyc.personalInfo.dropdown.barangayPlaceholder", fallback: "Select barangay")
        /// Select city of birth
        internal static let birthCityPlaceholder = L10n.tr("Localizable", "kyc.personalInfo.dropdown.birthCityPlaceholder", fallback: "Select city of birth")
        /// Select country of birth
        internal static let birthCountryPlaceholder = L10n.tr("Localizable", "kyc.personalInfo.dropdown.birthCountryPlaceholder", fallback: "Select country of birth")
        /// Select city
        internal static let cityPlaceholder = L10n.tr("Localizable", "kyc.personalInfo.dropdown.cityPlaceholder", fallback: "Select city")
        /// Select state / province
        internal static let statePlaceholder = L10n.tr("Localizable", "kyc.personalInfo.dropdown.statePlaceholder", fallback: "Select state / province")
        /// Select zip code
        internal static let zipCodePlaceholder = L10n.tr("Localizable", "kyc.personalInfo.dropdown.zipCodePlaceholder", fallback: "Select zip code")
      }
      internal enum ForeignCompliance {
        /// Your 
        internal static let initialString = L10n.tr("Localizable", "kyc.personalInfo.foreignCompliance.initialString", fallback: "Your ")
        ///  needs to be in the 
        internal static let secondaryStringCurrent = L10n.tr("Localizable", "kyc.personalInfo.foreignCompliance.secondaryStringCurrent", fallback: " needs to be in the ")
        ///  needs to be in 
        internal static let secondaryStringPermanent = L10n.tr("Localizable", "kyc.personalInfo.foreignCompliance.secondaryStringPermanent", fallback: " needs to be in ")
        internal enum Initial {
          /// current address
          internal static let boldStringCurrent = L10n.tr("Localizable", "kyc.personalInfo.foreignCompliance.initial.boldStringCurrent", fallback: "current address")
          /// permanent address
          internal static let boldStringPermanent = L10n.tr("Localizable", "kyc.personalInfo.foreignCompliance.initial.boldStringPermanent", fallback: "permanent address")
        }
        internal enum Secondary {
          /// Philippines
          internal static let boldStringCurrent = L10n.tr("Localizable", "kyc.personalInfo.foreignCompliance.secondary.boldStringCurrent", fallback: "Philippines")
          /// another country
          internal static let boldStringPermanent = L10n.tr("Localizable", "kyc.personalInfo.foreignCompliance.secondary.boldStringPermanent", fallback: "another country")
        }
      }
      internal enum FreeText {
        /// Enter barangay
        internal static let barangayPlaceholder = L10n.tr("Localizable", "kyc.personalInfo.freeText.barangayPlaceholder", fallback: "Enter barangay")
        /// Enter city of birth
        internal static let birthCityPlaceholder = L10n.tr("Localizable", "kyc.personalInfo.freeText.birthCityPlaceholder", fallback: "Enter city of birth")
        /// Enter city
        internal static let cityPlaceholder = L10n.tr("Localizable", "kyc.personalInfo.freeText.cityPlaceholder", fallback: "Enter city")
        /// Enter state/province
        internal static let statePlaceholder = L10n.tr("Localizable", "kyc.personalInfo.freeText.statePlaceholder", fallback: "Enter state/province")
        /// Enter zip code
        internal static let zipCodePlaceholder = L10n.tr("Localizable", "kyc.personalInfo.freeText.zipCodePlaceholder", fallback: "Enter zip code")
      }
      internal enum Picker {
        /// Search
        internal static let search = L10n.tr("Localizable", "kyc.personalInfo.picker.search", fallback: "Search")
        internal enum NoResult {
          /// We could not find what you are looking for.
          /// Please try again.
          internal static let description = L10n.tr("Localizable", "kyc.personalInfo.picker.noResult.description", fallback: "We could not find what you are looking for.\nPlease try again.")
          /// No results found
          internal static let title = L10n.tr("Localizable", "kyc.personalInfo.picker.noResult.title", fallback: "No results found")
        }
      }
    }
    internal enum Spiel {
      /// Owner of Business (SME) / Self Employed
      internal static let businessOwner = L10n.tr("Localizable", "kyc.spiel.businessOwner", fallback: "Owner of Business (SME) / Self Employed")
      /// KYC
      internal static let callToCompleteUpgrade = L10n.tr("Localizable", "kyc.spiel.callToCompleteUpgrade", fallback: "Give us a call to complete your upgrade.")
      /// Not Applicable
      internal static let notApplicable = L10n.tr("Localizable", "kyc.spiel.notApplicable", fallback: "Not Applicable")
      /// Resending
      internal static let resending = L10n.tr("Localizable", "kyc.spiel.resending", fallback: "Resending")
      /// Same as Present Address
      internal static let sameAsPresentAddress = L10n.tr("Localizable", "kyc.spiel.sameAsPresentAddress", fallback: "Same as Present Address")
      /// Send a copy of your validation code via SMS
      internal static let sendValidationCode = L10n.tr("Localizable", "kyc.spiel.sendValidationCode", fallback: "Send a copy of your validation code via SMS")
      internal enum Download {
        /// Download form here.
        internal static let form = L10n.tr("Localizable", "kyc.spiel.download.form", fallback: "Download form here.")
      }
      internal enum Id {
        /// Check this list
        internal static let checklist = L10n.tr("Localizable", "kyc.spiel.id.checklist", fallback: "Check this list")
        /// list
        internal static let list = L10n.tr("Localizable", "kyc.spiel.id.list", fallback: "list")
      }
      internal enum KycSuccess {
        /// Validation Code: %@.
        internal static func formattedValidationCode(_ p1: Any) -> String {
          return L10n.tr("Localizable", "kyc.spiel.kycSuccess.formattedValidationCode", String(describing: p1), fallback: "Validation Code: %@.")
        }
        /// To finish your application, start a video call. Here’s your Validation Code: %@.
        internal static func validationCode(_ p1: Any) -> String {
          return L10n.tr("Localizable", "kyc.spiel.kycSuccess.validationCode", String(describing: p1), fallback: "To finish your application, start a video call. Here’s your Validation Code: %@.")
        }
      }
      internal enum No {
        /// It looks like you have slow or no internet connection. Please make sure you're online and try again.
        /// 
        /// Note: Please make sure you call us back. Your upgrade will only be complete after successful video call.
        internal static let internet = L10n.tr("Localizable", "kyc.spiel.no.internet", fallback: "It looks like you have slow or no internet connection. Please make sure you're online and try again.\n\nNote: Please make sure you call us back. Your upgrade will only be complete after successful video call.")
        internal enum Internet {
          /// It looks like you have slow or no internet connection. Please make sure you're online and try again.
          internal static let note = L10n.tr("Localizable", "kyc.spiel.no.internet.note", fallback: "It looks like you have slow or no internet connection. Please make sure you're online and try again.")
          /// Internet Connection Issue
          internal static let title = L10n.tr("Localizable", "kyc.spiel.no.internet.title", fallback: "Internet Connection Issue")
        }
      }
      internal enum Required {
        /// Don't forget to fill in your Barangay! Your details must be complete to complete your upgrade.
        internal static let barangay = L10n.tr("Localizable", "kyc.spiel.required.barangay", fallback: "Don't forget to fill in your Barangay! Your details must be complete to complete your upgrade.")
        /// Don't forget to fill in your Birth Date! Your details must be complete to complete your upgrade.
        internal static let birthdate = L10n.tr("Localizable", "kyc.spiel.required.birthdate", fallback: "Don't forget to fill in your Birth Date! Your details must be complete to complete your upgrade.")
        /// Don't forget to fill in your Place of Birth! Your details must be complete to complete your upgrade.
        internal static let birthPlace = L10n.tr("Localizable", "kyc.spiel.required.birthPlace", fallback: "Don't forget to fill in your Place of Birth! Your details must be complete to complete your upgrade.")
        /// Don't forget to fill in your Business Name! Your details must be complete to complete your upgrade.
        internal static let businessName = L10n.tr("Localizable", "kyc.spiel.required.businessName", fallback: "Don't forget to fill in your Business Name! Your details must be complete to complete your upgrade.")
        /// Don't forget to fill in your City! Your details must be complete to complete your upgrade.
        internal static let city = L10n.tr("Localizable", "kyc.spiel.required.city", fallback: "Don't forget to fill in your City! Your details must be complete to complete your upgrade.")
        /// Don't forget to fill in your First Name! Your details must be complete to complete your upgrade.
        internal static let firstName = L10n.tr("Localizable", "kyc.spiel.required.firstName", fallback: "Don't forget to fill in your First Name! Your details must be complete to complete your upgrade.")
        /// Don't forget to fill in your House No./Building No./Street No./Subdivision! Your details must be complete to complete your upgrade.
        internal static let houseNumber = L10n.tr("Localizable", "kyc.spiel.required.houseNumber", fallback: "Don't forget to fill in your House No./Building No./Street No./Subdivision! Your details must be complete to complete your upgrade.")
        /// Don't forget to fill in your GSIS / SSS / TIN! Your details must be complete to complete your upgrade.
        internal static let idDetails = L10n.tr("Localizable", "kyc.spiel.required.idDetails", fallback: "Don't forget to fill in your GSIS / SSS / TIN! Your details must be complete to complete your upgrade.")
        /// Don't forget to upload your ID photo! Please make sure you upload your ID to complete your upgrade.
        internal static let idPhoto = L10n.tr("Localizable", "kyc.spiel.required.idPhoto", fallback: "Don't forget to upload your ID photo! Please make sure you upload your ID to complete your upgrade.")
        /// Don't forget to fill in your Source of Income! Your details must be complete to complete your upgrade.
        internal static let incomeSource = L10n.tr("Localizable", "kyc.spiel.required.incomeSource", fallback: "Don't forget to fill in your Source of Income! Your details must be complete to complete your upgrade.")
        /// Don't forget to fill in your Last Name! Your details must be complete to complete your upgrade.
        internal static let lastName = L10n.tr("Localizable", "kyc.spiel.required.lastName", fallback: "Don't forget to fill in your Last Name! Your details must be complete to complete your upgrade.")
        /// Don't forget to fill in your Nationality! Your details must be complete to complete your upgrade.
        internal static let nationality = L10n.tr("Localizable", "kyc.spiel.required.nationality", fallback: "Don't forget to fill in your Nationality! Your details must be complete to complete your upgrade.")
        /// Don't forget to fill in your Permanent Address! Your details must be complete to complete your upgrade.
        /// 
        /// If you live permanently in your present address, please tap the "Same as Present Address." If not, update your permanent address.
        internal static let permanentAddress = L10n.tr("Localizable", "kyc.spiel.required.permanentAddress", fallback: "Don't forget to fill in your Permanent Address! Your details must be complete to complete your upgrade.\n\nIf you live permanently in your present address, please tap the \"Same as Present Address.\" If not, update your permanent address.")
        /// Don't forget to fill in your State/Province! Your details must be complete to complete your upgrade.
        internal static let province = L10n.tr("Localizable", "kyc.spiel.required.province", fallback: "Don't forget to fill in your State/Province! Your details must be complete to complete your upgrade.")
        /// Don't forget to fill in your Nature of Work! Your details must be complete to complete your upgrade.
        internal static let workNature = L10n.tr("Localizable", "kyc.spiel.required.workNature", fallback: "Don't forget to fill in your Nature of Work! Your details must be complete to complete your upgrade.")
        /// Don't forget to fill in your Zip Code! Your details must be complete to complete your upgrade.
        internal static let zipcode = L10n.tr("Localizable", "kyc.spiel.required.zipcode", fallback: "Don't forget to fill in your Zip Code! Your details must be complete to complete your upgrade.")
      }
      internal enum ResendReferenceId {
        internal enum Success {
          /// Reference ID Sent!
          internal static let message = L10n.tr("Localizable", "kyc.spiel.resendReferenceId.success.message", fallback: "Reference ID Sent!")
        }
      }
      internal enum Selection {
        /// Other/s, please specify
        internal static let others = L10n.tr("Localizable", "kyc.spiel.selection.others", fallback: "Other/s, please specify")
      }
      internal enum Submit {
        internal enum Success {
          /// Just 1 last step to fully upgrade your account. Visit our online Guide for more information: https://paymaya.com/upgrade/.
          /// Don't forget to bring your Validation Code SMS, and a Valid ID.
          internal static let message = L10n.tr("Localizable", "kyc.spiel.submit.success.message", fallback: "Just 1 last step to fully upgrade your account. Visit our online Guide for more information: https://paymaya.com/upgrade/.\nDon't forget to bring your Validation Code SMS, and a Valid ID.")
          /// Upgrade PayMaya application submitted!
          internal static let title = L10n.tr("Localizable", "kyc.spiel.submit.success.title", fallback: "Upgrade PayMaya application submitted!")
        }
      }
      internal enum Unable {
        /// Uh-oh! Connection to the video call was lost. Please make sure you're online and try again.
        /// 
        /// Note: Please make sure you call us back. Your upgrade will only be complete after successful video call.
        internal static let connect = L10n.tr("Localizable", "kyc.spiel.unable.connect", fallback: "Uh-oh! Connection to the video call was lost. Please make sure you're online and try again.\n\nNote: Please make sure you call us back. Your upgrade will only be complete after successful video call.")
        internal enum Connect {
          /// Unable to Connect
          internal static let title = L10n.tr("Localizable", "kyc.spiel.unable.connect.title", fallback: "Unable to Connect")
        }
      }
      internal enum Unavailable {
        /// It's a busy time, all our officers are helping other customers right now. Please try again soon.
        /// 
        /// Note: Please make sure you call us back. Your upgrade will only be complete after successful video call.
        internal static let officers = L10n.tr("Localizable", "kyc.spiel.unavailable.officers", fallback: "It's a busy time, all our officers are helping other customers right now. Please try again soon.\n\nNote: Please make sure you call us back. Your upgrade will only be complete after successful video call.")
        internal enum Officers {
          /// Upgrade Officers Unavailable
          internal static let title = L10n.tr("Localizable", "kyc.spiel.unavailable.officers.title", fallback: "Upgrade Officers Unavailable")
        }
      }
      internal enum VideoCall {
        /// Here’s your validation code:
        /// %@
        internal static func validationCode(_ p1: Any) -> String {
          return L10n.tr("Localizable", "kyc.spiel.videoCall.validationCode", String(describing: p1), fallback: "Here’s your validation code:\n%@")
        }
        internal enum Call {
          /// Call later
          internal static let later = L10n.tr("Localizable", "kyc.spiel.videoCall.call.later", fallback: "Call later")
          internal enum Later {
            /// When you’re ready, just tap More > Check My Upgrade Status
            /// 
            /// %@
            /// 
            /// NOTE: Your upgrade application will only be completed after the video call. Approval confirmation to follow.
            internal static func ekyc(_ p1: Any) -> String {
              return L10n.tr("Localizable", "kyc.spiel.videoCall.call.later.ekyc", String(describing: p1), fallback: "When you’re ready, just tap More > Check My Upgrade Status\n\n%@\n\nNOTE: Your upgrade application will only be completed after the video call. Approval confirmation to follow.")
            }
            /// Complete upgrade later?
            internal static let title = L10n.tr("Localizable", "kyc.spiel.videoCall.call.later.title", fallback: "Complete upgrade later?")
          }
        }
      }
      internal enum VideoCallEnabled {
        internal enum Success {
          /// To finish your application, start a video call. Here’s your Validation Code: %@.
          internal static func message(_ p1: Any) -> String {
            return L10n.tr("Localizable", "kyc.spiel.videoCallEnabled.success.message", String(describing: p1), fallback: "To finish your application, start a video call. Here’s your Validation Code: %@.")
          }
        }
      }
    }
    internal enum Title {
      internal enum Valid {
        /// List of Valid IDs
        internal static let ids = L10n.tr("Localizable", "kyc.title.valid.ids", fallback: "List of Valid IDs")
      }
    }
    internal enum Videocall {
      internal enum Analytics {
        internal enum Reason {
          /// Agen Disconnected
          internal static let agenDisconnected = L10n.tr("Localizable", "kyc.videocall.analytics.reason.agenDisconnected", fallback: "Agen Disconnected")
          /// Application Terminated
          internal static let applicationTerminated = L10n.tr("Localizable", "kyc.videocall.analytics.reason.applicationTerminated", fallback: "Application Terminated")
          /// Application Backgrounded
          internal static let backgrounded = L10n.tr("Localizable", "kyc.videocall.analytics.reason.backgrounded", fallback: "Application Backgrounded")
          /// Insufficient Network
          internal static let insufficientNetwork = L10n.tr("Localizable", "kyc.videocall.analytics.reason.insufficientNetwork", fallback: "Insufficient Network")
          /// Invalid Token
          internal static let invalidToken = L10n.tr("Localizable", "kyc.videocall.analytics.reason.invalidToken", fallback: "Invalid Token")
          /// Network Disconnection
          internal static let networkDisconnection = L10n.tr("Localizable", "kyc.videocall.analytics.reason.networkDisconnection", fallback: "Network Disconnection")
          /// Timeout
          internal static let timeout = L10n.tr("Localizable", "kyc.videocall.analytics.reason.timeout", fallback: "Timeout")
          /// Unknown Error
          internal static let unknown = L10n.tr("Localizable", "kyc.videocall.analytics.reason.unknown", fallback: "Unknown Error")
          /// User Disconnected
          internal static let userDisconnected = L10n.tr("Localizable", "kyc.videocall.analytics.reason.userDisconnected", fallback: "User Disconnected")
        }
      }
    }
    internal enum Work {
      /// Work and ID
      internal static let title = L10n.tr("Localizable", "kyc.work.title", fallback: "Work and ID")
    }
  }
  internal enum Licenses {
    /// PayMaya uses the following open source libraries:
    internal static let subtitle = L10n.tr("Localizable", "licenses.subtitle", fallback: "PayMaya uses the following open source libraries:")
    /// Licenses screen
    internal static let title = L10n.tr("Localizable", "licenses.title", fallback: "Libraries We Use")
  }
  internal enum Loans {
    internal enum Icl {
      internal enum Dbl {
        internal enum Upload {
          /// Loans
          internal static let header = L10n.tr("Localizable", "loans.icl.dbl.upload.header", fallback: "Add proof of income")
          /// Uploading your proof of income helps us give you better lending offers
          internal static let subheader = L10n.tr("Localizable", "loans.icl.dbl.upload.subheader", fallback: "Uploading your proof of income helps us give you better lending offers")
        }
      }
    }
  }
  internal enum Location {
    internal enum Permission {
      internal enum Bottomsheet {
        /// Continue
        internal static let `continue` = L10n.tr("Localizable", "location.permission.bottomsheet.continue", fallback: "Continue")
        /// Maya only uses your info to spot and prevent transactions that originate from unverified or potentially suspicious locations.
        internal static let message = L10n.tr("Localizable", "location.permission.bottomsheet.message", fallback: "Maya only uses your info to spot and prevent transactions that originate from unverified or potentially suspicious locations.")
        /// Location Permission
        internal static let title = L10n.tr("Localizable", "location.permission.bottomsheet.title", fallback: "Protect your account by sharing your location")
      }
    }
  }
  internal enum LuckyGames {
    internal enum Interstitial {
      /// Choose a PAGCOR-authorized game of your choice to be redirected to a third-party platform
      internal static let message1 = L10n.tr("Localizable", "luckyGames.interstitial.message1", fallback: "Choose a PAGCOR-authorized game of your choice to be redirected to a third-party platform")
      /// Available to upgraded Maya users who are at least 21 years old and are not government employees
      internal static let message2 = L10n.tr("Localizable", "luckyGames.interstitial.message2", fallback: "Available to upgraded Maya users who are at least 21 years old and are not government employees")
      /// Maya is responsible for cash in and cash out services only. We are not liable for any losses you may experience in your transactions.
      internal static let message3 = L10n.tr("Localizable", "luckyGames.interstitial.message3", fallback: "Maya is responsible for cash in and cash out services only. We are not liable for any losses you may experience in your transactions.")
      /// By continuing, you fully understand the Terms and Conditions on the use of this service, accept all the risks involved, and acknowledge that profits and returns are not guaranteed.
      internal static let termsAndConditions = L10n.tr("Localizable", "luckyGames.interstitial.termsAndConditions", fallback: "By continuing, you fully understand the Terms and Conditions on the use of this service, accept all the risks involved, and acknowledge that profits and returns are not guaranteed.")
      /// Lucky Games
      internal static let title = L10n.tr("Localizable", "luckyGames.interstitial.title", fallback: "Lucky games await!")
      internal enum Banner {
        /// Available to upgraded Maya users who are at least 21 years old and are not government employees
        internal static let message1 = L10n.tr("Localizable", "luckyGames.interstitial.banner.message1", fallback: "Available to upgraded Maya users who are at least 21 years old and are not government employees")
        /// All games are PAGCOR-authorized and will redirect you to a third-party platform
        internal static let message2 = L10n.tr("Localizable", "luckyGames.interstitial.banner.message2", fallback: "All games are PAGCOR-authorized and will redirect you to a third-party platform")
        /// Maya is responsible for cash in and cash out services only. We are not liable for any losses you may experience in your transactions.
        internal static let message3 = L10n.tr("Localizable", "luckyGames.interstitial.banner.message3", fallback: "Maya is responsible for cash in and cash out services only. We are not liable for any losses you may experience in your transactions.")
        /// By continuing, you fully understand the Terms and Conditions on the use of this service, accept all the risks involved, and acknowledge that profits and returns are not guaranteed
        internal static let termsAndConditions = L10n.tr("Localizable", "luckyGames.interstitial.banner.termsAndConditions", fallback: "By continuing, you fully understand the Terms and Conditions on the use of this service, accept all the risks involved, and acknowledge that profits and returns are not guaranteed")
        /// Before you start
        internal static let title = L10n.tr("Localizable", "luckyGames.interstitial.banner.title", fallback: "Before you start")
      }
      internal enum TermsAndConditions {
        /// Terms and Conditions
        internal static let link = L10n.tr("Localizable", "luckyGames.interstitial.termsAndConditions.link", fallback: "Terms and Conditions")
      }
    }
  }
  internal enum Maya {
    internal enum AccountLimits {
      /// %@ used (max: %@)
      internal static func amountRange(_ p1: Any, _ p2: Any) -> String {
        return L10n.tr("Localizable", "maya.accountLimits.amountRange", String(describing: p1), String(describing: p2), fallback: "%@ used (max: %@)")
      }
      /// %@ remaining
      internal static func amountRemaining(_ p1: Any) -> String {
        return L10n.tr("Localizable", "maya.accountLimits.amountRemaining", String(describing: p1), fallback: "%@ remaining")
      }
      /// Limits will reset tomorrow. Learn more
      internal static let learnMore = L10n.tr("Localizable", "maya.accountLimits.learnMore", fallback: "Limits will reset tomorrow. Learn more")
      internal enum CountLimit {
        /// %@ transactions left (max: %@)
        internal static func plural(_ p1: Any, _ p2: Any) -> String {
          return L10n.tr("Localizable", "maya.accountLimits.countLimit.plural", String(describing: p1), String(describing: p2), fallback: "%@ transactions left (max: %@)")
        }
        /// %@ transaction left (max: %@)
        internal static func singular(_ p1: Any, _ p2: Any) -> String {
          return L10n.tr("Localizable", "maya.accountLimits.countLimit.singular", String(describing: p1), String(describing: p2), fallback: "%@ transaction left (max: %@)")
        }
      }
      internal enum LearnMore {
        /// Learn more
        internal static let link = L10n.tr("Localizable", "maya.accountLimits.learnMore.link", fallback: "Learn more")
        /// Limits will reset on the first day next month. Learn more
        internal static let monthly = L10n.tr("Localizable", "maya.accountLimits.learnMore.monthly", fallback: "Limits will reset on the first day next month. Learn more")
      }
      internal enum Segments {
        /// Maya Account Limits
        internal static let daily = L10n.tr("Localizable", "maya.accountLimits.segments.daily", fallback: "Daily")
        /// Monthly
        internal static let monthly = L10n.tr("Localizable", "maya.accountLimits.segments.monthly", fallback: "Monthly")
      }
    }
    internal enum Activity {
      internal enum Detail {
        /// Date
        internal static let date = L10n.tr("Localizable", "maya.activity.detail.date", fallback: "Date")
        /// Product name
        internal static let item = L10n.tr("Localizable", "maya.activity.detail.item", fallback: "Product name")
        internal enum Account {
          /// Account name
          internal static let name = L10n.tr("Localizable", "maya.activity.detail.account.name", fallback: "Account name")
          /// Account number
          internal static let number = L10n.tr("Localizable", "maya.activity.detail.account.number", fallback: "Account number")
          /// Account type
          internal static let type = L10n.tr("Localizable", "maya.activity.detail.account.type", fallback: "Account type")
        }
        internal enum Amount {
          /// Amount - Approved
          internal static let approved = L10n.tr("Localizable", "maya.activity.detail.amount.approved", fallback: "Amount - Approved")
          /// Amount - Cancelled
          internal static let cancelled = L10n.tr("Localizable", "maya.activity.detail.amount.cancelled", fallback: "Amount - Cancelled")
        }
        internal enum Bank {
          /// Bank name
          internal static let name = L10n.tr("Localizable", "maya.activity.detail.bank.name", fallback: "Bank name")
        }
        internal enum Fund {
          /// Fund source
          internal static let source = L10n.tr("Localizable", "maya.activity.detail.fund.source", fallback: "Fund source")
        }
        internal enum Invoice {
          /// Invoice number
          internal static let number = L10n.tr("Localizable", "maya.activity.detail.invoice.number", fallback: "Invoice number")
        }
        internal enum Payment {
          /// Payment reference
          internal static let reference = L10n.tr("Localizable", "maya.activity.detail.payment.reference", fallback: "Payment reference")
        }
        internal enum Purchase {
          /// Purchase date
          internal static let date = L10n.tr("Localizable", "maya.activity.detail.purchase.date", fallback: "Purchase date")
        }
        internal enum Share {
          internal enum P2p {
            /// Sent %@ from my Maya Account to %@ with Ref# %@ on %@
            internal static func message(_ p1: Any, _ p2: Any, _ p3: Any, _ p4: Any) -> String {
              return L10n.tr("Localizable", "maya.activity.detail.share.p2p.message", String(describing: p1), String(describing: p2), String(describing: p3), String(describing: p4), fallback: "Sent %@ from my Maya Account to %@ with Ref# %@ on %@")
            }
          }
        }
        internal enum Trace {
          /// Trace number
          internal static let number = L10n.tr("Localizable", "maya.activity.detail.trace.number", fallback: "Trace number")
        }
        internal enum Travel {
          /// Maya Activity Detail
          internal static let info = L10n.tr("Localizable", "maya.activity.detail.travel.info", fallback: "This ticket is valid for 24 hours since purchase.")
        }
        internal enum Withdrawal {
          /// Withdrawal date
          internal static let date = L10n.tr("Localizable", "maya.activity.detail.withdrawal.date", fallback: "Withdrawal date")
        }
      }
    }
    internal enum Auto {
      internal enum Provisioning {
        /// By continuing, you consent to the following and certify that your information is true and complete 
        internal static let disclaimer = L10n.tr("Localizable", "maya.auto.provisioning.disclaimer", fallback: "By continuing, you consent to the following and certify that your information is true and complete ")
        /// We’ll open the following accounts so you can start using them straight away
        internal static let subtitle = L10n.tr("Localizable", "maya.auto.provisioning.subtitle", fallback: "We’ll open the following accounts so you can start using them straight away")
        /// Auto Provisioning + Data Privacy
        internal static let title = L10n.tr("Localizable", "maya.auto.provisioning.title", fallback: "Let’s get started")
        internal enum Item {
          internal enum Savings {
            /// Earn up to 14%% as you use Maya
            internal static let subtitle = L10n.tr("Localizable", "maya.auto.provisioning.item.savings.subtitle", fallback: "Earn up to 14%% as you use Maya")
            /// Savings
            internal static let title = L10n.tr("Localizable", "maya.auto.provisioning.item.savings.title", fallback: "Savings")
            internal enum Privacy {
              ///  • Maya Bank privacy policy
              internal static let spiel = L10n.tr("Localizable", "maya.auto.provisioning.item.savings.privacy.spiel", fallback: " • Maya Bank privacy policy")
            }
            internal enum Terms {
              ///  • Maya Bank terms and conditions
              internal static let spiel = L10n.tr("Localizable", "maya.auto.provisioning.item.savings.terms.spiel", fallback: " • Maya Bank terms and conditions")
            }
          }
          internal enum Wallet {
            /// Opened by default
            internal static let subtitle = L10n.tr("Localizable", "maya.auto.provisioning.item.wallet.subtitle", fallback: "Opened by default")
            /// Wallet
            internal static let title = L10n.tr("Localizable", "maya.auto.provisioning.item.wallet.title", fallback: "Wallet")
            internal enum Privacy {
              ///  • Maya Philippines privacy policy
              internal static let spiel = L10n.tr("Localizable", "maya.auto.provisioning.item.wallet.privacy.spiel", fallback: " • Maya Philippines privacy policy")
            }
            internal enum Terms {
              ///  • Maya Philippines terms and conditions
              internal static let spiel = L10n.tr("Localizable", "maya.auto.provisioning.item.wallet.terms.spiel", fallback: " • Maya Philippines terms and conditions")
            }
          }
        }
      }
    }
    internal enum Components {
      internal enum Action {
        /// Maya Components
        internal static let favorites = L10n.tr("Localizable", "maya.components.action.favorites", fallback: "Save details to favorites")
        /// Report an issue
        internal static let report = L10n.tr("Localizable", "maya.components.action.report", fallback: "Report an issue")
        /// Share or save this receipt
        internal static let share = L10n.tr("Localizable", "maya.components.action.share", fallback: "Share or save this receipt")
      }
    }
    internal enum Credit {
      internal enum Accountsummary {
        internal enum Accountnumber {
          /// Account number copied
          internal static let copied = L10n.tr("Localizable", "maya.credit.accountsummary.accountnumber.copied", fallback: "Account number copied")
        }
        internal enum Placeholder {
          /// Select a relationship
          internal static let contactRelationship = L10n.tr("Localizable", "maya.credit.accountsummary.placeholder.contactRelationship", fallback: "Select a relationship")
          /// Enter first name
          internal static let firstname = L10n.tr("Localizable", "maya.credit.accountsummary.placeholder.firstname", fallback: "Enter first name")
          /// Choose your gender
          internal static let gender = L10n.tr("Localizable", "maya.credit.accountsummary.placeholder.gender", fallback: "Choose your gender")
          /// Enter last name
          internal static let lastname = L10n.tr("Localizable", "maya.credit.accountsummary.placeholder.lastname", fallback: "Enter last name")
          /// Choose your marital status
          internal static let maritalStatus = L10n.tr("Localizable", "maya.credit.accountsummary.placeholder.maritalStatus", fallback: "Choose your marital status")
          /// Enter mobile number
          internal static let mobileNumber = L10n.tr("Localizable", "maya.credit.accountsummary.placeholder.mobileNumber", fallback: "Enter mobile number")
        }
        internal enum Settings {
          /// Unverified
          internal static let unverified = L10n.tr("Localizable", "maya.credit.accountsummary.settings.unverified", fallback: "Unverified")
          /// Verified
          internal static let verified = L10n.tr("Localizable", "maya.credit.accountsummary.settings.verified", fallback: "Verified")
        }
        internal enum Update {
          /// Add
          internal static let add = L10n.tr("Localizable", "maya.credit.accountsummary.update.add", fallback: "Add")
          /// Edit
          internal static let edit = L10n.tr("Localizable", "maya.credit.accountsummary.update.edit", fallback: "Edit")
        }
      }
      internal enum Allset {
        /// %@%% + Documentary Stamp Tax
        internal static func servicefee(_ p1: Any) -> String {
          return L10n.tr("Localizable", "maya.credit.allset.servicefee", String(describing: p1), fallback: "%@%% + Documentary Stamp Tax")
        }
        internal enum Billing {
          internal enum End {
            /// Every %@ of the month
            internal static func date(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.credit.allset.billing.end.date", String(describing: p1), fallback: "Every %@ of the month")
            }
          }
        }
      }
      internal enum Application {
        internal enum Billing {
          internal enum Due {
            /// %@ days after billing
            /// end date
            internal static func date(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.credit.application.billing.due.date", String(describing: p1), fallback: "%@ days after billing\nend date")
            }
          }
          internal enum End {
            /// %@ of the month
            internal static func date(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.credit.application.billing.end.date", String(describing: p1), fallback: "%@ of the month")
            }
          }
        }
        internal enum Day {
          /// Day
          internal static let unit = L10n.tr("Localizable", "maya.credit.application.day.unit", fallback: "Day")
        }
        internal enum Expires {
          /// Offer ends on %@
          internal static func on(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.credit.application.expires.on", String(describing: p1), fallback: "Offer ends on %@")
          }
        }
        internal enum Interest {
          /// %@%% against used limit.
          internal static func rate(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.credit.application.interest.rate", String(describing: p1), fallback: "%@%% against used limit.")
          }
        }
        internal enum Learn {
          /// Learn More
          internal static let more = L10n.tr("Localizable", "maya.credit.application.learn.more", fallback: "Learn More")
        }
        internal enum Please {
          internal enum Provide {
            /// Please provide and verify your email on the settings menu
            internal static let email = L10n.tr("Localizable", "maya.credit.application.please.provide.email", fallback: "Please provide and verify your email on the settings menu")
          }
        }
        internal enum Select {
          /// Select Day
          internal static let day = L10n.tr("Localizable", "maya.credit.application.select.day", fallback: "Select Day")
          internal enum An {
            internal enum End {
              /// Select an end date
              internal static let date = L10n.tr("Localizable", "maya.credit.application.select.an.end.date", fallback: "Select an end date")
            }
          }
        }
      }
      internal enum Assign {
        internal enum Contact {
          internal enum Reference {
            /// Contact relationship
            internal static let contactRelationship = L10n.tr("Localizable", "maya.credit.assign.contact.reference.contactRelationship", fallback: "Contact relationship")
            /// Add a person we can contact in case of urgent concerns about your Maya Easy Credit.
            internal static let subtitle = L10n.tr("Localizable", "maya.credit.assign.contact.reference.subtitle", fallback: "Add a person we can contact in case of urgent concerns about your Maya Easy Credit.")
            /// Assign a contact reference
            internal static let title = L10n.tr("Localizable", "maya.credit.assign.contact.reference.title", fallback: "Assign a contact reference")
          }
        }
      }
      internal enum Bill {
        internal enum Period {
          /// BILL STATEMENT FOR %@ %@
          internal static func label(_ p1: Any, _ p2: Any) -> String {
            return L10n.tr("Localizable", "maya.credit.bill.period.label", String(describing: p1), String(describing: p2), fallback: "BILL STATEMENT FOR %@ %@")
          }
        }
      }
      internal enum Billing {
        internal enum Latest {
          internal enum Bill {
            /// Due %@
            internal static func due(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.credit.billing.latest.bill.due", String(describing: p1), fallback: "Due %@")
            }
            /// Latest bill
            internal static let label = L10n.tr("Localizable", "maya.credit.billing.latest.bill.label", fallback: "Latest bill")
            /// Statement for %@
            internal static func statement(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.credit.billing.latest.bill.statement", String(describing: p1), fallback: "Statement for %@")
            }
            internal enum Description {
              /// This amount is based on your latest billing statement. This does not include credit used (plus fees) after the coverage period.
              internal static let label = L10n.tr("Localizable", "maya.credit.billing.latest.bill.description.label", fallback: "This amount is based on your latest billing statement. This does not include credit used (plus fees) after the coverage period.")
            }
            internal enum Info {
              /// To avoid daily penalty fees, remember to pay in full on or before the due date.
              internal static let label = L10n.tr("Localizable", "maya.credit.billing.latest.bill.info.label", fallback: "To avoid daily penalty fees, remember to pay in full on or before the due date.")
            }
          }
        }
        internal enum Outstanding {
          internal enum Balance {
            /// Outstanding balance
            internal static let label = L10n.tr("Localizable", "maya.credit.billing.outstanding.balance.label", fallback: "Outstanding balance")
            internal enum Sub {
              /// Includes your total unpaid credit plus fees, documentary stamp tax, and any overdue penalties
              internal static let label = L10n.tr("Localizable", "maya.credit.billing.outstanding.balance.sub.label", fallback: "Includes your total unpaid credit plus fees, documentary stamp tax, and any overdue penalties")
            }
          }
        }
        internal enum Paybill {
          internal enum Button {
            /// Pay this bill
            internal static let title = L10n.tr("Localizable", "maya.credit.billing.paybill.button.title", fallback: "Pay this bill")
          }
        }
        internal enum Paynow {
          internal enum Button {
            /// Pay now
            internal static let title = L10n.tr("Localizable", "maya.credit.billing.paynow.button.title", fallback: "Pay now")
          }
          internal enum Sub {
            /// Pay partially or in full anytime 😉
            internal static let label = L10n.tr("Localizable", "maya.credit.billing.paynow.sub.label", fallback: "Pay partially or in full anytime 😉")
          }
        }
        internal enum Statement {
          /// Billing Statement for %@
          internal static func header(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.credit.billing.statement.header", String(describing: p1), fallback: "Billing Statement for %@")
          }
          /// For billing period: %@ - %@
          internal static func period(_ p1: Any, _ p2: Any) -> String {
            return L10n.tr("Localizable", "maya.credit.billing.statement.period", String(describing: p1), String(describing: p2), fallback: "For billing period: %@ - %@")
          }
        }
        internal enum Statements {
          /// Billing Statements
          internal static let label = L10n.tr("Localizable", "maya.credit.billing.statements.label", fallback: "Billing Statements")
        }
        internal enum View {
          internal enum Button {
            /// View
            internal static let title = L10n.tr("Localizable", "maya.credit.billing.view.button.title", fallback: "View")
          }
          internal enum Statement {
            internal enum Button {
              /// View statement
              internal static let title = L10n.tr("Localizable", "maya.credit.billing.view.statement.button.title", fallback: "View statement")
            }
          }
        }
      }
      internal enum ChatWithUs {
        /// Chat with us
        internal static let link = L10n.tr("Localizable", "maya.credit.chatWithUs.link", fallback: "Chat with us")
      }
      internal enum Confirm {
        internal enum Change {
          internal enum Billing {
            internal enum End {
              /// Every %@ of the month
              internal static func date(_ p1: Any) -> String {
                return L10n.tr("Localizable", "maya.credit.confirm.change.billing.end.date", String(describing: p1), fallback: "Every %@ of the month")
              }
            }
          }
        }
      }
      internal enum ConfirmDetails {
        /// Confirm details
        internal static let title = L10n.tr("Localizable", "maya.credit.confirmDetails.title", fallback: "Confirm details")
        internal enum Section {
          /// Billing details
          internal static let billing = L10n.tr("Localizable", "maya.credit.confirmDetails.section.billing", fallback: "Billing details")
          /// Assigned contact reference
          internal static let contact = L10n.tr("Localizable", "maya.credit.confirmDetails.section.contact", fallback: "Assigned contact reference")
          /// Personal details
          internal static let personal = L10n.tr("Localizable", "maya.credit.confirmDetails.section.personal", fallback: "Personal details")
          internal enum Billing {
            /// Due date is 15 days after your billing end date
            internal static let info = L10n.tr("Localizable", "maya.credit.confirmDetails.section.billing.info", fallback: "Due date is 15 days after your billing end date")
          }
        }
      }
      internal enum Contact {
        internal enum Reference {
          internal enum Required {
            /// assign a contact reference first.
            internal static let link = L10n.tr("Localizable", "maya.credit.contact.reference.required.link", fallback: "assign a contact reference first.")
            /// To continue using Maya Easy Credit, 
            /// assign a contact reference first.
            internal static let message = L10n.tr("Localizable", "maya.credit.contact.reference.required.message", fallback: "To continue using Maya Easy Credit, \nassign a contact reference first.")
          }
        }
      }
      internal enum ContactReference {
        internal enum Header {
          /// Assign a contact reference
          internal static let add = L10n.tr("Localizable", "maya.credit.contactReference.header.add", fallback: "Assign a contact reference")
          /// My contact reference
          internal static let edit = L10n.tr("Localizable", "maya.credit.contactReference.header.edit", fallback: "My contact reference")
          /// Assign a contact reference
          internal static let signup = L10n.tr("Localizable", "maya.credit.contactReference.header.signup", fallback: "Assign a contact reference")
        }
      }
      internal enum Contacts {
        internal enum No {
          internal enum Permission {
            /// For more security and convenience in referring a mobile number, please open your settings and allow Maya access to your contacts.
            internal static let message = L10n.tr("Localizable", "maya.credit.contacts.no.permission.message", fallback: "For more security and convenience in referring a mobile number, please open your settings and allow Maya access to your contacts.")
            /// Allow Contacts
            internal static let title = L10n.tr("Localizable", "maya.credit.contacts.no.permission.title", fallback: "Allow Contacts")
          }
        }
      }
      internal enum Dashboard {
        internal enum Badge {
          /// Exclusive Offer
          internal static let exclusiveOffer = L10n.tr("Localizable", "maya.credit.dashboard.badge.exclusiveOffer", fallback: "Exclusive Offer")
          /// New Update
          internal static let newUpdate = L10n.tr("Localizable", "maya.credit.dashboard.badge.newUpdate", fallback: "New Update")
          /// Now Available
          internal static let nowAvailable = L10n.tr("Localizable", "maya.credit.dashboard.badge.nowAvailable", fallback: "Now Available")
        }
        internal enum Balance {
          /// %@ limit
          internal static func limit(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.credit.dashboard.balance.limit", String(describing: p1), fallback: "%@ limit")
          }
          /// %@ used
          internal static func used(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.credit.dashboard.balance.used", String(describing: p1), fallback: "%@ used")
          }
        }
        internal enum Spiel {
          internal enum Approved {
            /// We're doing final checks and we'll notify you once we're ready.
            internal static let subtitle = L10n.tr("Localizable", "maya.credit.dashboard.spiel.approved.subtitle", fallback: "We're doing final checks and we'll notify you once we're ready.")
            /// Thanks for joining the early access.
            internal static let title = L10n.tr("Localizable", "maya.credit.dashboard.spiel.approved.title", fallback: "Thanks for joining the early access.")
          }
          internal enum Booked {
            internal enum Error {
              /// We encountered an issue loading Maya Easy Credit.
              internal static let subtitle = L10n.tr("Localizable", "maya.credit.dashboard.spiel.booked.error.subtitle", fallback: "We encountered an issue loading Maya Easy Credit.")
              /// Failed to Load
              internal static let title = L10n.tr("Localizable", "maya.credit.dashboard.spiel.booked.error.title", fallback: "Failed to Load")
            }
          }
          internal enum Eligible {
            /// Activate your own credit line now.
            internal static let subtitle = L10n.tr("Localizable", "maya.credit.dashboard.spiel.eligible.subtitle", fallback: "Activate your own credit line now.")
            /// Maya Easy Credit
            internal static let title = L10n.tr("Localizable", "maya.credit.dashboard.spiel.eligible.title", fallback: "All Set!")
            internal enum Cta {
              /// Activate Now
              internal static let title = L10n.tr("Localizable", "maya.credit.dashboard.spiel.eligible.cta.title", fallback: "Activate Now")
            }
          }
          internal enum Error {
            /// We encountered an issue while retrieving your application status.
            internal static let subtitle = L10n.tr("Localizable", "maya.credit.dashboard.spiel.error.subtitle", fallback: "We encountered an issue while retrieving your application status.")
            /// Error
            internal static let title = L10n.tr("Localizable", "maya.credit.dashboard.spiel.error.title", fallback: "Error")
            internal enum Cta {
              /// Tap to Retry
              internal static let title = L10n.tr("Localizable", "maya.credit.dashboard.spiel.error.cta.title", fallback: "Tap to Retry")
            }
          }
          internal enum Ineligible {
            /// Based on our checks, you are not eligible for the service at this time.
            internal static let subtitle = L10n.tr("Localizable", "maya.credit.dashboard.spiel.ineligible.subtitle", fallback: "Based on our checks, you are not eligible for the service at this time.")
            /// Thank you for signing up.
            internal static let title = L10n.tr("Localizable", "maya.credit.dashboard.spiel.ineligible.title", fallback: "Thank you for signing up.")
          }
          internal enum Optin {
            /// Open a low-cost credit line for your PayMaya transactions
            internal static let subtitle = L10n.tr("Localizable", "maya.credit.dashboard.spiel.optin.subtitle", fallback: "Open a low-cost credit line for your PayMaya transactions")
            /// Get exclusive early access
            internal static let title = L10n.tr("Localizable", "maya.credit.dashboard.spiel.optin.title", fallback: "Get exclusive early access")
            internal enum Cta {
              /// Join Now
              internal static let title = L10n.tr("Localizable", "maya.credit.dashboard.spiel.optin.cta.title", fallback: "Join Now")
            }
          }
          internal enum Submitted {
            /// We're just doing final reviews. Please check back later.
            internal static let subtitle = L10n.tr("Localizable", "maya.credit.dashboard.spiel.submitted.subtitle", fallback: "We're just doing final reviews. Please check back later.")
            /// Thank you for signing up.
            internal static let title = L10n.tr("Localizable", "maya.credit.dashboard.spiel.submitted.title", fallback: "Thank you for signing up.")
          }
        }
      }
      internal enum DisclosureStatement {
        /// Maya Bank, Inc. is regulated by the Bangko Sentral ng Pilipinas – bsp.gov.ph.
        /// Chat with us for any concerns or send us an <NAME_EMAIL>.
        internal static let label = L10n.tr("Localizable", "maya.credit.disclosureStatement.label", fallback: "Maya Bank, Inc. is regulated by the Bangko Sentral ng Pilipinas – bsp.gov.ph.\nChat with us for any concerns or send us an <NAME_EMAIL>.")
      }
      internal enum Discovery {
        internal enum Signup {
          /// Your own low-cost credit line
          internal static let header = L10n.tr("Localizable", "maya.credit.discovery.signup.header", fallback: "Your own low-cost credit line")
          /// Cash in, pay your bills, buy load, and more!
          internal static let subheader = L10n.tr("Localizable", "maya.credit.discovery.signup.subheader", fallback: "Cash in, pay your bills, buy load, and more!")
          internal enum Button {
            /// Sign up now
            internal static let title = L10n.tr("Localizable", "maya.credit.discovery.signup.button.title", fallback: "Sign up now")
          }
          internal enum Credit {
            /// • Up to ₱30,000 credit limit 
            /// • Low service fees based on credit used within billing period 
            /// • No additional documents needed
            internal static let features = L10n.tr("Localizable", "maya.credit.discovery.signup.credit.features", fallback: "• Up to ₱30,000 credit limit \n• Low service fees based on credit used within billing period \n• No additional documents needed")
          }
        }
        internal enum Statement {
          /// I have read and agreed to the Maya Data Privacy Policy.
          internal static let label = L10n.tr("Localizable", "maya.credit.discovery.statement.label", fallback: "I have read and agreed to the Maya Data Privacy Policy.")
          /// Maya Data Privacy Policy
          internal static let policy = L10n.tr("Localizable", "maya.credit.discovery.statement.policy", fallback: "Maya Data Privacy Policy")
          internal enum Label {
            /// By signing up, I have read and agreed to the Maya Data Privacy Policy.
            internal static let enhanced = L10n.tr("Localizable", "maya.credit.discovery.statement.label.enhanced", fallback: "By signing up, I have read and agreed to the Maya Data Privacy Policy.")
          }
        }
      }
      internal enum Error {
        internal enum Modal {
          /// We ran into an issue while getting your request. Please try again.
          internal static let spiel = L10n.tr("Localizable", "maya.credit.error.modal.spiel", fallback: "We ran into an issue while getting your request. Please try again.")
        }
      }
      internal enum HelpCenter {
        /// Maya Bank, Inc. is regulated by the Bangko Sentral ng Pilipinas – bsp.gov.ph.
        /// Visit our Help Center or check our service advisories for any concerns.
        internal static let label = L10n.tr("Localizable", "maya.credit.helpCenter.label", fallback: "Maya Bank, Inc. is regulated by the Bangko Sentral ng Pilipinas – bsp.gov.ph.\nVisit our Help Center or check our service advisories for any concerns.")
      }
      internal enum Interest {
        internal enum Details {
          internal enum Percent {
            internal enum Limit {
              /// Maya Easy Credit Interest Details
              internal static func label(_ p1: Any) -> String {
                return L10n.tr("Localizable", "maya.credit.interest.details.percent.limit.label", String(describing: p1), fallback: "%@%% of used limit")
              }
            }
          }
        }
      }
      internal enum Location {
        internal enum Open {
          internal enum Settings {
            /// Maya requires access to properly assess your credit score.
            internal static let message = L10n.tr("Localizable", "maya.credit.location.open.settings.message", fallback: "Maya requires access to properly assess your credit score.")
            /// “Maya” would like to access your contacts, calendars, photos, and installed apps
            internal static let title = L10n.tr("Localizable", "maya.credit.location.open.settings.title", fallback: "“Maya” would like to access your contacts, calendars, photos, and installed apps")
          }
        }
        internal enum Permission {
          internal enum Needed {
            /// In order to asses your application, we need permissions to access your location
            internal static let message = L10n.tr("Localizable", "maya.credit.location.permission.needed.message", fallback: "In order to asses your application, we need permissions to access your location")
            /// Location permission needed
            internal static let title = L10n.tr("Localizable", "maya.credit.location.permission.needed.title", fallback: "Location permission needed")
          }
        }
        internal enum Try {
          internal enum Again {
            /// We couldn't get your location.
            /// Please try again.
            internal static let message = L10n.tr("Localizable", "maya.credit.location.try.again.message", fallback: "We couldn't get your location.\nPlease try again.")
            /// Let's try that again
            internal static let title = L10n.tr("Localizable", "maya.credit.location.try.again.title", fallback: "Let's try that again")
          }
        }
      }
      internal enum Management {
        internal enum Header {
          /// MANAGE MY CREDIT
          internal static let label = L10n.tr("Localizable", "maya.credit.management.header.label", fallback: "MANAGE MY CREDIT")
        }
      }
      internal enum Mfa {
        internal enum RiskChallenge {
          internal enum Error {
            /// We couldn’t complete your transfer. For more information, please visit our Help Center.
            internal static let message = L10n.tr("Localizable", "maya.credit.mfa.riskChallenge.error.message", fallback: "We couldn’t complete your transfer. For more information, please visit our Help Center.")
            /// Transfer unsuccessful
            internal static let title = L10n.tr("Localizable", "maya.credit.mfa.riskChallenge.error.title", fallback: "Transfer unsuccessful")
          }
        }
      }
      internal enum PersonalDetails {
        internal enum Header {
          /// Add your 
          /// personal details
          internal static let add = L10n.tr("Localizable", "maya.credit.personalDetails.header.add", fallback: "Add your \npersonal details")
          /// Update your 
          /// personal details
          internal static let edit = L10n.tr("Localizable", "maya.credit.personalDetails.header.edit", fallback: "Update your \npersonal details")
          /// Update your 
          /// personal details
          internal static let signup = L10n.tr("Localizable", "maya.credit.personalDetails.header.signup", fallback: "Update your \npersonal details")
        }
      }
      internal enum Recent {
        internal enum Transactions {
          internal enum Header {
            /// RECENT TRANSACTIONS
            internal static let label = L10n.tr("Localizable", "maya.credit.recent.transactions.header.label", fallback: "RECENT TRANSACTIONS")
          }
          internal enum Information {
            /// You can pay this amount partially or in full anytime.
            internal static let bold = L10n.tr("Localizable", "maya.credit.recent.transactions.information.bold", fallback: "You can pay this amount partially or in full anytime.")
            /// This amount shows your latest used credit for this period, service fees and documentary tax (DST). 
            internal static let label = L10n.tr("Localizable", "maya.credit.recent.transactions.information.label", fallback: "This amount shows your latest used credit for this period, service fees and documentary tax (DST). ")
          }
        }
      }
      internal enum Settings {
        internal enum Spiel {
          internal enum Billing {
            internal enum End {
              /// Every %@ of the month
              internal static func date(_ p1: Any) -> String {
                return L10n.tr("Localizable", "maya.credit.settings.spiel.billing.end.date", String(describing: p1), fallback: "Every %@ of the month")
              }
            }
          }
          internal enum No {
            /// No verified email
            internal static let email = L10n.tr("Localizable", "maya.credit.settings.spiel.no.email", fallback: "No verified email")
          }
          internal enum Service {
            /// %@%% against used limit
            internal static func fee(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.credit.settings.spiel.service.fee", String(describing: p1), fallback: "%@%% against used limit")
            }
          }
        }
      }
      internal enum Signup {
        /// disclosure statement
        internal static let disclosurestatement = L10n.tr("Localizable", "maya.credit.signup.disclosurestatement", fallback: "disclosure statement")
        /// By signing up for Maya Easy Credit, you have read and understood our terms and conditions, disclosure statement, truth and transparency in lending, and agree to share your data with Maya Bank, Inc.
        internal static let info = L10n.tr("Localizable", "maya.credit.signup.info", fallback: "By signing up for Maya Easy Credit, you have read and understood our terms and conditions, disclosure statement, truth and transparency in lending, and agree to share your data with Maya Bank, Inc.")
        /// terms and conditions
        internal static let termsandconditions = L10n.tr("Localizable", "maya.credit.signup.termsandconditions", fallback: "terms and conditions")
        /// Sign up for Maya Easy Credit
        internal static let title = L10n.tr("Localizable", "maya.credit.signup.title", fallback: "Sign up for Maya Easy Credit")
        /// truth and transparency in lending
        internal static let truthandtransparency = L10n.tr("Localizable", "maya.credit.signup.truthandtransparency", fallback: "truth and transparency in lending")
        internal enum Acknowledge {
          internal enum Button {
            /// Got it
            internal static let title = L10n.tr("Localizable", "maya.credit.signup.acknowledge.button.title", fallback: "Got it")
          }
        }
        internal enum AllSet {
          /// Proceed with the offer below to start using Maya Easy Credit for payments and purchases
          internal static let description = L10n.tr("Localizable", "maya.credit.signup.allSet.description", fallback: "Proceed with the offer below to start using Maya Easy Credit for payments and purchases")
          /// Your offer is ready! ⚡
          internal static let title = L10n.tr("Localizable", "maya.credit.signup.allSet.title", fallback: "Your offer is ready! ⚡")
        }
        internal enum Automatedpayments {
          /// We automatically deduct your credit bill from your wallet balance upon reaching the due date. You may incur penalties if you don’t pay your bill on time.
          internal static let content = L10n.tr("Localizable", "maya.credit.signup.automatedpayments.content", fallback: "We automatically deduct your credit bill from your wallet balance upon reaching the due date. You may incur penalties if you don’t pay your bill on time.")
          /// 💳  Automated payments
          internal static let title = L10n.tr("Localizable", "maya.credit.signup.automatedpayments.title", fallback: "💳  Automated payments")
        }
        internal enum Billing {
          internal enum End {
            /// Every %@ of the month
            internal static func date(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.credit.signup.billing.end.date", String(describing: p1), fallback: "Every %@ of the month")
            }
          }
        }
        internal enum Consent {
          internal enum PrivacyNotice {
            /// Privacy notice
            internal static let title = L10n.tr("Localizable", "maya.credit.signup.consent.privacyNotice.title", fallback: "Privacy notice")
          }
        }
        internal enum Contact {
          internal enum Reference {
            /// To enjoy Maya Easy Credit, please provide the following information
            internal static let subtitle = L10n.tr("Localizable", "maya.credit.signup.contact.reference.subtitle", fallback: "To enjoy Maya Easy Credit, please provide the following information")
            /// Set up your credit
            internal static let title = L10n.tr("Localizable", "maya.credit.signup.contact.reference.title", fallback: "Set up your credit")
            internal enum Automatedpayments {
              /// On your due date, the remaining balance from your total amount due is auto-deducted from your Maya Wallet to help you avoid getting late penalty fees.
              internal static let content = L10n.tr("Localizable", "maya.credit.signup.contact.reference.automatedpayments.content", fallback: "On your due date, the remaining balance from your total amount due is auto-deducted from your Maya Wallet to help you avoid getting late penalty fees.")
            }
            internal enum Section {
              internal enum Billing {
                /// Alternative mobile number (optional)
                internal static let altmobile = L10n.tr("Localizable", "maya.credit.signup.contact.reference.section.billing.altmobile", fallback: "Alternative mobile number (optional)")
                /// Billing email address
                internal static let emailaddress = L10n.tr("Localizable", "maya.credit.signup.contact.reference.section.billing.emailaddress", fallback: "Billing email address")
                /// Billing end date
                internal static let enddate = L10n.tr("Localizable", "maya.credit.signup.contact.reference.section.billing.enddate", fallback: "Billing end date")
                /// Credit limit
                internal static let limit = L10n.tr("Localizable", "maya.credit.signup.contact.reference.section.billing.limit", fallback: "Credit limit")
                /// Billing details
                internal static let title = L10n.tr("Localizable", "maya.credit.signup.contact.reference.section.billing.title", fallback: "Billing details")
                internal enum Altmobile {
                  /// Mobile and landline numbers are accepted Switch to landline number
                  internal static let info = L10n.tr("Localizable", "maya.credit.signup.contact.reference.section.billing.altmobile.info", fallback: "Mobile and landline numbers are accepted Switch to landline number")
                  /// Alternative mobile number
                  internal static let label = L10n.tr("Localizable", "maya.credit.signup.contact.reference.section.billing.altmobile.label", fallback: "Alternative mobile number")
                }
                internal enum Emailaddress {
                  /// Your billing statement will be sent to this email
                  internal static let info = L10n.tr("Localizable", "maya.credit.signup.contact.reference.section.billing.emailaddress.info", fallback: "Your billing statement will be sent to this email")
                }
                internal enum Enddate {
                  /// Billing end date is the cutoff for transactions included in your upcoming billing statement.
                  internal static let info = L10n.tr("Localizable", "maya.credit.signup.contact.reference.section.billing.enddate.info", fallback: "Billing end date is the cutoff for transactions included in your upcoming billing statement.")
                }
              }
              internal enum Personal {
                /// Personal details
                internal static let title = L10n.tr("Localizable", "maya.credit.signup.contact.reference.section.personal.title", fallback: "Personal details")
              }
            }
          }
        }
        internal enum EmailVerification {
          internal enum Invalid {
            /// Please enter a valid email address
            internal static let email = L10n.tr("Localizable", "maya.credit.signup.emailVerification.invalid.email", fallback: "Please enter a valid email address")
          }
        }
        internal enum Interstitial {
          internal enum Consent {
            internal enum Error {
              /// Our privacy notice failed to load. Please try again to proceed with your application.
              internal static let message = L10n.tr("Localizable", "maya.credit.signup.interstitial.consent.error.message", fallback: "Our privacy notice failed to load. Please try again to proceed with your application.")
            }
          }
          internal enum Error {
            /// That didn’t load right
            internal static let title = L10n.tr("Localizable", "maya.credit.signup.interstitial.error.title.", fallback: "That didn’t load right")
          }
          internal enum InitialEligibility {
            internal enum Error {
              /// We couldn’t check your eligibility. Please try again to proceed with your application.
              internal static let message = L10n.tr("Localizable", "maya.credit.signup.interstitial.initialEligibility.error.message", fallback: "We couldn’t check your eligibility. Please try again to proceed with your application.")
            }
          }
        }
        internal enum Learnmore {
          /// Learn more
          internal static let title = L10n.tr("Localizable", "maya.credit.signup.learnmore.title", fallback: "Learn more")
        }
        internal enum Lowservicefees {
          /// Your service fee is a variable amount added to your total Maya Easy Credit dues. This fee is computed based on the age of your Maya account, your transaction history, and other usage factors.
          internal static let content = L10n.tr("Localizable", "maya.credit.signup.lowservicefees.content", fallback: "Your service fee is a variable amount added to your total Maya Easy Credit dues. This fee is computed based on the age of your Maya account, your transaction history, and other usage factors.")
          /// 🙏  Low service fees
          internal static let title = L10n.tr("Localizable", "maya.credit.signup.lowservicefees.title", fallback: "🙏  Low service fees")
          internal enum Computation {
            /// View sample computation
            internal static let link = L10n.tr("Localizable", "maya.credit.signup.lowservicefees.computation.link", fallback: "View sample computation")
          }
        }
        internal enum MaidenName {
          internal enum FirstName {
            /// Mother’s maiden first name
            internal static let label = L10n.tr("Localizable", "maya.credit.signup.maidenName.FirstName.label", fallback: "Mother’s maiden first name")
          }
          internal enum LastName {
            /// Mother’s maiden last name
            internal static let label = L10n.tr("Localizable", "maya.credit.signup.maidenName.LastName.label", fallback: "Mother’s maiden last name")
          }
          internal enum MiddleName {
            /// Mother’s maiden middle name
            internal static let label = L10n.tr("Localizable", "maya.credit.signup.maidenName.MiddleName.label", fallback: "Mother’s maiden middle name")
          }
        }
        internal enum MobileVerification {
          internal enum Invalid {
            /// Alternative number can't be your current number
            internal static let current = L10n.tr("Localizable", "maya.credit.signup.mobileVerification.invalid.current", fallback: "Alternative number can't be your current number")
            /// The mobile number you entered appears to be invalid. Please try again.
            internal static let email = L10n.tr("Localizable", "maya.credit.signup.mobileVerification.invalid.email", fallback: "The mobile number you entered appears to be invalid. Please try again.")
          }
        }
        internal enum NameVerification {
          internal enum Invalid {
            /// Please use only letters and hyphens
            internal static let characters = L10n.tr("Localizable", "maya.credit.signup.nameVerification.invalid.characters", fallback: "Please use only letters and hyphens")
            internal enum Characters {
              /// Repeated characters detected. Please check if you entered the correct name. 
              internal static let repeated = L10n.tr("Localizable", "maya.credit.signup.nameVerification.invalid.characters.repeated", fallback: "Repeated characters detected. Please check if you entered the correct name. ")
            }
            internal enum Length {
              /// Please enter a name that is 50 characters or less
              internal static let maximum = L10n.tr("Localizable", "maya.credit.signup.nameVerification.invalid.length.maximum", fallback: "Please enter a name that is 50 characters or less")
              /// Please enter a name that is at least 2 characters long
              internal static let minimum = L10n.tr("Localizable", "maya.credit.signup.nameVerification.invalid.length.minimum", fallback: "Please enter a name that is at least 2 characters long")
            }
          }
        }
        internal enum PartnerMerchant {
          /// Enjoy a %@ credit limit to cash in, pay your bills, buy load, and more! 
          internal static func success(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.credit.signup.partnerMerchant.success", String(describing: p1), fallback: "Enjoy a %@ credit limit to cash in, pay your bills, buy load, and more! ")
          }
        }
      }
      internal enum Transfer {
        internal enum Alert {
          internal enum Spiel {
            internal enum Locked {
              /// Pay your overdue bill of ₱%@ to avoid further penalties. Once paid, we will review your account for unlocking.
              internal static func message(_ p1: Any) -> String {
                return L10n.tr("Localizable", "maya.credit.transfer.alert.spiel.locked.message", String(describing: p1), fallback: "Pay your overdue bill of ₱%@ to avoid further penalties. Once paid, we will review your account for unlocking.")
              }
              /// Your account is locked
              internal static let title = L10n.tr("Localizable", "maya.credit.transfer.alert.spiel.locked.title", fallback: "Your account is locked")
              internal enum Active {
                /// Your account is currently being reviewed for unlocking. While waiting, you can still use your Maya wallet.
                internal static let message = L10n.tr("Localizable", "maya.credit.transfer.alert.spiel.locked.active.message", fallback: "Your account is currently being reviewed for unlocking. While waiting, you can still use your Maya wallet.")
                /// Account under review
                internal static let title = L10n.tr("Localizable", "maya.credit.transfer.alert.spiel.locked.active.title", fallback: "Account under review")
              }
            }
            internal enum Overdue {
              /// Your account will be locked in %@ days unless your overdue credit bill of ₱%@ is paid
              internal static func message(_ p1: Any, _ p2: Any) -> String {
                return L10n.tr("Localizable", "maya.credit.transfer.alert.spiel.overdue.message", String(describing: p1), String(describing: p2), fallback: "Your account will be locked in %@ days unless your overdue credit bill of ₱%@ is paid")
              }
              /// Your account is about to be locked
              internal static let title = L10n.tr("Localizable", "maya.credit.transfer.alert.spiel.overdue.title", fallback: "Your account is about to be locked")
              internal enum Tomorrow {
                /// Your account will be locked tomorrow unless your overdue credit bill of ₱%@ is paid
                internal static func message(_ p1: Any) -> String {
                  return L10n.tr("Localizable", "maya.credit.transfer.alert.spiel.overdue.tomorrow.message", String(describing: p1), fallback: "Your account will be locked tomorrow unless your overdue credit bill of ₱%@ is paid")
                }
              }
            }
          }
        }
        internal enum Credit {
          /// Your available credit limit is %@
          internal static func limit(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.credit.transfer.credit.limit", String(describing: p1), fallback: "Your available credit limit is %@")
          }
        }
        internal enum Fee {
          /// Fees apply. View sample computation
          internal static let sample = L10n.tr("Localizable", "maya.credit.transfer.fee.sample", fallback: "Fees apply. View sample computation")
          internal enum Sample {
            /// View sample computation
            internal static let green = L10n.tr("Localizable", "maya.credit.transfer.fee.sample.green", fallback: "View sample computation")
          }
        }
        internal enum Spiel {
          internal enum Confirm {
            /// Confirm Transfer
            internal static let transfer = L10n.tr("Localizable", "maya.credit.transfer.spiel.confirm.transfer", fallback: "Confirm Transfer")
          }
          internal enum Exceeds {
            internal enum Available {
              /// Amount entered exceeds credit limit
              internal static let credit = L10n.tr("Localizable", "maya.credit.transfer.spiel.exceeds.available.credit", fallback: "Amount entered exceeds credit limit")
              internal enum And {
                internal enum Cashin {
                  /// Amount entered exceeds cash in and credit limit
                  internal static let credit = L10n.tr("Localizable", "maya.credit.transfer.spiel.exceeds.available.and.cashin.credit", fallback: "Amount entered exceeds cash in and credit limit")
                }
              }
            }
            internal enum Cashin {
              /// Amount entered exceeds cash in limit
              internal static let credit = L10n.tr("Localizable", "maya.credit.transfer.spiel.exceeds.cashin.credit", fallback: "Amount entered exceeds cash in limit")
            }
          }
          internal enum Maximum {
            internal enum Credit {
              internal enum Limit {
                /// Sorry, you have reached your maximum Maya Easy Credit limit.
                internal static let reached = L10n.tr("Localizable", "maya.credit.transfer.spiel.maximum.credit.limit.reached", fallback: "Sorry, you have reached your maximum Maya Easy Credit limit.")
                internal enum New {
                  /// Maximum credit limit reached
                  internal static let reached = L10n.tr("Localizable", "maya.credit.transfer.spiel.maximum.credit.limit.new.reached", fallback: "Maximum credit limit reached")
                }
              }
            }
          }
          internal enum Slide {
            internal enum To {
              /// Slide to Transfer
              internal static let transfer = L10n.tr("Localizable", "maya.credit.transfer.spiel.slide.to.transfer", fallback: "Slide to Transfer")
            }
          }
          internal enum Your {
            internal enum Mobile {
              /// You (%@)
              internal static func number(_ p1: Any) -> String {
                return L10n.tr("Localizable", "maya.credit.transfer.spiel.your.mobile.number", String(describing: p1), fallback: "You (%@)")
              }
            }
          }
        }
        internal enum ToWallet {
          /// •••• •••• %@
          internal static func accountNumber(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.credit.transfer.toWallet.accountNumber", String(describing: p1), fallback: "•••• •••• %@")
          }
          /// Service fee (%@%)
          internal static func serviceFee(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.credit.transfer.toWallet.serviceFee", String(describing: p1), fallback: "Service fee (%@%)")
          }
          /// Transfer to wallet
          internal static let title = L10n.tr("Localizable", "maya.credit.transfer.toWallet.title", fallback: "Transfer to wallet")
          /// Transfer now
          internal static let transferNow = L10n.tr("Localizable", "maya.credit.transfer.toWallet.transferNow", fallback: "Transfer now")
          internal enum Success {
            internal enum Bottomsheet {
              /// From Maya Easy Credit to your Wallet
              internal static let subtitle = L10n.tr("Localizable", "maya.credit.transfer.toWallet.success.bottomsheet.subtitle", fallback: "From Maya Easy Credit to your Wallet")
              /// You have transferred 
              /// %@
              internal static func title(_ p1: Any) -> String {
                return L10n.tr("Localizable", "maya.credit.transfer.toWallet.success.bottomsheet.title", String(describing: p1), fallback: "You have transferred \n%@")
              }
              internal enum Button {
                /// Done
                internal static let title = L10n.tr("Localizable", "maya.credit.transfer.toWallet.success.bottomsheet.button.title", fallback: "Done")
              }
            }
          }
        }
        internal enum Wallet {
          /// Your Wallet cash in limit is %@
          internal static func limit(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.credit.transfer.wallet.limit", String(describing: p1), fallback: "Your Wallet cash in limit is %@")
          }
        }
      }
      internal enum WrittenOff {
        /// If you have any inquiries, you can visit our Help Center to learn more.
        internal static let info = L10n.tr("Localizable", "maya.credit.writtenOff.info", fallback: "If you have any inquiries, you can visit our Help Center to learn more.")
      }
    }
    internal enum DeviceManagement {
      internal enum Main {
        internal enum ActiveDevice {
          /// Device Management
          internal static let subtitle = L10n.tr("Localizable", "maya.deviceManagement.main.activeDevice.subtitle", fallback: "This device")
        }
        internal enum Error {
          internal enum FailedAPI {
            /// We ran into a connection issue. Please try again in a bit.
            internal static let message = L10n.tr("Localizable", "maya.deviceManagement.main.error.failedAPI.message", fallback: "We ran into a connection issue. Please try again in a bit.")
          }
          internal enum NoDeviceLoaded {
            /// We couldn’t load your login history at the moment. Please login again to retry.
            internal static let message = L10n.tr("Localizable", "maya.deviceManagement.main.error.noDeviceLoaded.message", fallback: "We couldn’t load your login history at the moment. Please login again to retry.")
          }
        }
        internal enum LoginHistoryHeader {
          /// See what devices have been used to log in to your Maya account in the last %@ days.
          internal static func subtitle(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.deviceManagement.main.loginHistoryHeader.subtitle", String(describing: p1), fallback: "See what devices have been used to log in to your Maya account in the last %@ days.")
          }
          /// You'll get an email whenever your account is logged in from a new device.
          internal static let updateEmail = L10n.tr("Localizable", "maya.deviceManagement.main.loginHistoryHeader.updateEmail", fallback: "You'll get an email whenever your account is logged in from a new device.")
        }
        internal enum Section {
          /// Active
          internal static let active = L10n.tr("Localizable", "maya.deviceManagement.main.section.active", fallback: "Active")
          /// Logged out
          internal static let loggedOut = L10n.tr("Localizable", "maya.deviceManagement.main.section.loggedOut", fallback: "Logged out")
        }
      }
    }
    internal enum Ekyc {
      internal enum Alert {
        internal enum MultipleSubmission {
          /// You can check your account status in the Profile page
          internal static let message = L10n.tr("Localizable", "maya.ekyc.alert.multipleSubmission.message", fallback: "You can check your account status in the Profile page")
          /// You may already have a KYC submission
          internal static let title = L10n.tr("Localizable", "maya.ekyc.alert.multipleSubmission.title", fallback: "You may already have a KYC submission")
        }
      }
      internal enum Capture {
        /// Position your ID in the frame
        internal static let instruction = L10n.tr("Localizable", "maya.ekyc.capture.instruction", fallback: "Position your ID in the frame")
        /// Take ID photo
        internal static let takeIdPhoto = L10n.tr("Localizable", "maya.ekyc.capture.takeIdPhoto", fallback: "Take ID photo")
        internal enum BackID {
          /// Back of ID
          internal static let title = L10n.tr("Localizable", "maya.ekyc.capture.backID.title", fallback: "Back of ID")
        }
        internal enum Focus {
          /// Focusing on ID
          internal static let instruction = L10n.tr("Localizable", "maya.ekyc.capture.focus.instruction", fallback: "Focusing on ID")
        }
        internal enum FrontID {
          /// Maya EKYC - Simple Capture
          internal static let title = L10n.tr("Localizable", "maya.ekyc.capture.frontID.title", fallback: "Front of ID")
        }
        internal enum ImageQuality {
          internal enum Error {
            /// Something went wrong. Please try again in a bit.
            internal static let description = L10n.tr("Localizable", "maya.ekyc.capture.imageQuality.error.description", fallback: "Something went wrong. Please try again in a bit.")
            /// That didn't load right.
            internal static let title = L10n.tr("Localizable", "maya.ekyc.capture.imageQuality.error.title", fallback: "That didn't load right.")
          }
        }
        internal enum Photo {
          ///   • %@
          internal static func livetip(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.ekyc.capture.photo.livetip", String(describing: p1), fallback: "  • %@")
          }
          /// Tips for taking a photo of your ID:
          /// %@
          internal static func tips(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.ekyc.capture.photo.tips", String(describing: p1), fallback: "Tips for taking a photo of your ID:\n%@")
          }
        }
      }
      internal enum CaptureV3 {
        /// Maya EKYC - Simple Capture V3
        internal static let title = L10n.tr("Localizable", "maya.ekyc.captureV3.title", fallback: "Take an ID photo")
        internal enum ScanType {
          /// Scan the _ of your
          internal static let title = L10n.tr("Localizable", "maya.ekyc.captureV3.scanType.title", fallback: "Scan the _ of your")
        }
        internal enum Tips {
          /// How to get a perfect picture
          internal static let title = L10n.tr("Localizable", "maya.ekyc.captureV3.tips.title", fallback: "How to get a perfect picture")
        }
      }
      internal enum Common {
        /// Continue
        internal static let `continue` = L10n.tr("Localizable", "maya.ekyc.common.continue", fallback: "Continue")
        internal enum Service {
          internal enum Title {
            /// Credit
            internal static let credit = L10n.tr("Localizable", "maya.ekyc.common.service.title.credit", fallback: "Credit")
            /// Crypto
            internal static let crypto = L10n.tr("Localizable", "maya.ekyc.common.service.title.crypto", fallback: "Crypto")
            /// Savings
            internal static let savings = L10n.tr("Localizable", "maya.ekyc.common.service.title.savings", fallback: "Savings")
            /// Maya EKYC - Common
            internal static let wallet = L10n.tr("Localizable", "maya.ekyc.common.service.title.wallet", fallback: "Wallet")
          }
        }
      }
      internal enum CompletingPersonalInformation {
        internal enum AddressDropdown {
          internal enum Error {
            /// We ran into a connection issue. Please try again in a bit.
            internal static let message = L10n.tr("Localizable", "maya.ekyc.completingPersonalInformation.addressDropdown.error.message", fallback: "We ran into a connection issue. Please try again in a bit.")
            /// That didn't load right.
            internal static let title = L10n.tr("Localizable", "maya.ekyc.completingPersonalInformation.addressDropdown.error.title", fallback: "That didn't load right.")
          }
        }
      }
      internal enum IdReview {
        internal enum AllGoods {
          /// Make sure all information on your ID can be read easily.
          internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.idReview.allGoods.subtitle", fallback: "Make sure all information on your ID can be read easily.")
          /// All good?
          internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.allGoods.title", fallback: "All good?")
        }
        internal enum Blurry {
          /// Make sure your whole ID is in the shot and in focus.
          internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.idReview.blurry.subtitle", fallback: "Make sure your whole ID is in the shot and in focus.")
          /// Your picture isn't clear
          internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.blurry.title", fallback: "Your picture isn't clear")
        }
        internal enum Failed {
          /// Choose a different ID
          internal static let chooseDifferentIdTitle = L10n.tr("Localizable", "maya.ekyc.idReview.failed.chooseDifferentIdTitle", fallback: "Choose a different ID")
          /// Take a new photo
          internal static let retakeTitle = L10n.tr("Localizable", "maya.ekyc.idReview.failed.retakeTitle", fallback: "Take a new photo")
          /// This photo doesn’t match the type of ID you picked. Please take a photo of your %@.
          internal static func subtitle(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.ekyc.idReview.failed.subtitle", String(describing: p1), fallback: "This photo doesn’t match the type of ID you picked. Please take a photo of your %@.")
          }
          /// This isn’t the right ID
          internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.failed.title", fallback: "This isn’t the right ID")
        }
        internal enum ImageQuality {
          internal enum Error {
            internal enum Blurred {
              /// Please make sure your whole ID is in focus and all information can be read clearly.
              internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.blurred.subtitle", fallback: "Please make sure your whole ID is in focus and all information can be read clearly.")
              /// Maya EKYC - Image-Quality Scenario
              internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.blurred.title", fallback: "Your photo isn’t clear")
            }
            internal enum Bright {
              /// Please take a photo of your ID under better lighting and make sure all information can be read clearly
              internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.bright.subtitle", fallback: "Please take a photo of your ID under better lighting and make sure all information can be read clearly")
              /// Your photo is too bright
              internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.bright.title", fallback: "Your photo is too bright")
            }
            internal enum CannotRead {
              internal enum Id {
                /// Please make sure your ID is in good condition and all information can be read clearly.
                internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.cannotRead.id.subtitle", fallback: "Please make sure your ID is in good condition and all information can be read clearly.")
                /// Your ID cannot be read
                internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.cannotRead.id.title", fallback: "Your ID cannot be read")
              }
            }
            internal enum Cropped {
              /// Please make sure your whole ID is within the indicated borders and all information can be read clearly.
              internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.cropped.subtitle", fallback: "Please make sure your whole ID is within the indicated borders and all information can be read clearly.")
              /// Parts of your ID are missing
              internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.cropped.title", fallback: "Parts of your ID are missing")
            }
            internal enum DarkPhoto {
              /// Please take a photo of your ID under better lighting and make sure all information can be read clearly
              internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.darkPhoto.subtitle", fallback: "Please take a photo of your ID under better lighting and make sure all information can be read clearly")
              /// Your photo is too dark
              internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.darkPhoto.title", fallback: "Your photo is too dark")
            }
            internal enum MoreThanOne {
              /// Please take a clear photo of 1 ID only.
              internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.moreThanOne.subtitle", fallback: "Please take a clear photo of 1 ID only.")
              /// Multiple IDs detected
              internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.moreThanOne.title", fallback: "Multiple IDs detected")
            }
            internal enum NoFaceDetected {
              /// Please make sure the face on your ID can be clearly seen in the photo.
              internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.noFaceDetected.subtitle", fallback: "Please make sure the face on your ID can be clearly seen in the photo.")
              /// Face on ID not detected
              internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.noFaceDetected.title", fallback: "Face on ID not detected")
            }
            internal enum NotFlat {
              /// Please take a photo of your ID on a flat surface.
              internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.notFlat.subtitle", fallback: "Please take a photo of your ID on a flat surface.")
            }
            internal enum NotLive {
              /// This photo appears to be a screenshot. Please take a photo of an actual ID.
              internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.notLive.subtitle", fallback: "This photo appears to be a screenshot. Please take a photo of an actual ID.")
            }
            internal enum Paper {
              /// Your ID appears to be printed on paper. Please secure a proper ID first and try again.
              internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.paper.subtitle", fallback: "Your ID appears to be printed on paper. Please secure a proper ID first and try again.")
            }
            internal enum ReflectonHologram {
              /// Please take a clear photo of your ID without any glare and make sure all information can be read clearly.
              internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.reflectonHologram.subtitle", fallback: "Please take a clear photo of your ID without any glare and make sure all information can be read clearly.")
            }
            internal enum Selfie {
              /// Please take a photo of an actual ID.
              internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.selfie.subtitle", fallback: "Please take a photo of an actual ID.")
              /// No ID detected
              internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.imageQuality.error.selfie.title", fallback: "No ID detected")
            }
          }
        }
        internal enum Mismatch {
          /// You've already submitted the %@ of your %@. Now, please upload a clear photo of the %@ of your ID. The image you provided is not the %@ of your ID.
          internal static func subtitle(_ p1: Any, _ p2: Any, _ p3: Any, _ p4: Any) -> String {
            return L10n.tr("Localizable", "maya.ekyc.idReview.mismatch.subtitle", String(describing: p1), String(describing: p2), String(describing: p3), String(describing: p4), fallback: "You've already submitted the %@ of your %@. Now, please upload a clear photo of the %@ of your ID. The image you provided is not the %@ of your ID.")
          }
          /// Incorrect Second Image
          internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.mismatch.title", fallback: "Incorrect Second Image")
        }
        internal enum Philsys {
          internal enum Mismatch {
            internal enum Capture {
              /// Please take a photo of your Phil ID Card (Plastic Card) or ePhil ID (Paper ID)
              internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.idReview.philsys.mismatch.capture.subtitle", fallback: "Please take a photo of your Phil ID Card (Plastic Card) or ePhil ID (Paper ID)")
              /// Only Physical IDs are allowed for camera capture
              internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.philsys.mismatch.capture.title", fallback: "Only Physical IDs are allowed for camera capture")
            }
            internal enum Upload {
              /// Upload a new ID
              internal static let retakeTitle = L10n.tr("Localizable", "maya.ekyc.idReview.philsys.mismatch.upload.retakeTitle", fallback: "Upload a new ID")
              /// Please upload a screenshot of your Philippine National IDs from the eGov app or generated from the National ID Website
              internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.idReview.philsys.mismatch.upload.subtitle", fallback: "Please upload a screenshot of your Philippine National IDs from the eGov app or generated from the National ID Website")
              /// Only digital IDs are allowed for upload.
              internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.philsys.mismatch.upload.title", fallback: "Only digital IDs are allowed for upload.")
            }
          }
        }
        internal enum Replace {
          /// Replace
          internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.replace.title", fallback: "Replace")
        }
        internal enum Retake {
          /// Retake
          internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.retake.title", fallback: "Retake")
        }
        internal enum UseThis {
          /// Use this photo
          internal static let title = L10n.tr("Localizable", "maya.ekyc.idReview.useThis.title", fallback: "Use this photo")
        }
      }
      internal enum Maintenance {
        /// Please try again later
        internal static let header = L10n.tr("Localizable", "maya.ekyc.maintenance.header", fallback: "Please try again later")
        /// This feature is currently under maintenance and will be back soon
        internal static let subheader = L10n.tr("Localizable", "maya.ekyc.maintenance.subheader", fallback: "This feature is currently under maintenance and will be back soon")
        internal enum Title {
          /// Maya EKYC - Maintenance
          internal static let kyc = L10n.tr("Localizable", "maya.ekyc.maintenance.title.kyc", fallback: "Upgrade your account")
          /// Update your account
          internal static let rekyc = L10n.tr("Localizable", "maya.ekyc.maintenance.title.rekyc", fallback: "Update your account")
        }
      }
      internal enum NewSelectAnotherID {
        /// SECONDARY ID
        internal static let pickerLabel = L10n.tr("Localizable", "maya.ekyc.newSelectAnotherID.pickerLabel", fallback: "SECONDARY ID")
        /// Two IDs are required
        internal static let pickerSubLabel = L10n.tr("Localizable", "maya.ekyc.newSelectAnotherID.pickerSubLabel", fallback: "Two IDs are required")
        /// Your ID helps us confirm your identity. Please choose a valid ID to submit from the list below:
        internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.newSelectAnotherID.subtitle", fallback: "Your ID helps us confirm your identity. Please choose a valid ID to submit from the list below:")
        /// Select another ID
        internal static let title = L10n.tr("Localizable", "maya.ekyc.newSelectAnotherID.title", fallback: "Select another ID")
      }
      internal enum Reminders {
        /// Note: Please make sure you have a stable internet connection throughout the process. Standard telco rates apply if you’re using a data connection.
        internal static let note = L10n.tr("Localizable", "maya.ekyc.reminders.note", fallback: "Note: Please make sure you have a stable internet connection throughout the process. Standard telco rates apply if you’re using a data connection.")
      }
      internal enum SelectAnotherID {
        /// Secondary IDs
        internal static let pickerLabel = L10n.tr("Localizable", "maya.ekyc.selectAnotherID.pickerLabel", fallback: "Secondary IDs")
        /// Your ID helps us confirm your identity and secure your account. Please submit a valid ID from the list below:
        internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.selectAnotherID.subtitle", fallback: "Your ID helps us confirm your identity and secure your account. Please submit a valid ID from the list below:")
        /// Select one more ID
        internal static let title = L10n.tr("Localizable", "maya.ekyc.selectAnotherID.title", fallback: "Select one more ID")
      }
      internal enum Steps {
        /// It takes just 3
        /// easy steps
        internal static let title = L10n.tr("Localizable", "maya.ekyc.steps.title", fallback: "It takes just 3\neasy steps")
      }
      internal enum Tencent {
        internal enum Error {
          /// Try again
          internal static let action = L10n.tr("Localizable", "maya.ekyc.tencent.error.action", fallback: "Try again")
          /// Something went wrong with your selfie. Please try again.
          internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.tencent.error.subtitle", fallback: "Something went wrong with your selfie. Please try again.")
          /// Maya EKYC - Tencent
          internal static let title = L10n.tr("Localizable", "maya.ekyc.tencent.error.title", fallback: "Let's try another take.")
          internal enum IncompleteFace {
            /// Please make sure your whole face is visible. Remove any headwear, face masks or glasses
            internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.tencent.error.incompleteFace.subtitle", fallback: "Please make sure your whole face is visible. Remove any headwear, face masks or glasses")
          }
          internal enum SeveralFaces {
            /// Please make sure there is no one else visible on your selfie
            internal static let subtitle = L10n.tr("Localizable", "maya.ekyc.tencent.error.severalFaces.subtitle", fallback: "Please make sure there is no one else visible on your selfie")
          }
        }
        internal enum Selfie {
          internal enum Error {
            internal enum Default {
              /// Let’s try another take.
              internal static let title = L10n.tr("Localizable", "maya.ekyc.tencent.selfie.error.default.title", fallback: "Let’s try another take.")
            }
            internal enum Spiel {
              /// Something went wrong with your selfie. Please try again.
              internal static let `default` = L10n.tr("Localizable", "maya.ekyc.tencent.selfie.error.spiel.default", fallback: "Something went wrong with your selfie. Please try again.")
            }
          }
        }
      }
      internal enum UpgradeType {
        internal enum Filipino {
          /// I am a Filipino adult
          internal static let adult = L10n.tr("Localizable", "maya.ekyc.upgradeType.filipino.adult", fallback: "I am a Filipino adult")
          /// I am a Filipino below 18 years old
          internal static let minor = L10n.tr("Localizable", "maya.ekyc.upgradeType.filipino.minor", fallback: "I am a Filipino below 18 years old")
        }
        internal enum Foreigner {
          /// I am an adult foreigner
          internal static let adult = L10n.tr("Localizable", "maya.ekyc.upgradeType.foreigner.adult", fallback: "I am an adult foreigner")
          /// I am a foreigner below 18 years old
          internal static let minor = L10n.tr("Localizable", "maya.ekyc.upgradeType.foreigner.minor", fallback: "I am a foreigner below 18 years old")
        }
      }
      internal enum Welcome {
        internal enum ButtonTitle {
          /// Maya EKYC
          internal static let `default` = L10n.tr("Localizable", "maya.ekyc.welcome.buttonTitle.default", fallback: "Let's go")
          /// Upgrade for free
          internal static let upgradeForFree = L10n.tr("Localizable", "maya.ekyc.welcome.buttonTitle.upgradeForFree", fallback: "Upgrade for free")
          /// Verify now
          internal static let verifyNow = L10n.tr("Localizable", "maya.ekyc.welcome.buttonTitle.verifyNow", fallback: "Verify now")
        }
      }
    }
    internal enum HelpCenter {
      internal enum ReachHelpCenter {
        /// Maya Help Center
        internal static let spiel = L10n.tr("Localizable", "maya.helpCenter.reachHelpCenter.spiel", fallback: "Reach us anytime in our Help Center")
      }
    }
    internal enum Mfa {
      internal enum Common {
        internal enum ThatsNotIt {
          /// MFA Framework
          internal static let title = L10n.tr("Localizable", "maya.mfa.common.thatsNotIt.title", fallback: "That's not it")
        }
      }
      internal enum Fallback {
        internal enum AlertBox {
          /// Complete your verification with an OTP.
          internal static let message = L10n.tr("Localizable", "maya.mfa.fallback.alertBox.message", fallback: "Complete your verification with an OTP.")
          /// You're almost there!
          internal static let title = L10n.tr("Localizable", "maya.mfa.fallback.alertBox.title", fallback: "You're almost there!")
        }
        internal enum Button {
          internal enum Send {
            internal enum Me {
              internal enum The {
                internal enum Otp {
                  /// Send me the OTP
                  internal static let title = L10n.tr("Localizable", "maya.mfa.fallback.button.send.me.the.otp.title", fallback: "Send me the OTP")
                }
              }
            }
          }
          internal enum Take {
            internal enum Me {
              internal enum Back {
                /// Take me back
                internal static let title = L10n.tr("Localizable", "maya.mfa.fallback.button.take.me.back.title", fallback: "Take me back")
              }
            }
          }
        }
        internal enum NoFaceData {
          /// We had a problem authenticating you.
          internal static let message = L10n.tr("Localizable", "maya.mfa.fallback.noFaceData.message", fallback: "We had a problem authenticating you.")
        }
        internal enum PrimaryButton {
          /// Verify via OTP
          internal static let title = L10n.tr("Localizable", "maya.mfa.fallback.primaryButton.title", fallback: "Verify via OTP")
        }
      }
      internal enum Max {
        internal enum Attempt {
          internal enum Error {
            /// Max attempts reached
            internal static let title = L10n.tr("Localizable", "maya.mfa.max.attempt.error.title", fallback: "Max attempts reached")
          }
        }
      }
      internal enum Mismatch {
        internal enum AlertBox {
          /// We were unable to verify your identity. Please retry again or contact customer support for assistance.
          internal static let message = L10n.tr("Localizable", "maya.mfa.mismatch.alertBox.message", fallback: "We were unable to verify your identity. Please retry again or contact customer support for assistance.")
        }
        internal enum PrimaryButton {
          /// Submit a support ticket
          internal static let title = L10n.tr("Localizable", "maya.mfa.mismatch.primaryButton.title", fallback: "Submit a support ticket")
        }
      }
      internal enum Otp {
        /// 000000
        internal static let placeholder = L10n.tr("Localizable", "maya.mfa.otp.placeholder", fallback: "000000")
        /// MFA Otp Screen
        internal static let title = L10n.tr("Localizable", "maya.mfa.otp.title", fallback: "Enter the OTP")
        internal enum InlineValidation {
          /// This OTP is invalid. Please try again.
          internal static let invalid = L10n.tr("Localizable", "maya.mfa.otp.inlineValidation.invalid", fallback: "This OTP is invalid. Please try again.")
          /// OTP limit reached. Please try again later.
          internal static let limit = L10n.tr("Localizable", "maya.mfa.otp.inlineValidation.limit", fallback: "OTP limit reached. Please try again later.")
        }
        internal enum MaxAttempts {
          /// You have exceeded the attempts for authentication. Please retry.
          internal static let message = L10n.tr("Localizable", "maya.mfa.otp.maxAttempts.message", fallback: "You have exceeded the attempts for authentication. Please retry.")
        }
        internal enum Spiel {
          /// Resend OTP
          internal static let resendOTP = L10n.tr("Localizable", "maya.mfa.otp.spiel.resendOTP", fallback: "Resend OTP")
          /// Resend OTP in %ld %@
          internal static func resendOTPIn(_ p1: Int, _ p2: Any) -> String {
            return L10n.tr("Localizable", "maya.mfa.otp.spiel.resendOTPIn", p1, String(describing: p2), fallback: "Resend OTP in %ld %@")
          }
          /// second
          internal static let second = L10n.tr("Localizable", "maya.mfa.otp.spiel.second", fallback: "second")
          /// seconds
          internal static let seconds = L10n.tr("Localizable", "maya.mfa.otp.spiel.seconds", fallback: "seconds")
        }
        internal enum Subtitle {
          /// Please enter the one-time PIN (OTP) sent to your registered email %@
          internal static func email(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.mfa.otp.subtitle.email", String(describing: p1), fallback: "Please enter the one-time PIN (OTP) sent to your registered email %@")
          }
          /// Please enter the one-time PIN (OTP) sent to %@
          internal static func sms(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.mfa.otp.subtitle.sms", String(describing: p1), fallback: "Please enter the one-time PIN (OTP) sent to %@")
          }
        }
      }
      internal enum Retry {
        internal enum AlertBox {
          /// Let's try another take
          internal static let title = L10n.tr("Localizable", "maya.mfa.retry.alertBox.title", fallback: "Let's try another take")
        }
        internal enum PrimaryButton {
          /// Take video selfie
          internal static let title = L10n.tr("Localizable", "maya.mfa.retry.primaryButton.title", fallback: "Take video selfie")
        }
        internal enum SecondaryButton {
          /// Close
          internal static let title = L10n.tr("Localizable", "maya.mfa.retry.secondaryButton.title", fallback: "Close")
        }
      }
    }
    internal enum MgmV2 {
      internal enum Button {
        /// MGM 2.0
        internal static let updateNow = L10n.tr("Localizable", "maya.mgmV2.button.updateNow", fallback: "Update now")
      }
      internal enum Spiel {
        /// Update your app to enjoy the newest features and fixes.
        internal static let forceUpdate = L10n.tr("Localizable", "maya.mgmV2.spiel.forceUpdate", fallback: "Update your app to enjoy the newest features and fixes.")
      }
      internal enum Title {
        /// Invite a friend
        internal static let inviteAFriend = L10n.tr("Localizable", "maya.mgmV2.title.inviteAFriend", fallback: "Invite a friend")
        /// Update required
        internal static let updateRequired = L10n.tr("Localizable", "maya.mgmV2.title.updateRequired", fallback: "Update required")
      }
    }
    internal enum Missions {
      internal enum Inprogress {
        internal enum Section {
          /// In Progress
          internal static let title = L10n.tr("Localizable", "maya.missions.inprogress.section.title", fallback: "In Progress")
        }
      }
      internal enum List {
        internal enum No {
          internal enum Completed {
            /// All of your completed missions will be shown in this page.
            internal static let info = L10n.tr("Localizable", "maya.missions.list.no.completed.info", fallback: "All of your completed missions will be shown in this page.")
            /// No completed missions
            internal static let title = L10n.tr("Localizable", "maya.missions.list.no.completed.title", fallback: "No completed missions")
          }
          internal enum Inprogress {
            /// All of your missions in-progress will be shown in this page.
            internal static let info = L10n.tr("Localizable", "maya.missions.list.no.inprogress.info", fallback: "All of your missions in-progress will be shown in this page.")
            /// Maya Missions
            internal static let title = L10n.tr("Localizable", "maya.missions.list.no.inprogress.title", fallback: "No missions in progress")
          }
        }
      }
      internal enum Tasks {
        internal enum Completed {
          /// %d of %d tasks completed
          internal static func spiel(_ p1: Int, _ p2: Int) -> String {
            return L10n.tr("Localizable", "maya.missions.tasks.completed.spiel", p1, p2, fallback: "%d of %d tasks completed")
          }
        }
      }
    }
    internal enum New {
      internal enum Credit {
        internal enum Activation {
          internal enum Acknowledge {
            internal enum Button {
              /// I agree and acknowledge
              internal static let title = L10n.tr("Localizable", "maya.new.credit.activation.acknowledge.button.title", fallback: "I agree and acknowledge")
            }
          }
          internal enum ChangeDate {
            /// Change date
            internal static let title = L10n.tr("Localizable", "maya.new.credit.activation.changeDate.title", fallback: "Change date")
          }
          internal enum Expires {
            /// New Maya Easy Credit Activations
            internal static func on(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.new.credit.activation.expires.on", String(describing: p1), fallback: "Offer ends %@")
            }
          }
          internal enum IdentityStatus {
            /// No email
            internal static let noEmail = L10n.tr("Localizable", "maya.new.credit.activation.identityStatus.noEmail", fallback: "No email")
            /// Unverified
            internal static let unverified = L10n.tr("Localizable", "maya.new.credit.activation.identityStatus.unverified", fallback: "Unverified")
            /// Verified
            internal static let verified = L10n.tr("Localizable", "maya.new.credit.activation.identityStatus.verified", fallback: "Verified")
          }
          internal enum Interest {
            /// %@%% against used limit + DST
            internal static func rate(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.new.credit.activation.interest.rate", String(describing: p1), fallback: "%@%% against used limit + DST")
            }
          }
          internal enum No {
            /// Provide and verify your email so you can receive a copy of your billing statement.
            internal static let email = L10n.tr("Localizable", "maya.new.credit.activation.no.email", fallback: "Provide and verify your email so you can receive a copy of your billing statement.")
          }
          internal enum NotVerified {
            /// Verify your email so you can receive a copy of your billing statement to this email.
            internal static let email = L10n.tr("Localizable", "maya.new.credit.activation.notVerified.email", fallback: "Verify your email so you can receive a copy of your billing statement to this email.")
          }
          internal enum Verified {
            /// Your billing statement will be sent to this email.
            internal static let email = L10n.tr("Localizable", "maya.new.credit.activation.verified.email", fallback: "Your billing statement will be sent to this email.")
          }
        }
      }
    }
    internal enum P2m {
      internal enum FlowProgressBar {
        /// %@/2
        internal static func progress(_ p1: Any) -> String {
          return L10n.tr("Localizable", "maya.p2m.flowProgressBar.progress", String(describing: p1), fallback: "%@/2")
        }
      }
      internal enum Paying {
        internal enum For {
          internal enum Maximum {
            /// Maya P2M
            internal static func amount(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.p2m.paying.for.maximum.amount", String(describing: p1), fallback: "Maximum amount of ₱%@")
            }
          }
        }
      }
      internal enum Payment {
        internal enum Failure {
          /// We tried but failed with your payment. No worries, refund is on its way.
          internal static let spiel = L10n.tr("Localizable", "maya.p2m.payment.failure.spiel", fallback: "We tried but failed with your payment. No worries, refund is on its way.")
        }
      }
    }
    internal enum Paybills {
      internal enum Biller {
        internal enum Confirmation {
          internal enum Bill {
            internal enum Ammount {
              /// Amount
              internal static let placeholder = L10n.tr("Localizable", "maya.paybills.biller.confirmation.bill.ammount.placeholder", fallback: "Amount")
            }
            internal enum Rdo {
              internal enum Code {
                /// RDO code
                internal static let placeholder = L10n.tr("Localizable", "maya.paybills.biller.confirmation.bill.rdo.code.placeholder", fallback: "RDO code")
              }
            }
            internal enum Tin {
              /// TIN
              internal static let placeholder = L10n.tr("Localizable", "maya.paybills.biller.confirmation.bill.tin.placeholder", fallback: "TIN")
              internal enum Branch {
                internal enum Code {
                  /// TIN branch code
                  internal static let placeholder = L10n.tr("Localizable", "maya.paybills.biller.confirmation.bill.tin.branch.code.placeholder", fallback: "TIN branch code")
                }
              }
            }
          }
          internal enum Pay {
            internal enum Button {
              /// Pay
              internal static let title = L10n.tr("Localizable", "maya.paybills.biller.confirmation.pay.button.title", fallback: "Pay")
            }
          }
        }
        internal enum Form {
          internal enum Fields {
            /// Enter %@
            internal static func placeholder(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.paybills.biller.form.fields.placeholder", String(describing: p1), fallback: "Enter %@")
            }
            internal enum Customer {
              internal enum Account {
                internal enum Number {
                  /// Customer account number
                  internal static let hint = L10n.tr("Localizable", "maya.paybills.biller.form.fields.customer.account.number.hint", fallback: "Customer account number")
                }
              }
            }
            internal enum Enter {
              internal enum Tin {
                internal enum Branch {
                  /// Enter TIN branch code
                  internal static let code = L10n.tr("Localizable", "maya.paybills.biller.form.fields.enter.tin.branch.code", fallback: "Enter TIN branch code")
                }
              }
            }
            internal enum For {
              internal enum Bill {
                /// Amount
                internal static let amount = L10n.tr("Localizable", "maya.paybills.biller.form.fields.for.bill.amount", fallback: "Amount")
                /// TIN
                internal static let tin = L10n.tr("Localizable", "maya.paybills.biller.form.fields.for.bill.tin", fallback: "TIN")
                internal enum Account {
                  /// Account number
                  internal static let number = L10n.tr("Localizable", "maya.paybills.biller.form.fields.for.bill.account.number", fallback: "Account number")
                  internal enum Number {
                    /// Enter account number
                    internal static let placeholder = L10n.tr("Localizable", "maya.paybills.biller.form.fields.for.bill.account.number.placeholder", fallback: "Enter account number")
                  }
                }
                internal enum Amount {
                  /// Enter amount
                  internal static let placeholder = L10n.tr("Localizable", "maya.paybills.biller.form.fields.for.bill.amount.placeholder", fallback: "Enter amount")
                }
                internal enum Customer {
                  internal enum Account {
                    /// Customer account number
                    internal static let number = L10n.tr("Localizable", "maya.paybills.biller.form.fields.for.bill.customer.account.number", fallback: "Customer account number")
                    internal enum Number {
                      /// Enter customer account number
                      internal static let placeholder = L10n.tr("Localizable", "maya.paybills.biller.form.fields.for.bill.customer.account.number.placeholder", fallback: "Enter customer account number")
                    }
                  }
                }
                internal enum Tin {
                  /// Enter TIN
                  internal static let placeholder = L10n.tr("Localizable", "maya.paybills.biller.form.fields.for.bill.tin.placeholder", fallback: "Enter TIN")
                }
              }
              internal enum Maximum {
                /// You only have %@ in your wallet.
                internal static func amount(_ p1: Any) -> String {
                  return L10n.tr("Localizable", "maya.paybills.biller.form.fields.for.maximum.amount", String(describing: p1), fallback: "You only have %@ in your wallet.")
                }
              }
              internal enum Wrong {
                internal enum Number {
                  internal enum Of {
                    /// Please enter a valid account number with 5 to 30 characters only.
                    internal static let characters = L10n.tr("Localizable", "maya.paybills.biller.form.fields.for.wrong.number.of.characters", fallback: "Please enter a valid account number with 5 to 30 characters only.")
                  }
                }
                internal enum Tin {
                  internal enum Number {
                    internal enum Of {
                      /// Please enter a valid TIN with 5 to 30 characters only.
                      internal static let characters = L10n.tr("Localizable", "maya.paybills.biller.form.fields.for.wrong.tin.number.of.characters", fallback: "Please enter a valid TIN with 5 to 30 characters only.")
                    }
                  }
                }
              }
              internal enum Zero {
                /// Amount must be greater than 0.
                internal static let amount = L10n.tr("Localizable", "maya.paybills.biller.form.fields.for.zero.amount", fallback: "Amount must be greater than 0.")
              }
            }
            internal enum Select {
              /// Select %@
              internal static func placeholder(_ p1: Any) -> String {
                return L10n.tr("Localizable", "maya.paybills.biller.form.fields.select.placeholder", String(describing: p1), fallback: "Select %@")
              }
              internal enum Rdo {
                internal enum Code {
                  /// Select RDO code
                  internal static let placeholder = L10n.tr("Localizable", "maya.paybills.biller.form.fields.select.rdo.code.placeholder", fallback: "Select RDO code")
                }
              }
              internal enum Return {
                internal enum Period {
                  /// Select a return date
                  internal static let placeholder = L10n.tr("Localizable", "maya.paybills.biller.form.fields.select.return.period.placeholder", fallback: "Select a return date")
                }
              }
            }
          }
        }
        internal enum Subtext {
          /// Accepts Maya Easy Credit
          internal static let mayaCredit = L10n.tr("Localizable", "maya.paybills.biller.subtext.mayaCredit", fallback: "Accepts Maya Easy Credit")
          /// Service temporarily unavailable
          internal static let serviceUnavailable = L10n.tr("Localizable", "maya.paybills.biller.subtext.serviceUnavailable", fallback: "Service temporarily unavailable")
        }
      }
      internal enum Bottomsheet {
        internal enum Action {
          internal enum MayaCredit {
            /// Apply Now
            internal static let applyNow = L10n.tr("Localizable", "maya.paybills.bottomsheet.action.mayaCredit.applyNow", fallback: "Apply Now")
          }
        }
        internal enum Close {
          /// Close
          internal static let mayaCredit = L10n.tr("Localizable", "maya.paybills.bottomsheet.close.mayaCredit", fallback: "Close")
        }
        internal enum Info {
          /// Don’t have Maya Easy Credit yet? Apply now and never miss any of your bills again!
          internal static let mayaCredit = L10n.tr("Localizable", "maya.paybills.bottomsheet.info.mayaCredit", fallback: "Don’t have Maya Easy Credit yet? Apply now and never miss any of your bills again!")
          internal enum MayaCredit {
            /// Already have Maya Easy Credit? You're all set! We'll let you know once we've enabled paying bills with Maya Easy Credit! 
            /// If not, tap "Activate now" below to sign up for Maya Easy Credit.
            internal static let soon = L10n.tr("Localizable", "maya.paybills.bottomsheet.info.mayaCredit.soon", fallback: "Already have Maya Easy Credit? You're all set! We'll let you know once we've enabled paying bills with Maya Easy Credit! \nIf not, tap \"Activate now\" below to sign up for Maya Easy Credit.")
            /// We’ll send you a notification as soon as this becomes a payment option for you.
            /// 
            /// Don’t have Maya Easy Credit yet? Apply now and never miss any of your bills again!
            internal static let teaser = L10n.tr("Localizable", "maya.paybills.bottomsheet.info.mayaCredit.teaser", fallback: "We’ll send you a notification as soon as this becomes a payment option for you.\n\nDon’t have Maya Easy Credit yet? Apply now and never miss any of your bills again!")
          }
        }
        internal enum Title {
          /// Pay your bills on time with Maya Easy Credit
          internal static let mayaCredit = L10n.tr("Localizable", "maya.paybills.bottomsheet.title.mayaCredit", fallback: "Pay your bills on time with Maya Easy Credit")
        }
      }
      internal enum Confirmation {
        /// Bill amount
        internal static let billAmount = L10n.tr("Localizable", "maya.paybills.confirmation.billAmount", fallback: "Bill amount")
        /// Biller convenience fee
        internal static let billerConvenienceFee = L10n.tr("Localizable", "maya.paybills.confirmation.billerConvenienceFee", fallback: "Biller convenience fee")
        /// Confirm payment
        internal static let title = L10n.tr("Localizable", "maya.paybills.confirmation.title", fallback: "Confirm payment")
        internal enum Bottomsheet {
          internal enum Favorite {
            /// Favorite's details failed to update
            internal static let failedUpdate = L10n.tr("Localizable", "maya.paybills.confirmation.bottomsheet.favorite.failedUpdate", fallback: "Favorite's details failed to update")
            /// We’re now processing this bills payment to account ending %@.
            internal static func message(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.paybills.confirmation.bottomsheet.favorite.message", String(describing: p1), fallback: "We’re now processing this bills payment to account ending %@.")
            }
            /// Favorite's details have been saved
            internal static let saved = L10n.tr("Localizable", "maya.paybills.confirmation.bottomsheet.favorite.saved", fallback: "Favorite's details have been saved")
          }
        }
        internal enum Success {
          internal enum Bottomsheet {
            /// Save details to favorites
            internal static let saveToFavorites = L10n.tr("Localizable", "maya.paybills.confirmation.success.bottomsheet.saveToFavorites", fallback: "Save details to favorites")
            /// We’re now processing this bill payment to %@
            internal static func subtitle(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.paybills.confirmation.success.bottomsheet.subtitle", String(describing: p1), fallback: "We’re now processing this bill payment to %@")
            }
            /// You’re almost there!
            internal static let title = L10n.tr("Localizable", "maya.paybills.confirmation.success.bottomsheet.title", fallback: "You’re almost there!")
            internal enum Done {
              internal enum Button {
                /// Done
                internal static let title = L10n.tr("Localizable", "maya.paybills.confirmation.success.bottomsheet.done.button.title", fallback: "Done")
              }
            }
            internal enum Viewreceipt {
              internal enum Button {
                /// View receipt
                internal static let title = L10n.tr("Localizable", "maya.paybills.confirmation.success.bottomsheet.viewreceipt.button.title", fallback: "View receipt")
              }
            }
          }
        }
        internal enum Viewinititialreceipt {
          internal enum Button {
            /// View initial receipt
            internal static let title = L10n.tr("Localizable", "maya.paybills.confirmation.viewinititialreceipt.button.title", fallback: "View initial receipt")
          }
        }
      }
      internal enum Error {
        /// %@ is currently not available. Please check back again later or pay another bill in the meantime.
        internal static func description(_ p1: Any) -> String {
          return L10n.tr("Localizable", "maya.paybills.error.description", String(describing: p1), fallback: "%@ is currently not available. Please check back again later or pay another bill in the meantime.")
        }
      }
      internal enum FeatureBanner {
        internal enum Spiel {
          /// Pay your bills with Maya Easy Credit
          internal static let mayaCredit = L10n.tr("Localizable", "maya.paybills.featureBanner.spiel.mayaCredit", fallback: "Pay your bills with Maya Easy Credit")
          internal enum MayaCredit {
            /// Pay your bills with Maya Easy Credit soon
            internal static let soon = L10n.tr("Localizable", "maya.paybills.featureBanner.spiel.mayaCredit.soon", fallback: "Pay your bills with Maya Easy Credit soon")
          }
        }
      }
      internal enum Form {
        internal enum Amount {
          /// Maya Pay bills
          internal static let `default` = L10n.tr("Localizable", "maya.paybills.form.amount.default", fallback: "₱0")
        }
      }
      internal enum Receipt {
        /// Bill amount
        internal static let billAmount = L10n.tr("Localizable", "maya.paybills.receipt.billAmount", fallback: "Bill amount")
        /// Biller convenience fee
        internal static let billerConvenienceFee = L10n.tr("Localizable", "maya.paybills.receipt.billerConvenienceFee", fallback: "Biller convenience fee")
        /// Biller's fee
        internal static let billersFee = L10n.tr("Localizable", "maya.paybills.receipt.billersFee", fallback: "Biller's fee")
      }
      internal enum Reminder {
        /// PAY TODAY
        internal static let payToday = L10n.tr("Localizable", "maya.paybills.reminder.payToday", fallback: "PAY TODAY")
        /// REMINDER SET
        internal static let reminderSet = L10n.tr("Localizable", "maya.paybills.reminder.reminderSet", fallback: "REMINDER SET")
      }
      internal enum SampleReminder {
        /// We will notify you to pay your bills on the day you select.
        internal static let description = L10n.tr("Localizable", "maya.paybills.sampleReminder.description", fallback: "We will notify you to pay your bills on the day you select.")
      }
      internal enum Savetofavorites {
        /// Pay bills details
        internal static let spiel = L10n.tr("Localizable", "maya.paybills.savetofavorites.spiel", fallback: "Pay bills details")
        internal enum Success {
          /// You can now easily pay bills under favorites.
          internal static let info = L10n.tr("Localizable", "maya.paybills.savetofavorites.success.info", fallback: "You can now easily pay bills under favorites.")
          /// Added to your favorites!
          internal static let title = L10n.tr("Localizable", "maya.paybills.savetofavorites.success.title", fallback: "Added to your favorites!")
        }
      }
      internal enum Search {
        /// Search for billers or merchants
        internal static let placeholder = L10n.tr("Localizable", "maya.paybills.search.placeholder", fallback: "Search for billers or merchants")
      }
    }
    internal enum Privacy {
      internal enum ConsentChange {
        /// Do you want to save the changes you’ve made to your settings?
        internal static let message = L10n.tr("Localizable", "maya.privacy.consentChange.message", fallback: "Do you want to save the changes you’ve made to your settings?")
        /// Maya Data Personalization
        internal static let title = L10n.tr("Localizable", "maya.privacy.consentChange.title", fallback: "Save changes?")
      }
    }
    internal enum Receipt {
      internal enum Detail {
        /// Code
        internal static let code = L10n.tr("Localizable", "maya.receipt.detail.code", fallback: "Code")
        /// Destination
        internal static let destination = L10n.tr("Localizable", "maya.receipt.detail.destination", fallback: "Destination")
        /// Purpose
        internal static let purpose = L10n.tr("Localizable", "maya.receipt.detail.purpose", fallback: "Purpose")
        /// Share Treats
        internal static let shareTreats = L10n.tr("Localizable", "maya.receipt.detail.shareTreats", fallback: "Share Treats")
        /// Source
        internal static let source = L10n.tr("Localizable", "maya.receipt.detail.source", fallback: "Source")
        internal enum Payment {
          /// Payment method
          internal static let method = L10n.tr("Localizable", "maya.receipt.detail.payment.method", fallback: "Payment method")
        }
        internal enum Powered {
          internal enum By {
            /// Powered by Share Treats
            internal static let shareTreats = L10n.tr("Localizable", "maya.receipt.detail.powered.by.shareTreats", fallback: "Powered by Share Treats")
          }
        }
        internal enum Source {
          /// Maya Easy Credit
          internal static let mayaCredit = L10n.tr("Localizable", "maya.receipt.detail.source.mayaCredit", fallback: "Maya Easy Credit")
          /// Pay in 4 Installments
          internal static let payIn4Installments = L10n.tr("Localizable", "maya.receipt.detail.source.payIn4Installments", fallback: "Pay in 4 Installments")
          /// My Wallet
          internal static let wallet = L10n.tr("Localizable", "maya.receipt.detail.source.wallet", fallback: "My Wallet")
        }
        internal enum Transaction {
          /// Transaction details
          internal static let details = L10n.tr("Localizable", "maya.receipt.detail.transaction.details", fallback: "Transaction details")
          /// Transaction fee
          internal static let fee = L10n.tr("Localizable", "maya.receipt.detail.transaction.fee", fallback: "Transaction fee")
          /// Gateway
          internal static let gateway = L10n.tr("Localizable", "maya.receipt.detail.transaction.gateway", fallback: "Gateway")
          /// Payment ID
          internal static let paymentId = L10n.tr("Localizable", "maya.receipt.detail.transaction.paymentId", fallback: "Payment ID")
          /// QR Ph Invoice No.
          internal static let qrphInvoiceNumber = L10n.tr("Localizable", "maya.receipt.detail.transaction.qrphInvoiceNumber", fallback: "QR Ph Invoice No.")
          /// Reference ID
          internal static let referenceID = L10n.tr("Localizable", "maya.receipt.detail.transaction.referenceID", fallback: "Reference ID")
          /// Purchased
          internal static let title = L10n.tr("Localizable", "maya.receipt.detail.transaction.title", fallback: "Purchased")
          /// Trace number
          internal static let traceNumber = L10n.tr("Localizable", "maya.receipt.detail.transaction.traceNumber", fallback: "Trace number")
        }
      }
      internal enum Status {
        /// Processing
        internal static let processing = L10n.tr("Localizable", "maya.receipt.status.processing", fallback: "Processing")
      }
      internal enum Title {
        /// Maya Receipt
        internal static let banktransfer = L10n.tr("Localizable", "maya.receipt.title.banktransfer", fallback: "Bank transfer to")
      }
    }
    internal enum Referral {
      internal enum InsertCode {
        /// Submit
        internal static let submit = L10n.tr("Localizable", "maya.referral.insertCode.submit", fallback: "Submit")
        internal enum Error {
          /// Whoops! Looks like your invite code is invalid. Please try again.
          internal static let message = L10n.tr("Localizable", "maya.referral.insertCode.error.message", fallback: "Whoops! Looks like your invite code is invalid. Please try again.")
        }
        internal enum Success {
          /// Get your free reward by upgrading your account and completing the required tasks. Check the Missions page for more details.
          internal static let description = L10n.tr("Localizable", "maya.referral.insertCode.success.description", fallback: "Get your free reward by upgrading your account and completing the required tasks. Check the Missions page for more details.")
          /// Code looks good!
          internal static let title = L10n.tr("Localizable", "maya.referral.insertCode.success.title", fallback: "Code looks good!")
          internal enum Action {
            /// Go to home
            internal static let home = L10n.tr("Localizable", "maya.referral.insertCode.success.action.home", fallback: "Go to home")
            /// Upgrade now
            internal static let upgrade = L10n.tr("Localizable", "maya.referral.insertCode.success.action.upgrade", fallback: "Upgrade now")
          }
        }
        internal enum TextField {
          /// Invite code
          internal static let helper = L10n.tr("Localizable", "maya.referral.insertCode.textField.helper", fallback: "Invite code")
          /// Enter invite code
          internal static let placeholder = L10n.tr("Localizable", "maya.referral.insertCode.textField.placeholder", fallback: "Enter invite code")
        }
      }
      internal enum ShareCode {
        /// Invite code copied
        internal static let copied = L10n.tr("Localizable", "maya.referral.shareCode.copied", fallback: "Invite code copied")
        /// Maya referral
        internal static let info = L10n.tr("Localizable", "maya.referral.shareCode.info", fallback: "Get a ₱100.00 voucher each time a friend uses your invite code to join Maya, upgrades their account, and completes their first transaction.\n\nLearn more at maya.ph/refer")
      }
    }
    internal enum Rekyc {
      internal enum Nudge {
        /// In compliance with BSP regulations, please update your account to continue using these features:
        internal static let subtitle = L10n.tr("Localizable", "maya.rekyc.nudge.subtitle", fallback: "In compliance with BSP regulations, please update your account to continue using these features:")
        /// Maya ReKYC
        internal static let title = L10n.tr("Localizable", "maya.rekyc.nudge.title", fallback: "Please update your account")
        internal enum PrimaryButton {
          /// Update now
          internal static let title = L10n.tr("Localizable", "maya.rekyc.nudge.primaryButton.title", fallback: "Update now")
        }
        internal enum SecondaryButton {
          /// I'll do it later
          internal static let title = L10n.tr("Localizable", "maya.rekyc.nudge.secondaryButton.title", fallback: "I'll do it later")
        }
      }
      internal enum UnderReview {
        /// Thanks for submitting your additional information.
        internal static let subtitle = L10n.tr("Localizable", "maya.rekyc.underReview.subtitle", fallback: "Thanks for submitting your additional information.")
        /// We'll take it from here.
        internal static let title = L10n.tr("Localizable", "maya.rekyc.underReview.title", fallback: "We'll take it from here.")
        internal enum Status {
          /// Reference Number: 
          internal static let referenceNumber = L10n.tr("Localizable", "maya.rekyc.underReview.status.referenceNumber", fallback: "Reference Number: ")
          /// Your account update is under review. Successfully updated accounts will be able to enjoy all the features that Maya has to offer.
          internal static let subtitle = L10n.tr("Localizable", "maya.rekyc.underReview.status.subtitle", fallback: "Your account update is under review. Successfully updated accounts will be able to enjoy all the features that Maya has to offer.")
          /// Account status:
          internal static let title = L10n.tr("Localizable", "maya.rekyc.underReview.status.title", fallback: "Account status:")
          internal enum Subtitle {
            /// under review
            internal static let highlightedPhrase = L10n.tr("Localizable", "maya.rekyc.underReview.status.subtitle.highlightedPhrase", fallback: "under review")
          }
        }
        internal enum Title {
          /// here
          internal static let highlightedPhrase = L10n.tr("Localizable", "maya.rekyc.underReview.title.highlightedPhrase", fallback: "here")
        }
      }
    }
    internal enum Scanner {
      internal enum Instructions {
        /// Maya Scanner
        internal static let `default` = L10n.tr("Localizable", "maya.scanner.instructions.default", fallback: "Align QR Code to scan.")
        internal enum Invalid {
          /// QR Code Invalid. 
          /// Please scan another code.
          internal static let qr = L10n.tr("Localizable", "maya.scanner.instructions.invalid.qr", fallback: "QR Code Invalid. \nPlease scan another code.")
          internal enum Photo {
            internal enum Error {
              /// We are unable to read the QR you selected. Please upload a clearer image of the QR Ph code and try again.
              internal static let message = L10n.tr("Localizable", "maya.scanner.instructions.invalid.photo.error.message", fallback: "We are unable to read the QR you selected. Please upload a clearer image of the QR Ph code and try again.")
              /// Upload QR error
              internal static let title = L10n.tr("Localizable", "maya.scanner.instructions.invalid.photo.error.title", fallback: "Upload QR error")
            }
          }
        }
      }
      internal enum Invalid {
        /// Bills QR not supported
        internal static let bills = L10n.tr("Localizable", "maya.scanner.invalid.bills", fallback: "Bills QR not supported")
        /// Please scan a valid QR Code
        internal static let qr = L10n.tr("Localizable", "maya.scanner.invalid.qr", fallback: "Please scan a valid QR Code")
      }
    }
    internal enum Settings {
      internal enum Biometrics {
        internal enum Disable {
          internal enum Generic {
            /// Please enter your password to confirm that you are disabling %@ login
            internal static func description(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.settings.biometrics.disable.generic.description", String(describing: p1), fallback: "Please enter your password to confirm that you are disabling %@ login")
            }
            /// Disable %@ login
            internal static func title(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.settings.biometrics.disable.generic.title", String(describing: p1), fallback: "Disable %@ login")
            }
          }
        }
        internal enum Enable {
          internal enum Generic {
            /// Please enter your password to confirm that you are enabling %@ login
            internal static func description(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.settings.biometrics.enable.generic.description", String(describing: p1), fallback: "Please enter your password to confirm that you are enabling %@ login")
            }
            /// Enable %@ login
            internal static func title(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.settings.biometrics.enable.generic.title", String(describing: p1), fallback: "Enable %@ login")
            }
          }
        }
        internal enum Error {
          /// Whoops! Looks like your password is incorrect. Please try again.
          internal static let description = L10n.tr("Localizable", "maya.settings.biometrics.error.description", fallback: "Whoops! Looks like your password is incorrect. Please try again.")
        }
      }
      internal enum ChangePassword {
        internal enum Placeholder {
          /// Enter new password
          internal static let confirmNewPassword = L10n.tr("Localizable", "maya.settings.changePassword.placeholder.confirmNewPassword", fallback: "Enter new password")
          /// Enter current password
          internal static let currentPassword = L10n.tr("Localizable", "maya.settings.changePassword.placeholder.currentPassword", fallback: "Enter current password")
          /// Enter new password
          internal static let newPassword = L10n.tr("Localizable", "maya.settings.changePassword.placeholder.newPassword", fallback: "Enter new password")
        }
        internal enum Title {
          /// Confirm new password
          internal static let confirmNewPassword = L10n.tr("Localizable", "maya.settings.changePassword.title.confirmNewPassword", fallback: "Confirm new password")
          /// Current password
          internal static let currentPassword = L10n.tr("Localizable", "maya.settings.changePassword.title.currentPassword", fallback: "Current password")
          /// New password
          internal static let newPassword = L10n.tr("Localizable", "maya.settings.changePassword.title.newPassword", fallback: "New password")
        }
        internal enum Validation {
          /// Submitted password is the same as the current
          internal static let newPasswordSameAsCurrent = L10n.tr("Localizable", "maya.settings.changePassword.validation.newPasswordSameAsCurrent", fallback: "Submitted password is the same as the current")
          /// Does not match your new password
          internal static let retypedPasswordNotMatch = L10n.tr("Localizable", "maya.settings.changePassword.validation.retypedPasswordNotMatch", fallback: "Does not match your new password")
        }
      }
      internal enum Menu {
        internal enum Title {
          /// %@ login
          internal static func biometryLogin(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.settings.menu.title.biometryLogin", String(describing: p1), fallback: "%@ login")
          }
          /// Change mobile number
          internal static let changeNumber = L10n.tr("Localizable", "maya.settings.menu.title.changeNumber", fallback: "Change mobile number")
          /// Change password
          internal static let changePassword = L10n.tr("Localizable", "maya.settings.menu.title.changePassword", fallback: "Change password")
          /// Close account
          internal static let closeAccount = L10n.tr("Localizable", "maya.settings.menu.title.closeAccount", fallback: "Close account")
          /// Data personalization
          internal static let data = L10n.tr("Localizable", "maya.settings.menu.title.data", fallback: "Data personalization")
          /// Licenses
          internal static let licenses = L10n.tr("Localizable", "maya.settings.menu.title.licenses", fallback: "Licenses")
          /// Manage notifications
          internal static let manageNotifications = L10n.tr("Localizable", "maya.settings.menu.title.manageNotifications", fallback: "Manage notifications")
          /// Privacy policy
          internal static let privacy = L10n.tr("Localizable", "maya.settings.menu.title.privacy", fallback: "Privacy policy")
          /// Account recovery
          internal static let recovery = L10n.tr("Localizable", "maya.settings.menu.title.recovery", fallback: "Account recovery")
          /// Show my name
          internal static let showName = L10n.tr("Localizable", "maya.settings.menu.title.showName", fallback: "Show my name")
          /// Terms & conditions
          internal static let terms = L10n.tr("Localizable", "maya.settings.menu.title.terms", fallback: "Terms & conditions")
        }
      }
      internal enum Password {
        /// •  Combine upper and lower case letters, numbers, and special characters (e.g., $, #, &, etc.).
        /// •  Keep your password at least 6 to 12 characters long.
        /// •  Avoid consecutive characters (e.g., 12345, abcde, qwerty, etc.) or repeating characters (e.g., 11111).
        /// •  Avoid personal info like names of friends or relatives, your birthday, or your address.
        /// •  Avoid common or obvious words (e.g., password, maya, bank, money, etc.).
        /// •  Avoid using the same password from other accounts you own.
        internal static let tips = L10n.tr("Localizable", "maya.settings.password.tips", fallback: "•  Combine upper and lower case letters, numbers, and special characters (e.g., $, #, &, etc.).\n•  Keep your password at least 6 to 12 characters long.\n•  Avoid consecutive characters (e.g., 12345, abcde, qwerty, etc.) or repeating characters (e.g., 11111).\n•  Avoid personal info like names of friends or relatives, your birthday, or your address.\n•  Avoid common or obvious words (e.g., password, maya, bank, money, etc.).\n•  Avoid using the same password from other accounts you own.")
        internal enum Alert {
          /// Congratulations, you have successfully updated your preferred settings.
          internal static let congratulations = L10n.tr("Localizable", "maya.settings.password.alert.congratulations", fallback: "Congratulations, you have successfully updated your preferred settings.")
          /// You can update your password anytime by going to settings.
          internal static let description = L10n.tr("Localizable", "maya.settings.password.alert.description", fallback: "You can update your password anytime by going to settings.")
          /// Password changed!
          internal static let passwordChanged = L10n.tr("Localizable", "maya.settings.password.alert.passwordChanged", fallback: "Password changed!")
          /// Maya Settings Change Password
          internal static let settingsUpdated = L10n.tr("Localizable", "maya.settings.password.alert.settingsUpdated", fallback: "Settings updated")
        }
        internal enum Validation {
          internal enum Not {
            /// Please re-enter your password. It must be at least 6 characters long and has no spaces
            internal static let allowed = L10n.tr("Localizable", "maya.settings.password.validation.not.allowed", fallback: "Please re-enter your password. It must be at least 6 characters long and has no spaces")
            /// Does not match your new password
            internal static let match = L10n.tr("Localizable", "maya.settings.password.validation.not.match", fallback: "Does not match your new password")
          }
        }
      }
      internal enum Section {
        /// ABOUT MAYA
        internal static let about = L10n.tr("Localizable", "maya.settings.section.about", fallback: "ABOUT MAYA")
        /// ACCOUNT
        internal static let account = L10n.tr("Localizable", "maya.settings.section.account", fallback: "ACCOUNT")
        /// Maya settings
        internal static let display = L10n.tr("Localizable", "maya.settings.section.display", fallback: "DISPLAY")
        /// PRIVACY & SECURITY
        internal static let privacy = L10n.tr("Localizable", "maya.settings.section.privacy", fallback: "PRIVACY & SECURITY")
      }
    }
    internal enum Shop {
      internal enum Confirm {
        internal enum Success {
          internal enum Bottomsheet {
            /// Thank you for always using Maya!
            internal static let description = L10n.tr("Localizable", "maya.shop.confirm.success.bottomsheet.description", fallback: "Thank you for always using Maya!")
            /// You've purchased %@
            internal static func title(_ p1: Any) -> String {
              return L10n.tr("Localizable", "maya.shop.confirm.success.bottomsheet.title", String(describing: p1), fallback: "You've purchased %@")
            }
          }
        }
      }
      internal enum History {
        internal enum ShareTreats {
          /// Powered by Share Treats
          internal static let label = L10n.tr("Localizable", "maya.shop.history.shareTreats.label", fallback: "Powered by Share Treats")
        }
      }
      internal enum LoadV2 {
        internal enum Banner {
          /// Click here to buy load
          internal static let subtitle = L10n.tr("Localizable", "maya.shop.loadV2.banner.subtitle", fallback: "Click here to buy load")
          /// Maya Shop
          internal static let title = L10n.tr("Localizable", "maya.shop.loadV2.banner.title", fallback: "Load feature is relocated")
        }
      }
      internal enum Payment {
        internal enum Confirmation {
          internal enum Item {
            /// Product name
            internal static let title = L10n.tr("Localizable", "maya.shop.payment.confirmation.item.title", fallback: "Product name")
          }
        }
      }
      internal enum Product {
        internal enum Disabled {
          internal enum Price {
            /// Not Available
            internal static let message = L10n.tr("Localizable", "maya.shop.product.disabled.price.message", fallback: "Not Available")
          }
        }
      }
      internal enum Products {
        internal enum Search {
          /// Search for a product
          internal static let placeholder = L10n.tr("Localizable", "maya.shop.products.search.placeholder", fallback: "Search for a product")
        }
      }
      internal enum Search {
        /// Search shop
        internal static let placeholder = L10n.tr("Localizable", "maya.shop.search.placeholder", fallback: "Search shop")
        internal enum Loading {
          /// Maya Shop
          internal static let message = L10n.tr("Localizable", "maya.shop.search.loading.message", fallback: "Please wait")
        }
      }
      internal enum Spiel {
        internal enum Required {
          /// Please enter a valid mobile number
          internal static let recipientMobileNumber = L10n.tr("Localizable", "maya.shop.spiel.required.recipientMobileNumber", fallback: "Please enter a valid mobile number")
        }
      }
    }
    internal enum Travel {
      internal enum Ticket {
        internal enum Confirmation {
          /// QR ticket is valid for 24hrs from date of purchase.
          internal static let info = L10n.tr("Localizable", "maya.travel.ticket.confirmation.info", fallback: "QR ticket is valid for 24hrs from date of purchase.")
          /// Confirm payment
          internal static let title = L10n.tr("Localizable", "maya.travel.ticket.confirmation.title", fallback: "Confirm payment")
          internal enum Continue {
            /// Pay now
            internal static let text = L10n.tr("Localizable", "maya.travel.ticket.confirmation.continue.text", fallback: "Pay now")
          }
          internal enum Detail {
            /// Available balance
            internal static let availableBalance = L10n.tr("Localizable", "maya.travel.ticket.confirmation.detail.availableBalance", fallback: "Available balance")
            /// Convenience fee
            internal static let convenienceFee = L10n.tr("Localizable", "maya.travel.ticket.confirmation.detail.convenienceFee", fallback: "Convenience fee")
            /// Destination
            internal static let destination = L10n.tr("Localizable", "maya.travel.ticket.confirmation.detail.destination", fallback: "Destination")
            /// Fare
            internal static let fare = L10n.tr("Localizable", "maya.travel.ticket.confirmation.detail.fare", fallback: "Fare")
            /// My Wallet
            internal static let myWallet = L10n.tr("Localizable", "maya.travel.ticket.confirmation.detail.myWallet", fallback: "My Wallet")
            /// Operator
            internal static let `operator` = L10n.tr("Localizable", "maya.travel.ticket.confirmation.detail.operator", fallback: "Operator")
            /// Origin
            internal static let origin = L10n.tr("Localizable", "maya.travel.ticket.confirmation.detail.origin", fallback: "Origin")
            /// Payment method
            internal static let paymentMethod = L10n.tr("Localizable", "maya.travel.ticket.confirmation.detail.paymentMethod", fallback: "Payment method")
            /// Route
            internal static let route = L10n.tr("Localizable", "maya.travel.ticket.confirmation.detail.route", fallback: "Route")
          }
        }
        internal enum StopSelection {
          internal enum Destination {
            internal enum Picker {
              /// Select destination
              internal static let title = L10n.tr("Localizable", "maya.travel.ticket.stopSelection.destination.picker.title", fallback: "Select destination")
            }
          }
          internal enum Origin {
            internal enum Picker {
              /// Maya Travel
              internal static let title = L10n.tr("Localizable", "maya.travel.ticket.stopSelection.origin.picker.title", fallback: "Select origin")
            }
          }
        }
      }
    }
    internal enum UpdateProfile {
      internal enum CivilStatus {
        /// Select civil status
        internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.civilStatus.placeholder", fallback: "Select civil status")
        /// Civil status
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.civilStatus.title", fallback: "Civil status")
      }
      internal enum ContactNumber {
        /// ************ or ********
        internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.contactNumber.placeholder", fallback: "************ or ********")
        /// Alternative contact number
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.contactNumber.title", fallback: "Alternative contact number")
      }
      internal enum ContactReference {
        /// We will contact this person in case of urgent concerns about your account. Learn more
        internal static let subtitle = L10n.tr("Localizable", "maya.updateProfile.contactReference.subtitle", fallback: "We will contact this person in case of urgent concerns about your account. Learn more")
        /// Add a contact reference
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.contactReference.title", fallback: "Add a contact reference")
        internal enum FirstName {
          /// Enter their first name
          internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.contactReference.firstName.placeholder", fallback: "Enter their first name")
          /// Contact reference’s first name
          internal static let title = L10n.tr("Localizable", "maya.updateProfile.contactReference.firstName.title", fallback: "Contact reference’s first name")
        }
        internal enum LastName {
          /// Enter their last name
          internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.contactReference.lastName.placeholder", fallback: "Enter their last name")
          /// Contact reference’s last name
          internal static let title = L10n.tr("Localizable", "maya.updateProfile.contactReference.lastName.title", fallback: "Contact reference’s last name")
        }
        internal enum MiddleName {
          /// Enter their middle name
          internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.contactReference.middleName.placeholder", fallback: "Enter their middle name")
          /// Contact reference’s middle name
          internal static let title = L10n.tr("Localizable", "maya.updateProfile.contactReference.middleName.title", fallback: "Contact reference’s middle name")
        }
        internal enum MobileNumber {
          /// 9123456789
          internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.contactReference.mobileNumber.placeholder", fallback: "9123456789")
          /// Contact reference’s mobile number
          internal static let title = L10n.tr("Localizable", "maya.updateProfile.contactReference.mobileNumber.title", fallback: "Contact reference’s mobile number")
        }
        internal enum NoLegalMiddleName {
          /// No legal middle name
          internal static let title = L10n.tr("Localizable", "maya.updateProfile.contactReference.noLegalMiddleName.title", fallback: "No legal middle name")
        }
        internal enum Relationship {
          /// Select a relationship
          internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.contactReference.relationship.placeholder", fallback: "Select a relationship")
          /// Relationship to you
          internal static let title = L10n.tr("Localizable", "maya.updateProfile.contactReference.relationship.title", fallback: "Relationship to you")
        }
        internal enum Return {
          /// Return
          internal static let title = L10n.tr("Localizable", "maya.updateProfile.contactReference.return.title", fallback: "Return")
        }
        internal enum Save {
          /// Save
          internal static let title = L10n.tr("Localizable", "maya.updateProfile.contactReference.save.title", fallback: "Save")
        }
        internal enum Subtitle {
          /// Learn more
          internal static let highlighted = L10n.tr("Localizable", "maya.updateProfile.contactReference.subtitle.highlighted", fallback: "Learn more")
        }
      }
      internal enum Educational {
        internal enum Attainment {
          /// Select educational attainment
          internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.educational.attainment.placeholder", fallback: "Select educational attainment")
          /// Educational attainment
          internal static let title = L10n.tr("Localizable", "maya.updateProfile.educational.attainment.title", fallback: "Educational attainment")
        }
      }
      internal enum EmailAdress {
        /// Enter your email address
        internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.emailAdress.placeholder", fallback: "Enter your email address")
        /// Email address
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.emailAdress.title", fallback: "Email address")
      }
      internal enum Gender {
        /// Select gender
        internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.gender.placeholder", fallback: "Select gender")
        /// Gender
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.gender.title", fallback: "Gender")
      }
      internal enum JobTitle {
        /// Select a job title
        internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.jobTitle.placeholder", fallback: "Select a job title")
        /// Job title
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.jobTitle.title", fallback: "Job title")
      }
      internal enum MotherMaidenFirstName {
        /// Enter mother's first name
        internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.motherMaidenFirstName.placeholder", fallback: "Enter mother's first name")
        /// Mother's maiden first name
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.motherMaidenFirstName.title", fallback: "Mother's maiden first name")
      }
      internal enum MotherMaidenLastName {
        /// Enter mother's last name
        internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.motherMaidenLastName.placeholder", fallback: "Enter mother's last name")
        /// Mother's maiden last name
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.motherMaidenLastName.title", fallback: "Mother's maiden last name")
      }
      internal enum MotherMaidenMiddleName {
        /// Enter mother's middle name
        internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.motherMaidenMiddleName.placeholder", fallback: "Enter mother's middle name")
        /// Mother's maiden middle name
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.motherMaidenMiddleName.title", fallback: "Mother's maiden middle name")
      }
      internal enum NameOfCompany {
        /// Enter name of company or business
        internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.nameOfCompany.placeholder", fallback: "Enter name of company or business")
        /// Name of company/business
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.nameOfCompany.title", fallback: "Name of company/business")
      }
      internal enum NatureOfWork {
        /// Select a nature of work
        internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.natureOfWork.placeholder", fallback: "Select a nature of work")
        /// Nature of work
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.natureOfWork.title", fallback: "Nature of work")
      }
      internal enum NoLegalMiddleName {
        /// No legal middle name
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.noLegalMiddleName.title", fallback: "No legal middle name")
      }
      internal enum PersonalDetails {
        /// These details will help us verify your identity during the application process
        internal static let subtitle = L10n.tr("Localizable", "maya.updateProfile.personalDetails.subtitle", fallback: "These details will help us verify your identity during the application process")
        /// Update Profile
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.personalDetails.title", fallback: "Tell us about yourself")
      }
      internal enum SourceOfIncome {
        /// Select a source of income
        internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.sourceOfIncome.placeholder", fallback: "Select a source of income")
        /// Source of income
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.sourceOfIncome.title", fallback: "Source of income")
      }
      internal enum TakeHomePay {
        /// ₱
        internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.takeHomePay.placeholder", fallback: "₱")
        /// Monthly take-home pay
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.takeHomePay.title", fallback: "Monthly take-home pay")
      }
      internal enum Tin {
        /// ***********-000
        internal static let placeholder = L10n.tr("Localizable", "maya.updateProfile.tin.placeholder", fallback: "***********-000")
        /// Tax identification number (Optional)
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.tin.title", fallback: "Tax identification number (Optional)")
      }
      internal enum Validation {
        internal enum Field {
          /// %@ is required
          internal static func isRequired(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.updateProfile.validation.field.isRequired", String(describing: p1), fallback: "%@ is required")
          }
          /// This
          internal static let this = L10n.tr("Localizable", "maya.updateProfile.validation.field.this", fallback: "This")
          internal enum AltNumber {
            internal enum Same {
              /// Alternate contact number cannot be the same as existing contact reference mobile number : %@
              internal static func contactReference(_ p1: Any) -> String {
                return L10n.tr("Localizable", "maya.updateProfile.validation.field.altNumber.same.contactReference", String(describing: p1), fallback: "Alternate contact number cannot be the same as existing contact reference mobile number : %@")
              }
            }
          }
          internal enum ContactNumber {
            internal enum Cannot {
              /// Alternative contact number cannot be the same as your mobile number with Maya
              internal static let same = L10n.tr("Localizable", "maya.updateProfile.validation.field.contactNumber.cannot.same", fallback: "Alternative contact number cannot be the same as your mobile number with Maya")
            }
          }
          internal enum ContactReference {
            internal enum ContactNumber {
              internal enum Cannot {
                /// Contact reference’s mobile number cannot be the same as your mobile number with Maya
                internal static let same = L10n.tr("Localizable", "maya.updateProfile.validation.field.contactReference.contactNumber.cannot.same", fallback: "Contact reference’s mobile number cannot be the same as your mobile number with Maya")
              }
            }
            internal enum Different {
              /// You must name a different person as your contact reference
              internal static let person = L10n.tr("Localizable", "maya.updateProfile.validation.field.contactReference.different.person", fallback: "You must name a different person as your contact reference")
            }
            internal enum MobileNumber {
              /// A valid 12 digit mobile number of your contact reference is required
              internal static let isRequired = L10n.tr("Localizable", "maya.updateProfile.validation.field.contactReference.mobileNumber.isRequired", fallback: "A valid 12 digit mobile number of your contact reference is required")
            }
            internal enum Same {
              /// Contact reference mobile number cannot be the same as existing alternate contact number : %@
              internal static func altNumber(_ p1: Any) -> String {
                return L10n.tr("Localizable", "maya.updateProfile.validation.field.contactReference.same.altNumber", String(describing: p1), fallback: "Contact reference mobile number cannot be the same as existing alternate contact number : %@")
              }
            }
          }
          internal enum MotherName {
            internal enum Different {
              /// You must name a different person for Mother’s Maiden name
              internal static let person = L10n.tr("Localizable", "maya.updateProfile.validation.field.motherName.different.person", fallback: "You must name a different person for Mother’s Maiden name")
            }
          }
          internal enum Repeated {
            /// Cannot have character repeated 3 or more times consecutively
            internal static let characters = L10n.tr("Localizable", "maya.updateProfile.validation.field.repeated.characters", fallback: "Cannot have character repeated 3 or more times consecutively")
          }
        }
      }
      internal enum WorkDetails {
        /// These details let us assess your eligibility for credit cards
        internal static let subtitle = L10n.tr("Localizable", "maya.updateProfile.workDetails.subtitle", fallback: "These details let us assess your eligibility for credit cards")
        /// Add your work details
        internal static let title = L10n.tr("Localizable", "maya.updateProfile.workDetails.title", fallback: "Add your work details")
      }
    }
    internal enum Vouchers {
      internal enum Available {
        /// Maya Vouchers
        internal static let spiel = L10n.tr("Localizable", "maya.vouchers.available.spiel", fallback: "Claim until:")
        internal enum Button {
          /// Claim
          internal static let title = L10n.tr("Localizable", "maya.vouchers.available.button.title", fallback: "Claim")
        }
      }
      internal enum Claimed {
        internal enum Button {
          /// Claimed
          internal static let title = L10n.tr("Localizable", "maya.vouchers.claimed.button.title", fallback: "Claimed")
        }
      }
      internal enum Details {
        internal enum Code {
          /// Reference ID %@
          internal static func label(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.vouchers.details.code.label", String(describing: p1), fallback: "Reference ID %@")
          }
        }
      }
      internal enum Expired {
        /// Expired on:
        internal static let spiel = L10n.tr("Localizable", "maya.vouchers.expired.spiel", fallback: "Expired on:")
        internal enum Button {
          /// Expired
          internal static let title = L10n.tr("Localizable", "maya.vouchers.expired.button.title", fallback: "Expired")
        }
        internal enum Cell {
          /// Expired on %@
          internal static func spiel(_ p1: Any) -> String {
            return L10n.tr("Localizable", "maya.vouchers.expired.cell.spiel", String(describing: p1), fallback: "Expired on %@")
          }
        }
      }
      internal enum Receipt {
        internal enum Code {
          /// Voucher code copied
          internal static let copied = L10n.tr("Localizable", "maya.vouchers.receipt.code.copied", fallback: "Voucher code copied")
          /// Voucher code
          internal static let title = L10n.tr("Localizable", "maya.vouchers.receipt.code.title", fallback: "Voucher code")
          /// Claim until:
          internal static let valid = L10n.tr("Localizable", "maya.vouchers.receipt.code.valid", fallback: "Claim until:")
        }
        internal enum Details {
          /// Use this voucher to redeem PayMaya credits and more!
          internal static let description = L10n.tr("Localizable", "maya.vouchers.receipt.details.description", fallback: "Use this voucher to redeem PayMaya credits and more!")
        }
        internal enum State {
          /// Voucher successfully claimed
          internal static let claimed = L10n.tr("Localizable", "maya.vouchers.receipt.state.claimed", fallback: "Voucher successfully claimed")
        }
      }
      internal enum Redeemed {
        /// Claimed on %@
        internal static func spiel(_ p1: Any) -> String {
          return L10n.tr("Localizable", "maya.vouchers.redeemed.spiel", String(describing: p1), fallback: "Claimed on %@")
        }
      }
    }
  }
  internal enum Missions {
    internal enum Completed {
      internal enum Section {
        /// Completed
        internal static let title = L10n.tr("Localizable", "missions.completed.section.title", fallback: "Completed")
      }
    }
    internal enum Default {
      internal enum Reward {
        /// PayMaya Credits
        internal static let name = L10n.tr("Localizable", "missions.default.reward.name", fallback: "PayMaya Credits")
      }
    }
    internal enum Details {
      internal enum Progress {
        internal enum Aggregate {
          /// Your progress: %.2f
          internal static func notarget(_ p1: Float) -> String {
            return L10n.tr("Localizable", "missions.details.progress.aggregate.notarget", p1, fallback: "Your progress: %.2f")
          }
          /// %.2f of %.2f
          internal static func withtarget(_ p1: Float, _ p2: Float) -> String {
            return L10n.tr("Localizable", "missions.details.progress.aggregate.withtarget", p1, p2, fallback: "%.2f of %.2f")
          }
        }
        internal enum Customandmultiple {
          /// Plural format key: "%#@times@"
          internal static func notarget(_ p1: Int) -> String {
            return L10n.tr("Localizable", "missions.details.progress.customandmultiple.notarget", p1, fallback: "Plural format key: \"%#@times@\"")
          }
          /// Plural format key: "%d of %#@times@"
          internal static func withtarget(_ p1: Int, _ p2: Int) -> String {
            return L10n.tr("Localizable", "missions.details.progress.customandmultiple.withtarget", p1, p2, fallback: "Plural format key: \"%d of %#@times@\"")
          }
        }
        internal enum Others {
          /// Your progress: %d
          internal static func notarget(_ p1: Int) -> String {
            return L10n.tr("Localizable", "missions.details.progress.others.notarget", p1, fallback: "Your progress: %d")
          }
          /// %d of %d
          internal static func withtarget(_ p1: Int, _ p2: Int) -> String {
            return L10n.tr("Localizable", "missions.details.progress.others.withtarget", p1, p2, fallback: "%d of %d")
          }
        }
      }
    }
    internal enum Other {
      internal enum Missions {
        internal enum Section {
          /// Other Missions
          internal static let title = L10n.tr("Localizable", "missions.other.missions.section.title", fallback: "Other Missions")
        }
      }
    }
    internal enum Title {
      internal enum Current {
        /// Rewards and Missions
        internal static let mission = L10n.tr("Localizable", "missions.title.current.mission", fallback: "Current Mission")
      }
      internal enum Mission {
        /// Mission Duration
        internal static let duration = L10n.tr("Localizable", "missions.title.mission.duration", fallback: "Mission Duration")
      }
    }
    internal enum Validity {
      internal enum Duration {
        /// left
        internal static let suffix = L10n.tr("Localizable", "missions.validity.duration.suffix", fallback: "left")
      }
    }
  }
  internal enum My {
    internal enum Qr {
      internal enum Screen {
        /// My QR
        internal static let title = L10n.tr("Localizable", "my.qr.screen.title", fallback: "My QR")
      }
    }
  }
  internal enum MyFavorites {
    internal enum Empty {
      internal enum Bank {
        /// Complete a transaction to add it your favorites
        internal static let spiel = L10n.tr("Localizable", "myFavorites.empty.bank.spiel", fallback: "Complete a transaction to add it your favorites")
      }
      internal enum Biller {
        /// Pay a bill to add it to your favorites
        internal static let spiel = L10n.tr("Localizable", "myFavorites.empty.biller.spiel", fallback: "Pay a bill to add it to your favorites")
      }
      internal enum Sendmoney {
        /// Complete a transaction to add it your favorites
        internal static let spiel = L10n.tr("Localizable", "myFavorites.empty.sendmoney.spiel", fallback: "Complete a transaction to add it your favorites")
      }
    }
    internal enum Title {
      /// MY FAVORITES
      internal static let uppercased = L10n.tr("Localizable", "myFavorites.title.uppercased", fallback: "MY FAVORITES")
    }
    internal enum Update {
      internal enum Error {
        /// Sorry, we failed to update your favorite.
        internal static let message = L10n.tr("Localizable", "myFavorites.update.error.message", fallback: "Sorry, we failed to update your favorite.")
      }
      internal enum Success {
        /// My Favorites
        internal static let message = L10n.tr("Localizable", "myFavorites.update.success.message", fallback: "Your favorite has been updated")
      }
    }
  }
  internal enum Mycards {
    internal enum Activate {
      internal enum Card {
        /// You can now receive money. Go to your nearest reloading stations to know more.
        internal static let message = L10n.tr("Localizable", "mycards.activate.card.message", fallback: "You can now receive money. Go to your nearest reloading stations to know more.")
        /// Your Online Payment Card Number is now activated!
        internal static let title = L10n.tr("Localizable", "mycards.activate.card.title", fallback: "Your Online Payment Card Number is now activated!")
      }
    }
    internal enum Card {
      internal enum Masking {
        /// ***** ***** ***** %@
        internal static func bancnet(_ p1: Any) -> String {
          return L10n.tr("Localizable", "mycards.card.masking.bancnet", String(describing: p1), fallback: "***** ***** ***** %@")
        }
        /// **** **** **** %@
        internal static func `default`(_ p1: Any) -> String {
          return L10n.tr("Localizable", "mycards.card.masking.default", String(describing: p1), fallback: "**** **** **** %@")
        }
      }
    }
    internal enum Link {
      internal enum Card {
        /// Link PayMaya Card
        internal static let title = L10n.tr("Localizable", "mycards.link.card.title", fallback: "Link PayMaya Card")
        internal enum Success {
          /// New PayMaya Card added!
          internal static let title = L10n.tr("Localizable", "mycards.link.card.success.title", fallback: "New PayMaya Card added!")
        }
      }
    }
    internal enum Paymaya {
      internal enum Card {
        /// My Cards screen
        internal static let name = L10n.tr("Localizable", "mycards.paymaya.card.name", fallback: "PayMaya Card")
      }
    }
    internal enum View {
      internal enum Card {
        /// View Card
        internal static let title = L10n.tr("Localizable", "mycards.view.card.title", fallback: "View Card")
      }
    }
    internal enum Virtual {
      internal enum BalanceTreshold {
        /// To view your virtual card, you must have at least Php100 in your PayMaya account.
        internal static let message = L10n.tr("Localizable", "mycards.virtual.balanceTreshold.message", fallback: "To view your virtual card, you must have at least Php100 in your PayMaya account.")
      }
    }
  }
  internal enum P2m {
    internal enum Cancel {
      internal enum Transaction {
        /// Yes, Cancel
        internal static let approval = L10n.tr("Localizable", "p2m.cancel.transaction.approval", fallback: "Yes, Cancel")
        /// Are you sure you want to cancel this transaction?
        internal static let message = L10n.tr("Localizable", "p2m.cancel.transaction.message", fallback: "Are you sure you want to cancel this transaction?")
        /// P2M
        internal static let title = L10n.tr("Localizable", "p2m.cancel.transaction.title", fallback: "Cancel Transaction")
      }
    }
    internal enum Maintenance {
      /// Non-Maya QR payment is currently under maintenance. Please check again later.
      internal static let message = L10n.tr("Localizable", "p2m.maintenance.message", fallback: "Non-Maya QR payment is currently under maintenance. Please check again later.")
      /// Non-Maya QR Payment
      internal static let screenTitle = L10n.tr("Localizable", "p2m.maintenance.screenTitle", fallback: "Non-Maya QR Payment")
      /// We'll be back shortly.
      internal static let title = L10n.tr("Localizable", "p2m.maintenance.title", fallback: "We'll be back shortly.")
    }
    internal enum Success {
      /// Payment Receipt
      internal static let message = L10n.tr("Localizable", "p2m.success.message", fallback: "Payment Receipt")
      /// Payment Successful!
      internal static let title = L10n.tr("Localizable", "p2m.success.title", fallback: "Payment Successful!")
      internal enum Back {
        internal enum To {
          /// Back to Merchant
          internal static let merchant = L10n.tr("Localizable", "p2m.success.back.to.merchant", fallback: "Back to Merchant")
          internal enum Merchant {
            /// Back to merchant
            internal static let bottomsheet = L10n.tr("Localizable", "p2m.success.back.to.merchant.bottomsheet", fallback: "Back to merchant")
            internal enum Bottomsheet {
              /// Back to merchant in %@
              internal static func withCountdown(_ p1: Any) -> String {
                return L10n.tr("Localizable", "p2m.success.back.to.merchant.bottomsheet.withCountdown", String(describing: p1), fallback: "Back to merchant in %@")
              }
            }
          }
        }
      }
      internal enum Detail {
        internal enum Title {
          /// Amount
          internal static let amount = L10n.tr("Localizable", "p2m.success.detail.title.amount", fallback: "Amount")
          /// Merchant
          internal static let merchant = L10n.tr("Localizable", "p2m.success.detail.title.merchant", fallback: "Merchant")
          /// Reference ID
          internal static let referenceid = L10n.tr("Localizable", "p2m.success.detail.title.referenceid", fallback: "Reference ID")
          /// Updated Balance
          internal static let updatedbalance = L10n.tr("Localizable", "p2m.success.detail.title.updatedbalance", fallback: "Updated Balance")
        }
      }
    }
  }
  internal enum PasswordChanged {
    /// You can now login with your new password
    internal static let subtitle = L10n.tr("Localizable", "passwordChanged.subtitle", fallback: "You can now login with your new password")
    /// Password Changed Screen
    internal static let title = L10n.tr("Localizable", "passwordChanged.title", fallback: "Password changed")
    internal enum Tips {
      ///  •  Password
      ///  •  16-digit card number or CVV
      ///  •  One-Time Pin (OTP)
      internal static let list = L10n.tr("Localizable", "passwordChanged.tips.list", fallback: " •  Password\n •  16-digit card number or CVV\n •  One-Time Pin (OTP)")
      /// never
      internal static let never = L10n.tr("Localizable", "passwordChanged.tips.never", fallback: "never")
      /// Maya's official representatives will never ask for your:
      internal static let subtitle = L10n.tr("Localizable", "passwordChanged.tips.subtitle", fallback: "Maya's official representatives will never ask for your:")
      /// Remember this tip 💡
      internal static let title = L10n.tr("Localizable", "passwordChanged.tips.title", fallback: "Remember this tip 💡")
    }
  }
  internal enum Paybills {
    internal enum AccountNumber {
      /// Account Number is required
      internal static let `required` = L10n.tr("Localizable", "paybills.accountNumber.required", fallback: "Account Number is required")
    }
    internal enum Activity {
      internal enum Title {
        /// Account Number
        internal static let `default` = L10n.tr("Localizable", "paybills.activity.title.default", fallback: "Account Number")
      }
    }
    internal enum AddFavorites {
      internal enum Alias {
        internal enum Example {
          /// House, Postpaid
          internal static let label = L10n.tr("Localizable", "paybills.addFavorites.alias.example.label", fallback: "House, Postpaid")
        }
      }
    }
    internal enum Amount {
      /// Amount is required
      internal static let `required` = L10n.tr("Localizable", "paybills.amount.required", fallback: "Amount is required")
    }
    internal enum Categories {
      /// BILLER CATEGORIES
      internal static let label = L10n.tr("Localizable", "paybills.categories.label", fallback: "BILLER CATEGORIES")
    }
    internal enum Field {
      /// %@ is invalid
      internal static func invalid(_ p1: Any) -> String {
        return L10n.tr("Localizable", "paybills.field.invalid", String(describing: p1), fallback: "%@ is invalid")
      }
      /// %@ is required
      internal static func `required`(_ p1: Any) -> String {
        return L10n.tr("Localizable", "paybills.field.required", String(describing: p1), fallback: "%@ is required")
      }
    }
    internal enum Placeholder {
      /// Account Number
      internal static let `default` = L10n.tr("Localizable", "paybills.placeholder.default", fallback: "Account Number")
    }
    internal enum Receipt {
      internal enum Title {
        /// Account Number
        internal static let `default` = L10n.tr("Localizable", "paybills.receipt.title.default", fallback: "Account Number")
      }
    }
    internal enum Search {
      /// Search for billers or merchants
      internal static let placeholder = L10n.tr("Localizable", "paybills.search.placeholder", fallback: "Search for billers or merchants")
    }
    internal enum Section {
      internal enum Header {
        /// Categories
        internal static let categories = L10n.tr("Localizable", "paybills.section.header.categories", fallback: "Categories")
        /// Other Billers
        internal static let otherBillers = L10n.tr("Localizable", "paybills.section.header.otherBillers", fallback: "Other Billers")
        /// Pay bills
        internal static let recentlyPaidBillers = L10n.tr("Localizable", "paybills.section.header.recentlyPaidBillers", fallback: "Recently Paid Billers")
      }
    }
    internal enum Spiel {
      internal enum AccountNumber {
        /// 10-digit Customer Account Number found at the upper left portion of the SOA
        internal static let meralco = L10n.tr("Localizable", "paybills.spiel.accountNumber.meralco", fallback: "10-digit Customer Account Number found at the upper left portion of the SOA")
      }
      internal enum Amount {
        /// Biller Convenience Fee (BCF) may apply.
        internal static let `default` = L10n.tr("Localizable", "paybills.spiel.amount.default", fallback: "Biller Convenience Fee (BCF) may apply.")
      }
      internal enum Invalid {
        /// Looks like you didn't enter a valid %@. Please enter the information to complete your payment.
        internal static func field(_ p1: Any) -> String {
          return L10n.tr("Localizable", "paybills.spiel.invalid.field", String(describing: p1), fallback: "Looks like you didn't enter a valid %@. Please enter the information to complete your payment.")
        }
      }
      internal enum Processing {
        /// Processing Bills Payment Transaction
        internal static let title = L10n.tr("Localizable", "paybills.spiel.processing.title", fallback: "Processing Bills Payment Transaction")
      }
      internal enum Required {
        /// Looks like you didn't enter the account number. Please enter the information to complete your payment.
        internal static let accountNumber = L10n.tr("Localizable", "paybills.spiel.required.accountNumber", fallback: "Looks like you didn't enter the account number. Please enter the information to complete your payment.")
        /// Looks like you didn't enter the amount. Please enter the information to complete your payment.
        internal static let amount = L10n.tr("Localizable", "paybills.spiel.required.amount", fallback: "Looks like you didn't enter the amount. Please enter the information to complete your payment.")
        /// Looks like you didn't enter the %@. Please enter the information to complete your payment.
        internal static func field(_ p1: Any) -> String {
          return L10n.tr("Localizable", "paybills.spiel.required.field", String(describing: p1), fallback: "Looks like you didn't enter the %@. Please enter the information to complete your payment.")
        }
      }
      internal enum Success {
        /// Payment Receipt
        internal static let message = L10n.tr("Localizable", "paybills.spiel.success.message", fallback: "Payment Receipt")
        internal enum Detail {
          /// Account Number
          internal static let accountNumber = L10n.tr("Localizable", "paybills.spiel.success.detail.accountNumber", fallback: "Account Number")
          /// Amount
          internal static let amount = L10n.tr("Localizable", "paybills.spiel.success.detail.amount", fallback: "Amount")
          /// Biller Convenience Fee
          internal static let bcf = L10n.tr("Localizable", "paybills.spiel.success.detail.bcf", fallback: "Biller Convenience Fee")
          /// Biller
          internal static let biller = L10n.tr("Localizable", "paybills.spiel.success.detail.biller", fallback: "Biller")
          /// Reference ID
          internal static let referenceId = L10n.tr("Localizable", "paybills.spiel.success.detail.referenceId", fallback: "Reference ID")
          /// Updated Balance
          internal static let updatedBalance = L10n.tr("Localizable", "paybills.spiel.success.detail.updatedBalance", fallback: "Updated Balance")
        }
      }
    }
    internal enum Tin {
      /// TIN is required
      internal static let `required` = L10n.tr("Localizable", "paybills.tin.required", fallback: "TIN is required")
    }
    internal enum Title {
      /// MY FAVORITES
      internal static let favorites = L10n.tr("Localizable", "paybills.title.favorites", fallback: "MY FAVORITES")
      /// View All
      internal static let viewAll = L10n.tr("Localizable", "paybills.title.viewAll", fallback: "View All")
      /// View Less
      internal static let viewLess = L10n.tr("Localizable", "paybills.title.viewLess", fallback: "View Less")
    }
  }
  internal enum Paywithqr {
    internal enum Confirm {
      internal enum Success {
        internal enum Bottomsheet {
          /// From your wallet to %@
          internal static func subtitle(_ p1: Any) -> String {
            return L10n.tr("Localizable", "paywithqr.confirm.success.bottomsheet.subtitle", String(describing: p1), fallback: "From your wallet to %@")
          }
          /// Maya Pay with QR
          internal static func title(_ p1: Any) -> String {
            return L10n.tr("Localizable", "paywithqr.confirm.success.bottomsheet.title", String(describing: p1), fallback: "You’ve successfully paid \n %@")
          }
          internal enum Viewreceipt {
            internal enum Button {
              /// View receipt
              internal static let title = L10n.tr("Localizable", "paywithqr.confirm.success.bottomsheet.viewreceipt.button.title", fallback: "View receipt")
            }
          }
        }
      }
    }
  }
  internal enum Permission {
    internal enum Camera {
      internal enum Scanner {
        /// Your camera will be used to scan QR and barcodes, and take photos and videos of account upgrading requirements.
        internal static let message = L10n.tr("Localizable", "permission.camera.scanner.message", fallback: "Your camera will be used to scan QR and barcodes, and take photos and videos of account upgrading requirements.")
        /// Permissions
        internal static let title = L10n.tr("Localizable", "permission.camera.scanner.title", fallback: "Allow PayMaya to Access the Camera")
      }
    }
  }
  internal enum PhilippineAirlines {
    internal enum Interstitial {
      /// Seamlessly link your Mabuhay Miles account in Maya
      internal static let message1 = L10n.tr("Localizable", "philippineAirlines.interstitial.message1", fallback: "Seamlessly link your Mabuhay Miles account in Maya")
      /// Instantly check your current Mabuhay Miles balance
      internal static let message2 = L10n.tr("Localizable", "philippineAirlines.interstitial.message2", fallback: "Instantly check your current Mabuhay Miles balance")
      /// Stay tuned for more features coming soon
      internal static let message3 = L10n.tr("Localizable", "philippineAirlines.interstitial.message3", fallback: "Stay tuned for more features coming soon")
      /// Philippine Airlines
      internal static let title = L10n.tr("Localizable", "philippineAirlines.interstitial.title", fallback: "Fly with PAL, right here in Maya")
    }
  }
  internal enum Prekyc {
    internal enum Spiel {
      /// Upgrade your account now to enjoy all of Maya’s features. It takes just a few minutes!
      internal static let message = L10n.tr("Localizable", "prekyc.spiel.message", fallback: "Upgrade your account now to enjoy all of Maya’s features. It takes just a few minutes!")
      /// Pre KYC
      internal static let title = L10n.tr("Localizable", "prekyc.spiel.title", fallback: "Let's take it all the way")
      internal enum Primary {
        /// Upgrade now
        internal static let button = L10n.tr("Localizable", "prekyc.spiel.primary.button", fallback: "Upgrade now")
      }
      internal enum Secondary {
        /// Maybe later
        internal static let button = L10n.tr("Localizable", "prekyc.spiel.secondary.button", fallback: "Maybe later")
      }
    }
  }
  internal enum Privacy {
    /// We have updated our Data Privacy Policy and Terms of Service.
    internal static let hasUpdate = L10n.tr("Localizable", "privacy.hasUpdate", fallback: "We have updated our Data Privacy Policy and Terms of Service.")
    /// Data Privacy Policy
    internal static let title = L10n.tr("Localizable", "privacy.title", fallback: "Data Privacy Policy")
    /// Data Privacy screen
    internal static let viewterms = L10n.tr("Localizable", "privacy.viewterms", fallback: "View Terms and Conditions")
    internal enum Alert {
      /// Disagree
      internal static let disagree = L10n.tr("Localizable", "privacy.alert.disagree", fallback: "Disagree")
    }
    internal enum Disagree {
      /// By disagreeing on our Terms and Conditions, your registration will be cancelled.
      internal static let confirmation = L10n.tr("Localizable", "privacy.disagree.confirmation", fallback: "By disagreeing on our Terms and Conditions, your registration will be cancelled.")
    }
    internal enum HasUpdate {
      /// Data Privacy Policy and Terms of Service Update Version
      internal static let title = L10n.tr("Localizable", "privacy.hasUpdate.title", fallback: "Data Privacy Policy and Terms of Service Update Version")
    }
    internal enum Policy {
      /// Privacy Policy
      internal static let title = L10n.tr("Localizable", "privacy.policy.title", fallback: "Privacy Policy")
    }
    internal enum Terms {
      internal enum And {
        internal enum Conditions {
          /// Terms and Conditions
          internal static let title = L10n.tr("Localizable", "privacy.terms.and.conditions.title", fallback: "Terms and Conditions")
        }
      }
    }
    internal enum Title {
      internal enum With {
        /// Data Privacy Policy Version %@
        internal static func version(_ p1: Any) -> String {
          return L10n.tr("Localizable", "privacy.title.with.version", String(describing: p1), fallback: "Data Privacy Policy Version %@")
        }
      }
    }
  }
  internal enum Profile {
    internal enum Account {
      internal enum Rekyc {
        internal enum Upgrade {
          /// Some of your information may be outdated. Please update to access all of Maya's features.
          internal static let spiel = L10n.tr("Localizable", "profile.account.rekyc.upgrade.spiel", fallback: "Some of your information may be outdated. Please update to access all of Maya's features.")
          /// Your personal info is outdated
          internal static let title = L10n.tr("Localizable", "profile.account.rekyc.upgrade.title", fallback: "Your personal info is outdated")
        }
      }
      internal enum Review {
        /// Please give us up to 24 hours to review and update your account status.
        internal static let spiel = L10n.tr("Localizable", "profile.account.review.spiel", fallback: "Please give us up to 24 hours to review and update your account status.")
        /// Account under review
        internal static let title = L10n.tr("Localizable", "profile.account.review.title", fallback: "Account under review")
        internal enum Button {
          /// Check status
          internal static let title = L10n.tr("Localizable", "profile.account.review.button.title", fallback: "Check status")
        }
      }
      internal enum Upgrade {
        /// Enjoy high-interest savings, access to loans, easy transfers with @username, and more when you upgrade your account.
        internal static let spiel = L10n.tr("Localizable", "profile.account.upgrade.spiel", fallback: "Enjoy high-interest savings, access to loans, easy transfers with @username, and more when you upgrade your account.")
        /// Get more features
        internal static let title = L10n.tr("Localizable", "profile.account.upgrade.title", fallback: "Get more features")
        internal enum Button {
          /// Upgrade now for free
          internal static let title = L10n.tr("Localizable", "profile.account.upgrade.button.title", fallback: "Upgrade now for free")
        }
      }
      internal enum Verified {
        internal enum Seller {
          /// Verified Sellers are legitimate small business owners that accept payments using Maya. As Verified Sellers, they get a badge that signals credibility and trustworthiness, and enjoy higher account limits and access to exclusive deals.
          /// 
          /// You become a Verified Seller if you:
          /// 
          /// ∙  Have an upgraded Maya account
          /// ∙  Consistently receive payments in your PayMaya account
          /// ∙  Maintain a good track record with no customer complaints
          internal static let about = L10n.tr("Localizable", "profile.account.verified.seller.about", fallback: "Verified Sellers are legitimate small business owners that accept payments using Maya. As Verified Sellers, they get a badge that signals credibility and trustworthiness, and enjoy higher account limits and access to exclusive deals.\n\nYou become a Verified Seller if you:\n\n∙  Have an upgraded Maya account\n∙  Consistently receive payments in your PayMaya account\n∙  Maintain a good track record with no customer complaints")
        }
      }
    }
    internal enum Duplicate {
      internal enum Account {
        /// Uh oh! You may already have a registered Maya account under a different number. Adding money to this account will be temporarily blocked.
        internal static let spiel = L10n.tr("Localizable", "profile.duplicate.account.spiel", fallback: "Uh oh! You may already have a registered Maya account under a different number. Adding money to this account will be temporarily blocked.")
        /// Duplicate account
        internal static let title = L10n.tr("Localizable", "profile.duplicate.account.title", fallback: "Duplicate account")
        internal enum Visit {
          internal enum Support {
            /// Visit support page
            internal static let page = L10n.tr("Localizable", "profile.duplicate.account.visit.support.page", fallback: "Visit support page")
          }
        }
      }
    }
    internal enum Ekyc {
      internal enum Button {
        /// Check Application Status
        internal static let checkApplicationStatus = L10n.tr("Localizable", "profile.ekyc.button.checkApplicationStatus", fallback: "Check Application Status")
      }
      internal enum Spiel {
        /// Upgrade now to enjoy more features and higher account limits!
        internal static let upgrade = L10n.tr("Localizable", "profile.ekyc.spiel.upgrade", fallback: "Upgrade now to enjoy more features and higher account limits!")
        /// We are processing your upgrade.
        internal static let upgradeProcessing = L10n.tr("Localizable", "profile.ekyc.spiel.upgradeProcessing", fallback: "We are processing your upgrade.")
        /// You account upgrade application is still under review.
        internal static let upgradeProcessingEdd = L10n.tr("Localizable", "profile.ekyc.spiel.upgradeProcessingEdd", fallback: "You account upgrade application is still under review.")
      }
    }
    internal enum Kyc {
      internal enum Button {
        /// Review Application
        internal static let review = L10n.tr("Localizable", "profile.kyc.button.review", fallback: "Review Application")
        /// Upgrade my Account
        internal static let upgrade = L10n.tr("Localizable", "profile.kyc.button.upgrade", fallback: "Upgrade my Account")
      }
    }
    internal enum Logout {
      internal enum Confirmation {
        /// Are you sure you want to log out?
        internal static let message = L10n.tr("Localizable", "profile.logout.confirmation.message", fallback: "Are you sure you want to log out?")
        internal enum Action {
          /// Log Out
          internal static let signout = L10n.tr("Localizable", "profile.logout.confirmation.action.signout", fallback: "Log Out")
        }
      }
    }
    internal enum Menu {
      internal enum Title {
        /// Account Limits
        internal static let accountLimits = L10n.tr("Localizable", "profile.menu.title.accountLimits", fallback: "Account Limits")
        /// Profile screen
        internal static let authorizeRequests = L10n.tr("Localizable", "profile.menu.title.authorizeRequests", fallback: "Authorize requests")
        /// My Cards
        internal static let cards = L10n.tr("Localizable", "profile.menu.title.cards", fallback: "My Cards")
        /// Contact Reference
        internal static let contactReference = L10n.tr("Localizable", "profile.menu.title.contactReference", fallback: "Contact Reference")
        /// Contact Reference (Deeplink)
        internal static let contactReferenceDeeplink = L10n.tr("Localizable", "profile.menu.title.contactReferenceDeeplink", fallback: "Contact Reference (Deeplink)")
        /// Deals
        internal static let deals = L10n.tr("Localizable", "profile.menu.title.deals", fallback: "Deals")
        /// My Favorites
        internal static let favorites = L10n.tr("Localizable", "profile.menu.title.favorites", fallback: "My Favorites")
        /// Quick Guide
        internal static let guide = L10n.tr("Localizable", "profile.menu.title.guide", fallback: "Quick Guide")
        /// Get help
        internal static let help = L10n.tr("Localizable", "profile.menu.title.help", fallback: "Get help")
        /// How to use
        internal static let howto = L10n.tr("Localizable", "profile.menu.title.howto", fallback: "How to use")
        /// Inbox
        internal static let inbox = L10n.tr("Localizable", "profile.menu.title.inbox", fallback: "Inbox")
        /// Invite a friend
        internal static let inviteAFriend = L10n.tr("Localizable", "profile.menu.title.inviteAFriend", fallback: "Invite a friend")
        /// Submit Invite Code
        internal static let inviteCode = L10n.tr("Localizable", "profile.menu.title.inviteCode", fallback: "Submit Invite Code")
        /// Log out
        internal static let logout = L10n.tr("Localizable", "profile.menu.title.logout", fallback: "Log out")
        /// Account limits
        internal static let mayaAccountLimits = L10n.tr("Localizable", "profile.menu.title.mayaAccountLimits", fallback: "Account limits")
        /// Favorites
        internal static let mayaFavorites = L10n.tr("Localizable", "profile.menu.title.mayaFavorites", fallback: "Favorites")
        /// Quick guide
        internal static let mayaGuide = L10n.tr("Localizable", "profile.menu.title.mayaGuide", fallback: "Quick guide")
        /// Missions
        internal static let missions = L10n.tr("Localizable", "profile.menu.title.missions", fallback: "Missions")
        /// Partner Merchants
        internal static let partnerMerchants = L10n.tr("Localizable", "profile.menu.title.partnerMerchants", fallback: "Partner Merchants")
        /// Receive Money
        internal static let profileqr = L10n.tr("Localizable", "profile.menu.title.profileqr", fallback: "Receive Money")
        /// Rate this app
        internal static let rateThisApp = L10n.tr("Localizable", "profile.menu.title.rateThisApp", fallback: "Rate this app")
        /// Security center
        internal static let securityCenter = L10n.tr("Localizable", "profile.menu.title.securityCenter", fallback: "Security center")
        /// Settings
        internal static let settings = L10n.tr("Localizable", "profile.menu.title.settings", fallback: "Settings")
        /// Shop
        internal static let shop = L10n.tr("Localizable", "profile.menu.title.shop", fallback: "Shop")
        /// Sign out
        internal static let signout = L10n.tr("Localizable", "profile.menu.title.signout", fallback: "Sign out")
        /// Submit invite code
        internal static let submitInviteCode = L10n.tr("Localizable", "profile.menu.title.submitInviteCode", fallback: "Submit invite code")
        /// Where to load up
        internal static let topup = L10n.tr("Localizable", "profile.menu.title.topup", fallback: "Where to load up")
        /// Update Profile
        internal static let updateProfile = L10n.tr("Localizable", "profile.menu.title.updateProfile", fallback: "Update Profile")
        /// Update Profile (Deeplink)
        internal static let updateProfileDeeplink = L10n.tr("Localizable", "profile.menu.title.updateProfileDeeplink", fallback: "Update Profile (Deeplink)")
        /// Vouchers
        internal static let vouchers = L10n.tr("Localizable", "profile.menu.title.vouchers", fallback: "Vouchers")
        /// Work Details
        internal static let workDetails = L10n.tr("Localizable", "profile.menu.title.workDetails", fallback: "Work Details")
        /// Work Details (Deeplink)
        internal static let workDetailsDeeplink = L10n.tr("Localizable", "profile.menu.title.workDetailsDeeplink", fallback: "Work Details (Deeplink)")
      }
    }
    internal enum Qr {
      internal enum Header {
        /// Profile QR
        internal static let spiel = L10n.tr("Localizable", "profile.qr.header.spiel", fallback: "Send money to")
      }
      internal enum Scan {
        internal enum Header {
          /// Just scan the QR Code below.
          internal static let spiel = L10n.tr("Localizable", "profile.qr.scan.header.spiel", fallback: "Just scan the QR Code below.")
        }
      }
    }
    internal enum Signout {
      internal enum Confirmation {
        /// Are you sure you want to sign out?
        internal static let message = L10n.tr("Localizable", "profile.signout.confirmation.message", fallback: "Are you sure you want to sign out?")
        internal enum Action {
          /// Cancel
          internal static let cancel = L10n.tr("Localizable", "profile.signout.confirmation.action.cancel", fallback: "Cancel")
          /// Sign Out
          internal static let signout = L10n.tr("Localizable", "profile.signout.confirmation.action.signout", fallback: "Sign Out")
        }
      }
    }
  }
  internal enum Pullfunds {
    internal enum Amount {
      /// account linking
      internal static let accountLinking = L10n.tr("Localizable", "pullfunds.amount.accountLinking", fallback: "account linking")
      /// Amount is required. Please try again.
      internal static let `required` = L10n.tr("Localizable", "pullfunds.amount.required", fallback: "Amount is required. Please try again.")
      /// Amount
      internal static let title = L10n.tr("Localizable", "pullfunds.amount.title", fallback: "Amount")
      internal enum Maya {
        /// Bank Pull Funds
        internal static func spiel(_ p1: Any, _ p2: Any, _ p3: Any) -> String {
          return L10n.tr("Localizable", "pullfunds.amount.maya.spiel", String(describing: p1), String(describing: p2), String(describing: p3), fallback: "From %@ up to %@ per transaction only. Cash in up to %@ times per day.")
        }
      }
    }
    internal enum Confirm {
      internal enum Transaction {
        /// We’re now processing this cash in transaction to your bank account.
        internal static let message = L10n.tr("Localizable", "pullfunds.confirm.transaction.message", fallback: "We’re now processing this cash in transaction to your bank account.")
        /// You’re almost there!
        internal static let title = L10n.tr("Localizable", "pullfunds.confirm.transaction.title", fallback: "You’re almost there!")
      }
    }
    internal enum Confirmation {
      internal enum Continue {
        /// By clicking Continue, you will be directed to %@'s online portal to complete your cash in transaction.
        internal static func spiel(_ p1: Any) -> String {
          return L10n.tr("Localizable", "pullfunds.confirmation.continue.spiel", String(describing: p1), fallback: "By clicking Continue, you will be directed to %@'s online portal to complete your cash in transaction.")
        }
      }
      internal enum Unlink {
        internal enum Account {
          /// Account number %@ in %@
          internal static func message(_ p1: Any, _ p2: Any) -> String {
            return L10n.tr("Localizable", "pullfunds.confirmation.unlink.account.message", String(describing: p1), String(describing: p2), fallback: "Account number %@ in %@")
          }
          /// Are you sure you want to unlink your account?
          internal static let title = L10n.tr("Localizable", "pullfunds.confirmation.unlink.account.title", fallback: "Are you sure you want to unlink your account?")
        }
      }
    }
    internal enum Default {
      internal enum Header {
        /// Link a bank account to cash in with ease
        internal static let title = L10n.tr("Localizable", "pullfunds.default.header.title", fallback: "Link a bank account to cash in with ease")
      }
    }
    internal enum Error {
      /// Maximum limit has been reached
      internal static let maxLimit = L10n.tr("Localizable", "pullfunds.error.maxLimit", fallback: "Maximum limit has been reached")
    }
    internal enum Linked {
      internal enum Account {
        internal enum Menu {
          /// Account (%@) options
          internal static func title(_ p1: Any) -> String {
            return L10n.tr("Localizable", "pullfunds.linked.account.menu.title", String(describing: p1), fallback: "Account (%@) options")
          }
        }
      }
      internal enum Header {
        /// My linked bank accounts
        internal static let title = L10n.tr("Localizable", "pullfunds.linked.header.title", fallback: "My linked bank accounts")
      }
    }
    internal enum Redesign {
      internal enum Account {
        internal enum Error {
          internal enum MaxLimit {
            /// Maximum accounts have been reached
            internal static let message = L10n.tr("Localizable", "pullfunds.redesign.account.error.maxLimit.message", fallback: "Maximum accounts have been reached")
          }
        }
      }
      internal enum Confirmation {
        internal enum Unlink {
          /// You are about to unlink this account: 
          /// **** **** %@
          /// %@
          internal static func message(_ p1: Any, _ p2: Any) -> String {
            return L10n.tr("Localizable", "pullfunds.redesign.confirmation.unlink.message", String(describing: p1), String(describing: p2), fallback: "You are about to unlink this account: \n**** **** %@\n%@")
          }
          /// Unlink your account?
          internal static let title = L10n.tr("Localizable", "pullfunds.redesign.confirmation.unlink.title", fallback: "Unlink your account?")
        }
      }
      internal enum Failed {
        internal enum Unlink {
          /// Account unlinking failed
          internal static let message = L10n.tr("Localizable", "pullfunds.redesign.failed.unlink.message", fallback: "Account unlinking failed")
        }
      }
      internal enum List {
        internal enum Payment {
          internal enum Options {
            /// Select a bank
            internal static let title = L10n.tr("Localizable", "pullfunds.redesign.list.payment.options.title", fallback: "Select a bank")
          }
        }
      }
      internal enum Success {
        internal enum Unlink {
          /// Account successfully unlinked
          internal static let message = L10n.tr("Localizable", "pullfunds.redesign.success.unlink.message", fallback: "Account successfully unlinked")
        }
      }
    }
    internal enum Spiel {
      internal enum Transaction {
        internal enum Failed {
          /// Please contact your bank or try again with a different bank account.
          internal static let message = L10n.tr("Localizable", "pullfunds.spiel.transaction.failed.message", fallback: "Please contact your bank or try again with a different bank account.")
          /// Cash in transaction failed
          internal static let title = L10n.tr("Localizable", "pullfunds.spiel.transaction.failed.title", fallback: "Cash in transaction failed")
        }
      }
    }
    internal enum Success {
      internal enum Unlink {
        internal enum Account {
          /// You have successfully unlinked your
          /// %@ account from Maya.
          internal static func message(_ p1: Any) -> String {
            return L10n.tr("Localizable", "pullfunds.success.unlink.account.message", String(describing: p1), fallback: "You have successfully unlinked your\n%@ account from Maya.")
          }
          /// Account unlinking success
          internal static let title = L10n.tr("Localizable", "pullfunds.success.unlink.account.title", fallback: "Account unlinking success")
        }
      }
    }
    internal enum Unlink {
      /// Unlink Account
      internal static let account = L10n.tr("Localizable", "pullfunds.unlink.account", fallback: "Unlink Account")
    }
  }
  internal enum Pushapproval {
    internal enum Cell {
      internal enum Date {
        /// Sent on %@
        internal static func label(_ p1: Any) -> String {
          return L10n.tr("Localizable", "pushapproval.cell.date.label", String(describing: p1), fallback: "Sent on %@")
        }
      }
    }
    internal enum Loading {
      /// Push Approval
      internal static let message = L10n.tr("Localizable", "pushapproval.loading.message", fallback: "Retrieving list")
    }
    internal enum ReviewRequest {
      internal enum Alert {
        internal enum DoneAction {
          /// Done
          internal static let title = L10n.tr("Localizable", "pushapproval.reviewRequest.alert.doneAction.title", fallback: "Done")
        }
        internal enum RequestAuthorized {
          /// Please return to your other device to complete the transaction.
          internal static let message = L10n.tr("Localizable", "pushapproval.reviewRequest.alert.requestAuthorized.message", fallback: "Please return to your other device to complete the transaction.")
          /// Request authorized
          internal static let title = L10n.tr("Localizable", "pushapproval.reviewRequest.alert.requestAuthorized.title", fallback: "Request authorized")
          internal enum Image {
            /// pmios_requestAuthorizedAlert_image_view
            internal static let accessibilityIdentifier = L10n.tr("Localizable", "pushapproval.reviewRequest.alert.requestAuthorized.image.accessibilityIdentifier", fallback: "pmios_requestAuthorizedAlert_image_view")
          }
          internal enum Message {
            /// pmios_requestAuthorizedAlert_message_label
            internal static let accessibilityIdentifier = L10n.tr("Localizable", "pushapproval.reviewRequest.alert.requestAuthorized.message.accessibilityIdentifier", fallback: "pmios_requestAuthorizedAlert_message_label")
          }
          internal enum Title {
            /// pmios_requestAuthorizedAlert_title_label
            internal static let accessibilityIdentifier = L10n.tr("Localizable", "pushapproval.reviewRequest.alert.requestAuthorized.title.accessibilityIdentifier", fallback: "pmios_requestAuthorizedAlert_title_label")
          }
        }
        internal enum RequestExpired {
          /// Please make a new request by choosing Maya on your other device.
          internal static let message = L10n.tr("Localizable", "pushapproval.reviewRequest.alert.requestExpired.message", fallback: "Please make a new request by choosing Maya on your other device.")
          /// Request expired
          internal static let title = L10n.tr("Localizable", "pushapproval.reviewRequest.alert.requestExpired.title", fallback: "Request expired")
          internal enum Image {
            /// pmios_requestExpiredAlert_image_view
            internal static let accessibilityIdentifier = L10n.tr("Localizable", "pushapproval.reviewRequest.alert.requestExpired.image.accessibilityIdentifier", fallback: "pmios_requestExpiredAlert_image_view")
          }
          internal enum Message {
            /// pmios_requestExpiredAlert_message_label
            internal static let accessibilityIdentifier = L10n.tr("Localizable", "pushapproval.reviewRequest.alert.requestExpired.message.accessibilityIdentifier", fallback: "pmios_requestExpiredAlert_message_label")
          }
          internal enum Title {
            /// pmios_requestExpiredAlert_title_label
            internal static let accessibilityIdentifier = L10n.tr("Localizable", "pushapproval.reviewRequest.alert.requestExpired.title.accessibilityIdentifier", fallback: "pmios_requestExpiredAlert_title_label")
          }
        }
      }
      internal enum Timer {
        /// Please complete within %@ seconds.
        internal static func informationText(_ p1: Any) -> String {
          return L10n.tr("Localizable", "pushapproval.reviewRequest.timer.informationText", String(describing: p1), fallback: "Please complete within %@ seconds.")
        }
        internal enum InformationText {
          /// %@ seconds
          internal static func highlightedPhrase(_ p1: Any) -> String {
            return L10n.tr("Localizable", "pushapproval.reviewRequest.timer.informationText.highlightedPhrase", String(describing: p1), fallback: "%@ seconds")
          }
        }
      }
    }
  }
  internal enum ReceiveMoney {
    internal enum Form {
      /// Amount
      internal static let amount = L10n.tr("Localizable", "receiveMoney.form.amount", fallback: "Amount")
      /// Generate
      internal static let button = L10n.tr("Localizable", "receiveMoney.form.button", fallback: "Generate")
      /// Include message
      internal static let includeMessage = L10n.tr("Localizable", "receiveMoney.form.includeMessage", fallback: "Include message")
      /// Message
      internal static let message = L10n.tr("Localizable", "receiveMoney.form.message", fallback: "Message")
      /// Receive money
      internal static let specifyAmount = L10n.tr("Localizable", "receiveMoney.form.specifyAmount", fallback: "Specify amount")
      /// Generate custom QR code
      internal static let title = L10n.tr("Localizable", "receiveMoney.form.title", fallback: "Generate custom QR code")
      internal enum Amount {
        /// Enter amount
        internal static let placeholder = L10n.tr("Localizable", "receiveMoney.form.amount.placeholder", fallback: "Enter amount")
      }
      internal enum Message {
        /// Enter message
        internal static let placeholder = L10n.tr("Localizable", "receiveMoney.form.message.placeholder", fallback: "Enter message")
      }
    }
    internal enum Maximum {
      /// Maximum amount of ₱500,000.00
      internal static let amount = L10n.tr("Localizable", "receiveMoney.maximum.amount", fallback: "Maximum amount of ₱500,000.00")
    }
    internal enum Title {
      /// Custom QR code
      internal static let custom = L10n.tr("Localizable", "receiveMoney.title.custom", fallback: "Custom QR code")
    }
  }
  internal enum Referral {
    internal enum MyInviteCode {
      /// Referral Share Code
      internal static func spiel(_ p1: Any) -> String {
        return L10n.tr("Localizable", "referral.myInviteCode.spiel", String(describing: p1), fallback: "My Invite Code is %@")
      }
    }
    internal enum MyInviteCodeLink {
      /// My Invite Code is %@
      /// or click here to create an account now: %@
      internal static func spiel(_ p1: Any, _ p2: Any) -> String {
        return L10n.tr("Localizable", "referral.myInviteCodeLink.spiel", String(describing: p1), String(describing: p2), fallback: "My Invite Code is %@\nor click here to create an account now: %@")
      }
    }
  }
  internal enum Registration {
    internal enum Email {
      internal enum Field {
        /// Email address (Optional)
        internal static let name = L10n.tr("Localizable", "registration.email.field.name", fallback: "Email address (Optional)")
        /// Enter email address
        internal static let placeholder = L10n.tr("Localizable", "registration.email.field.placeholder", fallback: "Enter email address")
        internal enum Error {
          internal enum Format {
            internal enum Regex {
              /// A valid e-mail address is required
              internal static let invalid = L10n.tr("Localizable", "registration.email.field.error.format.regex.invalid", fallback: "A valid e-mail address is required")
            }
          }
        }
      }
      internal enum Label {
        /// We’ll verify your email after you create an account.
        internal static let note = L10n.tr("Localizable", "registration.email.label.note", fallback: "We’ll verify your email after you create an account.")
      }
    }
    internal enum First {
      /// Start an
      /// account
      internal static let spiel = L10n.tr("Localizable", "registration.first.spiel", fallback: "Start an\naccount")
      internal enum Spiel {
        /// account
        internal static let account = L10n.tr("Localizable", "registration.first.spiel.account", fallback: "account")
      }
    }
    internal enum Firstname {
      /// First name is required
      internal static let `required` = L10n.tr("Localizable", "registration.firstname.required", fallback: "First name is required")
      internal enum Field {
        /// First name
        internal static let name = L10n.tr("Localizable", "registration.firstname.field.name", fallback: "First name")
        /// Enter first name
        internal static let placeholder = L10n.tr("Localizable", "registration.firstname.field.placeholder", fallback: "Enter first name")
      }
    }
    internal enum Lastname {
      /// Last name is required
      internal static let `required` = L10n.tr("Localizable", "registration.lastname.required", fallback: "Last name is required")
      internal enum Field {
        /// Last name
        internal static let name = L10n.tr("Localizable", "registration.lastname.field.name", fallback: "Last name")
        /// Enter last name
        internal static let placeholder = L10n.tr("Localizable", "registration.lastname.field.placeholder", fallback: "Enter last name")
      }
    }
    internal enum Middlename {
      /// Middle name is required
      internal static let `required` = L10n.tr("Localizable", "registration.middlename.required", fallback: "Middle name is required")
      internal enum Field {
        /// Middle name
        internal static let name = L10n.tr("Localizable", "registration.middlename.field.name", fallback: "Middle name")
        /// Enter middle name
        internal static let placeholder = L10n.tr("Localizable", "registration.middlename.field.placeholder", fallback: "Enter middle name")
      }
    }
    internal enum Mobilenumber {
      /// Mobile Number is required
      internal static let `required` = L10n.tr("Localizable", "registration.mobilenumber.required", fallback: "Mobile Number is required")
    }
    internal enum Name {
      internal enum Field {
        internal enum Error {
          internal enum Format {
            /// Cannot have character repeated 3 or more times consecutively
            internal static let repeating = L10n.tr("Localizable", "registration.name.field.error.format.repeating", fallback: "Cannot have character repeated 3 or more times consecutively")
            internal enum Regex {
              /// Only letters, dashes, and apostrophes within 50 characters are allowed
              internal static let invalid = L10n.tr("Localizable", "registration.name.field.error.format.regex.invalid", fallback: "Only letters, dashes, and apostrophes within 50 characters are allowed")
            }
          }
        }
      }
    }
    internal enum Nomiddlename {
      internal enum Checkbox {
        /// I have no legal middle name
        internal static let label = L10n.tr("Localizable", "registration.nomiddlename.checkbox.label", fallback: "I have no legal middle name")
      }
    }
    internal enum Password {
      /// Password is required
      internal static let `required` = L10n.tr("Localizable", "registration.password.required", fallback: "Password is required")
      internal enum Spiel {
        /// At least %@ characters and no spaces.
        internal static func minlength(_ p1: Any) -> String {
          return L10n.tr("Localizable", "registration.password.spiel.minlength", String(describing: p1), fallback: "At least %@ characters and no spaces.")
        }
      }
    }
    internal enum Second {
      /// Set your
      /// login details
      internal static let spiel = L10n.tr("Localizable", "registration.second.spiel", fallback: "Set your\nlogin details")
      internal enum Inlinevalidation {
        /// Please enter a valid mobile number
        internal static let validnumber = L10n.tr("Localizable", "registration.second.inlinevalidation.validnumber", fallback: "Please enter a valid mobile number")
      }
      internal enum Spiel {
        /// login details
        internal static let logindetails = L10n.tr("Localizable", "registration.second.spiel.logindetails", fallback: "login details")
      }
      internal enum Tinbo {
        /// I don’t have a Philippine SIM
        /// Get a Tinbo SIM (PLDT Global) to use with Maya
        internal static let spiel = L10n.tr("Localizable", "registration.second.tinbo.spiel", fallback: "I don’t have a Philippine SIM\nGet a Tinbo SIM (PLDT Global) to use with Maya")
        /// I don’t have a Philippine SIM
        internal static let urlSpiel = L10n.tr("Localizable", "registration.second.tinbo.urlSpiel", fallback: "I don’t have a Philippine SIM")
        internal enum Alert {
          /// Get a smart virtual number powered by Tinbo (from PLDT Global). You’ll be redirected to an external site by our third-party partner.
          internal static let message = L10n.tr("Localizable", "registration.second.tinbo.alert.message", fallback: "Get a smart virtual number powered by Tinbo (from PLDT Global). You’ll be redirected to an external site by our third-party partner.")
          /// Don’t have a Philippine SIM?
          internal static let title = L10n.tr("Localizable", "registration.second.tinbo.alert.title", fallback: "Don’t have a Philippine SIM?")
          internal enum Button {
            /// Open tinbo.ph
            internal static let title = L10n.tr("Localizable", "registration.second.tinbo.alert.button.title", fallback: "Open tinbo.ph")
          }
        }
      }
    }
    internal enum Spiel {
      /// Registration screen
      internal static let resendCode = L10n.tr("Localizable", "registration.spiel.resendCode", fallback: "Resend Code")
    }
  }
  internal enum Rekyc {
    internal enum OtherId {
      internal enum Spiel {
        /// 1
        internal static let first = L10n.tr("Localizable", "rekyc.otherId.spiel.first", fallback: "1")
        /// We’ll notify you once we receive and validate your IDs.
        internal static let note = L10n.tr("Localizable", "rekyc.otherId.spiel.note", fallback: "We’ll notify you once we receive and validate your IDs.")
        /// 2
        internal static let second = L10n.tr("Localizable", "rekyc.otherId.spiel.second", fallback: "2")
        /// Updating your account with Other IDs
        internal static let title = L10n.tr("Localizable", "rekyc.otherId.spiel.title", fallback: "Updating your account with Other IDs")
        internal enum Button {
          /// Got it
          internal static let title = L10n.tr("Localizable", "rekyc.otherId.spiel.button.title", fallback: "Got it")
        }
        internal enum First {
          /// One of the following:
          ///   • UMID
          ///   • Philippine Driver’s License
          ///   • Philippine Passport
          ///   • Postal ID
          ///   • PRC ID
          ///   • SSS
          ///   • Foreign Passport
          ///   • Philippine National ID / Phil ID
          ///   • ePhil ID (Printed Phil ID)
          ///   • NBI Clearance
          ///   • ACR i-Card
          ///   • Government Office / GOCC ID
          ///   • IBP Card
          ///   • School ID
          /// Any 2 of the following:
          ///   • PhilHealth ID
          ///   • TIN ID
          ///   • Voter's ID
          ///   • Police Clearance
          ///   • Senior Citizen’s Card
          ///   • GSIS e-Card
          ///   • OWWA ID/OFW e-Card
          internal static let note = L10n.tr("Localizable", "rekyc.otherId.spiel.first.note", fallback: "One of the following:\n  • UMID\n  • Philippine Driver’s License\n  • Philippine Passport\n  • Postal ID\n  • PRC ID\n  • SSS\n  • Foreign Passport\n  • Philippine National ID / Phil ID\n  • ePhil ID (Printed Phil ID)\n  • NBI Clearance\n  • ACR i-Card\n  • Government Office / GOCC ID\n  • IBP Card\n  • School ID\nAny 2 of the following:\n  • PhilHealth ID\n  • TIN ID\n  • Voter's ID\n  • Police Clearance\n  • Senior Citizen’s Card\n  • GSIS e-Card\n  • OWWA ID/OFW e-Card")
          /// Take a picture of any of the IDs below. Make sure to include the FRONT and BACK of your ID.
          internal static let title = L10n.tr("Localizable", "rekyc.otherId.spiel.first.title", fallback: "Take a picture of any of the IDs below. Make sure to include the FRONT and BACK of your ID.")
        }
        internal enum Second {
          /// Email your ID with the subject:
          /// Account Update for <your mobile number>
          /// Include in the email the following :
          ///   • Your full name
          ///   • Mobile number you use in Maya
          ///   • The type of ID you submitted
          ///   • ID number
          ///   • ID expiry date
          internal static let note = L10n.tr("Localizable", "rekyc.otherId.spiel.second.note", fallback: "Email your ID with the subject:\nAccount Update for <your mobile number>\nInclude in the email the following :\n  • Your full name\n  • Mobile number you use in Maya\n  • The type of ID you submitted\n  • ID number\n  • ID expiry date")
          /// Email your <NAME_EMAIL>
          internal static let title = L10n.tr("Localizable", "rekyc.otherId.spiel.second.title", fallback: "Email your <NAME_EMAIL>")
          internal enum Note {
            /// Account Update for <your mobile number>
            internal static let highlight = L10n.tr("Localizable", "rekyc.otherId.spiel.second.note.highlight", fallback: "Account Update for <your mobile number>")
          }
        }
      }
    }
    internal enum Spiel {
      /// Upon checking, we found that your information is in need of updating. In compliance with BSP Regulations and for your protection, please ensure your personal information is up-to-date.
      /// 
      /// Rest assured, your funds are secure and will be accessible after you update.
      internal static let message = L10n.tr("Localizable", "rekyc.spiel.message", fallback: "Upon checking, we found that your information is in need of updating. In compliance with BSP Regulations and for your protection, please ensure your personal information is up-to-date.\n\nRest assured, your funds are secure and will be accessible after you update.")
      /// Re-KYC
      internal static let title = L10n.tr("Localizable", "rekyc.spiel.title", fallback: "Update your account and enjoy all of Maya’s features")
      internal enum Feature {
        /// Get credit fast
        internal static let credit = L10n.tr("Localizable", "rekyc.spiel.feature.credit", fallback: "Get credit fast")
        /// Invest in crypto
        internal static let crypto = L10n.tr("Localizable", "rekyc.spiel.feature.crypto", fallback: "Invest in crypto")
        /// Get a Savings Account
        internal static let savings = L10n.tr("Localizable", "rekyc.spiel.feature.savings", fallback: "Get a Savings Account")
      }
      internal enum Primary {
        /// Update now
        internal static let button = L10n.tr("Localizable", "rekyc.spiel.primary.button", fallback: "Update now")
      }
      internal enum Secondary {
        /// I'll do it later
        internal static let button = L10n.tr("Localizable", "rekyc.spiel.secondary.button", fallback: "I'll do it later")
      }
    }
    internal enum Under {
      internal enum Review {
        internal enum Secondary {
          /// View status
          internal static let button = L10n.tr("Localizable", "rekyc.under.review.Secondary.button", fallback: "View status")
        }
        internal enum Spiel {
          /// Please give us up to 24 hours to finish reviewing your submitted information.
          internal static let message = L10n.tr("Localizable", "rekyc.under.review.spiel.message", fallback: "Please give us up to 24 hours to finish reviewing your submitted information.")
          /// Under review
          internal static let title = L10n.tr("Localizable", "rekyc.under.review.spiel.title", fallback: "Under review")
        }
      }
    }
  }
  internal enum Request {
    internal enum Money {
      internal enum Account {
        internal enum Limits {
          /// Account Limits
          internal static let text = L10n.tr("Localizable", "request.money.account.limits.text", fallback: "Account Limits")
          internal enum Full {
            /// Make sure your account can still accommodate the amount of this request. Refer to your Account Limits for more details.
            internal static let message = L10n.tr("Localizable", "request.money.account.limits.full.message", fallback: "Make sure your account can still accommodate the amount of this request. Refer to your Account Limits for more details.")
          }
        }
      }
      internal enum Maximum {
        /// Maximum amount of PHP 500,000.00
        internal static let amount = L10n.tr("Localizable", "request.money.maximum.amount", fallback: "Maximum amount of PHP 500,000.00")
      }
      internal enum Remaining {
        /// %@ character remaining
        internal static func character(_ p1: Any) -> String {
          return L10n.tr("Localizable", "request.money.remaining.character", String(describing: p1), fallback: "%@ character remaining")
        }
        /// %@ characters remaining
        internal static func characters(_ p1: Any) -> String {
          return L10n.tr("Localizable", "request.money.remaining.characters", String(describing: p1), fallback: "%@ characters remaining")
        }
      }
      internal enum Required {
        /// Request Money
        internal static let amount = L10n.tr("Localizable", "request.money.required.amount", fallback: "Please enter an amount to request.")
      }
      internal enum Share {
        /// You have a money request from %@. Please scan the QR provided or tap the link below to proceed.
        /// https://www.paymaya.com/app/sendmoney?p=%@
        internal static func generic(_ p1: Any, _ p2: Any) -> String {
          return L10n.tr("Localizable", "request.money.share.generic", String(describing: p1), String(describing: p2), fallback: "You have a money request from %@. Please scan the QR provided or tap the link below to proceed.\nhttps://www.paymaya.com/app/sendmoney?p=%@")
        }
      }
    }
  }
  internal enum Scanner {
    internal enum Instructions {
      /// Scanner
      internal static let `default` = L10n.tr("Localizable", "scanner.instructions.default", fallback: "Align QR Code to scan.")
      internal enum Invalid {
        /// QR Code Invalid. 
        /// Please scan another code.
        internal static let qr = L10n.tr("Localizable", "scanner.instructions.invalid.qr", fallback: "QR Code Invalid. \nPlease scan another code.")
        internal enum Photo {
          internal enum Error {
            /// Uh oh! We can’t seem to read the QR you selected. Please try again with a clearer image or make sure to use a PayMaya QR code.
            internal static let message = L10n.tr("Localizable", "scanner.instructions.invalid.photo.error.message", fallback: "Uh oh! We can’t seem to read the QR you selected. Please try again with a clearer image or make sure to use a PayMaya QR code.")
            /// Import QR Error
            internal static let title = L10n.tr("Localizable", "scanner.instructions.invalid.photo.error.title", fallback: "Import QR Error")
          }
        }
      }
    }
  }
  internal enum Sendmoney {
    internal enum Activity {
      internal enum Title {
        /// You have received
        internal static let receiver = L10n.tr("Localizable", "sendmoney.activity.title.receiver", fallback: "You have received")
        /// You have sent
        internal static let sender = L10n.tr("Localizable", "sendmoney.activity.title.sender", fallback: "You have sent")
      }
    }
    internal enum AddFavorites {
      internal enum Alias {
        internal enum Example {
          /// House, Postpaid
          internal static let label = L10n.tr("Localizable", "sendmoney.addFavorites.alias.example.label", fallback: "House, Postpaid")
        }
      }
    }
    internal enum Bank {
      /// Bank Details
      internal static let details = L10n.tr("Localizable", "sendmoney.bank.details", fallback: "Bank Details")
      /// Bank Transfer
      internal static let title = L10n.tr("Localizable", "sendmoney.bank.title", fallback: "Bank Transfer")
      internal enum Field {
        /// Account Number
        internal static let accountNumber = L10n.tr("Localizable", "sendmoney.bank.field.accountNumber", fallback: "Account Number")
        /// Amount
        internal static let amount = L10n.tr("Localizable", "sendmoney.bank.field.amount", fallback: "Amount")
        /// First Name
        internal static let firstName = L10n.tr("Localizable", "sendmoney.bank.field.firstName", fallback: "First Name")
        /// Last Name
        internal static let lastName = L10n.tr("Localizable", "sendmoney.bank.field.lastName", fallback: "Last Name")
        /// Purpose
        internal static let purpose = L10n.tr("Localizable", "sendmoney.bank.field.purpose", fallback: "Purpose")
      }
      internal enum Success {
        internal enum Detail {
          /// Account Name
          internal static let accountName = L10n.tr("Localizable", "sendmoney.bank.success.detail.accountName", fallback: "Account Name")
          /// Account Number
          internal static let accountNumber = L10n.tr("Localizable", "sendmoney.bank.success.detail.accountNumber", fallback: "Account Number")
          /// Bank
          internal static let accountType = L10n.tr("Localizable", "sendmoney.bank.success.detail.accountType", fallback: "Bank")
          /// Amount
          internal static let amount = L10n.tr("Localizable", "sendmoney.bank.success.detail.amount", fallback: "Amount")
          /// Transaction Fee
          internal static let fee = L10n.tr("Localizable", "sendmoney.bank.success.detail.fee", fallback: "Transaction Fee")
          /// You may confirm the status of your transaction with your recipient.
          internal static let hint = L10n.tr("Localizable", "sendmoney.bank.success.detail.hint", fallback: "You may confirm the status of your transaction with your recipient.")
          /// Reference ID
          internal static let id = L10n.tr("Localizable", "sendmoney.bank.success.detail.id", fallback: "Reference ID")
          /// Recipient Mobile Number
          internal static let mobile = L10n.tr("Localizable", "sendmoney.bank.success.detail.mobile", fallback: "Recipient Mobile Number")
          /// Purpose
          internal static let purpose = L10n.tr("Localizable", "sendmoney.bank.success.detail.purpose", fallback: "Purpose")
          internal enum AccountNo {
            /// Account No.:
            internal static let colon = L10n.tr("Localizable", "sendmoney.bank.success.detail.accountNo.colon", fallback: "Account No.:")
          }
        }
        internal enum Receipt {
          /// Processing Bank Transfer Transaction
          internal static let title = L10n.tr("Localizable", "sendmoney.bank.success.receipt.title", fallback: "Processing Bank Transfer Transaction")
        }
      }
    }
    internal enum Card {
      internal enum Receiver {
        /// Send Money Cards
        internal static func shareMesage(_ p1: Any, _ p2: Any) -> String {
          return L10n.tr("Localizable", "sendmoney.card.receiver.shareMesage", String(describing: p1), String(describing: p2), fallback: "You have received %@ on your PayMaya account from PayMaya user %@.")
        }
      }
      internal enum Sender {
        /// You have successfully sent %@ from your PayMaya account to PayMaya user %@.
        internal static func shareMesage(_ p1: Any, _ p2: Any) -> String {
          return L10n.tr("Localizable", "sendmoney.card.sender.shareMesage", String(describing: p1), String(describing: p2), fallback: "You have successfully sent %@ from your PayMaya account to PayMaya user %@.")
        }
      }
    }
    internal enum Confirmation {
      internal enum Button {
        /// Send now
        internal static let sendNow = L10n.tr("Localizable", "sendmoney.confirmation.button.sendNow", fallback: "Send now")
      }
    }
    internal enum Favorites {
      internal enum Details {
        /// Send money details
        internal static let spiel = L10n.tr("Localizable", "sendmoney.favorites.details.spiel", fallback: "Send money details")
      }
      internal enum Success {
        internal enum Message {
          /// You can now easily send money under favorites.
          internal static let spiel = L10n.tr("Localizable", "sendmoney.favorites.success.message.spiel", fallback: "You can now easily send money under favorites.")
        }
      }
    }
    internal enum Field {
      /// Account Type
      internal static let accountType = L10n.tr("Localizable", "sendmoney.field.accountType", fallback: "Account Type")
      /// Amount
      internal static let amount = L10n.tr("Localizable", "sendmoney.field.amount", fallback: "Amount")
      /// Message
      internal static let message = L10n.tr("Localizable", "sendmoney.field.message", fallback: "Message")
      /// Name, Mobile or Account Number
      internal static let recipient = L10n.tr("Localizable", "sendmoney.field.recipient", fallback: "Name, Mobile or Account Number")
      internal enum Recipient {
        internal enum Or {
          /// Recipient/Account Number
          internal static let accountNumber = L10n.tr("Localizable", "sendmoney.field.recipient.or.accountNumber", fallback: "Recipient/Account Number")
        }
      }
    }
    internal enum Form {
      internal enum Amount {
        /// Maya Send Money
        internal static let `default` = L10n.tr("Localizable", "sendmoney.form.amount.default", fallback: "₱0")
        /// Transfer fees may apply.
        internal static let fees = L10n.tr("Localizable", "sendmoney.form.amount.fees", fallback: "Transfer fees may apply.")
        /// You have %@ in your wallet.
        internal static func info(_ p1: Any) -> String {
          return L10n.tr("Localizable", "sendmoney.form.amount.info", String(describing: p1), fallback: "You have %@ in your wallet.")
        }
        /// You only have %@ in your wallet.
        internal static func insufficient(_ p1: Any) -> String {
          return L10n.tr("Localizable", "sendmoney.form.amount.insufficient", String(describing: p1), fallback: "You only have %@ in your wallet.")
        }
        /// Amount is required. You have %@ in your wallet.
        internal static func `required`(_ p1: Any) -> String {
          return L10n.tr("Localizable", "sendmoney.form.amount.required", String(describing: p1), fallback: "Amount is required. You have %@ in your wallet.")
        }
      }
    }
    internal enum Maya {
      internal enum Message {
        /// - from %@
        internal static func signature(_ p1: Any) -> String {
          return L10n.tr("Localizable", "sendmoney.maya.message.signature", String(describing: p1), fallback: "- from %@")
        }
      }
    }
    internal enum Recipient {
      /// Recipient Details
      internal static let details = L10n.tr("Localizable", "sendmoney.recipient.details", fallback: "Recipient Details")
    }
    internal enum RecipientType {
      /// PayMaya
      internal static let paymaya = L10n.tr("Localizable", "sendmoney.recipientType.paymaya", fallback: "PayMaya")
      /// Send Money
      internal static let smartPadala = L10n.tr("Localizable", "sendmoney.recipientType.smartPadala", fallback: "Smart Padala by PayMaya")
    }
    internal enum Spiel {
      /// - From
      internal static let from = L10n.tr("Localizable", "sendmoney.spiel.from", fallback: "- From")
      /// This field is required
      internal static let `required` = L10n.tr("Localizable", "sendmoney.spiel.required", fallback: "This field is required")
      /// Transfer Fee: PHP 
      internal static let transferFee = L10n.tr("Localizable", "sendmoney.spiel.transferFee", fallback: "Transfer Fee: PHP ")
      internal enum Required {
        /// Account number is required. Please try again.
        internal static let accountNumber = L10n.tr("Localizable", "sendmoney.spiel.required.accountNumber", fallback: "Account number is required. Please try again.")
        /// Amount is required. Please try again.
        internal static let amount = L10n.tr("Localizable", "sendmoney.spiel.required.amount", fallback: "Amount is required. Please try again.")
        /// Bank is required. Please try again.
        internal static let bank = L10n.tr("Localizable", "sendmoney.spiel.required.bank", fallback: "Bank is required. Please try again.")
        /// First name is required. Please try again.
        internal static let firstName = L10n.tr("Localizable", "sendmoney.spiel.required.firstName", fallback: "First name is required. Please try again.")
        /// Last name is required. Please try again.
        internal static let lastName = L10n.tr("Localizable", "sendmoney.spiel.required.lastName", fallback: "Last name is required. Please try again.")
        /// Purpose is required. Please try again.
        internal static let purpose = L10n.tr("Localizable", "sendmoney.spiel.required.purpose", fallback: "Purpose is required. Please try again.")
        /// Name, Mobile or Account Number is required. Please try again.
        internal static let recipient = L10n.tr("Localizable", "sendmoney.spiel.required.recipient", fallback: "Name, Mobile or Account Number is required. Please try again.")
      }
    }
    internal enum Success {
      /// Send Money Successful!
      internal static let title = L10n.tr("Localizable", "sendmoney.success.title", fallback: "Send Money Successful!")
      internal enum Bottomsheet {
        /// From your wallet to %@
        internal static func message(_ p1: Any) -> String {
          return L10n.tr("Localizable", "sendmoney.success.bottomsheet.message", String(describing: p1), fallback: "From your wallet to %@")
        }
        /// You've successfully sent %@
        internal static func title(_ p1: Any) -> String {
          return L10n.tr("Localizable", "sendmoney.success.bottomsheet.title", String(describing: p1), fallback: "You've successfully sent %@")
        }
      }
      internal enum Detail {
        /// Recipient
        internal static let recipient = L10n.tr("Localizable", "sendmoney.success.detail.recipient", fallback: "Recipient")
        /// Transfer Fee
        internal static let transferFee = L10n.tr("Localizable", "sendmoney.success.detail.transferFee", fallback: "Transfer Fee")
        internal enum TransferFee {
          /// Transfer Fee:
          internal static let colon = L10n.tr("Localizable", "sendmoney.success.detail.transferFee.colon", fallback: "Transfer Fee:")
        }
      }
    }
    internal enum Title {
      internal enum AccountType {
        /// Maya
        internal static let maya = L10n.tr("Localizable", "sendmoney.title.accountType.maya", fallback: "Maya")
        /// Maya Center
        internal static let mayaCenter = L10n.tr("Localizable", "sendmoney.title.accountType.mayaCenter", fallback: "Maya Center")
      }
    }
  }
  internal enum Services {
    internal enum Actions {
      internal enum Title {
        /// About Maya
        internal static let about = L10n.tr("Localizable", "services.actions.title.about", fallback: "About Maya")
        /// Help Center
        internal static let help = L10n.tr("Localizable", "services.actions.title.help", fallback: "Help Center")
        /// Chat with Maya
        internal static let messages = L10n.tr("Localizable", "services.actions.title.messages", fallback: "Chat with Maya")
        /// My profile
        internal static let profile = L10n.tr("Localizable", "services.actions.title.profile", fallback: "My profile")
        /// Security center
        internal static let security = L10n.tr("Localizable", "services.actions.title.security", fallback: "Security center")
        /// Settings
        internal static let settings = L10n.tr("Localizable", "services.actions.title.settings", fallback: "Settings")
      }
    }
    internal enum Category {
      internal enum Title {
        /// Maya Services
        internal static let essentials = L10n.tr("Localizable", "services.category.title.essentials", fallback: "Essentials")
        /// Lifestyle
        internal static let lifestyle = L10n.tr("Localizable", "services.category.title.lifestyle", fallback: "Lifestyle")
        /// Money Makers
        internal static let moneyMakers = L10n.tr("Localizable", "services.category.title.moneyMakers", fallback: "Money Makers")
        /// Payments
        internal static let payments = L10n.tr("Localizable", "services.category.title.payments", fallback: "Payments")
        /// Rewards
        internal static let rewards = L10n.tr("Localizable", "services.category.title.rewards", fallback: "Rewards")
      }
    }
    internal enum DynamicStatus {
      internal enum New {
        /// NEW
        internal static let title = L10n.tr("Localizable", "services.dynamicStatus.new.title", fallback: "NEW")
      }
    }
    internal enum Underage {
      /// You do not currently meet the age required for this feature. For now, feel free to explore the rest of the app!
      internal static let message = L10n.tr("Localizable", "services.underage.message", fallback: "You do not currently meet the age required for this feature. For now, feel free to explore the rest of the app!")
      /// 21 and up only
      internal static let title = L10n.tr("Localizable", "services.underage.title", fallback: "21 and up only")
    }
  }
  internal enum Sessiontimeout {
    internal enum Login {
      /// Session Timeout
      internal static func biometry(_ p1: Any) -> String {
        return L10n.tr("Localizable", "sessiontimeout.login.biometry", String(describing: p1), fallback: "Use %@ to Login")
      }
      internal enum FaceId {
        /// Log in with Face ID
        internal static let spiel = L10n.tr("Localizable", "sessiontimeout.login.faceId.spiel", fallback: "Log in with Face ID")
      }
      internal enum Hello {
        /// Hello, %@
        internal static func user(_ p1: Any) -> String {
          return L10n.tr("Localizable", "sessiontimeout.login.hello.user", String(describing: p1), fallback: "Hello, %@")
        }
      }
      internal enum Passcode {
        /// Log in with Device Passcode
        internal static let spiel = L10n.tr("Localizable", "sessiontimeout.login.passcode.spiel", fallback: "Log in with Device Passcode")
      }
      internal enum TouchId {
        /// Log in with your fingerprint
        internal static let spiel = L10n.tr("Localizable", "sessiontimeout.login.touchId.spiel", fallback: "Log in with your fingerprint")
      }
    }
  }
  internal enum SetPassword {
    /// This will be used for login and account recovery
    internal static let subtitle = L10n.tr("Localizable", "setPassword.subtitle", fallback: "This will be used for login and account recovery")
    /// Tips for a secure password
    internal static let tips = L10n.tr("Localizable", "setPassword.tips", fallback: "Tips for a secure password")
    /// Set Password Screen
    internal static let title = L10n.tr("Localizable", "setPassword.title", fallback: "Set a new password")
    internal enum ConfirmPassword {
      /// Confirm password
      internal static let title = L10n.tr("Localizable", "setPassword.confirmPassword.title", fallback: "Confirm password")
    }
    internal enum NewPassword {
      /// Enter new password
      internal static let placeholder = L10n.tr("Localizable", "setPassword.newPassword.placeholder", fallback: "Enter new password")
      /// New password
      internal static let title = L10n.tr("Localizable", "setPassword.newPassword.title", fallback: "New password")
    }
  }
  internal enum Settings {
    internal enum Biometrics {
      /// Device Passcode
      internal static let `default` = L10n.tr("Localizable", "settings.biometrics.default", fallback: "Device Passcode")
      /// Face ID
      internal static let faceId = L10n.tr("Localizable", "settings.biometrics.faceId", fallback: "Face ID")
      /// Touch ID
      internal static let touchId = L10n.tr("Localizable", "settings.biometrics.touchId", fallback: "Touch ID")
      internal enum Disabled {
        internal enum Confirmation {
          /// This will prevent you from using your phone lock credentials to login to PayMaya. Please enter your password to confirm
          internal static let message = L10n.tr("Localizable", "settings.biometrics.disabled.confirmation.message", fallback: "This will prevent you from using your phone lock credentials to login to PayMaya. Please enter your password to confirm")
        }
      }
      internal enum Enabled {
        internal enum Confirmation {
          /// This feature will allow you to use your phone lock credentials to login to PayMaya. Please enter your PayMaya password to confirm
          internal static let message = L10n.tr("Localizable", "settings.biometrics.enabled.confirmation.message", fallback: "This feature will allow you to use your phone lock credentials to login to PayMaya. Please enter your PayMaya password to confirm")
        }
      }
      internal enum Error {
        /// There was a problem encountered processing this request
        internal static let message = L10n.tr("Localizable", "settings.biometrics.error.message", fallback: "There was a problem encountered processing this request")
      }
    }
    internal enum Menu {
      internal enum Title {
        /// Mobile Number
        internal static let mobileNumber = L10n.tr("Localizable", "settings.menu.title.mobileNumber", fallback: "Mobile Number")
        /// Settings screen
        internal static let privacy = L10n.tr("Localizable", "settings.menu.title.privacy", fallback: "Privacy Policy")
        /// Terms of Service
        internal static let terms = L10n.tr("Localizable", "settings.menu.title.terms", fallback: "Terms of Service")
        /// Terms and Conditions
        internal static let tnc = L10n.tr("Localizable", "settings.menu.title.tnc", fallback: "Terms and Conditions")
        internal enum Account {
          /// Account Closure
          internal static let closure = L10n.tr("Localizable", "settings.menu.title.account.closure", fallback: "Account Closure")
        }
      }
    }
    internal enum Password {
      internal enum Invalid {
        /// You have entered an invalid password. Please re-enter your PayMaya password.
        internal static let format = L10n.tr("Localizable", "settings.password.invalid.format", fallback: "You have entered an invalid password. Please re-enter your PayMaya password.")
      }
      internal enum Not {
        /// Passwords do not match
        internal static let match = L10n.tr("Localizable", "settings.password.not.match", fallback: "Passwords do not match")
      }
    }
    internal enum Version {
      /// Version %@ (%@)
      internal static func number(_ p1: Any, _ p2: Any) -> String {
        return L10n.tr("Localizable", "settings.version.number", String(describing: p1), String(describing: p2), fallback: "Version %@ (%@)")
      }
    }
  }
  internal enum Share {
    internal enum Maya {
      internal enum Paybills {
        internal enum No {
          internal enum Acct {
            /// Paid %@ %@ from my Maya Account to %@ with Ref# %@ on %@
            internal static func number(_ p1: Any, _ p2: Any, _ p3: Any, _ p4: Any, _ p5: Any) -> String {
              return L10n.tr("Localizable", "share.maya.paybills.no.acct.number", String(describing: p1), String(describing: p2), String(describing: p3), String(describing: p4), String(describing: p5), fallback: "Paid %@ %@ from my Maya Account to %@ with Ref# %@ on %@")
            }
          }
        }
      }
    }
    internal enum Paybills {
      internal enum No {
        internal enum Acct {
          /// Share
          internal static func number(_ p1: Any, _ p2: Any, _ p3: Any, _ p4: Any, _ p5: Any) -> String {
            return L10n.tr("Localizable", "share.paybills.no.acct.number", String(describing: p1), String(describing: p2), String(describing: p3), String(describing: p4), String(describing: p5), fallback: "Paid %@ %@ from my PayMaya Account to %@ with Ref# %@ on %@")
          }
        }
      }
    }
  }
  internal enum Shop {
    internal enum Home {
      internal enum Section {
        internal enum BrandsForYou {
          internal enum Maya {
            /// BRANDS FOR YOU
            internal static let title = L10n.tr("Localizable", "shop.home.section.brandsForYou.maya.title", fallback: "BRANDS FOR YOU")
          }
        }
        internal enum BuyAgain {
          internal enum Maya {
            /// BUY AGAIN
            internal static let title = L10n.tr("Localizable", "shop.home.section.buyAgain.maya.title", fallback: "BUY AGAIN")
          }
        }
        internal enum Deals {
          internal enum Maya {
            /// DEALS & PROMOS
            internal static let title = L10n.tr("Localizable", "shop.home.section.deals.maya.title", fallback: "DEALS & PROMOS")
          }
        }
        internal enum Load {
          internal enum Maya {
            /// LOAD
            internal static let title = L10n.tr("Localizable", "shop.home.section.load.maya.title", fallback: "LOAD")
          }
        }
        internal enum MostPopular {
          internal enum Maya {
            /// MOST POPULAR
            internal static let title = L10n.tr("Localizable", "shop.home.section.mostPopular.maya.title", fallback: "MOST POPULAR")
          }
        }
      }
    }
    internal enum Notification {
      /// Successful Shop Purchase
      internal static let success = L10n.tr("Localizable", "shop.notification.success", fallback: "Successful Shop Purchase")
      internal enum Success {
        /// Tap here to check the details
        internal static let details = L10n.tr("Localizable", "shop.notification.success.details", fallback: "Tap here to check the details")
      }
    }
    internal enum Payments {
      internal enum Contact {
        internal enum Mobile {
          internal enum Number {
            internal enum Display {
              /// %@ (%@)
              internal static func text(_ p1: Any, _ p2: Any) -> String {
                return L10n.tr("Localizable", "shop.payments.contact.mobile.number.display.text", String(describing: p1), String(describing: p2), fallback: "%@ (%@)")
              }
            }
          }
        }
      }
      internal enum Mobile {
        internal enum Number {
          internal enum Display {
            /// You (%@)
            internal static func text(_ p1: Any) -> String {
              return L10n.tr("Localizable", "shop.payments.mobile.number.display.text", String(describing: p1), fallback: "You (%@)")
            }
          }
        }
      }
      internal enum Recipient {
        internal enum Mobile {
          internal enum Number {
            internal enum Placeholder {
              /// Recipient's Mobile Number
              internal static let text = L10n.tr("Localizable", "shop.payments.recipient.mobile.number.placeholder.text", fallback: "Recipient's Mobile Number")
            }
          }
        }
      }
      internal enum Signature {
        internal enum Display {
          /// From %@
          internal static func text(_ p1: Any) -> String {
            return L10n.tr("Localizable", "shop.payments.signature.display.text", String(describing: p1), fallback: "From %@")
          }
        }
      }
    }
    internal enum Placeholder {
      internal enum Beep {
        /// Beep Card Account Number
        internal static let accountNumber = L10n.tr("Localizable", "shop.placeholder.beep.accountNumber", fallback: "Beep Card Account Number")
      }
      internal enum Cignal {
        /// Cignal Account Number
        internal static let accountNumber = L10n.tr("Localizable", "shop.placeholder.cignal.accountNumber", fallback: "Cignal Account Number")
      }
    }
    internal enum Product {
      internal enum Disabled {
        internal enum Price {
          /// Not Available
          internal static let message = L10n.tr("Localizable", "shop.product.disabled.price.message", fallback: "Not Available")
        }
      }
    }
    internal enum Purchase {
      internal enum History {
        /// Purchase History
        internal static let title = L10n.tr("Localizable", "shop.purchase.history.title", fallback: "Purchase History")
        internal enum Account {
          internal enum Number {
            /// Account Number
            internal static let title = L10n.tr("Localizable", "shop.purchase.history.account.number.title", fallback: "Account Number")
          }
        }
        internal enum Completed {
          /// Completed
          internal static let title = L10n.tr("Localizable", "shop.purchase.history.completed.title", fallback: "Completed")
        }
        internal enum Failure {
          /// We tried but failed with your purchase. No worries, refund is on its way.
          internal static let spiel = L10n.tr("Localizable", "shop.purchase.history.failure.spiel", fallback: "We tried but failed with your purchase. No worries, refund is on its way.")
          /// Something went wrong
          internal static let title = L10n.tr("Localizable", "shop.purchase.history.failure.title", fallback: "Something went wrong")
        }
        internal enum Processing {
          /// Shop Purchase History
          internal static let title = L10n.tr("Localizable", "shop.purchase.history.processing.title", fallback: "Processing")
          internal enum Empty {
            internal enum Default {
              /// Visit our shop and purchase from our wide variety of choices.
              internal static let spiel = L10n.tr("Localizable", "shop.purchase.history.processing.empty.default.spiel", fallback: "Visit our shop and purchase from our wide variety of choices.")
            }
            internal enum Final {
              /// High-five! All purchases have already been processed. Check the Completed tab for your full purchase history.
              internal static let spiel = L10n.tr("Localizable", "shop.purchase.history.processing.empty.final.spiel", fallback: "High-five! All purchases have already been processed. Check the Completed tab for your full purchase history.")
            }
          }
        }
      }
      internal enum Title {
        /// Buy
        internal static let buy = L10n.tr("Localizable", "shop.purchase.title.buy", fallback: "Buy")
      }
    }
    internal enum Search {
      internal enum Error {
        /// Could not load more items. Please try again.
        internal static let message = L10n.tr("Localizable", "shop.search.error.message", fallback: "Could not load more items. Please try again.")
      }
    }
    internal enum Spiel {
      internal enum Beep {
        /// Your beep™ card balance will be updated once you tap your card on the beep™ e-Load/Ticket Vending Machine.
        internal static let confirmation = L10n.tr("Localizable", "shop.spiel.beep.confirmation", fallback: "Your beep™ card balance will be updated once you tap your card on the beep™ e-Load/Ticket Vending Machine.")
      }
      internal enum Detail {
        internal enum Placeholder {
          /// Enter
          internal static let enter = L10n.tr("Localizable", "shop.spiel.detail.placeholder.enter", fallback: "Enter")
        }
        internal enum Title {
          /// Contact
          internal static let contact = L10n.tr("Localizable", "shop.spiel.detail.title.contact", fallback: "Contact")
          /// Optional message
          internal static let message = L10n.tr("Localizable", "shop.spiel.detail.title.message", fallback: "Optional message")
          /// Recipient
          internal static let recipient = L10n.tr("Localizable", "shop.spiel.detail.title.recipient", fallback: "Recipient")
          /// Include signature:
          internal static let signature = L10n.tr("Localizable", "shop.spiel.detail.title.signature", fallback: "Include signature:")
          /// Source
          internal static let source = L10n.tr("Localizable", "shop.spiel.detail.title.source", fallback: "Source")
          internal enum Message {
            /// Add a message
            internal static let placeholder = L10n.tr("Localizable", "shop.spiel.detail.title.message.placeholder", fallback: "Add a message")
          }
        }
      }
      internal enum Required {
        internal enum Payments {
          /// Shop screen
          internal static func accountNumber(_ p1: Any) -> String {
            return L10n.tr("Localizable", "shop.spiel.required.payments.accountNumber.", String(describing: p1), fallback: "%@ is required. Please try again.")
          }
        }
      }
    }
  }
  internal enum Shortened {
    internal enum Registration {
      /// Continue
      internal static let `continue` = L10n.tr("Localizable", "shortened.registration.continue", fallback: "Continue")
      /// Shortened registration
      internal static let title = L10n.tr("Localizable", "shortened.registration.title", fallback: "Create an account")
      internal enum Data {
        internal enum Personalization {
          /// We’ll use your data to understand your needs, personalize features, and work with partners to offer relevant products
          internal static let description = L10n.tr("Localizable", "shortened.registration.data.personalization.description", fallback: "We’ll use your data to understand your needs, personalize features, and work with partners to offer relevant products")
          /// Allow data use to improve our offers and services
          internal static let title = L10n.tr("Localizable", "shortened.registration.data.personalization.title", fallback: "Allow data use to improve our offers and services")
        }
      }
      internal enum Email {
        internal enum Field {
          /// Email address (Optional)
          internal static let name = L10n.tr("Localizable", "shortened.registration.email.field.name", fallback: "Email address (Optional)")
          /// Your email address
          internal static let placeholder = L10n.tr("Localizable", "shortened.registration.email.field.placeholder", fallback: "Your email address")
          /// We'll verify your email after you register
          internal static let subtitle = L10n.tr("Localizable", "shortened.registration.email.field.subtitle", fallback: "We'll verify your email after you register")
        }
      }
      internal enum Firstname {
        internal enum Field {
          /// First name
          internal static let name = L10n.tr("Localizable", "shortened.registration.firstname.field.name", fallback: "First name")
          /// Your first name
          internal static let placeholder = L10n.tr("Localizable", "shortened.registration.firstname.field.placeholder", fallback: "Your first name")
        }
      }
      internal enum FlowProgressBar {
        /// %@/2
        internal static func progress(_ p1: Any) -> String {
          return L10n.tr("Localizable", "shortened.registration.flowProgressBar.progress", String(describing: p1), fallback: "%@/2")
        }
      }
      internal enum Lastname {
        internal enum Field {
          /// Last name
          internal static let name = L10n.tr("Localizable", "shortened.registration.lastname.field.name", fallback: "Last name")
          /// Your last name
          internal static let placeholder = L10n.tr("Localizable", "shortened.registration.lastname.field.placeholder", fallback: "Your last name")
        }
      }
      internal enum Middlename {
        internal enum Field {
          /// Middle name
          internal static let name = L10n.tr("Localizable", "shortened.registration.middlename.field.name", fallback: "Middle name")
          /// Your middle name
          internal static let placeholder = L10n.tr("Localizable", "shortened.registration.middlename.field.placeholder", fallback: "Your middle name")
        }
      }
      internal enum Mobilenumber {
        internal enum Field {
          /// Mobile number
          internal static let name = L10n.tr("Localizable", "shortened.registration.mobilenumber.field.name", fallback: "Mobile number")
          /// 9 · ·  · · ·  · · · ·
          internal static let placeholder = L10n.tr("Localizable", "shortened.registration.mobilenumber.field.placeholder", fallback: "9 · ·  · · ·  · · · ·")
        }
      }
      internal enum No {
        internal enum Legal {
          /// I have no legal middle name
          internal static let middlename = L10n.tr("Localizable", "shortened.registration.no.legal.middlename", fallback: "I have no legal middle name")
        }
      }
      internal enum Password {
        internal enum Field {
          /// Password
          internal static let name = L10n.tr("Localizable", "shortened.registration.password.field.name", fallback: "Password")
          /// Create a password
          internal static let placeholder = L10n.tr("Localizable", "shortened.registration.password.field.placeholder", fallback: "Create a password")
        }
        internal enum Tips {
          /// 👌 Strong password tips
          internal static let label = L10n.tr("Localizable", "shortened.registration.password.tips.label", fallback: "👌 Strong password tips")
        }
      }
      internal enum Show {
        internal enum Less {
          /// Show less
          internal static let label = L10n.tr("Localizable", "shortened.registration.show.less.label", fallback: "Show less")
        }
        internal enum More {
          /// Show more
          internal static let label = L10n.tr("Localizable", "shortened.registration.show.more.label", fallback: "Show more")
        }
      }
    }
  }
  internal enum SparkHackathon {
    internal enum Interstitial {
      /// Discover innovative solutions developed by Maya’s talented teams
      internal static let message1 = L10n.tr("Localizable", "sparkHackathon.interstitial.message1", fallback: "Discover innovative solutions developed by Maya’s talented teams")
      /// Be the first to try new features and help shape the future of Maya products
      internal static let message2 = L10n.tr("Localizable", "sparkHackathon.interstitial.message2", fallback: "Be the first to try new features and help shape the future of Maya products")
      /// Network with fellow innovators and learn about the latest trends in tech
      internal static let message3 = L10n.tr("Localizable", "sparkHackathon.interstitial.message3", fallback: "Network with fellow innovators and learn about the latest trends in tech")
      /// Spark Hackathon
      internal static let title = L10n.tr("Localizable", "sparkHackathon.interstitial.title", fallback: "Powering the Future of Maya")
    }
  }
  internal enum Stocks {
    internal enum Interstitial {
      /// Trade stocks with SEC licensed brokers
      internal static let message1 = L10n.tr("Localizable", "stocks.interstitial.message1", fallback: "Trade stocks with SEC licensed brokers")
      /// Easily manage your stock portfolio
      internal static let message2 = L10n.tr("Localizable", "stocks.interstitial.message2", fallback: "Easily manage your stock portfolio")
      /// Use your Maya Wallet to fund your trades with each partner
      internal static let message3 = L10n.tr("Localizable", "stocks.interstitial.message3", fallback: "Use your Maya Wallet to fund your trades with each partner")
      /// Stocks
      internal static let title = L10n.tr("Localizable", "stocks.interstitial.title", fallback: "Step into the exciting world of stock trading")
    }
  }
  internal enum StocksGame {
    internal enum Interstitial {
      /// Discover what it’s like to trade stocks, ETFs, and more
      internal static let message1 = L10n.tr("Localizable", "stocksGame.interstitial.message1", fallback: "Discover what it’s like to trade stocks, ETFs, and more")
      /// Learn how to trade stocks without using any real money
      internal static let message2 = L10n.tr("Localizable", "stocksGame.interstitial.message2", fallback: "Learn how to trade stocks without using any real money")
      /// Simulate trading with world’s best companies from Apple to Zoom
      internal static let message3 = L10n.tr("Localizable", "stocksGame.interstitial.message3", fallback: "Simulate trading with world’s best companies from Apple to Zoom")
      /// Stocks Game
      internal static let title = L10n.tr("Localizable", "stocksGame.interstitial.title", fallback: "Learn stock trading securely with Ztock")
    }
  }
  internal enum Stream {
    internal enum Interstitial {
      /// Access a curated collection of streaming providers
      internal static let message1 = L10n.tr("Localizable", "stream.interstitial.message1", fallback: "Access a curated collection of streaming providers")
      /// Stream movies, TV shows, and more from your favorite platforms
      internal static let message2 = L10n.tr("Localizable", "stream.interstitial.message2", fallback: "Stream movies, TV shows, and more from your favorite platforms")
      /// Use your Maya Wallet for hassle-free and secure payments
      internal static let message3 = L10n.tr("Localizable", "stream.interstitial.message3", fallback: "Use your Maya Wallet for hassle-free and secure payments")
      /// Stream
      internal static let title = L10n.tr("Localizable", "stream.interstitial.title", fallback: "Your one-stop streaming hub")
    }
  }
  internal enum Success {
    internal enum Add {
      internal enum To {
        internal enum Contacts {
          internal enum No {
            internal enum Permission {
              /// Please allow PayMaya to access Contacts in the IOS settings to access this feature.
              internal static let message = L10n.tr("Localizable", "success.add.to.contacts.no.permission.message", fallback: "Please allow PayMaya to access Contacts in the IOS settings to access this feature.")
              /// Access to Contacts Needed
              internal static let title = L10n.tr("Localizable", "success.add.to.contacts.no.permission.title", fallback: "Access to Contacts Needed")
            }
          }
        }
      }
    }
    internal enum Shop {
      internal enum CouponButton {
        internal enum Snackbar {
          /// Coupon code copied!
          internal static let message = L10n.tr("Localizable", "success.shop.couponButton.snackbar.message", fallback: "Coupon code copied!")
        }
      }
    }
    internal enum Status {
      internal enum Title {
        /// New Success
        internal static let completed = L10n.tr("Localizable", "success.status.title.completed", fallback: "COMPLETED")
        /// PROCESSING
        internal static let processing = L10n.tr("Localizable", "success.status.title.processing", fallback: "PROCESSING")
        /// REJECTED
        internal static let rejected = L10n.tr("Localizable", "success.status.title.rejected", fallback: "REJECTED")
      }
    }
  }
  internal enum Tab {
    internal enum Menu {
      /// Freeze
      internal static let freeze = L10n.tr("Localizable", "tab.menu.freeze", fallback: "Freeze")
      /// Tab Menu
      internal static let home = L10n.tr("Localizable", "tab.menu.home", fallback: "Home")
      /// My Cards
      internal static let mycards = L10n.tr("Localizable", "tab.menu.mycards", fallback: "My Cards")
      /// Pay
      internal static let pay = L10n.tr("Localizable", "tab.menu.pay", fallback: "Pay")
      /// Pay Bills
      internal static let paybills = L10n.tr("Localizable", "tab.menu.paybills", fallback: "Pay Bills")
      /// Send Money
      internal static let sendmoney = L10n.tr("Localizable", "tab.menu.sendmoney", fallback: "Send Money")
      /// Services
      internal static let services = L10n.tr("Localizable", "tab.menu.services", fallback: "Services")
      /// Shop
      internal static let shop = L10n.tr("Localizable", "tab.menu.shop", fallback: "Shop")
      /// Unfreeze
      internal static let unfreeze = L10n.tr("Localizable", "tab.menu.unfreeze", fallback: "Unfreeze")
    }
  }
  internal enum Transport {
    internal enum Activity {
      /// Transport
      internal static let type = L10n.tr("Localizable", "transport.activity.type", fallback: "Transport")
    }
    internal enum Stops {
      internal enum Destination {
        /// Destination
        internal static let title = L10n.tr("Localizable", "transport.stops.destination.title", fallback: "Destination")
      }
    }
  }
  internal enum Vouchers {
    internal enum Available {
      /// Claim until %@
      internal static func spiel(_ p1: Any) -> String {
        return L10n.tr("Localizable", "vouchers.available.spiel", String(describing: p1), fallback: "Claim until %@")
      }
    }
    internal enum Error {
      internal enum Connection {
        internal enum Issue {
          /// We ran into a connection issue. Please try again in a bit.
          internal static let message = L10n.tr("Localizable", "vouchers.error.connection.issue.message", fallback: "We ran into a connection issue. Please try again in a bit.")
        }
      }
    }
    internal enum Maintenance {
      /// Buy load, data and other
      /// cool stuff on our Shop!
      internal static let message = L10n.tr("Localizable", "vouchers.maintenance.message", fallback: "Buy load, data and other\ncool stuff on our Shop!")
      internal enum Button {
        /// Shop Now
        internal static let title = L10n.tr("Localizable", "vouchers.maintenance.button.title", fallback: "Shop Now")
      }
    }
    internal enum Receipt {
      internal enum Success {
        internal enum Copy {
          internal enum Code {
            /// Voucher Code Copied!
            internal static let spiel = L10n.tr("Localizable", "vouchers.receipt.success.copy.code.spiel", fallback: "Voucher Code Copied!")
          }
        }
      }
      internal enum Terms {
        internal enum And {
          internal enum Conditions {
            /// 1. This voucher can only be redeemed during the validity period
            /// 2. This voucher is non-refundable and cannot be exchanged for cash in part or full and is valid for a single transaction only.
            internal static let description = L10n.tr("Localizable", "vouchers.receipt.terms.and.conditions.description", fallback: "1. This voucher can only be redeemed during the validity period\n2. This voucher is non-refundable and cannot be exchanged for cash in part or full and is valid for a single transaction only.")
            /// Terms and Conditions
            internal static let title = L10n.tr("Localizable", "vouchers.receipt.terms.and.conditions.title", fallback: "Terms and Conditions")
          }
        }
      }
    }
    internal enum Reminder {
      internal enum Calendar {
        internal enum Denied {
          internal enum Access {
            /// You have declined access to your Calendar. To change this, go to Settings.
            internal static let spiel = L10n.tr("Localizable", "vouchers.reminder.calendar.denied.access.spiel", fallback: "You have declined access to your Calendar. To change this, go to Settings.")
          }
        }
        internal enum Settings {
          /// Go to Settings
          internal static let spiel = L10n.tr("Localizable", "vouchers.reminder.calendar.settings.spiel", fallback: "Go to Settings")
        }
      }
      internal enum Cancelled {
        /// Notifications turned off for this voucher.
        internal static let spiel = L10n.tr("Localizable", "vouchers.reminder.cancelled.spiel", fallback: "Notifications turned off for this voucher.")
      }
      internal enum Denied {
        /// Turn on your notifications to receive reminders and more!
        internal static let spiel = L10n.tr("Localizable", "vouchers.reminder.denied.spiel", fallback: "Turn on your notifications to receive reminders and more!")
      }
      internal enum Scheduled {
        /// You have successfully set a reminder!
        internal static let spiel = L10n.tr("Localizable", "vouchers.reminder.scheduled.spiel", fallback: "You have successfully set a reminder!")
        internal enum Failed {
          /// We had trouble saving your reminder.
          internal static let spiel = L10n.tr("Localizable", "vouchers.reminder.scheduled.failed.spiel", fallback: "We had trouble saving your reminder.")
        }
      }
    }
    internal enum State {
      internal enum Available {
        /// Vouchers
        internal static let title = L10n.tr("Localizable", "vouchers.state.available.title", fallback: "Available")
      }
      internal enum Claimed {
        /// Claimed
        internal static let title = L10n.tr("Localizable", "vouchers.state.claimed.title", fallback: "Claimed")
      }
      internal enum Expired {
        /// Expired
        internal static let title = L10n.tr("Localizable", "vouchers.state.expired.title", fallback: "Expired")
      }
    }
  }
}
// swiftlint:enable explicit_type_interface function_parameter_count identifier_name line_length
// swiftlint:enable nesting type_body_length type_name vertical_whitespace_opening_braces

// MARK: - Implementation Details

extension L10n {
  private static func tr(_ table: String, _ key: String, _ args: CVarArg..., fallback value: String) -> String {
    let format = BundleToken.bundle.localizedString(forKey: key, value: value, table: table)
    return String(format: format, locale: Locale.current, arguments: args)
  }
}

// swiftlint:disable convenience_type
private final class BundleToken {
  static let bundle: Bundle = {
    #if SWIFT_PACKAGE
    return Bundle.module
    #else
    return Bundle(for: BundleToken.self)
    #endif
  }()
}
// swiftlint:enable convenience_type
