// swiftlint:disable all
// Generated using SwiftGen — https://github.com/SwiftGen/SwiftGen

// swiftlint:disable sorted_imports
import Foundation
import UIKit

// swiftlint:disable superfluous_disable_command
// swiftlint:disable file_length implicit_return

// MARK: - Storyboard Scenes

// swiftlint:disable explicit_type_interface identifier_name line_length prefer_self_in_static_references
// swiftlint:disable type_body_length type_name
internal enum StoryboardScene {
  internal enum Account: StoryboardType {
    internal static let storyboardName = "Account"

    internal static let mayaForgotPasswordViewController = SceneType<MayaForgotPasswordViewController>(storyboard: Account.self, identifier: "MayaForgotPasswordViewController")

    internal static let mayaLoginViewController = SceneType<MayaLoginViewController>(storyboard: Account.self, identifier: "MayaLoginViewController")

    internal static let mayaOTPViewController = SceneType<MayaOTPViewController>(storyboard: Account.self, identifier: "MayaOTPViewController")

    internal static let mayaPasswordChangedViewController = SceneType<MayaPasswordChangedViewController>(storyboard: Account.self, identifier: "MayaPasswordChangedViewController")

    internal static let mayaProminentDisclosureV2ViewController = SceneType<MayaProminentDisclosureV2ViewController>(storyboard: Account.self, identifier: "MayaProminentDisclosureV2ViewController")

    internal static let mayaProminentDisclosureViewController = SceneType<MayaProminentDisclosureViewController>(storyboard: Account.self, identifier: "MayaProminentDisclosureViewController")

    internal static let mayaRegistrationFormFirstViewController = SceneType<MayaRegistrationFormFirstViewController>(storyboard: Account.self, identifier: "MayaRegistrationFormFirstViewController")

    internal static let mayaRegistrationFormSecondViewController = SceneType<MayaRegistrationFormSecondViewController>(storyboard: Account.self, identifier: "MayaRegistrationFormSecondViewController")

    internal static let mayaRegistrationPasswordInfoViewController = SceneType<MayaRegistrationPasswordInfoViewController>(storyboard: Account.self, identifier: "MayaRegistrationPasswordInfoViewController")

    internal static let mayaSessionTimeoutViewController = SceneType<MayaSessionTimeoutViewController>(storyboard: Account.self, identifier: "MayaSessionTimeoutViewController")

    internal static let mayaSetNewPasswordViewController = SceneType<MayaSetNewPasswordViewController>(storyboard: Account.self, identifier: "MayaSetNewPasswordViewController")
  }
  internal enum AccountRecovery: StoryboardType {
    internal static let storyboardName = "AccountRecovery"

    internal static let mayaAccountRecoveryEmailVerificationViewController = SceneType<MayaAccountRecoveryEmailVerificationViewController>(storyboard: AccountRecovery.self, identifier: "MayaAccountRecoveryEmailVerificationViewController")

    internal static let mayaAccountRecoveryNominateEmailViewController = SceneType<MayaAccountRecoveryNominateEmailViewController>(storyboard: AccountRecovery.self, identifier: "MayaAccountRecoveryNominateEmailViewController")
  }
  internal enum AddFunds: StoryboardType {
    internal static let storyboardName = "AddFunds"

    internal static let mayaCashInPartnerInstructionDetailsViewController = SceneType<MayaCashInPartnerInstructionDetailsViewController>(storyboard: AddFunds.self, identifier: "MayaCashInPartnerInstructionDetailsViewController")

    internal static let mayaCashInViewController = SceneType<MayaCashInViewController>(storyboard: AddFunds.self, identifier: "MayaCashInViewController")

    internal static let mayaMoneyInAmountViewController = SceneType<MayaMoneyInAmountViewController>(storyboard: AddFunds.self, identifier: "MayaMoneyInAmountViewController")

    internal static let mayaMoneyInCodeViewController = SceneType<MayaMoneyInCodeViewController>(storyboard: AddFunds.self, identifier: "MayaMoneyInCodeViewController")

    internal static let mayaWalletSettingsViewController = SceneType<MayaWalletSettingsViewController>(storyboard: AddFunds.self, identifier: "MayaWalletSettingsViewController")

    internal static let mayaWesternUnionFormViewController = SceneType<MayaWesternUnionFormViewController>(storyboard: AddFunds.self, identifier: "MayaWesternUnionFormViewController")
  }
  internal enum AddMoneyViaCard: StoryboardType {
    internal static let storyboardName = "AddMoneyViaCard"

    internal static let mayaAddMoneyViaCardFormViewController = SceneType<MayaAddMoneyViaCardFormViewController>(storyboard: AddMoneyViaCard.self, identifier: "MayaAddMoneyViaCardFormViewController")

    internal static let mayaAddMoneyViaCardListViewController = SceneType<MayaAddMoneyViaCardListViewController>(storyboard: AddMoneyViaCard.self, identifier: "MayaAddMoneyViaCardListViewController")
  }
  internal enum BankPullFunds: StoryboardType {
    internal static let storyboardName = "BankPullFunds"

    internal static let mayaBankPullFundsAmountViewController = SceneType<MayaBankPullFundsAmountViewController>(storyboard: BankPullFunds.self, identifier: "MayaBankPullFundsAmountViewController")

    internal static let mayaBankPullFundsLinkedAccountListViewController = SceneType<MayaBankPullFundsLinkedAccountListViewController>(storyboard: BankPullFunds.self, identifier: "MayaBankPullFundsLinkedAccountListViewController")

    internal static let mayaBankPullFundsListV3ViewController = SceneType<MayaBankPullFundsListV3ViewController>(storyboard: BankPullFunds.self, identifier: "MayaBankPullFundsListV3ViewController")

    internal static let mayaBankPullFundsListViewController = SceneType<MayaBankPullFundsListViewController>(storyboard: BankPullFunds.self, identifier: "MayaBankPullFundsListViewController")
  }
  internal enum BankTransfer: StoryboardType {
    internal static let storyboardName = "BankTransfer"

    internal static let mayaBankTransferFormViewController = SceneType<MayaBankTransferFormViewController>(storyboard: BankTransfer.self, identifier: "MayaBankTransferFormViewController")

    internal static let mayaBankTransferListViewController = SceneType<MayaBankTransferListViewController>(storyboard: BankTransfer.self, identifier: "MayaBankTransferListViewController")
  }
  internal enum Compliance: StoryboardType {
    internal static let storyboardName = "Compliance"

    internal static let mayaDosriViewController = SceneType<MayaDosriViewController>(storyboard: Compliance.self, identifier: "MayaDosriViewController")

    internal static let mayaFATCAViewController = SceneType<MayaFATCAViewController>(storyboard: Compliance.self, identifier: "MayaFATCAViewController")
  }
  internal enum DataPrivacy: StoryboardType {
    internal static let storyboardName = "DataPrivacy"

    internal static let mayaAutoProvisionDataPrivacyViewController = SceneType<MayaAutoProvisionDataPrivacyViewController>(storyboard: DataPrivacy.self, identifier: "MayaAutoProvisionDataPrivacyViewController")

    internal static let mayaDataPrivacyViewController = SceneType<MayaDataPrivacyViewController>(storyboard: DataPrivacy.self, identifier: "MayaDataPrivacyViewController")

    internal static let mayaSecondaryConsentViewController = SceneType<MayaSecondaryConsentViewController>(storyboard: DataPrivacy.self, identifier: "MayaSecondaryConsentViewController")

    internal static let newMayaCreditDiscoveryDataPrivacyViewController = SceneType<NewMayaCreditDiscoveryDataPrivacyViewController>(storyboard: DataPrivacy.self, identifier: "NewMayaCreditDiscoveryDataPrivacyViewController")
  }
  internal enum DeviceManagement: StoryboardType {
    internal static let storyboardName = "DeviceManagement"

    internal static let mayaDeviceDetailsViewController = SceneType<MayaDeviceDetailsViewController>(storyboard: DeviceManagement.self, identifier: "MayaDeviceDetailsViewController")

    internal static let mayaDeviceManagementMainViewController = SceneType<MayaDeviceManagementMainViewController>(storyboard: DeviceManagement.self, identifier: "MayaDeviceManagementMainViewController")
  }
  internal enum Edd: StoryboardType {
    internal static let storyboardName = "EDD"

    internal static let eddBankInformationViewController = SceneType<EDDBankInformationViewController>(storyboard: Edd.self, identifier: "EDDBankInformationViewController")

    internal static let eddFinancialDocumentsViewController = SceneType<EDDFinancialDocumentsViewController>(storyboard: Edd.self, identifier: "EDDFinancialDocumentsViewController")

    internal static let eddMainViewController = SceneType<EDDMainViewController>(storyboard: Edd.self, identifier: "EDDMainViewController")

    internal static let eddRelatedCompaniesViewController = SceneType<EDDRelatedCompaniesViewController>(storyboard: Edd.self, identifier: "EDDRelatedCompaniesViewController")

    internal static let eddReviewDetailsViewController = SceneType<EDDReviewDetailsViewController>(storyboard: Edd.self, identifier: "EDDReviewDetailsViewController")

    internal static let eddReviewValidationCodeViewController = SceneType<EDDReviewValidationCodeViewController>(storyboard: Edd.self, identifier: "EDDReviewValidationCodeViewController")

    internal static let eddSearchViewController = SceneType<EDDSearchViewController>(storyboard: Edd.self, identifier: "EDDSearchViewController")

    internal static let eddSourceOfIncomeViewController = SceneType<EDDSourceOfIncomeViewController>(storyboard: Edd.self, identifier: "EDDSourceOfIncomeViewController")

    internal static let eddSplashScreenViewController = SceneType<EDDSplashScreenViewController>(storyboard: Edd.self, identifier: "EDDSplashScreenViewController")

    internal static let eddSubmitReviewViewController = SceneType<EDDSubmitReviewViewController>(storyboard: Edd.self, identifier: "EDDSubmitReviewViewController")

    internal static let eddSubmitSuccessViewController = SceneType<EDDSubmitSuccessViewController>(storyboard: Edd.self, identifier: "EDDSubmitSuccessViewController")

    internal static let eddUsageViewController = SceneType<EDDUsageViewController>(storyboard: Edd.self, identifier: "EDDUsageViewController")
  }
  internal enum Ekyc: StoryboardType {
    internal static let storyboardName = "EKYC"

    internal static let idCaptureViewController = SceneType<IDCaptureViewController>(storyboard: Ekyc.self, identifier: "IDCaptureViewController")
  }
  internal enum EKYCZoloz: StoryboardType {
    internal static let storyboardName = "EKYC-Zoloz"

    internal static let mayaEKYCIDSelectViewController = SceneType<MayaEKYCIDSelectViewController>(storyboard: EKYCZoloz.self, identifier: "MayaEKYCIDSelectViewController")

    internal static let mayaEKYCIntroScreenViewController = SceneType<MayaEKYCIntroScreenViewController>(storyboard: EKYCZoloz.self, identifier: "MayaEKYCIntroScreenViewController")

    internal static let mayaNameRulesBottomSheetViewControllert = SceneType<MayaNameRulesBottomSheetViewController>(storyboard: EKYCZoloz.self, identifier: "MayaNameRulesBottomSheetViewControllert")

    internal static let mayaPersonalInformationPickerViewController = SceneType<MayaPersonalInformationPickerViewController>(storyboard: EKYCZoloz.self, identifier: "MayaPersonalInformationPickerViewController")

    internal static let mayaPersonalInformationViewController = SceneType<MayaPersonalInformationViewController>(storyboard: EKYCZoloz.self, identifier: "MayaPersonalInformationViewController")

    internal static let mayaZolozAcceptedIDsViewController = SceneType<MayaZolozAcceptedIDsViewController>(storyboard: EKYCZoloz.self, identifier: "MayaZolozAcceptedIDsViewController")

    internal static let mayaZolozAccountUnderReviewViewController = SceneType<MayaZolozAccountUnderReviewViewController>(storyboard: EKYCZoloz.self, identifier: "MayaZolozAccountUnderReviewViewController")

    internal static let mayaZolozAdditionalDocumentsViewController = SceneType<MayaZolozAdditionalDocumentsViewController>(storyboard: EKYCZoloz.self, identifier: "MayaZolozAdditionalDocumentsViewController")

    internal static let mayaZolozBenefitsAndStepsViewController = SceneType<MayaZolozBenefitsAndStepsViewController>(storyboard: EKYCZoloz.self, identifier: "MayaZolozBenefitsAndStepsViewController")

    internal static let mayaZolozCaptureAdditionalDocumentConfirmationViewController = SceneType<MayaZolozCaptureAdditionalDocumentConfirmationViewController>(storyboard: EKYCZoloz.self, identifier: "MayaZolozCaptureAdditionalDocumentConfirmationViewController")

    internal static let mayaZolozCaptureAdditionalDocumentViewController = SceneType<MayaZolozCaptureAdditionalDocumentViewController>(storyboard: EKYCZoloz.self, identifier: "MayaZolozCaptureAdditionalDocumentViewController")

    internal static let mayaZolozExampleTransactionViewController = SceneType<MayaZolozExampleTransactionViewController>(storyboard: EKYCZoloz.self, identifier: "MayaZolozExampleTransactionViewController")

    internal static let mayaZolozIdSubmitViewController = SceneType<MayaZolozIdSubmitViewController>(storyboard: EKYCZoloz.self, identifier: "MayaZolozIdSubmitViewController")

    internal static let mayaZolozTipsViewController = SceneType<MayaZolozTipsViewController>(storyboard: EKYCZoloz.self, identifier: "MayaZolozTipsViewController")

    internal static let mayaZolozUnderReviewViewController = SceneType<MayaZolozUnderReviewViewController>(storyboard: EKYCZoloz.self, identifier: "MayaZolozUnderReviewViewController")
  }
  internal enum Home: StoryboardType {
    internal static let storyboardName = "Home"

    internal static let mayaActivityDetailsViewController = SceneType<MayaActivityDetailsViewController>(storyboard: Home.self, identifier: "MayaActivityDetailsViewController")

    internal static let mayaAdCarouselViewController = SceneType<MayaAdCarouselViewController>(storyboard: Home.self, identifier: "MayaAdCarouselViewController")

    internal static let mayaAllActivitiesViewController = SceneType<MayaAllActivitiesViewController>(storyboard: Home.self, identifier: "MayaAllActivitiesViewController")

    internal static let mayaBannerInterstitialViewController = SceneType<MayaBannerInterstitialViewController>(storyboard: Home.self, identifier: "MayaBannerInterstitialViewController")

    internal static let mayaCreatorStoreViewController = SceneType<MayaCreatorStoreViewController>(storyboard: Home.self, identifier: "MayaCreatorStoreViewController")

    internal static let mayaCreditApplicationBannerViewController = SceneType<MayaCreditApplicationBannerViewController>(storyboard: Home.self, identifier: "MayaCreditApplicationBannerViewController")

    internal static let mayaCreditBalanceBannerViewController = SceneType<MayaCreditBalanceBannerViewController>(storyboard: Home.self, identifier: "MayaCreditBalanceBannerViewController")

    internal static let mayaDashboardMenuPagerViewController = SceneType<MayaDashboardMenuPagerViewController>(storyboard: Home.self, identifier: "MayaDashboardMenuPagerViewController")

    internal static let mayaDashboardV2ViewController = SceneType<MayaDashboardV2ViewController>(storyboard: Home.self, identifier: "MayaDashboardV2ViewController")

    internal static let mayaDashboardViewController = SceneType<MayaDashboardViewController>(storyboard: Home.self, identifier: "MayaDashboardViewController")

    internal static let mayaInterstitialViewController = SceneType<MayaInterstitialViewController>(storyboard: Home.self, identifier: "MayaInterstitialViewController")

    internal static let mayaLatestActivitiesViewController = SceneType<MayaLatestActivitiesViewController>(storyboard: Home.self, identifier: "MayaLatestActivitiesViewController")

    internal static let mayaNoActivityViewController = SceneType<MayaNoActivityViewController>(storyboard: Home.self, identifier: "MayaNoActivityViewController")

    internal static let mayaRewardsViewController = SceneType<MayaRewardsViewController>(storyboard: Home.self, identifier: "MayaRewardsViewController")

    internal static let mayaSavingsViewController = SceneType<MayaSavingsViewController>(storyboard: Home.self, identifier: "MayaSavingsViewController")

    internal static let mayaServicesViewController = SceneType<MayaServicesViewController>(storyboard: Home.self, identifier: "MayaServicesViewController")

    internal static let mayaWalletBalanceViewController = SceneType<MayaWalletBalanceViewController>(storyboard: Home.self, identifier: "MayaWalletBalanceViewController")

    internal static let mayaWalletServicesViewController = SceneType<MayaWalletServicesViewController>(storyboard: Home.self, identifier: "MayaWalletServicesViewController")
  }
  internal enum Inbox: StoryboardType {
    internal static let storyboardName = "Inbox"

    internal static let mayaEmptyInboxViewController = SceneType<MayaEmptyInboxViewController>(storyboard: Inbox.self, identifier: "MayaEmptyInboxViewController")

    internal static let mayaInboxDetailsViewController = SceneType<MayaInboxDetailsViewController>(storyboard: Inbox.self, identifier: "MayaInboxDetailsViewController")

    internal static let mayaInboxMenuPagerViewController = SceneType<MayaInboxMenuPagerViewController>(storyboard: Inbox.self, identifier: "MayaInboxMenuPagerViewController")

    internal static let mayaInboxViewController = SceneType<MayaInboxViewController>(storyboard: Inbox.self, identifier: "MayaInboxViewController")
  }
  internal enum Mfa: StoryboardType {
    internal static let storyboardName = "MFA"

    internal static let mayaMFAOtpViewController = SceneType<MayaMFAOtpViewController>(storyboard: Mfa.self, identifier: "MayaMFAOtpViewController")
  }
  internal enum Main: StoryboardType {
    internal static let storyboardName = "Main"

    internal static let importerViewController = SceneType<ImporterViewController>(storyboard: Main.self, identifier: "ImporterViewController")

    internal static let mainTabBarController = SceneType<UIKit.UITabBarController>(storyboard: Main.self, identifier: "MainTabBarController")

    internal static let mayaInitialViewController = SceneType<MayaInitialViewController>(storyboard: Main.self, identifier: "MayaInitialViewController")

    internal static let mayaIntroductionPageViewController = SceneType<MayaIntroductionPageViewController>(storyboard: Main.self, identifier: "MayaIntroductionPageViewController")

    internal static let mayaIntroductionViewController = SceneType<MayaIntroductionViewController>(storyboard: Main.self, identifier: "MayaIntroductionViewController")

    internal static let mayaWelcomeViewController = SceneType<MayaWelcomeViewController>(storyboard: Main.self, identifier: "MayaWelcomeViewController")

    internal static let mayaWelcomeViewControllerV2 = SceneType<MayaWelcomeV2ViewController>(storyboard: Main.self, identifier: "MayaWelcomeViewControllerV2")
  }
  internal enum MayaCredit: StoryboardType {
    internal static let storyboardName = "MayaCredit"

    internal static let mayaCreditActivationViewController = SceneType<MayaCreditActivationViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditActivationViewController")

    internal static let mayaCreditAllSetViewController = SceneType<MayaCreditAllSetViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditAllSetViewController")

    internal static let mayaCreditAssignContactReferenceViewController = SceneType<MayaCreditAssignContactReferenceViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditAssignContactReferenceViewController")

    internal static let mayaCreditAutomatedPaymentsViewController = SceneType<MayaCreditAutomatedPaymentsViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditAutomatedPaymentsViewController")

    internal static let mayaCreditBillingEndDateSelectorViewController = SceneType<MayaCreditBillingEndDateSelectorViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditBillingEndDateSelectorViewController")

    internal static let mayaCreditConfirmDetailsViewController = SceneType<MayaCreditConfirmDetailsViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditConfirmDetailsViewController")

    internal static let mayaCreditContactRelationshipSelectorViewController = SceneType<MayaCreditContactRelationshipSelectorViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditContactRelationshipSelectorViewController")

    internal static let mayaCreditDataPrivacyPolicyViewController = SceneType<MayaCreditDataPrivacyPolicyViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditDataPrivacyPolicyViewController")

    internal static let mayaCreditGenderSelectorViewController = SceneType<MayaCreditGenderSelectorViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditGenderSelectorViewController")

    internal static let mayaCreditHomeViewController = SceneType<MayaCreditHomeViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditHomeViewController")

    internal static let mayaCreditIneligibleViewController = SceneType<MayaCreditIneligibleViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditIneligibleViewController")

    internal static let mayaCreditInitialEligibilityViewController = SceneType<MayaCreditInitialEligibilityViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditInitialEligibilityViewController")

    internal static let mayaCreditInterestDetailsViewController = SceneType<MayaCreditInterestDetailsViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditInterestDetailsViewController")

    internal static let mayaCreditLatestBillInfoViewController = SceneType<MayaCreditLatestBillInfoViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditLatestBillInfoViewController")

    internal static let mayaCreditLearnMoreContactReferenceViewController = SceneType<MayaCreditLearnMoreContactReferenceViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditLearnMoreContactReferenceViewController")

    internal static let mayaCreditMaritalStatusSelectorViewController = SceneType<MayaCreditMaritalStatusSelectorViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditMaritalStatusSelectorViewController")

    internal static let mayaCreditOptInViewController = SceneType<MayaCreditOptInViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditOptInViewController")

    internal static let mayaCreditPartnerMerchantSignupSuccessBottomSheet = SceneType<MayaCreditPartnerMerchantSignupSuccessBottomSheetViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditPartnerMerchantSignupSuccessBottomSheet")

    internal static let mayaCreditSettingsViewController = SceneType<MayaCreditSettingsViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditSettingsViewController")

    internal static let mayaCreditSignUpViewController = SceneType<MayaCreditSignUpViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditSignUpViewController")

    internal static let mayaCreditTermDetailsViewController = SceneType<MayaCreditTermDetailsViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditTermDetailsViewController")

    internal static let mayaCreditTransactionsViewController = SceneType<MayaCreditTransactionsViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditTransactionsViewController")

    internal static let mayaCreditTransferFundsViewController = SceneType<MayaCreditTransferFundsViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditTransferFundsViewController")

    internal static let mayaCreditUpdatePersonalDetailsViewController = SceneType<MayaCreditUpdatePersonalDetailsViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditUpdatePersonalDetailsViewController")

    internal static let mayaCreditViewController = SceneType<MayaCreditViewController>(storyboard: MayaCredit.self, identifier: "MayaCreditViewController")

    internal static let newMayaCreditAccountSummaryViewController = SceneType<NewMayaCreditAccountSummaryViewController>(storyboard: MayaCredit.self, identifier: "NewMayaCreditAccountSummaryViewController")

    internal static let newMayaCreditActivationViewController = SceneType<NewMayaCreditActivationViewController>(storyboard: MayaCredit.self, identifier: "NewMayaCreditActivationViewController")

    internal static let newMayaCreditBillingEndDateSelectorViewController = SceneType<NewMayaCreditBillingEndDateSelectorViewController>(storyboard: MayaCredit.self, identifier: "NewMayaCreditBillingEndDateSelectorViewController")

    internal static let newMayaCreditConfirmChangeBillingDateViewController = SceneType<NewMayaCreditConfirmChangeBillingDateViewController>(storyboard: MayaCredit.self, identifier: "NewMayaCreditConfirmChangeBillingDateViewController")

    internal static let newMayaCreditInterestDetailsViewController = SceneType<NewMayaCreditInterestDetailsViewController>(storyboard: MayaCredit.self, identifier: "NewMayaCreditInterestDetailsViewController")

    internal static let newMayaCreditSuccessTransferViewController = SceneType<NewMayaCreditSuccessTransferViewController>(storyboard: MayaCredit.self, identifier: "NewMayaCreditSuccessTransferViewController")

    internal static let newMayaCreditTermDetailsViewController = SceneType<NewMayaCreditTermDetailsViewController>(storyboard: MayaCredit.self, identifier: "NewMayaCreditTermDetailsViewController")

    internal static let newMayaCreditTransferConfirmationViewController = SceneType<NewMayaCreditTransferConfirmationViewController>(storyboard: MayaCredit.self, identifier: "NewMayaCreditTransferConfirmationViewController")

    internal static let newMayaCreditTransferFundsViewController = SceneType<NewMayaCreditTransferFundsViewController>(storyboard: MayaCredit.self, identifier: "NewMayaCreditTransferFundsViewController")
  }
  internal enum MayaEKYC: StoryboardType {
    internal static let storyboardName = "MayaEKYC"

    internal static let initialScene = InitialSceneType<UIKit.UINavigationController>(storyboard: MayaEKYC.self)

    internal static let mayaEKYCBenefitsViewController = SceneType<MayaEKYCBenefitsViewController>(storyboard: MayaEKYC.self, identifier: "MayaEKYCBenefitsViewController")

    internal static let mayaEKYCMaintenanceViewController = SceneType<MayaEKYCMaintenanceViewController>(storyboard: MayaEKYC.self, identifier: "MayaEKYCMaintenanceViewController")

    internal static let mayaEKYCNewSecondaryIDViewController = SceneType<MayaEKYCSecondaryIDViewController>(storyboard: MayaEKYC.self, identifier: "MayaEKYCNewSecondaryIDViewController")

    internal static let mayaEKYCPhilsysIDGuideViewController = SceneType<MayaEKYCPhilsysIDGuideViewController>(storyboard: MayaEKYC.self, identifier: "MayaEKYCPhilsysIDGuideViewController")

    internal static let mayaEKYCPhilsysIDUploadViewController = SceneType<MayaEKYCPhilsysIDUploadViewController>(storyboard: MayaEKYC.self, identifier: "MayaEKYCPhilsysIDUploadViewController")

    internal static let mayaEKYCRemindersViewController = SceneType<MayaEKYCRemindersViewController>(storyboard: MayaEKYC.self, identifier: "MayaEKYCRemindersViewController")

    internal static let mayaEKYCSecondaryIDViewController = SceneType<MayaEKYCSecondaryIDViewController>(storyboard: MayaEKYC.self, identifier: "MayaEKYCSecondaryIDViewController")

    internal static let mayaEKYCSelfieTipsViewController = SceneType<MayaEKYCSelfieTipsViewController>(storyboard: MayaEKYC.self, identifier: "MayaEKYCSelfieTipsViewController")

    internal static let mayaEKYCSimpleCaptureIDReviewViewController = SceneType<MayaEKYCSimpleCaptureIDReviewViewController>(storyboard: MayaEKYC.self, identifier: "MayaEKYCSimpleCaptureIDReviewViewController")

    internal static let mayaEKYCSimpleCaptureIDV3ViewController = SceneType<MayaEKYCSimpleCaptureIDV3ViewController>(storyboard: MayaEKYC.self, identifier: "MayaEKYCSimpleCaptureIDV3ViewController")

    internal static let mayaEKYCSimpleCaptureIDViewController = SceneType<MayaEKYCSimpleCaptureIDViewController>(storyboard: MayaEKYC.self, identifier: "MayaEKYCSimpleCaptureIDViewController")

    internal static let mayaLivenessTipsViewController = SceneType<MayaLivenessTipsViewController>(storyboard: MayaEKYC.self, identifier: "MayaLivenessTipsViewController")

    internal static let mayaReKYCOtherIDViewController = SceneType<MayaReKYCOtherIDViewController>(storyboard: MayaEKYC.self, identifier: "MayaReKYCOtherIDViewController")

    internal static let mayaUpgradeTypeViewController = SceneType<MayaUpgradeTypeViewController>(storyboard: MayaEKYC.self, identifier: "MayaUpgradeTypeViewController")
  }
  internal enum MayaReKYC: StoryboardType {
    internal static let storyboardName = "MayaReKYC"

    internal static let mayaReKYCAlertBottomSheetViewController = SceneType<MayaReKYCAlertBottomSheetViewController>(storyboard: MayaReKYC.self, identifier: "MayaReKYCAlertBottomSheetViewController")

    internal static let mayaReKYCNudgeViewController = SceneType<MayaReKYCNudgeViewController>(storyboard: MayaReKYC.self, identifier: "MayaReKYCNudgeViewController")

    internal static let mayaReKYCUnderReviewViewController = SceneType<MayaReKYCUnderReviewViewController>(storyboard: MayaReKYC.self, identifier: "MayaReKYCUnderReviewViewController")
  }
  internal enum MayaTravel: StoryboardType {
    internal static let storyboardName = "MayaTravel"

    internal static let mayaTravelHomeViewController = SceneType<MayaTravelHomeViewController>(storyboard: MayaTravel.self, identifier: "MayaTravelHomeViewController")

    internal static let mayaTravelOperatorListViewController = SceneType<MayaTravelOperatorListViewController>(storyboard: MayaTravel.self, identifier: "MayaTravelOperatorListViewController")

    internal static let mayaTravelRouteListViewController = SceneType<MayaTravelRouteListViewController>(storyboard: MayaTravel.self, identifier: "MayaTravelRouteListViewController")

    internal static let mayaTravelStopSelectionViewController = SceneType<MayaTravelStopSelectionViewController>(storyboard: MayaTravel.self, identifier: "MayaTravelStopSelectionViewController")
  }
  internal enum Missions: StoryboardType {
    internal static let storyboardName = "Missions"

    internal static let mayaMissionPreviewViewController = SceneType<MayaMissionPreviewViewController>(storyboard: Missions.self, identifier: "MayaMissionPreviewViewController")

    internal static let mayaMissionsDetailsViewController = SceneType<MayaMissionsDetailsViewController>(storyboard: Missions.self, identifier: "MayaMissionsDetailsViewController")

    internal static let mayaMissionsEnterCodeViewController = SceneType<MayaMissionsEnterCodeViewController>(storyboard: Missions.self, identifier: "MayaMissionsEnterCodeViewController")

    internal static let mayaMissionsListViewController = SceneType<MayaMissionsListViewController>(storyboard: Missions.self, identifier: "MayaMissionsListViewController")

    internal static let mayaMissionsMainViewController = SceneType<MayaMissionsMainViewController>(storyboard: Missions.self, identifier: "MayaMissionsMainViewController")

    internal static let mayaMissionsPartSuccessModalViewController = SceneType<MayaMissionsPartSuccessModalViewController>(storyboard: Missions.self, identifier: "MayaMissionsPartSuccessModalViewController")

    internal static let mayaMissionsSuccessModalViewController = SceneType<MayaMissionsSuccessModalViewController>(storyboard: Missions.self, identifier: "MayaMissionsSuccessModalViewController")
  }
  internal enum More: StoryboardType {
    internal static let storyboardName = "More"

    internal static let mayaAccountLimitsEmptyViewController = SceneType<MayaAccountLimitsEmptyViewController>(storyboard: More.self, identifier: "MayaAccountLimitsEmptyViewController")

    internal static let mayaAccountLimitsViewController = SceneType<MayaAccountLimitsViewController>(storyboard: More.self, identifier: "MayaAccountLimitsViewController")

    internal static let mayaChangeMinInputNewMinViewController = SceneType<MayaChangeMinInputNewMinViewController>(storyboard: More.self, identifier: "MayaChangeMinInputNewMinViewController")

    internal static let mayaChangeMinMobileNumberViewController = SceneType<MayaChangeMinMobileNumberViewController>(storyboard: More.self, identifier: "MayaChangeMinMobileNumberViewController")

    internal static let mayaChangeMinRemindersViewController = SceneType<MayaChangeMinRemindersViewController>(storyboard: More.self, identifier: "MayaChangeMinRemindersViewController")

    internal static let mayaChangePasswordTipsViewController = SceneType<MayaChangePasswordTipsViewController>(storyboard: More.self, identifier: "MayaChangePasswordTipsViewController")

    internal static let mayaChangePasswordViewController = SceneType<MayaChangePasswordViewController>(storyboard: More.self, identifier: "MayaChangePasswordViewController")

    internal static let mayaLicensesViewController = SceneType<MayaLicensesViewController>(storyboard: More.self, identifier: "MayaLicensesViewController")

    internal static let mayaProfileViewController = SceneType<MayaProfileViewController>(storyboard: More.self, identifier: "MayaProfileViewController")

    internal static let mayaPublicNameDisplayViewController = SceneType<MayaPublicNameDisplayViewController>(storyboard: More.self, identifier: "MayaPublicNameDisplayViewController")

    internal static let mayaPublicNameShowExampleViewController = SceneType<MayaPublicNameShowExampleViewController>(storyboard: More.self, identifier: "MayaPublicNameShowExampleViewController")

    internal static let mayaSettingsViewController = SceneType<MayaSettingsViewController>(storyboard: More.self, identifier: "MayaSettingsViewController")
  }
  internal enum MyFavorites: StoryboardType {
    internal static let storyboardName = "MyFavorites"

    internal static let mayaAddToFavoritesViewController = SceneType<MayaAddToFavoritesViewController>(storyboard: MyFavorites.self, identifier: "MayaAddToFavoritesViewController")

    internal static let mayaMyFavoritesAllListViewController = SceneType<MayaMyFavoritesAllListViewController>(storyboard: MyFavorites.self, identifier: "MayaMyFavoritesAllListViewController")

    internal static let mayaMyFavoritesListViewController = SceneType<MayaMyFavoritesListViewController>(storyboard: MyFavorites.self, identifier: "MayaMyFavoritesListViewController")

    internal static let mayaMyFavoritesMainViewController = SceneType<MayaMyFavoritesMainViewController>(storyboard: MyFavorites.self, identifier: "MayaMyFavoritesMainViewController")

    internal static let mayaReminderSchedulePickerViewController = SceneType<MayaReminderSchedulePickerViewController>(storyboard: MyFavorites.self, identifier: "MayaReminderSchedulePickerViewController")

    internal static let mayaSampleReminderViewController = SceneType<MayaSampleReminderViewController>(storyboard: MyFavorites.self, identifier: "MayaSampleReminderViewController")
  }
  internal enum Others: StoryboardType {
    internal static let storyboardName = "Others"

    internal static let cameraPermissionViewController = SceneType<CameraPermissionViewController>(storyboard: Others.self, identifier: "CameraPermissionViewController")

    internal static let confirmationBottomSheetViewController = SceneType<ConfirmationBottomSheetViewController>(storyboard: Others.self, identifier: "ConfirmationBottomSheetViewController")

    internal static let errorAlertViewController = SceneType<ErrorAlertViewController>(storyboard: Others.self, identifier: "ErrorAlertViewController")

    internal static let errorFullViewNavigationController = SceneType<UIKit.UINavigationController>(storyboard: Others.self, identifier: "ErrorFullViewNavigationController")

    internal static let flutterScrollViewControllerContainer = SceneType<FlutterScrollViewControllerContainer>(storyboard: Others.self, identifier: "FlutterScrollViewControllerContainer")

    internal static let imageViewerViewController = SceneType<ImageViewerViewController>(storyboard: Others.self, identifier: "ImageViewerViewController")

    internal static let infinitePageViewController = SceneType<InfinitePageViewController>(storyboard: Others.self, identifier: "InfinitePageViewController")

    internal static let mayaAboutVerifiedSellerViewController = SceneType<MayaAboutVerifiedSellerViewController>(storyboard: Others.self, identifier: "MayaAboutVerifiedSellerViewController")

    internal static let mayaAlertBottomSheetV2ViewController = SceneType<MayaAlertBottomSheetV2ViewController>(storyboard: Others.self, identifier: "MayaAlertBottomSheetV2ViewController")

    internal static let mayaAlertBottomSheetViewController = SceneType<MayaAlertBottomSheetViewController>(storyboard: Others.self, identifier: "MayaAlertBottomSheetViewController")

    internal static let mayaAlertModalViewController = SceneType<MayaAlertModalViewController>(storyboard: Others.self, identifier: "MayaAlertModalViewController")

    internal static let mayaBottomSheetWkWebViewViewController = SceneType<MayaBottomSheetWkWebViewViewController>(storyboard: Others.self, identifier: "MayaBottomSheetWkWebViewViewController")

    internal static let mayaCameraPermissionViewController = SceneType<MayaCameraPermissionViewController>(storyboard: Others.self, identifier: "MayaCameraPermissionViewController")

    internal static let mayaConfirmationViewController = SceneType<MayaConfirmationViewController>(storyboard: Others.self, identifier: "MayaConfirmationViewController")

    internal static let mayaContactsPermissionViewController = SceneType<MayaContactsPermissionViewController>(storyboard: Others.self, identifier: "MayaContactsPermissionViewController")

    internal static let mayaContactsViewController = SceneType<MayaContactsViewController>(storyboard: Others.self, identifier: "MayaContactsViewController")

    internal static let mayaCustomDatePickerViewController = SceneType<MayaCustomDatePickerViewController>(storyboard: Others.self, identifier: "MayaCustomDatePickerViewController")

    internal static let mayaCustomPickerViewController = SceneType<MayaCustomPickerViewController>(storyboard: Others.self, identifier: "MayaCustomPickerViewController")

    internal static let mayaFeatureSheetViewController = SceneType<MayaFeatureSheetViewController>(storyboard: Others.self, identifier: "MayaFeatureSheetViewController")

    internal static let mayaMaintenanceViewController = SceneType<MayaMaintenanceViewController>(storyboard: Others.self, identifier: "MayaMaintenanceViewController")

    internal static let mayaPasswordPromptBottomSheetViewController = SceneType<MayaPasswordPromptBottomSheetViewController>(storyboard: Others.self, identifier: "MayaPasswordPromptBottomSheetViewController")

    internal static let mayaPickerViewController = SceneType<MayaPickerViewController>(storyboard: Others.self, identifier: "MayaPickerViewController")

    internal static let mayaQRScannerViewController = SceneType<MayaQRScannerViewController>(storyboard: Others.self, identifier: "MayaQRScannerViewController")

    internal static let mayaReceiptViewController = SceneType<MayaReceiptViewController>(storyboard: Others.self, identifier: "MayaReceiptViewController")

    internal static let mayaSendMoneyBankTransferConfirmationViewController = SceneType<MayaSendMoneyBankTransferConfirmationViewController>(storyboard: Others.self, identifier: "MayaSendMoneyBankTransferConfirmationViewController")

    internal static let mayaServiceMaintenanceViewController = SceneType<MayaServiceMaintenanceViewController>(storyboard: Others.self, identifier: "MayaServiceMaintenanceViewController")

    internal static let mayaUpdaterViewController = SceneType<MayaUpdaterViewController>(storyboard: Others.self, identifier: "MayaUpdaterViewController")

    internal static let mayaWKWebViewController = SceneType<MayaWKWebViewController>(storyboard: Others.self, identifier: "MayaWKWebViewController")

    internal static let mayaWebViewParametersLandingErrorViewController = SceneType<MayaWebViewParametersLandingErrorViewController>(storyboard: Others.self, identifier: "MayaWebViewParametersLandingErrorViewController")

    internal static let mayaWebViewParametersLandingPageViewController = SceneType<MayaWebViewParametersLandingPageViewController>(storyboard: Others.self, identifier: "MayaWebViewParametersLandingPageViewController")

    internal static let pageViewController = SceneType<PageViewController>(storyboard: Others.self, identifier: "PageViewController")

    internal static let successAlertViewController = SceneType<SuccessAlertViewController>(storyboard: Others.self, identifier: "SuccessAlertViewController")

    internal static let webViewParametersRequiringLandingPageViewController = SceneType<WebViewParametersRequiringLandingPageViewController>(storyboard: Others.self, identifier: "WebViewParametersRequiringLandingPageViewController")
  }
  internal enum P2M: StoryboardType {
    internal static let storyboardName = "P2M"

    internal static let mayaP2MAmountViewController = SceneType<MayaP2MAmountViewController>(storyboard: P2M.self, identifier: "MayaP2MAmountViewController")

    internal static let mayaP2MConfirmationViewController = SceneType<MayaP2MConfirmationViewController>(storyboard: P2M.self, identifier: "MayaP2MConfirmationViewController")

    internal static let mayaP2MEmptyAmountViewController = SceneType<MayaP2MEmptyAmountViewController>(storyboard: P2M.self, identifier: "MayaP2MEmptyAmountViewController")
  }
  internal enum PayBills: StoryboardType {
    internal static let storyboardName = "PayBills"

    internal static let barcodeScanInstructionViewController = SceneType<BarcodeScanInstructionViewController>(storyboard: PayBills.self, identifier: "BarcodeScanInstructionViewController")

    internal static let barcodeScannerViewController = SceneType<BarcodeScannerViewController>(storyboard: PayBills.self, identifier: "BarcodeScannerViewController")

    internal static let mayaBIRBillerFormViewController = SceneType<MayaBIRBillerFormViewController>(storyboard: PayBills.self, identifier: "MayaBIRBillerFormViewController")

    internal static let mayaBIRRemindersViewController = SceneType<MayaBIRRemindersViewController>(storyboard: PayBills.self, identifier: "MayaBIRRemindersViewController")

    internal static let mayaBillerErrorViewController = SceneType<MayaBillerErrorViewController>(storyboard: PayBills.self, identifier: "MayaBillerErrorViewController")

    internal static let mayaBillerFormViewController = SceneType<MayaBillerFormViewController>(storyboard: PayBills.self, identifier: "MayaBillerFormViewController")

    internal static let mayaPayBillsCategoryBillerListViewController = SceneType<MayaPayBillsCategoryBillerListViewController>(storyboard: PayBills.self, identifier: "MayaPayBillsCategoryBillerListViewController")

    internal static let mayaPayBillsCategoryViewController = SceneType<MayaPayBillsCategoryViewController>(storyboard: PayBills.self, identifier: "MayaPayBillsCategoryViewController")

    internal static let mayaPayBillsSearchViewController = SceneType<MayaPayBillsSearchViewController>(storyboard: PayBills.self, identifier: "MayaPayBillsSearchViewController")
  }
  internal enum PushApproval: StoryboardType {
    internal static let storyboardName = "PushApproval"

    internal static let pushApprovalAuthorizationListViewController = SceneType<PushApprovalAuthorizationListViewController>(storyboard: PushApproval.self, identifier: "PushApprovalAuthorizationListViewController")

    internal static let pushApprovalAuthorizationRequestDeniedViewController = SceneType<PushApprovalAuthorizationRequestDeniedViewController>(storyboard: PushApproval.self, identifier: "PushApprovalAuthorizationRequestDeniedViewController")

    internal static let pushApprovalReviewAuthorizationRequestViewController = SceneType<PushApprovalReviewAuthorizationRequestViewController>(storyboard: PushApproval.self, identifier: "PushApprovalReviewAuthorizationRequestViewController")
  }
  internal enum Referral: StoryboardType {
    internal static let storyboardName = "Referral"

    internal static let mayaReferralInputCodeViewController = SceneType<MayaReferralInputCodeViewController>(storyboard: Referral.self, identifier: "MayaReferralInputCodeViewController")

    internal static let mayaReferralShareCodeGeneratedViewController = SceneType<MayaReferralShareCodeGeneratedViewController>(storyboard: Referral.self, identifier: "MayaReferralShareCodeGeneratedViewController")

    internal static let mayaReferralShareCodeLandingViewController = SceneType<MayaReferralShareCodeLandingViewController>(storyboard: Referral.self, identifier: "MayaReferralShareCodeLandingViewController")
  }
  internal enum Registration: StoryboardType {
    internal static let storyboardName = "Registration"

    internal static let mayaAutoProvisionV2ViewController = SceneType<MayaAutoProvisionV2ViewController>(storyboard: Registration.self, identifier: "MayaAutoProvisionV2ViewController")

    internal static let mayaRegistrationFormV2Controller = SceneType<MayaRegistrationFormV2Controller>(storyboard: Registration.self, identifier: "MayaRegistrationFormV2Controller")
  }
  internal enum RequestMoney: StoryboardType {
    internal static let storyboardName = "RequestMoney"

    internal static let mayaRequestMoneyFormViewController = SceneType<MayaRequestMoneyFormViewController>(storyboard: RequestMoney.self, identifier: "MayaRequestMoneyFormViewController")

    internal static let mayaRequestMoneyGeneratedQRViewController = SceneType<MayaRequestMoneyGeneratedQRViewController>(storyboard: RequestMoney.self, identifier: "MayaRequestMoneyGeneratedQRViewController")

    internal static let mayaRequestMoneyV2ViewController = SceneType<MayaRequestMoneyV2ViewController>(storyboard: RequestMoney.self, identifier: "MayaRequestMoneyV2ViewController")

    internal static let mayaRequestMoneyViewController = SceneType<MayaRequestMoneyViewController>(storyboard: RequestMoney.self, identifier: "MayaRequestMoneyViewController")
  }
  internal enum SendMoney: StoryboardType {
    internal static let storyboardName = "SendMoney"

    internal static let mayaSendMoneyThemeCardPreviewViewController = SceneType<MayaSendMoneyThemeCardPreviewViewController>(storyboard: SendMoney.self, identifier: "MayaSendMoneyThemeCardPreviewViewController")

    internal static let mayaSendMoneyViewController = SceneType<MayaSendMoneyViewController>(storyboard: SendMoney.self, identifier: "MayaSendMoneyViewController")
  }
  internal enum Shop: StoryboardType {
    internal static let storyboardName = "Shop"

    internal static let initialScene = InitialSceneType<UIKit.UINavigationController>(storyboard: Shop.self)

    internal static let mayaShopAllProvidersViewController = SceneType<MayaShopAllProvidersViewController>(storyboard: Shop.self, identifier: "MayaShopAllProvidersViewController")

    internal static let mayaShopHomeViewController = SceneType<MayaShopHomeViewController>(storyboard: Shop.self, identifier: "MayaShopHomeViewController")

    internal static let mayaShopListPurchaseHistoryViewController = SceneType<MayaShopListPurchaseHistoryViewController>(storyboard: Shop.self, identifier: "MayaShopListPurchaseHistoryViewController")

    internal static let mayaShopMainPurchaseHistoryViewController = SceneType<MayaShopMainPurchaseHistoryViewController>(storyboard: Shop.self, identifier: "MayaShopMainPurchaseHistoryViewController")

    internal static let mayaShopMainViewController = SceneType<MayaShopMainViewController>(storyboard: Shop.self, identifier: "MayaShopMainViewController")

    internal static let mayaShopPaymentConfirmationViewController = SceneType<MayaShopPaymentConfirmationViewController>(storyboard: Shop.self, identifier: "MayaShopPaymentConfirmationViewController")

    internal static let mayaShopPaymentViewController = SceneType<MayaShopPaymentViewController>(storyboard: Shop.self, identifier: "MayaShopPaymentViewController")

    internal static let mayaShopProductListV2ViewController = SceneType<MayaShopProductListV2ViewController>(storyboard: Shop.self, identifier: "MayaShopProductListV2ViewController")

    internal static let mayaShopProductListViewController = SceneType<MayaShopProductListViewController>(storyboard: Shop.self, identifier: "MayaShopProductListViewController")

    internal static let mayaShopProviderSubcategoryProductsViewController = SceneType<MayaShopProviderSubcategoryProductsViewController>(storyboard: Shop.self, identifier: "MayaShopProviderSubcategoryProductsViewController")

    internal static let mayaShopProvidersViewController = SceneType<MayaShopProvidersViewController>(storyboard: Shop.self, identifier: "MayaShopProvidersViewController")

    internal static let mayaShopSearchViewController = SceneType<MayaShopSearchViewController>(storyboard: Shop.self, identifier: "MayaShopSearchViewController")

    internal static let pageViewController = SceneType<PageViewController>(storyboard: Shop.self, identifier: "PageViewController")
  }
  internal enum UpdateProfile: StoryboardType {
    internal static let storyboardName = "UpdateProfile"

    internal static let mayaContactReferenceLearnMoreViewController = SceneType<MayaContactReferenceLearnMoreViewController>(storyboard: UpdateProfile.self, identifier: "MayaContactReferenceLearnMoreViewController")

    internal static let mayaContactReferenceViewController = SceneType<MayaContactReferenceViewController>(storyboard: UpdateProfile.self, identifier: "MayaContactReferenceViewController")

    internal static let mayaUpdateProfilePickerViewController = SceneType<MayaUpdateProfilePickerViewController>(storyboard: UpdateProfile.self, identifier: "MayaUpdateProfilePickerViewController")

    internal static let mayaUpdateProfileViewcontroller = SceneType<MayaUpdateProfileViewController>(storyboard: UpdateProfile.self, identifier: "MayaUpdateProfileViewcontroller")

    internal static let mayaWorkDetailsViewController = SceneType<MayaWorkDetailsViewController>(storyboard: UpdateProfile.self, identifier: "MayaWorkDetailsViewController")
  }
  internal enum Vouchers: StoryboardType {
    internal static let storyboardName = "Vouchers"

    internal static let mayaVoucherConfirmationViewController = SceneType<MayaVoucherConfirmationViewController>(storyboard: Vouchers.self, identifier: "MayaVoucherConfirmationViewController")

    internal static let mayaVouchersDetailsViewController = SceneType<MayaVouchersDetailsViewController>(storyboard: Vouchers.self, identifier: "MayaVouchersDetailsViewController")

    internal static let mayaVouchersListViewController = SceneType<MayaVouchersListViewController>(storyboard: Vouchers.self, identifier: "MayaVouchersListViewController")

    internal static let mayaVouchersMenuPagerViewController = SceneType<MayaVouchersMenuPagerViewController>(storyboard: Vouchers.self, identifier: "MayaVouchersMenuPagerViewController")
  }
}
// swiftlint:enable explicit_type_interface identifier_name line_length prefer_self_in_static_references
// swiftlint:enable type_body_length type_name

// MARK: - Implementation Details

internal protocol StoryboardType {
  static var storyboardName: String { get }
}

internal extension StoryboardType {
  static var storyboard: UIStoryboard {
    let name = self.storyboardName
    return UIStoryboard(name: name, bundle: BundleToken.bundle)
  }
}

internal struct SceneType<T: UIViewController> {
  internal let storyboard: StoryboardType.Type
  internal let identifier: String

  internal func instantiate() -> T {
    let identifier = self.identifier
    guard let controller = storyboard.storyboard.instantiateViewController(withIdentifier: identifier) as? T else {
      fatalError("ViewController '\(identifier)' is not of the expected class \(T.self).")
    }
    return controller
  }

  @available(iOS 13.0, tvOS 13.0, *)
  internal func instantiate(creator block: @escaping (NSCoder) -> T?) -> T {
    return storyboard.storyboard.instantiateViewController(identifier: identifier, creator: block)
  }
}

internal struct InitialSceneType<T: UIViewController> {
  internal let storyboard: StoryboardType.Type

  internal func instantiate() -> T {
    guard let controller = storyboard.storyboard.instantiateInitialViewController() as? T else {
      fatalError("ViewController is not of the expected class \(T.self).")
    }
    return controller
  }

  @available(iOS 13.0, tvOS 13.0, *)
  internal func instantiate(creator block: @escaping (NSCoder) -> T?) -> T {
    guard let controller = storyboard.storyboard.instantiateInitialViewController(creator: block) else {
      fatalError("Storyboard \(storyboard.storyboardName) does not have an initial scene.")
    }
    return controller
  }
}

// swiftlint:disable convenience_type
private final class BundleToken {
  static let bundle: Bundle = {
    #if SWIFT_PACKAGE
    return Bundle.module
    #else
    return Bundle(for: BundleToken.self)
    #endif
  }()
}
// swiftlint:enable convenience_type
