// swiftlint:disable all
// Generated using SwiftGen — https://github.com/SwiftGen/SwiftGen

#if os(macOS)
  import AppKit
#elseif os(iOS)
  import UIKit
#elseif os(tvOS) || os(watchOS)
  import UIKit
#endif

// Deprecated typealiases
@available(*, deprecated, renamed: "ImageAsset.Image", message: "This typealias will be removed in SwiftGen 7.0")
internal typealias AssetImageTypeAlias = ImageAsset.Image

// swiftlint:disable superfluous_disable_command file_length implicit_return

// MARK: - Asset Catalogs

// swiftlint:disable identifier_name line_length nesting type_body_length type_name
internal enum Asset {
  internal enum Images {
    internal enum Dashboard {
      internal enum AddMoney {
        internal static let backgroundReceipt = ImageAsset(name: "backgroundReceipt")
        internal static let backgroundReceiptLarge = ImageAsset(name: "backgroundReceiptLarge")
        internal static let iconAddMoney = ImageAsset(name: "iconAddMoney")
        internal static let iconAutoCashin = ImageAsset(name: "iconAutoCashin")
        internal static let iconAutoCashinOff = ImageAsset(name: "iconAutoCashinOff")
        internal static let iconAutoCashinOn = ImageAsset(name: "iconAutoCashinOn")
        internal static let iconChevronWalletSettings = ImageAsset(name: "iconChevronWalletSettings")
        internal static let iconInstaFill = ImageAsset(name: "iconInstaFill")
        internal static let iconUnion = ImageAsset(name: "iconUnion")
        internal static let imageSchemeJcb = ImageAsset(name: "imageSchemeJcb")
        internal static let imageSchemeMastercard = ImageAsset(name: "imageSchemeMastercard")
        internal static let imageSchemeVisa = ImageAsset(name: "imageSchemeVisa")
        internal static let padalaAgentSearch = ImageAsset(name: "padalaAgentSearch")
        internal static let receiptSeparator = ImageAsset(name: "receiptSeparator")
      }
      internal enum BankPullFunds {
        internal static let addMoneyBank = ImageAsset(name: "addMoneyBank")
      }
      internal enum MayaCredit {
        internal static let iconCreditSettings = ImageAsset(name: "iconCreditSettings")
        internal static let iconPayCredit = ImageAsset(name: "iconPayCredit")
        internal static let iconTransferCredit = ImageAsset(name: "iconTransferCredit")
        internal static let iconViewSummary = ImageAsset(name: "iconViewSummary")
        internal static let mayaCreditLeftRibbon = ImageAsset(name: "mayaCreditLeftRibbon")
        internal static let mayaCreditRightRibbon = ImageAsset(name: "mayaCreditRightRibbon")
      }
      internal enum OnlinePayments {
        internal static let imageUnavailablePartners = ImageAsset(name: "imageUnavailablePartners")
      }
      internal enum Overlay {
        internal static let iconExitGuide = ImageAsset(name: "iconExitGuide")
      }
    }
    internal enum Edd {
      internal static let checkCircle = ImageAsset(name: "checkCircle")
      internal static let dropdown = ImageAsset(name: "dropdown")
      internal static let eddIconCamera = ImageAsset(name: "eddIconCamera")
      internal static let iconRemove = ImageAsset(name: "iconRemove")
      internal static let smallProfileIcon = ImageAsset(name: "smallProfileIcon")
    }
    internal enum Inbox {
      internal static let iconOptions = ImageAsset(name: "iconOptions")
    }
    internal enum MayaCredit {
      internal static let imageGigalifeLogo = ImageAsset(name: "imageGigalifeLogo")
      internal static let optInBackground = ImageAsset(name: "optInBackground")
    }
    internal enum More {
      internal enum AccountLimits {
        internal static let upgradeAccountLimits = ImageAsset(name: "upgradeAccountLimits")
      }
      internal enum AccountRecovery {
        internal static let iconRecoveryemail = ImageAsset(name: "iconRecoveryemail")
      }
      internal enum Kyc {
        internal static let iconAddressActive = ImageAsset(name: "iconAddressActive")
        internal static let iconAddressInactive = ImageAsset(name: "iconAddressInactive")
        internal static let iconCamera = ImageAsset(name: "iconCamera")
        internal static let iconGrayChevronRight = ImageAsset(name: "iconGrayChevronRight")
        internal static let iconPersonalActive = ImageAsset(name: "iconPersonalActive")
        internal static let iconPersonalInactive = ImageAsset(name: "iconPersonalInactive")
        internal static let iconPhotoCapture = ImageAsset(name: "iconPhotoCapture")
        internal static let iconVideo = ImageAsset(name: "iconVideo")
        internal static let iconWorkActive = ImageAsset(name: "iconWorkActive")
        internal static let iconWorkInactive = ImageAsset(name: "iconWorkInactive")
        internal static let imageUpgrade = ImageAsset(name: "imageUpgrade")
      }
      internal enum MyFavorites {
        internal static let iconBankActive = ImageAsset(name: "iconBankActive")
        internal static let iconBankInactive = ImageAsset(name: "iconBankInactive")
        internal static let iconSendMoneyFavoritesActive = ImageAsset(name: "iconSendMoneyFavoritesActive")
        internal static let iconSendMoneyFavoritesInactive = ImageAsset(name: "iconSendMoneyFavoritesInactive")
      }
      internal enum MyCards {
        internal enum New {
          internal static let iconMyCardsActiveNew = ImageAsset(name: "iconMyCardsActiveNew")
          internal static let iconMyCardsInactiveNew = ImageAsset(name: "iconMyCardsInactiveNew")
        }
        internal static let backgroundVirtualCardMastercard = ImageAsset(name: "backgroundVirtualCardMastercard")
        internal static let backgroundVirtualCardVisa = ImageAsset(name: "backgroundVirtualCardVisa")
        internal static let iconCVV = ImageAsset(name: "iconCVV")
        internal static let iconCard = ImageAsset(name: "iconCard")
        internal static let iconDashOnlinePayment = ImageAsset(name: "iconDashOnlinePayment")
        internal static let iconDashOnlinePaymentSmart = ImageAsset(name: "iconDashOnlinePaymentSmart")
        internal static let iconLinkCard = ImageAsset(name: "iconLinkCard")
        internal static let iconLinkNoCard = ImageAsset(name: "iconLinkNoCard")
        internal static let iconMyCardsActive = ImageAsset(name: "iconMyCardsActive")
        internal static let iconMyCardsInactive = ImageAsset(name: "iconMyCardsInactive")
        internal static let iconOnlinePaymentCardPayMaya = ImageAsset(name: "iconOnlinePaymentCardPayMaya")
        internal static let iconPayMayaBancnet = ImageAsset(name: "iconPayMayaBancnet")
        internal static let iconPayMayaMastercard = ImageAsset(name: "iconPayMayaMastercard")
        internal static let iconPayMayaVisa = ImageAsset(name: "iconPayMayaVisa")
        internal static let imageLinkCard = ImageAsset(name: "imageLinkCard")
        internal static let imageMaximumLinkedCardsNewBird = ImageAsset(name: "imageMaximumLinkedCardsNewBird")
        internal static let imageMayaActivateVirtualCard = ImageAsset(name: "imageMayaActivateVirtualCard")
        internal static let imageMayaVirtualCard = ImageAsset(name: "imageMayaVirtualCard")
        internal static let imageOverlaySolidGreen = ImageAsset(name: "imageOverlaySolidGreen")
        internal static let imageWelcomeVirtualCard = ImageAsset(name: "imageWelcomeVirtualCard")
        internal static let logoJCBDisabled = ImageAsset(name: "logoJCBDisabled")
        internal static let logoJCBLarge = ImageAsset(name: "logoJCBLarge")
        internal static let logoJCBSmall = ImageAsset(name: "logoJCBSmall")
        internal static let logoMastercard = ImageAsset(name: "logoMastercard")
        internal static let logoMastercardDisabled = ImageAsset(name: "logoMastercardDisabled")
        internal static let logoMastercardLarge = ImageAsset(name: "logoMastercardLarge")
        internal static let logoMastercardSmall = ImageAsset(name: "logoMastercardSmall")
        internal static let logoMvp = ImageAsset(name: "logoMvp")
        internal static let logoPldtSmart = ImageAsset(name: "logoPldtSmart")
        internal static let logoVISADisabled = ImageAsset(name: "logoVISADisabled")
        internal static let logoVisa = ImageAsset(name: "logoVisa")
        internal static let logoVisaLarge = ImageAsset(name: "logoVisaLarge")
        internal static let logoVisaSmall = ImageAsset(name: "logoVisaSmall")
      }
      internal enum Ekyc {
        internal static let captureId = ImageAsset(name: "CaptureId")
        internal static let iconAddressEKYCActive = ImageAsset(name: "iconAddressEKYCActive")
        internal static let iconAddressEKYCInactive = ImageAsset(name: "iconAddressEKYCInactive")
        internal static let iconCopy = ImageAsset(name: "iconCopy")
        internal static let iconFaceVerificationActive = ImageAsset(name: "iconFaceVerificationActive")
        internal static let iconFaceVerificationInactive = ImageAsset(name: "iconFaceVerificationInactive")
        internal static let iconIdVerificationActive = ImageAsset(name: "iconIdVerificationActive")
        internal static let iconIdentificationEKYCActive = ImageAsset(name: "iconIdentificationEKYCActive")
        internal static let iconIdentificationEKYCInactive = ImageAsset(name: "iconIdentificationEKYCInactive")
        internal static let iconInfoSecondId = ImageAsset(name: "iconInfoSecondId")
        internal static let iconPersonalEKYCActive = ImageAsset(name: "iconPersonalEKYCActive")
        internal static let iconPersonalInfoActive = ImageAsset(name: "iconPersonalInfoActive")
        internal static let iconPersonalInfoInactive = ImageAsset(name: "iconPersonalInfoInactive")
        internal static let iconStartRecord = ImageAsset(name: "iconStartRecord")
        internal static let imageLivenessGuide = ImageAsset(name: "imageLivenessGuide")
        internal static let imagePlayVideo = ImageAsset(name: "imagePlayVideo")
        internal static let imageUserTypeActive = ImageAsset(name: "imageUserTypeActive")
        internal static let imageUserTypeInactive = ImageAsset(name: "imageUserTypeInactive")
        internal static let paginationInactive = ImageAsset(name: "pagination-inactive")
      }
      internal static let iconBadgeForEdd = ImageAsset(name: "iconBadgeForEdd")
      internal static let iconBadgeKYC0 = ImageAsset(name: "iconBadgeKYC0")
      internal static let iconBadgeKYC1 = ImageAsset(name: "iconBadgeKYC1")
      internal static let iconBadgeVerified = ImageAsset(name: "iconBadgeVerified")
      internal static let iconBadgeVerifiedInfo = ImageAsset(name: "iconBadgeVerifiedInfo")
      internal static let iconKYCBasic = ImageAsset(name: "iconKYCBasic")
      internal static let iconLimitedAccount = ImageAsset(name: "iconLimitedAccount")
      internal static let iconMessenger = ImageAsset(name: "iconMessenger")
      internal static let iconShareLink = ImageAsset(name: "iconShareLink")
    }
    internal enum PayBills {
      internal static let iconBarcode = ImageAsset(name: "iconBarcode")
      internal static let iconBillerFavorite = ImageAsset(name: "iconBillerFavorite")
      internal static let imageBarcodeFrame = ImageAsset(name: "imageBarcodeFrame")
      internal static let imageBarcodeFrameInvalid = ImageAsset(name: "imageBarcodeFrameInvalid")
      internal static let imageBarcodeGuide = ImageAsset(name: "imageBarcodeGuide")
      internal static let placeholderBillerTiles = ImageAsset(name: "placeholderBillerTiles")
    }
    internal enum PreDashboard {
      internal static let iconDisabledCheckbox = ImageAsset(name: "iconDisabledCheckbox")
      internal static let iconFlagPhilippines = ImageAsset(name: "iconFlagPhilippines")
      internal static let iconLandingWizard = ImageAsset(name: "iconLandingWizard")
      internal static let iconPageActive = ImageAsset(name: "iconPageActive")
      internal static let iconPageInactive = ImageAsset(name: "iconPageInactive")
      internal static let iconPrivacyActive = ImageAsset(name: "iconPrivacyActive")
      internal static let iconPrivacyInactive = ImageAsset(name: "iconPrivacyInactive")
      internal static let imageAddMoney = ImageAsset(name: "imageAddMoney")
      internal static let imageGreenHidePassword = ImageAsset(name: "imageGreenHidePassword")
      internal static let imageGreenShowPassword = ImageAsset(name: "imageGreenShowPassword")
      internal static let imageHidePassword = ImageAsset(name: "imageHidePassword")
      internal static let imageShowPassword = ImageAsset(name: "imageShowPassword")
      internal static let imageWhiteHidePassword = ImageAsset(name: "imageWhiteHidePassword")
      internal static let imageWhiteShowPassword = ImageAsset(name: "imageWhiteShowPassword")
    }
    internal enum QuickActions {
      internal static let iconMyQR = ImageAsset(name: "iconMyQR")
      internal static let iconScanQR = ImageAsset(name: "iconScanQR")
    }
    internal enum Referral {
      internal static let backgroundInputCode = ImageAsset(name: "backgroundInputCode")
      internal static let backgroundSuccessSubmitCode = ImageAsset(name: "backgroundSuccessSubmitCode")
      internal static let inviteFriends = ImageAsset(name: "inviteFriends")
      internal static let logoShareCodeError = ImageAsset(name: "logoShareCodeError")
      internal static let logoShareCodeReachQuota = ImageAsset(name: "logoShareCodeReachQuota")
    }
    internal enum SendMoney {
      internal static let headerBankTransfer = ImageAsset(name: "headerBankTransfer")
      internal static let iconAddContact = ImageAsset(name: "iconAddContact")
      internal static let iconBankTransferPlaceholder = ImageAsset(name: "iconBankTransferPlaceholder")
      internal static let iconGiphy = ImageAsset(name: "iconGiphy")
      internal static let iconGiphyError = ImageAsset(name: "iconGiphyError")
      internal static let iconInstapay = ImageAsset(name: "iconInstapay")
      internal static let iconLightGrayChevronRight = ImageAsset(name: "iconLightGrayChevronRight")
      internal static let iconPreviewDesign = ImageAsset(name: "iconPreviewDesign")
      internal static let iconToBankAccount = ImageAsset(name: "iconToBankAccount")
      internal static let iconToMobileNumber = ImageAsset(name: "iconToMobileNumber")
      internal static let imageSendMoneyHeader = ImageAsset(name: "imageSendMoneyHeader")
      internal static let logoQRPH = ImageAsset(name: "logoQRPH")
    }
    internal enum Shop {
      internal enum New {
        internal static let iconShopActiveNew = ImageAsset(name: "iconShopActiveNew")
        internal static let iconShopInactiveNew = ImageAsset(name: "iconShopInactiveNew")
        internal static let iconShopUnreadActiveNew = ImageAsset(name: "iconShopUnreadActiveNew")
        internal static let iconShopUnreadInactiveNew = ImageAsset(name: "iconShopUnreadInactiveNew")
      }
      internal static let iconGiftedProduct = ImageAsset(name: "iconGiftedProduct")
      internal static let iconNewShopPlaceholderImage = ImageAsset(name: "iconNewShopPlaceholderImage")
      internal static let iconShopActive = ImageAsset(name: "iconShopActive")
      internal static let iconShopInactive = ImageAsset(name: "iconShopInactive")
      internal static let iconShopSuggestion = ImageAsset(name: "iconShopSuggestion")
      internal static let iconShopUnreadActive = ImageAsset(name: "iconShopUnreadActive")
      internal static let iconShopUnreadInactive = ImageAsset(name: "iconShopUnreadInactive")
      internal static let imageShopCannotLoadList = ImageAsset(name: "imageShopCannotLoadList")
      internal static let shopSegmentDivider = ImageAsset(name: "shopSegmentDivider")
    }
    internal enum Success {
      internal static let iconAddToContacts = ImageAsset(name: "iconAddToContacts")
      internal static let receiptTearDown = ImageAsset(name: "receiptTearDown")
    }
    internal enum Transport {
      internal static let iconSelectionCompleted = ImageAsset(name: "iconSelectionCompleted")
      internal static let iconSelectionOngoing = ImageAsset(name: "iconSelectionOngoing")
      internal static let imageTransportBeep = ImageAsset(name: "imageTransportBeep")
    }
    internal enum Vouchers {
      internal static let iconPromosDefault = ImageAsset(name: "iconPromosDefault")
    }
  }
  internal enum MayaImages {
    internal enum BankTransfer {
      internal static let iconBankTransfer = ImageAsset(name: "iconBankTransfer")
      internal static let logoInstapay = ImageAsset(name: "logoInstapay")
    }
    internal enum Cards {
      internal static let activateMayaCardIcon = ImageAsset(name: "activateMayaCardIcon")
      internal static let limitCardIcon = ImageAsset(name: "limitCardIcon")
      internal static let mayaCardIcon = ImageAsset(name: "mayaCardIcon")
      internal static let takeMayaCardImage = ImageAsset(name: "takeMayaCardImage")
      internal static let virtualCardIcon = ImageAsset(name: "virtualCardIcon")
    }
    internal enum CashIn {
      internal static let barcodePhone = ImageAsset(name: "barcodePhone")
      internal static let cashInAccountLimits = ImageAsset(name: "cashInAccountLimits")
      internal static let cashInBankPull = ImageAsset(name: "cashInBankPull")
      internal static let cashInCard = ImageAsset(name: "cashInCard")
      internal static let cashInMayaCenter = ImageAsset(name: "cashInMayaCenter")
      internal static let cashInPadala = ImageAsset(name: "cashInPadala")
      internal static let cashInRemoveCard = ImageAsset(name: "cashInRemoveCard")
      internal static let cashInWallet = ImageAsset(name: "cashInWallet")
      internal static let iconBankPullPlaceholder = ImageAsset(name: "iconBankPullPlaceholder")
      internal static let iconCvv = ImageAsset(name: "iconCvv")
      internal static let iconScanCamera = ImageAsset(name: "iconScanCamera")
      internal static let iconSmallJCB = ImageAsset(name: "iconSmallJCB")
      internal static let iconSmallMastercard = ImageAsset(name: "iconSmallMastercard")
      internal static let iconSmallUnknownCard = ImageAsset(name: "iconSmallUnknownCard")
      internal static let iconSmallVisa = ImageAsset(name: "iconSmallVisa")
      internal static let imageRemoveCard = ImageAsset(name: "imageRemoveCard")
      internal static let mayaCenterLogo = ImageAsset(name: "mayaCenterLogo")
      internal static let newViolator = ImageAsset(name: "newViolator")
      internal static let padalaLogo = ImageAsset(name: "padalaLogo")
    }
    internal enum Common {
      internal enum CheckBox {
        internal static let iconSelectedReadOnly = ImageAsset(name: "iconSelectedReadOnly")
      }
      internal enum Emoji {
        internal static let imageGreenHeart = ImageAsset(name: "imageGreenHeart")
        internal static let imagePig = ImageAsset(name: "imagePig")
      }
      internal enum Icons {
        internal static let iconBlackFilledCircleX = ImageAsset(name: "iconBlackFilledCircleX")
      }
      internal static let image3DPiggyBank = ImageAsset(name: "image3DPiggyBank")
      internal static let image3DSuccess = ImageAsset(name: "image3DSuccess")
    }
    internal enum Compliance {
      internal static let fatcaIDPhoto = ImageAsset(name: "fatcaIDPhoto")
    }
    internal enum CreatorStore {
      internal static let iconFundsCreatorStoreInterstitial = ImageAsset(name: "iconFundsCreatorStoreInterstitial")
      internal static let iconGlobalRemittanceCreatorStoreInterstitial = ImageAsset(name: "iconGlobalRemittanceCreatorStoreInterstitial")
      internal static let iconGlobalStocksCreatorStoreInterstitial = ImageAsset(name: "iconGlobalStocksCreatorStoreInterstitial")
      internal static let iconInsuranceCreatorStoreInterstitial = ImageAsset(name: "iconInsuranceCreatorStoreInterstitial")
      internal static let iconLuckyGamesCreatorStoreInterstitial = ImageAsset(name: "iconLuckyGamesCreatorStoreInterstitial")
      internal static let iconSparkHackathonCreatorStoreInterstitial = ImageAsset(name: "iconSparkHackathonCreatorStoreInterstitial")
      internal static let iconStocksCreatorStoreInterstitial = ImageAsset(name: "iconStocksCreatorStoreInterstitial")
      internal static let iconStocksGameCreatorStoreInterstitial = ImageAsset(name: "iconStocksGameCreatorStoreInterstitial")
      internal static let iconStreamCreatorStoreInterstitial = ImageAsset(name: "iconStreamCreatorStoreInterstitial")
      internal static let imageBannerPlaceholder = ImageAsset(name: "imageBannerPlaceholder")
    }
    internal enum Credit {
      internal static let mayaClockIcon = ImageAsset(name: "MayaClockIcon")
      internal static let system = ImageAsset(name: "System")
      internal static let systemInfo = ImageAsset(name: "System_Info")
      internal static let accountSummary = ImageAsset(name: "accountSummary")
      internal static let coinBag = ImageAsset(name: "coinBag")
      internal static let creditSettings = ImageAsset(name: "creditSettings")
      internal static let imageCashInCropped = ImageAsset(name: "imageCashInCropped")
      internal static let imageElevateQR3Cropped = ImageAsset(name: "imageElevateQR3Cropped")
      internal static let imageLightning = ImageAsset(name: "imageLightning")
      internal static let imagePaidCropped = ImageAsset(name: "imagePaidCropped")
      internal static let imageQRPH = ImageAsset(name: "imageQRPH")
      internal static let imageShopCropped = ImageAsset(name: "imageShopCropped")
      internal static let info = ImageAsset(name: "info")
      internal static let kycUpgradeRocket = ImageAsset(name: "kycUpgradeRocket")
      internal static let mayaAccountError = ImageAsset(name: "mayaAccountError")
      internal static let mayaBankLogo = ImageAsset(name: "mayaBankLogo")
      internal static let other = ImageAsset(name: "other")
      internal static let payCreditBill = ImageAsset(name: "payCreditBill")
      internal static let penAndPaper = ImageAsset(name: "penAndPaper")
    }
    internal enum Dashboard {
      internal static let actionCardFailedIcon = ImageAsset(name: "actionCardFailedIcon")
      internal static let activityDetailsFailedIcon = ImageAsset(name: "activityDetailsFailedIcon")
      internal static let activityDetailsProcessingIcon = ImageAsset(name: "activityDetailsProcessingIcon")
      internal static let backgroundFrozen = ImageAsset(name: "backgroundFrozen")
      internal static let blackArrowTopRightSmallIcon = ImageAsset(name: "blackArrowTopRightSmallIcon")
      internal static let cashInV2Icon = ImageAsset(name: "cashInV2Icon")
      internal static let detailsShareIcon = ImageAsset(name: "detailsShareIcon")
      internal static let eyeClosedIcon = ImageAsset(name: "eyeClosedIcon")
      internal static let eyeOpenedIcon = ImageAsset(name: "eyeOpenedIcon")
      internal static let greenArrowBottomLeftSmallIcon = ImageAsset(name: "greenArrowBottomLeftSmallIcon")
      internal static let instapayExtraSmallRoundedLogo = ImageAsset(name: "instapayExtraSmallRoundedLogo")
      internal static let moreIcon = ImageAsset(name: "moreIcon")
      internal static let pageControlSelected = ImageAsset(name: "pageControlSelected")
      internal static let pageControlUnselected = ImageAsset(name: "pageControlUnselected")
      internal static let qrphExtraSmallLogo = ImageAsset(name: "qrphExtraSmallLogo")
      internal static let savingsIcon = ImageAsset(name: "savingsIcon")
      internal static let sendMoneyV2Icon = ImageAsset(name: "sendMoneyV2Icon")
      internal static let walletRewardsMissionIcon = ImageAsset(name: "walletRewardsMissionIcon")
      internal static let walletRewardsVouchersIcon = ImageAsset(name: "walletRewardsVouchersIcon")
    }
    internal enum DeviceManagement {
      internal static let iconDeviceAndroid = ImageAsset(name: "iconDeviceAndroid")
      internal static let iconDeviceApple = ImageAsset(name: "iconDeviceApple")
      internal static let iconDeviceManagementHelp = ImageAsset(name: "iconDeviceManagementHelp")
      internal static let iconDeviceManagementQuestionMark = ImageAsset(name: "iconDeviceManagementQuestionMark")
      internal static let iconDeviceUnknown = ImageAsset(name: "iconDeviceUnknown")
    }
    internal enum Ekyc {
      internal static let ekycStep1 = ImageAsset(name: "ekycStep1")
      internal static let ekycStep2 = ImageAsset(name: "ekycStep2")
      internal static let ekycStep3 = ImageAsset(name: "ekycStep3")
      internal static let ekycWelcomeFeaturesIcon = ImageAsset(name: "ekycWelcomeFeaturesIcon")
      internal static let iconBank = ImageAsset(name: "iconBank")
      internal static let iconCamera = ImageAsset(name: "iconCamera")
      internal static let iconCameraWhite = ImageAsset(name: "iconCameraWhite")
      internal static let iconEkycIdActive = ImageAsset(name: "iconEkycIdActive")
      internal static let iconEkycPersonalActive = ImageAsset(name: "iconEkycPersonalActive")
      internal static let iconEkycPersonalInactive = ImageAsset(name: "iconEkycPersonalInactive")
      internal static let iconEkycStepDone = ImageAsset(name: "iconEkycStepDone")
      internal static let iconEkycVideoActive = ImageAsset(name: "iconEkycVideoActive")
      internal static let iconEkycVideoInactive = ImageAsset(name: "iconEkycVideoInactive")
      internal static let iconFlex = ImageAsset(name: "iconFlex")
      internal static let iconInfoFilledWhite = ImageAsset(name: "iconInfoFilledWhite")
      internal static let iconPhone = ImageAsset(name: "iconPhone")
      internal static let iconRedFilledCircleX = ImageAsset(name: "iconRedFilledCircleX")
      internal static let imageMayaGreenCamera = ImageAsset(name: "imageMayaGreenCamera")
      internal static let imageMayaRainbow = ImageAsset(name: "imageMayaRainbow")
      internal static let imageMayaUnderReviewLarge = ImageAsset(name: "imageMayaUnderReviewLarge")
      internal static let imageSimpleCaptureIDFrame = ImageAsset(name: "imageSimpleCaptureIDFrame")
      internal static let sampleIdDocument = ImageAsset(name: "sampleIdDocument")
    }
    internal enum EKYCZoloz {
      internal static let bioSheet = ImageAsset(name: "BioSheet")
      internal static let failed = ImageAsset(name: "Failed")
      internal static let rockOn = ImageAsset(name: "RockOn")
      internal static let warning = ImageAsset(name: "Warning")
      internal static let additionalDocumentsGuide = ImageAsset(name: "additionalDocumentsGuide")
      internal static let copyToClipboard = ImageAsset(name: "copyToClipboard")
      internal static let exampleTransactionDetail = ImageAsset(name: "exampleTransactionDetail")
      internal static let iconID = ImageAsset(name: "iconID")
      internal static let iconInfoWarning = ImageAsset(name: "iconInfoWarning")
      internal static let placeholder = ImageAsset(name: "placeholder")
      internal static let sampleDocument = ImageAsset(name: "sampleDocument")
      internal static let takePhoto = ImageAsset(name: "takePhoto")
    }
    internal enum Inbox {
      internal static let iconEmptyNotifications = ImageAsset(name: "iconEmptyNotifications")
      internal static let iconGreenMessageBox = ImageAsset(name: "iconGreenMessageBox")
      internal static let iconInboxHistory = ImageAsset(name: "iconInboxHistory")
      internal static let iconInboxSupport = ImageAsset(name: "iconInboxSupport")
      internal static let iconInboxV2 = ImageAsset(name: "iconInboxV2")
      internal static let imageEmptyNotifications = ImageAsset(name: "imageEmptyNotifications")
      internal static let imageInboxDetailsPlaceholder = ImageAsset(name: "imageInboxDetailsPlaceholder")
    }
    internal enum Mma {
      internal static let iconPhilippineAirlinesMMAInterstitial = ImageAsset(name: "iconPhilippineAirlinesMMAInterstitial")
    }
    internal enum Missions {
      internal static let iconMissionTaskDone = ImageAsset(name: "iconMissionTaskDone")
      internal static let iconMissionsCoinBag = ImageAsset(name: "iconMissionsCoinBag")
      internal static let iconTargetAndArrow = ImageAsset(name: "iconTargetAndArrow")
      internal static let missionsGiftBox = ImageAsset(name: "missionsGiftBox")
      internal static let missionsSuccessIcon = ImageAsset(name: "missionsSuccessIcon")
      internal static let missionsVoucherAqua = ImageAsset(name: "missionsVoucherAqua")
    }
    internal enum MyFavorites {
      internal static let iconFavoriteBank = ImageAsset(name: "iconFavoriteBank")
      internal static let iconFavoriteBankPlaceholder = ImageAsset(name: "iconFavoriteBankPlaceholder")
      internal static let iconFavoriteEmptyBank = ImageAsset(name: "iconFavoriteEmptyBank")
      internal static let iconFavoriteEmptyBiller = ImageAsset(name: "iconFavoriteEmptyBiller")
      internal static let iconFavoriteEmptySendMoney = ImageAsset(name: "iconFavoriteEmptySendMoney")
      internal static let iconFavoriteSendMoney = ImageAsset(name: "iconFavoriteSendMoney")
    }
    internal enum PayBills {
      internal static let arrowDownIcon = ImageAsset(name: "arrowDownIcon")
      internal static let billsIcon = ImageAsset(name: "billsIcon")
      internal static let calendarIcon = ImageAsset(name: "calendarIcon")
      internal static let dotImage = ImageAsset(name: "dot_image")
      internal static let iconBillerReminderSet = ImageAsset(name: "iconBillerReminderSet")
      internal static let iconBillerReminderToday = ImageAsset(name: "iconBillerReminderToday")
      internal static let imageMCCLPreview = ImageAsset(name: "imageMCCLPreview")
      internal static let mayaCreditPreview = ImageAsset(name: "mayaCreditPreview")
      internal static let newTagGreen = ImageAsset(name: "newTagGreen")
      internal static let placeholderBillerTiles = ImageAsset(name: "placeholderBillerTiles")
      internal static let sampleBillerReminder = ImageAsset(name: "sampleBillerReminder")
    }
    internal enum Prelogin {
      internal static let iconFaceId = ImageAsset(name: "iconFaceId")
      internal static let iconPaper = ImageAsset(name: "iconPaper")
      internal static let iconSecure = ImageAsset(name: "iconSecure")
      internal static let iconThinking = ImageAsset(name: "iconThinking")
      internal static let iconTinbo = ImageAsset(name: "iconTinbo")
      internal static let iconTouchId = ImageAsset(name: "iconTouchId")
      internal static let imageDataPrivacy = ImageAsset(name: "imageDataPrivacy")
      internal static let imageLocationPermission = ImageAsset(name: "imageLocationPermission")
      internal static let imageOpenedFolder = ImageAsset(name: "imageOpenedFolder")
      internal static let imagePartyPopper = ImageAsset(name: "imagePartyPopper")
      internal static let imageWelcome1 = ImageAsset(name: "imageWelcome1")
      internal static let imageWelcome2 = ImageAsset(name: "imageWelcome2")
      internal static let imageWelcome3 = ImageAsset(name: "imageWelcome3")
      internal static let imageWelcomeBorrow = ImageAsset(name: "imageWelcomeBorrow")
      internal static let imageWelcomeGrow = ImageAsset(name: "imageWelcomeGrow")
      internal static let imageWelcomePay = ImageAsset(name: "imageWelcomePay")
      internal static let imageWelcomeSave = ImageAsset(name: "imageWelcomeSave")
      internal static let imageWelcomeSend = ImageAsset(name: "imageWelcomeSend")
    }
    internal enum Profile {
      internal static let iconCopyBlack = ImageAsset(name: "iconCopyBlack")
      internal static let iconDuplicateAccount = ImageAsset(name: "iconDuplicateAccount")
      internal static let iconInviteAFriend = ImageAsset(name: "iconInviteAFriend")
      internal static let iconMayaAccountLimitsMenu = ImageAsset(name: "iconMayaAccountLimitsMenu")
      internal static let iconMayaAuthorizeRequests = ImageAsset(name: "iconMayaAuthorizeRequests")
      internal static let iconMayaFavoritesMenu = ImageAsset(name: "iconMayaFavoritesMenu")
      internal static let iconMayaHelpMenu = ImageAsset(name: "iconMayaHelpMenu")
      internal static let iconMayaInviteFriends = ImageAsset(name: "iconMayaInviteFriends")
      internal static let iconMayaMissionsMenu = ImageAsset(name: "iconMayaMissionsMenu")
      internal static let iconMayaProfile = ImageAsset(name: "iconMayaProfile")
      internal static let iconMayaQuickGuideMenu = ImageAsset(name: "iconMayaQuickGuideMenu")
      internal static let iconMayaSettingsMenu = ImageAsset(name: "iconMayaSettingsMenu")
      internal static let iconMayaSubmitInviteCodeMenu = ImageAsset(name: "iconMayaSubmitInviteCodeMenu")
      internal static let iconMayaVouchersMenu = ImageAsset(name: "iconMayaVouchersMenu")
      internal static let iconQR = ImageAsset(name: "iconQR")
      internal static let iconRateThisApp = ImageAsset(name: "iconRateThisApp")
      internal static let imageAccountError = ImageAsset(name: "imageAccountError")
      internal static let imageMayaInvite = ImageAsset(name: "imageMayaInvite")
      internal static let imageMayaUnderReview = ImageAsset(name: "imageMayaUnderReview")
      internal static let imageMayaUpgrade = ImageAsset(name: "imageMayaUpgrade")
      internal static let imageMayaUsername = ImageAsset(name: "imageMayaUsername")
      internal static let imageShowName = ImageAsset(name: "imageShowName")
      internal static let imageTitleViewBasic = ImageAsset(name: "imageTitleViewBasic")
      internal static let imageTitleViewUpgraded = ImageAsset(name: "imageTitleViewUpgraded")
      internal static let imageTitleViewVerified = ImageAsset(name: "imageTitleViewVerified")
      internal static let imageUpgradeMyAccount = ImageAsset(name: "imageUpgradeMyAccount")
      internal static let imageWalletBalance = ImageAsset(name: "imageWalletBalance")
      internal static let passwordInfo = ImageAsset(name: "passwordInfo")
    }
    internal enum Qr {
      internal static let iconGcashLogoSmall = ImageAsset(name: "iconGcashLogoSmall")
      internal static let iconInstaPayLogoSmall = ImageAsset(name: "iconInstaPayLogoSmall")
      internal static let iconNewUploadQR = ImageAsset(name: "iconNewUploadQR")
      internal static let iconQRPhLogoSmall = ImageAsset(name: "iconQRPhLogoSmall")
      internal static let iconShowMyQR = ImageAsset(name: "iconShowMyQR")
    }
    internal enum RequestMoney {
      internal static let fullLogoMaya = ImageAsset(name: "fullLogoMaya")
      internal static let iconMessage = ImageAsset(name: "iconMessage")
      internal static let iconMoney = ImageAsset(name: "iconMoney")
      internal static let iconQRCodeInstaPay = ImageAsset(name: "iconQRCodeInstaPay")
      internal static let iconRoundMaya = ImageAsset(name: "iconRoundMaya")
      internal static let iconVerifiedFilled = ImageAsset(name: "iconVerifiedFilled")
      internal static let imageSeparator = ImageAsset(name: "imageSeparator")
    }
    internal enum SendMoney {
      internal static let image3DWallet = ImageAsset(name: "image3DWallet")
      internal static let logoSendMoneyUser = ImageAsset(name: "logoSendMoneyUser")
    }
    internal enum Services {
      internal static let iconHelpArrowRight = ImageAsset(name: "iconHelpArrowRight")
      internal static let iconServiceBankTransfer = ImageAsset(name: "iconServiceBankTransfer")
      internal static let iconServiceBills = ImageAsset(name: "iconServiceBills")
      internal static let iconServiceBlackpinkGiveaway = ImageAsset(name: "iconServiceBlackpinkGiveaway")
      internal static let iconServiceCards = ImageAsset(name: "iconServiceCards")
      internal static let iconServiceCashIn = ImageAsset(name: "iconServiceCashIn")
      internal static let iconServiceCredit = ImageAsset(name: "iconServiceCredit")
      internal static let iconServiceCreditCard = ImageAsset(name: "iconServiceCreditCard")
      internal static let iconServiceCrypto = ImageAsset(name: "iconServiceCrypto")
      internal static let iconServiceDeals = ImageAsset(name: "iconServiceDeals")
      internal static let iconServiceDonations = ImageAsset(name: "iconServiceDonations")
      internal static let iconServiceESims = ImageAsset(name: "iconServiceESims")
      internal static let iconServiceFood = ImageAsset(name: "iconServiceFood")
      internal static let iconServiceFunds = ImageAsset(name: "iconServiceFunds")
      internal static let iconServiceGift = ImageAsset(name: "iconServiceGift")
      internal static let iconServiceGlobalRemittance = ImageAsset(name: "iconServiceGlobalRemittance")
      internal static let iconServiceGlobalStocks = ImageAsset(name: "iconServiceGlobalStocks")
      internal static let iconServiceGovernment = ImageAsset(name: "iconServiceGovernment")
      internal static let iconServiceInsurance = ImageAsset(name: "iconServiceInsurance")
      internal static let iconServiceInviteAFriend = ImageAsset(name: "iconServiceInviteAFriend")
      internal static let iconServiceLoad = ImageAsset(name: "iconServiceLoad")
      internal static let iconServiceLoans = ImageAsset(name: "iconServiceLoans")
      internal static let iconServiceLuckyGames = ImageAsset(name: "iconServiceLuckyGames")
      internal static let iconServiceMayaMall = ImageAsset(name: "iconServiceMayaMall")
      internal static let iconServiceMissions = ImageAsset(name: "iconServiceMissions")
      internal static let iconServiceMyQR = ImageAsset(name: "iconServiceMyQR")
      internal static let iconServiceMyQRv2 = ImageAsset(name: "iconServiceMyQRv2")
      internal static let iconServicePAL = ImageAsset(name: "iconServicePAL")
      internal static let iconServicePBB = ImageAsset(name: "iconServicePBB")
      internal static let iconServicePartners = ImageAsset(name: "iconServicePartners")
      internal static let iconServicePayLater = ImageAsset(name: "iconServicePayLater")
      internal static let iconServicePersonalGoals = ImageAsset(name: "iconServicePersonalGoals")
      internal static let iconServicePurchaseFinancing = ImageAsset(name: "iconServicePurchaseFinancing")
      internal static let iconServiceRafflePromo = ImageAsset(name: "iconServiceRafflePromo")
      internal static let iconServiceRetail = ImageAsset(name: "iconServiceRetail")
      internal static let iconServiceSavings = ImageAsset(name: "iconServiceSavings")
      internal static let iconServiceScanQR = ImageAsset(name: "iconServiceScanQR")
      internal static let iconServiceScheduler = ImageAsset(name: "iconServiceScheduler")
      internal static let iconServiceSecurityCenter = ImageAsset(name: "iconServiceSecurityCenter")
      internal static let iconServiceSendMoney = ImageAsset(name: "iconServiceSendMoney")
      internal static let iconServiceShop = ImageAsset(name: "iconServiceShop")
      internal static let iconServiceSparkHackathon = ImageAsset(name: "iconServiceSparkHackathon")
      internal static let iconServiceStocks = ImageAsset(name: "iconServiceStocks")
      internal static let iconServiceStocksGame = ImageAsset(name: "iconServiceStocksGame")
      internal static let iconServiceStream = ImageAsset(name: "iconServiceStream")
      internal static let iconServiceTimeDeposit = ImageAsset(name: "iconServiceTimeDeposit")
      internal static let iconServiceTrain = ImageAsset(name: "iconServiceTrain")
      internal static let iconServiceTravel = ImageAsset(name: "iconServiceTravel")
      internal static let iconServiceUSStocks = ImageAsset(name: "iconServiceUSStocks")
      internal static let iconServiceVouchers = ImageAsset(name: "iconServiceVouchers")
      internal static let iconServiceWallet = ImageAsset(name: "iconServiceWallet")
      internal static let iconServicesActionAbout = ImageAsset(name: "iconServicesActionAbout")
      internal static let iconServicesActionChat = ImageAsset(name: "iconServicesActionChat")
      internal static let iconServicesActionHelp = ImageAsset(name: "iconServicesActionHelp")
      internal static let iconServicesActionProfile = ImageAsset(name: "iconServicesActionProfile")
      internal static let iconServicesActionSecurityCenter = ImageAsset(name: "iconServicesActionSecurityCenter")
      internal static let iconServicesActionSettings = ImageAsset(name: "iconServicesActionSettings")
      internal static let iconSubtitleRewards = ImageAsset(name: "iconSubtitleRewards")
      internal static let imageStatusNew = ImageAsset(name: "imageStatusNew")
    }
    internal enum Settings {
      internal static let iconChangeNumber = ImageAsset(name: "iconChangeNumber")
      internal static let iconChangePassword = ImageAsset(name: "iconChangePassword")
      internal static let iconCloseAccount = ImageAsset(name: "iconCloseAccount")
      internal static let iconData = ImageAsset(name: "iconData")
      internal static let iconFaceId = ImageAsset(name: "iconFaceId")
      internal static let iconLicenses = ImageAsset(name: "iconLicenses")
      internal static let iconManageNotifications = ImageAsset(name: "iconManageNotifications")
      internal static let iconPrivacy = ImageAsset(name: "iconPrivacy")
      internal static let iconRecovery = ImageAsset(name: "iconRecovery")
      internal static let iconShowName = ImageAsset(name: "iconShowName")
      internal static let iconTerms = ImageAsset(name: "iconTerms")
      internal static let iconTouchId = ImageAsset(name: "iconTouchId")
    }
    internal enum Shop {
      internal static let serviceHistory = ImageAsset(name: "Service_History")
      internal static let serviceMerchants = ImageAsset(name: "Service_Merchants")
      internal static let iconUser = ImageAsset(name: "iconUser")
      internal static let iconUserGray = ImageAsset(name: "iconUserGray")
      internal static let noResultsIcon = ImageAsset(name: "noResultsIcon")
    }
    internal enum Tab {
      internal static let iconDashboardActive = ImageAsset(name: "iconDashboardActive")
      internal static let iconDashboardInactive = ImageAsset(name: "iconDashboardInactive")
      internal static let iconFreeze = ImageAsset(name: "iconFreeze")
      internal static let iconFreezeLoading = ImageAsset(name: "iconFreezeLoading")
      internal static let iconFreezeWarning = ImageAsset(name: "iconFreezeWarning")
      internal static let iconScannerActive = ImageAsset(name: "iconScannerActive")
      internal static let iconScannerInactive = ImageAsset(name: "iconScannerInactive")
      internal static let iconServicesActive = ImageAsset(name: "iconServicesActive")
      internal static let iconServicesInactive = ImageAsset(name: "iconServicesInactive")
      internal static let iconUnfreeze = ImageAsset(name: "iconUnfreeze")
    }
    internal enum Travel {
      internal static let iconGallery = ImageAsset(name: "iconGallery")
      internal static let iconPlane = ImageAsset(name: "iconPlane")
      internal static let iconTicketBoat = ImageAsset(name: "iconTicketBoat")
      internal static let iconTicketBus = ImageAsset(name: "iconTicketBus")
      internal static let iconTicketRoute = ImageAsset(name: "iconTicketRoute")
      internal static let iconTicketTrain = ImageAsset(name: "iconTicketTrain")
      internal static let iconToll = ImageAsset(name: "iconToll")
      internal static let logoBeep = ImageAsset(name: "logoBeep")
    }
    internal enum Updater {
      internal static let imageUpdater = ImageAsset(name: "imageUpdater")
    }
    internal enum Vouchers {
      internal static let detailsVoucherPlaceholder = ImageAsset(name: "detailsVoucherPlaceholder")
      internal static let voucherAqua = ImageAsset(name: "voucherAqua")
    }
  }
}
// swiftlint:enable identifier_name line_length nesting type_body_length type_name

// MARK: - Implementation Details

internal struct ImageAsset {
  internal fileprivate(set) var name: String

  #if os(macOS)
  internal typealias Image = NSImage
  #elseif os(iOS) || os(tvOS) || os(watchOS)
  internal typealias Image = UIImage
  #endif

  @available(iOS 8.0, tvOS 9.0, watchOS 2.0, macOS 10.7, *)
  internal var image: Image {
    let bundle = BundleToken.bundle
    #if os(iOS) || os(tvOS)
    let image = Image(named: name, in: bundle, compatibleWith: nil)
    #elseif os(macOS)
    let name = NSImage.Name(self.name)
    let image = (bundle == .main) ? NSImage(named: name) : bundle.image(forResource: name)
    #elseif os(watchOS)
    let image = Image(named: name)
    #endif
    guard let result = image else {
      fatalError("Unable to load image asset named \(name).")
    }
    return result
  }

  #if os(iOS) || os(tvOS)
  @available(iOS 8.0, tvOS 9.0, *)
  internal func image(compatibleWith traitCollection: UITraitCollection) -> Image {
    let bundle = BundleToken.bundle
    guard let result = Image(named: name, in: bundle, compatibleWith: traitCollection) else {
      fatalError("Unable to load image asset named \(name).")
    }
    return result
  }
  #endif
}

internal extension ImageAsset.Image {
  @available(iOS 8.0, tvOS 9.0, watchOS 2.0, *)
  @available(macOS, deprecated,
    message: "This initializer is unsafe on macOS, please use the ImageAsset.image property")
  convenience init!(asset: ImageAsset) {
    #if os(iOS) || os(tvOS)
    let bundle = BundleToken.bundle
    self.init(named: asset.name, in: bundle, compatibleWith: nil)
    #elseif os(macOS)
    self.init(named: NSImage.Name(asset.name))
    #elseif os(watchOS)
    self.init(named: asset.name)
    #endif
  }
}

// swiftlint:disable convenience_type
private final class BundleToken {
  static let bundle: Bundle = {
    #if SWIFT_PACKAGE
    return Bundle.module
    #else
    return Bundle(for: BundleToken.self)
    #endif
  }()
}
// swiftlint:enable convenience_type
