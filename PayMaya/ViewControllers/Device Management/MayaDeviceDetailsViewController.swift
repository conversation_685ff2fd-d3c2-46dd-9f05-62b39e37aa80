//
//  MayaDeviceDetailsViewController.swift
//  PayMaya
//
//  Created by <PERSON> on 2/6/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Injector
import MayaFormatterManager
import UIKit

protocol MayaDeviceDetailsViewControllerDelegate: AnyObject {
    func didTapChangePassword(_ viewController: MayaDeviceDetailsViewController)
}

class MayaDeviceDetailsViewController: ViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    @IBOutlet private weak var deviceNameLabel: UILabel!
    @IBOutlet private weak var locationLabel: UILabel!
    @IBOutlet private weak var lastLoginLabel: UILabel!
    @IBOutlet private weak var brandLabel: UILabel!
    @IBOutlet private weak var osVersionLabel: UILabel!
    @IBOutlet private weak var ipAddressLabel: UILabel!

    weak var delegate: MayaDeviceDetailsViewControllerDelegate?

    @IBAction func didTapGetHelp() {
        guard let url = Constants.WebView.loginHistoryDetails.url else { return }

        analyticsService.logMayaEvents(
            .tap(Analytics.DeviceManagement.Details.item),
            attributes: [.button: AnalyticsConstants.Screen.DeviceManagement.getHelp.rawValue])

        routeActionDelegate?.didRequestWebView(self, with: url, willPresent: true, completion: nil)
    }

    @IBAction func didTapChangePassword() {
        analyticsService.logMayaEvents(
            .tap(Analytics.DeviceManagement.Details.item),
            attributes: [.button: AnalyticsConstants.Screen.DeviceManagement.changePassword.rawValue])

        delegate?.didTapChangePassword(self)
    }

    override func didTapBack(_ sender: Any) {
        super.didTapBack(sender)
        analyticsService.logMayaEvents(
            .tap(Analytics.DeviceManagement.Details.item),
            attributes: [.button: AnalyticsConstants.Screen.DeviceManagement.back.rawValue])
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.DeviceManagement.Details.exit)
        super.viewWillAppear(animated)
        analyticsService.logMayaEvents(.appear(Analytics.DeviceManagement.Details()))
    }

    override var shouldUseMayaNavigationBar: Bool { true }
    override var navigationBarBackgroundColor: UIColor { .grey1 }
}

extension MayaDeviceDetailsViewController: ViewModelBindable {
    func binding(_ viewModel: MayaDeviceDataModel?) {
        guard let viewModel else { return }
        deviceNameLabel.text = viewModel.title
        locationLabel.text = viewModel.location
        lastLoginLabel.text = viewModel.lastLogin.format(with: .manageDeviceDate, locale: "en_US_POSIX")
        brandLabel.text = viewModel.brand
        osVersionLabel.text = viewModel.osVersion
        ipAddressLabel.text = viewModel.ipAddress
    }
}
