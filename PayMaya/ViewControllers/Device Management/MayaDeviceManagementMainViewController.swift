//
//  MayaDeviceManagementMainViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> Rabago on 2/6/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Injector
import ReactiveSwift
import UIKit

protocol MayaDeviceManagementMainViewControllerDelegate: AnyObject {
    func didSelectDevice(
        _ viewController: MayaDeviceManagementMainViewController,
        dataModel: MayaDeviceDataModel)
    func didTapUpdateEmailButton(_ viewController: MayaDeviceManagementMainViewController)
}

final class MayaDeviceManagementMainViewController: ViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    @IBOutlet private weak var helpBarButtonItem: UIBarButtonItem!
    @IBOutlet private weak var tableView: UITableView!
    @IBOutlet private weak var tableHeaderView: MayaDeviceTableHeaderView!
    @IBOutlet private weak var errorView: MayaDefaultErrorView!

    @IBAction func didTapHelpBarButtonItem(_ sender: UIBarButtonItem) {
        guard let url = Constants.WebView.loginHistoryMain.url else { return }

        analyticsService.logMayaEvents(
            .tap(Analytics.DeviceManagement.List.item),
            attributes: [.button: AnalyticsConstants.Screen.DeviceManagement.getHelp.rawValue])

        routeActionDelegate?.didRequestWebView(self, with: url, willPresent: true, completion: nil)
    }

    @IBAction func didTapUpdateEmailButtonItem(_ sender: UIButton) {
        delegate?.didTapUpdateEmailButton(self)
        analyticsService.logMayaEvents(
            .tap(Analytics.DeviceManagement.List.item),
            attributes: [.button: AnalyticsConstants.Screen.DeviceManagement.updateEmail.rawValue])
    }

    override var shouldUseMayaNavigationBar: Bool { return true }
    override var navigationBarBackgroundColor: UIColor { return .clear }
    override var mayaNavigationBarScrollView: UIScrollView? { return tableView }
    override var titleContentOffset: CGFloat {
        switch viewState {
        case .loaded: return tableHeaderView.titleContentOffset
        default: return -1 // Force show navigation title while content is not yet displayed
        }
    }

    weak var delegate: MayaDeviceManagementMainViewControllerDelegate?

    override func viewDidLoad() {
        super.viewDidLoad()
        setupTableView()
        tableView.layoutIfNeeded()
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.DeviceManagement.List.exit)
        super.viewWillAppear(animated)
        analyticsService.logMayaEvents(.appear(Analytics.DeviceManagement.List()))
    }

    override func didTapBack(_ sender: Any) {
        super.didTapBack(sender)
        analyticsService.logMayaEvents(
            .tap(Analytics.DeviceManagement.List.item),
            attributes: [.button: AnalyticsConstants.Screen.DeviceManagement.back.rawValue])
    }
}

private extension MayaDeviceManagementMainViewController {
    func setupTableHeaderView() {
        guard let viewModel else { return }
        tableHeaderView.setSubtitle(to: viewModel.tableHeaderSubtitle)
    }

    func setupTableView() {
        setupTableHeaderView()
        tableView.registerCell(of: MayaDeviceTableViewCell.self)
        tableView.registerHeaderFooter(of: MayaDeviceTableSectionHeaderView.self)
        tableView.tableHeaderView = tableHeaderView
    }

    func hideHelpBarButtonItem(_ isHidden: Bool) {
        helpBarButtonItem.isEnabled = !isHidden
        helpBarButtonItem.tintColor = isHidden ? .clear : .black
    }

    func displayLoading() {
        showLoadingView(shouldPinToSuperview: true)
        navigationBarTitleView?.shouldShowTitle(true)
        hideHelpBarButtonItem(true)
    }

    func displayContent() {
        showContentView()
        navigationBarTitleView?.shouldShowTitle(titleContentOffset < tableView.contentOffset.y)
        hideHelpBarButtonItem(false)
    }

    func displayError(viewModel: MayaDeviceManagementErrorViewModel) {
        errorView.bind(
            image: viewModel.image,
            imageSize: CGSize(width: 200, height: 200),
            title: viewModel.title,
            description: viewModel.description)
        errorView.actionButton.isHidden = viewModel.hideActionButton

        showErrorView(shouldPinToSuperview: true)
        navigationBarTitleView?.shouldShowTitle(true)
        hideHelpBarButtonItem(true)
    }
}

extension MayaDeviceManagementMainViewController: BaseLoadingViewControllerProtocol {
    var loadingType: LoadingType { return .inlineLoader(message: .empty) }
    var baseContainerView: UIView! { return view }
    var baseContentView: UIView! { return tableView }
    var baseErrorView: UIView! {
        let errorView = errorView
        errorView?.delegate = self
        errorView?.frame = view.bounds
        return errorView
    }
}

extension MayaDeviceManagementMainViewController: ViewModelBindable {
    func binding(_ viewModel: MayaDeviceManagementMainViewModelProtocol?) {
        guard let viewModel else { return }

        viewWillAppearSignal
            .observe(on: UIScheduler())
            .observeValues { _ in
                viewModel.getDevices()
            }?.addToDisposeBag(disposeBag)

        tableView.reactive.reloadData <~
            viewModel.deviceGroupsProperty.producer.skipRepeats().map { _ in }

        viewModel.getDevicesAction.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                guard let self, isExecuting else { return }
                displayLoading()
            }?.addToDisposeBag(disposeBag)

        viewModel.getDevicesAction.values.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self else { return }
                displayContent()
                analyticsService.logMayaEvents(.success(Analytics.DeviceManagement.List()))
            }?.addToDisposeBag(disposeBag)

        viewModel.getDevicesAction.errors.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self else { return }

                let errorViewModel = viewModel.generateErrorViewModel(for: error)
                displayError(viewModel: errorViewModel)

                if error.type == .noDeviceLoadedError {
                    analyticsService.logMayaEvents(.success(Analytics.DeviceManagement.List()))
                } else {
                    analyticsService.logMayaEvents(
                        .failure(Analytics.DeviceManagement.List()),
                        attributes: [
                            .errorCode: (error.viewModel?.error?.code).flatMap(String.init),
                            .errorMessage: errorViewModel.description
                        ].discardNil())
                }
            }?.addToDisposeBag(disposeBag)
    }
}

extension MayaDeviceManagementMainViewController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return viewModel?.getNumberOfDeviceGroups() ?? 0
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel?.getNumberOfDevices(inGroup: section) ?? 0
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard
            let cell = tableView.dequeueReusableCell(
                withIdentifier: MayaDeviceTableViewCell.defaultReuseIdentifier,
                for: indexPath
            ) as? MayaDeviceTableViewCell,
            let dataModel = viewModel?.getDevice(inGroup: indexPath.section, atIndex: indexPath.row),
            let position = viewModel?.getPositionOfDevice(inGroup: indexPath.section, atIndex: indexPath.row)
        else {
            return UITableViewCell()
        }

        cell.bind(dataModel: dataModel, position: position)
        return cell
    }
}

extension MayaDeviceManagementMainViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        guard
            let headerView = tableView.dequeueReusableHeaderFooterView(
                withIdentifier: MayaDeviceTableSectionHeaderView.defaultReuseIdentifier
            ) as? MayaDeviceTableSectionHeaderView,
            let dataModel = viewModel?.getDeviceGroup(atIndex: section)
        else {
            return nil
        }

        headerView.bind(dataModel: dataModel)
        return headerView
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        guard let dataModel = viewModel?.getDevice(inGroup: indexPath.section, atIndex: indexPath.row) else {
            return
        }

        analyticsService.logMayaEvents(
            .tap(Analytics.DeviceManagement.List.device),
            attributes: [
                .position: String(indexPath.section == 0 ? 1 : indexPath.row + 2),
                .deviceType: dataModel.platform,
                .os: dataModel.osVersion
            ].discardNil())

        delegate?.didSelectDevice(self, dataModel: dataModel)
    }
}

extension MayaDeviceManagementMainViewController: MayaDefaultErrorViewDelegate {
    func didTapRetry(_ view: MayaDefaultErrorView) {
        viewModel?.getDevices()
    }
}
