//
//  MayaSendMoneyViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 6/30/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import Injector
import MayaFormatterManager
import ReactiveCocoa
import ReactiveSwift
import UIKit

private let progressBarText = "1/2"
private let dropDownPadding: CGFloat = 16
private let maxRecipientCount: Int = 98

protocol SendMoneyViewControllerDelegate: AnyObject {
    func didTapViewAllContacts(_ viewController: UIViewController)
    func didTapContinue(_ viewController: UIViewController)
    func didTapPreviewDesign(_ viewController: UIViewController, viewModel: MayaSendMoneyDesignCardPreviewViewModel)
}

class MayaSendMoneyViewController: FormsViewController, MayaProgressBarProtocol, MayaAnalyticsDurationManagerProtocol, AnalyticsProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    typealias AnalyticsSendMoney = AnalyticsConstants.Screen.SendMoney

    @IBOutlet weak var accessoryLabel: UILabel!
    @IBOutlet weak var recipientAccessoryLabel: UILabel!
    @IBOutlet weak var recipientAccountTypeView: UIView!

    @IBOutlet weak var sendMoneyTitleLabel: UILabel!
    @IBOutlet weak var continueButton: MayaButton!

    @IBOutlet weak var includeSignatureCheckbox: MayaCheckBox!
    @IBOutlet weak var signatureLabel: UILabel!

    /// Contacts
    @IBOutlet weak var containerView: UIView!
    @IBOutlet weak var dropDownContainerView: UIView!
    @IBOutlet weak var dropDownTableView: UITableView!

    /// My Favorites Section
    @IBOutlet weak var seeAllButton: UIButton!
    @IBOutlet weak var myFavoritesLabel: UILabel!
    @IBOutlet weak var favoritesCollectionView: UICollectionView!
    @IBOutlet weak var favoritesMaintenanceView: MayaMyFavoritesMaintenanceView!
    @IBOutlet weak var favoritesServerErrorView: MayaMyFavoritesServerErrorView!

    @IBOutlet weak var recipientView: MayaValidationFieldView! {
        didSet {
            recipientView.setTextFieldProperties(keyboardType: .emailAddress)
        }
    }
    @IBOutlet weak var amountView: MayaValidationFieldView! {
        didSet {
            amountView.setTextFieldProperties(keyboardType: .decimalPad)
        }
    }
    @IBOutlet weak var noteView: MayaValidationFieldView! {
        didSet {
            noteView.setTextFieldProperties(capitalizationType: .sentences)
        }
    }

    /// Personalization Section
    @IBOutlet weak var personalizationStackView: UIStackView!
    @IBOutlet weak var personalizeMenuBar: MayaMenuBar!

    @IBOutlet weak var addGIFStackView: UIStackView!
    @IBOutlet weak var addGIFView: UIView!
    @IBOutlet weak var previewGIFView: UIView!

    @IBOutlet weak var giphyMediaContainerView: View!
    @IBOutlet weak var selectedGIFImageView: UIImageView!
    @IBOutlet weak var errorPreviewGIFView: UIView!

    @IBOutlet weak var addThemeStackView: UIStackView!
    @IBOutlet weak var themeSelectionView: SendMoneyThemesSelectionView!
    @IBOutlet weak var themeSelectionContainerView: UIView!
    @IBOutlet weak var failedLoadThemeView: View!
    @IBOutlet weak var themeSelectionPreviewButton: MayaButton!

    @Inject private var formatter: MayaFormatterManager

    var recipientTextField: TextField {
        return recipientView.textField
    }

    var amountTextField: TextField {
        return amountView.textField
    }

    var noteTextField: TextField {
        return noteView.textField
    }

    var analyticsSourcePage: AnalyticsSendMoney.Page = .dashboard

    // This variable is set as flag to indicate that the next time the VC appears,
    // it will be from the Send Money Confirm Screen.
    var didAlreadyLoad: Bool = false

    /// Maya NavigationBar properties
    override var mayaNavigationBarScrollView: UIScrollView? {
        return scrollView
    }

    override var titleContentOffset: CGFloat {
        return sendMoneyTitleLabel.frame.minY + sendMoneyTitleLabel.frame.size.height
    }

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    var segment: Int = 1

    /// Temporary delegate, once the flow is completed to Maya 2.0, should have own delegate
    weak var delegate: SendMoneyViewControllerDelegate?

    override func viewDidLoad() {
        super.viewDidLoad()
        setRightButtonItemTitle(progressBarText)
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.SendMoney())
        super.viewWillAppear(animated)

        guard let viewModel = viewModel else { return }
        viewModel.balanceAction.apply().start()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        logAppear()
    }

    override func didTapBack(_ sender: Any) {
        super.didTapBack(sender)
        logButtonTapped(sourePage: .form, button: .back, destinationPage: analyticsSourcePage != .confirmation ? analyticsSourcePage : .dashboard)
    }

    @IBAction func didTapSeeAllButton(_ sender: Any) {
        guard let favoritesDeepLink = FavoriteType.moneysend.deepLinkURL else { return }
        logButtonTapped(sourePage: analyticsSourcePage, button: .seeAll, destinationPage: .favorite)
        routeActionDelegate?.didRequestStartDeepLink(self, with: favoritesDeepLink, completion: nil)
    }

    @IBAction func didTapIncludeSignature(_ sender: Any) {
        let currentValue = includeSignatureCheckbox.status
        let includeSignature = !currentValue
        includeSignatureCheckbox.setStatus(includeSignature)
        viewModel?.includeSignatureProperty.value = includeSignature
    }

    @IBAction func didTapPreviewTheme(_ sender: Any) {
        showPreviewTheme()
    }

    @IBAction func didTapRemoveGIF(_ sender: Any) {
        viewModel?.giphyMediaId.value = nil
        previewGIFView.isHidden = true
        selectedGIFImageView.image = nil
        addGIFView.isHidden = false
    }

    @IBAction func didTapRetryLoadThemes(_ sender: Any) {
        guard let viewModel = viewModel else { return }
        themeSelectionContainerView.safeSetHidden(false)
        failedLoadThemeView.safeSetHidden(true)
        themeSelectionView.reloadThemes()

        viewModel.retryLoadDecorations()
    }
}

extension MayaSendMoneyViewController {
    func setupFavoritesView() {
        favoritesCollectionView.register(UINib.init(nibName: String(describing: MayaMyFavoritesEmptyCollectionViewCell.self), bundle: nil), forCellWithReuseIdentifier: MayaMyFavoritesEmptyCollectionViewCell.reuseIdentifier)
        favoritesCollectionView.register(UINib.init(nibName: String(describing: MayaMyFavoritesSendMoneyCollectionViewCell.self), bundle: nil), forCellWithReuseIdentifier: MayaMyFavoritesSendMoneyCollectionViewCell.reuseIdentifier)
        if let flowLayout = favoritesCollectionView.collectionViewLayout as? UICollectionViewFlowLayout {
            flowLayout.minimumLineSpacing = 8
        }
        favoritesServerErrorView.delegate = self
    }

    func autoFillForm(_ formDetails: SendMoneyFormDetails) {
        guard let viewModel = viewModel else { return }
        resetForm()

        let contactNumber = viewModel.findMatchedMobileNumber(formDetails.recipient.getMobileNumberSearchString())
        if let contactNumber = contactNumber {
            recipientTextField.text = "\(contactNumber.fullName) (\(contactNumber.number))"
        } else {
            recipientTextField.text = formDetails.recipient
        }

        viewModel.recipient.mutableProperty.value = formDetails.recipient
        viewModel.selectedContactNumberProperty.value = contactNumber

        if let amount = formDetails.amount {
            amountTextField.text = formatter.number.currencyPesoStringValue(string: amount)
            viewModel.amountProperty.mutableProperty.value = formatter.number.currencyStringValue(string: amount)
        }

        if let message = formDetails.message {
            noteTextField.text = message
            viewModel.message.value = message
        }
    }

    func resetForm() {
        recipientTextField.clearValues()
        amountTextField.clearValues()
        noteTextField.clearValues()
        viewModel?.includeSignatureProperty.value = true
    }

    func showDropDown(fieldView: MayaValidationFieldView) {
        var tableHeight: CGFloat = 0
        let defaultTableViewHeight: CGFloat = 250
        tableHeight = dropDownTableView.contentSize.height >= defaultTableViewHeight ? defaultTableViewHeight : dropDownTableView.contentSize.height

        if tableHeight < dropDownTableView.contentSize.height {
            tableHeight -= 10
        }

        var tableViewFrame = CGRect(x: 0, y: 0, width: fieldView.frame.size.width + (dropDownPadding * 2), height: tableHeight)
        tableViewFrame.origin = fieldView.convert(tableViewFrame.origin, to: containerView)

        tableViewFrame.origin.x -= dropDownPadding
        tableViewFrame.origin.y += fieldView.frame.size.height

        dropDownContainerView?.frame = tableViewFrame

        if dropDownContainerView.superview == nil {
            containerView.addSubview(dropDownContainerView)
        }
    }

    func hideDropDown(fieldView: MayaValidationFieldView) {
        var tableViewFrame = CGRect(x: fieldView.frame.origin.x, y: fieldView.frame.origin.y, width: 0, height: 0)
        tableViewFrame.origin = fieldView.convert(tableViewFrame.origin, to: containerView)
        tableViewFrame.origin.x -= dropDownPadding
        tableViewFrame.origin.y += fieldView.textField.frame.size.height

        UIView.animate(withDuration: 0.2, animations: { [weak self] in
            self?.dropDownContainerView?.frame = tableViewFrame
        })
    }

    func showPreviewTheme() {
        guard let previewViewModel = viewModel?.getSendMoneyDesignCardPreviewViewModel() else { return }

        logButtonTapped(sourePage: analyticsSourcePage, button: .preview, destinationPage: .preview)

        analyticsUtils.logMayaEvents(.tap(Analytics.SendMoney.preview), attributes: [.type: DesignCardType.theme.rawValue, .themeId: previewViewModel.selectedDecoration.value.id])
        delegate?.didTapPreviewDesign(self, viewModel: previewViewModel)
    }

    func toggleGifPreviewErrorViewVisibility(shouldHide: Bool) {
        if shouldHide {
            selectedGIFImageView.image = nil
        }
        errorPreviewGIFView.isHidden = shouldHide
    }
}

// MARK: - ViewControllerBindable Methods
extension MayaSendMoneyViewController: ViewControllerBindable {
    func binding() {
        guard let viewModel = viewModel else { return }

        recipientTextField.maxCount = maxRecipientCount
        accessoryLabel.text = viewModel.availableBalanceText

        formFields = [recipientView, amountView, noteView]
        setKeyboardReturnTypeOfTextFields()

        /// Default is to include signature in note
        includeSignatureCheckbox.setStatus(true)
        signatureLabel.text = String(format: "From %@", viewModel.senderFirstName)

        lastFormFieldCallback = { [weak self] in
            guard let self = self else { return }
            self.view.endEditing(true)
        }

        setupFavoritesView()

        themeSelectionView.delegate = self

        continueButton.reactive.pressed = CocoaAction(viewModel.createTransferAction) { [weak self] _ in
            guard let self = self else { return }
            logButtonTapped(sourePage: analyticsSourcePage, button: .continueButton, destinationPage: .confirmation)
            self.analyticsUtils.logMayaEvents(.tap(Analytics.SendMoney.continue))
            self.view.endEditing(true)
        }
    }
}

// MARK: - ViewModelBindable Methods
extension MayaSendMoneyViewController: ViewModelBindable {
    func binding(_ viewModel: MayaSendMoneyViewModelProtocol?) {
        guard let viewModel = viewModel else { return }

        recipientView.firstActionImageHandler = { [weak self] in
            guard let self = self else { return }
            logButtonTapped(sourePage: analyticsSourcePage, button: .contacts, destinationPage: .contacts)
            self.delegate?.didTapViewAllContacts(self)
        }

        viewModel.amountProperty.mutableProperty <~ amountTextField.reactive.continuousTextValues
                .skip(first: 1)
                .map({ amount in
                    return amount.filter("0123456789.".contains)
                })
        viewModel.recipientProperty <~ recipientTextField.reactive.continuousTextValues.skip(first: 1).skipRepeats().throttle(0.25, on: QueueScheduler.main)
        viewModel.message <~ noteTextField.reactive.continuousTextValues.skip(first: 1)
            .map({ messageText -> String? in
            if messageText.isEmpty {
                return nil
            }
            return messageText
        })

        viewModel.shouldShowContactsProperty <~ recipientTextField.reactive.controlEvents(.editingDidEnd).delay(0.25, on: QueueScheduler.main).map { _ in return false }

        amountView.bindFieldProperty(viewModel.amountProperty)
        recipientView.bindFieldProperty(viewModel.recipient)

        continueButton.reactive.isEnabled <~ viewModel.validatingProperty

        amountView.inputStateProperty.signal.observeValues { [weak self] state in
            guard let self = self else { return }

            switch state {
            case .error:
                self.accessoryLabel.text = ""
            default:
                self.accessoryLabel.text = viewModel.availableBalanceText
            }
        }?.addToDisposeBag(disposeBag)

        viewModel.selectedContactNumberProperty
            .signal
            .observe(on: UIScheduler())
            .skipNil()
            .observeValues { [weak self] selectedContactNumber in
            guard let self = self else { return }
            let contactDetails = "\(selectedContactNumber.fullName) (\(selectedContactNumber.number))"
            self.recipientTextField.text = contactDetails
            }?.addToDisposeBag(disposeBag)

        viewModel.recipientAccountTypeProperty
            .signal
            .skipRepeats()
            .observe(on: UIScheduler())
            .observeValues { [weak self] type in
                guard
                    let self = self,
                    let accountType = type
                else {
                    self?.personalizationStackView.safeSetHidden(false)
                    self?.recipientAccountTypeView.safeSetHidden(true)
                    return
                }

                self.recipientAccessoryLabel.text = accountType.mayaDisplay
                self.recipientAccountTypeView.safeSetHidden(false)

                let isMayaCenterAccount = accountType == .smartMoney
                self.personalizationStackView.safeSetHidden(isMayaCenterAccount)
            }?.addToDisposeBag(disposeBag)

        viewModel.createTransferAction.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                isExecuting ? self?.showMayaLoader() : self?.hideMayaLoader()
            }?.addToDisposeBag(disposeBag)

        viewModel.createTransferAction.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self = self else { return }

                var reason = ""
                let accountType = viewModel.recipientAccountTypeProperty.value ?? .payMaya
                if case .sessionTimeout(let message) = error.type {
                    reason = message
                } else if let errorViewModel = error.viewModel {
                    reason = errorViewModel.message
                    self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
                }

                logError(reason: reason, errorCode: "\(error.viewModel?.error?.code)", accountType: accountType.rawValue)
            }?.addToDisposeBag(disposeBag)

        viewModel.createTransferAction.values
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self = self else { return }
                let accountType = viewModel.recipientAccountTypeProperty.value ?? .payMaya
                self.analyticsUtils.logMayaEvents(.success(Analytics.SendMoney.create), attributes: [.type: accountType.rawValue])
                self.delegate?.didTapContinue(self)
            }?.addToDisposeBag(disposeBag)

        viewModel.requestMoneyDetailsProperty
            .signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] details in
                guard let self = self, let requestDetails = details else { return }
                let formDetails = SendMoneyFormDetails(requestDetails)
                self.autoFillForm(formDetails)
            }?.addToDisposeBag(disposeBag)

        viewModel.shouldShowContactsProperty
            .signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] shouldShow in
            guard let self = self else { return }
            self.dropDownTableView.reloadData()
            self.dropDownTableView.layoutIfNeeded()
            if shouldShow {
                self.showDropDown(fieldView: self.recipientView)
            } else {
                self.hideDropDown(fieldView: self.recipientView)
            }
            }?.addToDisposeBag(disposeBag)

        setupFavoritesBinding()
        setupPersonalizationBindings()

        viewModel.balanceAction.apply().start()
    }

    private func setupFavoritesBinding() {
        guard let viewModel = viewModel else { return }
        /// My Favorites
        viewModel.favoriteItemsProperty.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                self?.favoritesCollectionView.reloadData()
            }?.addToDisposeBag(disposeBag)

        viewModel.getFavoritesAction.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                guard let self = self else { return }
                if isExecuting {
                    self.favoritesCollectionView.reloadData()
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.getFavoritesAction.completed.signal
            .observe(on: UIScheduler())
            .observeValues { _ in
                if viewModel.favoriteStateProperty.value == .success {
                    viewModel.favoriteLimitAction.apply().start()
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.favoriteDetailsAction.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            executing ? self.showMayaLoader() : self.hideMayaLoader()
        }?.addToDisposeBag(disposeBag)

        viewModel.favoriteDetailsAction.values.observe(on: UIScheduler()).observeValues { [weak self] favoriteItem in
            guard let self = self, let template = favoriteItem.template as? SendMoneyFavoriteTemplate else { return }
            viewModel.selectedFavoriteItemProperty.value = favoriteItem
            let formDetails = SendMoneyFormDetails(template)
            self.autoFillForm(formDetails)
        }?.addToDisposeBag(disposeBag)

        viewModel.favoriteDetailsAction.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self else { return }
            if let errorViewModel = error.viewModel {
                self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
            }
        }?.addToDisposeBag(disposeBag)

        viewModel.favoriteLimitProperty.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] favoriteLimit in
                guard let self = self else { return }
                if let sendMoneyLimit = favoriteLimit?.moneysend {
                    self.myFavoritesLabel.text = "\(L10n.MyFavorites.Title.uppercased) (\(sendMoneyLimit.total)/\(sendMoneyLimit.limit))"
                } else {
                    self.myFavoritesLabel.text = L10n.MyFavorites.Title.uppercased
                }
            }?.addToDisposeBag(disposeBag)

        let handleFavoriteState = { [weak self] in
            guard let self = self,
                  let favoriteState = viewModel.favoriteStateProperty.value
            else {
                return
            }

            switch favoriteState {
            case .maintenance:
                self.seeAllButton.isHidden = true
                self.favoritesCollectionView.isHidden = true
                self.favoritesServerErrorView.isHidden = true
                self.favoritesMaintenanceView.isHidden = false
            case .success, .executing:
                self.seeAllButton.isHidden = false
                self.favoritesCollectionView.isHidden = false
                self.favoritesServerErrorView.isHidden = true
                self.favoritesMaintenanceView.isHidden = true
            case .empty:
                self.seeAllButton.isHidden = true
                self.favoritesCollectionView.isHidden = false
                self.favoritesServerErrorView.isHidden = true
                self.favoritesMaintenanceView.isHidden = true
            case .error:
                self.seeAllButton.isHidden = true
                self.favoritesCollectionView.isHidden = true
                self.favoritesServerErrorView.isHidden = false
                self.favoritesMaintenanceView.isHidden = true
            }
        }

        handleFavoriteState()
        viewModel.favoriteStateProperty.signal
            .observe(on: UIScheduler())
            .observeValues { _ in
                handleFavoriteState()
            }?.addToDisposeBag(disposeBag)

        if viewModel.favoriteStateProperty.value != .maintenance {
            viewModel.getFavoritesAction.apply().start()
        }
    }

    private func setupPersonalizationBindings() {
        guard let viewModel = viewModel else { return }

        viewModel.availableDecorations.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] decorations in
                guard let self = self else { return }

                if let themes = decorations,
                   !themes.isEmpty {
                    self.themeSelectionContainerView.safeSetHidden(false)
                    self.failedLoadThemeView.safeSetHidden(true)

                    self.themeSelectionView.selectedTheme = nil
                    self.themeSelectionView.reloadThemes(themes)
                } else {
                    self.themeSelectionContainerView.safeSetHidden(true)
                    self.failedLoadThemeView.safeSetHidden(false)
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.selectedDecoration.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] theme in
                guard
                    let self = self,
                    let selectedTheme = theme
                else { return }
                self.themeSelectionPreviewButton.isHidden = false
                self.themeSelectionView.reloadSelectedTheme(selectedTheme)
            }?.addToDisposeBag(disposeBag)

        self.addGIFView.isHidden = true
        self.previewGIFView.isHidden = true
        self.themeSelectionView.isHidden = false
        self.themeSelectionPreviewButton.isHidden = true
        self.addThemeStackView.isHidden = false
        self.view.layoutIfNeeded()

        viewModel.getDecorationsAction.apply().start()
    }
}

// MARK: - MayaMyFavoritesServerErrorViewDelegate Methods
extension MayaSendMoneyViewController: MayaMyFavoritesServerErrorViewDelegate {
    func didTapTryAgain(_ view: MayaMyFavoritesServerErrorView) {
        guard let viewModel = viewModel else { return }
        if viewModel.favoriteStateProperty.value != .maintenance {
            viewModel.getFavoritesAction.apply().start()
        }
    }
}

extension MayaSendMoneyViewController: MenuBarDelegate {
    func didSelectMenuView(view: UIView?, at index: Int) {
        let isAddGifMenuActive = index == 0

        addGIFStackView.safeSetHidden(!isAddGifMenuActive)
        addThemeStackView.safeSetHidden(isAddGifMenuActive)

        analyticsUtils.logMayaEvents(.tap(isAddGifMenuActive ? Analytics.SendMoney.gifTab : Analytics.SendMoney.themeTab))

        viewModel?.activePersonalizationProvider.value = isAddGifMenuActive ? .giphy : .paymaya
    }
}

extension MayaSendMoneyViewController: SendMoneyThemesSelectionViewDelegate {
    func didSelectTheme(_ theme: SendMoneyDecoration) {
        analyticsUtils.logMayaEvents(.tap(Analytics.SendMoney.theme), attributes: [.themeId: theme.id])

        logButtonTapped(sourePage: analyticsSourcePage, button: .theme, destinationPage: .form)
        viewModel?.selectedDecoration.value = theme
    }
}

// MARK: - UICollectionViewDelegate, UICollectionViewDataSource, and UICollectionViewDelegateFlowLayout Methods
extension MayaSendMoneyViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        guard let viewModel = viewModel,
              let items = viewModel.favoriteItemsProperty.value,
              !items.isEmpty
        else {
            return 1
        }

        return items.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let viewModel = viewModel,
              let items = viewModel.favoriteItemsProperty.value
        else {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: MayaMyFavoritesSendMoneyCollectionViewCell.reuseIdentifier, for: indexPath) as! MayaMyFavoritesSendMoneyCollectionViewCell
            cell.showAnimation()
            return cell
        }

        if items.isEmpty {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: MayaMyFavoritesEmptyCollectionViewCell.reuseIdentifier, for: indexPath) as! MayaMyFavoritesEmptyCollectionViewCell
            cell.setupType(.moneysend)
            return cell
        } else {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: MayaMyFavoritesSendMoneyCollectionViewCell.reuseIdentifier, for: indexPath) as! MayaMyFavoritesSendMoneyCollectionViewCell
            let item = items[indexPath.row]
            cell.hideAnimation()
            cell.setItemDetails(item)
            return cell
        }
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        guard let viewModel = viewModel,
              let items = viewModel.favoriteItemsProperty.value,
              let selectedItem = items[safeIndex: indexPath.row]
        else {
            return
        }

        if let id = selectedItem.id {
            viewModel.favoriteDetailsAction.apply(id).start()
            logButtonTapped(sourePage: analyticsSourcePage, button: .favorite, destinationPage: .form)
        }
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        guard let items = viewModel?.favoriteItemsProperty.value,
              !items.isEmpty
        else {
            return MayaMyFavoritesEmptyCollectionViewCell.estimatedSize
        }

        return MayaMyFavoritesSendMoneyCollectionViewCell.estimatedSize
    }
}

extension MayaSendMoneyViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel?.suggestedContactNumbersProperty.value.count ?? 0
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let viewModel = self.viewModel, let contactNumber = viewModel.suggestedContactNumbersProperty.value[safeIndex: indexPath.row] else { return UITableViewCell() }
        if let recentContact = viewModel.recentTransferNumber, recentContact.number == contactNumber.number {
            let cell = tableView.dequeueReusableCell(withIdentifier: MayaShopRecentContactsDropDownTableViewCell.defaultReuseIdentifier, for: indexPath) as? MayaShopRecentContactsDropDownTableViewCell
            cell?.bind(contactNumber, shouldColor: true)
            return cell ?? UITableViewCell()
        }
        let cell = tableView.dequeueReusableCell(withIdentifier: MayaShopContactsDropDownTableViewCell.defaultReuseIdentifier, for: indexPath) as? MayaShopContactsDropDownTableViewCell
        cell?.bind(contactNumber)
        return cell ?? UITableViewCell()
    }
}

extension MayaSendMoneyViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        guard let viewModel = self.viewModel, let contactNumber = viewModel.suggestedContactNumbersProperty.value[safeIndex: indexPath.row] else { return }
        viewModel.selectedContactNumberProperty.value = contactNumber

        if let activeFormField = self.activeFormField.value {
            moveToNextField(currentField: activeFormField)
        }
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        guard let viewModel = self.viewModel, let contactNumber = viewModel.suggestedContactNumbersProperty.value[safeIndex: indexPath.row] else { return 0 }
        if let recentContact = viewModel.recentTransferNumber, recentContact.number == contactNumber.number {
            return UITableView.automaticDimension
        }
        return MayaShopContactsDropDownTableViewCell.height
    }
}

// MARK: - UITextFieldDelegate Methods
extension MayaSendMoneyViewController {
    override func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        guard let viewModel = viewModel else { return true }

        if textField == amountTextField {
            let text = viewModel.formatMayaAmount(with: String.unwrappedValue(textField.text).trim(), range: range, and: string)
            amountView.setFormattedAmount(with: text.newValue, offset: text.offset)
            return false
        } else if textField == recipientTextField,
                  string == "",
                  viewModel.selectedContactNumberProperty.value != nil {
            viewModel.selectedContactNumberProperty.value = nil
            recipientTextField.clearValues()
            return false
        }

        return true
    }

    func textFieldDidEndEditing(_ textField: UITextField) {
        if textField == amountTextField,
           String.unwrappedValue(textField.text).isEmpty {
            guard let viewModel = viewModel else { return }
            amountTextField.text = viewModel.defaultAmountValue
            viewModel.amountProperty.mutableProperty.value = viewModel.defaultAmountValue
        }

        guard let viewModel else { return }

        switch textField {
        case amountTextField:
            let isInvalid = !viewModel.amountProperty.isValidProperty.value
            let invalidReason = isInvalid ? viewModel.amountProperty.inlineValidationMessage.replaceAmountWithPlaceholder() : nil

            logTextFieldEvent(inputLabel: .amount, isInvalid: isInvalid, invalidReason: invalidReason)

            if String.unwrappedValue(textField.text).isEmpty {
                amountTextField.text = viewModel.defaultAmountValue
                viewModel.amountProperty.mutableProperty.value = viewModel.defaultAmountValue
            }
        case recipientTextField:
            let isInvalid = recipientTextField.text?.isEmpty ?? true

            logTextFieldEvent(inputLabel: .recipient, isInvalid: isInvalid, invalidReason: nil)
        case noteTextField:
            logTextFieldEvent(inputLabel: .note, isInvalid: false, invalidReason: nil)
        default:
            break
        }
    }
}

// MARK: New Events Analytics Methods
extension MayaSendMoneyViewController {
    func logAppear() {
        guard let viewModel else { return }

        if viewModel.isAppEventV2SendMoneyEnabled {
            var keyAttributes: [AnalyticsAttributeKey: Any] = [
                .sourcePage: analyticsSourcePage.rawValue,
                .hasFavorites: false,
                .numberOfFavorites: "0"
            ]

            if let favoriteRecipient = viewModel.favoriteItemsProperty.value {
                keyAttributes[.hasFavorites] = !favoriteRecipient.isEmpty
                keyAttributes[.numberOfFavorites] = "\(favoriteRecipient.count)"
            }

            analyticsService.logMayaEvents(AnalyticsSendMoney.screen.appear(), attributes: keyAttributes)
        } else {
            analyticsUtils.logMayaAttributableEvent(.appear(Analytics.SendMoney()))
        }
    }

    func logButtonTapped(sourePage: AnalyticsSendMoney.Page, button: AnalyticsSendMoney.Button, destinationPage: AnalyticsSendMoney.Page) {
        guard let viewModel, viewModel.isAppEventV2SendMoneyEnabled else { return }

        let keyAttributes: [AnalyticsAttributeKey: Any] = [
            .sourcePage: sourePage.rawValue,
            .button: button.rawValue,
            .destinationPage: destinationPage.rawValue,
            .hasSignature: includeSignatureCheckbox.status
        ]

        if destinationPage != .form {
            analyticsSourcePage = destinationPage
        }

        analyticsService.logMayaEvents(AnalyticsSendMoney.screen.buttonTapped(), attributes: keyAttributes)
    }

    func logTextFieldEvent(inputLabel: AnalyticsSendMoney.Input, isInvalid: Bool, invalidReason: String?) {
        guard let viewModel, viewModel.isAppEventV2SendMoneyEnabled else { return }

        let keyAttributes: [AnalyticsAttributeKey: Any] = [
            .sourcePage: analyticsSourcePage.rawValue,
            .inputLabel: inputLabel.rawValue,
            .isInvalid: isInvalid,
            .invalidReason: invalidReason,
            .isDisabled: false,
            .isRecipientFilled: !(recipientTextField.text?.isEmpty ?? true),
            .isAmountFilled: !(amountTextField.text?.isEmpty ?? true),
            .isNoteFilled: !(noteTextField.text?.isEmpty ?? true)
        ]

        analyticsService.logMayaEvents(AnalyticsSendMoney.screen.input(), attributes: keyAttributes)
    }

    func logError(reason: String, errorCode: String, accountType: String) {
        guard let viewModel else { return }

        if viewModel.isAppEventV2SendMoneyEnabled {
            let keyAttributes: [AnalyticsAttributeKey: Any] = [
                .sourcePage: analyticsSourcePage.rawValue,
                .errorReason: reason,
                .errorCode: errorCode
            ]

            analyticsService.logMayaEvents(AnalyticsSendMoney.screen.failed(), attributes: keyAttributes)
        } else {
            analyticsUtils.logMayaEvents(.failure(Analytics.SendMoney.create), attributes: [.reason: reason, .type: accountType])
        }
    }
}
