//
//  MayaRequestMoneyFormViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 21/03/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import Foundation
import Glimpse
import ReactiveCocoa
import ReactiveSwift
import UIKit

protocol MayaRequestMoneyFormViewControllerDelegate: AnyObject {
    func didTapGenerate(_ viewController: MayaRequestMoneyFormViewController, qrDetails: QRDetails)
    func didTapAccountLimits(_ viewController: MayaRequestMoneyFormViewController)
}

class MayaRequestMoneyFormViewController: FormsViewController, MayaAnalyticsDurationProtocol, MayaValidationFieldViewAccessoryViewDelegate {
    @IBOutlet weak var amountToggleContainer: MayaRequestMoneyToggleStackView!
    @IBOutlet weak var messageToggleContainer: MayaRequestMoneyToggleStackView!

    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var confirmationView: MayaConfirmationView!

    weak var delegate: MayaRequestMoneyFormViewControllerDelegate?
    private let accountLimitsUrl = Constants.DeepLinkPath.accountLimits.url

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupToggles()
        setupTextViews()
        titleLabel.text = L10n.ReceiveMoney.Form.title
        confirmationView.continueButton.isEnabled = false
        confirmationView.continueButton.setTitle(L10n.ReceiveMoney.Form.button, for: .normal)

        amountToggleContainer.toggle.addTarget(self, action: #selector(didTapAmountSwitch), for: .valueChanged)
        messageToggleContainer.toggle.addTarget(self, action: #selector(didTapMessageSwitch), for: .valueChanged)
        confirmationView.continueButton.addTarget(self, action: #selector(didTapGenerate), for: .touchUpInside)

        scrollView.delegate = self
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.RequestMoneyCustomize())
        super.viewWillAppear(animated)
        navigationBarTitleView?.shouldShowTitle(false)
        // Hide messageToggleContainer since it is not allowed to have custom purpose/message in QRPH
        messageToggleContainer.isHidden = true
    }

    override func setupAccessibilityIdentifiers() {
        navigationItem.leftBarButtonItem?.isAccessibilityElement = true
        navigationItem.leftBarButtonItem?.accessibilityIdentifier = "pmios_request_money_form_back_button"
    }

    @objc
    func didTapGenerate(_ sender: Any) {
        if let viewModel = viewModel, viewModel.isGenerateCodeEnabled.value {
            delegate?.didTapGenerate(self, qrDetails: viewModel.qrDetails)
        }
    }

    @objc
    func didTapAmountSwitch() {
        viewModel?.toggleAmountSwitch()
    }

    @objc
    func didTapMessageSwitch() {
        viewModel?.toggleMessageSwitch()
    }

    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        navigationBarTitleView?.shouldShowTitle(!scrollView.bounds.intersects(titleLabel.frame))
    }
}

private extension MayaRequestMoneyFormViewController {
    func setupToggles() {
        amountToggleContainer.setup(with: Asset.MayaImages.RequestMoney.iconMoney.image,
                                    text: L10n.ReceiveMoney.Form.specifyAmount,
                                    textFieldPlaceholder: L10n.ReceiveMoney.Form.Amount.placeholder,
                                    textFieldHintText: L10n.ReceiveMoney.Form.amount)
        amountToggleContainer.setMessageText(L10n.ReceiveMoney.Maximum.amount)
        amountToggleContainer.amountFieldView.textFieldDelegate = self
        amountToggleContainer.amountFieldView.setTextFieldProperties(keyboardType: .decimalPad)

        amountToggleContainer.amountFieldView.accessoryViewDelegate = self
        messageToggleContainer.setup(with: Asset.MayaImages.RequestMoney.iconMessage.image,
                                     text: L10n.ReceiveMoney.Form.includeMessage,
                                     textFieldPlaceholder: L10n.ReceiveMoney.Form.Message.placeholder,
                                     textFieldHintText: L10n.ReceiveMoney.Form.message)
        messageToggleContainer.setMessageText(viewModel?.messageRemainingText.value ?? "")
        messageToggleContainer.textView.isHidden = true
        messageToggleContainer.amountFieldView.textFieldDelegate = self
        messageToggleContainer.amountFieldView.setTextFieldProperties(keyboardType: .asciiCapable)
    }

    func setupTextViews() {
        let accountLimitsMessage = L10n.Request.Money.Account.Limits.Full.message
        let accountLimitsText = L10n.Request.Money.Account.Limits.text
        let accountLimitsRange = (accountLimitsMessage as NSString).range(of: accountLimitsText)
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineHeightMultiple = 1.2

        let attributedString = NSMutableAttributedString(string: accountLimitsMessage, attributes: [
            .paragraphStyle: paragraphStyle,
            .font: CommonFontFamily.CerebriSansPro.regular.font(size: 12)!,
            .foregroundColor: CommonAsset.MayaColors.Content.contentGrey5.color
        ])

        attributedString.addAttribute(.foregroundColor, value: CommonAsset.MayaColors.Primary.primaryGrownGreen.color, range: accountLimitsRange)
        if let url = accountLimitsUrl {
            attributedString.addAttribute(NSAttributedString.Key.link, value: url, range: accountLimitsRange)
        }

        amountToggleContainer.textView.infoTextView.linkTextAttributes = [.foregroundColor: CommonAsset.MayaColors.Primary.primaryGrownGreen.color]

        amountToggleContainer.textView.setText(attributedString)
        amountToggleContainer.textView.infoTextView.delegate = self
    }
}

// MARK: - ViewControllerBindable methods
extension MayaRequestMoneyFormViewController: ViewControllerBindable {
    func binding() {
        willTriggerActionOnCallBack = false

        setKeyboardReturnTypeOfTextFields()
        amountToggleContainer.amountFieldView.textField.returnKeyType = .done
        messageToggleContainer.amountFieldView.textField.returnKeyType = .done

        lastFormFieldCallback = { [weak self] in
            self?.view.endEditing(true)
        }
    }
}

// MARK: - ViewModelBindable Methods
extension MayaRequestMoneyFormViewController: ViewModelBindable {
    func binding(_ viewModel: MayaRequestMoneyFormViewModelProtocol?) {
        guard let viewModel = viewModel else { return }
        amountToggleContainer.amountFieldView.bindFieldProperty(viewModel.amountProperty)

        viewModel.isAmountFieldEnabled.signal.merge(with: viewModel.isMessageFieldEnabled.signal)
            .observe(on: UIScheduler())
            .observeValues({ [weak self] _ in
                guard let self = self else { return }
                var formFields = [MayaValidationFieldView]()
                if viewModel.isAmountFieldEnabled.value {
                    formFields.append(self.amountToggleContainer.amountFieldView)
                }
                if viewModel.isMessageFieldEnabled.value {
                    formFields.append(self.messageToggleContainer.amountFieldView)
                }
                self.formFields = formFields

                formFields.forEach { $0.isCopyPasteEnabled = true }

                self.amountToggleContainer.setContainerHidden(!viewModel.isAmountFieldEnabled.value)
                self.messageToggleContainer.setContainerHidden(!viewModel.isMessageFieldEnabled.value)
            })?.addToDisposeBag(disposeBag)

        viewModel.isGenerateCodeEnabled.signal
            .observe(on: UIScheduler())
            .observeValues({ [weak self] isGenerateEnabled in
                guard let self = self else { return }
                self.confirmationView.continueButton.isEnabled = isGenerateEnabled
            })?.addToDisposeBag(disposeBag)

        viewModel.messageRemainingText.signal
            .observe(on: UIScheduler())
            .observeValues({ [weak self] remaining in
                self?.messageToggleContainer.setMessageText(remaining)
            })?.addToDisposeBag(disposeBag)
    }
}

// MARK: - UITextFieldDelegate and UITextViewDelegate Methods
extension MayaRequestMoneyFormViewController {
    override func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        guard let viewModel = viewModel  else { return true }
        if textField == messageToggleContainer.amountFieldView.textField,
           let text = textField.text,
           let textRange = Range(range, in: text) {
            textField.willChangeValue(forKey: "text")
            textField.text = viewModel.updateMessageField(with: text, range: textRange, replacement: string)
            textField.didChangeValue(forKey: "text")
            return false
        } else if textField == amountToggleContainer.amountFieldView.textField {
            let text = viewModel.formatMayaAmount(with: String.unwrappedValue(textField.text).trim(), range: range, and: string)
            amountToggleContainer.amountFieldView.setFormattedAmount(with: text.newValue, offset: text.offset)
            return false
        }
        return true
    }

    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        if URL.absoluteString == accountLimitsUrl?.absoluteString {
            analyticsUtils.logMayaEvents(.tap(Analytics.RequestMoneyCustomize.accountLimits))
            delegate?.didTapAccountLimits(self)
        }
        return false
    }
}
