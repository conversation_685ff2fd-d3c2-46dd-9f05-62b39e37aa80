//
//  MayaProfileViewController.swift
//  PayMaya
//
//  Created by <PERSON> on 3/24/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import ConfigurationService
import Error
import Injector
import ReactiveSwift
import UIKit

private let mayaDesignValue = "2.0"

protocol MayaProfileViewControllerDelegate: AnyObject {
    func didTapProfileQr(_ viewController: MayaPro<PERSON>leViewController)
    func didTapQuickGuide(_ viewController: MayaProfileViewController)
    func didTapSecurityCenter(_ viewController: MayaProfileViewController)
    func didTapFavorites(_ viewController: MayaProfileViewController)
    func didTapMissions(_ viewController: MayaProfileViewController)
    func didTapVouchers(_ viewController: <PERSON><PERSON>rofileViewController)
    func didTapSubmitInviteCode(_ viewController: MayaProfileViewController)
    func didTapAccountLimits(_ viewController: MayaProfileViewController)
    func didTapGetHelp(_ viewController: MayaProfileViewController)
    func didTapSettings(_ viewController: MayaProfileViewController)
    func didTapUpgradeAccount(_ viewController: MayaProfileViewController)
    func didTapUpdateAccount(_ viewController: MayaProfileViewController)
    func didTapReviewApplication(_ viewController: MayaProfileViewController)
    func didTapReviewEDD(_ viewController: MayaProfileViewController)
    func didTapInviteFriends(_ viewController: MayaProfileViewController)
    func didTapVisitSupportPage(_ viewController: MayaProfileViewController)
    func didTapDuplicateAccount(_ viewController: MayaProfileViewController)
    func didTapVerifiedSeller(_ viewController: MayaProfileViewController)
    func didTapCreateUsername(_ viewController: MayaProfileViewController)
    func didTapAuthorizeRequests(_ viewController: MayaProfileViewController)
    func didTapRateThisApp(_ viewController: MayaProfileViewController)
    func didTapInviteAFriend(_ viewController: MayaProfileViewController)
    func didTapAdBanner(url: URL, _ viewController: MayaProfileViewController)
}

class MayaProfileViewController: ViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @IBOutlet weak var profileStackView: UIStackView!
    @IBOutlet weak var profileDetailsView: MayaProfileDetailsView!
    @IBOutlet weak var inviteFriendsView: MayaProfileReferralView!
    @IBOutlet weak var accountStatusView: MayaProfileAccountUpgradeView!
    @IBOutlet weak var createUsernameView: MayaProfileCreateUsernameView!
    @IBOutlet weak var menuTableView: UITableView!
    @IBOutlet weak var menuTableViewHeightConstraint: NSLayoutConstraint!
    @IBOutlet weak var userDetailsStackView: UIStackView!
    @IBOutlet weak var fullNameLabel: UILabel!
    @IBOutlet weak var usernameLabel: UILabel!
    @IBOutlet weak var mobileNumberLabel: UILabel!
    @IBOutlet weak var internalIDsStackView: UIStackView!
    @IBOutlet weak var deviceIDLabel: UILabel!
    @IBOutlet weak var walletIDLabel: UILabel!
    @IBOutlet weak var profileImageView: UIImageView!

    @IBOutlet weak var accountStatusContainerStackView: UIStackView!
    @IBOutlet weak var userDetailsContainerStackView: UIStackView!
    @IBOutlet weak var cleverTapBannersContainerStackView: UIStackView!

    private var adCarouselViewController: MayaAdCarouselViewController?

    @Inject private var configurationService: ConfigurationService

    var profileTitleView: MayaProfileTitleView?
    weak var delegate: MayaProfileViewControllerDelegate?

    override var shouldUseMayaNavigationBar: Bool { true }
    override var shouldUseMayaArrowBackButton: Bool { false }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupTableView()
        #if !PROD_TARGET && !SANDBOX_TARGET
        setupInternalIDsStackView()
        #endif
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.Menu())
        super.viewWillAppear(animated)

        analyticsService.logMayaEvents(.appear(Analytics.Menu.none))
        viewModel?.customerAccountAction.apply().start()
        viewModel?.referralEligibilityAction.apply().start()
        viewModel?.fetchApprovalRequestsAction.apply().start()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        viewModel?.userProfileAction.apply().start()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        menuTableView.layer.borderColor = CommonAsset.MayaColors.Grey.grey3.color.cgColor
        menuTableView.layer.borderWidth = 1.0
        menuTableView.layer.cornerRadius = 16.0
        menuTableViewHeightConstraint.constant = menuTableView.contentSize.height

        updateProfileHeaderMargins()
    }

    @objc func didTapProfileImageView() {
        delegate?.didTapDuplicateAccount(self)
    }

    @IBAction func didTapProfileQrButton(_ sender: Any) {
        analyticsService.logMayaEvents(.tap(Analytics.Menu.qr))
        guard let viewModel = viewModel else { return }
        if viewModel.isAccountLimited.value {
            delegate?.didTapDuplicateAccount(self)
        } else {
            delegate?.didTapProfileQr(self)
        }
    }

    @objc private func didDoubleTapName() {
        internalIDsStackView.isShown = true
    }

    @objc private func didTapIDList() {
        guard let deviceID = viewModel?.deviceID,
            let walletID = viewModel?.walletID else {
            return
        }

        let idString = "Device ID: \(deviceID), wallet ID: \(walletID)"
        UIPasteboard.general.string = idString

        showMayaSnackBar(message: "Device and wallet IDs copied!")
    }
}

extension MayaProfileViewController {
    func setupTableView() {
        menuTableView.estimatedRowHeight = 0.0
        menuTableView.estimatedSectionHeaderHeight = 0.0
        menuTableView.estimatedSectionFooterHeight = 0.0
        if #available(iOS 15.0, *) {
            menuTableView.sectionHeaderTopPadding = 0.0
        }
        menuTableView.register(UINib(nibName: "MayaProfileMenuPushApprovalTableViewCell", bundle: nil), forCellReuseIdentifier: MayaProfileMenuPushApprovalTableViewCell.defaultReuseIdentifier)
    }

    func setupInternalIDsStackView() {
        let nameTapGesture = UITapGestureRecognizer(target: self, action: #selector(didDoubleTapName))
        nameTapGesture.numberOfTapsRequired = 2
        fullNameLabel.addGestureRecognizer(nameTapGesture)

        let idsTapGesture = UITapGestureRecognizer(target: self, action: #selector(didTapIDList))
        internalIDsStackView.addGestureRecognizer(idsTapGesture)
    }

    func setupAccountStatusView(kycStatus: KYCClientStatus) {
        let reKYCStatus = viewModel?.reKYCStatusProperty.value ?? .none

        // kycStatus Flags
        // .zero        means that user haven't started the KYC submission process
        // .pointFive   means that the user have completed the KYC submission but waiting for approval.
        // .forEdd      means that the user have completed the KYC submission but requires more information from the user.
        // .one         means that the user have completed the KYC submission and was verified successfully

        // reKYCStatus Flags
        // .tagged      means that user haven't started the Re-KYC submission process
        // .rejected    means that the user have completed the Re-KYC submission but was rejected.
        // .submitted   means that the user have completed the Re-KYC submission but waiting for approval.
        // .approved    means that the user have completed the Re-KYC submission and was verified successfully.
        // .none        means that the user does not need to undergo Re-KYC process.
        // NOTE: EDD state is yet to be implemented in Re-KYC.

        // Business Rule: Banner is shown for unverified account
        let shouldShowKYCBanner = kycStatus == .zero || kycStatus == .pointFive
        // Business Rule: Banner is shown for unverified account and are flagged to EDD.
        // Note: Re-KYC EDD condition can be added here.
        let shouldShowEDDBanner = kycStatus == .forEdd
        // Business Rule: Banner is shown if account is verified and flagged for Re-KYC
        let shouldShowReKYCBanner = kycStatus == .one && (reKYCStatus == .tagged || reKYCStatus == .rejected || reKYCStatus == .submitted)
        // If either of the business rule is satisfied then the account status banner should be resented to the user.
        let shouldShowAccountStatusBanner = shouldShowKYCBanner || shouldShowEDDBanner || shouldShowReKYCBanner

        if !shouldShowAccountStatusBanner || kycStatus == .verifiedSeller {
            if accountStatusView.isDescendant(of: accountStatusContainerStackView) {
                accountStatusContainerStackView.removeArrangedSubview(accountStatusView)
                accountStatusView.removeFromSuperview()
            }
        } else {
            accountStatusView.bind(kycStatus: kycStatus, reKYCStatus: reKYCStatus)
            if !accountStatusView.isDescendant(of: accountStatusContainerStackView) {
                accountStatusContainerStackView.addArrangedSubview(accountStatusView)
            }
        }
    }

    func setupUsernameBanner(kycStatus: KYCClientStatus) {
        guard
            let viewModel = viewModel,
            viewModel.isSetUsernameEnabled
        else { return }
        if viewModel.username.value.isEmpty, kycStatus == .one {
            if !createUsernameView.isDescendant(of: accountStatusContainerStackView) {
                accountStatusContainerStackView.addArrangedSubview(createUsernameView)
            }
        } else {
            if createUsernameView.isDescendant(of: accountStatusContainerStackView) {
                accountStatusContainerStackView.removeArrangedSubview(createUsernameView)
                createUsernameView.removeFromSuperview()
            }
        }
        usernameLabel.isHidden = viewModel.username.value.isEmpty
        usernameLabel.text = viewModel.username.value
    }

    func handleMenuAction(_ menu: MayaProfileMenuItem) {
        switch menu {
        case .quickGuide:
            delegate?.didTapQuickGuide(self)
        case .securityCenter:
            delegate?.didTapSecurityCenter(self)
        case .favorites:
            delegate?.didTapFavorites(self)
        case .missions:
            delegate?.didTapMissions(self)
        case .vouchers:
            delegate?.didTapVouchers(self)
        case .submitInviteCode:
            delegate?.didTapSubmitInviteCode(self)
        case .accountLimits:
            delegate?.didTapAccountLimits(self)
        case .settings:
            delegate?.didTapSettings(self)
        case .getHelp:
            delegate?.didTapGetHelp(self)
        case .signout:
            self.confirmSignoutAction()
        case .auth:
            delegate?.didTapAuthorizeRequests(self)
        case .rateThisApp:
            delegate?.didTapRateThisApp(self)
        case .inviteAFriend:
            delegate?.didTapInviteAFriend(self)
        }

        if let module = menu.analyticsModule {
            analyticsService.logMayaEvents(.tap(module), attributes: [.design: mayaDesignValue])
        }
    }

    func confirmSignoutAction() {
        let alert = UIAlertController(title: nil, message: L10n.Profile.Logout.Confirmation.message, preferredStyle: .actionSheet)
        alert.addAction(UIAlertAction(title: L10n.Profile.Logout.Confirmation.Action.signout, style: .destructive, handler: { [weak self] _ in
            guard let self = self else { return }
            self.viewModel?.signoutAction.apply().start()
        }))
        alert.addAction(UIAlertAction(title: L10n.Profile.Signout.Confirmation.Action.cancel, style: .cancel, handler: { _ in }))
        present(alert, animated: true)
    }
}

// MARK: - ViewControllerBindable methods
extension MayaProfileViewController: ViewControllerBindable {
    func binding() {
        profileDetailsView.delegate = self
        inviteFriendsView.delegate = self
        accountStatusView.delegate = self
        createUsernameView.delegate = self

        profileTitleView = UINib(nibName: String(describing: MayaProfileTitleView.self), bundle: nil).instantiate(withOwner: nil, options: nil)[0] as? MayaProfileTitleView
        profileTitleView?.delegate = self
        navigationItem.titleView = profileTitleView
    }
}

// MARK: ViewModelBindable methods
extension MayaProfileViewController: ViewModelBindable {
    func binding(_ viewModel: MayaProfileViewModelProtocol?) {
        guard let viewModel = viewModel else { return }

        if viewModel.isSetUsernameEnabled {
            let isAccountLimited = viewModel.isAccountLimited.value
            profileDetailsView.isHidden = true
            usernameLabel.isHidden = viewModel.username.value.isEmpty
            fullNameLabel.text = viewModel.name.value
            usernameLabel.text = viewModel.username.value
            mobileNumberLabel.text = viewModel.mobileNumber.value.applyMayaMobileNumberFormat()
            profileImageView.image = isAccountLimited ? Asset.MayaImages.Profile.iconDuplicateAccount.image : Asset.MayaImages.Profile.iconMayaProfile.image

            if isAccountLimited {
                let tapGestureRecognizer = UITapGestureRecognizer(target: self, action: #selector(didTapProfileImageView))
                profileImageView.addGestureRecognizer(tapGestureRecognizer)
            }
        } else {
            userDetailsStackView.isHidden = true
            let profileName = viewModel.username.value.isEmpty ? viewModel.name.value : viewModel.username.value
            profileDetailsView.bind(profileName: profileName, profileNumber: viewModel.mobileNumber.value, isAccountLimited: viewModel.isAccountLimited.value)
        }

        inviteFriendsView.bind(inviteCodeBannerSpiel: viewModel.inviteCodeBannerSpiel)

        if let kycStatus = viewModel.kycClientStatusProperty.value {
            setupAccountStatusView(kycStatus: kycStatus)
            setupUsernameBanner(kycStatus: kycStatus)
            profileTitleView?.updateStatusImage(kycStatus)
        }

        #if !PROD_TARGET && !SANDBOX_TARGET
        setupInternalIDs()
        #endif

        viewModel.profileMenuProperty.producer
            .skipRepeats()
            .observe(on: UIScheduler())
            .startWithValues { [weak self] _ in
                guard let self = self else { return }
                self.menuTableView.reloadData()
                self.view.layoutIfNeeded()
            }.addToDisposeBag(disposeBag)

        // TODO: Miquido Maya Auth - Add cell indicator

        viewModel.canInviteFriendsProperty.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] canInviteFriends in
                guard let self = self else { return }
                if canInviteFriends {
                    self.accountStatusContainerStackView.addArrangedSubview(self.inviteFriendsView)
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.userProfileAction.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] executing in
                guard let self = self else { return }
                executing ? self.showMayaLoader() : self.hideMayaLoader()
            }?.addToDisposeBag(disposeBag)

        viewModel.userProfileAction.completed
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard
                    let self = self,
                    let kycStatus = viewModel.kycClientStatusProperty.value
                else { return }
                self.setupAccountStatusView(kycStatus: kycStatus)
                self.setupUsernameBanner(kycStatus: kycStatus)
                self.profileTitleView?.updateStatusImage(kycStatus)

                // Differentiate handling between expandedReKYC and reKYC toggles
                if self.configurationService.reKYCEnabled.value {
                    if self.configurationService.expandedReKYCEnabled.value {
                        guard let reKYCAction = viewModel.expandedReKYCAction else { return }
                        // Do not force the user to go back to Dashboard if action is Nudge or No Action
                        if kycStatus == .one && reKYCAction != ReKYCData.ActionCode.nudge.rawValue && reKYCAction != ReKYCData.ActionCode.noAction.rawValue {
                            // Force the user to go back to Dashboard and show appropriate action
                            self.didTapBack(self)
                        }
                    } else {
                        // Force the user to go back to Dashboard and show Re-KYC bottom sheet reminder
                        let reKYCStatus = viewModel.reKYCStatusProperty.value
                        if kycStatus == .one && (reKYCStatus == .tagged || reKYCStatus == .rejected) {
                            self.didTapBack(self)
                        }
                    }
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.signoutAction.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] executing in
                guard let self = self else { return }
                executing ? self.showMayaLoader() : self.hideMayaLoader()
            }?.addToDisposeBag(disposeBag)

        let didLogoutSignal = Signal.merge([viewModel.signoutAction.completed.map { _ in Error.none }, viewModel.signoutAction.errors])
        didLogoutSignal.observe(on: UIScheduler()).observeValues { [weak self] _ in
            guard let self = self else { return }
            self.routeActionDelegate?.didRequestSignout(self, viewModel: nil, signoutRoute: .landing, performAction: false, showAlert: true)
        }?.addToDisposeBag(disposeBag)

        if viewModel.isProfileClevertapUnliBannersEnabled {
            viewDidAppearSignal.take(first: 1)
                .delay(1, on: QueueScheduler.main)
                .observeValues { [weak self] _ in
                    guard let self,
                          let viewModel = self.viewModel,
                          self.adCarouselViewController?.viewModel == nil
                    else { return }

                    self.setupClevertapBanners()
                }?.addToDisposeBag(disposeBag)

            viewWillAppearSignal.skip(first: 1)
                .throttle(1, on: QueueScheduler.main)
                .observeValues { [weak self] _ in
                    guard let self else { return }
                    self.sendClevertapEvent()
                }?.addToDisposeBag(disposeBag)
        }
    }

    private func setupInternalIDs() {
        if let deviceID = viewModel?.deviceID {
            deviceIDLabel.text = "Device ID: \(deviceID)"
        }

        if let walletID = viewModel?.walletID {
            walletIDLabel.text = "Wallet ID: \(walletID)"
        }
    }

    private func updateProfileHeaderMargins() {
        userDetailsContainerStackView.translatesAutoresizingMaskIntoConstraints = false
        userDetailsContainerStackView.layoutMargins = UIEdgeInsets(top: 0, left: 24, bottom: 0, right: 24)
        userDetailsContainerStackView.isLayoutMarginsRelativeArrangement = true

        NSLayoutConstraint.activate([
            userDetailsStackView.leadingAnchor.constraint(equalTo: userDetailsContainerStackView.layoutMarginsGuide.trailingAnchor),
            userDetailsStackView.topAnchor.constraint(equalTo: userDetailsContainerStackView.topAnchor),
            userDetailsStackView.trailingAnchor.constraint(equalTo: userDetailsContainerStackView.layoutMarginsGuide.trailingAnchor)
        ])

        accountStatusContainerStackView.translatesAutoresizingMaskIntoConstraints = false
        accountStatusContainerStackView.layoutMargins = UIEdgeInsets(top: 0, left: 24, bottom: 0, right: 24)
        accountStatusContainerStackView.isLayoutMarginsRelativeArrangement = true
    }
}

// MARK: UITableViewDelegates and UITableViewDataSource
extension MayaProfileViewController: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard
            let viewModel = viewModel,
            let menuItems = viewModel.profileMenuProperty.value
        else {
            return 0
        }
        return menuItems.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let viewModel = viewModel,
              let menuItems = viewModel.profileMenuProperty.value,
              let menuItem = menuItems[safeIndex: indexPath.row]
        else {
            return UITableViewCell()
        }

        switch menuItem {
        case .signout:
            let cell = tableView.dequeueReusableCell(withIdentifier: MayaProfileButtonTableViewCell.defaultReuseIdentifier, for: indexPath) as? MayaProfileButtonTableViewCell
            if let viewModel = menuItem.viewModel {
                cell?.bind(viewModel)
            }
            cell?.selectionStyle = .none
            return cell ?? UITableViewCell()
        case .auth:
            let cell = tableView.dequeueReusableCell(withIdentifier: MayaProfileMenuPushApprovalTableViewCell.defaultReuseIdentifier, for: indexPath) as? MayaProfileMenuPushApprovalTableViewCell
            if let viewModel = menuItem.viewModel {
                cell?.bind(viewModel)
            }
            cell?.selectionStyle = .none
            cell?.isNotificationDotHidden <~ viewModel.hasPendingApprovalRequests.producer.map { !$0 }
            return cell ?? UITableViewCell()
        default:
            let cell = tableView.dequeueReusableCell(withIdentifier: MayaProfileMenuTableViewCell.defaultReuseIdentifier, for: indexPath) as? MayaProfileMenuTableViewCell
            if let viewModel = menuItem.viewModel {
                cell?.bind(viewModel)
            }
            cell?.selectionStyle = .none
            return cell ?? UITableViewCell()
        }
    }

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView = tableView.dequeueReusableCell(withIdentifier: MayaProfileHeaderView.defaultReuseIdentifier) as? MayaProfileHeaderView
        return headerView
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        guard let menuItems = viewModel?.profileMenuProperty.value,
              let menuItem = menuItems[safeIndex: indexPath.row]
        else {
            return
        }
        handleMenuAction(menuItem)
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        guard let viewModel = viewModel,
              let menuItems = viewModel.profileMenuProperty.value,
              let menuItem = menuItems[safeIndex: indexPath.row]
        else {
            return 0
        }

        switch menuItem {
        case .signout:
            return MayaProfileButtonTableViewCell.defaultHeight
        case .auth:
            return MayaProfileMenuPushApprovalTableViewCell.defaultHeight
        default:
            return MayaProfileMenuTableViewCell.defaultHeight
        }
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return MayaProfileHeaderView.estimatedHeight
    }

    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return CGFloat.leastNormalMagnitude
    }
}

// MARK: MayaProfileAccountUpgradeViewDelegate Methods
extension MayaProfileViewController: MayaProfileAccountUpgradeViewDelegate {
    func didTapCTAButton(_ cell: MayaProfileAccountUpgradeView) {
        guard let kycStatus = viewModel?.kycClientStatusProperty.value else {
            return
        }
        let reKYCStatus = viewModel?.reKYCStatusProperty.value

        switch kycStatus {
        case .zero:
            delegate?.didTapUpgradeAccount(self)
        case .pointFive:
            analyticsService.logMayaEvents(.tap(Analytics.Menu.checkApplicationStatus), attributes: [.type: "ekyc"])
            delegate?.didTapReviewApplication(self)
        case .one:
            switch reKYCStatus {
            case .submitted:
                analyticsService.logMayaEvents(.appear(Analytics.EKYCV6.reKYCCheckStatus))
                delegate?.didTapReviewApplication(self)
            case .tagged, .rejected:
                analyticsService.logMayaEvents(.tap(Analytics.EKYCV6.reKYCProfile))
                delegate?.didTapUpdateAccount(self)
            default: break
            }
        case .forEdd:
            analyticsService.logMayaEvents(.tap(Analytics.Menu.checkApplicationStatus), attributes: [.type: "edd"])
            delegate?.didTapReviewEDD(self)
        default:
            break
        }
    }
}

// MARK: MayaProfileAccountUpgradeViewDelegate Methods
extension MayaProfileViewController: MayaProfileReferralViewDelegate {
    func didTapInviteFriends(_ cell: MayaProfileReferralView) {
        delegate?.didTapInviteFriends(self)
    }
}

// MARK: MayaProfileDetailsViewDelegate Methods
extension MayaProfileViewController: MayaProfileDetailsViewDelegate {
    func didTapProfileImage(_ view: MayaProfileDetailsView) {
        delegate?.didTapDuplicateAccount(self)
    }
}

// MARK: MayaProfileTitleViewDelegate Methods
extension MayaProfileViewController: MayaProfileTitleViewDelegate {
    func didTapStatusView(_ view: MayaProfileTitleView) {
        analyticsService.logMayaEvents(.tap(Analytics.Menu.verifiedSeller))
        delegate?.didTapVerifiedSeller(self)
    }
}

// MARK: MayaProfileCreateUsernameViewDelegate Methods
extension MayaProfileViewController: MayaProfileCreateUsernameViewDelegate {
    func didTapCreateUsername(_ view: MayaProfileCreateUsernameView) {
        analyticsService.logMayaEvents(.tap(Analytics.Menu.username))
        delegate?.didTapCreateUsername(self)
    }
}

extension MayaProfileViewController {
    private func setupClevertapBanners() {
        guard let viewModel,
              viewModel.isProfileClevertapUnliBannersEnabled
        else { return }

        adCarouselViewController = StoryboardScene.Home.mayaAdCarouselViewController.instantiate()
        if let adCarouselViewController = adCarouselViewController {
            adCarouselViewController.delegate = self
            adCarouselViewController.tapAnalyticsModule = Analytics.MenuBanner.none
            adCarouselViewController.bind(viewModel.adCarouselViewModel)
            cleverTapBannersContainerStackView.removeAllArrangedSubviews()

            let bannerView = UIView()
            bannerView.translatesAutoresizingMaskIntoConstraints = false

            cleverTapBannersContainerStackView.addArrangedSubview(bannerView)
            add(child: adCarouselViewController, to: bannerView, padding: 0)

            NSLayoutConstraint.activate([
                bannerView.heightAnchor.constraint(equalTo: bannerView.widthAnchor, multiplier: AdModule.profileBanner.bannerAspectRatio ?? AdBannerProperties.defaultHeight)
            ])
        }

        hideClevertapBanners(hide: false)
        sendClevertapEvent()
    }

    private func sendClevertapEvent() {
        guard let viewModel,
              viewModel.isProfileClevertapUnliBannersEnabled
        else { return }

        viewModel.adCarouselViewModel.initializeAdBannerService()
        analyticsService.logMayaEvents(action: .appear(Analytics.MenuBanner.none))
    }

    func hideClevertapBanners(hide: Bool) {
        UIView.animate(withDuration: 0.2) {
            self.cleverTapBannersContainerStackView.isHidden = hide
        }
    }
}

extension MayaProfileViewController: AdCarouselViewControllerDelegate {
    func didTapAdBanner(_ viewController: ViewController, link: URL) {
        delegate?.didTapAdBanner(url: link, self)
    }

    func didChangeBannerState(_ viewController: ViewController, state: AdBannerState) {
        switch state {
        case .failed, .unloaded: hideClevertapBanners(hide: true)
        default: break
        }
    }
}
