//
//  MayaServicesViewController.swift
//  PayMaya
//
//  Created by <PERSON> on 3/7/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import ConfigurationService
import Injector
import ReactiveSwift
import UIKit

protocol MayaServicesViewControllerDelegate: AnyObject {
    func didTapService(_ viewController: MayaServicesViewController, service: Service)
    func didTapSecurityCenter(_ viewController: MayaServicesViewController)
    func didTapMessages(_ viewController: MayaServicesViewController)
    func didTapProfile(_ viewController: MayaServicesViewController)
    func didTapHelp(_ viewController: MayaServicesViewController)
    func didTapSettings(_ viewController: MayaServicesViewController)
    func didTapAboutMaya(_ viewController: MayaServicesViewController)
    func didTapBack(_ viewController: MayaServicesViewController)
    func didTapAdBanner(url: URL, _ viewController: MayaServicesViewController)
}

private typealias CellConstants = MayaServiceCellConstants.CoreServices

class MayaServicesViewController: ViewController, MayaTabBarChildViewControllerProtocol, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject var configurationServiceV2: ConfigurationServiceV2

    @IBOutlet private weak var scrollView: UIScrollView!
    @IBOutlet private weak var scrollContainerView: UIView!
    @IBOutlet private weak var coreServicesCollectionView: UICollectionView!
    @IBOutlet private weak var categoriesTableView: ContentSizedTableView!
    @IBOutlet private weak var actionsBackgroundView: View!
    @IBOutlet private weak var actionsTableView: ContentSizedTableView!
    @IBOutlet private weak var servicesLabel: UILabel!
    @IBOutlet private weak var servicesLabelTopConstraint: NSLayoutConstraint!
    @IBOutlet weak var leftBarButtonItem: UIBarButtonItem!
    @IBOutlet private weak var adCarouselView: UIView!
    @IBOutlet private weak var adCarouselViewHeightConstraint: NSLayoutConstraint!

    weak var delegate: MayaServicesViewControllerDelegate?

    private var adCarouselViewController: MayaAdCarouselViewController?

    override var titleContentOffset: CGFloat { servicesLabel.frame.minY + servicesLabel.frame.size.height }

    override var shouldUseMayaNavigationBar: Bool {
        guard let viewModel else { return false }

        return viewModel.isDashboardNavigationRefactorEnabled
    }

    override var shouldUseMayaArrowBackButton: Bool {
        guard let viewModel else { return false }

        return !viewModel.isDashboardNavigationRefactorEnabled
    }

    override var titleFont: UIFont {
        guard let viewModel else { return super.titleFont }

        return viewModel.isDashboardNavigationRefactorEnabled ? CommonFontFamily.Jeko.bold.font(size: 20) : super.titleFont
    }

    override var titleOffset: UIOffset {
        guard let viewModel else { return super.titleOffset }

        return viewModel.isDashboardNavigationRefactorEnabled ? UIOffset(horizontal: -CGFloat.greatestFiniteMagnitude, vertical: 0) : super.titleOffset
    }

    override var leftBarButtonInset: UIEdgeInsets {
        guard let viewModel else { return super.leftBarButtonInset }

        return viewModel.isDashboardNavigationRefactorEnabled ? UIEdgeInsets(top: 0, left: 6, bottom: 0, right: 0) : super.leftBarButtonInset
    }

    override func didTapBack(_ sender: Any) {
        delegate?.didTapBack(self)
    }

    override var mayaNavigationBarScrollView: UIScrollView? {
        guard let viewModel else { return nil }

        if viewModel.isDashboardNavigationRefactorEnabled {
            return scrollView
        }

        return nil
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.Services.exit)
        super.viewWillAppear(animated)
        analyticsService.logMayaAttributableEvents(action: .appear(Analytics.Services()))
    }

    private func sendClevertapBannerAppearEvent() {
        guard let viewModel = viewModel, viewModel.isServicesClevertapUnliBannersEnabled else { return }

        adCarouselViewController?.viewModel?.initializeAdBannerService()
        analyticsService.logMayaEvents(action: .appear(Analytics.ServicesBanner.none))
    }

    private func setupClevertapBanners() {
        guard let viewModel = viewModel,
              viewModel.isServicesClevertapUnliBannersEnabled
        else { return }

        adCarouselViewController = StoryboardScene.Home.mayaAdCarouselViewController.instantiate()
        if let adCarouselViewController = adCarouselViewController {
            adCarouselViewController.delegate = self
            adCarouselViewController.tapAnalyticsModule = Analytics.ServicesBanner.none
            adCarouselViewController.navigationBarHidden = !viewModel.isDashboardNavigationRefactorEnabled

            switch viewModel.bannerLocation {
            case .coreServicesSection:
                add(child: adCarouselViewController, to: adCarouselView, padding: 0)
                hideClevertapBannerFromCoreServicesSection(hide: false)
                adCarouselViewController.bind(viewModel.adCarouselViewModel)
            case .categoriesSection:
                addChild(adCarouselViewController)
                adCarouselViewController.bind(viewModel.adCarouselViewModel)
            default:
                break
            }
        }
    }

    func refreshClevertapBanners() {
        guard let viewModel = viewModel,
              viewModel.isServicesClevertapUnliBannersEnabled,
              viewModel.bannerLocation != .none
        else { return }

        adCarouselViewController?.refreshBanner()

        switch viewModel.bannerLocation {
        case .coreServicesSection: hideClevertapBannerFromCoreServicesSection(hide: false)
        case .categoriesSection: viewModel.addClevertapBannerCategory()
        default: break
        }
        sendClevertapBannerAppearEvent()
    }

    func hideClevertapBannerFromCoreServicesSection(hide: Bool) {
        guard let viewModel = viewModel,
              viewModel.isServicesClevertapUnliBannersEnabled
        else { return }

        let height: CGFloat = hide ? 0 : 136
        adCarouselViewController?.view.isHidden = hide
        adCarouselViewHeightConstraint.constant = height
        adCarouselView.isHidden = hide
    }
}

// MARK: Internal methods
extension MayaServicesViewController {
    func scrollToSection(_ section: Param.ServicesSection) {
        func scrollToMoreActions() {
            let offsetY = scrollContainerView.bounds.height - scrollView.bounds.height

            guard offsetY > 0 else { return }
            let offset = CGPoint(x: 0, y: offsetY)

            scrollView.setContentOffset(offset, animated: true)
        }

        func scrollToCategory(id: String) {
            guard let row = viewModel?.categoryRow(forID: id) else { return }

            let rect = categoriesTableView.rectForRow(at: IndexPath(row: row, section: 0))
            let convertedRect = categoriesTableView.convert(rect, to: scrollContainerView)
            let offset = convertedRect.origin

            scrollView.setContentOffset(offset, animated: true)
        }

        // Deley needed to wait for table/collection views to finish their layouts first
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            switch section {
            case .category(let id):
                scrollToCategory(id: id)
            case .moreActions:
                scrollToMoreActions()
            }
        }
    }
}

// MARK: ViewControllerModelBindable
extension MayaServicesViewController: ViewControllerBindable {
    func binding() {
        guard let viewModel else { return }

        setupViews()

        if viewModel.isDashboardNavigationRefactorEnabled {
            servicesLabelTopConstraint.constant = 16
            self.navigationBarHidden = false
        } else {
            servicesLabelTopConstraint.constant = 40
            self.navigationBarHidden = true
        }

        NotificationCenter.default.reactive.notifications(forName: Notification.Name.willOpenMoreServices)
            .observe(on: UIScheduler())
            .observeValues { [weak self] notification in
                guard let sourceScreen = notification.userInfo?[NotificationUserInfoKey.source.rawValue] as? MayaServicesSourceScreen else { return }
                self?.viewModel?.updateSourceScreen(sourceScreen)
            }?.addToDisposeBag(disposeBag)
    }

    private func setupViews() {
        coreServicesCollectionView.dataSource = self
        coreServicesCollectionView.delegate = self
        coreServicesCollectionView.registerCell(of: MayaServiceCollectionViewCell.self)

        categoriesTableView.dataSource = self
        categoriesTableView.delegate = self
        categoriesTableView.registerCell(of: MayaServiceCategoryTableViewCell.self)
        categoriesTableView.registerCell(of: MayaServiceBannerTableViewCell.self)

        actionsTableView.dataSource = self
        actionsTableView.delegate = self
        actionsTableView.registerCell(of: MayaServiceActionTableViewCell.self)

        actionsTableView.layer.borderWidth = 1.0
        actionsTableView.layer.cornerRadius = 16.0
        actionsTableView.layer.borderColor = CommonAsset.MayaColors.Grey.grey3.color.cgColor
    }
}

// MARK: ViewModelBindable
extension MayaServicesViewController: ViewModelBindable {
    func binding(_ viewModel: MayaServicesViewModelProtocol?) {
        guard let viewModel = self.viewModel else { return }

        categoriesTableView.reactive.reloadData <~ viewModel.categoriesProperty.signal.map { _ in }

        if viewModel.isServicesClevertapUnliBannersEnabled {
            viewDidAppearSignal.take(first: 1)
                .delay(1, on: QueueScheduler.main) // Adding delay on first view did appear signal. This will give ample time to the MayaDashboardViewController to fetch for ads.
                .observeValues { [weak self] _ in
                    guard let self = self,
                          let viewModel = self.viewModel,
                          self.adCarouselViewController?.viewModel == nil
                    else { return }

                    self.setupClevertapBanners()
                    self.refreshClevertapBanners()
                }?.addToDisposeBag(disposeBag)

            viewDidAppearSignal.skip(first: 1)
                .throttle(1, on: QueueScheduler.main)
                .observeValues { [weak self] _ in
                    guard let self = self else { return }

                    self.refreshClevertapBanners()
                }?.addToDisposeBag(disposeBag)
        }
    }
}

// MARK: UITableViewDataSource and UITableViewDelegate
extension MayaServicesViewController: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let viewModel = viewModel else {
            return 0
        }

        if tableView === categoriesTableView {
            return viewModel.numberOfCategories
        } else if tableView == actionsTableView {
            return viewModel.numberOfActions
        } else {
            return 0
        }
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if tableView === categoriesTableView {
            return categoriesTableView(tableView, cellForRowAt: indexPath)
        } else if tableView == actionsTableView {
            return actionsTableView(tableView, cellForRowAt: indexPath)
        } else {
            return UITableViewCell()
        }
    }

    private func categoriesTableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let viewModel = viewModel else { return UITableViewCell() }

        if viewModel.isCategoryRowBanner(forRowAt: indexPath) {
            let cell = tableView.dequeueCell(of: MayaServiceBannerTableViewCell.self, for: indexPath)
            if let adCarouselViewController = self.adCarouselViewController {
                cell.addAdCarouselView(adCarouselView: adCarouselViewController.view)
                adCarouselViewController.collectionView.isAutoscrollEnabled = true
            }
            return cell ?? UITableViewCell()
        }

        guard let cellViewModel = viewModel.categoryViewModel(forRowAt: indexPath) else { return UITableViewCell() }

        let cell = tableView.dequeueCell(of: MayaServiceCategoryTableViewCell.self, for: indexPath)
        cell.delegate = self
        cell.bind(cellViewModel)

        let height = viewModel.categoryHeight(forRowAt: indexPath)
        cell.updateHeight(height)

        return cell
    }

    private func actionsTableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let viewModel = viewModel,
            let action = viewModel.action(forRowAt: indexPath) else {
            return UITableViewCell()
        }

        let cell = tableView.dequeueCell(of: MayaServiceActionTableViewCell.self, for: indexPath)
        let showSeparator: Bool = indexPath.row != (viewModel.numberOfActions - 1)
        cell.bind(action, showSeparator: showSeparator)
        return cell
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        guard tableView === actionsTableView,
              let viewModel = self.viewModel,
              let action = viewModel.action(forRowAt: indexPath) else {
            return
        }

        let attributes: [AnalyticsAttributeKey: Any] = [AnalyticsAttributeKey.button: action.analyticsValue, AnalyticsAttributeKey.buttonType: "button"]
        analyticsService.logMayaEvents(action: .tap(Analytics.Services.item), keyAttributes: attributes)

        switch action {
        case .chat:
            delegate?.didTapMessages(self)
        case .securityCenter:
            delegate?.didTapSecurityCenter(self)
        case .help:
            delegate?.didTapHelp(self)
        case .profile:
            delegate?.didTapProfile(self)
        case .settings:
            delegate?.didTapSettings(self)
        case .about:
            delegate?.didTapAboutMaya(self)
        }
    }
}

// MARK: UITableViewDataSource and UITableViewDelegate
extension MayaServicesViewController: UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return viewModel?.numberOfCoreServices ?? 0
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let service = viewModel?.coreService(forItemAt: indexPath) else {
            return UICollectionViewCell()
        }

        let cell = collectionView.dequeueCell(of: MayaServiceCollectionViewCell.self, for: indexPath)

        let dataModel = MayaServiceCellDataModel(service: service, style: .core)
        cell.bind(dataModel)

        return cell
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        guard let viewModel = self.viewModel, let service = viewModel.coreService(forItemAt: indexPath) else { return }

        // position and positionWidget both start at index 1
        var attributes: [AnalyticsAttributeKey: Any] = [
            .tile: service.id,
            .module: AnalyticsConstants.Module.servicesDashboard.rawValue,
            .category: "core",
            .position: indexPath.row + 1,
            .positionWidget: 0
        ]
        attributes.merge(with: sourceClickAttributes)
        attributes[.status] = service.dynamicStatusType.analyticsValue
        analyticsService.logMayaEvents(action: .tap(Analytics.Services.tile), keyAttributes: attributes)
        delegate?.didTapService(self, service: service)
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CellConstants.collectionViewProperties.computedSize(maximumWidth: CellConstants.width, targetHeight: CellConstants.height)
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return CellConstants.collectionViewProperties.minimumSpacing
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        if let numberOfCoreServices = viewModel?.numberOfCoreServices {
            return CellConstants.collectionViewProperties.interItemSpacing(maximumWidth: CellConstants.width, numberOfActualColumns: numberOfCoreServices)
        }
        return CellConstants.collectionViewProperties.interItemSpacing(maximumWidth: CellConstants.width)
    }
}

extension MayaServicesViewController: MayaServiceCategoryTableViewCellDelegate {
    func didTapService(_ cell: MayaServiceCategoryTableViewCell, service: Service) {
        delegate?.didTapService(self, service: service)
    }

    func additionalAnalyticsAttributes(_ cell: MayaServiceCategoryTableViewCell) -> [AnalyticsAttributeKey: Any] {
        return sourceClickAttributes
    }

    private var sourceClickAttributes: [AnalyticsAttributeKey: Any] {
        guard let attributeValue = viewModel?.sourceScreen?.attributeValue else {
            return [:]
        }
        return [AnalyticsAttributeKey.sourceClick: attributeValue]
    }
}

// MARK: - AdCarouselViewControllerDelegate Methods
extension MayaServicesViewController: AdCarouselViewControllerDelegate {
    func didTapAdBanner(_ viewController: ViewController, link: URL) {
        delegate?.didTapAdBanner(url: link, self)
    }

    func didChangeBannerState(_ viewController: ViewController, state: AdBannerState) {
        guard let viewModel = viewModel, state == .failed || state == .unloaded,
              viewModel.isServicesClevertapUnliBannersEnabled
        else { return }

        switch viewModel.bannerLocation {
        case .coreServicesSection: hideClevertapBannerFromCoreServicesSection(hide: true)
        case .categoriesSection where viewModel.adCarouselViewModel.adBannersProperty.value.isEmpty: viewModel.removeClevertapBannerCategory()
        default: break
        }
    }
}
