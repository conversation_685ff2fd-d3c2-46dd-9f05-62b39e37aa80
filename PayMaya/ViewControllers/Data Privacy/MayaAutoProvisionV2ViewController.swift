//
//  MayaAutoProvisionV2ViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 5/8/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import ConfigurationService
import Injector
import ReactiveCocoa
import ReactiveSwift

protocol MayaAutoProvisionV2ControllerDelegate: AnyObject {
    func didStartVerificationFlow(_ viewController: MayaAutoProvisionV2ViewController)
    func didTapLink(_ viewController: MayaAutoProvisionV2ViewController, link: URL, title: String, analyticsName: String)
}

final class MayaAutoProvisionV2ViewController: ViewController, MayaProgressBarProtocol, MayaAnalyticsDurationManagerProtocol {
    @Inject private var locationAuthorizerService: LocationAuthorizerServiceProtocol
    @Inject private var shieldDataCollector: ShieldDataCollectorProtocol
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    @IBOutlet private weak var eWalletAutoProvisionItem: MayaAutoProvisionItemView!
    @IBOutlet private weak var savingsAutoProvisionItem: MayaAutoProvisionItemView!
    @IBOutlet private weak var linkWalletTermLabel: MayaAutoProvisionLinkLabel!
    @IBOutlet private weak var linkWalletPrivacyLabel: MayaAutoProvisionLinkLabel!
    @IBOutlet private weak var linkSavingsTermLabel: MayaAutoProvisionLinkLabel!
    @IBOutlet private weak var linkSavingsPrivacyLabel: MayaAutoProvisionLinkLabel!
    @IBOutlet private weak var linkWalletStackView: UIStackView!
    @IBOutlet private weak var linkSavingsStackView: UIStackView!
    @IBOutlet private weak var tableView: UITableView!
    @IBOutlet private weak var tableViewHeightConstraint: NSLayoutConstraint!
    @IBOutlet private weak var dataPersonalizationTitle: UILabel!
    @IBOutlet private weak var dataPersonalizationDescription: UILabel!
    @IBOutlet private weak var showMoreLabel: UILabel!
    @IBOutlet private weak var enableAllCheckBox: MayaCheckBox!
    @IBOutlet private weak var consentsStackView: UIStackView!

    var segment: Int = 2
    weak var delegate: MayaAutoProvisionV2ControllerDelegate?

    override var shouldUseMayaNavigationBar: Bool { true }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.RegV2.accountProvisioning)
        super.viewWillAppear(animated)
        viewModel?.observeLocationAuthorization(delegate: self)
        viewModel?.observeLocationChecking()
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupViews()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        logViewed()
    }

    override func didTapBack(_ sender: Any) {
        super.didTapBack(sender)
        viewModel?.resetConsents()
        logBackButtonTapped()
    }

    private func setupViews() {
        setRightButtonItemTitle(L10n.Shortened.Registration.FlowProgressBar.progress(segment))

        // Wallet
        let eWalletItemInfo = MayaAutoProvisionItemInfo(type: .eWallet,
                                                        title: L10n.Maya.Auto.Provisioning.Item.Wallet.title,
                                                        subtitle: L10n.Maya.Auto.Provisioning.Item.Wallet.subtitle,
                                                        image: Asset.MayaImages.Common.Emoji.imageGreenHeart.image,
                                                        imageBackgroundColor: CommonAsset.MayaColors.Opacity.opacityPurple25.color,
                                                        isChecked: true,
                                                        isReadOnly: true)
        eWalletAutoProvisionItem.delegate = self
        eWalletAutoProvisionItem.setValues(eWalletItemInfo)
        linkWalletTermLabel.text = L10n.Maya.Auto.Provisioning.Item.Wallet.Terms.spiel
        linkWalletTermLabel.link = Constants.WebView.tnc.url
        linkWalletTermLabel.title = L10n.Settings.Menu.Title.tnc
        linkWalletTermLabel.analyticsName = AnalyticsConstants.Screen.RegistrationV2.mayaPHTermsAndConditions.rawValue
        linkWalletTermLabel.delegate = self
        linkWalletPrivacyLabel.text = L10n.Maya.Auto.Provisioning.Item.Wallet.Privacy.spiel
        linkWalletPrivacyLabel.link = Constants.WebView.privacy.url
        linkWalletPrivacyLabel.title = L10n.Settings.Menu.Title.privacy
        linkWalletPrivacyLabel.analyticsName = AnalyticsConstants.Screen.RegistrationV2.mayaPHPrivacyPolicy.rawValue
        linkWalletPrivacyLabel.delegate = self

        // Savings
        let savingsItemInfo = MayaAutoProvisionItemInfo(type: .savings,
                                                        title: L10n.Maya.Auto.Provisioning.Item.Savings.title,
                                                        subtitle: L10n.Maya.Auto.Provisioning.Item.Savings.subtitle,
                                                        image: Asset.MayaImages.Common.Emoji.imagePig.image,
                                                        imageBackgroundColor: CommonAsset.MayaColors.Secondary.secondaryLightGreen.color,
                                                        isChecked: true)
        savingsAutoProvisionItem.delegate = self
        savingsAutoProvisionItem.setValues(savingsItemInfo)
        linkSavingsTermLabel.text = L10n.Maya.Auto.Provisioning.Item.Savings.Terms.spiel
        linkSavingsTermLabel.link = Constants.WebView.mayaBankTerms.url
        linkSavingsTermLabel.title = L10n.Settings.Menu.Title.tnc
        linkSavingsTermLabel.analyticsName = AnalyticsConstants.Screen.RegistrationV2.mayaBankTermsAndConditions.rawValue
        linkSavingsTermLabel.delegate = self
        linkSavingsPrivacyLabel.text = L10n.Maya.Auto.Provisioning.Item.Savings.Privacy.spiel
        linkSavingsPrivacyLabel.link = Constants.WebView.mayaBankPrivacy.url
        linkSavingsPrivacyLabel.title = L10n.Settings.Menu.Title.privacy
        linkSavingsPrivacyLabel.analyticsName = AnalyticsConstants.Screen.RegistrationV2.mayaBankPrivacyPolicy.rawValue
        linkSavingsPrivacyLabel.delegate = self

        MayaOnboardingUtility().resetAutoProvisionInfo()
        MayaOnboardingUtility().setSavingsAutoProvisionOptIn(true)

        // Data personalization
        viewModel?.enableAllConsents(true)
        dataPersonalizationTitle.text = L10n.Shortened.Registration.Data.Personalization.title
        dataPersonalizationDescription.text = L10n.Shortened.Registration.Data.Personalization.description
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(didTapShowMore))
        showMoreLabel.addGestureRecognizer(tapGesture)
    }

    @IBAction func didTapEnableAllButton(_ sender: Any) {
        var status = enableAllCheckBox.status
        status.toggle()
        enableAllCheckBox.setStatus(status)
        viewModel?.enableAllConsents(status)
        logAllowDataPersonalizationTapped(status)
    }

    @objc private func didTapShowMore() {
        guard let viewModel else { return }
        logShowMoreButtonTapped(buttonText: showMoreLabel.text ?? "")
        viewModel.shouldShowPolicyConsents.value.toggle()
        if viewModel.shouldShowPolicyConsents.value {
            showMoreLabel.text = L10n.Shortened.Registration.Show.Less.label
        } else {
            showMoreLabel.text = L10n.Shortened.Registration.Show.More.label
        }
    }

    @IBAction private func didTapContinue(_ sender: Any) {
        if locationAuthorizerService.authorizationStatus == .notDetermined {
            locationAuthorizerService.requestAuthorization()
            logLocationPermissionViewed()
        } else {
            viewModel?.registerAction.apply().start()
        }
        logContinueButtonTapped()
    }
}

// MARK: - ViewModelBindable
extension MayaAutoProvisionV2ViewController: ViewModelBindable {
    func binding(_ viewModel: MayaRegistrationFormV2ViewModelProtocol?) {
        guard let viewModel else { return }

        tableView.reactive.reloadData <~ viewModel.secondaryConsentsProperty.signal.skipRepeats().map { _ in }

        tableView.reactive
            .signal(forKeyPath: "contentSize")
            .observeValues { [weak self] contentSize in
                guard let self, let size = contentSize as? CGSize else { return }
                tableViewHeightConstraint.constant = size.height
            }?
            .addToDisposeBag(disposeBag)

        consentsStackView.reactive.isHidden <~ viewModel.shouldShowPolicyConsents.negate()

        viewModel.allConsentsAreEnabledProperty.producer.skipRepeats()
            .start(on: UIScheduler())
            .startWithResult { [weak self] result in
                guard let self, let isEnabled = result.value else { return }
                enableAllCheckBox.setStatus(isEnabled)
            }.addToDisposeBag(disposeBag)

        viewModel.registerAction.completed
            .observe(on: UIScheduler())
            .observeValues { [weak self] in
                guard let self else { return }
                delegate?.didStartVerificationFlow(self)
            }?.addToDisposeBag(disposeBag)

        viewModel.registerAction.isExecuting.signal
            .observe(on: UIScheduler()).observeValues { [weak self] isExecuting in
                guard let self else { return }
                if isExecuting {
                    showMayaLoader()
                } else {
                    hideMayaLoader()
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.registerAction.errors.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self, let errorViewModel = error.viewModel else { return }
                logErrorDialogViewed(errorViewModel)
                showErrorAlert(viewModel: errorViewModel, useMayaModal: true) {
                    self.logErrorDialogButtonTapped(errorViewModel)
                }
            }?.addToDisposeBag(disposeBag)
    }
}

// MARK: MayaAutoProvisionItemViewDelegate Method
extension MayaAutoProvisionV2ViewController: MayaAutoProvisionItemViewDelegate {
    func didTapCheckbox(itemView: MayaAutoProvisionItemView) {
        guard let type = itemView.getType() else { return }
        let status = itemView.getStatus()

        switch type {
        case .eWallet:
            linkWalletStackView.isHidden = !status
        case .savings:
            linkSavingsStackView.isHidden = !status
            MayaOnboardingUtility().setSavingsAutoProvisionOptIn(status)
        }

        logSavingsButtonTapped(status)
    }
}

// MARK: MayaAutoProvisionLinkLabel Method
extension MayaAutoProvisionV2ViewController: MayaAutoProvisionLinkLabelDelegate {
    func didTap(link: URL, title: String, analyticsName: String) {
        delegate?.didTapLink(self, link: link, title: title, analyticsName: analyticsName)
        guard let name = AnalyticsConstants.Screen.RegistrationV2(rawValue: analyticsName) else { return }
        logAutoProvisionLinkViewed(analyticsName: name)
    }
}

// MARK: - UITableViewDataSource Methods
extension MayaAutoProvisionV2ViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel?.secondaryConsentsProperty.value.count ?? 0
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard
            let viewModel = viewModel,
            let tableViewCell = tableView.dequeueReusableCell(withIdentifier: MayaEditPolicyConsentTableViewCell.defaultReuseIdentifier, for: indexPath) as? MayaEditPolicyConsentTableViewCell,
            let consent = viewModel.secondaryConsentsProperty.value[safeIndex: indexPath.row]
        else { return UITableViewCell() }
        tableViewCell.delegate = self
        tableViewCell.updateCellWithConsentViewModel(consent)
        return tableViewCell
    }
}

// MARK: - MayaEditPolicyConsentTableViewCellDelegate Methods
extension MayaAutoProvisionV2ViewController: MayaEditPolicyConsentTableViewCellDelegate {
    func didToggleEnableConsent(_ consent: SecondaryConsent, cell: MayaEditPolicyConsentTableViewCell) {
        guard let viewModel = viewModel,
              let index = viewModel.secondaryConsentsProperty.value.firstIndex(of: consent),
              let checked = viewModel.toggleEnableConsent(index: index)
        else { return }

        logEnableConsent(consentName: consent.name, checked: checked)
    }

    func didToggleShowConsentDescription(_ consent: SecondaryConsent, cell: MayaEditPolicyConsentTableViewCell) {
        guard let viewModel = viewModel,
              let index = viewModel.secondaryConsentsProperty.value.firstIndex(of: consent),
              let expanded = viewModel.toggleShowConsentDescription(at: index)
        else { return }

        logShowConsentDescription(consentName: consent.name, expanded: expanded)
    }
}

// MARK: - LocationAuthorizerServiceDelegate Methods
 extension MayaAutoProvisionV2ViewController: LocationAuthorizerServiceDelegate {
    func didDenyLocationAuthorization(didShowPrompt: Bool) {
        if didShowPrompt {
            viewModel?.registerAction.apply().start()
            logLocationPermissionButtonTapped(buttonText: AnalyticsConstants.ButtonText.deny)
        }
    }

    func didAcceptLocationAuthorization(didShowPrompt: Bool) {
        if didShowPrompt {
            viewModel?.registerAction.apply().start()
            shieldDataCollector.sendDeviceFingerprint(screen: Constants.Shield.login.rawValue)
            logLocationPermissionButtonTapped(buttonText: AnalyticsConstants.ButtonText.allow)
        }
    }

     func didNotDetermineLocationAuthorization() {
         // do nothing
     }
 }

// MARK: - Analytics Events Methods
private extension MayaAutoProvisionV2ViewController {
    func logViewed() {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.accountProvisioning.viewed(),
                                       keyAttributes: [AnalyticsAttributeKey.screenName: AnalyticsConstants.ScreenName.accountProvisioning,
                                                       AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.accountCreation])
    }

    func logBackButtonTapped() {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.accountProvisioning.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.accountProvisioning,
                                                       AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.accountCreation,
                                                       AnalyticsAttributeKey.button: AnalyticsConstants.ButtonText.back])
    }

    func logSavingsButtonTapped(_ status: Bool) {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.accountProvisioning.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.checkbox: AnalyticsConstants.ActionFunction().status(status),
                                                       AnalyticsAttributeKey.actionFunction: AnalyticsConstants.ActionFunction().checked(status),
                                                       AnalyticsAttributeKey.optionLabel: AnalyticsConstants.ButtonText.savings])
    }

    func logAllowDataPersonalizationTapped(_ status: Bool) {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.accountProvisioning.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.checkbox: AnalyticsConstants.ActionFunction().status(status),
                                                       AnalyticsAttributeKey.actionFunction: AnalyticsConstants.ActionFunction().checked(status),
                                                       AnalyticsAttributeKey.optionLabel: AnalyticsConstants.ButtonText.allowData])
    }

    func logEnableConsent(consentName: String, checked: Bool) {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.accountProvisioning.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.checkbox: AnalyticsConstants.ActionFunction().status(checked),
                                                       AnalyticsAttributeKey.actionFunction: AnalyticsConstants.ActionFunction().checked(checked),
                                                       AnalyticsAttributeKey.optionLabel: consentName])
    }

    func logShowConsentDescription(consentName: String, expanded: Bool) {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.accountProvisioning.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.actionFunction: AnalyticsConstants.ActionFunction().expanded(expanded),
                                                       AnalyticsAttributeKey.button: consentName])
    }

    func logShowMoreButtonTapped(buttonText: String) {
        let hasShownPolicyConsents = viewModel?.shouldShowPolicyConsents.value ?? false
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.accountProvisioning.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.actionFunction: AnalyticsConstants.ActionFunction().shown(hasShownPolicyConsents),
                                                       AnalyticsAttributeKey.button: buttonText])
    }

    func logAutoProvisionLinkViewed(analyticsName: AnalyticsConstants.Screen.RegistrationV2) {
        analyticsService.logMayaEvents(name: analyticsName.viewed(),
                                       keyAttributes: [AnalyticsAttributeKey.screenName: analyticsName.autoProvisionLinkName(),
                                                       AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.accountProvisioning])
    }

    func logContinueButtonTapped() {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.accountProvisioning.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.accountProvisioning,
                                                       AnalyticsAttributeKey.destinationPage: AnalyticsConstants.Dialog.locationPermission,
                                                       AnalyticsAttributeKey.button: AnalyticsConstants.ButtonText.continue])
    }

    func logLocationPermissionViewed() {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.locationPermission.viewed(),
                                       keyAttributes: [AnalyticsAttributeKey.screenName: AnalyticsConstants.Dialog.locationPermission,
                                                       AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.accountProvisioning])
    }

    func logLocationPermissionButtonTapped(buttonText: String) {
        let locationCheckStatus = viewModel?.locationCheckerService.checkStatus.value.analyticsValue
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.locationPermission.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.Dialog.locationPermission,
                                                       AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.registrationCode,
                                                       AnalyticsAttributeKey.location: locationCheckStatus,
                                                       AnalyticsAttributeKey.button: buttonText])
    }

    func logErrorDialogViewed(_ errorViewModel: ErrorAlertViewModel) {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.error.viewed(),
                                       keyAttributes: [AnalyticsAttributeKey.screenName: AnalyticsConstants.Dialog.registrationError,
                                                       AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.accountProvisioning,
                                                       AnalyticsAttributeKey.reason: errorViewModel.message,
                                                       AnalyticsAttributeKey.errorCode: errorViewModel.error?.code])
    }

    func logErrorDialogButtonTapped(_ errorViewModel: ErrorAlertViewModel) {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.error.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.Dialog.registrationError,
                                                       AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.accountProvisioning,
                                                       AnalyticsAttributeKey.button: errorViewModel.closeButtonTitle])
    }
}
