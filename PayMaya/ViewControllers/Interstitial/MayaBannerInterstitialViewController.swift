//
//  MayaBannerInterstitialViewController.swift
//  PayMaya
//
//  Created by <PERSON> on 6/5/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Injector
import ReactiveSwift
import UIKit

class MayaBannerInterstitialViewController: MayaInterstitialViewController {
    // MARK: IBOutlets
    @IBOutlet private weak var adCarouselView: UIView!
    @IBOutlet private weak var stickyTermsContainerView: UIView!
    @IBOutlet private weak var stickyTermsTextView: UITextView!

    // MARK: Private properties
    private var adCarouselViewController: MayaAdCarouselViewController?
    private var bannerViewModel: MayaBannerInterstitialViewModelProtocol? { viewModel as? MayaBannerInterstitialViewModelProtocol }

    // MARK: View controller lifecycle
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        if let bannerViewModel {
            analyticsService.logMayaEvents(.appear(Analytics.CreatorStoreInterstitial()), attributes: bannerViewModel.appearAnalyticsAttributes)
        }

        if let adCarouselViewController {
            adCarouselViewController.viewModel?.initializeAdBannerService()
            adCarouselViewController.refreshBanner()
        }
    }

    override func binding(_ viewModel: MayaInterstitialViewModelProtocol?) {
        super.binding(viewModel)

        guard let bannerViewModel else { return }

        if let adCarouselViewController {
            let adCarouselViewModel = bannerViewModel.adCarouselViewModel
            adCarouselViewController.bind(adCarouselViewModel)
        }

        if let footerText = bannerViewModel.footerText {
            stickyTermsTextView.setLink(
                attributedText: footerText,
                linkColor: CommonAsset.MayaColors.Content.contentPrimaryGreen.color,
                linkFont: CommonFontFamily.CerebriSansPro.book.font(size: 12)!
            )
        } else {
            stickyTermsContainerView.isHidden = true
        }
    }

    override func binding() {
        super.binding()
        setupTermsLabel()
        setupBanners()
    }

    override func setupCloseButton() {
        guard let leftBarButton = navigationItem.leftBarButtonItem else { return }
        leftBarButton.image = CommonAsset.Images.Icons.iconSystemCross.image.withRenderingMode(.alwaysOriginal)
    }
}

// MARK: Setup methods
extension MayaBannerInterstitialViewController {
    private func setupTermsLabel() {
        stickyTermsContainerView.isShown = isFooterSticky
        stickyTermsTextView.removePaddings()
        stickyTermsTextView.delegate = self
    }

    private func setupBanners() {
        let adCarouselViewController = StoryboardScene.Home.mayaAdCarouselViewController.instantiate()
        adCarouselViewController.delegate = self
        adCarouselViewController.navigationBarHidden = false
        self.adCarouselViewController = adCarouselViewController

        add(child: adCarouselViewController, to: adCarouselView, padding: 0)
    }
}

// MARK: UITextViewDelegate methods
extension MayaBannerInterstitialViewController: UITextViewDelegate {
    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        if let viewModel, let tapURLModule = viewModel.tapURLModules[URL] {
            analyticsService.logMayaEvents(action: .tap(tapURLModule))
        }
        delegate?.viewController(self, didTapURL: URL)
        return false
    }
}

// MARK: AdCarouselViewControllerDelegate methods
extension MayaBannerInterstitialViewController: AdCarouselViewControllerDelegate {
    func didTapAdBanner(_ viewController: ViewController, link: URL) {
        delegate?.viewController(self, didTapURL: link)
    }

    func didChangeBannerState(_ viewController: ViewController, state: AdBannerState) {
        adCarouselView.isShown = state != .failed
    }
}
