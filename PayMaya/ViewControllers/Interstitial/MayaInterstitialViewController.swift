//
//  MayaInterstitialViewController.swift
//  PayMaya
//
//  Created by <PERSON> on 4/12/23.
//  Copyright © 2023 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import Injector
import UIKit

protocol MayaInterstitialViewControllerDelegate: AnyObject {
    func viewController(_ viewController: MayaInterstitialViewController, didTapURL url: URL)
    func viewControllerDidTapContinue(_ viewController: MayaInterstitialViewController)
}

class MayaInterstitialViewController: ViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    @IBOutlet private weak var scrollView: UIScrollView!
    @IBOutlet private weak var scrollViewContainerView: UIView!

    @IBOutlet private weak var iconImageView: UIImageView!
    @IBOutlet private weak var titleLabel: UILabel!
    @IBOutlet private weak var messageStackView: UIStackView!

    @IBOutlet private weak var stickyContinueContainerView: UIView!
    @IBOutlet private weak var stickyContinueButton: MayaButton!
    @IBOutlet private weak var scrollingContinueContainerView: UIView!
    @IBOutlet private weak var scrollingContinueButton: MayaButton!

    var isFooterSticky: Bool {
        guard let viewModel else {
            return false
        }
        // Adjust logic to consider height of footer view in MayaBannerInsterstitialViewController
        let willScrollViewScroll = scrollViewContainerView.frame.height > scrollView.frame.height
        return viewModel.isButtonSticky || !willScrollViewScroll
    }

    weak var delegate: MayaInterstitialViewControllerDelegate?

    override var shouldUseMayaNavigationBar: Bool { true }

    override func viewWillAppear(_ animated: Bool) {
        if let viewModel {
            setModule(viewModel.analyticsModule)
            setKeyAttributes(viewModel.analyticsAttributes)
        }
        super.viewWillAppear(animated)
        setupCloseButton()
        setupContinueButton()
    }

    override func didTapBack(_ sender: Any) {
        if let viewModel {
            analyticsService.logMayaEvents(action: .tap(viewModel.tapBackModule), keyAttributes: viewModel.analyticsAttributes)
        }
        super.didTapBack(sender)
    }

    @IBAction func didTapContinue() {
        guard let viewModel else { return }

        viewModel.setInterstitialShown()
        analyticsService.logMayaEvents(action: .tap(viewModel.tapContinueModule), keyAttributes: viewModel.analyticsAttributes)

        delegate?.viewControllerDidTapContinue(self)
    }

    func binding(_ viewModel: MayaInterstitialViewModelProtocol?) {
        guard let viewModel else { return }

        title = viewModel.navigationTitle
        iconImageView.image = viewModel.iconImage.image
        titleLabel.text = viewModel.title

        for message in viewModel.messages {
            let messageView = MayaInterstitialMessageView()
            messageView.delegate = self
            messageView.bind(message: message)
            messageStackView.addArrangedSubview(messageView)
        }
    }

    func binding() {
        // Do nothing
    }

    func setupCloseButton() {
        // Do nothing (for now), transfer logic from MayaBannerInstitialViewController
    }
}

private extension MayaInterstitialViewController {
    func setupContinueButton() {
        guard let viewModel else { return }

        if isFooterSticky {
            stickyContinueContainerView.isShown = true
            stickyContinueButton.setTitle(viewModel.buttonText, for: .normal)
            scrollingContinueContainerView.isHidden = true
        } else {
            scrollingContinueContainerView.isShown = true
            scrollingContinueButton.setTitle(viewModel.buttonText, for: .normal)
            stickyContinueContainerView.isHidden = true
        }
    }
}

// MARK: ViewControllerBindable methods
extension MayaInterstitialViewController: ViewControllerBindable {
    // binding() transferred to main body to allow method to be overriden
}

// MARK: - ViewModelBindable methods
extension MayaInterstitialViewController: ViewModelBindable {
    // binding(_:) transferred to main body to allow method to be overriden
}

// MARK: - MayaInterstitialMessageViewDelegate methods
extension MayaInterstitialViewController: MayaInterstitialMessageViewDelegate {
    func messageView(_ view: MayaInterstitialMessageView, didTapURL url: URL) {
        if let viewModel, let tapURLModule = viewModel.tapURLModules[url] {
            analyticsService.logMayaEvents(action: .tap(tapURLModule))
        }
        delegate?.viewController(self, didTapURL: url)
    }
}
