//
//  MayaCreditAssignContactReferenceViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 10/3/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Injector
import ReactiveSwift
import UIKit

protocol MayaCreditAssignContactReferenceViewControllerDelegate: AnyObject {
    func didTapLearnMore(_ viewController: MayaCreditAssignContactReferenceViewController)
    func didTapContactRelationship(_ viewController: MayaCreditAssignContactReferenceViewController, personalDetailsSelection: PersonalDetailsSelection)
    func didTapMobileNumber(_ viewController: MayaCreditAssignContactReferenceViewController, personalDetailsSelection: PersonalDetailsSelection)
    func didTapContinue(_ viewController: MayaCreditAssignContactReferenceViewController)
}

class MayaCreditAssignContactReferenceViewController: <PERSON>sViewController, AnalyticsServiceProtocol, MayaProgressBarProtocol {
    @Inject var analyticsService: AnalyticsService

    var segment: Int = 2

    weak var delegate: MayaCreditAssignContactReferenceViewControllerDelegate?

    @IBOutlet weak var headerLabel: UILabel!
    @IBOutlet weak var subheaderLabel: UILabel!
    @IBOutlet weak var learnMoreButton: MayaButton!
    @IBOutlet weak var continueButton: MayaButton!

    @IBOutlet weak var firstNameFieldView: MayaValidationFieldView! {
        didSet {
            setFirstNameFieldView()
        }
    }

    @IBOutlet weak var lastNameFieldView: MayaValidationFieldView! {
        didSet {
            setLastNameFieldView()
        }
    }

    @IBOutlet weak var contactRelationshipFieldView: MayaValidationFieldView! {
        didSet {
            setContactRelationshipFieldView()
        }
    }

    @IBOutlet weak var mobileNumberFieldView: MayaValidationFieldView! {
        didSet {
            setMobileNumberFieldView()
        }
    }

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override var mayaNavigationBarScrollView: UIScrollView? {
        return scrollView
    }

    var firstNameTextField: TextField {
        return firstNameFieldView.textField
    }

    var lastNameTextField: TextField {
        return lastNameFieldView.textField
    }

    var contactRelationshipTextField: TextField {
        return contactRelationshipFieldView.textField
    }

    var mobileNumberTextField: TextField {
        return mobileNumberFieldView.textField
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        continueButton.isEnabled = false

        let tap = UITapGestureRecognizer(target: self, action: #selector(onTapParentView))
        view.addGestureRecognizer(tap)

        setupView()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        guard viewModel?.contactReferenceState == nil else { return }
        setRightButtonItemTitle("2/3")
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        scrollView.setContentOffset(.zero, animated: true)
    }

    override func setupAccessibilityIdentifiers() {
        navigationItem.leftBarButtonItem?.isAccessibilityElement = true
        navigationItem.leftBarButtonItem?.accessibilityIdentifier = "pmios_maya_credit_activation_back_button"
    }

    @IBAction func showLearnMore(_ sender: Any) {
        delegate?.didTapLearnMore(self)
    }

    @IBAction func didTapContinue(_ sender: Any) {
        if viewModel?.contactReferenceState != nil {
            viewModel?.submitContactReference.apply().start()
        } else {
            delegate?.didTapContinue(self)
        }
    }

    private func setFirstNameFieldView() {
        firstNameFieldView.setTextFieldProperties(capitalizationType: .words)
        firstNameFieldView.textField.delegate = self
        firstNameFieldView.textField.autocorrectionType = .no
        firstNameFieldView.textField.spellCheckingType = .no
    }

    private func setLastNameFieldView() {
        lastNameFieldView.setTextFieldProperties(capitalizationType: .words)
        lastNameFieldView.textField.delegate = self
        lastNameFieldView.textField.autocorrectionType = .no
        lastNameFieldView.textField.spellCheckingType = .no
    }

    private func setContactRelationshipFieldView() {
        contactRelationshipFieldView.setTextFieldProperties(capitalizationType: .words)
        contactRelationshipFieldView.showChevron()
        contactRelationshipFieldView.textField.delegate = self
    }

    private func setMobileNumberFieldView() {
        mobileNumberFieldView.setTextFieldProperties(capitalizationType: .words)
        mobileNumberFieldView.firstActionImage = CommonAsset.Images.Icons.iconContact.image
        mobileNumberFieldView.textField.delegate = self
        mobileNumberFieldView.firstActionImageHandler = { [weak self] in
            guard let self = self else { return }
            guard let viewModel = self.viewModel else { return }
            self.delegate?.didTapMobileNumber(self, personalDetailsSelection: viewModel.getPersonalDetailsSelection())
        }
    }

    @objc private func onTapParentView(_ gesture: UIGestureRecognizer) {
        firstNameTextField.resignFirstResponder()
        lastNameTextField.resignFirstResponder()
    }
}

// MARK: - ViewModelBindable
extension MayaCreditAssignContactReferenceViewController: ViewModelBindable {
    func binding(_ viewModel: MayaCreditAssignContactReferenceViewModelProtocol?) {
        guard let viewModel = viewModel else { return }

        var header: String {
            switch viewModel.contactReferenceState {
            case .add:
                return L10n.Maya.Credit.ContactReference.Header.add
            case .edit:
                return L10n.Maya.Credit.ContactReference.Header.edit
            default:
                return L10n.Maya.Credit.ContactReference.Header.signup
            }
        }
        headerLabel.text = header

        if let contactReference = viewModel.contactReference?.contactReference {
            viewModel.firstNameField.mutableProperty.value = contactReference.firstName
            viewModel.lastNameField.mutableProperty.value = contactReference.lastName
            viewModel.contactRelationship.value = viewModel.selectedContactRelationship?.value ?? ""
            viewModel.mobileNumber.value = contactReference.mobileNumber
        }

        viewModel.firstNameField.mutableProperty <~ firstNameTextField.reactive.continuousTextValues
        viewModel.lastNameField.mutableProperty <~ lastNameTextField.reactive.continuousTextValues
        viewModel.contactRelationshipField.mutableProperty <~ contactRelationshipTextField.reactive.continuousTextValues
        viewModel.mobileNumberField.mutableProperty <~ mobileNumberTextField.reactive.continuousTextValues

        firstNameFieldView.bindFieldProperty(viewModel.firstNameField)
        lastNameFieldView.bindFieldProperty(viewModel.lastNameField)
        contactRelationshipFieldView.bindFieldProperty(viewModel.contactRelationshipField)
        mobileNumberFieldView.bindFieldProperty(viewModel.mobileNumberField)

        viewModel.contactRelationship.signal.observe(on: UIScheduler()).observeValues { [weak self] contactRelationship in
            guard let self = self else { return }
            self.contactRelationshipTextField.text = contactRelationship
        }?.addToDisposeBag(disposeBag)

        viewModel.mobileNumber.signal.observe(on: UIScheduler()).observeValues { [weak self] phoneNumber in
            guard let self = self else { return }
            self.mobileNumberTextField.text = phoneNumber
        }?.addToDisposeBag(disposeBag)

        viewModel.isButtonEnabled.signal
            .observe(on: UIScheduler())
            .observeValues({ [weak self] isEnabled in
                guard let self = self else { return }
                self.continueButton.isEnabled = isEnabled
            })?.addToDisposeBag(disposeBag)

        firstNameTextField.text = viewModel.firstNameField.value
        lastNameTextField.text = viewModel.lastNameField.value
        mobileNumberTextField.text = viewModel.mobileNumber.value
        contactRelationshipTextField.text = viewModel.contactRelationship.value
        continueButton.isEnabled = viewModel.isButtonEnabled.value

        formFields = [firstNameFieldView, lastNameFieldView, contactRelationshipFieldView, mobileNumberFieldView]
        setKeyboardReturnTypeOfTextFields()
        lastNameFieldView.textField.returnKeyType = .done
        lastFormFieldCallback = { [weak self] in
            guard let self = self else { return }

            self.view.endEditing(true)
        }

        viewModel.submitContactReference.values.observe(on: UIScheduler()).observeValues { [weak self] _ in
            guard let self = self else { return }
            self.backActionDelegate?.didTapBack(self)
        }?.addToDisposeBag(disposeBag)

        viewModel.submitContactReference.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            executing ? self.showMayaLoader(nil, CommonStrings.Maya.Loader.Default.message) : self.hideMayaLoader()
        }?.addToDisposeBag(disposeBag)

        viewModel.submitContactReference.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self else { return }
            switch error.type {
            case .sessionTimeout, .noInternetConnection:
                break
            default:
                self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                    AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                    AnalyticsAttributeKey.error.rawValue: error.type
                ])
            }
            if let errorViewModel = error.viewModel {
                self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
            } else {
                self.analyticsService.logMayaEvents(action: .failure(Analytics.NewMayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: ""])
            }
        }?.addToDisposeBag(disposeBag)
    }
}

private extension MayaCreditAssignContactReferenceViewController {
    private func setupView() {
        subheaderLabel.text = L10n.Maya.Credit.Assign.Contact.Reference.subtitle

        firstNameFieldView.hintText = CommonStrings.Common.firstname
        lastNameFieldView.hintText = CommonStrings.Common.lastname
        contactRelationshipFieldView.hintText = L10n.Maya.Credit.Assign.Contact.Reference.contactRelationship
        mobileNumberFieldView.hintText = CommonStrings.Common.Mobile.number

        learnMoreButton.setTitle(CommonStrings.Common.learnmore, for: .normal)
        continueButton.setTitle(CommonStrings.Common.continue, for: .normal)
    }
}

// MARK: - UITextFieldDelegate Methods
extension MayaCreditAssignContactReferenceViewController {
    override func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        if let char = string.cString(using: String.Encoding.utf8) {
            let isBackSpace = strcmp(char, "\\b")
            if isBackSpace == -92 {
                return true
            }
        }

        let currentText = textField.text ?? ""
        guard let stringRange = Range(range, in: currentText) else { return false }
        let combinedText = currentText.replacingCharacters(in: stringRange, with: string)

        guard !combinedText.isEmpty else { return true }

        if textField === firstNameTextField || textField === lastNameTextField {
            return combinedText.isValidName()
        }

        return true
    }

    func textFieldShouldBeginEditing(_ textField: UITextField) -> Bool {
        if textField == contactRelationshipTextField {
            view.endEditing(true)
            guard let viewModel = self.viewModel else { return false }
            self.delegate?.didTapContactRelationship(self, personalDetailsSelection: viewModel.getPersonalDetailsSelection())
            return false
        } else if textField == mobileNumberTextField {
            view.endEditing(true)
            guard let viewModel = self.viewModel else { return false }
            self.delegate?.didTapMobileNumber(self, personalDetailsSelection: viewModel.getPersonalDetailsSelection())
            return false
        }

        return true
    }
}
