//
//  MayaCreditInitialEligibilityViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 9/15/23.
//  Copyright © 2023 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Foundation
import Injector
import ReactiveSwift

protocol MayaCreditInitialEligibilityViewControllerDelegate: AnyObject {
    func didTapApplyNow(_ viewController: MayaCreditInitialEligibilityViewController)
    func willStartMayaCreditSignUpFlow(_ viewController: MayaCreditInitialEligibilityViewController, initialEligibility: MayaCreditInitialEligibility)
    func willShowIneligibleView(_ viewController: MayaCreditInitialEligibilityViewController)
}

class MayaCreditInitialEligibilityViewController: ViewController, AnalyticsServiceProtocol {
    @Inject var analyticsService: AnalyticsService

    @IBOutlet weak var backgroundView: UIView!
    @IBOutlet weak var comingSoonLabel: UILabel!

    weak var delegate: MayaCreditInitialEligibilityViewControllerDelegate?

    @IBAction func didTapApplyNow(_ sender: Any) {
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditInitialEligibility.introContinue))
        delegate?.didTapApplyNow(self)
    }

    override var shouldUseAnimatedNavigationBar: Bool {
        return true
    }

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override var navigationBarBackgroundColor: UIColor {
        return UIColor.clear
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupBackgroundGradient()
        comingSoonLabel.setTextSpacingBy(value: 1)
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationItem.rightBarButtonItem?.tintColor = CommonAsset.MayaColors.Primary.primaryBlack.color
    }

    override func setupAccessibilityIdentifiers() {
        navigationItem.rightBarButtonItem?.isAccessibilityElement = true
        navigationItem.rightBarButtonItem?.accessibilityIdentifier = "pmios_maya_credit_initial_eligibility_interstitial_back_button"
    }

    private func setupBackgroundGradient() {
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [UIColor(red: 212, green: 230, blue: 255).cgColor, CommonAsset.MayaColors.Primary.primaryWhite.color.cgColor]
        gradientLayer.frame = backgroundView.bounds
        gradientLayer.locations = [0, 0.6]
        view.layer.insertSublayer(gradientLayer, at: 0)
    }
}

extension MayaCreditInitialEligibilityViewController: ViewModelBindable {
    func binding(_ binding: MayaCreditInitialEligibilityViewModelProtocol?) {
        guard let viewModel = viewModel else { return }

        viewModel.getMayaCreditConsentsAction.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            if executing {
                self.showMayaLoader()
            }
        }?.addToDisposeBag(disposeBag)

        viewModel.getMayaCreditConsentsAction.values
            .observe(on: UIScheduler())
            .observeValues { consents in
                viewModel.submitConsentAction.apply(consents).start()
            }?.addToDisposeBag(disposeBag)

        viewModel.getMayaCreditConsentsAction.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self = self else { return }
                self.hideMayaLoader()
                switch error.type {
                case .sessionTimeout:
                    return
                case .noInternetConnection:
                    break
                default:
                    self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                        AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                        AnalyticsAttributeKey.error.rawValue: error.type
                    ])
                }
                self.showCustomErrorAlert(
                    message: L10n.Maya.Credit.Signup.Interstitial.Consent.Error.message,
                    onTryAgain: {
                        viewModel.getMayaCreditConsentsAction.apply().start()
                    })
            }?.addToDisposeBag(disposeBag)

        viewModel.submitConsentAction.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            if executing {
                self.showMayaLoader()
            } else {
                self.hideMayaLoader()
            }
        }?.addToDisposeBag(disposeBag)

        viewModel.submitConsentAction.completed.observe(on: UIScheduler()).observe { _ in
            let rrn = UUID().uuidString
            viewModel.loanApplicationInitialEligibility.apply(rrn).start()
        }?.addToDisposeBag(disposeBag)

        viewModel.submitConsentAction.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self else { return }
            switch error.type {
            case .sessionTimeout:
                return
            case .noInternetConnection:
                break
            default:
                self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                    AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                    AnalyticsAttributeKey.error.rawValue: error.type
                ])
            }
            self.showCustomErrorAlert(
                message: L10n.Maya.Credit.Signup.Interstitial.Consent.Error.message,
                onTryAgain: {
                    viewModel.getMayaCreditConsentsAction.apply().start()
                })
        }?.addToDisposeBag(disposeBag)

        viewModel.loanApplicationInitialEligibility.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] executing in
                guard let self = self else { return }
                if executing {
                    self.showMayaLoader()
                } else {
                    self.hideMayaLoader()
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.loanApplicationInitialEligibility.values
            .observe(on: UIScheduler())
            .observeValues { [weak self] eligibility in
                guard let self = self else { return }
                self.delegate?.willStartMayaCreditSignUpFlow(self, initialEligibility: eligibility)
            }?.addToDisposeBag(disposeBag)

        viewModel.loanApplicationInitialEligibility.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self = self else { return }
                switch error.type {
                case .mcIneligible:
                    self.delegate?.willShowIneligibleView(self)
                    self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                        AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                        AnalyticsAttributeKey.error.rawValue: error.type
                    ])
                case .sessionTimeout:
                    return
                case .noInternetConnection:
                    break
                default:
                    self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                        AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                        AnalyticsAttributeKey.error.rawValue: error.type
                    ])
                }
                self.showCustomErrorAlert(
                    message: L10n.Maya.Credit.Signup.Interstitial.InitialEligibility.Error.message,
                    onTryAgain: {
                        let rrn = UUID().uuidString
                        viewModel.loanApplicationInitialEligibility.apply(rrn).start()
                    })
            }?.addToDisposeBag(disposeBag)
    }
}

extension MayaCreditInitialEligibilityViewController {
    private func showCustomErrorAlert(message: String, onTryAgain: @escaping (() -> Void)) {
        let alertViewModel = MayaAlertViewModel(title: L10n.Maya.Credit.Signup.Interstitial.Error.title, message: message, image: CommonAsset.Images.Alert.imageLarger3DWarning.image, closeButtonTitle: CommonStrings.Common.cancel)
        let tryAgainAction = MayaAlertAction(title: CommonStrings.Common.Try.again, style: .primary, handler: {
            self.dismissMayaAlertModal(viewModel: alertViewModel, viewController: self, completion: onTryAgain)
        })
        alertViewModel.addAction(tryAgainAction)
        self.showMayaAlertModal(viewModel: alertViewModel)
    }
}
