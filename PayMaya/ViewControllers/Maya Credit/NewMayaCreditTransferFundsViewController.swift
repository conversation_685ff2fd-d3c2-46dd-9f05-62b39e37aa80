//
//  NewMayaCreditTransferFundsViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 08/04/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Foundation
import Injector
import ReactiveCocoa
import ReactiveSwift
import UIKit

protocol NewMayaCreditTransferFundsViewControllerDelegate: AnyObject {
    func didSuccessfullyCreateTransfer(_ viewController: NewMayaCreditTransferFundsViewController, creditAccount: MayaCreditAccount, creditFeesAndTaxes: MayaCreditFeesAndTaxes?, amount: String, rrn: String)
    func didTapViewServiceFee(_ viewController: NewMayaCreditTransferFundsViewController)
}

class NewMayaCreditTransferFundsViewController: FormsViewController, MayaAnalyticsDurationManagerProtocol, MayaValidationFieldViewAccessoryViewDelegate, MayaProgressBarProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject private var locationAuthorizerService: LocationAuthorizerServiceProtocol

    @IBOutlet weak var sampleFeeTextView: UITextView!
    @IBOutlet weak var mayaValidationField: MayaValidationFieldView! {
        didSet {
            mayaValidationField.setTextFieldProperties(keyboardType: .decimalPad)
        }
    }
    @IBOutlet weak var confirmationView: MayaConfirmationView!
    @IBOutlet weak var creditLimitLabel: UILabel!
    @IBOutlet weak var walletCashLimitLabel: UILabel!

    @IBOutlet weak var transferHeaderLabel: UILabel!
    @IBOutlet weak var availableCreditLimitInfoView: UIView!
    @IBOutlet weak var feesInfoView: UIView!
    @IBOutlet weak var fromDetailsView: View!
    @IBOutlet weak var creditBalanceLabel: UILabel!
    @IBOutlet weak var accountNumberLabel: UILabel!

    weak var delegate: NewMayaCreditTransferFundsViewControllerDelegate?

    var segment = 1
    var ammountTextField: TextField {
        return mayaValidationField.textField
    }
    private let url = URL(string: "mayacredit/showSample")!

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        self.locationAuthorizerService.setupService(delegate: self)
        setupView()
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.NewMayaCreditTransferCredit())
        super.viewWillAppear(animated)
        setRightButtonItemTitle("1/2")
    }

    override func setupAccessibilityIdentifiers() {
        navigationItem.leftBarButtonItem?.isAccessibilityElement = true
        navigationItem.leftBarButtonItem?.accessibilityIdentifier = "pmios_maya_credit_transfer_funds_back_button"
    }
}

// MARK: - NewMayaCreditTransferFundsViewController setupView function
private extension NewMayaCreditTransferFundsViewController {
    private func setupView() {
        sampleFeeTextView.delegate = self

        let attributedString = NSMutableAttributedString(
            string: L10n.Maya.Credit.Transfer.Fee.sample,
            attributes: [
                .foregroundColor: CommonAsset.MayaColors.Grey.grey5.color,
                .font: CommonFontFamily.Jeko.semiBold.font(size: 12)!
            ]
        )

        attributedString.addAttribute(.link, value: url, range: attributedString.mutableString.range(of: L10n.Maya.Credit.Transfer.Fee.Sample.green))

        sampleFeeTextView.attributedText = attributedString

        sampleFeeTextView.contentInset = .zero
        sampleFeeTextView.textContainerInset = .zero
        sampleFeeTextView.textContainer.lineFragmentPadding = 0
        sampleFeeTextView.linkTextAttributes = [.foregroundColor: CommonAsset.MayaColors.Primary.primaryGrownGreen.color]
    }

    private func createAttributedString(source: String, label: String, color: UIColor = CommonAsset.MayaColors.Grey.grey5.color, bold: Bool = false) -> NSMutableAttributedString {
        let textColor = CommonAsset.MayaColors.Grey.grey5.color
        let attributedString = NSMutableAttributedString(
            string: source,
            attributes: [
                .foregroundColor: textColor,
                .font: CommonFontFamily.Jeko.semiBold.font(size: 12)!
            ]
        )
        attributedString.addAttribute(.foregroundColor, value: color, range: attributedString.mutableString.range(of: label))

        if bold {
            attributedString.addAttribute(.font, value: CommonFontFamily.Jeko.bold.font(size: 12)!, range: attributedString.mutableString.range(of: label))
        }

        return attributedString
    }

    @objc private func didTapMayaFeeSampleLabel( _ gesture: UITapGestureRecognizer) {
        delegate?.didTapViewServiceFee(self)
    }
}

// MARK: - ViewControllerBindable
extension NewMayaCreditTransferFundsViewController: ViewControllerBindable {
    func binding() {
        willTriggerActionOnCallBack = false
        willAutomaticallyDismissKeyboardOnActiveSignal = false
        formFields = [mayaValidationField]
        lastFormFieldCallback = { [weak self] in
            self?.view.endEditing(true)
        }
    }
}

// MARK: - ViewModelBindable
extension NewMayaCreditTransferFundsViewController: ViewModelBindable {
    func binding(_ viewModel: NewMayaCreditTransferFundsViewModelProtocol?) {
        guard let viewModel = viewModel else { return }
        var attributes: [AnalyticsAttributeKey: Any] = [AnalyticsAttributeKey.design: Constants.MayaCredit.designV2_1]
        let extractAttributes = [
            AnalyticsAttributeKey.triggeredBy: Constants.Trigger.lending,
            AnalyticsAttributeKey.eventTrigger: Constants.EventTrigger.credit
        ]
        let geolocAttributes = [
            AnalyticsAttributeKey.enabled: viewModel.isGeolocationEnabled
        ]
        var rrn: String = String.empty

        transferHeaderLabel.text = L10n.Maya.Credit.Transfer.ToWallet.title
        availableCreditLimitInfoView.isHidden = true
        feesInfoView.isHidden = true
        fromDetailsView.isHidden = false
        creditBalanceLabel.text = viewModel.availableCreditLimitValue
        accountNumberLabel.text = L10n.Maya.Credit.Transfer.ToWallet.accountNumber(viewModel.accountNumberLastFourDigits)
        confirmationView.continueButton.isEnabled = false

        viewModel.getAccountLimitsAction.apply().start()

        viewModel.amountProperty.mutableProperty <~ mayaValidationField.textField.reactive.continuousTextValues.map({ amount in
            return amount.filter("**********.".contains)
        })

        creditLimitLabel.attributedText = createAttributedString(source: L10n.Maya.Credit.Transfer.Credit.limit(viewModel.availableCreditLimitValue), label: viewModel.availableCreditLimitValue, bold: true)

        mayaValidationField.bindFieldProperty(viewModel.amountProperty)

        viewModel.createTransferAction.values.observe(on: UIScheduler()).observeValues { [weak self] _ in
            guard let self = self else { return }
            rrn = UUID().uuidString.lowercased()
            viewModel.calculateDisbursementAction.apply((rrn, viewModel.amountProperty.value ?? "")).start()
        }?.addToDisposeBag(disposeBag)

        viewModel.isCashInLimitHidden.signal.observe(on: UIScheduler()).observeValues({ [weak self] isHidden in
            guard let self = self else { return }
            self.setCashInLimitHidden(isHidden)
        })?.addToDisposeBag(disposeBag)

        viewModel.calculateDisbursementAction.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            executing ? self.showMayaLoader() : self.hideMayaLoader()
        }?.addToDisposeBag(disposeBag)

        viewModel.calculateDisbursementAction.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self else { return }
            switch error.type {
            case .sessionTimeout, .noInternetConnection:
                break
            default:
                self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                    AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                    AnalyticsAttributeKey.error.rawValue: error.type
                ])
            }
            if let errorViewModel = error.viewModel {
                self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
            }
        }?.addToDisposeBag(disposeBag)

        viewModel.calculateDisbursementAction.values
            .observe(on: UIScheduler())
            .observeValues { [weak self] feesAndTaxes in
            guard let self = self else { return }
            self.delegate?.didSuccessfullyCreateTransfer(self, creditAccount: viewModel.creditAccount, creditFeesAndTaxes: feesAndTaxes, amount: viewModel.amountProperty.value ?? "", rrn: rrn)
            }?.addToDisposeBag(disposeBag)

        confirmationView.continueButton.reactive.controlEvents(.touchUpInside)
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self else { return }
                if viewModel.isGeolocationEnabled {
                    viewModel.handleRavenGetGeolocation.apply().start()
                } else {
                    viewModel.createTransferAction.apply().start()
                }
                analyticsService.logMayaEvents(action: .tap(Analytics.NewMayaCreditTransferCredit.next), keyAttributes: attributes)
                analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditDrawdown.geolocation), keyAttributes: geolocAttributes)
            }?.addToDisposeBag(disposeBag)

        viewModel.isContinueButtonEnabled.signal
            .observe(on: UIScheduler())
            .observeValues({ [weak self] isEnabled in
                guard let self = self else { return }
                self.confirmationView.continueButton.isEnabled = isEnabled
            })?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.values
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self else { return }
                if viewModel.isLendingDataExtractionEnabled {
                    viewModel.handleRavenExtract.apply().start()
                } else {
                    viewModel.createTransferAction.apply().start()
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] err in
                guard let self else { return }
                switch err {
                case .notDetermined:
                    locationAuthorizerService.requestAuthorization()

                    let attributes = [
                        AnalyticsAttributeKey.sourcePage: AnalyticsConstants.Screen.MECDrawdown.page.rawValue,
                        AnalyticsAttributeKey.sourceButton: AnalyticsConstants.Screen.MECDrawdown.Button.continue.rawValue
                    ]
                    analyticsService.logMayaEvents(action: .requested(Analytics.MayaCreditDrawdown.geolocation), keyAttributes: attributes)
                case .denied, .restricted, .unknown:
                    // continue flow even if user denied permission
                    viewModel.createTransferAction.apply().start()
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenExtract.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] executing in
                guard let self else { return }
                if executing {
                    showMayaLoader()
                } else {
                    hideMayaLoader()
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenExtract.completed
            .observe(on: UIScheduler())
            .observeValues { [weak self] in
                guard let self else { return }
                viewModel.createTransferAction.apply().start()
                analyticsService.logMayaEvents(action: .success(Analytics.MayaDataExtraction.extract), keyAttributes: extractAttributes)
            }?.addToDisposeBag(disposeBag)

        // even on error, proceed to create transfer
        viewModel.handleRavenExtract.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self else { return }
                viewModel.createTransferAction.apply().start()
                analyticsService.logMayaEvents(action: .failure(Analytics.MayaDataExtraction.extract), keyAttributes: extractAttributes)
            }?.addToDisposeBag(disposeBag)
    }

    private func setCashInLimitHidden(_ isHidden: Bool) {
        if !isHidden {
            walletCashLimitLabel.isHidden = false
            walletCashLimitLabel.attributedText = createAttributedString(source: L10n.Maya.Credit.Transfer.Wallet.limit(viewModel?.cashInLimitValue ?? ""), label: viewModel?.cashInLimitValue ?? "", bold: true)
        }
    }
}

// MARK: - UITextFieldDelegate
extension NewMayaCreditTransferFundsViewController {
    override func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        guard let viewModel = viewModel else { return true }
        if textField == mayaValidationField.textField {
            let text = viewModel.formatMayaAmount(with: String.unwrappedValue(textField.text).trim(), range: range, and: string)
            mayaValidationField.setFormattedAmount(with: text.newValue, offset: text.offset)
            return false
        }
        return true
    }

    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        analyticsService.logMayaEvents(action: .tap(Analytics.NewMayaCreditTransferCredit.viewSample))
        delegate?.didTapViewServiceFee(self)
        return false
    }
}

extension NewMayaCreditTransferFundsViewController: LocationAuthorizerServiceDelegate {
    func didDenyLocationAuthorization(didShowPrompt: Bool) {
        if didShowPrompt {
            viewModel?.createTransferAction.apply().start()

            let attributes: [AnalyticsAttributeKey: Any] = [
                AnalyticsAttributeKey.sourcePage: AnalyticsConstants.Screen.MECDrawdown.Prompt.location.rawValue,
                AnalyticsAttributeKey.sourceButton: AnalyticsConstants.Screen.MECDrawdown.Button.deny.rawValue
            ]
            analyticsService.logMayaEvents(action: .denied(Analytics.MayaCreditDrawdown.geolocation), keyAttributes: attributes)
        }
    }

    func didAcceptLocationAuthorization(didShowPrompt: Bool) {
        if didShowPrompt {
            viewModel?.handleRavenGetGeolocation.apply().start()

            let attributes: [AnalyticsAttributeKey: Any] = [
                AnalyticsAttributeKey.sourcePage: AnalyticsConstants.Screen.MECDrawdown.Prompt.location.rawValue,
                AnalyticsAttributeKey.sourceButton: AnalyticsConstants.Screen.MECDrawdown.Button.allow.rawValue
            ]
            analyticsService.logMayaEvents(action: .allowed(Analytics.MayaCreditDrawdown.geolocation), keyAttributes: attributes)
        }
    }

    func didNotDetermineLocationAuthorization() {
        // Do nothing
    }
}
