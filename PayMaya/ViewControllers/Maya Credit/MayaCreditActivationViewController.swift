//
//  MayaCreditActivationViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 10/11/21.
//  Copyright © 2021 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Injector
import ReactiveSwift
import UIKit

protocol MayaCreditActivationViewControllerDelegate: AnyObject {
    func didTapCustomizeBillingEndDate(_ viewController: MayaCreditActivationViewController, billingEndDateSelection: BillingEndDateSelection)
    func didTapLearnMore(_ viewController: MayaCreditActivationViewController)
    func didTapTermsAndConditions(_ viewController: MayaCreditActivationViewController, activationTerm: ActivationTerm)
    func didSuccessfullySubmitted(_ viewController: MayaCreditActivationViewController, response: OTPResponse)
}

class MayaCreditActivationViewController: ViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    @IBOutlet weak var containerView: UIView!
    @IBOutlet weak var amountLabel: UILabel!
    @IBOutlet weak var expiryLabel: UILabel!

    @IBOutlet weak var billingDueDateLabel: UILabel!
    @IBOutlet weak var billingEndDateLabel: UILabel!
    @IBOutlet weak var selectDayLabel: UILabel!
    @IBOutlet weak var interestLabel: UILabel!
    @IBOutlet weak var learnMoreLabel: UILabel!

    @IBOutlet weak var backUpIdentityLabel: UILabel!
    @IBOutlet weak var backUpIdentityBadgeImageView: UIImageView!

    @IBOutlet weak var termsAndConditionsView: UIStackView!

    var applicationTermViews = [MayaCreditApplicationTerm]()

    weak var delegate: MayaCreditActivationViewControllerDelegate?

    override var shouldUseAnimatedNavigationBar: Bool {
        return true
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        containerView.layer.masksToBounds = false
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOpacity = 0.15
        containerView.layer.shadowRadius = 12.0
        containerView.layer.shadowOffset = CGSize(width: 0.0, height: 4.0)

        let selectDay = NSMutableAttributedString(string: L10n.Maya.Credit.Application.Select.day)
        selectDay.addAttribute(NSAttributedString.Key.underlineStyle, value: true, range: NSRange(location: 0, length: selectDay.length))
        selectDay.addAttribute(NSAttributedString.Key.font, value: CommonFontFamily.WorkSans.medium.font(size: 14)!, range: NSRange(location: 0, length: selectDay.length))
        selectDay.addAttribute(NSAttributedString.Key.foregroundColor, value: CommonAsset.Colors.Constants.newGreen.color, range: NSRange(location: 0, length: selectDay.length))
        selectDayLabel.attributedText = selectDay

        let learnMore = NSMutableAttributedString(string: L10n.Maya.Credit.Application.Learn.more)
        learnMore.addAttribute(NSAttributedString.Key.underlineStyle, value: true, range: NSRange(location: 0, length: learnMore.length))
        learnMore.addAttribute(NSAttributedString.Key.font, value: CommonFontFamily.WorkSans.medium.font(size: 14)!, range: NSRange(location: 0, length: learnMore.length))
        learnMore.addAttribute(NSAttributedString.Key.foregroundColor, value: CommonAsset.Colors.Constants.newGreen.color, range: NSRange(location: 0, length: learnMore.length))
        learnMoreLabel.attributedText = learnMore
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.MayaCreditActivation())
        super.viewWillAppear(animated)
        navigationItem.leftBarButtonItem?.setTitleTextAttributes([
            NSAttributedString.Key.font: CommonFontFamily.WorkSans.semiBold.font(size: 16)!,
            NSAttributedString.Key.foregroundColor: CommonAsset.Colors.Constants.newBlack.color
        ], for: .normal)
        navigationController?.navigationBar.titleTextAttributes = [
            NSAttributedString.Key.font: CommonFontFamily.WorkSans.bold.font(size: 18)!,
            NSAttributedString.Key.foregroundColor: CommonAsset.Colors.Constants.newBlack.color
        ]
    }

    override func setupAccessibilityIdentifiers() {
        navigationItem.leftBarButtonItem?.isAccessibilityElement = true
        navigationItem.leftBarButtonItem?.accessibilityIdentifier = "pmios_maya_credit_activation_back_button"
    }

    @IBAction func didTapCustomizeBillingEndDate(_ sender: Any) {
        guard let viewModel = viewModel else { return }
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditActivation.setEndDate))
        delegate?.didTapCustomizeBillingEndDate(self, billingEndDateSelection: viewModel.getBillingEndDateSelection())
    }

    @IBAction func didTapLearnMore(_ sender: Any) {
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditActivation.serviceFee))
        delegate?.didTapLearnMore(self)
    }

    @IBAction func didTapSubmit(_ sender: Any) {
        guard let viewModel = viewModel else { return }
        for termView in applicationTermViews {
            termView.checkBoxButton.submit()
        }
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditActivation.activate))
        viewModel.submitLoanAction.apply().start()
    }
}

// MARK: - ViewModelBindable
extension MayaCreditActivationViewController: ViewModelBindable {
    func binding(_ viewModel: MayaCreditActivationViewModelProtocol?) {
        guard let viewModel = viewModel else { return }
        amountLabel.text = viewModel.amountWithCurrency
        expiryLabel.text = L10n.Maya.Credit.Application.Expires.on(viewModel.expiry)
        interestLabel.text = L10n.Maya.Credit.Application.Interest.rate(viewModel.feeRate)
        billingDueDateLabel.text = L10n.Maya.Credit.Application.Billing.Due.date(String(viewModel.billingDueDateValue))
        setBillingEndDateDisplay(viewModel.billingEndDateDisplay.value)

        backUpIdentityBadgeImageView.isHidden = viewModel.isBadgeHidden
        backUpIdentityLabel.text = viewModel.backupIdentity
        if viewModel.isBackupIdentityMissing {
            backUpIdentityLabel.font = CommonFontFamily.WorkSans.italic.font(size: 14)
        }

        viewModel.submitLoanAction.values.observe(on: UIScheduler()).observeValues { [weak self] response in
            guard let self = self else { return }
            self.analyticsService.logMayaEvents(action: .success(Analytics.MayaCreditActivation()))
            self.delegate?.didSuccessfullySubmitted(self, response: response)
        }?.addToDisposeBag(disposeBag)

        viewModel.submitLoanAction.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            executing ? self.showHud() : self.hideHud()
        }?.addToDisposeBag(disposeBag)

        viewModel.submitLoanAction.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self else { return }
            switch error.type {
            case .sessionTimeout(let message):
                self.analyticsService.logMayaEvents(action: .failure(Analytics.MayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: message])
            case .noInternetConnection:
                break
            default:
                self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                    AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                    AnalyticsAttributeKey.error.rawValue: error.type
                ])
            }
            if let errorViewModel = error.viewModel {
                self.analyticsService.logMayaEvents(action: .failure(Analytics.MayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: errorViewModel.message])
                self.showErrorAlert(viewModel: errorViewModel)
            } else {
                self.analyticsService.logMayaEvents(action: .failure(Analytics.MayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: ""])
            }
        }?.addToDisposeBag(disposeBag)

        viewModel.billingEndDateDisplay.signal.observe(on: UIScheduler()).observeValues { [weak self] display in
            guard let self = self else { return }
            self.setBillingEndDateDisplay(display)
        }?.addToDisposeBag(disposeBag)

        for activationTerm in viewModel.activationTerms {
            if let termView = Bundle.main.loadNibNamed("MayaCreditApplicationTerm", owner: self, options: nil)?.first as? MayaCreditApplicationTerm {
                termView.setProperties(activationTerm: activationTerm)
                termView.delegate = self
                applicationTermViews.append(termView)
                termsAndConditionsView.insertArrangedSubview(termView, at: applicationTermViews.count - 1)
            }
        }
    }

    private func setBillingEndDateDisplay(_ display: BillingEndDateDisplay) {
        if let value = display.value {
            switch display.state {
            case .normal:
                billingEndDateLabel.text = L10n.Maya.Credit.Application.Billing.End.date(value)
                billingEndDateLabel.textColor = CommonAsset.Colors.Constants.newLightGray.color
            case .error:
                billingEndDateLabel.text = value
                billingEndDateLabel.textColor = CommonAsset.Colors.Constants.newRed.color
            }
            billingEndDateLabel.isHidden = false
        } else {
            billingEndDateLabel.isHidden = true
        }
    }
}

// MARK: - MayaCreditApplicationTermDelegate
extension MayaCreditActivationViewController: MayaCreditApplicationTermDelegate {
    func didTapGetTermDetails(_ view: MayaCreditApplicationTerm) {
        if let activationTerm = view.activationTerm {
            analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditActivation.agreement), keyAttributes: [AnalyticsAttributeKey.documentType: activationTerm.term.key])
            delegate?.didTapTermsAndConditions(self, activationTerm: activationTerm)
        }
    }

    func didTapCheckBox(_ view: MayaCreditApplicationTerm) {
        if let activationTerm = view.activationTerm {
            analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditActivation.agreement), keyAttributes: [AnalyticsAttributeKey.documentType: activationTerm.term.key])
            if !activationTerm.viewModel.isConfirmedOnce {
                delegate?.didTapTermsAndConditions(self, activationTerm: activationTerm)
            } else {
                activationTerm.viewModel.isConfirmed.value = !activationTerm.viewModel.isConfirmed.value
            }
        }
    }
}
