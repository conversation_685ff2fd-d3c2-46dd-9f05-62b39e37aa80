//
//  NewMayaCreditTransferConfirmationViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 13/04/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Foundation
import Injector
import ReactiveCocoa
import ReactiveSwift

protocol NewMayaCreditTransferConfirmationViewControllerDelegate: AnyObject {
    func didSuccessfullyExecute(_ viewController: NewMayaCreditTransferConfirmationViewController, amount: String)
    func didRequestMFA(_ viewController: NewMayaCreditTransferConfirmationViewController, challengeId: String)
}

class NewMayaCreditTransferConfirmationViewController: ViewController, MayaAnalyticsDurationManagerProtocol, MayaProgressBarProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    @IBOutlet weak var mainCashLabel: UILabel!
    @IBOutlet weak var amountCashLabel: UILabel!
    @IBOutlet weak var confirmButton: MayaButton!

    @IBOutlet weak var transactionDetailsView: UIView!
    @IBOutlet weak var enhancedTransactionDetailsView: UIView!

    @IBOutlet weak var accountNumberValueLabel: UILabel!
    @IBOutlet weak var transferAmountValueLabel: UILabel!
    @IBOutlet weak var serviceFeeTitleLabel: UILabel!
    @IBOutlet weak var serviceFeeValueLabel: UILabel!
    @IBOutlet weak var documentStampTitleLabel: UILabel!
    @IBOutlet weak var documentStampValueLabel: UILabel!
    @IBOutlet weak var totalCreditAmountValueLabel: UILabel!
    @IBOutlet weak var mobileNumberValueLabel: UILabel!

    weak var delegate: NewMayaCreditTransferConfirmationViewControllerDelegate?

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    var segment = 2

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.NewMayaCreditTransferCreditConfirmation())
        super.viewWillAppear(animated)
        setRightButtonItemTitle("2/2")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
    }

    override func setupAccessibilityIdentifiers() {
        navigationItem.leftBarButtonItem?.isAccessibilityElement = true
        navigationItem.leftBarButtonItem?.accessibilityIdentifier = "pmios_maya_credit_settings_back_button"
    }
}

// MARK: ViewModelBindable Methods
extension NewMayaCreditTransferConfirmationViewController: ViewModelBindable {
    func binding(_ viewModel: MayaCreditConfirmationViewModelProtocol?) {
        guard let viewModel = viewModel else { return }
        var attributes: [AnalyticsAttributeKey: Any] = [AnalyticsAttributeKey.design: Constants.MayaCredit.designV2_1]

        transactionDetailsView.isHidden = true
        serviceFeeTitleLabel.text = L10n.Maya.Credit.Transfer.ToWallet.serviceFee(viewModel.feeRate)
        serviceFeeValueLabel.text = viewModel.serviceFee
        documentStampValueLabel.text = viewModel.documentStamp
        totalCreditAmountValueLabel.text = viewModel.totalCreditDue
        confirmButton.setTitle(L10n.Maya.Credit.Transfer.ToWallet.transferNow, for: .normal)

        if viewModel.isMFAEnabled {
            confirmButton.reactive.pressed = CocoaAction(viewModel.riskChallenge) { [weak self] _ in
                guard let self = self else { return }
                self.analyticsService.logMayaEvents(action: .tap(Analytics.NewMayaCreditTransferCreditConfirmation.next), keyAttributes: attributes)
            }
        } else {
            confirmButton.reactive.pressed = CocoaAction(viewModel.executeAction) { [weak self] _ in
                guard let self = self else { return }
                self.analyticsService.logMayaEvents(action: .tap(Analytics.NewMayaCreditTransferCreditConfirmation.next), keyAttributes: attributes)
            }
        }

        viewModel.details.forEach { detail in
            switch detail.title {
            case CommonStrings.Confirmation.Detail.amount:
                mainCashLabel.text = detail.value
                amountCashLabel.text = detail.value
                transferAmountValueLabel.text = detail.value
            case CommonStrings.Confirmation.Detail.source:
                accountNumberValueLabel.text = detail.value
            case CommonStrings.Confirmation.Detail.recipient:
                mobileNumberValueLabel.text = detail.value.applyMayaMobileNumberFormat()
            default:
                break
            }
        }

        viewModel.executeAction.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self = self else { return }
                switch error.type {
                case .sessionTimeout(let message):
                    self.analyticsService.logMayaEvents(action: .failure(viewModel.analyticsModule), keyAttributes: [AnalyticsAttributeKey.reason: message])
                default:
                    self.dispatchToMain(withDelay: .now() + 0.5) {
                        guard let errorViewModel = error.viewModel else { return }
                        self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
                    }
                    self.analyticsService.logMayaEvents(action: .failure(viewModel.analyticsModule), keyAttributes: [AnalyticsAttributeKey.reason: error.viewModel?.message ?? ""])
                    if error.type != .noInternetConnection {
                        self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                            AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                            AnalyticsAttributeKey.error.rawValue: error.type
                        ])
                    }
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.executeAction.values
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self = self else { return }
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    self.analyticsService.logMayaEvents(action: .success(viewModel.analyticsModule), keyAttributes: attributes)
                    self.delegate?.didSuccessfullyExecute(self, amount: self.amountCashLabel.text ?? "")
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.executeAction.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                guard let self = self else { return }
                isExecuting ? self.showMayaLoader(nil, CommonStrings.Maya.Loader.Default.message) : self.hideMayaLoader()
            }?.addToDisposeBag(disposeBag)

        viewModel.riskChallenge.values
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self = self else { return }
                viewModel.executeAction.apply().start()
                analyticsService.logMayaEvents(action: .success(Analytics.MayaCreditRiskChallenge()), keyAttributes: attributes)
            }?.addToDisposeBag(disposeBag)

        viewModel.riskChallenge.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self = self else { return }

                analyticsService.logMayaEvents(
                    action: .failed(Analytics.MayaCreditRiskChallenge()),
                    keyAttributes: [
                        AnalyticsAttributeKey.error: error.viewModel?.error?.code ?? String.empty,
                        AnalyticsAttributeKey.reason: error.viewModel?.message ?? String.empty
                    ]
                )

                switch error.type {
                case .mfaChallengeFaceRequired(let challengeId, _, _):
                    delegate?.didRequestMFA(self, challengeId: challengeId)
                    self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                        AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                        AnalyticsAttributeKey.error.rawValue: error.type
                    ])
                case .mfaChallengeNoFaceData:
                    let title = L10n.Maya.Credit.Mfa.RiskChallenge.Error.title
                    let message = L10n.Maya.Credit.Mfa.RiskChallenge.Error.message
                    let helpCenterText = L10n.Bsp.HelpCenter.text
                    let action: AnalyticsAction = .tap(Analytics.MayaCreditTransferCredit.helpCenter)

                    if let url = Constants.WebView.mayaCreditHelpCenter.url {
                        let alertMessage = MayaAlertMessage(message: message, link: helpCenterText, url: url, analytics: action, module: viewModel.analyticsModule.name)

                        let alertViewModel = MayaAlertViewModel(title: title, image: CommonAsset.Images.Alert.imageLarger3DWarning.image, closeButtonTitle: CommonStrings.Common.Modal.Got.it, alertMessage: alertMessage)

                        self.showMayaAlertModal(viewModel: alertViewModel)
                    }
                    self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                        AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                        AnalyticsAttributeKey.error.rawValue: error.type
                    ])
                case .noInternetConnection:
                    break
                case .sessionTimeout:
                    guard let errorViewModel = error.viewModel else { return }
                    showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
                default:
                    guard let errorViewModel = error.viewModel else { return }

                    showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
                    self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                        AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                        AnalyticsAttributeKey.error.rawValue: error.type
                    ])
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.riskChallenge.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                guard let self = self else { return }
                isExecuting ? showMayaLoader(nil, CommonStrings.Maya.Loader.Default.message) : hideMayaLoader()
            }?.addToDisposeBag(disposeBag)
    }
}
