//
//  MayaCreditOptInViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 10/7/21.
//  Copyright © 2021 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Injector
import ReactiveSwift
import UIKit

protocol MayaCreditOptInViewControllerDelegate: AnyObject {
    func didSuccessfullyAgreed(_ viewController: MayaCreditOptInViewController)
}

class MayaCreditOptInViewController: ViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    @IBOutlet weak var scrollView: UIScrollView!

    @IBOutlet weak var containerView: UIView!
    @IBOutlet weak var contentDescriptionLabel: UILabel!

    weak var delegate: MayaCreditOptInViewControllerDelegate?

    override var shouldUseAnimatedNavigationBar: Bool {
        return true
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        containerView.layer.masksToBounds = false
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOpacity = 0.15
        containerView.layer.shadowRadius = 12.0
        containerView.layer.shadowOffset = CGSize(width: 0.0, height: 4.0)
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.MayaCreditOptIn())
        super.viewWillAppear(animated)
        navigationItem.leftBarButtonItem?.setTitleTextAttributes([
            NSAttributedString.Key.font: CommonFontFamily.WorkSans.semiBold.font(size: 16)!,
            NSAttributedString.Key.foregroundColor: CommonAsset.Colors.Constants.newBlack.color
        ], for: .normal)

        guard let navigationBar = navigationController?.navigationBar else { return }
        toggleNavigationBar()
        navigationBar.backgroundColor = .clear
        navigationBar.barTintColor = UIColor.clear
        navigationBar.tintColor = .white
        navigationBar.isTranslucent = true
        navigationBar.setBackgroundImage(UIImage(), for: .topAttached, barMetrics: .default)
        navigationBar.showShadow(show: true)
        extendedLayoutIncludesOpaqueBars = true
    }

    override func setupAccessibilityIdentifiers() {
        navigationItem.leftBarButtonItem?.isAccessibilityElement = true
        navigationItem.leftBarButtonItem?.accessibilityIdentifier = "pmios_maya_credit_optin_back_button"
    }

    @IBAction func didTapAgreeAndJoin(_ sender: Any) {
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditOptIn.agree))
        viewModel?.submitConsentAction.apply().start()
    }

    func toggleNavigationBar() {
        guard let navigationBar = navigationController?.navigationBar else { return }
        if scrollView.contentOffset.y > 0 {
            navigationBar.setBackgroundImage(UIImage(color: UIColor.white), for: .topAttached, barMetrics: .default)
        } else {
            navigationBar.setBackgroundImage(UIImage(), for: .topAttached, barMetrics: .default)
        }
    }
}

// MARK: - ViewModelBindable
extension MayaCreditOptInViewController: ViewModelBindable {
    func binding(_ viewModel: MayaCreditOptInViewModelProtocol?) {
        guard let viewModel = viewModel else { return }
        contentDescriptionLabel.text = viewModel.consentDescription

        viewModel.submitConsentAction.completed.observe(on: UIScheduler()).observe { [weak self] _ in
            guard let self = self else { return }
            self.delegate?.didSuccessfullyAgreed(self)
        }?.addToDisposeBag(disposeBag)

        viewModel.submitConsentAction.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            executing ? self.showHud() : self.hideHud()
        }?.addToDisposeBag(disposeBag)

        viewModel.submitConsentAction.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self else { return }
            switch error.type {
            case .sessionTimeout, .noInternetConnection:
                break
            default:
                self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                    AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                    AnalyticsAttributeKey.error.rawValue: error.type
                ])
            }
            if let errorViewModel = error.viewModel {
                self.showErrorAlert(viewModel: errorViewModel)
            }
        }?.addToDisposeBag(disposeBag)
    }
}

// MARK: UIScrollViewDelegate
extension MayaCreditOptInViewController: UIScrollViewDelegate {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        toggleNavigationBar()
    }
}
