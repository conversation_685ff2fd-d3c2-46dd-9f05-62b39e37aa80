//
//  NewMayaCreditActivationViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 14/04/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Injector
import ReactiveSwift
import UIKit

protocol NewMayaCreditActivationViewControllerDelegate: AnyObject {
    func didTapCustomizeBillingEndDate(_ viewController: NewMayaCreditActivationViewController, billingEndDateSelection: BillingEndDateSelection)
    func didTapLearnMore(_ viewController: NewMayaCreditActivationViewController)
    func didTapTermsAndConditions(_ viewController: NewMayaCreditActivationViewController, activationTerm: ActivationTerm)
    func didSuccessfullySubmitted(_ viewController: NewMayaCreditActivationViewController, response: OTPResponse)
}

class NewMayaCreditActivationViewController: ViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    @IBOutlet weak var amountLabel: UILabel!
    @IBOutlet weak var expiryLabel: UILabel!
    @IBOutlet weak var billingEndDateLabel: UILabel!
    @IBOutlet weak var selectDayButton: UIButton!
    @IBOutlet weak var interestLabel: UILabel!
    @IBOutlet weak var backUpInfoLabel: UILabel!
    @IBOutlet weak var backUpIdentityLabel: UILabel!
    @IBOutlet weak var termsAndConditionsView: UIStackView!
    @IBOutlet weak var termsAndConditionsContainerView: UIView!
    @IBOutlet weak var emailBadge: NewMayaCreditEmailBadge!
    @IBOutlet weak var activateButton: MayaButton!
    @IBOutlet weak var informationText: UITextView! {
      didSet {
        informationText.delegate = self
        informationText.linkTextAttributes = [.foregroundColor: CommonAsset.MayaColors.Content.contentPrimaryGreen.color]
      }
    }

    var applicationTermViews = [NewMayaCreditApplicationTermView]()

    weak var delegate: NewMayaCreditActivationViewControllerDelegate?

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        termsAndConditionsContainerView.layer.masksToBounds = true
        termsAndConditionsContainerView.layer.cornerRadius = 16
        termsAndConditionsContainerView.layer.borderWidth = 1
        termsAndConditionsContainerView.layer.borderColor = CommonAsset.MayaColors.Grey.grey3.color.cgColor
        activateButton.isEnabled = false
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.NewMayaCreditActivation())
        super.viewWillAppear(animated)
    }

    override func setupAccessibilityIdentifiers() {
        navigationItem.leftBarButtonItem?.isAccessibilityElement = true
        navigationItem.leftBarButtonItem?.accessibilityIdentifier = "pmios_maya_credit_activation_back_button"
    }

    @IBAction func didTapCustomizeBillingEndDate(_ sender: Any) {
        guard let viewModel = viewModel, let billingEndDateSelection = viewModel.getBillingEndDateSelection() else { return }
        analyticsService.logMayaEvents(action: .tap(Analytics.NewMayaCreditActivation.setEndDate))
        delegate?.didTapCustomizeBillingEndDate(self, billingEndDateSelection: billingEndDateSelection)
    }

    @IBAction func didTapLearnMore(_ sender: Any) {
        analyticsService.logMayaEvents(action: .tap(Analytics.NewMayaCreditActivation.learnMore))
        delegate?.didTapLearnMore(self)
    }

    @IBAction func didTapSubmit(_ sender: Any) {
        guard let viewModel = viewModel else { return }
        for termView in applicationTermViews {
            termView.checkBoxButton.submit()
        }
        analyticsService.logMayaEvents(action: .tap(Analytics.NewMayaCreditActivation.activate))
        viewModel.submitLoanAction.apply().start()
    }

    @IBAction func didTapUpdate(_ sender: Any) {
      guard let accountRecoveryURL = Constants.DeepLinkPath.accountrecovery.url else { return }
        analyticsService.logMayaEvents(action: .tap(Analytics.NewMayaCreditActivation.updateEmail))
      routeActionDelegate?.didRequestStartDeepLink(self, with: accountRecoveryURL, completion: nil)
    }
}

// MARK: - ViewModelBindable
extension NewMayaCreditActivationViewController: ViewModelBindable {
    func binding(_ viewModel: NewMayaCreditActivationViewModelProtocol?) {
        guard let viewModel = viewModel else { return }
        amountLabel.text = viewModel.amountWithCurrency
        expiryLabel.text = L10n.Maya.New.Credit.Activation.Expires.on(viewModel.expiry)
        interestLabel.text = L10n.Maya.New.Credit.Activation.Interest.rate(viewModel.feeRate)
        setBillingEndDate(viewModel.billingEndDate.value)

        backUpIdentityLabel.text = viewModel.backupIdentity
        backUpIdentityLabel.isHidden = viewModel.backupEmailStatus == .noEmail
        emailBadge.setStatus(viewModel.backupEmailStatus)
        backUpInfoLabel.text = viewModel.backupIdentityInfo

        viewModel.submitLoanAction.values.observe(on: UIScheduler()).observeValues { [weak self] response in
            guard let self = self else { return }
            self.analyticsService.logMayaEvents(action: .success(Analytics.NewMayaCreditActivation()))
            self.delegate?.didSuccessfullySubmitted(self, response: response)
        }?.addToDisposeBag(disposeBag)

        viewModel.submitLoanAction.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            executing ? self.showMayaLoader(nil, CommonStrings.Maya.Loader.Default.message) : self.hideMayaLoader()
        }?.addToDisposeBag(disposeBag)

        viewModel.submitLoanAction.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self else { return }
            switch error.type {
            case .sessionTimeout(let message):
                self.analyticsService.logMayaEvents(action: .failure(Analytics.NewMayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: message])
            case .noInternetConnection:
                break
            default:
                self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                    AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                    AnalyticsAttributeKey.error.rawValue: error.type
                ])
            }
            if let errorViewModel = error.viewModel {
                self.analyticsService.logMayaEvents(action: .failure(Analytics.NewMayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: errorViewModel.message])
                self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
            } else {
                self.analyticsService.logMayaEvents(action: .failure(Analytics.NewMayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: ""])
            }
        }?.addToDisposeBag(disposeBag)

        viewModel.billingEndDate.signal.observe(on: UIScheduler()).observeValues { [weak self] display in
            guard let self = self else { return }
            self.setBillingEndDate(display)
        }?.addToDisposeBag(disposeBag)

        viewModel.isButtonEnabled.signal
            .observe(on: UIScheduler())
            .observeValues({ [weak self] isEnabled in
                guard let self = self else { return }
                self.activateButton.isEnabled = isEnabled
            })?.addToDisposeBag(disposeBag)

        for activationTerm in viewModel.activationTerms.value {
            if let termView = Bundle.main.loadNibNamed("NewMayaCreditApplicationTermView", owner: self, options: nil)?.first as? NewMayaCreditApplicationTermView {
                termView.setProperties(activationTerm: activationTerm)
                termView.delegate = self
                applicationTermViews.append(termView)
                termsAndConditionsView.insertArrangedSubview(termView, at: applicationTermViews.count - 1)
            }
        }
    }

    private func setBillingEndDate(_ date: String) {
        guard !date.isEmpty else {
            billingEndDateLabel.isHidden = true
            return
        }

        billingEndDateLabel.isHidden = false
        billingEndDateLabel.text = L10n.Maya.Credit.Application.Billing.End.date(date)
        selectDayButton.setTitle(L10n.Maya.New.Credit.Activation.ChangeDate.title, for: .normal)
    }
}

// MARK: - MayaCreditApplicationTermDelegate
extension NewMayaCreditActivationViewController: NewMayaCreditApplicationTermDelegate {
    func didTapGetTermDetails(_ view: NewMayaCreditApplicationTermView) {
        if let activationTerm = view.activationTerm {
            analyticsService.logMayaEvents(action: .tap(Analytics.NewMayaCreditActivation.agreement), keyAttributes: [AnalyticsAttributeKey.documentType: activationTerm.term.key])
            delegate?.didTapTermsAndConditions(self, activationTerm: activationTerm)
        }
    }

    func didTapCheckBox(_ view: NewMayaCreditApplicationTermView) {
        if let activationTerm = view.activationTerm {
            analyticsService.logMayaEvents(action: .tap(Analytics.NewMayaCreditActivation.agreement), keyAttributes: [AnalyticsAttributeKey.documentType: activationTerm.term.key])

            if activationTerm.viewModel.isConfirmed.value {
                activationTerm.viewModel.isConfirmed.value.toggle()
            } else {
                delegate?.didTapTermsAndConditions(self, activationTerm: activationTerm)
            }
        }
    }
}

// MARK: UITextViewDelegate
extension NewMayaCreditActivationViewController: UITextViewDelegate {
  func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
    if let scheme = URL.scheme, scheme == "mailto" {
        analyticsService.logMayaEvents(action: .tap(Analytics.Credit.support))
      UIApplication.shared.open(URL)
    } else {
        analyticsService.logMayaEvents(action: .tap(Analytics.Credit.bsp))
        routeActionDelegate?.didRequestWebView(self, with: Constants.WebView.bsp.baseUrl!, willPresent: true, completion: nil)
    }
    return false
  }
}
