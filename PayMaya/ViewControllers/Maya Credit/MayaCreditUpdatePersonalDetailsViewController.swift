//
//  MayaCreditUpdatePersonalDetailsViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 11/8/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Injector
import ReactiveSwift
import UIKit

protocol MayaCreditUpdatePersonalDetailsViewControllerDelegate: AnyObject {
    func didTapGender(_ viewController: MayaCreditUpdatePersonalDetailsViewController, personalDetailsSelection: PersonalDetailsSelection)
    func didTapMaritalStatus(_ viewController: MayaCreditUpdatePersonalDetailsViewController, personalDetailsSelection: PersonalDetailsSelection)
    func didTapContinue(_ viewController: MayaCreditUpdatePersonalDetailsViewController)
    func didSuccessfullySubmitted(_ viewController: MayaCreditUpdatePersonalDetailsViewController)
}

class MayaCreditUpdatePersonalDetailsViewController: FormsViewController, AnalyticsServiceProtocol, MayaProgressBarProtocol {
    @Inject var analyticsService: AnalyticsService

    var segment: Int = 1

    weak var delegate: MayaCreditUpdatePersonalDetailsViewControllerDelegate?

    @IBOutlet weak var headerLabel: UILabel!

    @IBOutlet weak var alternativeMobileNumberView: MayaValidationFieldView! {
        didSet {
            setAlternativeMobileNumber()
        }
    }

    @IBOutlet weak var genderView: MayaValidationFieldView! {
        didSet {
            setGenderFieldView()
        }
    }

    @IBOutlet weak var maritalStatusView: MayaValidationFieldView! {
        didSet {
            setMaritalStatusFieldView()
        }
    }

    @IBOutlet weak var continueButton: MayaButton!

    var alternativeMobileNumberTextField: TextField {
        return alternativeMobileNumberView.textField
    }

    var genderTextField: TextField {
        return genderView.textField
    }

    var maritalStatusTextField: TextField {
        return maritalStatusView.textField
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        continueButton.isEnabled = false

        let tap = UITapGestureRecognizer(target: self, action: #selector(onTapParentView))
        view.addGestureRecognizer(tap)
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        guard viewModel?.personalDetailsState == nil else { return }
        setRightButtonItemTitle("1/3")
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        scrollView.setContentOffset(.zero, animated: true)
    }

    override func setupAccessibilityIdentifiers() {
        navigationItem.leftBarButtonItem?.isAccessibilityElement = true
        navigationItem.leftBarButtonItem?.accessibilityIdentifier = "pmios_maya_credit_activation_back_button"
    }

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override var mayaNavigationBarScrollView: UIScrollView? {
        return scrollView
    }

    @IBAction func didTapContinue(_ sender: Any) {
        guard viewModel != nil else { return }
        delegate?.didTapContinue(self)
    }

    @objc private func onTapParentView(_ gesture: UIGestureRecognizer) {
        alternativeMobileNumberTextField.resignFirstResponder()
    }
}

// MARK: - ViewModelBindable
extension MayaCreditUpdatePersonalDetailsViewController: ViewModelBindable {
    func binding(_ viewModel: NewMayaCreditActivationViewModelProtocol?) {
        guard let viewModel = viewModel else { return }

        var header: String {
            switch viewModel.personalDetailsState {
            case .add:
                return L10n.Maya.Credit.PersonalDetails.Header.add
            case .edit:
                return L10n.Maya.Credit.PersonalDetails.Header.edit
            default:
                return L10n.Maya.Credit.PersonalDetails.Header.signup
            }
        }
        headerLabel.text = header

        viewModel.isUpdateButtonEnabled.signal
            .observe(on: UIScheduler())
            .observeValues({ [weak self] isEnabled in
                guard let self = self else { return }
                self.continueButton.isEnabled = isEnabled
            })?.addToDisposeBag(disposeBag)

        viewModel.alternativeMobileNumberField.mutableProperty <~ alternativeMobileNumberTextField.reactive.continuousTextValues
        alternativeMobileNumberView.bindFieldProperty(viewModel.alternativeMobileNumberField)

        viewModel.alternativeMobileNumberField.mutableProperty.signal.observe(on: UIScheduler()).observeValues { [weak self] mobileNumber in
            guard let self = self else { return }
            self.alternativeMobileNumberTextField.text = mobileNumber
        }?.addToDisposeBag(disposeBag)

        viewModel.genderField.mutableProperty.signal.observe(on: UIScheduler()).observeValues { [weak self] gender in
            guard let self = self else { return }
            self.genderTextField.text = gender
        }?.addToDisposeBag(disposeBag)

        viewModel.maritalStatusField.mutableProperty.signal.observe(on: UIScheduler()).observeValues { [weak self] maritalStatus in
            guard let self = self else { return }
            self.maritalStatusTextField.text = maritalStatus
        }?.addToDisposeBag(disposeBag)

        // get contact reference
        viewModel.getContactReference.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            executing ? self.showMayaLoader() : self.hideMayaLoader()
        }?.addToDisposeBag(disposeBag)

        // submit contact reference
        viewModel.submitContactReference.values.observe(on: UIScheduler()).observeValues { [weak self] _ in
            guard let self = self else { return }
            if viewModel.personalDetailsState == nil {
                self.delegate?.didSuccessfullySubmitted(self)
            } else {
                self.backActionDelegate?.didTapBack(self)
            }
        }?.addToDisposeBag(disposeBag)

        viewModel.submitContactReference.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            executing ? self.showMayaLoader(nil, CommonStrings.Maya.Loader.Default.message) : self.hideMayaLoader()
        }?.addToDisposeBag(disposeBag)

        viewModel.submitContactReference.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self else { return }
            switch error.type {
            case .sessionTimeout(let message):
                self.analyticsService.logMayaEvents(action: .failure(Analytics.NewMayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: message])
            case .noInternetConnection:
                break
            default:
                self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                    AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                    AnalyticsAttributeKey.error.rawValue: error.type
                ])
            }
            if let errorViewModel = error.viewModel {
                self.analyticsService.logMayaEvents(action: .failure(Analytics.NewMayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: errorViewModel.message])
                self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
            } else {
                self.analyticsService.logMayaEvents(action: .failure(Analytics.NewMayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: ""])
            }
        }?.addToDisposeBag(disposeBag)

        viewModel.getContactReference.apply().start()
    }
}

private extension MayaCreditUpdatePersonalDetailsViewController {
    private func setAlternativeMobileNumber() {
        alternativeMobileNumberView.setTextFieldProperties(keyboardType: .phonePad)
        alternativeMobileNumberView.isInlineErrorIconHidden = true
        alternativeMobileNumberView.textField.delegate = self
        alternativeMobileNumberView.hintText = L10n.Maya.Credit.Signup.Contact.Reference.Section.Billing.Altmobile.label
    }

    private func setGenderFieldView() {
        genderView.setTextFieldProperties(capitalizationType: .words)
        genderView.hintText = CommonStrings.Common.gender
        genderView.showChevron()
        genderView.textField.delegate = self
    }

    private func setMaritalStatusFieldView() {
        maritalStatusView.setTextFieldProperties(capitalizationType: .words)
        maritalStatusView.hintText = CommonStrings.Common.maritalstatus
        maritalStatusView.showChevron()
        maritalStatusView.textField.delegate = self
    }
}

extension MayaCreditUpdatePersonalDetailsViewController {
    override func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        guard let viewModel = viewModel else { return true }

        if textField.text != nil && textField === alternativeMobileNumberTextField {
            let text = NSString(string: textField.text ?? "").replacingCharacters(in: range, with: string)

            if let formattedText = viewModel.formatAlternativeNumber(with: text, and: string) {
                alternativeMobileNumberView.textField.text = formattedText
                return false
            }
        }

        return true
    }
}

// MARK: - UITextFieldDelegate Methods
extension MayaCreditUpdatePersonalDetailsViewController {
    func textFieldShouldBeginEditing(_ textField: UITextField) -> Bool {
        guard let viewModel = self.viewModel else { return false }
        view.endEditing(true)

        if textField == genderTextField {
            self.delegate?.didTapGender(self, personalDetailsSelection: viewModel.getPersonalDetailsSelection())
            return false
        } else if textField == maritalStatusTextField {
            self.delegate?.didTapMaritalStatus(self, personalDetailsSelection: viewModel.getPersonalDetailsSelection())
            return false
        }

        return true
    }
}
