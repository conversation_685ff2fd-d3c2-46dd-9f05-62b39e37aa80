//
//  NewMayaCreditAccountSummary.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 24/03/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Foundation
import Injector
import ReactiveSwift
import UIKit

protocol NewMayaCreditAccountSummaryViewControllerDelegate: AnyObject {
    func didTapViewServiceFee(_ viewController: NewMayaCreditAccountSummaryViewController, feeRate: Float?)
    func didTapOurPolicies(_ viewController: NewMayaCreditAccountSummaryViewController, with agreements: [MayaCreditAgreement]?, of type: PoliciesTypes)
    func didTapDataPrivacyPolicy(_ viewController: NewMayaCreditAccountSummaryViewController)
    func didTapAutomatedPayments(_ viewController: NewMayaCreditAccountSummaryViewController)
    func didTapUpdatePersonalDetails(_ viewController: NewMayaCreditAccountSummaryViewController, personalDetailsState: PersonalDetailsState)
    func didTapUpdateContactReference(_ viewController: NewMayaCreditAccountSummaryViewController, contactReference: MayaCreditContactReference?, contactReferenceState: ContactReferenceState)
}

class NewMayaCreditAccountSummaryViewController: ViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    weak var delegate: NewMayaCreditAccountSummaryViewControllerDelegate?

    let placeholderColor = CommonAsset.MayaColors.Grey.grey4.color
    var personalDetailsState: PersonalDetailsState = PersonalDetailsState.edit
    var contactReferenceState: ContactReferenceState = ContactReferenceState.edit
    var contactReference: MayaCreditContactReference?

    @IBOutlet weak var updatePersonalDetailsLabel: UIButton!
    @IBOutlet weak var updateContactReferenceLabel: UIButton!

    @IBOutlet weak var genderLabel: UILabel!
    @IBOutlet weak var maritalStatusLabel: UILabel!
    @IBOutlet weak var alternateMobileNumberLabel: UILabel!
    @IBOutlet weak var firstNameLabel: UILabel!
    @IBOutlet weak var lastNameLabel: UILabel!
    @IBOutlet weak var contactRelationshipLabel: UILabel!
    @IBOutlet weak var mobileNumberLabel: UILabel!
    @IBOutlet weak var personalDetailsView: UIView!
    @IBOutlet weak var contactReferenceView: UIView!
    @IBOutlet weak var alternativeMobileNumberContainerView: UIView!
    @IBOutlet weak var alternativeMobileNumberBorderView: UIView!
    @IBOutlet weak var billingDetailsTitleLabel: UIView!

    @IBOutlet weak var creditOwnerLabel: UILabel!
    @IBOutlet weak var creditLimitLabel: UILabel!
    @IBOutlet weak var accountNumberLabel: UILabel!
    @IBOutlet weak var billingEndDateLabel: UILabel!
    @IBOutlet weak var serviceFeeLabel: UILabel!
    @IBOutlet weak var backupIdentityLabel: UILabel!
    @IBOutlet weak var howItWorksbutton: UIButton!
    @IBOutlet weak var informationText: UITextView! {
        didSet {
            informationText.delegate = self
            informationText.linkTextAttributes = [.foregroundColor: CommonAsset.MayaColors.Content.contentPrimaryGreen.color]

            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.paragraphSpacing = 8
            let attributedString = NSMutableAttributedString(
                string: L10n.Maya.Credit.DisclosureStatement.label,
                attributes: [
                    .foregroundColor: CommonAsset.MayaColors.Grey.grey6.color,
                    .font: CommonFontFamily.CerebriSansPro.regular.font(size: 12)!,
                    NSAttributedString.Key.paragraphStyle: paragraphStyle
                ]
            )

            guard let url = Constants.DeepLinkPath.chatWithUs.url else { return }
            attributedString.addAttribute(.link, value: url, range: attributedString.mutableString.range(of: L10n.Maya.Credit.ChatWithUs.link))
            informationText.attributedText = attributedString
            informationText.textAlignment = .center
        }
    }
    @IBOutlet weak var emailVerifiedLabel: UILabel!
    @IBOutlet weak var emailVerifiedView: UIView!
    @IBOutlet weak var dataPrivacyPolicyButton: UIButton!
    @IBOutlet weak var automatedPaymentsButton: UIButton!

    override var shouldUseAnimatedNavigationBar: Bool {
        return true
    }

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        viewModel?.getMayaCreditContactReferenceAction.apply().start()
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.MayaCreditAccountSummary())
        super.viewWillAppear(animated)
        navigationItem.leftBarButtonItem?.tintColor = CommonAsset.MayaColors.Primary.primaryBlack.color
        navigationController?.navigationBar.titleTextAttributes = [
            NSAttributedString.Key.font: CommonFontFamily.Jeko.bold.font(size: 16)!,
            NSAttributedString.Key.foregroundColor: CommonAsset.MayaColors.Primary.primaryBlack.color
        ]
    }

    override func setupAccessibilityIdentifiers() {
        navigationItem.leftBarButtonItem?.isAccessibilityElement = true
        navigationItem.leftBarButtonItem?.accessibilityIdentifier = "pmios_maya_credit_settings_back_button"
    }

    @IBAction func didTapCopyAccountNumber(_ sender: Any) {
        UIPasteboard.general.string = viewModel?.accountNumber
        showMayaSnackBar(message: L10n.Maya.Credit.Accountsummary.Accountnumber.copied)
    }

    @IBAction func didTapView(_ sender: Any) {
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditAccountSummary.howItWorks))
        delegate?.didTapViewServiceFee(self, feeRate: viewModel?.feeRate.value)
    }

    @IBAction func didTapUpdate(_ sender: Any) {
        guard let accountRecoveryURL = Constants.DeepLinkPath.accountrecovery.url else { return }
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditAccountSummary.updateEmail))
        routeActionDelegate?.didRequestStartDeepLink(self, with: accountRecoveryURL, completion: nil)
    }

    @IBAction func didTapTermsAndConditions(_ sender: Any) {
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditAccountSummary.terms))
        delegate?.didTapOurPolicies(self, with: viewModel?.agreements.value, of: .termsAndConditions)
    }

    @IBAction func didTapTruthAndTransparency(_ sender: Any) {
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditAccountSummary.truth))
        delegate?.didTapOurPolicies(self, with: viewModel?.agreements.value, of: .truthAndTransparency)
    }

    @IBAction func didTapDisclosureStatement(_ sender: Any) {
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditAccountSummary.disclosure))
        delegate?.didTapOurPolicies(self, with: viewModel?.agreements.value, of: .disclosureStatement)
    }

    @IBAction func didTapDataPrivacyPolicy(_ sender: Any) {
        delegate?.didTapDataPrivacyPolicy(self)
    }

    @IBAction func didTapAutomatedPayments(_ sender: Any) {
        delegate?.didTapAutomatedPayments(self)
    }

    @IBAction func didTapUpdatePersonalDetails(_ sender: Any) {
        delegate?.didTapUpdatePersonalDetails(self, personalDetailsState: personalDetailsState)
    }

    @IBAction func didTapUpdateContactReference(_ sender: Any) {
        delegate?.didTapUpdateContactReference(self, contactReference: contactReference, contactReferenceState: contactReferenceState)
    }

    @objc private func addPersonalDetailsPlaceholder() {
        genderLabel.textColor = placeholderColor
        maritalStatusLabel.textColor = placeholderColor
        alternateMobileNumberLabel.textColor = placeholderColor

        genderLabel.text = L10n.Maya.Credit.Accountsummary.Placeholder.gender
        maritalStatusLabel.text = L10n.Maya.Credit.Accountsummary.Placeholder.maritalStatus
        alternateMobileNumberLabel.text = L10n.Maya.Credit.Accountsummary.Placeholder.mobileNumber
    }

    @objc private func addContactReferencePlaceholder() {
        firstNameLabel.textColor = placeholderColor
        lastNameLabel.textColor = placeholderColor
        contactRelationshipLabel.textColor = placeholderColor
        mobileNumberLabel.textColor = placeholderColor

        firstNameLabel.text = L10n.Maya.Credit.Accountsummary.Placeholder.firstname
        lastNameLabel.text = L10n.Maya.Credit.Accountsummary.Placeholder.lastname
        contactRelationshipLabel.text = L10n.Maya.Credit.Accountsummary.Placeholder.contactRelationship
        mobileNumberLabel.text = L10n.Maya.Credit.Accountsummary.Placeholder.mobileNumber
    }
}

// MARK: ViewModelBindable
extension NewMayaCreditAccountSummaryViewController: ViewModelBindable {
  func binding(_ viewModel: MayaCreditSettingsViewModelProtocol?) {
    guard let viewModel = viewModel else { return }
      creditOwnerLabel.text = viewModel.creditOwner
      creditLimitLabel.text = viewModel.creditLimit.replacingOccurrences(of: "PHP", with: Constants.Defaults.Common.peso.rawValue) // viewModel resturns PHP as default currency label, this exchange PHP to Peso sign
      accountNumberLabel.text = viewModel.accountNumber
      billingEndDateLabel.text = viewModel.billingEndDate
      serviceFeeLabel.text = viewModel.serviceFee

      updatePersonalDetailsLabel.setTitle(L10n.Maya.Credit.Accountsummary.Update.edit, for: .normal)
      updateContactReferenceLabel.setTitle(L10n.Maya.Credit.Accountsummary.Update.edit, for: .normal)

      if viewModel.isBadgeHidden {
          emailVerifiedLabel.text = L10n.Maya.Credit.Accountsummary.Settings.unverified
          backupIdentityLabel.text = viewModel.backupIdentity
          emailVerifiedView.backgroundColor = CommonAsset.MayaColors.Grey.grey4.color
      } else {
          backupIdentityLabel.text = viewModel.backupIdentity
          emailVerifiedView.backgroundColor = CommonAsset.MayaColors.Primary.primaryGrownGreen.color
          emailVerifiedLabel.text = L10n.Maya.Credit.Accountsummary.Settings.verified
      }

      viewModel.feeRate.signal.observe(on: UIScheduler()).observeValues { [weak self] _ in
          guard let self = self else { return }
          self.serviceFeeLabel.text = self.viewModel?.serviceFee
      }?.addToDisposeBag(disposeBag)

      viewModel.contactReference.signal.observe(on: UIScheduler()).observeValues { [weak self] _ in
          guard let self = self else { return }

          self.firstNameLabel.text = self.viewModel?.contactFirstName
          self.lastNameLabel.text = self.viewModel?.contactLastName
          self.alternateMobileNumberLabel.text = self.viewModel?.alternativeMobileNumber
          self.genderLabel.text = self.viewModel?.gender
          self.maritalStatusLabel.text = self.viewModel?.maritalStatus
          self.contactRelationshipLabel.text = self.viewModel?.contactRelationship
          self.mobileNumberLabel.text = self.viewModel?.contactMobileNumber
      }

      viewModel.getMayaCreditContactReferenceAction.isExecuting.signal
          .observe(on: UIScheduler())
          .observeValues { [weak self] isExecuting in
              isExecuting && viewModel.contactReference.value == nil ? self?.showMayaLoader() : self?.hideMayaLoader()
          }?.addToDisposeBag(disposeBag)

      viewModel.getMayaCreditContactReferenceAction.values.observe(on: UIScheduler()).observeValues { [weak self] contactReference in
          guard let self = self else { return }

          self.contactReference = contactReference

          self.updatePersonalDetailsLabel.setTitle(L10n.Maya.Credit.Accountsummary.Update.edit, for: .normal)
          self.updateContactReferenceLabel.setTitle(L10n.Maya.Credit.Accountsummary.Update.edit, for: .normal)

          self.personalDetailsState = PersonalDetailsState.edit
          self.contactReferenceState = ContactReferenceState.edit
      }?.addToDisposeBag(disposeBag)

      viewModel.getMayaCreditContactReferenceAction.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
          guard let self = self else { return }
          switch error.type {
          case .sessionTimeout, .noInternetConnection:
              break
          case .mayaCreditEmptyContactRef:
              self.updatePersonalDetailsLabel.setTitle(L10n.Maya.Credit.Accountsummary.Update.add, for: .normal)
              self.updateContactReferenceLabel.setTitle(L10n.Maya.Credit.Accountsummary.Update.add, for: .normal)

              guard viewModel.isAddEnabled else {
                  self.updateContactReferenceLabel.isHidden = true
                  self.updatePersonalDetailsLabel.isHidden = true
                  return
              }

              self.personalDetailsState = PersonalDetailsState.add
              self.contactReferenceState = ContactReferenceState.add
              self.addPersonalDetailsPlaceholder()
              self.addContactReferencePlaceholder()
          default:
              self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                AnalyticsAttributeKey.error.rawValue: error.type
              ])
          }
      }?.addToDisposeBag(disposeBag)
  }
}

// MARK: UITextViewDelegate
extension NewMayaCreditAccountSummaryViewController: UITextViewDelegate {
    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        if URL == Constants.DeepLinkPath.chatWithUs.url {
            guard let url = Constants.DeepLinkPath.chatWithUs.url else { return false }
            self.analyticsService.logMayaEvents(action: .tap(Analytics.Credit.chatWithUs))
            self.routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
        } else if URL.scheme == "mailto" {
            analyticsService.logMayaEvents(action: .tap(Analytics.Credit.support))
            UIApplication.shared.open(URL)
        } else {
            analyticsService.logMayaEvents(action: .tap(Analytics.Credit.bsp))
            routeActionDelegate?.didRequestWebView(self, with: Constants.WebView.bsp.baseUrl!, willPresent: true, completion: nil)
        }
        return false
    }
}
