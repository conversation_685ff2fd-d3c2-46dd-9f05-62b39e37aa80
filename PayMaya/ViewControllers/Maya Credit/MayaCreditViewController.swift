//
//  MayaCreditViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 2/9/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import ConfigurationService
import Injector
import ReactiveSwift
import SkeletonView
import UIKit

protocol MayaCreditViewControllerDelegate: AnyObject {
    func didRequestMayaCreditDiscoveryFlow(_ viewController: MayaCreditViewController, consents: [MayaCreditOptInConsent])
    func didRequestMayaCreditSignUpFlow(_ viewController: MayaCreditViewController, term: MayaCreditEligibility.Term, feeRate: Float?)
    func didRequestMayaCreditUpdatePersonalDetailsFlow(_ viewController: MayaCreditViewController, term: MayaCreditEligibility.Term?, feeRate: Float?, account: MayaCreditAccount)
    func didRequestAccountSummaryFlow(_ viewController: MayaCreditViewController, term: MayaCreditCurrentLoanApplication.Term, account: MayaCreditAccount, eligibilityTerm: MayaCreditEligibility.Term?)
    func didRequestCreditSettingsFlow(_ viewController: MayaCreditViewController, term: MayaCreditCurrentLoanApplication.Term, account: MayaCreditAccount)
    func didRequestPayCreditBillFlow(_ viewController: MayaCreditViewController, url: URL)
    func didRequestCreditDataPrivacy(_ viewController: MayaCreditViewController, delegate: NewMayaCreditDiscoveryDataPrivacyViewControllerDelegate?)
    func didRequestTransactionFlow(_ viewController: MayaCreditViewController, account: MayaCreditAccount)
    func didRequestBillingStatementsFlow(_ viewController: MayaCreditViewController)
    func didRequestLatestBillInfoFlow(_ viewController: MayaCreditViewController)
    func didRequestMayaCreditInitalEligibilityFlow(_ viewController: MayaCreditViewController)
    func didTapChatWithUs(_ viewController: MayaCreditViewController)
    func didReceiveNoMaidenName(_ viewController: MayaCreditViewController, entryPoint: Constants.PersonalDetails.EntryPoint)
    func didReceiveMaidenName(_ viewController: MayaCreditViewController, entryPoint: Constants.PersonalDetails.EntryPoint)
}

class MayaCreditViewController: ViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var configurationService: ConfigurationService
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    private var mothersMaidenNameEntryPoint: Constants.PersonalDetails.EntryPoint?

    @IBOutlet weak var tableView: UITableView!

    weak var delegate: MayaCreditViewControllerDelegate?

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override var shouldUseMayaArrowBackButton: Bool {
        return false
    }

    override var willSetupDurationAnalyticsAutomatically: Bool {
        return false
    }

    private let refreshControl = MayaRefreshControl()

    override func viewDidLoad() {
        super.viewDidLoad()
        navigationBarHidden = true
        tableView.showsVerticalScrollIndicator = false
        setupTableView()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        refresh()
        NotificationCenter.default.addObserver(self, selector: #selector(refresh), name: NSNotification.Name(rawValue: sessionTimeoutSuccessNotification), object: nil)
    }

    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name(rawValue: sessionTimeoutSuccessNotification), object: nil)
    }

    private func setupTableView() {
        tableView.delegate = self
        tableView.dataSource = self
        refreshControl.addTarget(self, action: #selector(pullToRefresh), for: .valueChanged)
        tableView.refreshControl = refreshControl
        tableView.rowHeight = UITableView.automaticDimension

        tableView.registerCell(of: MayaCreditSpacerTableViewCell.self)
        tableView.registerCell(of: MayaCreditApprovedTableViewCell.self)
        tableView.registerCell(of: MayaCreditSubmittedTableViewCell.self)
        tableView.registerCell(of: MayaCreditContactInformationTableViewCell.self)
        tableView.registerCell(of: MayaCreditLogoTableViewCell.self)
        tableView.registerCell(of: MayaCreditHelpCenterTableViewCell.self)
        tableView.registerCell(of: MayaCreditIneligibleTableViewCell.self)
        tableView.registerCell(of: MayaCreditShimmerTableViewCell.self)
        tableView.registerCell(of: MayaCreditErrorTableViewCell.self)
        tableView.registerCell(of: MayaCreditWriteOffTableViewCell.self)
        tableView.registerCell(of: MayaCreditWrittenOffTableViewCell.self)
        tableView.registerCell(of: NewMayaAvailableCreditTableViewCell.self)
        tableView.registerCell(of: NewMayaManageMyCreditTableViewCell.self)
        tableView.registerCell(of: MayaCreditTransactionsTableViewCell.self)
        tableView.registerCell(of: MayaBillingStatementRevampTableViewCell.self)
        tableView.registerCell(of: MayaCreditInitialEligibilityTableViewCell.self)
        if configurationService.mayaCreditPartnerMerchantEnabled.value {
            tableView.registerCell(of: MayaCreditPartnerMerchantTableViewCell.self)
        }
    }

    private func showLockedErrorModal(account: MayaCreditAccount, alertViewModel: MayaAlertViewModel, url: URL) {
        if account.dues.total > 0 {
            let payNowAction = MayaAlertAction(title: L10n.Maya.Credit.Billing.Paynow.Button.title, style: .primary) {
                self.delegate?.didRequestPayCreditBillFlow(self, url: url)
            }
            alertViewModel.addAction(payNowAction)
        }
        self.showMayaAlertModal(viewModel: alertViewModel)
    }

    @objc private func refresh() {
        guard let viewModel = viewModel else { return }
        viewModel.getMayaCreditLoanApplication.apply().start()
        // refresh latest credit transactions
        viewModel.mayaCreditActivitiesViewModel?.refreshTransactionListAction.apply().start()
    }

    @objc private func pullToRefresh() {
        guard let viewModel = viewModel else { return }
        viewModel.pullToRefreshTriggered.value = true
        refresh()
    }
}

private extension MayaCreditViewController {
    func startTransferToWalletFlow() {
        guard let viewModel = viewModel, let term = viewModel.currentLoanApplication.value?.term, let account = viewModel.creditAccount.value else {
            return
        }

        mothersMaidenNameEntryPoint = .transferToWallet(term, account, viewModel.feeRate.value)

        viewModel.getPersonDetails.apply().start()
    }

    func startPayBillFlow(url: URL) {
        mothersMaidenNameEntryPoint = .payBill(url)
        guard let viewModel = viewModel else { return }
        viewModel.getPersonDetails.apply().start()
    }
}

// MARK: - ViewModelBindable methods
extension MayaCreditViewController: ViewModelBindable {
    func binding(_ viewModel: MayaCreditViewModelProtocol?) {
        guard let viewModel = viewModel else { return }

        viewModel.items.producer
            .observe(on: UIScheduler())
            .startWithValues { [weak self] _ in
                guard let self = self else { return }
                self.refreshControl.endRefreshing()
                self.viewModel?.pullToRefreshTriggered.value = false
                self.tableView.reloadData()
            }.addToDisposeBag(disposeBag)

        viewModel.isBalanceHiddenProperty.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self = self,
                      let index = viewModel.items.value.firstIndex(where: { $0 == .availableCredit }),
                      let cell = self.tableView.cellForRow(at: IndexPath(row: index, section: 0)) as? NewMayaAvailableCreditTableViewCell
                else { return }

                cell.bind(
                    isBalanceHidden: viewModel.isBalanceHiddenProperty.value,
                    displayBalance: viewModel.displayBalance,
                    progress: viewModel.progress,
                    creditLimit: viewModel.creditLimit,
                    usedBalance: viewModel.usedBalance,
                    showContactReferenceInfo: viewModel.showContactReference.value,
                    isAccountLocked: viewModel.isAccountLocked.value)

                self.tableView.reloadData()
            }?.addToDisposeBag(disposeBag)

        viewModel.getMayaCreditConsents.values
            .observe(on: UIScheduler())
            .observeValues { [weak self] consents in
            guard let self = self else { return }
                self.delegate?.didRequestMayaCreditDiscoveryFlow(self, consents: consents)
            }?.addToDisposeBag(disposeBag)

        viewModel.getMayaCreditConsents.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
            guard let self = self else { return }
                isExecuting ? self.showMayaLoader(nil, CommonStrings.Maya.Loader.Default.message) : self.hideMayaLoader()
            }?.addToDisposeBag(disposeBag)

        viewModel.getMayaCreditConsents.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self = self else { return }
                switch error.type {
                case .sessionTimeout:
                    return
                case .noInternetConnection:
                    break
                default:
                    self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                        AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                        AnalyticsAttributeKey.error.rawValue: error.type
                    ])
                }

                if let errorViewModel = error.viewModel {
                    self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.analyticsModule.signal
            .skipNil()
            .observe(on: UIScheduler())
            .observeValues { [weak self] module in
                guard let self = self else { return }
                self.logDurationAnalytics()
                self.setModule(module)
            }?.addToDisposeBag(disposeBag)

        viewModel.billingStatementUrlAction.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                guard let self = self else { return }
                isExecuting ? self.showMayaLoader() : self.hideMayaLoader()
            }?.addToDisposeBag(disposeBag)

        viewModel.billingStatementUrlAction.values
            .observe(on: UIScheduler())
            .observeValues { response in
                guard let url = URL(string: response.url) else { return }
                showExternalURI(url)
            }?.addToDisposeBag(disposeBag)

        viewModel.billingStatementUrlAction.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self = self else { return }
                switch error.type {
                case .sessionTimeout:
                    return
                case .noInternetConnection:
                    break
                default:
                    self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                        AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                        AnalyticsAttributeKey.error.rawValue: error.type
                    ])
                }
                if let errorViewModel = error.viewModel {
                    self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.submitConsentAction.completed.observe(on: UIScheduler()).observe { [weak self] _ in
            guard let self = self else { return }
            self.refresh()
        }?.addToDisposeBag(disposeBag)

        viewModel.submitConsentAction.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            executing ? self.showMayaLoader() : self.hideMayaLoader()
        }?.addToDisposeBag(disposeBag)

        viewModel.submitConsentAction.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self else { return }
            switch error.type {
            case .sessionTimeout, .noInternetConnection:
                break
            default:
                self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                    AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                    AnalyticsAttributeKey.error.rawValue: error.type
                ])
            }
            if let errorViewModel = error.viewModel {
                self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
            }
        }?.addToDisposeBag(disposeBag)

        viewModel.mayaCreditActivitiesViewModel?.transactionListGroupProperty.producer
            .observe(on: QueueScheduler.main)
            .startWithValues({  [weak self] items in
            guard let self = self, let items = items.first else {
                self?.showMayaLoader()
                return
            }

            if items.transactions.isEmpty {
                self.showMayaLoader()
            } else {
                self.hideMayaLoader()
            }
        }).addToDisposeBag(disposeBag)

        viewModel.getPersonDetails.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                guard let self = self else { return }
                isExecuting ? self.showMayaLoader() : self.hideMayaLoader()
            }?.addToDisposeBag(disposeBag)

        viewModel.getPersonDetails.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self = self else { return }
                switch error.type {
                case .sessionTimeout, .noInternetConnection:
                    break
                default:
                    self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                        AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                        AnalyticsAttributeKey.error.rawValue: error.type
                    ])
                }
                if let errorViewModel = error.viewModel {
                    self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.getPersonDetails.values
            .observe(on: UIScheduler())
            .observeValues { [weak self] personDetails in
                guard let self = self, let entryPoint = mothersMaidenNameEntryPoint else { return }
                if personDetails.mothersMaidenName == nil {
                    delegate?.didReceiveNoMaidenName(self, entryPoint: entryPoint)
                } else {
                    delegate?.didReceiveMaidenName(self, entryPoint: entryPoint)
                }
            }?.addToDisposeBag(disposeBag)
    }
}

extension MayaCreditViewController: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let viewModel = viewModel else { return 0 }
        return viewModel.items.value.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let viewModel = viewModel,
              let item = viewModel.items.value[safeIndex: indexPath.row],
              let cell = tableView.dequeueReusableCell(withIdentifier: item.identifier)
        else { return UITableViewCell() }

        switch item {
        case .spacer, .approved, .submitted, .logo, .ineligible:
            // No modification needed
            break
        case .contactInformation:
            if let cell = cell as? MayaCreditContactInformationTableViewCell {
                cell.delegate = self
            }
        case .helpCenter:
            if let cell = cell as? MayaCreditHelpCenterTableViewCell {
                cell.delegate = self
            }
        case .loading:
            if let cell = cell as? MayaCreditShimmerTableViewCell {
                cell.stackView.showAnimatedGradientSkeleton()
            }
        case .error:
            if let cell = cell as? MayaCreditErrorTableViewCell {
                cell.delegate = self
            }
        case .availableCredit:
            if let cell = cell as? NewMayaAvailableCreditTableViewCell {
                cell.delegate = self
                cell.bind(
                    isBalanceHidden: viewModel.isBalanceHiddenProperty.value,
                    displayBalance: viewModel.displayBalance,
                    progress: viewModel.progress,
                    creditLimit: viewModel.creditLimit,
                    usedBalance: viewModel.usedBalance,
                    showContactReferenceInfo: viewModel.showContactReference.value,
                    isAccountLocked: viewModel.isAccountLocked.value)
            }
        case .billingStatementRevamp:
            if let cell = cell as? MayaBillingStatementRevampTableViewCell {
                cell.delegate = self
                switch viewModel.billingStatementState {
                case .available:
                    cell.bind(oustandingBalance: viewModel.outstandingBalance, latestBill: viewModel.latestBill, statementMonth: viewModel.statementMonth, dueDate: viewModel.dueDate, isPayNowEnabled: viewModel.isPayNowEnabled, isPayThisBillEnabled: viewModel.isPayThisBillEnabled, latestBillDueDateColor: viewModel.latestBillDueDateColor)
                case _:
                    cell.unavailable(oustandingBalance: viewModel.outstandingBalance, isPayNowEnabled: viewModel.isPayNowEnabled)
                }
            }
        case .manageMyCredit:
            if let cell = cell as? NewMayaManageMyCreditTableViewCell {
                cell.delegate = self
            }
        case .transactions:
            if let cell = cell as? MayaCreditTransactionsTableViewCell {
                cell.delegate = self
                cell.bind(mayaCreditActivitiesViewModel: viewModel.mayaCreditActivitiesViewModel)
            }
        case .partnerMerchant:
            if let cell = cell as? MayaCreditPartnerMerchantTableViewCell {
                cell.delegate = self
            }
        case .initialEligibility:
            if let cell = cell as? MayaCreditInitialEligibilityTableViewCell {
                cell.delegate = self
            }
        case .writeOff:
            if let cell = cell as? MayaCreditWriteOffTableViewCell {
                cell.bind(balance: viewModel.displayBalance)
            }
        case .writtenOff:
            if let cell = cell as? MayaCreditWrittenOffTableViewCell {
                cell.delegate = self
            }
        }
        return cell
    }

    private func refreshHeightOfCell() {
        self.tableView.reloadData()
    }
}

// MARK: MayaCreditContactInformationTableViewCellDelegate Functions
extension MayaCreditViewController: MayaCreditContactInformationTableViewCellDelegate {
    func didTapSupportEmail(_ cell: MayaCreditContactInformationTableViewCell) {
        analyticsService.logMayaEvents(action: .tap(Analytics.Credit.support))
    }

    func didTapBSPURL(_ cell: MayaCreditContactInformationTableViewCell, url: URL) {
        analyticsService.logMayaEvents(action: .tap(Analytics.Credit.bsp))
        routeActionDelegate?.didRequestWebView(self, with: url, willPresent: true, completion: nil)
    }

    func didTapChatWithUs(_ cell: MayaCreditContactInformationTableViewCell) {
        guard let url = Constants.DeepLinkPath.chatWithUs.url else { return }
        self.analyticsService.logMayaEvents(action: .tap(Analytics.Credit.chatWithUs))
        if configurationService.inboxSupportEnabled.value {
            delegate?.didTapChatWithUs(self)
        } else {
            self.routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
        }
    }

    func didTapURL(_ cell: MayaCreditContactInformationTableViewCell, url: URL) {
        if url == Constants.WebView.serviceAdvisories.baseUrl {
            analyticsService.logMayaEvents(action: .tap(Analytics.Credit.servieAdvisories))
        }
        routeActionDelegate?.didRequestWebView(self, with: url, willPresent: true, completion: nil)
    }
}

// MARK: MayaCreditHelpCenterTableViewCellDelegate Functions
extension MayaCreditViewController: MayaCreditHelpCenterTableViewCellDelegate {
    func didTapHelpCenter(_ cell: MayaCreditHelpCenterTableViewCell, url: URL) {
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditActivate.help))
        routeActionDelegate?.didRequestWebView(self, with: url, willPresent: true, completion: nil)
    }
}

// MARK: MayaCreditErrorTableViewCellDelegate Functions
extension MayaCreditViewController: MayaCreditErrorTableViewCellDelegate {
    func didTapRetry(_ cell: MayaCreditErrorTableViewCell) {
        tableView.contentOffset = CGPoint(x: 0, y: 0)
        tableView.layoutIfNeeded()
        refresh()
    }
}

// MARK: NewMayaAvailableCreditTableViewCellDelegate Functions
extension MayaCreditViewController: NewMayaAvailableCreditTableViewCellDelegate {
    func didTapAssignContactReference(_ cell: NewMayaAvailableCreditTableViewCell) {
        if let account = viewModel?.creditAccount.value {
            delegate?.didRequestMayaCreditUpdatePersonalDetailsFlow(self, term: viewModel?.eligibility.value?.term, feeRate: viewModel?.feeRate.value, account: account)
        }
    }

    func didTapShowHideBalance(_ cell: NewMayaAvailableCreditTableViewCell) {
        viewModel?.toggleHideBalance()
    }

    func didTapTransferToWallet(_ cell: NewMayaAvailableCreditTableViewCell) {
        guard let viewModel = viewModel else { return }

        var attributes: [AnalyticsAttributeKey: Any] = [AnalyticsAttributeKey.design: Constants.MayaCredit.designV2_1]

        analyticsService.logMayaEvents(action: .tap(Analytics.NewMayaCreditSummary.transferToWallet), keyAttributes: attributes)

        if let account = viewModel.creditAccount.value, let url = viewModel.buildPayNowDeeplink() {
            let status = LoanAccountStatus(rawValue: account.status)
            let isAccountPasDue = status == LoanAccountStatus.pastDue && account.daysInArrears > 0

            if status == LoanAccountStatus.suspended || isAccountPasDue {
                let alertViewModel = viewModel.buildTransferAlertViewModel(account: account, url: url)
                showLockedErrorModal(account: account, alertViewModel: alertViewModel, url: url)
            } else {
                startTransferToWalletFlow()
            }
        }
    }
}

// MARK: NewMayaManageMyCreditTableViewCellDelegate Functions
extension MayaCreditViewController: NewMayaManageMyCreditTableViewCellDelegate {
    func didTapAccountSummary(_ cell: NewMayaManageMyCreditTableViewCell) {
        analyticsService.logMayaEvents(action: .tap(Analytics.NewMayaCreditSummary.accountSummary))
        if let term = viewModel?.currentLoanApplication.value?.term,
           let account = viewModel?.creditAccount.value {
            let eligibilityTerm = viewModel?.eligibility.value?.term
            delegate?.didRequestAccountSummaryFlow(self, term: term, account: account, eligibilityTerm: eligibilityTerm)
        }
    }
}

// MARK: MayaBillingStatementRevampTableViewCellDelegate Functions
extension MayaCreditViewController: MayaBillingStatementRevampTableViewCellDelegate {
    func didTapPayNow(_ cell: MayaBillingStatementRevampTableViewCell) {
        guard let viewModel = viewModel, let url = viewModel.buildPayEarlyCreditBillDeeplink() else { return }
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditBillingStatement.payNow))
        startPayBillFlow(url: url)
    }

    func didTapViewStatement(_ cell: MayaBillingStatementRevampTableViewCell) {
        guard let viewModel = viewModel else { return }
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditBillingStatement.statement))
        viewModel.billingStatementUrlAction.apply().start()
    }

    func didTapPayThisBill(_ cell: MayaBillingStatementRevampTableViewCell) {
        guard let viewModel = viewModel, let url = viewModel.buildPayNowDeeplink() else { return }
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditBillingStatement.payBillNow))
        startPayBillFlow(url: url)
    }

    func didTapLatestBillInfo(_ cell: MayaBillingStatementRevampTableViewCell) {
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditBillingStatement.billToolTip))
        delegate?.didRequestLatestBillInfoFlow(self)
    }
}

// MARK: MayaCreditTransactionsTableViewCellDelegate Functions
extension MayaCreditViewController: MayaCreditTransactionsTableViewCellDelegate {
    func refreshHeightOfCell(_ cell: MayaCreditTransactionsTableViewCell) {
        self.refreshHeightOfCell()
    }

    func didTapSeeAll(_ cell: MayaCreditTransactionsTableViewCell) {
        guard let account = viewModel?.creditAccount.value else { return }
        delegate?.didRequestTransactionFlow(self, account: account)
    }
}

// MARK: MayaCreditPartnerMerchantTableViewCellDelegate Functions
extension MayaCreditViewController: MayaCreditPartnerMerchantTableViewCellDelegate {
    func didTapPartnerMerchant(_ cell: MayaCreditPartnerMerchantTableViewCell) {
        guard let partnerMerchantsUrl = Constants.WebView.mayaCreditPartnerMerchants.url else {
            return
        }
        // TODO: VELARYON - add analytics and deeplink to gigalife
        routeActionDelegate?.didRequestWebView(self, with: partnerMerchantsUrl, willPresent: true, completion: nil)
    }

    func didTapSeeMorePartners(_ cell: MayaCreditPartnerMerchantTableViewCell) {
        guard let partnerMerchantsUrl = Constants.WebView.mayaCreditPartnerMerchants.url else {
            return
        }
        // TODO: VELARYON - add analytics
        routeActionDelegate?.didRequestWebView(self, with: partnerMerchantsUrl, willPresent: true, completion: nil)
    }
}

// MARK: MayaCreditInitialEligibilityTableViewCellDelegate Functions
extension MayaCreditViewController: MayaCreditInitialEligibilityTableViewCellDelegate {
    func didTapApplyNow(_ cell: MayaCreditInitialEligibilityTableViewCell) {
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditActivate.activate))
        delegate?.didRequestMayaCreditInitalEligibilityFlow(self)
    }
}

// MARK: MayaCreditWrittenOffTableViewCellDelegate Functions
extension MayaCreditViewController: MayaCreditWrittenOffTableViewCellDelegate {
    func didTapHelpCenter(_ cell: MayaCreditWrittenOffTableViewCell, url: URL) {
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditActivate.help))
        routeActionDelegate?.didRequestWebView(self, with: url, willPresent: true, completion: nil)
    }
}

// MARK: UIScrollViewDelegate Methods
extension MayaCreditViewController: UIScrollViewDelegate {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        refreshControl.containingScrollDidScroll(scrollView)
    }
}
