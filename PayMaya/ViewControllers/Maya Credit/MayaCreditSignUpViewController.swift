//
//  MayaCreditSignUpViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 7/26/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import ConfigurationService
import Injector
import ReactiveSwift
import UIKit

protocol MayaCreditSignUpViewControllerDelegate: AnyObject {
    func didTapCustomizeBillingEndDate(_ viewController: MayaCreditSignUpViewController, billingEndDateSelection: BillingEndDateSelection)
    func didTapGender(_ viewController: MayaCreditSignUpViewController, personalDetailsSelection: PersonalDetailsSelection)
    func didTapMaritalStatus(_ viewController: MayaCreditSignUpViewController, personalDetailsSelection: PersonalDetailsSelection)
    func didTapTermsAndConditions(_ viewController: MayaCreditSignUpViewController, activationTerm: ActivationTerm)
    func didTapSampleComputation(_ viewController: MayaCreditSignUpViewController)
    func didSuccessfullySubmitted(_ viewController: MayaCreditSignUpViewController, response: OTPResponse)
    func didTapContinue(_ viewController: MayaCreditSignUpViewController)
}

class MayaCreditSignUpViewController: FormsViewController, MayaAnalyticsDurationManagerProtocol, MayaProgressBarProtocol {
    var segment: Int = 1

    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject var configurationService: ConfigurationService

    @IBOutlet weak var headerLabel: UILabel!
    @IBOutlet weak var subheaderLabel: UILabel!

    @IBOutlet weak var billingDetailsSectionLabel: UILabel!
    @IBOutlet weak var personalDetailsSectionLabel: UILabel!
    @IBOutlet weak var learnMoreSectionLabel: UILabel!

    @IBOutlet weak var billingInfoLabel: UILabel!
    @IBOutlet weak var emailInfoLabel: UILabel!

    @IBOutlet weak var billingEndDateView: MayaValidationFieldView! {
        didSet {
            setBillingFieldView()
        }
    }

    @IBOutlet weak var emailAddressView: MayaValidationFieldView! {
        didSet {
            setBillingEmailView()
        }
    }

    var billingEndDateTextField: TextField {
        return billingEndDateView.textField
    }

    var emailAddressTextField: TextField {
        return emailAddressView.textField
    }

    @IBOutlet weak var alternativeMobileNumberView: MayaValidationFieldView! {
        didSet {
            setAlternativeMobileNumber()
        }
    }

    @IBOutlet weak var genderView: MayaValidationFieldView! {
        didSet {
            setGenderFieldView()
        }
    }

    @IBOutlet weak var maritalStatusView: MayaValidationFieldView! {
        didSet {
            setMaritalStatusFieldView()
        }
    }

    @IBOutlet weak var mothersMaidenFirstNameView: MayaValidationFieldView! {
        didSet {
            setMothersMaidenNameFieldView(mothersMaidenFirstNameView)
        }
    }

    @IBOutlet weak var mothersMaidenMiddleNameView: MayaValidationFieldView! {
        didSet {
            setMothersMaidenNameFieldView(mothersMaidenMiddleNameView)
        }
    }

    @IBOutlet weak var mothersMaidenLastNameView: MayaValidationFieldView! {
        didSet {
            setMothersMaidenNameFieldView(mothersMaidenLastNameView)
        }
    }

    var genderTextField: TextField {
        return genderView.textField
    }

    var maritalStatusTextField: TextField {
        return maritalStatusView.textField
    }

    var alternativeMobileNumberTextField: TextField {
        return alternativeMobileNumberView.textField
    }

    var mothersMaidenFirstNameTextField: TextField {
        return mothersMaidenFirstNameView.textField
    }

    var mothersMaidenMiddleNameTextField: TextField {
        return mothersMaidenMiddleNameView.textField
    }

    var mothersMaidenLastNameTextField: TextField {
        return mothersMaidenLastNameView.textField
    }

    @IBOutlet weak var learnMoreLabel: UILabel!
    @IBOutlet weak var lowServiceFeesSection: UIView!
    @IBOutlet weak var lowServiceFeesLabel: UILabel!
    @IBOutlet weak var lowServicesFeesToggleButton: UIButton!
    @IBOutlet weak var lowServiceFeesTextView: UITextView!
    @IBOutlet weak var automatedPaymentsSection: UIView!
    @IBOutlet weak var automatedPaymentLabel: UILabel!
    @IBOutlet weak var automatedPaymentToggleButton: UIButton!
    @IBOutlet weak var automatedPaymentTextView: UITextView!
    @IBOutlet weak var infoTextView: UITextView!
    @IBOutlet weak var activateButton: MayaButton!
    @IBOutlet weak var hasNoMiddleNameCheckBox: MayaCheckBox!

    @IBOutlet weak var personalDetailsView: UIView!
    @IBOutlet weak var learnMoreView: UIView!
    @IBOutlet weak var infoView: UIView!
    @IBOutlet weak var mothersMaidenNameView: UIView!

    weak var delegate: MayaCreditSignUpViewControllerDelegate?

    private let termsAndConditionURL = URL(string: "signup/termsandconditions")!
    private let disclosureStatementURL = URL(string: "signup/disclosurestatement")!
    private let truthAndTransparencyInLendingURL = URL(string: "signup/truthAndtransparencyinlending")!
    private let sampleComputationURL = URL(string: "signup/samplecomputation")!
    private let switchToURL = URL(string: "signup/switchto")!

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override var mayaNavigationBarScrollView: UIScrollView? {
        return scrollView
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        activateButton.isEnabled = false

        let tap = UITapGestureRecognizer(target: self, action: #selector(onTapParentView))
        view.addGestureRecognizer(tap)
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.NewMayaCreditActivation())
        super.viewWillAppear(animated)
        setupView()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        setRightButtonItemTitle("1/3")
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        scrollView.setContentOffset(.zero, animated: true)
    }

    override func setupAccessibilityIdentifiers() {
        navigationItem.leftBarButtonItem?.isAccessibilityElement = true
        navigationItem.leftBarButtonItem?.accessibilityIdentifier = "pmios_maya_credit_activation_back_button"
    }

    @IBAction func didTapCustomizeBillingEndDate(_ sender: Any) {
        guard let viewModel = viewModel, let billingEndDateSelection = viewModel.getBillingEndDateSelection() else { return }
        analyticsService.logMayaEvents(action: .tap(Analytics.NewMayaCreditActivation.setEndDate))
        delegate?.didTapCustomizeBillingEndDate(self, billingEndDateSelection: billingEndDateSelection)
    }

    @IBAction func didTapSubmit(_ sender: Any) {
        delegate?.didTapContinue(self)
    }

    @IBAction func toggleLowServices(_ sender: Any) {
        toggleView(lowServicesFeesToggleButton, lowServiceFeesTextView)
    }

    @IBAction func toggleAutomatedPayments(_ sender: Any) {
        toggleView(automatedPaymentToggleButton, automatedPaymentTextView)
    }

    @IBAction func didTapHasNoMiddleNameCheckBox(_ sender: Any) {
        var status = hasNoMiddleNameCheckBox.status
        status.toggle()
        handleCheckBoxUpdate(checked: status)
    }

    @objc private func onTapParentView(_ gesture: UIGestureRecognizer) {
        emailAddressTextField.resignFirstResponder()
        alternativeMobileNumberTextField.resignFirstResponder()
        mothersMaidenFirstNameTextField.resignFirstResponder()
        mothersMaidenMiddleNameTextField.resignFirstResponder()
        mothersMaidenLastNameTextField.resignFirstResponder()
    }
}

// MARK: - ViewModelBindable
extension MayaCreditSignUpViewController: ViewModelBindable {
    func binding(_ viewModel: NewMayaCreditActivationViewModelProtocol?) {
        guard let viewModel = viewModel else { return }

        viewModel.emailField.mutableProperty <~ emailAddressTextField.reactive.continuousTextValues
        emailAddressView.bindFieldProperty(viewModel.emailField)

        viewModel.alternativeMobileNumberField.mutableProperty <~ alternativeMobileNumberTextField.reactive.continuousTextValues
        alternativeMobileNumberView.bindFieldProperty(viewModel.alternativeMobileNumberField)

        viewModel.mothersMaidenFirstNameField.mutableProperty <~ mothersMaidenFirstNameTextField.reactive.continuousTextValues
        mothersMaidenFirstNameView.bindFieldProperty(viewModel.mothersMaidenFirstNameField)

        viewModel.mothersMaidenMiddleNameField.mutableProperty <~ mothersMaidenMiddleNameTextField.reactive.continuousTextValues
        mothersMaidenMiddleNameView.bindFieldProperty(viewModel.mothersMaidenMiddleNameField)

        viewModel.mothersMaidenLastNameField.mutableProperty <~ mothersMaidenLastNameTextField.reactive.continuousTextValues
        mothersMaidenLastNameView.bindFieldProperty(viewModel.mothersMaidenLastNameField)

        setBillingEndDate(viewModel.billingEndDate.value)

        hasNoMiddleNameCheckBox.setStatus(viewModel.isNoMaidenMiddleNameChecked.value)
        formFields = [mothersMaidenFirstNameTextField, mothersMaidenMiddleNameTextField, mothersMaidenLastNameTextField]
        setKeyboardReturnTypeOfTextFields()
        mothersMaidenLastNameTextField.returnKeyType = .done
        lastFormFieldCallback = { [weak self] in
            guard let self = self else { return }

            self.view.endEditing(true)
        }

        viewModel.submitLoanAction.values.observe(on: UIScheduler()).observeValues { [weak self] response in
            guard let self = self else { return }
            self.analyticsService.logMayaEvents(action: .success(Analytics.NewMayaCreditActivation()))
            self.navigationController?.popViewController(animated: false)
            self.delegate?.didSuccessfullySubmitted(self, response: response)
        }?.addToDisposeBag(disposeBag)

        viewModel.submitLoanAction.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            executing ? self.showMayaLoader(nil, CommonStrings.Maya.Loader.Default.message) : self.hideMayaLoader()
        }?.addToDisposeBag(disposeBag)

        viewModel.submitLoanAction.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self else { return }
            switch error.type {
            case .sessionTimeout(let message):
                self.analyticsService.logMayaEvents(action: .failure(Analytics.NewMayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: message])
            case .noInternetConnection:
                break
            default:
                self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                    AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                    AnalyticsAttributeKey.error.rawValue: error.type
                ])
            }
            if var errorViewModel = error.viewModel {
                self.analyticsService.logMayaEvents(action: .failure(Analytics.NewMayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: errorViewModel.message])
                errorViewModel.closeButtonClosure = {
                    self.navigationController?.popViewController(animated: true)
                }
                self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
            } else {
                self.analyticsService.logMayaEvents(action: .failure(Analytics.NewMayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: ""])
            }
        }?.addToDisposeBag(disposeBag)

        viewModel.billingEndDate.signal.observe(on: UIScheduler()).observeValues { [weak self] display in
            guard let self = self else { return }
            self.setBillingEndDate(display)
        }?.addToDisposeBag(disposeBag)

        viewModel.emailField.mutableProperty.signal.observe(on: UIScheduler()).observeValues { [weak self] email in
            guard let self = self else { return }
            self.emailAddressTextField.text = email
        }?.addToDisposeBag(disposeBag)

        viewModel.isButtonEnabled.signal
            .observe(on: UIScheduler())
            .observeValues({ [weak self] isEnabled in
                guard let self = self else { return }
                self.activateButton.isEnabled = isEnabled
            })?.addToDisposeBag(disposeBag)

        viewModel.setAcceptTerms()

        viewModel.alternativeMobileNumberField.mutableProperty.signal.observe(on: UIScheduler()).observeValues { [weak self] mobileNumber in
            guard let self = self else { return }
            self.alternativeMobileNumberTextField.text = mobileNumber
        }?.addToDisposeBag(disposeBag)

        viewModel.genderField.mutableProperty.signal.observe(on: UIScheduler()).observeValues { [weak self] gender in
            guard let self = self else { return }
            self.genderTextField.text = gender
        }?.addToDisposeBag(disposeBag)

        viewModel.maritalStatusField.mutableProperty.signal.observe(on: UIScheduler()).observeValues { [weak self] maritalStatus in
            guard let self = self else { return }
            self.maritalStatusTextField.text = maritalStatus
        }?.addToDisposeBag(disposeBag)

        viewModel.mothersMaidenFirstNameField.mutableProperty.signal.observe(on: UIScheduler()).observeValues { [weak self] name in
            guard let self = self else { return }
            self.mothersMaidenFirstNameTextField.text = name
        }?.addToDisposeBag(disposeBag)

        viewModel.mothersMaidenMiddleNameField.mutableProperty.signal.observe(on: UIScheduler()).observeValues { [weak self] name in
            guard let self = self else { return }
            self.mothersMaidenMiddleNameTextField.text = name
        }?.addToDisposeBag(disposeBag)

        viewModel.mothersMaidenLastNameField.mutableProperty.signal.observe(on: UIScheduler()).observeValues { [weak self] name in
            guard let self = self else { return }
            self.mothersMaidenLastNameTextField.text = name
        }?.addToDisposeBag(disposeBag)

        viewModel.isNoMaidenMiddleNameChecked.signal.observe(on: UIScheduler()).observeValues { [weak self] isChecked in
            guard let self = self else { return }
            if isChecked, let middleNameIndex = formFields.firstIndex(of: mothersMaidenMiddleNameTextField) {
                formFields.remove(at: middleNameIndex)
            } else if let firstNameIndex = formFields.firstIndex(of: mothersMaidenFirstNameTextField), !formFields.contains(mothersMaidenMiddleNameTextField) {
                formFields.insert(mothersMaidenMiddleNameTextField, at: formFields.index(after: firstNameIndex))
            }
            hasNoMiddleNameCheckBox.setStatus(isChecked)
            mothersMaidenMiddleNameTextField.isEnabled = !isChecked
            setKeyboardReturnTypeOfTextFields()
        }?.addToDisposeBag(disposeBag)

        // get contact reference
        viewModel.getContactReference.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            if executing {
                self.showMayaLoader()
            } else {
                self.hideMayaLoader()
            }
        }?.addToDisposeBag(disposeBag)

        // submit contact reference
        viewModel.submitContactReference.values.observe(on: UIScheduler()).observeValues { [weak self] _ in
            guard let self = self else { return }
            self.viewModel?.submitLoanAction.apply().start()
        }?.addToDisposeBag(disposeBag)

        viewModel.submitContactReference.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            executing ? self.showMayaLoader(nil, CommonStrings.Maya.Loader.Default.message) : self.hideMayaLoader()
        }?.addToDisposeBag(disposeBag)

        viewModel.submitContactReference.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self else { return }
            switch error.type {
            case .sessionTimeout(let message):
                self.analyticsService.logMayaEvents(action: .failure(Analytics.NewMayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: message])
            case .noInternetConnection:
                break
            default:
                self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                    AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                    AnalyticsAttributeKey.error.rawValue: error.type
                ])
            }
            if var errorViewModel = error.viewModel {
                self.analyticsService.logMayaEvents(action: .failure(Analytics.NewMayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: errorViewModel.message])
                errorViewModel.closeButtonClosure = {
                    self.navigationController?.popViewController(animated: true)
                }
                self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
            } else {
                self.analyticsService.logMayaEvents(action: .failure(Analytics.NewMayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: ""])
            }
        }?.addToDisposeBag(disposeBag)

        viewModel.getPersonDetails.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            executing ? self.showMayaLoader() : self.hideMayaLoader()
        }?.addToDisposeBag(disposeBag)

        viewModel.submitPersonDetails.values.observe(on: UIScheduler()).observeValues { [weak self] _ in
            guard let self = self else { return }
            self.viewModel?.submitLoanAction.apply().start()
        }?.addToDisposeBag(disposeBag)

        viewModel.submitPersonDetails.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            executing ? self.showMayaLoader(nil, CommonStrings.Maya.Loader.Default.message) : self.hideMayaLoader()
        }?.addToDisposeBag(disposeBag)

        viewModel.submitPersonDetails.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self else { return }
            switch error.type {
            case .sessionTimeout(let message):
                self.analyticsService.logMayaEvents(action: .failure(Analytics.NewMayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: message])
            case .noInternetConnection:
                break
            default:
                self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                    AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                    AnalyticsAttributeKey.error.rawValue: error.type
                ])
            }
            if var errorViewModel = error.viewModel {
                self.analyticsService.logMayaEvents(action: .failure(Analytics.NewMayaCreditActivation()), keyAttributes: [AnalyticsAttributeKey.reason: errorViewModel.message])
                errorViewModel.closeButtonClosure = {
                    self.navigationController?.popViewController(animated: true)
                }
                self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
            } else {
                self.analyticsService.logMayaEvents(action: .failure(Analytics.NewMayaCreditActivation()))
            }
        }?.addToDisposeBag(disposeBag)

        self.emailAddressTextField.text = viewModel.emailField.value

        viewModel.getPersonDetails.apply().start()
    }

    private func setBillingEndDate(_ date: String) {
        guard !date.isEmpty else {
            return
        }

        billingEndDateView.isHidden = false
        billingEndDateTextField.text = L10n.Maya.Credit.Signup.Billing.End.date(date)
    }
}

private extension MayaCreditSignUpViewController {
    private func setupView() {
        infoTextView.delegate = self
        infoTextView.textContainerInset = .zero
        infoTextView.textContainer.lineFragmentPadding = 0

        let attributedString = NSMutableAttributedString(
            string: L10n.Maya.Credit.Signup.info,
            attributes: [
                .foregroundColor: CommonAsset.MayaColors.Grey.grey5.color,
                .font: CommonFontFamily.Jeko.semiBold.font(size: 12)!
            ]
        )

        attributedString.addAttribute(.link, value: termsAndConditionURL, range: attributedString.mutableString.range(of: L10n.Maya.Credit.Signup.termsandconditions))
        attributedString.addAttribute(.link, value: disclosureStatementURL, range: attributedString.mutableString.range(of: L10n.Maya.Credit.Signup.disclosurestatement))
        attributedString.addAttribute(.link, value: truthAndTransparencyInLendingURL, range: attributedString.mutableString.range(of: L10n.Maya.Credit.Signup.truthandtransparency))

        infoTextView.linkTextAttributes = [.foregroundColor: CommonAsset.MayaColors.Primary.primaryGrownGreen.color]
        infoTextView.attributedText = attributedString
        infoTextView.isUserInteractionEnabled = true

        learnMoreLabel.text = L10n.Maya.Credit.Signup.Learnmore.title.uppercased()
        lowServiceFeesLabel.text = L10n.Maya.Credit.Signup.Lowservicefees.title
        lowServiceFeesTextView.text = L10n.Maya.Credit.Signup.Lowservicefees.content
        automatedPaymentLabel.text = L10n.Maya.Credit.Signup.Automatedpayments.title
        automatedPaymentTextView.text = L10n.Maya.Credit.Signup.Automatedpayments.content

        setupSectionView(lowServiceFeesSection)
        lowServiceFeesTextView.isHidden = true

        setupSectionView(automatedPaymentsSection)
        automatedPaymentTextView.isHidden = true

        lowServicesFeesToggleButton.setTitle("", for: .normal)
        lowServicesFeesToggleButton.layer.cornerRadius = 8
        automatedPaymentToggleButton.setTitle("", for: .normal)
        automatedPaymentToggleButton.layer.cornerRadius = 8

        // Contact reference related views
        infoView.isHidden = true

        billingDetailsSectionLabel.isHidden = false

        headerLabel.text = L10n.Maya.Credit.Signup.Contact.Reference.title
        subheaderLabel.text = L10n.Maya.Credit.Signup.Contact.Reference.subtitle
        subheaderLabel.isHidden = false

        billingDetailsSectionLabel.text = L10n.Maya.Credit.Signup.Contact.Reference.Section.Billing.title.uppercased()
        personalDetailsSectionLabel.text = L10n.Maya.Credit.Signup.Contact.Reference.Section.Personal.title.uppercased()

        billingDetailsSectionLabel.setTextSpacingBy(value: 1)
        personalDetailsSectionLabel.setTextSpacingBy(value: 1)
        learnMoreSectionLabel.setTextSpacingBy(value: 1)

        billingInfoLabel.text = L10n.Maya.Credit.Signup.Contact.Reference.Section.Billing.Enddate.info
        emailInfoLabel.text = L10n.Maya.Credit.Signup.Contact.Reference.Section.Billing.Emailaddress.info

        automatedPaymentTextView.text = L10n.Maya.Credit.Signup.Contact.Reference.Automatedpayments.content

        activateButton.setTitle(CommonStrings.Common.continue, for: .normal)
    }

    private func setupSectionView(_ view: UIView) {
        view.layer.cornerRadius = 16
        view.layer.borderWidth = 1
        view.layer.borderColor = CommonAsset.MayaColors.Grey.grey3.color.cgColor
    }

    private func setupToggleView(_ view: UIImageView) {
        view.image = CommonAsset.Images.Icons.iconPlus.image.withAlignmentRectInsets(UIEdgeInsets(top: -4, left: -4, bottom: -4, right: -4))
        view.layer.cornerRadius = 8
        view.backgroundColor = CommonAsset.MayaColors.Grey.grey1.color
        view.clipsToBounds = true
    }

    private func setBillingFieldView() {
        billingEndDateView.setTextFieldProperties(capitalizationType: .words)
        billingEndDateView.firstActionImage = CommonAsset.Images.Icons.iconDate.image
        billingEndDateView.firstActionImageHandler = { [weak self] in
            guard let self = self, let viewModel = self.viewModel, let billingEndDateSelection = viewModel.getBillingEndDateSelection() else { return }
            self.analyticsService.logMayaEvents(action: .tap(Analytics.NewMayaCreditActivation.setEndDate))
            self.delegate?.didTapCustomizeBillingEndDate(self, billingEndDateSelection: billingEndDateSelection)
        }
        billingEndDateView.textField.delegate = self
    }

    private func setBillingEmailView() {
        emailAddressView.setTextFieldProperties(keyboardType: .emailAddress)
        emailAddressView.isInlineErrorIconHidden = true
        emailAddressView.hintText = L10n.Maya.Credit.Signup.Contact.Reference.Section.Billing.emailaddress
    }

    private func setAlternativeMobileNumber() {
        alternativeMobileNumberView.setTextFieldProperties(keyboardType: .phonePad)
        alternativeMobileNumberView.isInlineErrorIconHidden = true
        alternativeMobileNumberView.textField.delegate = self
        alternativeMobileNumberView.hintText = L10n.Maya.Credit.Signup.Contact.Reference.Section.Billing.Altmobile.label
    }

    private func setGenderFieldView() {
        genderView.setTextFieldProperties(capitalizationType: .words)
        genderView.hintText = CommonStrings.Common.gender
        genderView.showChevron()
        genderView.textField.delegate = self
    }

    private func setMaritalStatusFieldView() {
        maritalStatusView.setTextFieldProperties(capitalizationType: .words)
        maritalStatusView.hintText = CommonStrings.Common.maritalstatus
        maritalStatusView.showChevron()
        maritalStatusView.textField.delegate = self
    }

    private func setMothersMaidenNameFieldView(_ validationField: MayaValidationFieldView) {
        validationField.setTextFieldProperties(keyboardType: .asciiCapable, capitalizationType: .words)
        validationField.textField.autocorrectionType = .no
        validationField.textField.spellCheckingType = .no
        validationField.isInlineErrorIconHidden = true
        validationField.textField.delegate = self
    }

    private func toggleView(_ button: UIButton, _ view: UIView) {
        view.isHidden = !view.isHidden
        let image = view.isHidden ? CommonAsset.Images.Icons.iconPlus.image : CommonAsset.Images.Icons.iconMinus.image
        button.setImage(image, for: .normal)
    }

    private func handleCheckBoxUpdate(checked: Bool) {
        hasNoMiddleNameCheckBox.setStatus(checked)
        if checked {
            mothersMaidenMiddleNameTextField.becomeFirstResponder()
            mothersMaidenMiddleNameTextField.clearValues()
        }
        viewModel?.isNoMaidenMiddleNameChecked.value = checked
    }
}

extension MayaCreditSignUpViewController {
    override func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        guard let viewModel = viewModel else { return true }

        if textField.text != nil && textField === alternativeMobileNumberTextField {
            let text = NSString(string: textField.text ?? "").replacingCharacters(in: range, with: string)

            if let formattedText = viewModel.formatAlternativeNumber(with: text, and: string) {
                alternativeMobileNumberView.textField.text = formattedText
                return false
            }
        }

        return true
    }

    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        switch URL {
        case termsAndConditionURL:
            if let activationTerm = viewModel?.activationTerms.value[PoliciesTypes.termsAndConditions.index] {
                didTapTermsAndConditions(self, activationTerm: activationTerm)
            }
        case truthAndTransparencyInLendingURL:
            if let activationTerm = viewModel?.activationTerms.value[PoliciesTypes.truthAndTransparency.index] {
                didTapTermsAndConditions(self, activationTerm: activationTerm)
            }
        case disclosureStatementURL:
            if let activationTerm = viewModel?.activationTerms.value[PoliciesTypes.disclosureStatement.index] {
                didTapTermsAndConditions(self, activationTerm: activationTerm)
            }
        case sampleComputationURL:
            delegate?.didTapSampleComputation(self)
        default: break
            // Do nothing.
        }

        return false
    }

    func didTapTermsAndConditions(_ viewController: MayaCreditSignUpViewController, activationTerm: ActivationTerm) {
        delegate?.didTapTermsAndConditions(self, activationTerm: activationTerm)
    }
}

// MARK: - UITextFieldDelegate Methods
extension MayaCreditSignUpViewController {
    func textFieldShouldBeginEditing(_ textField: UITextField) -> Bool {
        if textField == billingEndDateTextField {
            view.endEditing(true)
            guard let viewModel = self.viewModel, let billingEndDateSelection = viewModel.getBillingEndDateSelection() else { return false }
            self.analyticsService.logMayaEvents(action: .tap(Analytics.NewMayaCreditActivation.setEndDate))
            self.delegate?.didTapCustomizeBillingEndDate(self, billingEndDateSelection: billingEndDateSelection)
            return false
        } else if textField == genderTextField {
            view.endEditing(true)
            guard let viewModel = self.viewModel else { return false }
            self.delegate?.didTapGender(self, personalDetailsSelection: viewModel.getPersonalDetailsSelection())
            return false
        } else if textField == maritalStatusTextField {
            view.endEditing(true)
            guard let viewModel = self.viewModel else { return false }
            self.delegate?.didTapMaritalStatus(self, personalDetailsSelection: viewModel.getPersonalDetailsSelection())
            return false
        }

        return true
    }
}
