//
//  MayaCreditHomeViewController.swift
//  PayMaya
//
//  Created by <PERSON>az<PERSON> Paola Barroga on 11/3/21.
//  Copyright © 2021 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Foundation
import Injector
import ReactiveSwift

protocol MayaCreditHomeViewControllerDelegate: AnyObject {
    func didTapTransferCredit(_ viewController: MayaCreditHomeViewController)
    func didTapSettings(_ viewController: MayaCreditHomeViewController, creditAccount: MayaCreditAccount, term: MayaCreditCurrentLoanApplication.Term)
}

class MayaCreditHomeViewController: ViewController, BaseLoadingViewControllerProtocol, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    @IBOutlet weak var footerView: UIView!
    @IBOutlet weak var baseContainerView: UIView!
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet var defaultErrorView: DefaultErrorView!

    var loadingType: LoadingType = .progressHUD

    var baseErrorView: UIView! {
        defaultErrorView.delegate = self
        return defaultErrorView
    }

    var baseContentView: UIView! {
        return tableView
    }

    private let refreshControl = UIRefreshControl()

    weak var delegate: MayaCreditHomeViewControllerDelegate?

    override var shouldUseAnimatedNavigationBar: Bool {
        return true
    }

    private let minFooterHeight: CGFloat = 60

    override func viewDidLoad() {
        super.viewDidLoad()
        baseContentView.hide()
        setupTableView()
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.MayaCreditSummary())
        super.viewWillAppear(animated)
        navigationItem.leftBarButtonItem?.setTitleTextAttributes([
            NSAttributedString.Key.font: CommonFontFamily.WorkSans.semiBold.font(size: 16)!,
            NSAttributedString.Key.foregroundColor: CommonAsset.Colors.Constants.newBlack.color
        ], for: .normal)

        navigationItem.rightBarButtonItem?.setTitleTextAttributes([
            NSAttributedString.Key.font: CommonFontFamily.WorkSans.semiBold.font(size: 16)!,
            NSAttributedString.Key.foregroundColor: CommonAsset.Colors.Constants.newBlack.color
        ], for: .normal)

        navigationController?.navigationBar.titleTextAttributes = [
            NSAttributedString.Key.font: CommonFontFamily.WorkSans.bold.font(size: 18)!,
            NSAttributedString.Key.foregroundColor: CommonAsset.Colors.Constants.newBlack.color
        ]
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        refresh()
    }

    private func setupTableView() {
        tableView.delegate = self
        tableView.dataSource = self
        tableView.tableFooterView = footerView
        refreshControl.addTarget(self, action: #selector(refresh), for: .valueChanged)
        tableView.refreshControl = refreshControl
        tableView.backgroundColor = CommonAsset.Colors.Gray.gray20T.color
    }

    private func adjustFooterHeight() {
        let cells = tableView.visibleCells
        var height: CGFloat = 0
        for i in 0..<cells.count {
             height += cells[i].frame.height
        }
        height = tableView.bounds.height - ceil(height)

        height = height > minFooterHeight ? height : minFooterHeight

        if let footerView = tableView.tableFooterView {
            var footerFrame = footerView.frame
            footerFrame.size.height = height
            footerView.frame = footerFrame
            tableView.tableFooterView = footerView
        }
    }

    @IBAction func didTapSettings(_ sender: Any) {
        guard let viewModel = viewModel, let creditAccount = viewModel.creditAccount else { return }
        delegate?.didTapSettings(self, creditAccount: creditAccount, term: viewModel.term)
    }

    override func setupAccessibilityIdentifiers() {
        navigationItem.leftBarButtonItem?.isAccessibilityElement = true
        navigationItem.leftBarButtonItem?.accessibilityIdentifier = "pmios_maya_credit_home_back_button"

        navigationItem.rightBarButtonItem?.isAccessibilityElement = true
        navigationItem.rightBarButtonItem?.accessibilityIdentifier = "pmios_maya_credit_home_settings_button"
    }

    @objc private func refresh() {
        guard let viewModel = viewModel else { return }
        viewModel.mayaCreditHomeAction.apply().start()
    }
}

// MARK: ViewModelBindable protocol
extension MayaCreditHomeViewController: ViewModelBindable {
    func binding(_ viewModel: MayaCreditHomeViewModelProtocol?) {
        guard let viewModel = viewModel else { return }

        let isExecutingSignal = Signal.combineLatest(viewModel.mayaCreditHomeAction.isExecuting.signal, viewDidAppearSignal.signal)

        isExecutingSignal
            .take(first: 1)
            .observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting, _ in
                guard let self = self, isExecuting else { return }
                self.showLoadingView()
            }?.addToDisposeBag(disposeBag)

        viewModel.mayaCreditHomeAction.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self = self else { return }
                self.showErrorView(shouldPinToSuperview: true)
                self.refreshControl.endRefreshing()
                self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                    AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                    AnalyticsAttributeKey.error.rawValue: error.localizedDescription
                ])
            }?.addToDisposeBag(disposeBag)

        viewModel.mayaCreditHomeAction.values
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self = self else { return }
                self.refreshControl.endRefreshing()
                self.tableView.reloadData()
                self.adjustFooterHeight()
                self.showContentView()
            }?.addToDisposeBag(disposeBag)

        viewModel.billingStatementUrlAction.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                guard let self = self else { return }
                isExecuting ? self.showHud() : self.hideHud()
            }?.addToDisposeBag(disposeBag)

        viewModel.billingStatementUrlAction.values
            .observe(on: UIScheduler())
            .observeValues {  response in
                guard let url = URL(string: response.url) else { return }
                showExternalURI(url)
            }?.addToDisposeBag(disposeBag)

        viewModel.billingStatementUrlAction.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self = self else { return }
                switch error.type {
                case .sessionTimeout:
                    return
                case .noInternetConnection:
                    break
                default:
                    self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                        AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                        AnalyticsAttributeKey.error.rawValue: error.type
                    ])
                }
                if let errorViewModel = error.viewModel {
                    self.showErrorAlert(viewModel: errorViewModel)
                }
            }?.addToDisposeBag(disposeBag)
    }
}

// MARK: UITableViewDataSource protocol
extension MayaCreditHomeViewController: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let viewModel = viewModel else { return 0 }
        return viewModel.getRows(section: section).count
    }

    func numberOfSections(in tableView: UITableView) -> Int {
        guard let viewModel = viewModel else { return 0 }
        return viewModel.getSections().count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let viewModel = viewModel,
              let row = viewModel.getRows(section: indexPath.section)[safeIndex: indexPath.row],
              let tableViewCell = tableView.dequeueReusableCell(withIdentifier: row.identifier) else { return UITableViewCell() }
        switch row {
        case .summary(let summaryCell):
            if let cell = tableViewCell as? MayaCreditHomeSummaryTableViewCell {
                cell.delegate = self
                cell.bind(summaryCell)
            }
        case .billingStatement(let state):
            switch state {
            case .available(let billingStatementCell):
                if let cell = tableViewCell as? MayaCreditHomeBillingStatementTableViewCell {
                    cell.delegate = self
                    cell.bind(billingStatementCell)
                }
            case .unavailable:
                // No modification needed
                break
            }

        case .section:
            break
        }
        return tableViewCell
    }
}

// MARK: DefaultErrorViewDelegate protocol
extension MayaCreditHomeViewController: DefaultErrorViewDelegate {
    func didTapRetryAction(_ view: DefaultErrorView) {
        guard let viewModel = viewModel else { return }
        showLoadingView()
        viewModel.mayaCreditHomeAction.apply().start()
    }
}

// MARK: MayaCreditHomeSummaryTableViewCellDelegate protocol
extension MayaCreditHomeViewController: MayaCreditHomeSummaryTableViewCellDelegate {
    func didTapTransferCredit(_ cell: MayaCreditHomeSummaryTableViewCell) {
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditSummary.transfer))
        delegate?.didTapTransferCredit(self)
    }
}

// MARK: MayaCreditHomeBillingStatementTableViewCellDelegate protocol
extension MayaCreditHomeViewController: MayaCreditHomeBillingStatementTableViewCellDelegate {
    func didTapViewBill(_ cell: MayaCreditHomeBillingStatementTableViewCell) {
        guard let viewModel = viewModel else { return }
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditSummary.viewBill))
        viewModel.billingStatementUrlAction.apply().start()
    }

    func didTapPayBillNow(_ cell: MayaCreditHomeBillingStatementTableViewCell) {
        guard let viewModel = viewModel, let url = viewModel.buildPayCreditBillDeeplink() else { return }
        analyticsService.logMayaEvents(action: .tap(Analytics.MayaCreditSummary.payBill))
        routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
    }
}
