//
//  MayaCreditConfirmDetailsViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON>olo<PERSON> on 10/6/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Foundation
import Injector
import RavenLocation
import ReactiveSwift

protocol MayaCreditConfirmDetailsViewControllerDelegate: AnyObject {
    func didTapTermsAndConditions(_ viewController: MayaCreditConfirmDetailsViewController)
    func didTapDisclosureStatement(_ viewController: MayaCreditConfirmDetailsViewController)
    func didTapTruthAndTransparencyInLending(_ viewController: MayaCreditConfirmDetailsViewController)
    func didTapDataPrivacyPolicy(_ viewController: MayaCreditConfirmDetailsViewController)
    func didTapAutomatedPayments(_ viewController: MayaCreditConfirmDetailsViewController)
    func didTapConfirm(_ viewController: MayaCreditConfirmDetailsViewController)
    func willShowAllSetScreen(_ viewController: MayaCreditConfirmDetailsViewController, term: MayaCreditEligibility.Term)
    func willShowIneligibleView(_ viewController: MayaCreditConfirmDetailsViewController)
    func willStartCreditScoring(_ viewController: MayaCreditConfirmDetailsViewController, riskLevel: String)
}

class MayaCreditConfirmDetailsViewController: FormsViewController, MayaProgressBarProtocol, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject private var locationAuthorizerService: LocationAuthorizerServiceProtocol

    var segment: Int = 3

    let attributes = [
        AnalyticsAttributeKey.triggeredBy: Constants.Trigger.lending,
        AnalyticsAttributeKey.eventTrigger: Constants.EventTrigger.credit
    ]

    weak var delegate: MayaCreditConfirmDetailsViewControllerDelegate?

    @IBOutlet weak var headerLabel: UILabel!
    @IBOutlet weak var billingDetailsLabel: UILabel!
    @IBOutlet weak var personalDetailsLabel: UILabel!
    @IBOutlet weak var assignedContactReferenceLabel: UILabel!

    @IBOutlet weak var billingEndDateLabel: UILabel!
    @IBOutlet weak var billingEndDateValueLabel: UILabel!
    @IBOutlet weak var billingEndDateInfoLabel: UILabel!
    @IBOutlet weak var billingEmailAddressLabel: UILabel!
    @IBOutlet weak var billingEmailAddressValueLabel: UILabel!
    @IBOutlet weak var alternativeContactNumberLabel: UILabel!
    @IBOutlet weak var alternativeContactNumberValueLabel: UILabel!

    @IBOutlet weak var genderLabel: UILabel!
    @IBOutlet weak var genderValueLabel: UILabel!
    @IBOutlet weak var maritalStatusLabel: UILabel!
    @IBOutlet weak var maritalStatusValueLabel: UILabel!

    @IBOutlet weak var firstNameLabel: UILabel!
    @IBOutlet weak var firstNameValueLabel: UILabel!
    @IBOutlet weak var lastNameLabel: UILabel!
    @IBOutlet weak var lastNameValueLabel: UILabel!
    @IBOutlet weak var contactRelationshipLabel: UILabel!
    @IBOutlet weak var contactRelationshipValueLabel: UILabel!
    @IBOutlet weak var mobileNumberLabel: UILabel!
    @IBOutlet weak var mobileNumberValueLabel: UILabel!

    @IBOutlet weak var mothersMaidenNameView: UIView!
    @IBOutlet weak var maidenFirstNameLabel: UILabel!
    @IBOutlet weak var maidenFirstNameValueLabel: UILabel!
    @IBOutlet weak var maidenMiddleNameLabel: UILabel!
    @IBOutlet weak var maidenMiddleNameValueLabel: UILabel!
    @IBOutlet weak var maidenLastNameLabel: UILabel!
    @IBOutlet weak var maidenLastNameValueLabel: UILabel!

    @IBOutlet weak var confirmButton: MayaButton!

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override var mayaNavigationBarScrollView: UIScrollView? {
        return scrollView
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        self.locationAuthorizerService.setupService(delegate: self)
        setupView()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        setRightButtonItemTitle("3/3")
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        scrollView.setContentOffset(.zero, animated: true)
    }

    override func setupAccessibilityIdentifiers() {
        navigationItem.leftBarButtonItem?.isAccessibilityElement = true
        navigationItem.leftBarButtonItem?.accessibilityIdentifier = "pmios_maya_credit_activation_back_button"
    }

    @IBAction func didTapConfirm(_ sender: Any) {
        delegate?.didTapConfirm(self)
    }
}

// MARK: - ViewModelBindable
extension MayaCreditConfirmDetailsViewController: ViewModelBindable {
    func binding(_ viewModel: NewMayaCreditActivationViewModelProtocol?) {
        guard let viewModel = viewModel else { return }

        self.billingEndDateValueLabel.text = L10n.Maya.Credit.Signup.Billing.End.date(viewModel.billingEndDate.value)
        self.billingEmailAddressValueLabel.text = viewModel.emailField.value
        self.alternativeContactNumberValueLabel.text = viewModel.alternativeMobileNumberField.value?.applyMayaMobileNumberFormat()

        self.genderValueLabel.text = viewModel.genderField.value
        self.maritalStatusValueLabel.text = viewModel.maritalStatusField.value

        self.firstNameValueLabel.text = viewModel.assignContactReferenceViewModel.firstNameField.value
        self.lastNameValueLabel.text = viewModel.assignContactReferenceViewModel.lastNameField.value
        self.contactRelationshipValueLabel.text = viewModel.assignContactReferenceViewModel.contactRelationship.value
        self.mobileNumberValueLabel.text = viewModel.assignContactReferenceViewModel.mobileNumber.value.applyMayaMobileNumberFormat()

        self.maidenFirstNameValueLabel.text = viewModel.mothersMaidenFirstNameField.value
        self.maidenMiddleNameValueLabel.text = viewModel.mothersMaidenMiddleNameField.value
        self.maidenLastNameValueLabel.text = viewModel.mothersMaidenLastNameField.value

        viewModel.getMayaCreditFullEligibility.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] executing in
                guard let self = self else { return }
                if executing {
                    self.showMayaLoader()
                } else {
                    self.hideMayaLoader()
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.getMayaCreditFullEligibility.values
            .observe(on: UIScheduler())
            .observeValues { [weak self] eligibility in
                guard let self = self, let eligibilityTerm = eligibility.term else { return }
                self.delegate?.willShowAllSetScreen(self, term: eligibilityTerm)
            }?.addToDisposeBag(disposeBag)

        viewModel.getMayaCreditFullEligibility.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self = self else { return }
                switch error.type {
                case .mcIneligible:
                    self.delegate?.willShowIneligibleView(self)
                    self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                        AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                        AnalyticsAttributeKey.error.rawValue: error.type
                    ])
                case .sessionTimeout:
                    return
                case .noInternetConnection:
                    break
                default:
                    if let errorViewModel = error.viewModel {
                        self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
                    }
                    self.crashlyticsLogger.logNonFatalError(error, customKeys: [
                        AnalyticsAttributeKey.product.rawValue: AnalyticsConstants.Screen.MEC.product.rawValue,
                        AnalyticsAttributeKey.error.rawValue: error.type
                    ])
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.values
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self else { return }
                if viewModel.isLendingDataExtractionEnabled {
                    viewModel.handleRavenExtract.apply().start()
                } else {
                    if viewModel.shouldInitializeDeviceScoring {
                        delegate?.willStartCreditScoring(self, riskLevel: viewModel.riskLevel)
                    } else {
                        viewModel.getMayaCreditFullEligibility.apply((UUID().uuidString, nil, nil)).start()
                    }
                }
                let deviceScoringAttributes = [
                    AnalyticsAttributeKey.enabled.rawValue: viewModel.shouldInitializeDeviceScoring
                ]
                analyticsService.logMayaEvents(name: Analytics.LendingToggles.mecDeviceScoring.rawValue, attributes: deviceScoringAttributes)
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenGetGeolocation.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] err in
                guard let self else { return }
                switch err {
                case .notDetermined:
                    locationAuthorizerService.requestAuthorization()

                    let attributes = [
                        AnalyticsAttributeKey.sourcePage: AnalyticsConstants.Screen.MECApplication.page.rawValue,
                        AnalyticsAttributeKey.sourceButton: AnalyticsConstants.Screen.MECApplication.Button.confirm.rawValue
                    ]
                    analyticsService.logMayaEvents(action: .requested(Analytics.MayaCreditApplication.geolocation), keyAttributes: attributes)
                case .denied, .restricted:
                    showErrorAlert(viewModel: ErrorAlertViewModel(
                        title: L10n.Maya.Credit.Location.Permission.Needed.title,
                        message: L10n.Maya.Credit.Location.Permission.Needed.message),
                                   useMayaModal: true) {
                        self.showGoToSettingsAlert()

                        let attributes = [
                            AnalyticsAttributeKey.sourcePage: AnalyticsConstants.Screen.MECApplication.Prompt.location.rawValue,
                            AnalyticsAttributeKey.sourceButton: AnalyticsConstants.Screen.MECApplication.Button.deny.rawValue,
                            AnalyticsAttributeKey.errorMessage: L10n.Maya.Credit.Location.Permission.Needed.title
                        ]
                        self.analyticsService.logMayaEvents(action: .error(Analytics.MayaCreditApplication.none), keyAttributes: attributes)
                    }
                case .unknown:
                    showLetsTryThatAgainErrorAlertModal {
                        self.viewModel?.handleRavenGetGeolocation.apply().start()

                        let attributes = [
                            AnalyticsAttributeKey.sourcePage: AnalyticsConstants.Screen.MECApplication.page.rawValue,
                            AnalyticsAttributeKey.sourceButton: AnalyticsConstants.Screen.MECApplication.Button.confirm.rawValue,
                            AnalyticsAttributeKey.errorMessage: L10n.Maya.Credit.Location.Try.Again.title
                        ]
                        self.analyticsService.logMayaEvents(action: .error(Analytics.MayaCreditApplication.none), keyAttributes: attributes)
                    }
                }
            }?.addToDisposeBag(disposeBag)

        // if raven extract succeeded, then start device scoring
        viewModel.handleRavenExtract.completed
            .observe(on: UIScheduler())
            .observeValues { [weak self] in
                guard let self else { return }
                if viewModel.shouldInitializeDeviceScoring {
                    delegate?.willStartCreditScoring(self, riskLevel: viewModel.riskLevel)
                } else {
                    viewModel.getMayaCreditFullEligibility.apply((UUID().uuidString, nil, nil)).start()
                }
                let deviceScoringAttributes = [
                    AnalyticsAttributeKey.enabled.rawValue: viewModel.shouldInitializeDeviceScoring
                ]
                analyticsService.logMayaEvents(.success(Analytics.MayaDataExtraction.extract), attributes: attributes)
                analyticsService.logMayaEvents(name: Analytics.LendingToggles.mecDeviceScoring.rawValue, attributes: deviceScoringAttributes)
            }?.addToDisposeBag(disposeBag)

        viewModel.handleRavenExtract.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] executing in
                guard let self else { return }
                if executing {
                    showMayaLoader()
                } else {
                    hideMayaLoader()
                }
            }?.addToDisposeBag(disposeBag)

        // if raven extract failed, then show error pop up
        viewModel.handleRavenExtract.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self else { return }
                showLetsTryThatAgainErrorAlertModal {
                    self.viewModel?.handleRavenExtract.apply().start()

                    let attributes = [
                        AnalyticsAttributeKey.sourcePage: AnalyticsConstants.Screen.MECApplication.page.rawValue,
                        AnalyticsAttributeKey.sourceButton: AnalyticsConstants.Screen.MECApplication.Button.confirm.rawValue,
                        AnalyticsAttributeKey.errorMessage: L10n.Maya.Credit.Location.Try.Again.title
                    ]
                    self.analyticsService.logMayaEvents(action: .error(Analytics.MayaCreditApplication.none), keyAttributes: attributes)
                }
                analyticsService.logMayaEvents(action: .failure(Analytics.MayaDataExtraction.extract), keyAttributes: attributes)
            }?.addToDisposeBag(disposeBag)
    }
}

private extension MayaCreditConfirmDetailsViewController {
    private func setupView() {
        headerLabel.text = L10n.Maya.Credit.ConfirmDetails.title
        billingDetailsLabel.text = L10n.Maya.Credit.ConfirmDetails.Section.billing
        personalDetailsLabel.text = L10n.Maya.Credit.ConfirmDetails.Section.personal
        assignedContactReferenceLabel.text = L10n.Maya.Credit.ConfirmDetails.Section.contact

        billingEndDateLabel.text = L10n.Maya.Credit.Signup.Contact.Reference.Section.Billing.enddate
        billingEndDateInfoLabel.text = L10n.Maya.Credit.ConfirmDetails.Section.Billing.info
        billingEmailAddressLabel.text = L10n.Maya.Credit.Signup.Contact.Reference.Section.Billing.emailaddress
        alternativeContactNumberLabel.text = L10n.Maya.Credit.Signup.Contact.Reference.Section.Billing.Altmobile.label
        genderLabel.text = CommonStrings.Common.gender
        maritalStatusLabel.text = CommonStrings.Common.maritalstatus
        firstNameLabel.text = CommonStrings.Common.firstname
        lastNameLabel.text = CommonStrings.Common.lastname
        contactRelationshipLabel.text = L10n.Maya.Credit.Assign.Contact.Reference.contactRelationship
        mobileNumberLabel.text = CommonStrings.Common.Mobile.number
        maidenFirstNameLabel.text = L10n.Maya.Credit.Signup.MaidenName.FirstName.label
        maidenMiddleNameLabel.text = L10n.Maya.Credit.Signup.MaidenName.MiddleName.label
        maidenLastNameLabel.text = L10n.Maya.Credit.Signup.MaidenName.LastName.label

        confirmButton.setTitle(CommonStrings.Common.confirm, for: .normal)
    }

    private func showLetsTryThatAgainErrorAlertModal(tryAgainAction: (() -> Void)? = nil) {
        let alertViewModel = MayaAlertViewModel(title: L10n.Maya.Credit.Location.Try.Again.title,
                                                message: L10n.Maya.Credit.Location.Try.Again.message,
                                                image: CommonAsset.Images.Alert.imageLarger3DWarning.image)
        let tryAgainAlertAction = MayaAlertAction(title: CommonStrings.Common.Try.again,
                                                  style: .primary) { [weak self] in
            guard let self else { return }
            dismissMayaAlertModal(viewModel: alertViewModel, viewController: self) {
                tryAgainAction?()
            }
        }

        let gotItAlertAction = MayaAlertAction(title: CommonStrings.Common.Modal.Got.it,
                                               style: .secondary) { [weak self] in
            guard let self else { return }
            self.dismissMayaAlertModal(viewModel: alertViewModel,
                                       viewController: self,
                                       completion: nil)
        }
        alertViewModel.addAction(tryAgainAlertAction)
        alertViewModel.addAction(gotItAlertAction)
        self.showMayaAlertModal(viewModel: alertViewModel)
    }

    func showGoToSettingsAlert() {
        let alertController = UIAlertController(title: L10n.Maya.Credit.Location.Open.Settings.title,
                                                message: L10n.Maya.Credit.Location.Open.Settings.message,
                                                preferredStyle: .alert)
        let cancelAction = UIAlertAction(title: CommonStrings.Common.cancel,
                                         style: .cancel,
                                         handler: nil)
        let openSettings = UIAlertAction(title: L10n.Vouchers.Reminder.Calendar.Settings.spiel,
                                         style: .default,
                                         handler: { [weak self] _ in
            guard let self else { return }
            showExternalURI(URL(string: UIApplication.openSettingsURLString))
            let attributes = [
                AnalyticsAttributeKey.sourcePage: AnalyticsConstants.Screen.MECApplication.Prompt.deviceAccess.rawValue,
                AnalyticsAttributeKey.sourceButton: AnalyticsConstants.Screen.MECApplication.Button.goToSettings.rawValue
            ]
            self.analyticsService.logMayaEvents(action: .opened(Analytics.MayaCreditApplication.deviceSettings), keyAttributes: attributes)
        })
        alertController.addAction(openSettings)
        alertController.addAction(cancelAction)
        present(alertController, animated: true, completion: { [weak self] in
            guard let self else { return }
            let attributes = [
                AnalyticsAttributeKey.sourcePage: AnalyticsConstants.Screen.MECApplication.Prompt.location.rawValue,
                AnalyticsAttributeKey.sourceButton: AnalyticsConstants.Screen.MECApplication.Button.gotIt.rawValue
            ]
            self.analyticsService.logMayaEvents(action: .requested(Analytics.MayaCreditApplication.access), keyAttributes: attributes)
        })
    }

    @objc func tapTermsAndConditions(_ sender: UITapGestureRecognizer) {
        delegate?.didTapTermsAndConditions(self)
    }

    @objc func tapDisclosureStatement(_ sender: UITapGestureRecognizer) {
        delegate?.didTapDisclosureStatement(self)
    }

    @objc func tapTruthAndTransparencyInLending(_ sender: UITapGestureRecognizer) {
        delegate?.didTapTruthAndTransparencyInLending(self)
    }

    @objc func tapDataPrivacyPolicy(_ sender: UITapGestureRecognizer) {
        delegate?.didTapDataPrivacyPolicy(self)
    }

    @objc func tapAutomatedPayments(_ sender: UITapGestureRecognizer) {
        delegate?.didTapAutomatedPayments(self)
    }
}

extension MayaCreditConfirmDetailsViewController: LocationAuthorizerServiceDelegate {
    func didDenyLocationAuthorization(didShowPrompt: Bool) {
        if didShowPrompt {
            viewModel?.handleRavenGetGeolocation.apply().start()
            let attributes: [AnalyticsAttributeKey: Any] = [
                AnalyticsAttributeKey.sourcePage: AnalyticsConstants.Screen.MECApplication.Prompt.location.rawValue,
                AnalyticsAttributeKey.sourceButton: AnalyticsConstants.Screen.MECApplication.Button.deny.rawValue
            ]
            analyticsService.logMayaEvents(action: .denied(Analytics.MayaCreditApplication.geolocation), keyAttributes: attributes)
        }
    }

    func didAcceptLocationAuthorization(didShowPrompt: Bool) {
        if didShowPrompt {
            viewModel?.handleRavenGetGeolocation.apply().start()
            let attributes: [AnalyticsAttributeKey: Any] = [
                AnalyticsAttributeKey.sourcePage: AnalyticsConstants.Screen.MECApplication.Prompt.location.rawValue,
                AnalyticsAttributeKey.sourceButton: AnalyticsConstants.Screen.MECApplication.Button.allow.rawValue
            ]
            analyticsService.logMayaEvents(action: .allowed(Analytics.MayaCreditApplication.geolocation), keyAttributes: attributes)
        }
    }

    func didNotDetermineLocationAuthorization() {
        // Do nothing
    }
}
