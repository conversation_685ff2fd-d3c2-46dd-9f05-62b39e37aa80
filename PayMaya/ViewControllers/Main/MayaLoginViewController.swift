//
//  MayaLoginViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 3/3/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import ConfigurationService
import Foundation
import Injector
import ReactiveCocoa
import ReactiveSwift

protocol MayaLoginViewControllerDelegate: AnyObject {
    func didAuthenticate(_ viewController: MayaLoginViewController)
    func didReceiveOTPId(_ viewController: <PERSON>LoginViewController, otpId: String, mobileNumber: String?)
    func didViewTermsAndCondition(_ viewController: <PERSON>LoginViewController)
    func didTapForgotPassword(_ viewController: MayaLoginViewController)
    func didStartMFAOTPChallenge(_ viewController: MayaLoginViewController, challengeId: String, mobileNumber: String, transactionType: String)
    func didReceiveMFAFaceChallenge(_ viewController: <PERSON><PERSON><PERSON><PERSON>ViewController, challengeId: String, lifestyleId: String, transactionType: String)
    func didReceiveMFANoFaceDataError(_ viewController: MayaLoginViewController, errorViewModel: ErrorAlertViewModel)
}

class MayaLoginViewController: FormsViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject var configurationService: ConfigurationService

    @IBOutlet weak var numberView: MayaValidationFieldView! {
        didSet {
            numberView.setPhoneNumberFieldView()
        }
    }

    @IBOutlet weak var passwordView: MayaValidationFieldView! {
        didSet {
            passwordView.setPasswordFieldView()
            passwordView.passwordToggleActionHandler = { [weak self] in
                guard let self = self else { return }
                // Tap show/hide password
                self.analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Login.main.tapped(),
                                                    keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.loginPage,
                                                                    AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.loginPage,
                                                                    AnalyticsAttributeKey.screenType: AnalyticsConstants.ScreenType.mainLogin,
                                                                    AnalyticsAttributeKey.button: AnalyticsConstants.ButtonText.hidePassword])
            }
        }
    }

    @IBOutlet weak var continueButton: MayaButton!

    @Inject private var profileTrackingManager: ProfileTrackingManager

    weak var delegate: MayaLoginViewControllerDelegate?

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override var mayaNavigationBarScrollView: UIScrollView? {
        return scrollView
    }

    override var shouldMonitorScreenPerformance: Bool { true }

    private var numberTextField: TextField {
        return numberView.textField
    }

    private var passwordTextField: TextField {
        return passwordView.textField
    }

    override func viewWillAppear(_ animated: Bool) {
        self.setModule(Analytics.Login())
        super.viewWillAppear(animated)
        passwordTextField.text = String.empty
        passwordView.disableValidationSignal()
    }

    override func didTapBack(_ sender: Any) {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Login.main.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.loginPage,
                                                       AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.mayaStartPage,
                                                       AnalyticsAttributeKey.screenType: AnalyticsConstants.ScreenType.mainLogin,
                                                       AnalyticsAttributeKey.button: CommonStrings.Common.back])
        super.didTapBack(sender)
    }

    @IBAction func didTapForgotPassword(_ sender: UIButton) {
        let buttonText = sender.title(for: .normal) ?? ""
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Login.main.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.loginPage,
                                                       AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.forgotPassword,
                                                       AnalyticsAttributeKey.screenType: AnalyticsConstants.ScreenType.mainLogin,
                                                       AnalyticsAttributeKey.button: buttonText])
        delegate?.didTapForgotPassword(self)
    }

    override func setupAccessibilityIdentifiers() {
        let placeholder = "login"
        numberView.setAccessibilityIdentifiers(placeholder: placeholder)
        passwordView.setAccessibilityIdentifiers(placeholder: placeholder)
    }
}

// MARK: - ViewControllerBindable Methods
extension MayaLoginViewController: ViewControllerBindable {
    func binding() {
        formFields = [numberView, passwordView]
        setKeyboardReturnTypeOfTextFields()
        passwordView.textField.returnKeyType = .done
        numberTextField.becomeFirstResponder()
        lastFormFieldCallback = { [weak self] in
            guard let self = self else { return }
            self.view.endEditing(true)
            self.viewModel?.loginAction.apply().start()
        }
    }
}

// MARK: - ViewModel Bindable
extension MayaLoginViewController: ViewModelBindable {
    func binding(_ viewModel: MayaLoginViewModelProtocol?) {
        guard let viewModel = viewModel else { return }

        let prefixText = numberView.prefixLabel.text ?? ""
        if let user = viewModel.getUser(), let msisdn = user.msisdn {
            numberTextField.text = msisdn.replacingOccurrences(of: prefixText, with: "")
        }

        if let previouslyLoggedInMobileNumber = viewModel.getPreviousMobileNumber() {
            numberTextField.text = previouslyLoggedInMobileNumber.replacingOccurrences(of: prefixText, with: "")
        }

        viewModel.usernameField.mutableProperty.value = String.unwrappedValue(numberTextField.text)
        viewModel.usernameField.mutableProperty <~ numberTextField.reactive.continuousTextValues.map({ mobileNumber -> String in
            guard mobileNumber != String.empty
                else { return "" }
            return "\(prefixText)\(mobileNumber)"
        })

        viewModel.passwordField.mutableProperty <~ passwordTextField.reactive.continuousTextValues

        numberView.bindFieldProperty(viewModel.usernameField)
        passwordView.bindFieldProperty(viewModel.passwordField)

        continueButton.reactive.isEnabled <~ viewModel.validatingProperty

        viewWillAppearSignal.observe(on: UIScheduler()).observeValues { _ in
            viewModel.passwordField.mutableProperty.value = String.empty
        }?.addToDisposeBag(disposeBag)

        guard let loginAction = viewModel.loginAction else { return }

        viewModel.validateAction.completed
            .observe(on: UIScheduler())
            .observeValues { _ in
                loginAction.apply().start()
            }?.addToDisposeBag(disposeBag)

        loginAction.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            if executing {
                self.showMayaLoader()
            } else {
                self.hideMayaLoader()
            }
        }?.addToDisposeBag(disposeBag)

        loginAction.completed.observe(on: UIScheduler()).observeValues { [weak self] in
            guard let self = self, viewModel.mfaChallengeDetailsProperty.value == nil else { return }
            self.profileTrackingManager.setUserProfile(viewModel.user.value, source: .login)
            self.analyticsService.logMayaEvents(action: .success(Analytics.Login()))
            if viewModel.canUseTouchId() {
                self.showLocalAuthenticationPermissionAlert { self.processDataPrivacy() }
            } else {
                self.processDataPrivacy()
            }
        }?.addToDisposeBag(disposeBag)

        loginAction.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self else { return }
            if error.type == .loginMaxAttempts {
                if let errorViewModel = error.viewModel {
                    self.analyticsService.logMayaEvents(action: .failure(Analytics.Login()), keyAttributes: [AnalyticsAttributeKey.reason: errorViewModel.message])
                    if self.configurationService.forgotPasswordV2Enabled.value {
                        self.showResetPasswordAlert(errorViewModel)
                        return
                    }
                    self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
                }
            } else if case .redirect(let otpID) = error.type {
                if viewModel.canUseTouchId() {
                    self.showLocalAuthenticationPermissionAlert { self.showOTPScreen(otpId: otpID) }
                } else {
                    self.showOTPScreen(otpId: otpID)
                }
                return
            } else if case .mfaChallengeFaceRequired(let challengeID, let transactionType, let lifestyleID) = error.type {
                self.delegate?.didReceiveMFAFaceChallenge(self, challengeId: challengeID, lifestyleId: lifestyleID, transactionType: transactionType)
            } else if case .mfaChallengeNoFaceData = error.type,
                      let errorViewModel = error.viewModel {
                self.delegate?.didReceiveMFANoFaceDataError(self, errorViewModel: errorViewModel)
            } else if let errorViewModel = error.viewModel {
                let message: String
                if errorViewModel.message == CommonStrings.Maya.Error.Spiel.default {
                    // mapMayaError returned default spiel, use login and session timout's special handling
                    let viewModel = self.generateSpecialErrorViewModel()
                    // TODO: Temporarily removed link
//                    message = viewModel.alertMessage!.message
                    message = viewModel.message!
                    self.showMayaAlertModal(viewModel: viewModel)
                } else {
                    message = errorViewModel.message
                    self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
                }

                self.analyticsService.logMayaEvents(action: .failure(Analytics.Login()), keyAttributes: [AnalyticsAttributeKey.reason: message], inHouseKeyAttributes: [AnalyticsAttributeKey.errorUID: viewModel.usernameField.mutableProperty.value])
            }
        }?.addToDisposeBag(disposeBag)

        continueButton.reactive.pressed = CocoaAction(viewModel.validateAction) { [weak self] _ in
            guard let self = self else { return }
            self.view.endEditing(true)
            self.actionTriggered?()
            self.analyticsService.logMayaEvents(action: .tap(Analytics.Login.login))
        }

        viewModel.mfaChallengeDetailsProperty.signal.observe(on: UIScheduler())
            .skipNil()
            .observeValues { [weak self] mfaDetails in
                guard let self = self else { return }
                if viewModel.canUseTouchId() {
                    self.showLocalAuthenticationPermissionAlert {
                        self.verifyOTPChallenge(challengeId: mfaDetails.challengeId, transactionType: mfaDetails.transactionType)
                    }
                } else {
                    self.verifyOTPChallenge(challengeId: mfaDetails.challengeId, transactionType: mfaDetails.transactionType)
                }
            }?.addToDisposeBag(disposeBag)
    }
}

// MARK: Private methods
private extension MayaLoginViewController {
    func showLocalAuthenticationPermissionAlert(completion: @escaping () -> Void) {
        let alertTitle = viewModel?.biometricType.description
        let alertMessage = L10n.Credential.Authentication.confirmation(viewModel?.biometricType.description ?? "")
        let authenticationAlert = UIAlertController(title: alertTitle, message: alertMessage, preferredStyle: .alert)
        let cancelAction = UIAlertAction(title: CommonStrings.Common.no, style: .cancel, handler: { [weak self] _ in
            guard let self = self else { return }
            self.analyticsService.logMayaEvents(action: .tap(Analytics.Login.rejectConfirmCredential))
            self.viewModel?.updateLocalAuthenticationPermission(didAllow: false)
            completion()
        })
        let authenticateAction = UIAlertAction(title: CommonStrings.Common.yes, style: .default, handler: { [weak self] _ in
            guard let self = self else { return }
            self.analyticsService.logMayaEvents(action: .tap(Analytics.Login.acceptConfirmCredential))
            self.viewModel?.updateLocalAuthenticationPermission(didAllow: true)
            completion()
        })
        authenticationAlert.addAction(cancelAction)
        authenticationAlert.addAction(authenticateAction)
        authenticationAlert.preferredAction = authenticateAction
        dispatchToMain { [unowned self] in
            self.present(authenticationAlert, animated: true, completion: nil)
        }
    }

    func displayPrivacyPolicyAlert() {
        let title = L10n.Privacy.HasUpdate.title + " \(self.viewModel?.getPrivacyPolicyVersion() ?? "")"
        let message = L10n.Privacy.hasUpdate
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: L10n.Privacy.viewterms, style: .default, handler: { [weak self] _ in
            guard let self = self else { return }
            if self.presentedViewController == alert {
                self.dismiss(animated: true, completion: nil)
            }
            self.delegate?.didViewTermsAndCondition(self)
        }))
        present(alert, animated: true, completion: nil)
    }

    func showResetPasswordAlert(_ errorViewModel: ErrorAlertViewModel) {
        let alertViewModel = MayaAlertViewModel(title: errorViewModel.title, message: errorViewModel.message, image: errorViewModel.image)

        if let actionTitle = errorViewModel.primaryActionTitle {
            let resetPasswordAction = MayaAlertAction(title: actionTitle, style: .primary) { [weak self] in
                guard let self = self else { return }
                self.dismissMayaAlertModal(viewModel: alertViewModel, viewController: self)
                self.delegate?.didTapForgotPassword(self)
            }
            alertViewModel.addAction(resetPasswordAction)
        }
        let closeAction = MayaAlertAction(title: CommonStrings.Common.close, style: .secondary) { [weak self] in
            guard let self = self else { return }
            self.dismissMayaAlertModal(viewModel: alertViewModel, viewController: self)
        }
        alertViewModel.addAction(closeAction)
        showMayaAlertModal(viewModel: alertViewModel)
    }

    func processDataPrivacy() {
        guard
            let policyStatus = viewModel?.user.value?.privacyPolicy,
            policyStatus.status == PrivacyPolicyStatus.notAccepted.rawValue
            else {
                delegate?.didAuthenticate(self)
                return
        }
        view.endEditing(true)
        displayPrivacyPolicyAlert()
    }

    func showOTPScreen(otpId: String) {
        guard let mobile = numberTextField.text?.getMobileNumberSearchString() else { return }
        let mobileNumber = mobile.starts(with: "0") ? "\(mobile)" : "0\(mobile)"
        delegate?.didReceiveOTPId(self, otpId: otpId, mobileNumber: mobileNumber)
    }

    func verifyOTPChallenge(challengeId: String, transactionType: String) {
        guard let mobile = numberTextField.text?.getMobileNumberSearchString() else { return }
        let mobileNumber = mobile.starts(with: "0") ? "\(mobile)" : "0\(mobile)"
        delegate?.didStartMFAOTPChallenge(self, challengeId: challengeId, mobileNumber: mobileNumber, transactionType: transactionType)
    }

    func generateSpecialErrorViewModel() -> MayaAlertViewModel {
        let title = CommonStrings.Login.Error.title
        let image = CommonAsset.Images.Alert.image3DMaintenance.image
        let closeButtonTitle = CommonStrings.Common.close
        let message = CommonStrings.Login.Error.Default.Message.temporary
        // TODO: Temporarily removed link, different message. Uncomment this and remove last return statement to bring back
//        let message = CommonStrings.Login.Error.Default.message
//        let link = CommonStrings.Login.Error.Default.Message.link
//        let url = Constants.WebView.statusURL()!
//        let action: AnalyticsAction = .tap(Analytics.Error.link)
//        let alertMessage = MayaAlertMessage(message: message, link: link, url: url, analytics: action, module: "login")
//
//        return MayaAlertViewModel(title: title, image: image, closeButtonTitle: closeButtonTitle, alertMessage: alertMessage)

        return MayaAlertViewModel(title: title, message: message, image: image, closeButtonTitle: closeButtonTitle)
    }
}
