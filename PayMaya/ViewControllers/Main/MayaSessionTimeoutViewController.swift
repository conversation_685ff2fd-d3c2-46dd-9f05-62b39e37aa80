//
//  MayaSessionTimeoutViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 3/10/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import APIProvider
import AssetProvider
import ConfigurationService
import Error
import Injector
import LocalAuthentication
import ReactiveCocoa
import ReactiveSwift
import UIKit

protocol MayaSessionTimeoutViewControllerDelegate: AnyObject {
    func didAuthenticate(_ viewController: MayaSessionTimeoutViewController)
    func didReceiveOTPId(_ viewController: MayaSessionTimeoutViewController, otpId: String, mobileNumber: String?)
    func didViewTermsAndCondition(_ viewController: MayaSessionTimeoutViewController)
    func didForgotPassword(_ viewController: MayaSessionTimeoutViewController)
    func didSwitchAccount(_ viewController: MayaSessionTimeoutViewController)
    func didRequestLocationPermission(_ viewController: MayaSessionTimeoutViewController)
    func didStartMFAOTPChallenge(_ viewController: MayaSessionTimeoutViewController, challengeId: String, mobileNumber: String?, transactionType: String)
    func didReceiveMFAFaceChallenge(_ viewController: MayaSessionTimeoutViewController, challengeId: String, lifestyleId: String, transactionType: String)
    func didReceiveMFANoFaceDataError(_ viewController: MayaSessionTimeoutViewController, errorViewModel: ErrorAlertViewModel)
}

class MayaSessionTimeoutViewController: FormsViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject var configurationService: ConfigurationService
    @Inject var shieldDataCollector: ShieldDataCollectorProtocol
    @Inject private var profileTrackingManager: ProfileTrackingManager

    @IBOutlet weak var passwordView: MayaValidationFieldView! {
        didSet {
            passwordView.setPasswordFieldView()
            passwordView.passwordToggleActionHandler = { [weak self] in
                guard let self = self else { return }
                // Tap show/hide password
                self.analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Login.main.tapped(),
                                                    keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.loginPage,
                                                                    AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.loginPage,
                                                                    AnalyticsAttributeKey.screenType: AnalyticsConstants.ScreenType.sessionTimeoutLogin,
                                                                    AnalyticsAttributeKey.button: AnalyticsConstants.ButtonText.hidePassword])
            }
        }
    }
    @IBOutlet weak var numberLabel: UILabel!
    @IBOutlet weak var nameLabel: UILabel!
    @IBOutlet weak var loginButton: MayaButton!
    @IBOutlet weak var biometryButton: MayaButton!
    @IBOutlet weak var switchAccountButton: UIButton!
    @IBOutlet weak var logoTopConstraint: NSLayoutConstraint!

    weak var delegate: MayaSessionTimeoutViewControllerDelegate?
    private var touchIdAuthenticationAction: Action<Void, TouchIdResult, Error>?
    private var (passwordSignal, passwordObserver) = Signal<String?, Never>.pipe()
    private var errorViewModel: ErrorAlertViewModel?

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override var shouldMonitorScreenPerformance: Bool { true }

    override var mayaNavigationBarScrollView: UIScrollView? {
        return scrollView
    }

    override func viewDidLoad() {
        super.viewDidLoad()

        viewDidAppearSignal.take(until: viewWillDisappearSignal).observe(on: UIScheduler()).observeValues { [weak self] _ in
            guard let self = self else { return }
            self.processBiometricAuthentication()
        }?.addToDisposeBag(disposeBag)

        viewModel?.clearSessionToken()

        shieldDataCollector.sendDeviceFingerprint(screen: Constants.Shield.login.rawValue)
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        passwordView.textField.clearValues()

        let willEnterForegroundSignal = appWillEnterForegroundSignal.take(until: viewWillDisappearSignal)
        willEnterForegroundSignal.observe(on: UIScheduler()).observeValues { [weak self] _ in
            guard let self = self else { return }
            self.processBiometricAuthentication()
        }?.addToDisposeBag(disposeBag)

        viewModel?.observeLocationAuthorization(delegate: self)
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.SessionTimeout())
        super.viewWillAppear(animated)
    }

    deinit {
        viewModel?.unobserveLocationAuthorization()
        viewModel?.destroyLocationAuthorizerService()
    }

    @IBAction func didTapForgotPassword(sender: UIButton) {
        let buttonText = sender.title(for: .normal) ?? ""
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Login.main.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.loginPage,
                                                       AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.forgotPassword,
                                                       AnalyticsAttributeKey.screenType: AnalyticsConstants.ScreenType.sessionTimeoutLogin,
                                                       AnalyticsAttributeKey.button: buttonText])
        delegate?.didForgotPassword(self)
    }

    @IBAction func didTapUseBiometricLogin(_ sender: UIButton) {
        if viewModel?.biometricType == .touchID {
            analyticsService.logMayaEvents(action: .tap(Analytics.SessionTimeout.touchID))
        } else {
            analyticsService.logMayaEvents(action: .tap(Analytics.SessionTimeout.faceID))
        }

        self.processBiometricAuthentication()
    }

    override func setupAccessibilityIdentifiers() {
        passwordView.setAccessibilityIdentifiers(placeholder: "session_timeout")
    }
}

// MARK: - UI Bindable
extension MayaSessionTimeoutViewController: ViewControllerBindable {
    func binding() {
        // Scale top constraint based on phone height
        logoTopConstraint.constant = UIScreen.main.nativeBounds.height * 40 / 1136

        nameLabel.text = ""
        numberLabel.text = ""
        formFields = [passwordView]
        setKeyboardReturnTypeOfTextFields()
        passwordView.textField.returnKeyType = .done
        lastFormFieldCallback = { [weak self] in
            guard let self = self else { return }
            self.view.endEditing(true)
            self.viewModel?.loginAction.apply().start()
        }
    }
}

extension MayaSessionTimeoutViewController: ViewModelBindable {
    func binding(_ viewModel: MayaLoginViewModelProtocol?) {
        guard let viewModel = viewModel,
            let user = viewModel.getUser(),
            let signoutAction = viewModel.signoutAction
            else { return }

        let didLogoutSignal = Signal.merge([signoutAction.completed.map { _ in Error.none }, signoutAction.errors])

        didLogoutSignal.observe(on: UIScheduler()).observeValues { [weak self] _ in
            guard let self = self else { return }
            if let errorViewModel = self.errorViewModel {
                self.routeActionDelegate?.didRequestSignout(self, viewModel: errorViewModel, signoutRoute: .landing, performAction: false, showAlert: true)
            } else {
                self.delegate?.didSwitchAccount(self)
            }
        }?.addToDisposeBag(disposeBag)

        signoutAction.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            if executing {
                self.showMayaLoader()
            } else {
                self.hideMayaLoader()
            }
        }?.addToDisposeBag(disposeBag)

        passwordView.bindFieldProperty(viewModel.passwordField)

        touchIdAuthenticationAction = viewModel.touchIdAuthenticationAction
        touchIdAuthenticationAction?.events.observe(on: UIScheduler()).observeValues { [weak self] touchResult in
            guard
                let self = self,
                let result = touchResult.value
                else {
                    return
            }
            if result.success {
                self.loginViaBiometry()
            } else if let error = result.error {
                self.showBiometricLoginError(error)
            }
        }?.addToDisposeBag(disposeBag)

        switchAccountButton.reactive.pressed = CocoaAction(signoutAction) { [weak self] _ in
            guard let self = self else { return }
            self.errorViewModel = nil
        }

        switchAccountButton.reactive.controlEvents(.touchUpInside).observe(on: UIScheduler()).observeValues { [weak self] _ in
            guard let self = self else { return }
            self.analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Login.main.tapped(),
                                                keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.loginPage,
                                                                AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.loginPage,
                                                                AnalyticsAttributeKey.screenType: AnalyticsConstants.ScreenType.sessionTimeoutLogin,
                                                                AnalyticsAttributeKey.button: AnalyticsConstants.ButtonText.switchAccount])
        }?.addToDisposeBag(disposeBag)

        viewModel.validateAction.completed
            .observe(on: UIScheduler())
            .observeValues {
                viewModel.loginAction.apply().start()
            }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.isExecuting.signal.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            if executing {
                self.showMayaLoader()
            } else {
                self.hideMayaLoader()
            }
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.completed.observe(on: UIScheduler()).observeValues { [weak self] in
            guard let self = self, viewModel.mfaChallengeDetailsProperty.value == nil else { return }
            self.profileTrackingManager.setUserProfile(viewModel.user.value, source: .sessionTimeout)
            self.analyticsService.logMayaEvents(action: .success(Analytics.SessionTimeout.login))
            guard
                let policyStatus = self.viewModel?.getUser()?.privacyPolicy,
                policyStatus.status == PrivacyPolicyStatus.notAccepted.rawValue
                else {
                    self.delegate?.didAuthenticate(self)
                    return
            }
            self.showDataPrivacyAlert()
        }?.addToDisposeBag(disposeBag)

        viewModel.loginAction.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self else { return }
            if case .redirect(let otpID) = error.type {
                self.delegate?.didReceiveOTPId(self, otpId: otpID, mobileNumber: user.msisdn)
                return
            }

            if error.type == .loginMaxAttempts, let errorViewModel = error.viewModel {
                self.errorViewModel = errorViewModel
                viewModel.signoutAction.apply().start()
            } else if case .mfaChallengeFaceRequired(let challengeID, let transactionType, let lifestyleID) = error.type {
                self.delegate?.didReceiveMFAFaceChallenge(self, challengeId: challengeID, lifestyleId: lifestyleID, transactionType: transactionType)
            } else if case .mfaChallengeNoFaceData = error.type,
                      let errorViewModel = error.viewModel {
                self.delegate?.didReceiveMFANoFaceDataError(self, errorViewModel: errorViewModel)
            } else if let errorViewModel = error.viewModel {
                let message: String
                if errorViewModel.message == CommonStrings.Maya.Error.Spiel.default {
                    // mapMayaError returned default spiel, use login and session timout's special handling
                    let viewModel = self.generateSpecialErrorViewModel()
                    // TODO: Temporarily removed link
//                    message = viewModel.alertMessage!.message
                    message = viewModel.message!
                    self.showMayaAlertModal(viewModel: viewModel)
                } else {
                    message = errorViewModel.message
                    self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
                }

                if error.type != .validation {
                    self.analyticsService.logMayaEvents(action: .failure(Analytics.SessionTimeout.login), keyAttributes: [AnalyticsAttributeKey.reason: message])
                }

                if let passwordValue = self.passwordView.textField.text, passwordValue.isEmpty {
                    self.passwordObserver.send(value: String())
                }
            }
        }?.addToDisposeBag(disposeBag)

        loginButton.reactive.pressed = CocoaAction(viewModel.validateAction) { [weak self] _ in
            guard let self = self else { return }
            self.view.endEditing(true)
            self.actionTriggered?()
            self.errorViewModel = nil
        }

        viewModel.mfaChallengeDetailsProperty.signal.observe(on: UIScheduler())
            .skipNil()
            .observeValues { [weak self] mfaDetails in
                guard let self = self else { return }
                self.delegate?.didStartMFAOTPChallenge(self, challengeId: mfaDetails.challengeId, mobileNumber: user.msisdn, transactionType: mfaDetails.transactionType)
            }?.addToDisposeBag(disposeBag)

        viewModel.passwordField.mutableProperty <~ Signal.merge([self.passwordSignal.skipNil(), passwordView.textField.reactive.continuousTextValues])
        viewModel.usernameField.mutableProperty <~ Property<String?>(object: self.numberLabel, keyPath: "text").signal.skipNil()

        loginButton.reactive.isEnabled <~ viewModel.validatingProperty

        nameLabel.text = viewModel.getUserFirstName()
        numberLabel.text = String.unwrappedValue(user.msisdn?.applyMayaMobileNumberFormat())
        setupBiometryDisplay()
    }
}

private extension MayaSessionTimeoutViewController {
    func loginViaBiometry() {
        passwordObserver.send(value: viewModel?.getSavedCredential())
        viewModel?.loginAction.apply().start()
    }

    func showBiometricLoginError(_ error: LAError) {
        guard let viewModel = viewModel else { return }
        biometryButton.isHidden = !viewModel.showBiometryButton
    }

    func setupBiometryDisplay() {
        guard let viewModel = viewModel, viewModel.showBiometryButton else {
            biometryButton.isHidden = true
            return
        }

        biometryButton.isHidden = false
        if viewModel.biometricType == .touchID {
            biometryButton.leftHandImage = Asset.MayaImages.Prelogin.iconTouchId.image
            biometryButton.setTitle(L10n.Sessiontimeout.Login.TouchId.spiel, for: .normal)
        } else if viewModel.biometricType == .faceID {
            biometryButton.leftHandImage = Asset.MayaImages.Prelogin.iconFaceId.image
            biometryButton.setTitle(L10n.Sessiontimeout.Login.FaceId.spiel, for: .normal)
        } else {
            biometryButton.leftHandImage = Asset.MayaImages.Prelogin.iconTouchId.image
            biometryButton.setTitle(L10n.Sessiontimeout.Login.Passcode.spiel, for: .normal)
        }
    }

    func showDataPrivacyAlert() {
        view.endEditing(true)
        let title = L10n.Privacy.HasUpdate.title + " \(viewModel?.getPrivacyPolicyVersion() ?? String.empty)"
        let message = L10n.Privacy.hasUpdate
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "View Terms and Conditions", style: .default, handler: { [weak self] _ in
            guard let self = self else { return }
            if self.presentedViewController == alert {
                self.dismiss(animated: true, completion: nil)
            }
            self.delegate?.didViewTermsAndCondition(self)
        }))
        present(alert, animated: true, completion: nil)
    }

    func processBiometricAuthentication() {
        guard let viewModel = viewModel,
              !viewModel.needsLocationAuthorization,
              viewModel.isTouchIdAllowedForAuthentication()
        else { return }

        self.touchIdAuthenticationAction?.apply().start()
    }

    func generateSpecialErrorViewModel() -> MayaAlertViewModel {
        let title = CommonStrings.Login.Error.title
        let image = CommonAsset.Images.Alert.image3DMaintenance.image
        let closeButtonTitle = CommonStrings.Common.close
        let message = CommonStrings.Login.Error.Default.Message.temporary
        // TODO: Temporarily removed link, different message. Uncomment this and remove last return statement to bring back
//        let message = CommonStrings.Login.Error.Default.message
//        let link = CommonStrings.Login.Error.Default.Message.link
//        let url = Constants.WebView.statusURL()!
//        let action: AnalyticsAction = .tap(Analytics.Error.link)
//        let alertMessage = MayaAlertMessage(message: message, link: link, url: url, analytics: action, module: "session_timeout")
//
//        return MayaAlertViewModel(title: title, image: image, closeButtonTitle: closeButtonTitle, alertMessage: alertMessage)

        return MayaAlertViewModel(title: title, message: message, image: image, closeButtonTitle: closeButtonTitle)
    }
}

// MARK: - LocationAuthorizerServiceDelegate methods
extension MayaSessionTimeoutViewController: LocationAuthorizerServiceDelegate {
    func didNotDetermineLocationAuthorization() {
        delegate?.didRequestLocationPermission(self)
    }

    func didDenyLocationAuthorization(didShowPrompt: Bool) {
        delegate?.didRequestLocationPermission(self)
    }

    func didAcceptLocationAuthorization(didShowPrompt: Bool) {}
}
