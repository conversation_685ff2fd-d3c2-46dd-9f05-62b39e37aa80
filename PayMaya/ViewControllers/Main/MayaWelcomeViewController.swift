//
//  MayaWelcomeViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 3/3/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import ConfigurationService
import Foundation
import Injector

protocol MayaWelcomeViewControllerDelegate: AnyObject {
    func didTapRegistration(_ viewController: MayaWelcomeViewController)
    func didTapLogin(_ viewController: MayaWelcomeViewController)
    func didRequestLocationPermission(_ viewController: <PERSON><PERSON>elcomeViewController)
    func didRequireMayaIntroductionScreens(_ viewController: Maya<PERSON>elcomeViewController)
    func didTapRegisterToShowTinbo(_ viewController: MayaWelcomeViewController)
}

class MayaWelcomeViewController: ViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject var configurationService: ConfigurationService
    @Inject var shieldDataCollector: ShieldDataCollectorProtocol

    @IBOutlet weak var footerLabel: UILabel!
    @IBOutlet private weak var deviceIDLabel: UILabel!

    weak var delegate: MayaWelcomeViewControllerDelegate?
    deinit {
        viewModel?.unobserveLocationAuthorization()
        viewModel?.destroyLocationAuthorizerService()
        viewModel?.destroyLocationCheckerService()
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        #if !PROD_TARGET && !SANDBOX_TARGET
        setupDeviceIDLabel()
        #endif
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.Welcome())
        super.viewWillAppear(animated)

        if let viewModel, viewModel.shouldShowMayaIntroductionScreens,
           !viewModel.isShortenedRegistrationEnabled {
            delegate?.didRequireMayaIntroductionScreens(self)
        } else {
            viewModel?.observeLocationAuthorization(delegate: self)
            viewModel?.observeLocationChecking()
        }
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.mayaStartPage.viewed())
    }

    @IBAction func didTapLogin(_ sender: UIButton) {
        if let viewModel, viewModel.shouldShowNativeLocationPermissionDialog,
           viewModel.isShortenedRegistrationEnabled {
            viewModel.locationAuthorizerService.requestAuthorization()
        } else {
            delegate?.didTapLogin(self)
        }

        let buttonText = sender.title(for: .normal) ?? ""
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.mayaStartPage.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.mayaStartPage,
                                                       AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.loginPage,
                                                       AnalyticsAttributeKey.button: buttonText])
    }

    @IBAction func didTapRegister(_ sender: UIButton) {
        guard let viewModel = viewModel else { return }

        if viewModel.shouldShowTinboPrompt {
            delegate?.didTapRegisterToShowTinbo(self)
        } else {
            delegate?.didTapRegistration(self)
        }
        let locationCheckStatus = viewModel.locationCheckStatus.analyticsValue
        let buttonText = sender.title(for: .normal) ?? ""
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.mayaStartPage.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.mayaStartPage,
                                                       AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.startAccount,
                                                       AnalyticsAttributeKey.button: buttonText,
                                                       AnalyticsAttributeKey.location: locationCheckStatus])
    }

    @objc private func didDoubleTapFooter() {
        deviceIDLabel.isShown = true
    }

    @objc private func didTapDeviceID() {
        guard let deviceID = viewModel?.deviceID else { return }

        let deviceIDString = "Device ID: \(deviceID)"
        UIPasteboard.general.string = deviceIDString

        showMayaSnackBar(message: "Device ID copied!")
    }
}

// MARK: ViewModelBindable protocol
extension MayaWelcomeViewController: ViewModelBindable {
    func binding(_ viewModel: MayaLandingViewModelProtocol?) {
        #if !PROD_TARGET && !SANDBOX_TARGET
        setupDeviceID()
        #endif
    }

    private func setupDeviceID() {
        guard let deviceID = viewModel?.deviceID else { return }
        deviceIDLabel.text = "Device ID: \(deviceID)"
    }

    private func setupDeviceIDLabel() {
        let footerTapGesture = UITapGestureRecognizer(target: self, action: #selector(didDoubleTapFooter))
        footerTapGesture.numberOfTapsRequired = 2
        footerLabel.addGestureRecognizer(footerTapGesture)

        let deviceIDTapGesture = UITapGestureRecognizer(target: self, action: #selector(didTapDeviceID))
        deviceIDLabel.addGestureRecognizer(deviceIDTapGesture)
    }
}

// MARK: - LocationAuthorizerServiceDelegate methods
extension MayaWelcomeViewController: LocationAuthorizerServiceDelegate {
    func didNotDetermineLocationAuthorization() {
        if let viewModel, viewModel.isShortenedRegistrationEnabled {
            // Do nothing
        } else {
            delegate?.didRequestLocationPermission(self)
        }
    }

    func didDenyLocationAuthorization(didShowPrompt: Bool) {
        if let viewModel, viewModel.isShortenedRegistrationEnabled, didShowPrompt {
            delegate?.didTapLogin(self)
        } else {
            delegate?.didRequestLocationPermission(self)
        }
    }

    func didAcceptLocationAuthorization(didShowPrompt: Bool) {
        if let viewModel, viewModel.isShortenedRegistrationEnabled, didShowPrompt {
            delegate?.didTapLogin(self)
        }
        shieldDataCollector.sendDeviceFingerprint(screen: Constants.Shield.login.rawValue)
        viewModel?.requestIsInPH()
    }
}
