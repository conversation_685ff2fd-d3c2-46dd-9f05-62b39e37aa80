//
//  MayaWelcomeV2ViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 5/7/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import ConfigurationService
import Foundation
import Injector
import Lottie

protocol MayaWelcomeV2ViewControllerDelegate: AnyObject {
    func didTapRegistration(_ viewController: Maya<PERSON>elcomeV2ViewController)
    func didTapLogin(_ viewController: MayaWelcomeV2ViewController)
    func didRequestLocationPermission(_ viewController: MayaWelcomeV2ViewController)
    func didRequireMayaIntroductionScreens(_ viewController: MayaWelcomeV2ViewController)
    func didTapRegisterToShowTinbo(_ viewController: MayaWelcomeV2ViewController)
}

class MayaWelcomeV2ViewController: ViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject var shieldDataCollector: ShieldDataCollectorProtocol

    @IBOutlet weak var footerLabel: UILabel!
    @IBOutlet weak var deviceIDLabel: UILabel!
    @IBOutlet weak var registerButton: MayaButton!

    weak var delegate: MayaWelcomeV2ViewControllerDelegate?
    var analyticsSourcePage: AnalyticsConstants.SourcePage?

    override var shouldUseMayaNavigationBar: Bool { true }
    override var navigationBarBackgroundColor: UIColor { CommonAsset.MayaColors.Background.backgroundPrimaryBlack.color }

    private lazy var welcomeAnimationView: LottieAnimationView = {
        let view = LottieAnimationView(name: "welcome")
        view.loopMode = .loop
        view.contentMode = .scaleAspectFit
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()

    deinit {
        viewModel?.unobserveLocationAuthorization()
        viewModel?.destroyLocationAuthorizerService()
        viewModel?.destroyLocationCheckerService()
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        #if !PROD_TARGET && !SANDBOX_TARGET
        setupDeviceIDLabel()
        #endif
        setupWelcomeAnimationView()
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.RegV2.welcome)
        super.viewWillAppear(animated)

        viewModel?.observeLocationAuthorization(delegate: self)
        viewModel?.observeLocationChecking()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        logViewed()
    }

    @IBAction func didTapLogin(_ sender: UIButton) {
        if let viewModel, viewModel.shouldShowNativeLocationPermissionDialog {
            viewModel.locationAuthorizerService.requestAuthorization()
        } else {
            delegate?.didTapLogin(self)
        }

        let buttonText = sender.title(for: .normal) ?? ""
        logLoginButtonTapped(buttonText: buttonText)
    }

    @IBAction func didTapRegister(_ sender: UIButton) {
        guard let viewModel = viewModel else { return }

        if viewModel.shouldShowTinboPrompt {
            delegate?.didTapRegisterToShowTinbo(self)
        } else {
            delegate?.didTapRegistration(self)
        }

        let buttonText = sender.title(for: .normal) ?? ""
        logRegisterButtonTapped(buttonText: buttonText)
    }

    @objc private func didDoubleTapFooter() {
        deviceIDLabel.isShown = true
    }

    @objc private func didTapDeviceID() {
        guard let deviceID = viewModel?.deviceID else { return }

        let deviceIDString = "Device ID: \(deviceID)"
        UIPasteboard.general.string = deviceIDString

        showMayaSnackBar(message: "Device ID copied!")
    }

    private func setupWelcomeAnimationView() {
        view.addSubview(welcomeAnimationView)

        NSLayoutConstraint.activate([
            welcomeAnimationView.bottomAnchor.constraint(equalTo: registerButton.topAnchor),
            welcomeAnimationView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            welcomeAnimationView.widthAnchor.constraint(equalToConstant: view.bounds.width),
            welcomeAnimationView.heightAnchor.constraint(equalTo: welcomeAnimationView.widthAnchor)
        ])

        welcomeAnimationView.play()
    }
}

// MARK: ViewModelBindable protocol
extension MayaWelcomeV2ViewController: ViewModelBindable {
    func binding(_ viewModel: MayaLandingViewModelProtocol?) {
        #if !PROD_TARGET && !SANDBOX_TARGET
        setupDeviceID()
        #endif
    }

    private func setupDeviceID() {
        guard let deviceID = viewModel?.deviceID else { return }
        deviceIDLabel.text = "Device ID: \(deviceID)"
    }

    private func setupDeviceIDLabel() {
        let footerTapGesture = UITapGestureRecognizer(target: self, action: #selector(didDoubleTapFooter))
        footerTapGesture.numberOfTapsRequired = 2
        footerLabel.addGestureRecognizer(footerTapGesture)

        let deviceIDTapGesture = UITapGestureRecognizer(target: self, action: #selector(didTapDeviceID))
        deviceIDLabel.addGestureRecognizer(deviceIDTapGesture)
    }
}

// MARK: - LocationAuthorizerServiceDelegate methods
extension MayaWelcomeV2ViewController: LocationAuthorizerServiceDelegate {
    func didNotDetermineLocationAuthorization() {
        // Do nothing
    }

    func didDenyLocationAuthorization(didShowPrompt: Bool) {
        if didShowPrompt {
            delegate?.didTapLogin(self)
        }
    }

    func didAcceptLocationAuthorization(didShowPrompt: Bool) {
        if didShowPrompt {
            delegate?.didTapLogin(self)
        }
        shieldDataCollector.sendDeviceFingerprint(screen: Constants.Shield.login.rawValue)
        viewModel?.requestIsInPH()
    }
}

// MARK: - Analytics Events Methods
private extension MayaWelcomeV2ViewController {
    func logViewed() {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.landingPage.viewed(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: analyticsSourcePage?.rawValue ?? ""])
    }

    func logLoginButtonTapped(buttonText: String) {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.landingPage.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.screenName: AnalyticsConstants.ScreenName.landing,
                                                       AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.loginPage,
                                                       AnalyticsAttributeKey.button: buttonText])
    }

    func logRegisterButtonTapped(buttonText: String) {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.landingPage.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.screenName: AnalyticsConstants.ScreenName.landing,
                                                       AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.accountCreation,
                                                       AnalyticsAttributeKey.button: buttonText])
    }
}
