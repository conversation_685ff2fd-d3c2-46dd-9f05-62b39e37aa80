//
//  MayaProminentDisclosureViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 2/28/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import Foundation
import UIKit

protocol MayaProminentDisclosureViewControllerDelegate: AnyObject {
    func didAgreeAndContinue(_ viewController: MayaProminentDisclosureViewController)
}

class MayaProminentDisclosureViewController: BottomSheetV2ViewController, MayaAnalyticsDurationProtocol {
    @IBOutlet weak var textView: UITextView!
    @IBOutlet weak var textViewHeightConstraint: NSLayoutConstraint!

    weak var delegate: MayaProminentDisclosureViewControllerDelegate?

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.Disclosure())
        super.viewWillAppear(animated)
    }

    @IBAction func didTapAgreeAndContinue(_ sender: UIButton) {
        guard let viewModel = viewModel else { return }
        analyticsUtils.logMayaEvents(.tap(Analytics.Disclosure.agree))
        viewModel.setProminentDisclosureScreenShown()
        delegate?.didAgreeAndContinue(self)
    }

    override func viewDidLoad() {
        super.viewDidLoad()

        let spiel = textView.text ?? ""
        let attributedString = NSMutableAttributedString(string: spiel, attributes: [.font: CommonFontFamily.CerebriSansPro.regular.font(size: 14)!, .foregroundColor: CommonAsset.MayaColors.Grey.grey6.color])
        let keyHighlightsRange = (spiel as NSString).range(of: L10n.Disclosure.Spiel.KeyHighlights.title)
        attributedString.addAttribute(.font, value: CommonFontFamily.CerebriSansPro.bold.font(size: 14)!, range: keyHighlightsRange)
        textView.attributedText = attributedString
        textView.layer.cornerRadius = 16
        if UIScreen.main.bounds.height < 570 {
            textViewHeightConstraint.isActive = false
            textView.isScrollEnabled = true
            let constraints = [textView.heightAnchor.constraint(equalToConstant: 350)]
            NSLayoutConstraint.activate(constraints)
        }
    }
}

extension MayaProminentDisclosureViewController: ViewModelBindable {
    func binding(_ viewModel: MayaProminentDisclosureViewModelProtocol?) {
        // Do nothing
    }
}
