//
//  MayaOTPViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 2/17/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import ConfigurationService
import Error
import Injector
import ReactiveCocoa
import ReactiveSwift
import UIKit

protocol MayaOTPViewControllerDelegate: AnyObject {
    func didReceiveOTPError(error: PayMayaError, _ viewController: MayaOTPViewController)
    func didAuthenticate(_ viewController: <PERSON>OTPViewController, response: Codable?)
    func didViewTermsAndCondition(_ viewController: MayaOTPViewController)
    func didReceivePostAuthenticationActionError(_ viewController: MayaOTPViewController)
    func didRequestDisplayMayaAlertViewModel(viewModel: MayaAlertViewModel, response: Codable?, viewController: MayaOTPViewController)
    func didRequestDisplayMayaReceiptViewModel(viewModel: MayaReceiptViewModelProtocol, response: Codable?, viewController: MayaOTPViewController)
}

class MayaOTPViewController: FormsViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject private var profileTrackingManager: ProfileTrackingManager
    @Inject private var configurationService: ConfigurationService

    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var otpDescriptionLabel: UILabel!
    @IBOutlet weak var proceedButton: MayaButton!
    @IBOutlet weak var resendCountdownLabel: UILabel!
    @IBOutlet weak var resendButton: UIButton!
    @IBOutlet weak var helpCenterView: MayaHelpCenterView!

    @IBOutlet weak var firstOTPInputView: MayaSingleCharacterInputView!
    @IBOutlet weak var secondOTPInputView: MayaSingleCharacterInputView!
    @IBOutlet weak var thirdOTPInputView: MayaSingleCharacterInputView!
    @IBOutlet weak var fourthOTPInputView: MayaSingleCharacterInputView!
    @IBOutlet weak var fifthOTPInputView: MayaSingleCharacterInputView!
    @IBOutlet weak var sixthOTPInputView: MayaSingleCharacterInputView!

    private var firstOTPField: TextField { return firstOTPInputView.textField }
    private var secondOTPField: TextField { return secondOTPInputView.textField }
    private var thirdOTPField: TextField { return thirdOTPInputView.textField }
    private var fourthOTPField: TextField { return fourthOTPInputView.textField }
    private var fifthOTPField: TextField { return fifthOTPInputView.textField }
    private var sixthOTPField: TextField { return sixthOTPInputView.textField }

    private var inputViews: [MayaSingleCharacterInputView] {
        [firstOTPInputView, secondOTPInputView, thirdOTPInputView, fourthOTPInputView, fifthOTPInputView, sixthOTPInputView]
    }

    private var textFields: [TextField] {
        [firstOTPField, secondOTPField, thirdOTPField, fourthOTPField, fifthOTPField, sixthOTPField]
    }

    var countdownTimer: Timer?
    var countdownSeconds: Int = 0
    var analyticsModuleString: String?
    var errorSignals: Signal<PayMayaError, Never>?
    var requestOTP: Bool = false
    private var didAutoFillOtp: Bool = false

    weak var delegate: MayaOTPViewControllerDelegate?

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override var titleContentOffset: CGFloat {
        return titleLabel.frame.minY + titleLabel.frame.size.height
    }

    override var mayaNavigationBarScrollView: UIScrollView? {
        return scrollView
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        textFields.forEach {
            NotificationCenter.default.addObserver( self, selector: #selector(keyboardDidShow), name: UITextField.textDidChangeNotification, object: $0)
        }
    }

    override func viewWillAppear(_ animated: Bool) {
        if isShortenedRegistrationEnabled() {
            setModule(Analytics.RegV2.otp)
        } else {
            setModule(Analytics.Otp(analyticsModuleString))
        }
        super.viewWillAppear(animated)
    }

    override func viewDidAppear(_ animated: Bool) {
        guard let viewModel = viewModel else { return }

        switch viewModel.otpType {
        case .registration:
            if MayaOnboardingUtility().isOnboardingRoutingEnabled(),
               let partner = MayaOnboardingUtility().fetchOnboardingPartner() {
                analyticsService.logMayaEvents(action: .appear(Analytics.Verification()),
                                               keyAttributes: [AnalyticsAttributeKey.partner: partner,
                                                               AnalyticsAttributeKey.branch: MayaOnboardingUtility().fetchOnboardingBranch(),
                                                               AnalyticsAttributeKey.kycLevel: MayaOnboardingUtility().fetchKYCLevel()])
            }
            super.viewDidAppear(animated)
            firstOTPField.becomeFirstResponder()
        default:
            super.viewDidAppear(animated)
            firstOTPField.becomeFirstResponder()
        }
        logAnalyticsViewed()
    }

    override func didTapBack(_ sender: Any) {
        view.endEditing(true)

        guard let viewModel = viewModel else { return }
        switch viewModel.otpType {
        case .changeMin:
            let alertViewModel = MayaAlertViewModel(title: L10n.ChangeMin.Maya.Confirm.Back.title, message: L10n.ChangeMin.Maya.Confirm.Back.message, image: CommonAsset.Images.Alert.image3DWarning.image, closeButtonTitle: CommonStrings.Common.cancel, actionsAxis: .horizontal)
            let action = MayaAlertAction(title: CommonStrings.Common.leave, style: .primary) { [weak self] in
                guard let self = self else { return }
                self.dismissMayaBottomAlert(viewModel: alertViewModel, viewController: self) {
                    self.proceedToGoBack()
                }
            }
            alertViewModel.addAction(action)
            showBottomAlert(viewModel: alertViewModel)
        case .registration:
            logBackButtonTapped()
            proceedToGoBack()
        default:
            proceedToGoBack()
        }
    }

    override func setupAccessibilityIdentifiers() {
        inputViews.forEach { $0.setAccessibilityIdentifiers(placeholder: "otp") }
    }

    @IBAction func didTapResendCode(_ sender: UIButton) {
        if !isShortenedRegistrationEnabled() {
            analyticsService.logMayaEvents(action: .tap(Analytics.Otp.resend(analyticsModuleString)))
        }
        guard let viewModel = viewModel else { return }
        viewModel.resendOTPCodeAction.apply().start()
        startResendCountdown()

        switch viewModel.otpType {
        case .registration:
            logResendCodeButtonTapped()
        default: break
        }
    }

    @objc func updateResendCountdown() {
        let countdownEnded = countdownSeconds == 0
        resendCountdownLabel.isHidden = countdownEnded
        resendButton.isHidden = !countdownEnded
        guard !countdownEnded else {
            countdownTimer?.invalidate()
            return
        }
        let secondsString = countdownSeconds == 1 ? "second" : "seconds"
        resendCountdownLabel.text = L10n.Authenticate.Resendcode.New.spiel(countdownSeconds, secondsString)
        countdownSeconds -= 1
    }

    // Check if SMS autofill was trigged
    @objc func keyboardDidShow(notification: NSNotification) {
        textFields.forEach {
            guard let size = $0.text?.count, size > 0 else {
                didAutoFillOtp = false
                return }

            didAutoFillOtp = true
        }
    }

    private func proceedToGoBack() {
        super.didTapBack(self)
        countdownTimer?.invalidate()
    }

    private func isShortenedRegistrationEnabled() -> Bool {
        guard let viewModel = viewModel as? MayaRegistrationOTPViewModel else { return false }
        return viewModel.isShortenedRegistrationEnabled
    }
}

private extension MayaOTPViewController {
    func setupErrorHandling() {
        guard let errorSignals = self.errorSignals else { return }

        let showAlertWithSignoutClosure: ((PayMayaError) -> Void) = { [weak self] error in
            guard let self = self, var errorViewModel = error.viewModel else { return }
            errorViewModel.actionClosure = {
                self.viewModel?.signoutAction?.apply().start()
            }
            errorViewModel.closeButtonClosure = {
                self.viewModel?.signoutAction?.apply().start()
            }
            self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
        }

        let showAlertThenSendOTPErrorSignalClosure: ((PayMayaError) -> Void) = { [weak self] error in
            guard let self = self, var errorViewModel = error.viewModel else { return }
            errorViewModel.actionClosure = {
                self.delegate?.didReceiveOTPError(error: error, self)
            }
            errorViewModel.closeButtonClosure = {
                self.delegate?.didReceiveOTPError(error: error, self)
            }
            self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
        }

        errorSignals.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard
                let self = self,
                var errorViewModel = error.viewModel,
                let viewModel = self.viewModel
                else { return }
            switch error.type {
            case .verificationRedirect:
                errorViewModel.closeButtonClosure = {
                    switch viewModel.otpType {
                    case .registration:
                        self.logRegistrationErrorButtonTapped(errorType: error.type, errorMessage: errorViewModel.message)
                    default: break
                    }
                    self.delegate?.didReceiveOTPError(error: error, self)
                }
                self.logAnalyticsErrorViewed(errorViewModel: errorViewModel)
                self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
            case .otpExpired, .mfaChallengeMultipleStart:
                showAlertThenSendOTPErrorSignalClosure(error)
            case .mfaChallengeExpired:
                switch viewModel.otpType {
                case .sessionTimeoutv5, .loginv5:
                    showAlertWithSignoutClosure(error)
                default:
                    showAlertThenSendOTPErrorSignalClosure(error)
                }
            case .mfaChallengeMaxAttempts:
                showAlertWithSignoutClosure(error)
            case .sessionTimeout:
                switch viewModel.otpType {
                case .sessionTimeoutv5, .loginv5:
                    showAlertThenSendOTPErrorSignalClosure(error)
                default: break // Do nothing. Session Timeout screen will appear for any other modules using this VC
                }
            case .otpMaxAttempts:
                if case .changeMin = viewModel.otpType {
                    showAlertWithSignoutClosure(error)
                } else {
                    showAlertThenSendOTPErrorSignalClosure(error)
                }
            case .shieldSpecialError, .shieldStandardError:
                if viewModel is MayaSessionsOTPViewModel {
                    self.handleShieldError(errorViewModel: errorViewModel)
                } else {
                    fallthrough
                }
            default:
                let completion: () -> Void = {
                    switch viewModel.otpType {
                    case .registration:
                        self.logRegistrationErrorButtonTapped(errorType: error.type, errorMessage: errorViewModel.message)

                    default: break
                    }
                }
                self.logAnalyticsErrorViewed(errorViewModel: errorViewModel)
                self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true, completion: completion)
            }
        }?.addToDisposeBag(disposeBag)
    }

    func displayPrivacyPolicyAlert() {
        guard viewModel is MayaLoginOTPViewModel || viewModel is MayaSessionsOTPViewModel else { return }
        var policyNumber = ""
        if let viewModel = self.viewModel as? MayaLoginOTPViewModel {
            policyNumber = viewModel.getPrivacyPolicyVersion()
        } else if let viewModel = self.viewModel as? MayaSessionsOTPViewModel {
            policyNumber = viewModel.getPrivacyPolicyVersion()
        }
        view.endEditing(true)
        let title = L10n.Privacy.HasUpdate.title + " \(policyNumber)"
        let message = L10n.Privacy.hasUpdate
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: L10n.Privacy.viewterms, style: .default, handler: { [weak self] _ in
            guard let self = self else { return }
            if self.presentedViewController == alert {
                self.dismiss(animated: true, completion: nil)
            }
            self.delegate?.didViewTermsAndCondition(self)
        }))
        present(alert, animated: true, completion: nil)
    }

    func showSuccessForRegistration(response: Codable?) {
        guard let viewModel = self.viewModel as? MayaRegistrationOTPViewModel else { return }
        let title = L10n.Authenticate.Registration.Otp.Success.title
        let spiel = L10n.Authenticate.Registration.Otp.Success.message

        let attributedString = NSMutableAttributedString(string: spiel, attributes: [.font: CommonFontFamily.CerebriSansPro.regular.font(size: 14)!, .foregroundColor: CommonAsset.MayaColors.Content.contentGrey6.color])
        let basicAccountRange = (spiel as NSString).range(of: CommonStrings.Common.Basic.account)
        attributedString.addAttribute(.foregroundColor, value: CommonAsset.MayaColors.Content.contentPrimaryGreen.color, range: basicAccountRange)

        let alert1 = MayaAlertAction(title: CommonStrings.Common.Go.To.home, style: .secondary) { [weak self] in
            guard let self = self else { return }

            MayaOnboardingUtility().resetAutoProvisionInfoIfNeeded()

            self.analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.mainFlow.tapped(),
                                                keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.BottomSheet.registrationSuccess,
                                                                AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.dashboard,
                                                                AnalyticsAttributeKey.button: CommonStrings.Common.Go.To.home])
            if viewModel.shouldLogRefereeMGMAnalytics {
                analyticsService.logMayaEvents(action: .tapped(Analytics.RefereeMGM.welcomeSheet(.goToHome)))
            }

            if isShortenedRegistrationEnabled() {
                logRegistrationSuccessButtonTapped(AnalyticsConstants.ButtonText.goToHome,
                                                   destinationPage: AnalyticsConstants.ScreenName.dashboard)
            }
            self.delegate?.didAuthenticate(self, response: response)
        }

        let alert2 = MayaAlertAction(title: viewModel.upgradeNowLabelTreatment, style: .primary) { [weak self] in
            guard let self = self else { return }

            MayaOnboardingUtility().resetAutoProvisionInfoIfNeeded()

            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.mainFlow.tapped(),
                                                keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.BottomSheet.registrationSuccess,
                                                                AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.loader,
                                                                AnalyticsAttributeKey.button: CommonStrings.Common.Upgrade.now])
            if viewModel.shouldLogRefereeMGMAnalytics {
                analyticsService.logMayaEvents(action: .tapped(Analytics.RefereeMGM.welcomeSheet(.upgradeNow)))
            }

            if isShortenedRegistrationEnabled() {
                logRegistrationSuccessButtonTapped(AnalyticsConstants.ButtonText.upgradeYourAccount,
                                                   destinationPage: AnalyticsConstants.ScreenName.kycUpgrade)
            }
            viewModel.setUserDefaultShouldShowUpgradeAccount()
            delegate?.didAuthenticate(self, response: response)
        }

        var actions: [MayaAlertAction]
        // Don't add Go To Home Button if onboardingAndKYCFlowEnabled or (onboardingAndKYCFlowV2Enabled and partner onboarding)
        if configurationService.onboardingAndKYCFlowEnabled.value ||
            (configurationService.onboardingAndKYCFlowV2Enabled.value && MayaOnboardingUtility().fetchOnboardingPartner() != nil) || MayaOnboardingUtility().isSavingsAutoProvisionOptIn() {
            actions = [alert2]
        } else {
            actions = [alert1, alert2]
        }

        let alertViewModel = MayaAlertViewModel(title: title, attributedMessage: attributedString, image: CommonAsset.Images.Alert.imageDefaultSuccess.image, actions: actions, actionsAxis: .horizontal)

        if viewModel.isRegistrationMGMEnabled && viewModel.canBeReferred {
            alertViewModel.additionalInfo = getAdditionalInfoForSubmitInviteCode(with: response)
        } else {
            if viewModel.canBeReferred {
                viewModel.setUserDefaultShouldSubmitInviteCode()
            } else {
                viewModel.setShouldPrefillReferralCode(shouldPrefill: false)
            }
        }

        // Call Savings Auto-Provision Opt-in
        saveAutoProvisionIfNeeded()
        showBottomAlert(viewModel: alertViewModel, analyticsModuleType: .sheet(Analytics.RegistrationSuccess()), withCloseButton: false)
        if viewModel.shouldLogRefereeMGMAnalytics {
            analyticsService.logMayaEvents(action: .appeared(Analytics.RefereeMGM.welcomeSheet(.none)))
        }

        logRegistrationSuccess()
    }

    private func saveAutoProvisionIfNeeded() {
        guard let viewModel = self.viewModel as? MayaRegistrationOTPViewModel,
              configurationService.autoProvisioningEnabled.value
        else { return }

        if MayaOnboardingUtility().isSavingsAutoProvisionOptIn() {
            // TODO: use UserDefaultsStoreId.savingsAutoProvisionOptInSent as a flag to retry Opt-in request, to be impplemented on future release.
            MayaOnboardingUtility().setSavingsAutoProvisionOptInSent(false)
            viewModel.savingsAutoProvisionAction?.apply().start()
            return
        }
        // Remove all auto provision userdefaults if needed
        MayaOnboardingUtility().resetAutoProvisionInfoIfNeeded()
    }

    func handleAuthenticationResult(authenticationResult: AuthenticationResult) {
        guard let viewModel = self.viewModel else { return }
        switch authenticationResult {
        case .dataPrivacy:
            self.displayPrivacyPolicyAlert()
        case .success(let response):
            switch viewModel.otpType {
            case .registration:
                guard let regViewModel = self.viewModel as? MayaRegistrationOTPViewModel else {
                    self.showSuccessForRegistration(response: response)
                    return
                }
                if regViewModel.isRegToKYCFlowEnabled {
                    MayaOnboardingUtility().resetAutoProvisionInfoIfNeeded()
                    regViewModel.setUserDefaultShouldShowUpgradeAccount()
                    delegate?.didAuthenticate(self, response: response)
                } else {
                    self.showSuccessForRegistration(response: response)
                }
            default:
                self.delegate?.didAuthenticate(self, response: response)
            }
        case .successAlert(let viewModel, let response):
            if var successViewModel = viewModel as? SuccessAlertViewModel {
                successViewModel.closeCompletion = { [weak self] in
                    guard let self = self else { return }
                    self.delegate?.didAuthenticate(self, response: response)
                }
                if analyticsModuleString == OTPAnalyticsModule.changeMinNewIdentity.rawValue {
                    self.viewModel?.signoutAction?.apply().start()
                }
                showSuccessAlert(successViewModel)
            }
        case .successBottomAlert(let viewModel, let response):
            guard let alertViewModel = viewModel as? MayaAlertViewModel, let otpType = self.viewModel?.otpType else { return }
            switch otpType {
            case .bankTransfer, .sendMoney:
                delegate?.didRequestDisplayMayaAlertViewModel(viewModel: alertViewModel, response: response, viewController: self)
            case .changeMin(_, let changeMinOTPType):
                switch changeMinOTPType {
                case .newNumber:
                    delegate?.didRequestDisplayMayaAlertViewModel(viewModel: alertViewModel, response: response, viewController: self)
                default: break
                }
            default:
                alertViewModel.closeCompletionHandler = { [weak self] in
                    guard let self = self else { return }
                    self.proceedToGoBack()
                }
                showBottomAlert(viewModel: alertViewModel)
            }
        case .successReceipt(let receipt, let response):
            delegate?.didRequestDisplayMayaReceiptViewModel(viewModel: receipt, response: response, viewController: self)
        case .postAuthenticationAction:
            dispatchToMain(withDelay: .now() + 0.1) {
                viewModel.postAuthenticationAction?.apply().start()
            }
        }
    }

    func getAdditionalInfoForSubmitInviteCode(with response: Codable?) -> MayaAlertAdditionalInfoType? {
        guard let viewModel = self.viewModel as? MayaRegistrationOTPViewModel else { return nil }
        let text = L10n.Authenticate.Registration.SubmitInviteCode.subtitle
        let attributes = [NSAttributedString.Key.foregroundColor: AssetProvider.CommonAsset.MayaColors.Content.contentGrey5.color, NSAttributedString.Key.font: CommonFontFamily.CerebriSansPro.book.font(size: 14)!]
        let attributedString = NSMutableAttributedString(string: text, attributes: attributes)
        if let inviteCodeRange = text.range(of: L10n.Authenticate.Registration.SubmitInviteCode.inviteCode) {
            attributedString.addAttribute(NSAttributedString.Key.font, value: CommonFontFamily.CerebriSansPro.medium.font(size: 14)!, range: NSRange(inviteCodeRange, in: text))
            attributedString.addAttribute(NSAttributedString.Key.foregroundColor, value: AssetProvider.CommonAsset.MayaColors.Primary.primaryGrownGreen.color, range: NSRange(inviteCodeRange, in: text))
        }

        let additionalInfo = MayaAlertAdditionalInfoType.custom(icon: Asset.MayaImages.Prelogin.iconThinking.image,
                                                                title: L10n.Authenticate.Registration.SubmitInviteCode.title,
                                                                subtitle: attributedString) { [weak self] in
            guard let self = self else { return }
            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.mainFlow.tapped(),
                                                keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.BottomSheet.registrationSuccess,
                                                                AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.submitInviteCode,
                                                                AnalyticsAttributeKey.button: AnalyticsConstants.ButtonText.inviteCode])
            analyticsService.logMayaEvents(action: .tapped(Analytics.RefereeMGM.welcomeSheet(.inputCode)))
            if isShortenedRegistrationEnabled() {
                logRegistrationSuccessButtonTapped(AnalyticsConstants.ButtonText.inviteCode,
                                                   destinationPage: AnalyticsConstants.ScreenName.referral)
            }
            viewModel.setUserDefaultShouldSubmitInviteCode()
            delegate?.didAuthenticate(self, response: response)
        }

        return additionalInfo
    }

    func handleShieldError(errorViewModel: ErrorAlertViewModel) {
        var isClosed = true
        self.showMayaErrorAlert(viewModel: errorViewModel, disableAction: true, completion: { _isClosed in
            isClosed = _isClosed
            self.routeActionDelegate?.didRequestSignout(self, viewModel: errorViewModel, signoutRoute: .login, performAction: !isClosed, showAlert: false)
        })
    }
}

extension MayaOTPViewController: ViewModelBindable {
    func binding(_ viewModel: MayaOTPViewModelProtocol?) {
        guard let viewModel = viewModel else {
            return
        }

        switch viewModel.otpType {
        case .registration: break
        default: navigationItem.title = nil
        }

        otpDescriptionLabel.text = viewModel.otpMessage
        otpDescriptionLabel.textAlignment = viewModel.otpMessageAlignment
        helpCenterView.isHidden = !viewModel.willDisplayNeedHelp
        proceedButton.setTitle(viewModel.ctaButtonTitle, for: .normal)

        viewModel.firstCode.mutableProperty <~ firstOTPField.reactive.continuousTextValues
        viewModel.secondCode.mutableProperty <~ secondOTPField.reactive.continuousTextValues
        viewModel.thirdCode.mutableProperty <~ thirdOTPField.reactive.continuousTextValues
        viewModel.fourthCode.mutableProperty <~ fourthOTPField.reactive.continuousTextValues
        viewModel.fifthCode.mutableProperty <~ fifthOTPField.reactive.continuousTextValues
        viewModel.sixthCode.mutableProperty <~ sixthOTPField.reactive.continuousTextValues

        formFields = [firstOTPField, secondOTPField, thirdOTPField, fourthOTPField, fifthOTPField, sixthOTPField]
        formFields.forEach { field in
            guard let textField = field as? TextField else { return }
            textField.addTarget(self, action: #selector(editingChanged(sender:)), for: .editingChanged)
            textField.deleteBackwardsHandler = { [weak self] previousText, currentText in
                guard previousText?.isEmpty == true,
                    currentText?.isEmpty == true,
                    let previousField = self?.moveToPreviousField(currentField: textField) as? TextField else {
                    return
                }
                previousField.text = String.empty
            }
        }
        setKeyboardReturnTypeOfTextFields()
        lastFormFieldCallback = { [weak self] in
            self?.viewModel?.startAuthenticationAction()
        }

        guard let authenticateAction = viewModel.authenticateAction,
            let resendCodeAction = viewModel.resendOTPCodeAction
            else { return }

        var executingSignals = Signal.merge([authenticateAction.isExecuting.signal, resendCodeAction.isExecuting.signal])
        if let postVerificationAction = viewModel.postAuthenticationAction {
            executingSignals = executingSignals.merge(with: postVerificationAction.isExecuting.signal)
        }

        if let startOTPChallengeAction = viewModel.startOTPChallengeAction {
            executingSignals = executingSignals.merge(with: startOTPChallengeAction.isExecuting.signal)
        }

        if let signoutAction = viewModel.signoutAction {
            executingSignals = executingSignals.merge(with: signoutAction.isExecuting.signal)
        }

        let errorSignals = Signal.merge([authenticateAction.errors, resendCodeAction.errors])
        self.errorSignals = errorSignals
        errorSignals.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self,
                let errorViewModel = error.viewModel
                else { return }

            if error.type != .validation {
                if !isShortenedRegistrationEnabled() {
                    self.analyticsService.logMayaEvents(action: .failure(Analytics.Otp(self.analyticsModuleString)), keyAttributes: [AnalyticsAttributeKey.reason: errorViewModel.message])
                }
            }
        }?.addToDisposeBag(disposeBag)

        executingSignals.observe(on: UIScheduler()).observeValues { [weak self] executing in
            guard let self = self else { return }
            if executing {
                self.showMayaLoader()
            } else {
                self.hideMayaLoader()
            }
        }?.addToDisposeBag(disposeBag)

        authenticateAction.values.observe(on: UIScheduler()).observeValues { [weak self] authenticationResult in
            guard let self = self else { return }
            if viewModel is MayaLoginOTPViewModel || viewModel is MayaRegistrationOTPViewModel || viewModel is MayaSessionsOTPViewModel {
                self.profileTrackingManager.setUserProfile(viewModel.user.value, source: .loginOTP)
            }
            self.handleAuthenticationResult(authenticationResult: authenticationResult)
            if !isShortenedRegistrationEnabled() {
                self.analyticsService.logMayaEvents(action: .success(Analytics.Otp(self.analyticsModuleString)))
            }
        }?.addToDisposeBag(disposeBag)

        authenticateAction.completed.observe(on: UIScheduler()).observeValues { [weak self] _ in
            self?.countdownTimer?.invalidate()
        }?.addToDisposeBag(disposeBag)

        authenticateAction.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self else { return }
            if error.type == .otpExpired || error.type == .mayaCreditSubmissionError {
                self.countdownTimer?.invalidate()
            }
        }?.addToDisposeBag(disposeBag)

        proceedButton.reactive.controlEvents(.touchUpInside).observe(on: UIScheduler()).observeValues { [weak self] _ in
            guard let self = self else { return }
            viewModel.startAuthenticationAction()
            self.view.endEditing(true)

            switch viewModel.otpType {
            case .registration:
                logCtaButtonTapped()
            case .newMayaCredit:
                self.analyticsService.logMayaEvents(action: .tap(Analytics.Otp.verify(self.analyticsModuleString)))
            default:
                self.analyticsService.logMayaEvents(action: .tap(Analytics.Otp.proceed(self.analyticsModuleString)))
            }
        }?.addToDisposeBag(disposeBag)

        viewModel.postAuthenticationAction?.values.observe(on: UIScheduler()).observeValues { [weak self] authenticationResult in
            guard let self = self else { return }
            if viewModel is MayaLoginOTPViewModel || viewModel is MayaRegistrationOTPViewModel || viewModel is MayaSessionsOTPViewModel {
                self.profileTrackingManager.setUserProfile(viewModel.user.value, source: .loginOTP)
            }
            self.handleAuthenticationResult(authenticationResult: authenticationResult)
            if !isShortenedRegistrationEnabled() {
                self.analyticsService.logMayaEvents(action: .success(Analytics.Otp(self.analyticsModuleString)))
            }
        }?.addToDisposeBag(disposeBag)

        viewModel.postAuthenticationAction?.errors.observe(on: UIScheduler()).observeValues({ [weak self] error in
            guard let self = self, var errorViewModel = error.viewModel else { return }

            if !isShortenedRegistrationEnabled() {
                logError(errorViewModel: errorViewModel)
            }

            switch error.type {
            case .shieldSpecialError, .shieldStandardError:
                if viewModel is MayaSessionsOTPViewModel {
                    self.handleShieldError(errorViewModel: errorViewModel)
                } else {
                    fallthrough
                }
            default:
                errorViewModel.actionClosure = {
                    self.delegate?.didReceivePostAuthenticationActionError(self)
                }
                errorViewModel.closeButtonClosure = {
                    if viewModel.isReachable {
                        self.delegate?.didReceivePostAuthenticationActionError(self)
                    }
                }
                self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
            }
        })?.addToDisposeBag(disposeBag)

        viewModel.signoutAction?.completed.observe(on: UIScheduler()).observeValues({ [weak self] in
            guard let self = self else { return }
            self.routeActionDelegate?.didRequestSignout(self, viewModel: nil, signoutRoute: .login, performAction: false, showAlert: true)
        })?.addToDisposeBag(disposeBag)

        viewModel.signoutAction?.errors.observe(on: UIScheduler()).observeValues({ [weak self] _ in
            guard let self = self else { return }
            self.routeActionDelegate?.didRequestSignout(self, viewModel: nil, signoutRoute: .login, performAction: false, showAlert: true)
        })?.addToDisposeBag(disposeBag)

        viewModel.startOTPChallengeAction?.errors.observe(on: UIScheduler()).observeValues({ [weak self] error in
            guard let self = self, var errorViewModel = error.viewModel
            else { return }

            errorViewModel.actionClosure = {
                self.delegate?.didReceivePostAuthenticationActionError(self)
            }
            errorViewModel.closeButtonClosure = {
                if viewModel.isReachable {
                    self.delegate?.didReceivePostAuthenticationActionError(self)
                }
            }
            self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
        })?.addToDisposeBag(disposeBag)

        viewModel.savingsAutoProvisionAction?.completed.observe(on: UIScheduler()).observeValues({ _ in
            // Opt-in was successful, set sent flag to true
            MayaOnboardingUtility().setSavingsAutoProvisionOptInSent(true)
        })?.addToDisposeBag(disposeBag)

        viewModel.savingsAutoProvisionAction?.errors.observe(on: UIScheduler()).observeValues({ _ in
            // do nothing
        })?.addToDisposeBag(disposeBag)

        if requestOTP, let startOTPAction = viewModel.startOTPChallengeAction {
            startOTPAction.apply().start()
        }

        setupErrorHandling()
    }

    @objc func editingChanged(sender: TextField) {
        guard let text = sender.text,
            !text.isEmpty else {
            return
        }
        moveToNextField(currentField: sender)
    }
}

// MARK: - ViewControllerBindable Methods
extension MayaOTPViewController: ViewControllerBindable {
    func binding() {
        setupHelpCenterView()
        startResendCountdown()
        setupTitle()
        setupTextFields()
    }

    private func setupHelpCenterView() {
        helpCenterView.setProperties(web: Constants.WebView.visitOTPHelpCenter, with: self)
    }

    private func startResendCountdown() {
        if !isShortenedRegistrationEnabled() {
            analyticsService.logMayaEvents(action: .tap(Analytics.Otp.resend(analyticsModuleString)))
        }
        guard let viewModel = viewModel else { return }
        countdownSeconds = viewModel.resendCountdown
        countdownTimer = Timer.scheduledTimer(timeInterval: 1, target: self, selector: #selector(MayaOTPViewController.updateResendCountdown), userInfo: nil, repeats: true)
    }

    func setupTitle() {
        let spiel = L10n.Authenticate.Registration.Otp.Screen.title
        let attributedString = NSMutableAttributedString(string: spiel, attributes: [.font: CommonFontFamily.Jeko.bold.font(size: 48)!, .foregroundColor: CommonAsset.MayaColors.Content.contentPrimaryBlack.color])
        let pinRange = (spiel as NSString).range(of: CommonStrings.Common.pin)
        attributedString.addAttribute(.foregroundColor, value: CommonAsset.MayaColors.Content.contentPrimaryGreen.color, range: pinRange)
        titleLabel.attributedText = attributedString
    }

    func setupTextFields() {
        textFields.forEach { $0.textContentType = .oneTimeCode }

        activeFormField.signal.observeValues { [weak self] activeField in
            self?.inputViews.forEach {
                $0.borderWidth = $0.textField == activeField ? 1 : 0
            }
        }
    }
}

// MARK: - Private Methods
private extension MayaOTPViewController {
    func logAnalyticsViewed() {
        guard let viewModel = viewModel else { return }
        switch viewModel.otpType {
        case .registration:
            if isShortenedRegistrationEnabled() {
                analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.otp.viewed(),
                                               keyAttributes: [AnalyticsAttributeKey.screenName: AnalyticsConstants.ScreenName.registrationCode,
                                                               AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.accountProvisioning])
            } else {
                analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.otp.viewed(),
                                               keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.dataPersonalization])
            }
        default: break
        }
    }

    func logBackButtonTapped() {
        if isShortenedRegistrationEnabled() {
            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.otp.tapped(),
                                           keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.registrationCode,
                                                           AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.accountProvisioning,
                                                           AnalyticsAttributeKey.button: CommonStrings.Common.back])
        } else {
            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.otp.tapped(),
                                           keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.registrationOTP,
                                                           AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.dataPersonalization,
                                                           AnalyticsAttributeKey.button: CommonStrings.Common.back])
        }
    }

    func logCtaButtonTapped() {
        guard let viewModel else { return }
        if isShortenedRegistrationEnabled() {
            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.otp.tapped(),
                                           keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.registrationCode,
                                                           AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.registrationCode,
                                                           AnalyticsAttributeKey.button: AnalyticsConstants.ButtonText.verify])
        } else {
            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.otp.tapped(),
                                           keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.registrationOTP,
                                                           AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.registrationOTP,
                                                           AnalyticsAttributeKey.button: viewModel.ctaButtonTitle])
            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.otp.inputted(),
                                           keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.registrationOTP,
                                                           AnalyticsAttributeKey.inputType: AnalyticsConstants.ActionFunction().autofill(didAutoFillOtp)])
        }
    }

    func logHelpCenterButtonTapped() {
        if isShortenedRegistrationEnabled() {
            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.otp.tapped(),
                                           keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.registrationCode,
                                                           AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.helpCenter,
                                                           AnalyticsAttributeKey.button: AnalyticsConstants.ButtonText.helpCenter])
        } else {
            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.otp.tapped(),
                                           keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.registrationOTP,
                                                           AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.helpCenter,
                                                           AnalyticsAttributeKey.button: AnalyticsConstants.ButtonText.helpCenter])
        }
    }

    func logResendCodeButtonTapped() {
        if isShortenedRegistrationEnabled() {
            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.otp.tapped(),
                                           keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.registrationCode,
                                                           AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.registrationCode,
                                                           AnalyticsAttributeKey.button: AnalyticsConstants.ButtonText.resendCode])
        } else {
            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.otp.tapped(),
                                           keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.registrationOTP,
                                                           AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.registrationOTP,
                                                           AnalyticsAttributeKey.button: AnalyticsConstants.ButtonText.resendCode])
        }
    }

    func logRegistrationSuccess() {
        guard let cpmId = viewModel?.user.value?.walletId else { return }
        if isShortenedRegistrationEnabled() {
            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.mainFlow.success(),
                                           keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.registrationCode,
                                                           AnalyticsAttributeKey.destinationPage: AnalyticsConstants.BottomSheet.registrationV2Success,
                                                           AnalyticsAttributeKey.cpmId: cpmId])

            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.successBottomSheet.viewed(),
                                           keyAttributes: [AnalyticsAttributeKey.screenName: AnalyticsConstants.BottomSheet.registrationV2Success,
                                                           AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.registrationCode])
        } else {
            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.mainFlow.success(),
                                           keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.BottomSheet.registrationSuccess,
                                                           AnalyticsAttributeKey.cpmId: cpmId])
        }
    }

    func logRegistrationSuccessButtonTapped(_ button: String, destinationPage: String) {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.successBottomSheet.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.BottomSheet.registrationV2Success,
                                                       AnalyticsAttributeKey.destinationPage: destinationPage,
                                                       AnalyticsAttributeKey.button: button])
    }

    func logRegistrationErrorButtonTapped(errorType: ErrorType, errorMessage: String) {
        if isShortenedRegistrationEnabled() {
            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.error.tapped(),
                                           keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.Dialog.registrationError,
                                                           AnalyticsAttributeKey.destinationPage: errorType == .verificationRedirect ?
                                                           AnalyticsConstants.ScreenName.accountCreation : AnalyticsConstants.ScreenName.registrationCode,
                                                           AnalyticsAttributeKey.reason: errorMessage,
                                                           AnalyticsAttributeKey.button: CommonStrings.Common.Modal.Got.it])
        } else {
            analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.otpError.tapped(),
                                           keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.Dialog.registrationOTPError,
                                                           AnalyticsAttributeKey.destinationPage: errorType == .verificationRedirect ?
                                                           AnalyticsConstants.ScreenName.startAccount : AnalyticsConstants.ScreenName.registrationOTP,
                                                           AnalyticsAttributeKey.reason: errorMessage,
                                                           AnalyticsAttributeKey.button: CommonStrings.Common.Modal.Got.it])
        }
    }

    func logAnalyticsErrorViewed(errorViewModel: ErrorAlertViewModel) {
        guard let viewModel = viewModel else { return }
        switch viewModel.otpType {
        case .registration:
            if isShortenedRegistrationEnabled() {
                analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.error.viewed(),
                                               keyAttributes: [AnalyticsAttributeKey.screenName: AnalyticsConstants.Dialog.registrationError,
                                                               AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.registrationCode,
                                                               AnalyticsAttributeKey.reason: errorViewModel.message,
                                                               AnalyticsAttributeKey.errorCode: errorViewModel.error?.code ?? 0])
            } else {
                analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.Registration.otpError.viewed(),
                                               keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.registrationOTP,
                                                               AnalyticsAttributeKey.reason: errorViewModel.message])
            }
        default: break
        }
    }
}

// MARK: - UITextFieldDelegate Methods
extension MayaOTPViewController {
    override func textFieldDidBeginEditing(_ textField: UITextField) {
        super.textFieldDidBeginEditing(textField)
        DispatchQueue.main.async {
            // Do on next run loop
            let endPosition = textField.endOfDocument
            textField.selectedTextRange = textField.textRange(from: endPosition, to: endPosition)
        }
    }

    func textFieldDidEndEditing(_ textField: UITextField) {
        didAutoFillOtp = false
    }

    override func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        let newString = ((textField.text)! as NSString).replacingCharacters(in: range, with: string)
        if newString.count > 1 {
            textField.text = string
            moveToNextField(currentField: textField)
            return false
        }
        return true
    }
}

// MARK: - MayaHelpCenterViewDelegate Methods
extension MayaOTPViewController: MayaHelpCenterViewDelegate {
    func didRequestWebView(_ helpCenterView: MayaHelpCenterView, with url: URL) {
        routeActionDelegate?.didRequestWebView(self, with: url, willPresent: true, completion: nil)

        guard let viewModel = viewModel else { return }

        switch viewModel.otpType {
        case .registration:
            logHelpCenterButtonTapped()
        default: break
        }
    }
}

// Error Analytics Logging Method
// The method `logError` retrieves the `ErrorEventInfo` from the viewModel's `errorEventInfo` property (if available),
// modifies the `attributes` to include the error message and error code, and then logs the event using the analytics service.
extension MayaOTPViewController {
    func logError(errorViewModel: ErrorAlertViewModel) {
        guard var eventInfo = viewModel?.errorEventInfo else { return }

        eventInfo.attributes[.errorReason] = errorViewModel.message
        eventInfo.attributes[.errorCode] = errorViewModel.error?.code

        analyticsService.logMayaEvents(eventInfo.name, attributes: eventInfo.attributes)
    }
}
