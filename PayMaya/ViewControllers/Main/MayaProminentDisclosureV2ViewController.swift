//
//  MayaProminentDisclosureV2ViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 6/20/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Foundation
import Injector
import UIKit

protocol MayaProminentDisclosureV2ViewControllerDelegate: AnyObject {
    func didAgreeAndContinue(_ viewController: MayaProminentDisclosureV2ViewController)
}

final class MayaProminentDisclosureV2ViewController: BottomSheetV2ViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @IBOutlet weak var descriptionLabel: UILabel!
    @IBOutlet weak var textView: UITextView!
    weak var delegate: MayaProminentDisclosureV2ViewControllerDelegate?

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.RegV2.prominentDisclosure)
        super.viewWillAppear(animated)
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        logViewed()
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupViews()
    }

    func setupViews() {
        let spiel = textView.text ?? ""
        let baseFont = CommonFontFamily.CerebriSansPro.regular.font(size: 12)!
        let boldFont = CommonFontFamily.CerebriSansPro.bold.font(size: 12)!
        let textColor = CommonAsset.MayaColors.Grey.grey6.color

        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.paragraphSpacing = 4
        paragraphStyle.minimumLineHeight = 18

        let attributes: [NSAttributedString.Key: Any] = [
           .font: baseFont,
           .foregroundColor: textColor,
           .paragraphStyle: paragraphStyle
        ]

        let attributedString = NSMutableAttributedString(string: spiel, attributes: attributes)

        let boldedTitles = [
            L10n.Disclosure.Spiel.DataPrivacy.title,
            L10n.Disclosure.Spiel.KeyHighlights.title,
            L10n.Disclosure.Spiel.InstalledApplications.title,
            L10n.Disclosure.Spiel.ShareInformation.title,
            L10n.Disclosure.Spiel.DeviceAttributes.title,
            L10n.Disclosure.Spiel.DeviceIdentifiers.title,
            L10n.Disclosure.Spiel.DeviceSignals.title,
            L10n.Disclosure.Spiel.DataFromNetwork.title,
            L10n.Disclosure.Spiel.DataFromDevice.title,
            L10n.Disclosure.Spiel.AgreeAndContinue.title
        ]

        for title in boldedTitles {
            let range = (spiel as NSString).range(of: title)
            attributedString.addAttribute(.font, value: boldFont, range: range)
        }
        textView.textContainerInset = UIEdgeInsets(top: 14, left: 8, bottom: 14, right: 8)
        textView.attributedText = attributedString
        textView.layer.cornerRadius = 16

        let descriptionAttributedText = NSAttributedString(string: descriptionLabel.text ?? "", attributes: attributes)
        descriptionLabel.attributedText = descriptionAttributedText

        view.backgroundColor = .clear
    }

    @IBAction func didTapAgreeAndContinue(_ sender: UIButton) {
        guard let viewModel else { return }
        viewModel.setProminentDisclosureScreenShown()
        delegate?.didAgreeAndContinue(self)

        let buttonText = sender.titleLabel?.text ?? ""
        logContinueButtonTapped(buttonText: buttonText)
    }
}

// MARK: - ViewModelBindable Methods
extension MayaProminentDisclosureV2ViewController: ViewModelBindable {
    func binding(_ viewModel: MayaProminentDisclosureViewModelProtocol?) {
        // Do nothing
    }
}

// MARK: - Analytics Events Methods
extension MayaProminentDisclosureV2ViewController {
    func logViewed() {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.prominentDisclosure.viewed(),
                                       keyAttributes: [AnalyticsAttributeKey.screenName: AnalyticsConstants.BottomSheet.prominentDisclosure,
                                                       AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.initial])
    }

    func logContinueButtonTapped(buttonText: String) {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.prominentDisclosure.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.BottomSheet.prominentDisclosure,
                                                       AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.landing,
                                                       AnalyticsAttributeKey.button: buttonText])
    }
}
