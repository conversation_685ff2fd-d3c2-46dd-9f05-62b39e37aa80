//
//  MayaRegistrationFormV2ViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 5/8/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import Injector
import ReactiveSwift

protocol MayaRegistrationFormV2ControllerDelegate: AnyObject {
    func didTapPasswordTips(_ viewController: MayaRegistrationFormV2Controller)
    func didCompletePasswordValidationAndPolicyConsent(_ viewController: MayaRegistrationFormV2Controller)
}

final class MayaRegistrationFormV2Controller: FormsViewController, MayaProgressBarProtocol, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var firstNameView: MayaProfileValidationFieldView!
    @IBOutlet weak var middleNameView: MayaProfileValidationFieldView!
    @IBOutlet weak var noMiddleNameStackView: UIStackView!
    @IBOutlet weak var noMiddleNameCheckBox: MayaCheckBox!
    @IBOutlet weak var noMiddleNameLabel: UILabel!
    @IBOutlet weak var lastNameView: MayaProfileValidationFieldView!
    @IBOutlet weak var emailView: MayaProfileValidationFieldView!
    @IBOutlet weak var mobileNumberView: MayaProfileValidationFieldView!
    @IBOutlet weak var passwordView: MayaProfileValidationFieldView!
    @IBOutlet weak var passwordTipsLabel: UILabel!
    @IBOutlet weak var continueButton: MayaButton!

    var segment: Int = 1
    weak var delegate: MayaRegistrationFormV2ControllerDelegate?

    override var shouldUseMayaNavigationBar: Bool { true }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupViews()
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.RegV2.accountCreation)
        super.viewWillAppear(animated)
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        logViewed()
    }

    override func didTapBack(_ sender: Any) {
        super.didTapBack(sender)
        logBackButtonTapped()
    }

    private func setupViews() {
        setRightButtonItemTitle(L10n.Shortened.Registration.FlowProgressBar.progress(segment))
        titleLabel.text = L10n.Shortened.Registration.title
        noMiddleNameLabel.text = L10n.Shortened.Registration.No.Legal.middlename
        passwordTipsLabel.text = L10n.Shortened.Registration.Password.Tips.label
        continueButton.setTitle(L10n.Shortened.Registration.continue, for: .normal)

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(didTapPasswordTips))
        passwordTipsLabel.addGestureRecognizer(tapGesture)

        setupFieldViews()
    }

    @IBAction private func didTapNoMiddleNameCheckbox(_ sender: Any) {
        let status = !noMiddleNameCheckBox.status
        noMiddleNameCheckBox.setStatus(status)
        middleNameView.readOnly(status)
        viewModel?.hasNoLegalMiddleName.value = status
        updateMiddleNameView(status)
        logNoMiddleNameCheckBoxTapped(status)
    }

    @IBAction private func didTapContinue(_ sender: Any) {
        guard isAllFieldsValid() else { return }
        viewModel?.validateAndFetchConsentsAction.apply().start()
        logContinueButtonTapped()
    }

    private func updateMiddleNameView(_ hasNoLegalMiddleName: Bool) {
        if hasNoLegalMiddleName {
            middleNameView.resetField()
        } else {
            middleNameView.isValidOnInitialState = false
            middleNameView.actionTriggered()
            middleNameView.textField.becomeFirstResponder()
        }
    }

    @objc private func didTapPasswordTips() {
        delegate?.didTapPasswordTips(self)
        logShowPasswordTipsTapped()
    }
}

// MARK: - MayaRegistrationFormV2Controller Methods
extension MayaRegistrationFormV2Controller: ViewModelBindable {
    func binding(_ viewModel: MayaRegistrationFormV2ViewModelProtocol?) {
        guard let viewModel else { return }

        viewModel.validateAndFetchConsentsAction.isExecuting
            .signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                guard let self else { return }
                if isExecuting {
                    showMayaLoader()
                } else {
                    hideMayaLoader()
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.validateAndFetchConsentsAction.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self,
                      let errorViewModel = error.viewModel else { return }
                logErrorDialogViewed(errorViewModel)
                showErrorAlert(viewModel: errorViewModel, useMayaModal: true) {
                    self.logErrorDialogButtonTapped(errorViewModel)
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.validateAndFetchConsentsAction.completed
            .observe(on: UIScheduler())
            .observeValues { [weak self] in
                guard let self else { return }
                viewModel.setConsents()
                delegate?.didCompletePasswordValidationAndPolicyConsent(self)
            }?.addToDisposeBag(disposeBag)

        middleNameView.textField.reactive.continuousTextValues
            .observeValues { [weak self] text in
                let isHidden = text.count >= Constants.Registration.Field.Name.minimumChars
                UIView.animate(withDuration: 0.25) {
                    self?.noMiddleNameStackView.safeSetHidden(isHidden)
                }
            }?.addToDisposeBag(disposeBag)

        bindFieldViews()
    }
}

// MARK: - TextField Setup Methods
private extension MayaRegistrationFormV2Controller {
    func setupFieldViews() {
        firstNameView.textFieldDelegate = self
        firstNameView.titleLabel.text = L10n.Shortened.Registration.Firstname.Field.name
        firstNameView.textField.placeholder = L10n.Shortened.Registration.Firstname.Field.placeholder
        firstNameView.setTextFieldProperties(keyboardType: .asciiCapable, capitalizationType: .words, smartQuotesType: .no)

        middleNameView.textFieldDelegate = self
        middleNameView.titleLabel.text = L10n.Shortened.Registration.Middlename.Field.name
        middleNameView.textField.placeholder = L10n.Shortened.Registration.Middlename.Field.placeholder
        middleNameView.setTextFieldProperties(keyboardType: .asciiCapable, capitalizationType: .words, smartQuotesType: .no)
        noMiddleNameCheckBox.setProperties(initialStatus: false)

        lastNameView.textFieldDelegate = self
        lastNameView.titleLabel.text = L10n.Shortened.Registration.Lastname.Field.name
        lastNameView.textField.placeholder = L10n.Shortened.Registration.Lastname.Field.placeholder
        lastNameView.setTextFieldProperties(keyboardType: .asciiCapable, capitalizationType: .words, smartQuotesType: .no)

        emailView.textFieldDelegate = self
        emailView.titleLabel.text = L10n.Shortened.Registration.Email.Field.name
        emailView.setupSubtitle(L10n.Shortened.Registration.Email.Field.subtitle)
        emailView.textField.placeholder = L10n.Shortened.Registration.Email.Field.placeholder
        emailView.setTextFieldProperties(keyboardType: .emailAddress, smartQuotesType: .no)

        mobileNumberView.textFieldDelegate = self
        mobileNumberView.titleLabel.text = L10n.Shortened.Registration.Mobilenumber.Field.name
        mobileNumberView.textField.placeholder = L10n.Shortened.Registration.Mobilenumber.Field.placeholder
        mobileNumberView.setTextFieldProperties(keyboardType: .numberPad, smartQuotesType: .no, type: .fullContactNumber)
        mobileNumberView.setPhoneNumberFieldView(showFlag: true)

        passwordView.textFieldDelegate = self
        passwordView.titleLabel.text = L10n.Shortened.Registration.Password.Field.name
        passwordView.textField.placeholder = L10n.Shortened.Registration.Password.Field.placeholder
        passwordView.setPasswordFieldView()
        passwordView.passwordToggleActionHandler = { [weak self] in
            guard let self else { return }
            logShowPasswordButtonTapped(!passwordView.textField.isSecureTextEntry)
        }
    }

    func bindFieldViews() {
        guard let viewModel else { return }

        func bindFieldView(_ fieldView: MayaProfileValidationFieldView, with property: NewFieldProperty) {
            mayaFormFields.append(fieldView)
            fieldView.bindFieldProperty(property)
            property.mutableProperty <~ fieldView.textField.reactive.continuousTextValues
        }

        var mayaFormFields = [UIView]()
        bindFieldView(firstNameView, with: viewModel.firstNameField)
        bindFieldView(middleNameView, with: viewModel.middleNameField)
        bindFieldView(lastNameView, with: viewModel.lastNameField)
        bindFieldView(emailView, with: viewModel.emailAddressField)
        bindFieldView(mobileNumberView, with: viewModel.mobileNumberField)
        bindFieldView(passwordView, with: viewModel.passwordField)

        formFields = mayaFormFields
    }

    func isAllFieldsValid() -> Bool {
        var firstInvalidField: UIView?

        for field in actualFields where !field.isHidden && field.isUserInteractionEnabled {
            guard let fieldView = field as? MayaProfileValidationFieldView,
                  let fieldProperty = fieldView.fieldProperty else { continue }

            fieldView.isInitialState = false
            fieldView.smartTrim()
            fieldView.actionTriggered()

            if fieldProperty.validation.result.value.isInvalid, firstInvalidField == nil {
                firstInvalidField = fieldView
            }
        }

        if let invalidField = firstInvalidField {
            scrollView.scrollToView(view: invalidField, animated: true)
            return false
        }

        return true
    }
}

// MARK: - UITextFieldDelegate Method
extension MayaRegistrationFormV2Controller {
    func textFieldShouldEndEditing(_ textField: UITextField) -> Bool {
        guard let keyAttributes = getAnalyticsTextFieldAttributes(screenName: AnalyticsConstants.ScreenName.accountCreation,
                                                                  textField: textField)
        else { return true }
        logTextFieldInputted(keyAttributes: keyAttributes)
        return true
    }
}

// MARK: - Analytics Events Methods
private extension MayaRegistrationFormV2Controller {
    func logViewed() {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.accountCreation.viewed(),
                                       keyAttributes: [AnalyticsAttributeKey.screenName: AnalyticsConstants.ScreenName.accountCreation,
                                                       AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.landing])
    }

    func logTextFieldInputted(keyAttributes: [AnalyticsAttributeKey: Any]) {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.accountCreation.inputted(), keyAttributes: keyAttributes)
    }

    func logBackButtonTapped() {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.accountCreation.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.accountCreation,
                                                       AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.landing,
                                                       AnalyticsAttributeKey.button: AnalyticsConstants.ButtonText.back])
    }

    func logNoMiddleNameCheckBoxTapped(_ status: Bool) {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.accountCreation.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.checkbox: AnalyticsConstants.ActionFunction().status(status),
                                                       AnalyticsAttributeKey.actionFunction: AnalyticsConstants.ActionFunction().checked(status),
                                                       AnalyticsAttributeKey.optionLabel: AnalyticsConstants.ButtonText.noMiddleName])
    }

    func logShowPasswordButtonTapped(_ status: Bool) {
        let buttonText = status ? AnalyticsConstants.ButtonText.showPassword : AnalyticsConstants.ButtonText.hidePassword
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.accountCreation.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.actionFunction: AnalyticsConstants.ActionFunction().shown(status),
                                                       AnalyticsAttributeKey.button: buttonText])
    }

    func logShowPasswordTipsTapped() {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.accountCreation.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.accountCreation,
                                                       AnalyticsAttributeKey.destinationPage: AnalyticsConstants.BottomSheet.passwordTips,
                                                       AnalyticsAttributeKey.button: AnalyticsConstants.ButtonText.showPasswordTips])
    }

    func logContinueButtonTapped() {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.accountCreation.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.accountCreation,
                                                       AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.accountProvisioning,
                                                       AnalyticsAttributeKey.button: AnalyticsConstants.ButtonText.continue])
    }

    func logErrorDialogViewed(_ errorViewModel: ErrorAlertViewModel) {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.error.viewed(),
                                       keyAttributes: [AnalyticsAttributeKey.screenName: AnalyticsConstants.Dialog.registrationError,
                                                       AnalyticsAttributeKey.sourcePage: AnalyticsConstants.ScreenName.accountCreation,
                                                       AnalyticsAttributeKey.reason: errorViewModel.message,
                                                       AnalyticsAttributeKey.errorCode: errorViewModel.error?.code ?? 0])
    }

    func logErrorDialogButtonTapped(_ errorViewModel: ErrorAlertViewModel) {
        analyticsService.logMayaEvents(name: AnalyticsConstants.Screen.RegistrationV2.error.tapped(),
                                       keyAttributes: [AnalyticsAttributeKey.sourcePage: AnalyticsConstants.Dialog.registrationError,
                                                       AnalyticsAttributeKey.destinationPage: AnalyticsConstants.ScreenName.accountCreation,
                                                       AnalyticsAttributeKey.button: errorViewModel.closeButtonTitle ?? ""])
    }
}
