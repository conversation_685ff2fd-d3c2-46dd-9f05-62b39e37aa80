//
//  MayaFeatureUpdaterViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 7/3/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//
import AssetProvider

protocol MayaFeatureUpdaterViewControllerDelegate: AnyObject {
    func viewControllerDidTapLeftBarButton(_ viewController: MayaFeatureUpdaterViewController)
}

class MayaFeatureUpdaterViewController: ViewController {
    weak var delegate: MayaFeatureUpdaterViewControllerDelegate?

    // MARK: - Navigation Bar overrides
    override var shouldUseMayaNavigationBar: Bool { true }
    override var shouldUseMayaArrowBackButton: Bool { false }

    override var navigationBarBackgroundColor: UIColor {
        guard
            let style = viewModel?.style,
            let color = style.navigationBarColor
        else {
            return CommonAsset.MayaColors.Grey.grey1.color
        }

        return color
    }

    // MARK: - UI Components
    let centerImage: UIImageView = {
        let imageView = UIImageView()
        imageView.translatesAutoresizingMaskIntoConstraints = false
        return imageView
    }()

    let titleLabel: UILabel = {
        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.font = CommonFontFamily.Jeko.bold.font(size: 24)
        label.textAlignment = .center
        return label
    }()

    let subtitleLabel: UILabel = {
        let label = UILabel()
        label.font = CommonFontFamily.CerebriSansPro.book.font(size: 14)
        label.textColor = CommonAsset.MayaColors.Grey.grey6.color
        label.translatesAutoresizingMaskIntoConstraints = false
        label.textAlignment = .center
        label.numberOfLines = 2
        return label
    }()

    lazy var updateButton: MayaButton = {
        let button = MayaButton()
        button.addTarget(self, action: #selector(updateButtonTapped), for: .touchUpInside)
        button.heightAnchor.constraint(equalToConstant: 44).isActive = true
        button.buttonColor = MayaTheme.ButtonColor.primary
        button.buttonSize = MayaTheme.ButtonSize.large
        return button
    }()

    lazy var leftBarButton: UIBarButtonItem = {
        let button = UIBarButtonItem(image: CommonAsset.Images.Icons.iconBack.image.withRenderingMode(.alwaysTemplate),
                                     style: .plain,
                                     target: self,
                                     action: #selector(leftBarButtonTapped))
        button.tintColor = CommonAsset.MayaColors.Primary.primaryBlack.color
        button.imageInsets = leftBarButtonInset
        button.title = ""
        return button
    }()
}

// MARK: - ViewModelBindable
extension MayaFeatureUpdaterViewController: ViewModelBindable {
    func binding(_ viewModel: MayaFeatureUpdaterViewModelProtocol?) {
        guard let viewModel else { return }
        updateContent(viewModel.configuration)
        updateStyle(viewModel.style)
    }

    func updateContent(_ configuration: FeatureUpdaterContentConfiguration) {
        if let image = configuration.leftBarButtonImage {
            leftBarButton.image = image.withRenderingMode(.alwaysTemplate)
        }

        title = configuration.navigationBarTitle

        centerImage.image = configuration.centerImage
        titleLabel.text = configuration.title
        subtitleLabel.text = configuration.subtitle

        updateButton.setTitle(configuration.buttonTitle, for: .normal)
    }

    func updateStyle(_ style: FeatureUpdaterStyle) {
        if let tintColor = style.leftBarButtonTintColor {
            leftBarButton.tintColor = tintColor
        }

        if let backgroundColor = style.backgroundColor {
            view.backgroundColor = backgroundColor
        }

        if let style = style.titleStyle {
            titleLabel.setStyle(style)
        }

        if let style = style.subtitleStyle {
            subtitleLabel.setStyle(style)
        }
    }
}

// MARK: - View Life Cycle
extension MayaFeatureUpdaterViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        setupViews()
    }
}

// MARK: - Setup Methods
extension MayaFeatureUpdaterViewController {
    func setupViews() {
        navigationItem.leftBarButtonItem = leftBarButton
        view.backgroundColor = CommonAsset.MayaColors.Grey.grey1.color

        let textStackView = UIStackView(arrangedSubviews: [titleLabel, subtitleLabel, updateButton])
        textStackView.translatesAutoresizingMaskIntoConstraints = false
        textStackView.axis = .vertical
        textStackView.spacing = 8

        let mainStackView = UIStackView(arrangedSubviews: [centerImage, textStackView])
        mainStackView.translatesAutoresizingMaskIntoConstraints = false
        mainStackView.axis = .vertical
        mainStackView.spacing = 24

        view.addSubview(mainStackView)
        NSLayoutConstraint.activate([
            mainStackView.widthAnchor.constraint(equalTo: view.widthAnchor, multiplier: 0.74),
            mainStackView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            mainStackView.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            centerImage.heightAnchor.constraint(equalTo: mainStackView.widthAnchor)
        ])
    }
}

// MARK: - Actions
extension MayaFeatureUpdaterViewController {
    @objc func leftBarButtonTapped() {
        delegate?.viewControllerDidTapLeftBarButton(self)
    }

    @objc func updateButtonTapped() {
        guard let viewModel = viewModel else {
            return
        }
        showExternalURI(viewModel.appStoreLink)
    }
}
