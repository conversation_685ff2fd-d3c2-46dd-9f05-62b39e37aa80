//
//  MayaMaintenanceViewController.swift
//  PayMaya
//
//  Created by <PERSON> on 4/25/23.
//  Copyright © 2023 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Injector
import UIKit

class MayaMaintenanceViewController: ViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    @IBOutlet weak var containerView: UIView!
    @IBOutlet weak var backgroundView: UIView!
    @IBOutlet weak var messageTextView: UITextView!
    @IBOutlet weak var logoWidthConstraint: NSLayoutConstraint!
    @IBOutlet weak var iconWidthConstaint: NSLayoutConstraint!

    @IBOutlet weak var helpCenterCardView: MayaHelpCenterCardView! {
        didSet {
            helpCenterCardView.setProperties(web: Constants.WebView.baseHelpCenter, with: self)
        }
    }

    private var gradientLayer: CAGradientLayer?

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupLayout()
        setupBackgroundGradient()
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.Maintenance())
        super.viewWillAppear(animated)
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        analyticsService.logMayaEvents(action: .appear(Analytics.Maintenance()))
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        gradientLayer?.frame = backgroundView.bounds
    }
}

private extension MayaMaintenanceViewController {
    func setupLayout() {
        guard let containerView else { return }

        let verticalConstraint = UIScreen.isSmaller ?
            NSLayoutConstraint(item: containerView, attribute: .top, relatedBy: .equal, toItem: view, attribute: .top, multiplier: 1, constant: 40) :
            NSLayoutConstraint(item: containerView, attribute: .centerY, relatedBy: .equal, toItem: view, attribute: .centerY, multiplier: 1, constant: 0)
        let layoutConstants: (logoWidth: CGFloat, iconWidth: CGFloat) = UIScreen.isSmall ? (152, 215) : (198, 279)

        NSLayoutConstraint.activate([verticalConstraint])
        logoWidthConstraint.constant = layoutConstants.logoWidth
        iconWidthConstaint.constant = layoutConstants.iconWidth
    }

    func setupBackgroundGradient() {
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [UIColor(red: 0, green: 51, blue: 29).cgColor, CommonAsset.MayaColors.Background.backgroundPrimaryBlack.color.cgColor]
        gradientLayer.locations = [0, 0.68]
        gradientLayer.frame = backgroundView.bounds
        self.gradientLayer = gradientLayer
        backgroundView.layer.insertSublayer(gradientLayer, at: 0)
    }
}

extension MayaMaintenanceViewController: ViewModelBindable {
    func binding(_ viewModel: MayaMaintenanceViewModelProtocol?) {
        guard let viewModel = viewModel else { return }
        let paragraph = NSMutableParagraphStyle()
        paragraph.alignment = .center
        paragraph.lineSpacing = 4.0
        let attributedString = NSMutableAttributedString(attributedString: viewModel.maintenanceMessage.getHTMLAttributedString(with: CommonFontFamily.CerebriSansPro.book.font(size: 14), color: CommonAsset.MayaColors.Content.contentGrey4.color, shouldUseSystemFont: false) ?? NSAttributedString(string: ""))
        attributedString.addAttribute(.paragraphStyle, value: paragraph, range: NSMakeRange(0, attributedString.length))
        messageTextView.attributedText = attributedString
        messageTextView.linkTextAttributes = [.foregroundColor: CommonAsset.MayaColors.Content.contentPrimaryGreen.color, .underlineStyle: 0, .underlineColor: UIColor.clear]
    }
}

extension MayaMaintenanceViewController: UITextViewDelegate {
    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        guard UIApplication.shared.canOpenURL(URL) else {
            return false
        }
        analyticsService.logMayaEvents(action: .tap(Analytics.Maintenance.link), keyAttributes: [AnalyticsAttributeKey.url: URL.absoluteString.prefix(100)])
        UIApplication.shared.open(URL)
        return false
    }
}

extension MayaMaintenanceViewController: MayaHelpCenterCardViewDelegate {
    func didRequestWebView(_ helpCenterCardView: MayaHelpCenterCardView, with url: URL) {
        analyticsService.logMayaEvents(action: .tap(Analytics.Maintenance.help))
        routeActionDelegate?.didRequestWebView(self, with: url, willPresent: true, completion: nil)
    }
}
