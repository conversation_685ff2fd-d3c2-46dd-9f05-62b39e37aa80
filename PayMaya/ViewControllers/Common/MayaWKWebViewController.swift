//
//  MayaWKWebViewController.swift
//  PayMaya
//
//  Created by <PERSON> on 11/24/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Injector
import ReactiveCocoa
import ReactiveSwift
import UIKit
import WebKit

protocol MayaWKWebViewControllerDelegate: AnyObject {
    func didFinishWebView(_ viewController: ViewController, isSuccessful: Bool, response: Codable?)

    /// Remove on cleanup of 1.5 design
    func didCancelWebView(_ viewController: ViewController)
}

protocol WebViewBackDelegate: AnyObject {
    func didTapBackToConfirm(_ viewController: ViewController)
}

protocol WebViewPWPDelegate: AnyObject {
    func didRequestPWPFlow(_ viewController: ViewController, merchantPaymentId: String)
}

protocol WebViewShopProductDelegate: AnyObject {
    func didRequestShopFlow(_ viewController: ViewController, productID: String, prefilledFields: [String: String]?)
}

class MayaWKWebViewController: ViewController, MayaAnalyticsDurationProtocol {
    @Inject private var orientationController: OrientationController

    @IBOutlet weak var closeButton: UIButton!
    @IBOutlet weak var backButton: UIButton!
    @IBOutlet weak var containerView: UIView!
    @IBOutlet weak var forwardButton: UIButton!
    @IBOutlet weak var progressView: MayaWebViewProgressBar!
    @IBOutlet weak var reloadButton: UIButton!
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var appBarView: UIView!
    @IBOutlet weak var errorView: MayaDefaultErrorView! {
        didSet {
            errorView.delegate = self
        }
    }

    var webView: WKWebView!

    weak var delegate: MayaWKWebViewControllerDelegate?
    weak var backDelegate: WebViewBackDelegate?
    weak var pwpDelegate: WebViewPWPDelegate?
    weak var shopDelegate: WebViewShopProductDelegate?

    private lazy var supportWebviewButton = MayaInboxSupportWebviewButton()

    // Used in MayaErrorAlert's action button's completion handling
    var dismissCompletion: (() -> Void)?

    // Needed even though this view controller does not have a typical navigation bar.
    // This is to fix the blue navigation bar momentarily showing up when view controller is pushed.
    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        guard let viewModel = viewModel else { return .portrait }

        return viewModel.webViewConfiguration.allowsLandscape ? .all : .portrait
    }

    private var failingURLRequest: URLRequest?

    override func didTapBack(_ sender: Any) {
        guard let viewModel = viewModel,
                viewModel.shouldShowBackConfirmation
        else {
            super.didTapBack(sender)
            dismissCompletion?()

            return
        }

        backDelegate?.didTapBackToConfirm(self)
    }

    override func viewDidLoad() {
        super.viewDidLoad()

        /// WKWebView is done programmatically since WKWebViewConfiguration should be passed during WKWebView's initialization
        let webConfiguration = WKWebViewConfiguration()
        if let viewModel = viewModel {
            webConfiguration.allowsInlineMediaPlayback = viewModel.webViewConfiguration.allowsInlineMediaPlayback
            updateAppearance(viewModel.webViewConfiguration)
        }
        webView = WKWebView(frame: view.bounds, configuration: webConfiguration)
        webView.translatesAutoresizingMaskIntoConstraints = false
        webView.isAccessibilityElement = true
        webView.accessibilityIdentifier = "pmios_maya_web_view"
        webView.uiDelegate = self
        containerView.insertSubview(webView, at: 0)
        NSLayoutConstraint.enclose(view: webView, inside: containerView)

        backButton.reactive.isEnabled <~ webView.reactive.producer(for: \.canGoBack)
        forwardButton.reactive.isEnabled <~ webView.reactive.producer(for: \.canGoForward)

        backButton.reactive.controlEvents(.touchUpInside)
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let webView = self?.webView else { return }

                webView.goBack()
            }?.addToDisposeBag(disposeBag)

        forwardButton.reactive.controlEvents(.touchUpInside)
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let webView = self?.webView else { return }

                webView.goForward()
            }?.addToDisposeBag(disposeBag)

        reloadButton.reactive.controlEvents(.touchUpInside)
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let webView = self?.webView else { return }

                webView.reload()
            }?.addToDisposeBag(disposeBag)

        setupExternalLinkPrompt()
    }

    override func viewWillAppear(_ animated: Bool) {
        if let viewModel = viewModel {
            setModule(viewModel.analyticsModule)
        }

        super.viewWillAppear(animated)

        guard let viewModel = viewModel else { return }

        if let url = URLRequest(url: viewModel.link).url?.absoluteString {
           setAttributes([.link: url])
           applyMayaSupportButtons()
        }

        allowSupportedOrientations()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        viewModel?.triggerInitialSessionRefresh()
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        lockToPortraitOrientation()

        if isMovingFromParent {
            if webView.isLoading {
                webView.stopLoading()
            }
            webView.navigationDelegate = nil
            webView.loadHTMLString("", baseURL: nil)
        }

        viewModel?.disableSessionRefreshIntervals()
    }

    override func setupAccessibilityIdentifiers() {
        backButton.isAccessibilityElement = true
        backButton.accessibilityIdentifier = "pmios_maya_web_view_back_button"
    }
}

// MARK: - Private Methods
fileprivate extension MayaWKWebViewController {
    func setupExternalLinkPrompt() {
        guard let viewModel = viewModel, viewModel.checkURLForExternalLinkPrompt() else { return }

        dispatchToMain(withDelay: .now() + 0.6) {
            let view = MayaBottomSnackbarView(message: CommonStrings.External.Link.Prompt.message, image: CommonAsset.Images.Common.imageBinoculars.image)
            view.show(in: self.view)
        }
    }

    func reload() {
        if let request = failingURLRequest,
           let url = request.url,
           !url.absoluteString.isEmpty {
            progressView.signal()
            webView.load(request)
        }
    }

    func showErrorView(show: Bool, animated: Bool) {
        if show {
            lockToPortraitOrientation()
        } else {
            allowSupportedOrientations()
        }

        if animated {
            UIView.animate(withDuration: 0.3, animations: {
                self.errorView.isHidden = !show
            })
        } else {
            errorView.isHidden = !show
        }
    }

    func allowSupportedOrientations() {
        if let viewModel = viewModel,
            viewModel.webViewConfiguration.allowsLandscape {
            orientationController.lockOrientation(supportedInterfaceOrientations)
        }
    }

    func lockToPortraitOrientation() {
        if let viewModel = viewModel,
            viewModel.webViewConfiguration.allowsLandscape {
            orientationController.resetToDefaultOrientation()
        }
    }
}

// MARK: - MayaDefaultErrorViewDelegate Methods
extension MayaWKWebViewController: MayaDefaultErrorViewDelegate {
    func didTapRetry(_ view: MayaDefaultErrorView) {
        guard let viewModel = viewModel else { return }

        viewModel.updateRefreshSessionState(.retry)

        if case .error(.noInternetConnection) = viewModel.refreshSessionState.value {
            // Do nothing
        } else {
            reload()
        }
    }
}

// MARK: - ViewModelBindable Methods
extension MayaWKWebViewController: ViewModelBindable {
    func binding(_ viewModel: WebViewViewModelProtocol?) {
        guard let viewModel = viewModel else { return }

        let request = URLRequest(url: viewModel.link)
        webView.navigationDelegate = self
        webView.load(request)

        if let title = viewModel.title {
            titleLabel.text = title
        }

        viewModel.refreshSessionState.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] viewState in
                guard let self = self else { return }
                switch viewState {
                case .error(.noInternetConnection):
                    self.showErrorView(show: true, animated: false)
                case .error(.failedSessionRefresh), .error(.default):
                    self.showErrorView(show: true, animated: true)
                case .loadingStarted:
                    self.showErrorView(show: false, animated: false)
                case .completed:
                    self.showErrorView(show: false, animated: false)
                case .none:
                    self.showErrorView(show: false, animated: false)
                    self.progressView.signal()
                }
            }?.addToDisposeBag(disposeBag)
    }
}

// MARK: - WKNavigationDelegate Methods
extension MayaWKWebViewController: WKNavigationDelegate {
    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        func setAttribution(for url: URL) {
            guard
                viewModel is MayaMallWebViewViewModel,
                let analyticsAction = AnalyticsHelper.getAnalyticsAction(for: url)
            else { return }
            analyticsHelper.setAttribution(source: .mall, for: analyticsAction)
        }

        guard let viewModel = viewModel else {
            decisionHandler(.allow)
            return
        }

        if let requestUrl = navigationAction.request.url {
            viewModel.setResultStatus(from: requestUrl)
        }

        switch viewModel.module {
        case ._3ds:
            let status = viewModel.resultStatus
            if status != .processing {
                if status == .success {
                    analyticsUtils.logMayaAttributableEvent(.success(viewModel.analyticsModule), attributes: viewModel.getAnalyticsAttributes(for: status))
                } else {
                    analyticsUtils.logMayaEvents(.failure(viewModel.analyticsModule), attributes: viewModel.getAnalyticsAttributes(for: status))
                }

                delegate?.didFinishWebView(self, isSuccessful: viewModel.resultStatus == .success, response: viewModel.response)
                decisionHandler(.cancel)
            } else {
                decisionHandler(.allow)
            }
        case .normal:
            if let url = webView.url,
               viewModel.deepLinkRouter.isPWPLink(url) {
                let merchantPaymentId = URLComponents(url: url, resolvingAgainstBaseURL: true)?.queryItems?.filter { $0.name == "id" }.first
                if let id = merchantPaymentId?.value {
                    setAttribution(for: url)
                    decisionHandler(.cancel)
                    pwpDelegate?.didRequestPWPFlow(self, merchantPaymentId: id)
                } else {
                    decisionHandler(.allow)
                }
            } else if let url = navigationAction.request.url,
                      let params = viewModel.deepLinkRouter.isShopDirectURL(url) {
                setAttribution(for: url)
                decisionHandler(.cancel)
                shopDelegate?.didRequestShopFlow(self, productID: params.productId, prefilledFields: params.prefilledFields)
            } else if let url = navigationAction.request.url,
                      viewModel.deepLinkRouter.canOpenUrl(url) {
                setAttribution(for: url)
                routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: { canOpen in
                    canOpen ? decisionHandler(.cancel) : decisionHandler(.allow)
                })
            /// Handle firebase dynamic links. We fire a manual URLSession task so that the webview will not navigate to the firebase dynamic link and to ensure that the user goes back to the same page/url after exiting the new flow
            } else if let url = navigationAction.request.url,
                      let host = url.host,
                      host == Constants.DynamicLinks.link.rawValue {
                showMayaLoader()
                let task = URLSession.shared.dataTask(with: url) { _, response, _ in
                    DispatchQueue.main.async {
                        self.hideMayaLoader()
                        guard let url = response?.url else {
                            decisionHandler(.allow)
                            return
                        }

                        setAttribution(for: url)
                        if let params = viewModel.deepLinkRouter.isShopDirectURL(url) {
                            decisionHandler(.cancel)
                            self.shopDelegate?.didRequestShopFlow(self, productID: params.productId, prefilledFields: params.prefilledFields)
                        } else if viewModel.deepLinkRouter.canOpenUrl(url) {
                            self.routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: { canOpen in
                                canOpen ? decisionHandler(.cancel) : decisionHandler(.allow)
                            })
                        }
                    }
                }
                task.resume()
            } else if let url = navigationAction.request.url,
                      let scheme = url.scheme,
                      Bundle.externalURLSchemes.contains(scheme),
                      url.rootDomain == "google",
                      let queryItems = URLComponents(url: url, resolvingAgainstBaseURL: true)?.queryItems,
                      let deepLinkString = queryItems.first(where: { return $0.name == "deep_link_id" })?.value?.removingPercentEncoding,
                      let deepLinkURL = URL(string: deepLinkString) {
                setAttribution(for: deepLinkURL)
                routeActionDelegate?.didRequestStartDeepLink(self, with: deepLinkURL, completion: { canOpen in
                    canOpen ? decisionHandler(.cancel) : decisionHandler(.allow)
                })
            } else if let url = URL(string: navigationAction.request.url?.absoluteString ?? ""),
                    let queryItems = URLComponents(url: url, resolvingAgainstBaseURL: true)?.queryItems,
                    let deepLinkString = queryItems.first(where: { return $0.name == "link" })?.value?.removingPercentEncoding,
                    let deepLinkURL = URL(string: deepLinkString),
                    url.rootDomain == "page.link" {
                setAttribution(for: deepLinkURL)
                routeActionDelegate?.didRequestStartDeepLink(self, with: deepLinkURL, completion: { canOpen in
                    canOpen ? decisionHandler(.cancel) : decisionHandler(.allow)
                })
            } else {
                decisionHandler(.allow)
            }
        }
    }

    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        progressView.wait()
    }

    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        guard let viewModel = self.viewModel else { return }
        viewModel.updateRefreshSessionState(.completed)
        progressView.signal()

        if viewModel.title == nil,
           viewModel.willUseDefaultTitle,
           let title = webView.title {
            self.title = title
        }

        if viewModel.resultStatus == .success {
            delegate?.didFinishWebView(self, isSuccessful: true, response: nil)
        }
    }

    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Swift.Error) {
        webViewDidFail(with: error)
    }

    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        webViewDidFail(with: error)
    }

    /// Error code 102 is `Frame Load Interrupted` error caused by pwp redirect
    private func webViewDidFail(with error: Error) {
        progressView.signal()

        guard error._code != NSURLErrorCancelled,
              error._code != 102
        else { return }

        if let info = error._userInfo as? [String: Any],
           let url = info["NSErrorFailingURLKey"] as? URL {
            failingURLRequest = URLRequest(url: url)
        } else if let failedURL = webView.url {
            failingURLRequest = URLRequest(url: failedURL)
        } else if let viewModel = viewModel {
            failingURLRequest = URLRequest(url: viewModel.link)
        }

        viewModel?.updateRefreshSessionState(.failed)
    }
}

// MARK: - WKUIDelegate Methods
extension MayaWKWebViewController: WKUIDelegate {
    func webView(_ webView: WKWebView, createWebViewWith configuration: WKWebViewConfiguration, for navigationAction: WKNavigationAction, windowFeatures: WKWindowFeatures) -> WKWebView? {
        /// To load links with target=_blank
        if navigationAction.targetFrame == nil {
            webView.load(navigationAction.request)
        }

        return nil
    }
}

// MARK: - Inbox Methods
extension MayaWKWebViewController {
    private func applyMayaSupportButtons() {
        guard let viewModel = viewModel as? WebViewViewModel else { return }
        if viewModel.isSupportBaseUrl() {
            hideNavigationButtons(true)
            setupSupportButtons()
            setViewData()
        } else {
            supportWebviewButton.removeFromSuperview()
            hideNavigationButtons(false)
        }
    }

    private func hideNavigationButtons(_ isHidden: Bool) {
        forwardButton.isHidden = isHidden
        backButton.isHidden = isHidden
        reloadButton.isHidden = isHidden
    }

    private func setupSupportButtons() {
        appBarView.addSubview(supportWebviewButton)
        supportWebviewButton.delegate = self
        NSLayoutConstraint.activate([
            supportWebviewButton.centerYAnchor.constraint(equalTo: appBarView.centerYAnchor),
            supportWebviewButton.trailingAnchor.constraint(equalTo: appBarView.trailingAnchor, constant: -24),
            supportWebviewButton.heightAnchor.constraint(equalToConstant: 28)
        ])
        view.layoutIfNeeded()
        supportWebviewButton.clipsToBounds = false
    }

    private func setViewData() {
        guard let viewModel = viewModel as? WebViewViewModel else { return }
        let state = viewModel.fetchInboxWebViewDataState()

        supportWebviewButton.state = state
    }
}

extension MayaWKWebViewController: MayaInboxSupportWebviewButtonDelegate {
    func didTapHistory(_ view: MayaInboxSupportWebviewButton) {
        guard let supportUrl = Constants.DeepLinkPath.chatWithUs.urlWithRoute(.history) else { return }

        routeActionDelegate?.didRequestStartDeepLink(self, with: supportUrl, completion: nil)
    }

    func didTapResume(_ view: MayaInboxSupportWebviewButton) {
        guard let supportUrl = Constants.DeepLinkPath.chatWithUs.urlWithRoute(.support) else { return }

        routeActionDelegate?.didRequestStartDeepLink(self, with: supportUrl, completion: nil)
    }
}

// MARK: - App Bar Appearance
fileprivate extension MayaWKWebViewController {
    func updateAppBarIconsTintColor(_ tintColor: UIColor) {
        /// Converted rendering type to template to apply tintColor on Icons.
        let closeImage = CommonAsset.Images.Icons.iconSystemCross.image.withRenderingMode(.alwaysTemplate)
        closeButton.setImage(closeImage, for: .normal)
        closeButton.tintColor = tintColor

        let backImage = CommonAsset.Images.Icons.iconSystemChevronLeft.image.withRenderingMode(.alwaysTemplate)
        backButton.setImage(backImage, for: .normal)
        backButton.tintColor = tintColor

        let forwardImage = CommonAsset.Images.Icons.iconSystemChevronRight.image.withRenderingMode(.alwaysTemplate)
        forwardButton.setImage(forwardImage, for: .normal)
        forwardButton.tintColor = tintColor

        let reloadImage = CommonAsset.Images.Icons.iconOtherReload.image.withRenderingMode(.alwaysTemplate)
        reloadButton.setImage(reloadImage, for: .normal)
        reloadButton.tintColor = tintColor
    }

    func updateAppBarAppearance(_ configuration: AppBarConfiguration?) {
        guard let config = configuration else {
            return
        }

        if let color = config.color {
            appBarView.backgroundColor = color
        }

        if let iconColor = config.iconColor {
            updateAppBarIconsTintColor(iconColor)
        }
    }

    func updateAppearance(_ configuration: WebViewConfiguration?) {
        guard let config = configuration else {
            return
        }

        if let newStatusBarStyle = config.statusBarStyle {
            statusBarStyle = newStatusBarStyle
        }

        if let backgroundColor = config.backgroundColor {
            view.backgroundColor = backgroundColor
        }

        updateAppBarAppearance(config.appBarConfiguration)
    }
}
