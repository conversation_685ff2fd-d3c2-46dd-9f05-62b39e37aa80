//
//  MayaQRScannerViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 24/03/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import AVFoundation
import ConfigurationService
import Error
import Injector
import ReactiveSwift
import UIKit

#if canImport(Vision)
import Vision
#endif

protocol MayaQRScannerViewControllerDelegate: AnyObject {
    func didScanMerchantQr(_ viewController: MayaQRScannerViewController, merchant: MerchantQR)
    func didScanSendMoneyQr(_ viewController: MayaQRScannerViewController, transfer: SendMoneyQR)
    func didScanDynamicMerchantQrOldFlow(_ viewController: MayaQRScannerViewController, dynamicMerchant: DynamicMerchantQR)
    func didScanSendMoneyToBankQr(_ viewController: MayaQRScannerViewController, bankTransfer: BankTransferQRProtocol)
    func didScanOffUsMerchantQrOldFlow(_ viewController: MayaQRScannerViewController, offUsMerchant: OffUSQRPHMerchant)
    func didScanQRPHMerchantQr(_ viewController: MayaQRScannerViewController, qrphMerchant: QRPHMerchant)
    func didCreateMerchantPayment(_ viewController: MayaQRScannerViewController, createdMerchantPayment: CreatedMerchantPaymentV2)
    func didTapShowMyQR(_ viewController: MayaQRScannerViewController)
    func showMaintenanceModeOffUsMerchantQR()
}

class MayaQRScannerViewController: ViewController, AnalyticsProtocol, MayaAnalyticsDurationManagerProtocol, CodeDetectionProtocol {
    @Inject private var configurationService: ConfigurationService
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    @IBOutlet weak var cameraScanView: RecordPreviewView!
    @IBOutlet weak var errorInstructionLabel: UILabel!

    @IBOutlet weak var arrowBackButton: UIButton!
    @IBOutlet weak var scannerFrameView: UIView!

    @IBOutlet weak var bankLogosBottomConstraint: NSLayoutConstraint!
    @IBOutlet weak var bankLogosHeightConstraint: NSLayoutConstraint!
    @IBOutlet weak var scannerViewHeightConstraint: NSLayoutConstraint!
    @IBOutlet weak var scannerViewHeightConstraintForIphoneSE: NSLayoutConstraint!
    @IBOutlet weak var showQRButton: MayaButton!
    @IBOutlet weak var uploadQRButton: MayaButton!
    @IBOutlet weak var bankLogosStackView: UIStackView!

    var analyticsSourcePage: AnalyticsConstants.SourcePage?

    private lazy var imagePickerController: UIImagePickerController = {
        let imagePickerController = UIImagePickerController()
        imagePickerController.sourceType = .photoLibrary
        imagePickerController.setModalPresentationStyle()
        imagePickerController.delegate = self
        return imagePickerController
    }()

    weak var delegate: MayaQRScannerViewControllerDelegate?

    private let sessionQueue = DispatchQueue(label: "sessionqueue")
    private let videoBufferCallbackQueue = DispatchQueue(label: "videoBufferCallbackQueue")
    private lazy var captureSession = AVCaptureSession()
    private lazy var metadataOutput = AVCaptureMetadataOutput()
    private lazy var videoDataOutput = AVCaptureVideoDataOutput()

    private var isDynamicQr: Bool?
    private var hasScannedResult = false

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    var importQRAnalyticsModule: AnalyticsModule {
        return Analytics.NewScanQr.upload
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setCameraConfiguration()
        setupView()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        startCameraCapture()
        analyticsUtils.logAttributableEvent(.appear(Analytics.Scan()))
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        logViewed()
        stopCameraCapture()
    }

    override func didTapBack(_ sender: Any) {
        if configurationService.ecommAppEventV2QrEnabled.value {
            analyticsService.logMayaEvents(
                AnalyticsConstants.Screen.Qr.action.tapped(),
                attributes: [ .button: CommonStrings.Common.back ]
            )
        }

        super.didTapBack(sender)
    }

    @IBAction func didTapImportQrFromPhotos(_ sender: Any) {
        if configurationService.ecommAppEventV2QrEnabled.value,
           let uploadButtonText = uploadQRButton.titleLabel?.text {
            analyticsService.logMayaEvents(
                AnalyticsConstants.Screen.Qr.action.tapped(),
                attributes: [ .button: uploadButtonText ]
            )
        } else {
            analyticsUtils.logAllEvents(.tap(importQRAnalyticsModule))
        }

        stopCameraCapture()
        present(imagePickerController, animated: true)
    }

    @IBAction func showMyQRTapped(sender: Any) {
        if configurationService.ecommAppEventV2QrEnabled.value,
           let showQrButtonText = showQRButton.titleLabel?.text {
            analyticsService.logMayaEvents(
                AnalyticsConstants.Screen.Qr.action.tapped(),
                attributes: [ .button: showQrButtonText ]
            )
        } else {
            analyticsUtils.logMayaEvents(.tap(Analytics.NewScanQr.show))
        }

        delegate?.didTapShowMyQR(self)
    }
}

// MARK: ViewModelBindable Methods
extension MayaQRScannerViewController: ViewModelBindable {
    func binding(_ viewModel: QRScannerViewModelProtocol?) {
        guard let viewModel = viewModel else { return }

        appDidEnterBackgroundSignal.signal.observe(on: UIScheduler()).observeValues { [weak self] in
            guard let self = self else { return }
            self.logViewed()
        }

        if let classifyCodeAction = viewModel.classifyCodeAction {
            classifyCodeAction.values
                .observe(on: UIScheduler())
                .observeValues { [weak self] codeOutput in
                    guard let self else { return }

                    let scannedQR = codeOutput.detectedCode

                    self.isDynamicQr = codeOutput.isDynamicQr

                    if let qr = scannedQR as? SendMoneyQR {
                        self.delegate?.didScanSendMoneyQr(self, transfer: qr)
                    } else if let qr = scannedQR as? BankTransferQRProtocol {
                        self.delegate?.didScanSendMoneyToBankQr(self, bankTransfer: qr)
                    } else if let qr = scannedQR as? OffUSQRPHMerchant {
                        self.delegate?.didScanOffUsMerchantQrOldFlow(self, offUsMerchant: qr)
                    } else if let qr = scannedQR as? QRPHMerchant {
                        if !qr.isOnUsQr && !viewModel.isQROffUsAvailable {
                            self.delegate?.showMaintenanceModeOffUsMerchantQR()
                            return
                        }

                        if qr.pointOfInitiation == .dynamic {
                            self.viewModel?.createQRPHDynamicMerchantPaymentAction?.apply(qr).start()
                        } else {
                            self.delegate?.didScanQRPHMerchantQr(self, qrphMerchant: qr)
                        }
                    }

                    logScanSuccess(codeOutput: codeOutput)
                }?.addToDisposeBag(disposeBag)

            classifyCodeAction.errors
                .observe(on: UIScheduler())
                .observeValues { [weak self] error in
                    guard let self else { return }

                    switch error.type {
                    case .invalidScanP2B,
                         .invalidScan:

                        logScanError(error: error)
                        self.errorInstructionLabel.text = error.type == .invalidScanP2B ? L10n.Maya.Scanner.Invalid.bills : L10n.Maya.Scanner.Invalid.qr
                        self.errorInstructionLabel.isHidden = false
                        self.scannerFrameView.layer.borderColor = CommonAsset.MayaColors.System.systemError.color.cgColor

                        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0, execute: {
                            self.startCameraCapture()
                            self.errorInstructionLabel.isHidden = true
                            self.errorInstructionLabel.text = L10n.Maya.Scanner.Invalid.qr
                            self.scannerFrameView.layer.borderColor = UIColor.clear.cgColor
                        })
                    case .invalidImport:
                        logScanError(error: error)
                        if let errorViewModel = error.viewModel {
                            self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
                            self.startCameraCapture()
                        }
                    default:
                        return
                    }
                }?.addToDisposeBag(disposeBag)
        }

        if let createQRPHDynamicMerchantPaymentAction = viewModel.createQRPHDynamicMerchantPaymentAction {
            createQRPHDynamicMerchantPaymentAction.isExecuting.signal
                .observe(on: UIScheduler())
                .observeValues { [weak self] executing in
                    guard let self else { return }

                    if executing {
                        self.showMayaLoader()
                    } else {
                        self.hideMayaLoader()
                    }
                }?.addToDisposeBag(disposeBag)

            createQRPHDynamicMerchantPaymentAction.values
                .observe(on: UIScheduler())
                .observeValues { [weak self] createdMerchantPaymentV2 in
                    guard let self else { return }

                    self.logCreateMerchantPaymentSuccess()
                    if viewModel.isQrWithCheckoutFlowEnabled {
                        self.delegate?.didCreateMerchantPayment(self, createdMerchantPayment: createdMerchantPaymentV2)
                    }
                }?.addToDisposeBag(disposeBag)

            createQRPHDynamicMerchantPaymentAction.errors
                .observe(on: UIScheduler())
                .observeValues { [weak self] error in
                    guard let self, let errorViewModel = error.viewModel else { return }

                    self.logCreateMerchantPaymentError(error: error)
                    if case .sessionTimeout = error.type {
                        return
                    } else {
                        self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true) {
                            self.startCameraCapture()
                        }
                    }
                }?.addToDisposeBag(disposeBag)
        }

        if let restartCameraScanAction = viewModel.restartCameraScanAction {
            restartCameraScanAction.values
                .observe(on: UIScheduler())
                .observeValues { [weak self] _ in
                    guard let self else { return }
                    self.startCameraCapture()
                }?.addToDisposeBag(disposeBag)
        }
    }
}

// MARK: Utility Methods
private extension MayaQRScannerViewController {
    func setupView() {
        errorInstructionLabel?.isHidden = true
        scannerFrameView.layer.borderWidth = 2
        scannerFrameView.layer.borderColor = UIColor.clear.cgColor

        if UIScreen.isSmaller {
            bankLogosBottomConstraint?.constant = 10
            bankLogosHeightConstraint.constant = 30
            showQRButton.imageEdgeInsets = UIEdgeInsets(top: 0, left: -7, bottom: 0, right: 0)
            uploadQRButton.imageEdgeInsets = UIEdgeInsets(top: 0, left: -7, bottom: 0, right: 0)
            bankLogosStackView.distribution = .fillProportionally
            scannerViewHeightConstraint.isActive = false
            scannerViewHeightConstraintForIphoneSE.isActive = true
            scannerViewHeightConstraintForIphoneSE.priority = UILayoutPriority.required
        } else {
            scannerViewHeightConstraint.isActive = true
            scannerViewHeightConstraintForIphoneSE.isActive = false
        }
    }

    func startCameraCapture() {
        hasScannedResult = false
        self.sessionQueue.async { [weak self] in
            guard let self = self else { return }
            if !self.captureSession.isRunning {
                self.captureSession.startRunning()
            }
        }
    }

    func stopCameraCapture() {
        self.sessionQueue.async { [weak self] in
            guard let self = self else { return }
            if self.captureSession.isRunning {
                self.captureSession.stopRunning()
            }
        }
    }

    func logViewed() {
        if configurationService.ecommAppEventV2QrEnabled.value,
           let analyticsSourcePage = analyticsSourcePage {
            analyticsService.logMayaEvents(
                AnalyticsConstants.Screen.Qr.scanPage.viewed(),
                attributes: [ .sourcePage: analyticsSourcePage.rawValue ]
            )
        }
    }

    func logScanSuccess(codeOutput: CodeDetectionOutput) {
        // V1 Data
        var type = ""

        // V2 Data
        var qrType: AnalyticsConstants.Screen.Qr.QrType?
        var merchantName: String?

        let scannedQR = codeOutput.detectedCode

        if scannedQR is SendMoneyQR {
            type = "SEND_MONEY"
        } else if scannedQR is BankTransferQRProtocol {
            type = "SEND_MONEY_INSTAPAY_QR_BANK"
        } else if let qr = scannedQR as? OffUSQRPHMerchant {
            type = qr.pointOfInitiation == .static ? "P2M" : "DYNAMIC_P2M"
        } else if let qr = scannedQR as? QRPHMerchant {
            type = qr.pointOfInitiation == .static ? "P2M" : "DYNAMIC_P2M"

            qrType = qr.analyticsQrType
            merchantName = qr.name
        }

        if configurationService.ecommAppEventV2QrEnabled.value {
            if let qrClass = scannedQR.analyticsQrClass {
                analyticsService.logMayaEvents(
                    AnalyticsConstants.Screen.Qr.code.scanned(),
                    attributes: [
                        .merchantName: merchantName,
                        .merchantType: scannedQR.analyticsMerchantType?.rawValue,
                        .qrType: qrType?.rawValue,
                        .qrClass: qrClass.rawValue
                    ].discardNil()
                )
            }
        } else {
            let analyticsModule: AnalyticsModule = (codeOutput.source == .photo) ? Analytics.ScanQr.gallery : Analytics.Dashboard.scan
            self.analyticsUtils.logAllEvents(.success(analyticsModule), attributes: [.type: type])
        }
    }

    func logScanError(error: PayMayaError) {
        guard let viewModel = viewModel else { return }

        // V1 Data
        var analyticsV1Log: (() -> Void)?

        // V2 Data
        var analyticsV2Log: (() -> Void)?

        switch error.type {
        case .invalidScan:
            if let errorViewModel = error.viewModel {
                let errorMessage = errorViewModel.message
                analyticsV1Log = {
                    self.analyticsUtils.logAllEvents(.failure(Analytics.Dashboard.scan), attributes: [.reason: errorMessage])
                }

                analyticsV2Log = {
                     var attributes: [AnalyticsAttributeKey: Any] = [
                         .page: AnalyticsConstants.Screen.Qr.Page.qrScan.rawValue,
                         .reason: errorMessage
                     ]

                     let decoder = JSONDecoder()

                    if let data = error.data, let validationError = try? decoder.decode(QRError.self, from: data) {
                        attributes[.qrErrorCode] = QRPHConstants.invalidQR
                         switch validationError {
                         case .invalidValue(let value, let field, let bic),
                              .invalidLength(let value, let field, let bic):
                             attributes[.qrErrorReason] = validationError.caseName
                             attributes[.qrFieldValue] = value
                             attributes[.qrFieldName] = field.name
                             attributes[.qrFieldTag] = field.fullTag()
                             attributes[.receiverBIC] = bic
                         case  .missingRequiredField(let field, let bic):
                             attributes[.qrErrorReason] = validationError.caseName
                             attributes[.qrFieldName] = field.name
                             attributes[.qrFieldTag] = field.fullTag()
                             attributes[.receiverBIC] = bic
                         case .parsingError(let field):
                             attributes[.qrErrorReason] = validationError.caseName
                             attributes[.qrFieldName] = field.name
                             attributes[.qrFieldTag] = field.fullTag()
                         case .tamperedQR(let bic):
                             attributes[.qrErrorReason] = validationError.caseName
                             attributes[.receiverBIC] = bic
                         default: break
                         }
                     }

                    self.analyticsService.logMayaEvents(
                        AnalyticsConstants.Screen.Qr.scan.errorScreen(),
                        attributes: attributes
                    )
                }
            }
        case .invalidImport:
            analyticsV1Log = {
                self.analyticsUtils.logAllEvents(.failure(Analytics.ScanQr.gallery), attributes: [.reason: L10n.Import.Detection.Failed.reason])
            }

            analyticsV2Log = {
                var attributes: [AnalyticsAttributeKey: Any] = [
                   .page: AnalyticsConstants.Screen.Qr.Page.qrScan.rawValue,
                   .reason: L10n.Import.Detection.Failed.reason
                ]

                let decoder = JSONDecoder()

                if let data = error.data, let validationError = try? decoder.decode(QRError.self, from: data) {
                    attributes[.qrErrorCode] = QRPHConstants.invalidQR

                    switch validationError {
                    case .invalidValue(let value, let field, let bic),
                        .invalidLength(let value, let field, let bic):
                        attributes[.qrErrorReason] = validationError.caseName
                        attributes[.qrFieldValue] = value
                        attributes[.qrFieldName] = field.name
                        attributes[.qrFieldTag] = field.fullTag()
                        attributes[.receiverBIC] = bic
                    case  .missingRequiredField(let field, let bic):
                        attributes[.qrErrorReason] = validationError.caseName
                        attributes[.qrFieldName] = field.name
                        attributes[.qrFieldTag] = field.fullTag()
                        attributes[.receiverBIC] = bic
                    case .parsingError(let field):
                        attributes[.qrErrorReason] = validationError.caseName
                        attributes[.qrFieldName] = field.name
                        attributes[.qrFieldTag] = field.fullTag()
                    case .tamperedQR(let bic):
                        attributes[.qrErrorReason] = validationError.caseName
                        attributes[.receiverBIC] = bic
                    default: break
                    }
                }

               self.analyticsService.logMayaEvents(
                   AnalyticsConstants.Screen.Qr.scan.errorScreen(),
                   attributes: attributes
               )
            }
        default:
            return
        }

        if viewModel.isEcommAppEventV2QrEnabled {
            analyticsV2Log?()
        } else {
            analyticsV1Log?()
        }
    }

    func logCreateMerchantPaymentSuccess() {
        guard let isDynamicQr = self.isDynamicQr else { return }

        if isDynamicQr {
            analyticsUtils.logMayaEvents(.success(Analytics.DynamicP2M.create))
        } else {
            analyticsUtils.logMayaEvents(.success(Analytics.P2M.create))
        }
    }

    func logCreateMerchantPaymentError(error: PayMayaError) {
        guard let viewModel = viewModel,
                let errorViewModel = error.viewModel
        else { return }

        let message = {
            if case .sessionTimeout(let message) = error.type {
                return message
            } else {
                return errorViewModel.message
            }
        }()

        if error.type != .validation {
            if viewModel.isEcommAppEventV2QrEnabled {
                analyticsService.logMayaEvents(
                    AnalyticsConstants.Screen.Qr.scan.errorScreen(),
                    attributes: [
                        .errorCode: errorViewModel.error?.code ?? 0,
                        .errorReason: message,
                        .qrType: AnalyticsConstants.Screen.Qr.QrType.dynamic.rawValue,
                        .qrClass: AnalyticsConstants.Screen.Qr.QrClass.p2m.rawValue,
                        .page: AnalyticsConstants.Screen.Qr.Page.qrScan.rawValue
                    ]
                )
            } else {
                guard let isDynamicQr = self.isDynamicQr else { return }

                if isDynamicQr {
                    analyticsUtils.logMayaEvents(.failure(Analytics.DynamicP2M.create), attributes: [.reason: message])
                } else {
                    analyticsUtils.logMayaEvents(.failure(Analytics.P2M.create), attributes: [.reason: message])
                }
            }
        }
    }
}

// MARK: Camera Setup Methods
private extension MayaQRScannerViewController {
    func setCameraConfiguration() {
        self.sessionQueue.sync { [weak self] in
            guard let self = self else { return }
            self.captureSession.beginConfiguration()
            self.setCameraInput()
            self.setCameraOutput()
            self.setCameraPreview()
            self.captureSession.commitConfiguration()
        }
    }

    func setCameraInput() {
        func useFallbackDevice() {
            guard let videoDevice = AVCaptureDevice.default(for: .video) else { return }
            do {
                try videoDevice.lockForConfiguration()
                if videoDevice.isFocusModeSupported(.continuousAutoFocus) {
                    videoDevice.focusMode = .continuousAutoFocus
                }
                videoDevice.unlockForConfiguration()
                let input = try AVCaptureDeviceInput(device: videoDevice)
                captureSession.sessionPreset = .photo
                captureSession.addInput(input)
            } catch let error {
                print("Error Unable to initialize back camera:  \(error.localizedDescription)")
            }
        }

        let deviceWithCamera = AVCaptureDevice.DiscoverySession(
            deviceTypes: [.builtInWideAngleCamera, .builtInDualCamera],
            mediaType: .video,
            position: .back).devices.first ?? AVCaptureDevice.DiscoverySession(
                deviceTypes: [.builtInWideAngleCamera],
                mediaType: .video,
                position: .back).devices.first

        guard let device = deviceWithCamera,
              let cameraInput = try? AVCaptureDeviceInput(device: device)
        else {
            useFallbackDevice()
            return
        }
        captureSession.addInput(cameraInput)
    }

    func setCameraOutput() {
        videoDataOutput.videoSettings = [(kCVPixelBufferPixelFormatTypeKey as NSString): NSNumber(value: kCVPixelFormatType_32BGRA)] as [String: Any]
        videoDataOutput.alwaysDiscardsLateVideoFrames = true
        videoDataOutput.setSampleBufferDelegate(self, queue: videoBufferCallbackQueue)
        captureSession.addOutput(self.videoDataOutput)
        guard let connection = self.videoDataOutput.connection(with: AVMediaType.video),
              connection.isVideoOrientationSupported else { return }
        connection.videoOrientation = .portrait
    }

    func setCameraPreview() {
        dispatchToMain { [weak self] in
            guard let self = self else { return }
            self.cameraScanView.videoPreviewLayer.videoGravity = .resizeAspectFill
            self.cameraScanView.videoPreviewLayer.session = self.captureSession
        }
    }
}

// MARK: AVCaptureMetadataOutputObjectsDelegate Methods
extension MayaQRScannerViewController: AVCaptureMetadataOutputObjectsDelegate {
    func metadataOutput(_ output: AVCaptureMetadataOutput,
                        didOutput metadataObjects: [AVMetadataObject],
                        from connection: AVCaptureConnection) {
        AudioServicesPlaySystemSound(kSystemSoundID_Vibrate)
        stopCameraCapture()

        guard let metadataObject = metadataObjects.first,
              let readableObject = metadataObject as? AVMetadataMachineReadableCodeObject,
              let stringValue = readableObject.stringValue,
              let viewModel = viewModel,
              !hasScannedResult
        else { return }

        hasScannedResult = true
        let source: CodeInputSource = (viewModel.scanType == .meralcoBarcode) ? .barcodeScan : .qrScan
        let scanInput = CodeInput(text: stringValue, source: source)
        viewModel.classifyCodeAction?.apply(scanInput).start()
    }
}

// MARK: AVCaptureMetadataOutputObjectsDelegate Methods
extension MayaQRScannerViewController: AVCaptureVideoDataOutputSampleBufferDelegate {
    func captureOutput(_ output: AVCaptureOutput,
                       didOutput sampleBuffer: CMSampleBuffer,
                       from connection: AVCaptureConnection) {
        let qrScanRequest = VNDetectBarcodesRequest { [weak self] (request: VNRequest, _: Swift.Error?) in
            guard let self = self,
                  let results = request.results as? [VNBarcodeObservation],
                  let result = results.first,
                  let stringValue = result.payloadStringValue,
                  let viewModel = self.viewModel,
                  !self.hasScannedResult
            else { return }

            self.stopCameraCapture()
            self.hasScannedResult = true
            AudioServicesPlaySystemSound(kSystemSoundID_Vibrate)
            let source: CodeInputSource = (viewModel.scanType == .meralcoBarcode) ? .barcodeScan : .qrScan
            let scanInput = CodeInput(text: stringValue, source: source)
            viewModel.classifyCodeAction?.apply(scanInput).start()
        }

        guard let frame = CMSampleBufferGetImageBuffer(sampleBuffer) else { return }
        let imageRequestHandler = VNImageRequestHandler(cvPixelBuffer: frame, orientation: .leftMirrored, options: [:])
        try? imageRequestHandler.perform([qrScanRequest])
    }
}

// MARK: UIImagePickerControllerDelegate and UINavigationControllerDelegate Methods
extension MayaQRScannerViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]) {
        picker.dismiss(animated: true, completion: { [weak self] in
            guard
                let self = self,
                let importedImage = info[.originalImage] as? UIImage
            else { return }

            let qrCode = self.detectCode(in: importedImage)
            let qrInput = CodeInput(text: qrCode, source: .mayaPhoto)
            AudioServicesPlayAlertSoundWithCompletion(SystemSoundID(kSystemSoundID_Vibrate)) { }
            self.viewModel?.classifyCodeAction?.apply(qrInput).start()
        })
    }

    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true, completion: { [weak self] in
            self?.startCameraCapture()
        })
    }
}
