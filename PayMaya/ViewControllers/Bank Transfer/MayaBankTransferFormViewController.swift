//
//  MayaBankTransferFormViewController.swift
//  PayMaya
//
//  Created by <PERSON> on 5/31/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Injector
import MayaFormatterManager
import ReactiveCocoa
import ReactiveSwift
import UIKit

private let progressBarText = "1/2"

protocol MayaBankTransferFormViewControllerDelegate: AnyObject {
    func didCreateTransfer(_ viewController: MayaBankTransferFormViewController, item: MayaConfirmationViewItem)
    func didGetFavoriteDetails(_ viewController: MayaBankTransferFormViewController, favoriteItem: FavoriteItem)
    func shouldHideProgressBar(_ viewController: MayaBankTransferFormViewController, shouldHide: Bool)
}

class MayaBankTransferFormViewController: FormsViewController, MayaProgressBarProtocol, MayaAnalyticsDurationManagerProtocol {
    @IBOutlet weak var accessoryLabel: UILabel!
    @IBOutlet weak var bankNameLabel: UILabel!
    @IBOutlet weak var continueButton: MayaButton!

    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject private var formatter: MayaFormatterManager

    @IBOutlet weak var amountView: MayaValidationFieldView! {
        didSet {
            amountView.setTextFieldProperties(keyboardType: .decimalPad)
        }
    }

    @IBOutlet weak var purposeView: MayaValidationFieldView! {
        didSet {
            purposeView.setTextFieldProperties(capitalizationType: .sentences)
        }
    }

    @IBOutlet weak var accountNumberView: MayaValidationFieldView! {
        didSet {
            accountNumberView.setTextFieldProperties(keyboardType: .phonePad)
        }
    }

    @IBOutlet weak var accountNameView: MayaValidationFieldView! {
        didSet {
            accountNameView.setTextFieldProperties(capitalizationType: .words)
        }
    }

    var amountTextField: TextField {
        return amountView.textField
    }

    var purposeTextField: TextField {
        return purposeView.textField
    }

    var accountNumberTextField: TextField {
        return accountNumberView.textField
    }

    var accountNameTextField: TextField {
        return accountNameView.textField
    }

    var analyticsSourcePage: AnalyticsConstants.Screen.BankTransfer.Page?

    // These variables are set as flags to indicate that the next time the VC appears,
    // it will be from the Bank Transfer Confirmation Screen.
    private var didAlreadyLoad: Bool = false
    private var didContinueTapped: Bool = false

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override var titleContentOffset: CGFloat {
        return bankNameLabel.frame.minY + bankNameLabel.frame.size.height
    }

    var segment: Int = 1

    weak var delegate: MayaBankTransferFormViewControllerDelegate?

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.BankTransfer())

        super.viewWillAppear(animated)

        switch viewState {
        case .loading, .error: break
        default: setRightButtonItemTitle(progressBarText)
        }
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        logAppear()
    }

    override func didTapBack(_ sender: Any) {
        super.didTapBack(sender)
        logBackTapped()
    }

    override func setupAccessibilityIdentifiers() {
        navigationItem.leftBarButtonItem?.isAccessibilityElement = true
        navigationItem.leftBarButtonItem?.accessibilityIdentifier = "pmios_bank_transfer_back_button"
        continueButton.isAccessibilityElement = true
        continueButton.accessibilityIdentifier = "pmios_bank_transfer_continue_button"
    }

    // MARK: - Autofill Methods
    func autoFillForm(_ qr: BankTransferQRProtocol) {
        guard let viewModel = viewModel else { return }

        let accountNameProperty = viewModel.accountNameProperty.mutableProperty
        let accountNumberProperty = viewModel.accountNumberProperty.mutableProperty
        let amountProperty = viewModel.amountProperty.mutableProperty

        if let qr = qr as? SendMoneyToBankQR {
            viewModel.setBankWithIdentifier(qr.bankIdentifier)

            accountNumberTextField.text = qr.accountNumber
            accountNumberProperty.value = qr.accountNumber

            if let amount = qr.amount {
                amountTextField.text = formatter.number.currencyPesoStringValue(string: amount)
                amountProperty.value = formatter.number.currencyStringValue(string: amount)
            }
        } else if let qr = qr as? BankTransferQR {
            viewModel.setBankWithIdentifier(qr.institutionCode)
            viewModel.setAdditionalData(from: qr)

            accountNumberTextField.text = qr.account
            accountNumberProperty.value = qr.account

            if let amount = qr.amount {
                amountTextField.text = formatter.number.currencyPesoStringValue(string: amount)
                amountProperty.value = formatter.number.currencyStringValue(string: amount)
            }

            if let accountName = qr.accountName {
                accountNameTextField.text = accountName
                accountNameProperty.value = accountName
            }
        }
    }

    func autoFillForm(_ template: BankTransferFavoriteTemplate) {
        guard let viewModel = viewModel,
              let bank = template.bank,
              let amount = template.amount,
              let purpose = template.purpose,
              let recipient = template.recipient
        else { return }

        let amountProperty = viewModel.amountProperty.mutableProperty
        let purposeProperty = viewModel.purposeProperty.mutableProperty
        let accountNumberProperty = viewModel.accountNumberProperty.mutableProperty
        let accountNameProperty = viewModel.accountNameProperty.mutableProperty

        if let code = bank.code {
            viewModel.setBankWithCode(code)
        } else if let code = bank.institutionCode {
            viewModel.setBankWithIdentifier(code)
        }

        amountTextField.text = formatter.number.currencyPesoStringValue(double: amount.value)
        amountProperty.value = formatter.number.currencyStringValue(double: amount.value)

        purposeTextField.text = purpose
        purposeProperty.value = purpose

        accountNumberTextField.text = recipient.value
        accountNumberProperty.value = recipient.value

        if let accountName = bank.account.name {
            accountNameTextField.text = accountName
            accountNameProperty.value = accountName
        } else if let firstName = bank.account.firstName,
                  let lastName = bank.account.lastName {
            let fullName = "\(firstName) \(lastName)"
            accountNameTextField.text = fullName
            accountNameProperty.value = fullName
        }
    }

    // MARK: - UI Methods
    func showLoadingState() {
        showLoadingView()
        setRightButtonItemTitle(" ")
        delegate?.shouldHideProgressBar(self, shouldHide: true)
    }

    func showFilledState() {
        showContentView()
        setRightButtonItemTitle(progressBarText)
        delegate?.shouldHideProgressBar(self, shouldHide: false)
    }

    func showErrorState() {
        showErrorView(shouldPinToSuperview: true)
        setRightButtonItemTitle(" ")
        delegate?.shouldHideProgressBar(self, shouldHide: true)
    }
}

// MARK: - BaseLoadingViewControllerProtocol Methods
extension MayaBankTransferFormViewController: BaseLoadingViewControllerProtocol {
    var loadingType: LoadingType {
        return .inlineLoader(title: nil, message: "")
    }

    var baseContainerView: UIView! {
        return view
    }

    var baseContentView: UIView! {
        return view
    }
}

// MARK: - MayaDefaultErrorViewDelegate Methods
extension MayaBankTransferFormViewController: MayaDefaultErrorViewDelegate {
    func didTapRetry(_ view: MayaDefaultErrorView) {
        guard let viewModel = viewModel else { return }

        viewModel.favoriteDetailsAction.apply().start()
    }
}

// MARK: - ViewControllerBindable Methods
extension MayaBankTransferFormViewController: ViewControllerBindable {
    func binding() {
        guard let viewModel = viewModel else { return }

        accessoryLabel.text = "\(viewModel.availableBalanceText)\n\(viewModel.bankTransferFeeText)"

        formFields = [amountView, purposeView, accountNumberView, accountNameView]
        setKeyboardReturnTypeOfTextFields()
        accountNameView.textField.returnKeyType = .done
        lastFormFieldCallback = { [weak self] in
            guard let self = self else { return }

            self.view.endEditing(true)
        }

        continueButton.reactive.pressed = CocoaAction(viewModel.createAction) { [weak self] _ in
            guard let self = self else { return }

            logConfirmButtonTapped()
            self.view.endEditing(true)
        }
    }
}

// MARK: - ViewModelBindable Methods
extension MayaBankTransferFormViewController: ViewModelBindable {
    func binding(_ viewModel: MayaBankTransferFormViewModelProtocol?) {
        guard let viewModel = viewModel else { return }

        viewModel.amountProperty.mutableProperty <~ amountTextField.reactive.continuousTextValues
                .skip(first: 1)
                .map({ amount in
                    return amount.filter("**********.".contains)
                })
        viewModel.purposeProperty.mutableProperty <~ purposeTextField.reactive.continuousTextValues.skip(first: 1)
        viewModel.accountNumberProperty.mutableProperty <~ accountNumberTextField.reactive.continuousTextValues.skip(first: 1)
        viewModel.accountNameProperty.mutableProperty <~ accountNameTextField.reactive.continuousTextValues.skip(first: 1)

        amountView.bindFieldProperty(viewModel.amountProperty)
        purposeView.bindFieldProperty(viewModel.purposeProperty)
        accountNumberView.bindFieldProperty(viewModel.accountNumberProperty)
        accountNameView.bindFieldProperty(viewModel.accountNameProperty)

        continueButton.reactive.isEnabled <~ viewModel.validatingProperty

        viewModel.bankNameProperty.producer
            .observe(on: UIScheduler())
            .skipNil()
            .startWithValues { [weak self] name in
                self?.bankNameLabel.text = name
            }.addToDisposeBag(disposeBag)

        amountView.inputStateProperty.signal.observe(on: UIScheduler())
            .observeValues { [weak self] state in
                guard let self = self else { return }

                let bankTransfer = viewModel.bankTransferFee ?? Decimal.zero
                let bankTransferFeeText = viewModel.bankTransferFeeText

                switch state {
                case .error:
                    setAccesoryLabelVisibility(isError: true, text: bankTransferFeeText)
                default:
                    setAccesoryLabelVisibility(isError: false, text: "\(viewModel.availableBalanceText)\n\(bankTransferFeeText)")
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.createAction.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                isExecuting ? self?.showMayaLoader() : self?.hideMayaLoader()
            }?.addToDisposeBag(disposeBag)

        viewModel.createAction.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] error in
                guard let self = self else { return }

                var reason = ""
                if case .sessionTimeout(let message) = error.type {
                    reason = message
                } else if let errorViewModel = error.viewModel {
                    reason = errorViewModel.message

                    self.showErrorAlert(viewModel: errorViewModel, useMayaModal: true)
                }

                logError(reason: reason, errorCode: "\(error.viewModel?.error?.code)")
            }?.addToDisposeBag(disposeBag)

        viewModel.createAction.values
            .observe(on: UIScheduler())
            .observeValues { [weak self] item in
                guard let self = self else { return }

                self.analyticsService.logMayaEvents(action: AnalyticsAction.success(Analytics.BankTransfer.create))
                self.delegate?.didCreateTransfer(self, item: item)
            }?.addToDisposeBag(disposeBag)

        viewModel.favoriteDetailsAction.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] isExecuting in
                if isExecuting {
                    self?.showLoadingState()
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.favoriteDetailsAction.values
            .observe(on: UIScheduler())
            .observeValues { [weak self] favoriteItem in
                guard let self = self else { return }

                if let template = favoriteItem.template as? BankTransferFavoriteTemplate {
                    self.delegate?.didGetFavoriteDetails(self, favoriteItem: favoriteItem)
                    self.autoFillForm(template)
                    self.showFilledState()
                } else {
                    self.showErrorState()
                }
            }?.addToDisposeBag(disposeBag)

        viewModel.favoriteDetailsAction.errors
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                self?.showErrorState()
            }?.addToDisposeBag(disposeBag)

        viewModel.balanceAction.apply().start()

        if viewModel.favoriteIDProperty.value != nil {
            viewModel.favoriteDetailsAction.apply().start()
        }
    }

    func setAccesoryLabelVisibility(isError: Bool, text: String) {
        guard let viewModel = viewModel else { return }

        let isVisible = viewModel.isAccessoryLabelVisible(isError: isError)
        self.accessoryLabel.isShown = isVisible

        if isVisible {
            accessoryLabel.text = text
            accessoryLabel.sizeToFit()
        } else {
            accessoryLabel.text = ""
            accessoryLabel.frame.size.height = 0
        }
    }
}

// MARK: - UITextFieldDelegate Methods
extension MayaBankTransferFormViewController {
    override func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        guard let viewModel = viewModel else { return true }

        switch textField {
        case amountTextField:
            let text = viewModel.formatMayaAmount(with: String.unwrappedValue(textField.text).trim(), range: range, and: string)
            amountView.setFormattedAmount(with: text.newValue, offset: text.offset)
            return false
        case purposeTextField, accountNameTextField:
            guard let currentText = textField.text,
                  let textRange = Range(range, in: currentText) else { return false }

            let updatedText = currentText.replacingCharacters(in: textRange, with: string)
            // Replace curly quotes in input (if there are any) to straight quotes
            let replacedCurlyQuotesText = updatedText.replaceCurlyQuotes()

            if replacedCurlyQuotesText != updatedText {
                textField.text = replacedCurlyQuotesText
                return false
            }

            return true
        default:
            return true
        }
    }

    func textFieldDidEndEditing(_ textField: UITextField) {
        guard let viewModel else { return }

           switch textField {
           case amountTextField:
               let isInvalid = !viewModel.amountProperty.isValidProperty.value
               let invalidReason = isInvalid ? viewModel.amountProperty.inlineValidationMessage.replaceAmountWithPlaceholder() : nil

               logTextFieldEvent(inputLabel: .amount, isInvalid: isInvalid, invalidReason: invalidReason)

               if String.unwrappedValue(textField.text).isEmpty {
                   amountTextField.text = viewModel.defaultAmountValue
                   viewModel.amountProperty.mutableProperty.value = viewModel.defaultAmountValue
               }
           case purposeTextField:
               let isInvalid = !viewModel.purposeProperty.isValidProperty.value
               let invalidReason = isInvalid ? viewModel.purposeProperty.inlineValidationMessage : nil

               logTextFieldEvent(inputLabel: .purpose, isInvalid: isInvalid, invalidReason: invalidReason)
           case accountNumberTextField:
               let isInvalid = !viewModel.accountNumberProperty.isValidProperty.value
               let invalidReason = isInvalid ? viewModel.accountNumberProperty.inlineValidationMessage : nil

               logTextFieldEvent(inputLabel: .accountNumber, isInvalid: isInvalid, invalidReason: invalidReason)
           case accountNameTextField:
               let isInvalid = !viewModel.accountNameProperty.isValidProperty.value
               let invalidReason = isInvalid ? viewModel.accountNameProperty.inlineValidationMessage : nil

               logTextFieldEvent(inputLabel: .accountName, isInvalid: isInvalid, invalidReason: invalidReason)
           default:
               break
           }
    }
}

// MARK: New Events Analytics Methods
extension MayaBankTransferFormViewController {
    func logAppear() {
        guard let viewModel, viewModel.isAppEventV2BankTransferEnabled else { return }

        let keyAttributes: [AnalyticsAttributeKey: Any] = [.sourcePage: didAlreadyLoad ? AnalyticsConstants.Screen.BankTransfer.Page.confirmation.rawValue : analyticsSourcePage?.rawValue]

        didAlreadyLoad = true

        analyticsService.logMayaEvents(AnalyticsConstants.Screen.BankTransfer.screen.appear(), attributes: keyAttributes)
    }

    func logConfirmButtonTapped() {
        guard let viewModel else { return }

        if viewModel.isAppEventV2BankTransferEnabled {
            let sourcePage = didContinueTapped ? AnalyticsConstants.Screen.BankTransfer.Page.confirmation.rawValue : analyticsSourcePage?.rawValue

            let keyAttributes: [AnalyticsAttributeKey: Any] = [
                .sourcePage: sourcePage,
                .button: AnalyticsConstants.Screen.BankTransfer.Button.continueButton.rawValue,
                .destinationPage: AnalyticsConstants.Screen.BankTransfer.Page.confirmation.rawValue
            ]

            didContinueTapped = true

            analyticsService.logMayaEvents( AnalyticsConstants.Screen.BankTransfer.screen.buttonTapped(), attributes: keyAttributes)
        } else {
            analyticsService.logMayaEvents(action: AnalyticsAction.tap(Analytics.BankTransfer.continue))
        }
    }

    func logBackTapped() {
        guard let viewModel, viewModel.isAppEventV2BankTransferEnabled else { return }

        let keyAttributes: [AnalyticsAttributeKey: Any] = [
            .sourcePage: AnalyticsConstants.Screen.BankTransfer.Page.form.rawValue,
            .button: AnalyticsConstants.Screen.BankTransfer.Button.back.rawValue,
            .destinationPage: analyticsSourcePage?.rawValue
        ]

        analyticsService.logMayaEvents( AnalyticsConstants.Screen.BankTransfer.screen.buttonTapped(), attributes: keyAttributes)
    }

    func logTextFieldEvent(inputLabel: AnalyticsConstants.Screen.BankTransfer.Input, isInvalid: Bool, invalidReason: String?) {
            guard let viewModel, viewModel.isAppEventV2BankTransferEnabled else { return }

            let keyAttributes: [AnalyticsAttributeKey: Any] = [
                .sourcePage: analyticsSourcePage?.rawValue,
                .inputLabel: inputLabel.rawValue,
                .isInvalid: isInvalid,
                .invalidReason: invalidReason,
                .isDisabled: false,
                .isAmountFilled: !(amountTextField.text?.isEmpty ?? true),
                .isPurposeFilled: !(purposeTextField.text?.isEmpty ?? true),
                .isAccountNumberFilled: !(accountNumberTextField.text?.isEmpty ?? true),
                .isAccountNameFilled: !(accountNameTextField.text?.isEmpty ?? true)
            ]

            analyticsService.logMayaEvents(AnalyticsConstants.Screen.BankTransfer.screen.input(), attributes: keyAttributes)
    }

    func logError(reason: String, errorCode: String) {
        guard let viewModel else { return }

        if viewModel.isAppEventV2BankTransferEnabled {
            let keyAttributes: [AnalyticsAttributeKey: Any] = [
                .sourcePage: didAlreadyLoad ? AnalyticsConstants.Screen.BankTransfer.Page.confirmation.rawValue : analyticsSourcePage?.rawValue,
                .errorReason: reason,
                .errorCode: errorCode
            ]

            analyticsService.logMayaEvents(AnalyticsConstants.Screen.BankTransfer.screen.failed(), attributes: keyAttributes)
        } else {
            analyticsService.logMayaEvents(action: AnalyticsAction.failure(Analytics.BankTransfer.create), keyAttributes: [AnalyticsAttributeKey.reason: reason])
        }
    }
}
