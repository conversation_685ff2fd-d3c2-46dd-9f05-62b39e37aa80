//
//  MayaEKYCIntroScreenViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 6/2/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Injector
import ReactiveSwift
import UIKit

protocol MayaEKYCIntroScreenViewControllerDelegate: AnyObject {
    func didAppearEKYCIntroScreen()
    func didCancelEKYCIntroScreen()
    func didTapInviteCode(_ viewController: MayaEKYCIntroScreenViewController, action: String)
    func didTapSeeAcceptedIDs(_ viewController: MayaEKYCIntroScreenViewController, action: String)
    func didTapLearnMore(_ viewController: MayaEKYCIntroScreenViewController, action: String)
    func didTapPrimaryButton(_ viewController: MayaEKYCIntroScreenViewController, action: String)
    func didTapSecondaryButton(_ viewController: MayaEKYCIntroScreenViewController, action: String)
}

final class MayaEKYCIntroScreenViewController: ViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    @IBOutlet var backgroundView: UIView!
    @IBOutlet weak var headerView: UITextView!
    @IBOutlet weak var subheaderView: UITextView!
    @IBOutlet weak var inviteCodeView: UIView!
    @IBOutlet weak var inviteCodeViewHeight: NSLayoutConstraint!
    @IBOutlet weak var inviteCodeViewLeading: NSLayoutConstraint!
    @IBOutlet weak var inviteCodeViewTrailing: NSLayoutConstraint!
    @IBOutlet weak var inviteCodeDescription: UITextView!
    @IBOutlet weak var stepsHeader: UILabel!
    @IBOutlet weak var stepsSubHeader: UILabel!
    @IBOutlet weak var stepsCollectionView: UICollectionView!
    @IBOutlet weak var collectionViewHeightConstraint: NSLayoutConstraint!
    @IBOutlet weak var unlockEverythingLabel: UILabel!
    @IBOutlet weak var unlockStackView: UIStackView!
    @IBOutlet weak var unlockImageView: UIImageView!
    @IBOutlet weak var subtextHeader: UILabel!
    @IBOutlet weak var subtextDescription: UITextView!
    @IBOutlet weak var primaryButton: MayaButton!
    @IBOutlet weak var secondaryButton: MayaButton!

    private var inviteCodeFontSize: CGFloat = 14
    private var cellHeight: CGFloat = 128
    private var cellWidth: CGFloat = 112
    private let minimumInteritemSpacing: CGFloat = 8

    weak var delegate: MayaEKYCIntroScreenViewControllerDelegate?

    override var shouldUseAnimatedNavigationBar: Bool {
        return true
    }

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override var navigationBarBackgroundColor: UIColor {
        return UIColor.clear
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        delegate?.didAppearEKYCIntroScreen()
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupBackgroundGradient()
        setupViews()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if let leftBarButton = navigationItem.leftBarButtonItem {
            leftBarButton.image = CommonAsset.Images.Icons.iconSystemCross.image.withRenderingMode(.alwaysOriginal)
        }
    }

    override func didTapBack(_ sender: Any) {
        MayaOnboardingUtility().resetOnboardingInfo()
        super.didTapBack(sender)
        delegate?.didCancelEKYCIntroScreen()
    }

    private func setupBackgroundGradient() {
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [UIColor(red: 194.0 / 255.0, green: 242.0 / 255.0, blue: 202.0 / 255.0, alpha: 0.2).cgColor, UIColor(red: 255.0 / 255.0, green: 255.0 / 255.0, blue: 255.0 / 255.0, alpha: 1.0).cgColor]
        gradientLayer.frame = backgroundView.bounds
        gradientLayer.locations = [0, 0.3]
        view.layer.insertSublayer(gradientLayer, at: 0)
    }

    private func setupViews() {
        guard let viewModel = viewModel,
              let intro = viewModel.kycData.value?.intro
        else { return }

        var headerText, subHeaderText, unlockText, stepsHeaderString: String
        if viewModel.isFromReg.value {
            headerText = intro.newRegHeader
            subHeaderText = intro.newRegIntroDescription
            primaryButton.setTitle(intro.upgradeButtonTitleKyc0, for: .normal)
            unlockText = intro.unlockEverythingHeaderKyc0
            stepsHeaderString = intro.upgradeNowHeaderKyc0
        } else if viewModel.kycLevel == .zero {
            headerText = intro.kyc0Header
            subHeaderText = intro.kyc0Description
            primaryButton.setTitle(intro.upgradeButtonTitleKyc0, for: .normal)
            unlockText = intro.unlockEverythingHeaderKyc0
            stepsHeaderString = intro.upgradeNowHeaderKyc0
        } else {
            headerText = intro.kyc1Header
            subHeaderText = intro.kyc1Description
            primaryButton.setTitle(intro.upgradeButtonTitleKyc1, for: .normal)
            inviteCodeView.isHidden = true
            unlockText = intro.unlockEverythingHeaderKyc1
            stepsHeaderString = intro.upgradeNowHeaderKyc1
        }

        headerView.attributedText = NSAttributedString(string: headerText, attributes: [
            .font: CommonFontFamily.Jeko.bold.font(size: 32)!])
        headerView.textAlignment = .center
        headerView.textContainer.lineFragmentPadding = 0
        headerView.textContainerInset = .zero
        headerView.contentInset = .zero

        subheaderView.attributedText = subHeaderText.getHTMLAttributedString(with: CommonFontFamily.CerebriSansPro.book.font(size: 14)!, color: CommonAsset.MayaColors.Content.contentGrey6.color, shouldUseSystemFont: false)
        subheaderView.textAlignment = .center
        subheaderView.textContainer.lineFragmentPadding = 0
        subheaderView.textContainerInset = .zero
        subheaderView.contentInset = .zero

        inviteCodeView.layer.borderWidth = 1
        inviteCodeView.layer.borderColor = CommonAsset.MayaColors.Grey.grey3.color.cgColor
        inviteCodeView.layer.cornerRadius = inviteCodeView.frame.height / 2
        inviteCodeView.layer.masksToBounds = true

        inviteCodeDescription.linkTextAttributes = [.underlineStyle: 0,
                                                    .underlineColor: UIColor.clear,
                                                    .font: CommonFontFamily.CerebriSansPro.medium.font(size: 14)!,
                                                    .foregroundColor: CommonAsset.Colors.Constants._00A651.color]

        inviteCodeDescription.attributedText = intro.inviteCodeDescription.getHTMLAttributedString(with: CommonFontFamily.Jeko.bold.font(size: 14)!, color: CommonAsset.MayaColors.Primary.primaryBlack.color, shouldUseSystemFont: false)
        inviteCodeDescription.textAlignment = .center
        inviteCodeDescription.textContainer.lineFragmentPadding = 0
        inviteCodeDescription.textContainerInset = .zero
        inviteCodeDescription.contentInset = .zero
        inviteCodeDescription.textContainer.lineBreakMode = .byWordWrapping
        inviteCodeDescription.delegate = self

        while countNumberOfLines(textView: inviteCodeDescription) > 1 {
            inviteCodeFontSize -= 1
            inviteCodeDescription.font = CommonFontFamily.Jeko.bold.font(size: inviteCodeFontSize)
        }

        stepsHeader.font = CommonFontFamily.CerebriSansPro.bold.font(size: 14)
        stepsHeader.textColor = CommonAsset.MayaColors.Content.contentPrimaryBlack.color
        stepsHeader.text = stepsHeaderString

        stepsSubHeader.font = CommonFontFamily.CerebriSansPro.book.font(size: 14)
        stepsSubHeader.textColor = CommonAsset.MayaColors.Content.contentGrey6.color
        stepsSubHeader.text = intro.upgradeNowDescription

        unlockEverythingLabel.font = CommonFontFamily.CerebriSansPro.bold.font(size: 14)
        unlockEverythingLabel.textColor = CommonAsset.MayaColors.Content.contentPrimaryBlack.color
        unlockEverythingLabel.text = unlockText

        for item in intro.unlockEverythingDetails.features {
            let label = UILabel()
            label.textColor = CommonAsset.MayaColors.Content.contentPrimaryBlack.color
            label.font = CommonFontFamily.CerebriSansPro.book.font(size: 14)
            label.text = item.instruction
            unlockStackView.addArrangedSubview(label)
        }

        subtextHeader.font = CommonFontFamily.CerebriSansPro.bold.font(size: 14)
        subtextHeader.textColor = CommonAsset.MayaColors.Content.contentPrimaryBlack.color
        subtextHeader.text = intro.safeTransactionHeader

        subtextDescription.textContainer.lineFragmentPadding = 0
        subtextDescription.textContainerInset = .zero
        subtextDescription.contentInset = .zero
        subtextDescription.linkTextAttributes = [.underlineStyle: 0,
                                                 .underlineColor: UIColor.clear,
                                                 .font: CommonFontFamily.CerebriSansPro.book.font(size: 14)!,
                                                 .foregroundColor: CommonAsset.Colors.Constants._00A651.color]
        subtextDescription.attributedText = intro.safeTransactionDescription.getHTMLAttributedString(with: CommonFontFamily.CerebriSansPro.book.font(size: 14)!, color: CommonAsset.MayaColors.Content.contentGrey6.color, shouldUseSystemFont: false)
        subtextDescription.delegate = self

        secondaryButton.setTitle(intro.doItLaterButtonTitle, for: .normal)
    }

    private func countNumberOfLines(textView: UITextView) -> Int {
        let numberOfGlyphs = inviteCodeDescription.layoutManager.numberOfGlyphs
        var index = 0, numberOfLines = 0
        var lineRange = NSRange(location: NSNotFound, length: 0)

        while index < numberOfGlyphs {
            inviteCodeDescription.layoutManager.lineFragmentRect(forGlyphAt: index, effectiveRange: &lineRange)
            index = NSMaxRange(lineRange)
            numberOfLines += 1
        }

        return numberOfLines
    }

    @IBAction func didTapPrimaryButton(_ sender: Any) {
        delegate?.didTapPrimaryButton(self, action: primaryButton.titleLabel?.text ?? "")
    }

    @IBAction func didTapSecondaryButton(_ sender: Any) {
        MayaOnboardingUtility().resetOnboardingInfo()
        delegate?.didTapSecondaryButton(self, action: secondaryButton.titleLabel?.text ?? "")
        super.didTapBack(sender)
    }
}

// MARK: ViewModelBindable Methods
extension MayaEKYCIntroScreenViewController: ViewModelBindable {
    func binding(_ viewModel: MayaZolozEkycViewModel?) {
        guard let viewModel = viewModel else { return }
        stepsCollectionView.reloadData()
    }
}

// MARK: UICollectionViewDataSource Methods
extension MayaEKYCIntroScreenViewController: UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return viewModel?.kycData.value?.intro?.upgradeStepsGuide.images.count ?? 0
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let viewModel = viewModel,
              let cell = collectionView.dequeueReusableCell(withReuseIdentifier: MayaEKYCStepsCell.defaultReuseIdentifier, for: indexPath) as? MayaEKYCStepsCell,
              let benefit = viewModel.kycData.value?.intro?.upgradeStepsGuide.images[safeIndex: indexPath.row]
        else {
            return UICollectionViewCell()
        }

        cell.configure(with: benefit, step: indexPath.row)
        cell.layer.borderWidth = 1
        cell.layer.borderColor = CommonAsset.MayaColors.Grey.grey2.color.cgColor
        cell.layer.cornerRadius = 10
        cell.stepDescription.delegate = self

        return cell
    }

    // Implement the flow layout delegate method
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let collectionViewSize = collectionView.frame.size.width

        while calculateItemsPerRow() < 3 {
            cellWidth -= 8
            cellHeight += 12
            collectionViewHeightConstraint.constant += 12
        }

        return CGSize(width: cellWidth, height: cellHeight)
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return minimumInteritemSpacing
    }

    private func calculateItemsPerRow() -> Int {
        let totalPadding = minimumInteritemSpacing * 2
        let availableWidth = stepsCollectionView.frame.width - totalPadding
        let itemsPerRow = Int(availableWidth / cellWidth)
        return max(itemsPerRow, 1)
    }
}

// MARK: - UITextViewDelegate Methods
extension MayaEKYCIntroScreenViewController: UITextViewDelegate {
    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        var urlText = ""
        if let textRange = textView.textRange(from: characterRange),
           let text = textView.text(in: textRange) {
            urlText = text
        }

        if URL.lastPathComponent == "EnterCode" {
            delegate?.didTapInviteCode(self, action: urlText)
        } else if URL.lastPathComponent == "SeeIDs" {
            delegate?.didTapSeeAcceptedIDs(self, action: urlText)
        } else if URL.lastPathComponent == "LearnMore" {
            delegate?.didTapLearnMore(self, action: urlText)
        }

        return false
    }
}
