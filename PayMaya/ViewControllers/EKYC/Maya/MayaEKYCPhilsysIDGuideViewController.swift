//
//  MayaEKYCPhilsysIDGuideViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 2/26/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import ReactiveSwift
import UIKit

protocol MayaEKYCPhilsysIDGuideViewControllerDelegate: AnyObject {
    func didTapUploadID()
    func didTapTakePhoto()
    func didAppearIDGuide(events: [AnalyticsAttributeKey: String])
    func didTapIDGuideElement(action: String, destination: String?)
}

final class MayaEKYCPhilsysIDGuideViewController: ViewController, MayaProgressBarProtocol {
    var segment: Int = 1
    override var shouldUseMayaNavigationBar: Bool { true }

    @IBOutlet weak var mainScrollView: UIScrollView!
    @IBOutlet weak var titleLabel: Label!
    @IBOutlet weak var subtitleLabel: Label!
    @IBOutlet weak var guideTableView: UITableView!
    @IBOutlet weak var uploadIDButton: MayaButton!
    @IBOutlet weak var takePhotoButton: MayaButton!
    @IBOutlet weak var tableHeightConstraint: NSLayoutConstraint!

    weak var delegate: MayaEKYCPhilsysIDGuideViewControllerDelegate?

    override func viewDidLoad() {
        super.viewDidLoad()
        setupViews()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        setRightButtonItemTitle(L10n.EkycZoloz.FlowProgressBar.progress(segment))
    }

    override func viewDidAppear(_ animated: Bool) {
        let appearEvents: [AnalyticsAttributeKey: String] = [
            AnalyticsAttributeKey.idTypeGuide: Constants.EKYC.Document.Philsys.key
        ]
        delegate?.didAppearIDGuide(events: appearEvents)
        super.viewDidAppear(animated)
    }

    func setupViews() {
        guard let viewModel = viewModel else { return }
        titleLabel.text = viewModel.captureGuide?.header ?? ""
        subtitleLabel.text = viewModel.captureGuide?.subHeader ?? ""
        guideTableView.dataSource = self
        guideTableView.delegate = self

        if let isAllowedToUpload = viewModel.captureGuide?.isAllowedToUpload,
           !isAllowedToUpload {
            uploadIDButton.isHidden = true
            takePhotoButton.setTitle(L10n.Maya.Ekyc.Common.continue, for: .normal)
        }
    }

    @IBAction func uploadIDButtonClicked(_ sender: Any) {
        delegate?.didTapIDGuideElement(action: uploadIDButton.titleLabel?.text ?? "", destination: AnalyticsConstants.ScreenName.uploadID)
        delegate?.didTapUploadID()
    }

    @IBAction func takePhotoButtonClicked(_ sender: Any) {
        delegate?.didTapIDGuideElement(action: takePhotoButton.titleLabel?.text ?? "", destination: AnalyticsConstants.ScreenName.idTips)
        delegate?.didTapTakePhoto()
    }
}

// MARK: - ViewModelBindable
extension MayaEKYCPhilsysIDGuideViewController: ViewModelBindable {
    func binding(_ viewModel: MayaEKYCPhilsysIDGuideViewModel?) {
        guard let viewModel = viewModel else { return }
    }
}

// MARK: - UITextViewDelegate Methods
extension MayaEKYCPhilsysIDGuideViewController: UITextViewDelegate {
    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        var urlText = ""
        if let textRange = textView.textRange(from: characterRange),
           let text = textView.text(in: textRange) {
            urlText = text
        }

        if URL.lastPathComponent == "eGovPH" {
            delegate?.didTapIDGuideElement(action: urlText, destination: nil)
            let indexPath = IndexPath(row: 2, section: 0)
            if let row = self.guideTableView.cellForRow(at: indexPath) {
                self.mainScrollView.scrollToView(view: row, animated: true)
            }
        } else if URL.lastPathComponent == "DigitalID" {
            delegate?.didTapIDGuideElement(action: urlText, destination: nil)
            let indexPath = IndexPath(row: 3, section: 0)
            if let row = self.guideTableView.cellForRow(at: indexPath) {
                self.mainScrollView.scrollToView(view: row, animated: true)
            }
        }

        return false
    }
}

// MARK: - UITableViewDelegate and UITableViewDataSource Methods
extension MayaEKYCPhilsysIDGuideViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let viewModel = viewModel else { return 0 }
        return viewModel.captureGuide?.samples?.count ?? 0
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let viewModel = viewModel,
              let samples = viewModel.captureGuide?.samples,
              let sample = samples[safeIndex: indexPath.row],
              let cell = tableView.dequeueReusableCell(withIdentifier: MayaIDGuideTableViewCell.reuseIdentifier, for: indexPath) as? MayaIDGuideTableViewCell
        else {
            return UITableViewCell()
        }

        cell.setDetails(sample)

        if let images = sample.images {
            for image in images {
                let view = Bundle.main.loadNibNamed("MayaEKYCIDGuideImageView", owner: nil, options: nil)?.first as? MayaEKYCIDGuideImageView
                view?.setImage(image) { [weak self] in
                    DispatchQueue.main.async {
                        cell.setNeedsLayout()
                        cell.layoutIfNeeded()
                        self?.guideTableView.beginUpdates()
                        self?.guideTableView.endUpdates()
                        self?.guideTableView.layoutIfNeeded()
                        if let height = self?.guideTableView.contentSize.height {
                            self?.tableHeightConstraint.constant = height
                        }
                    }
                }

                if let view = view {
                    cell.imagesStackView.addArrangedSubview(view)
                }
            }
        }

        cell.descriptionTextView.delegate = self
        cell.additionalDescriptionTextView.delegate = self
        cell.selectionStyle = .none
        cell.preservesSuperviewLayoutMargins = false
        cell.separatorInset = UIEdgeInsets.zero
        cell.layoutMargins = UIEdgeInsets.zero
        let numberOfRows = self.tableView(tableView, numberOfRowsInSection: indexPath.section)
        cell.hidesTopSeparator = indexPath.row == 0
        cell.hidesBottomSeparator = indexPath.row == numberOfRows - 1
        return cell
    }

    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        tableView.layoutIfNeeded()
        tableHeightConstraint.constant = guideTableView.contentSize.height
    }
}

extension UITextView {
    func textRange(from nsRange: NSRange) -> UITextRange? {
        guard let start = position(from: beginningOfDocument, offset: nsRange.location),
              let end = position(from: start, offset: nsRange.length) else {
            return nil
        }
        return textRange(from: start, to: end)
    }
}
