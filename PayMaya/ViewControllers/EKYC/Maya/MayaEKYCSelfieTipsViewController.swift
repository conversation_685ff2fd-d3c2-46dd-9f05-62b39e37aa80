//
//  MayaEKYCSelfieTipsViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON><PERSON> on 6/18/25.
//  Copyright © 2025 PayMaya Philippines, Inc. All rights reserved.
//

import AssetProvider
import Foundation

protocol MayaEKYCSelfieTipsViewControllerDelegate: AnyObject {
    func didTapContinue(_ viewController: MayaEKYCSelfieTipsViewController, action: String)
    func didTapBack(_ viewController: MayaEKYCSelfieTipsViewController)
    func didAppearSelfieTips()
}

final class MayaEKYCSelfieTipsViewController: ViewController, MayaProgressBarProtocol {
    @IBOutlet weak var selfieIconImageView: UIImageView!
    @IBOutlet weak var headerLabel: UILabel!
    @IBOutlet weak var descriptionLabel: UILabel!
    @IBOutlet weak var continueButton: MayaButton!

    var segment: Int = 2

    weak var delegate: MayaEKYCSelfieTipsViewControllerDelegate?

    override var shouldUseMayaNavigationBar: Bool { true }
    override var navigationBarBackgroundColor: UIColor { return CommonAsset.MayaColors.Grey.grey1.color }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        delegate?.didAppearSelfieTips()
        setRightButtonItemTitle(L10n.EkycZoloz.FlowProgressBar.progress(segment))
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupViews()
    }

    override func didTapBack(_ sender: Any) {
        delegate?.didTapBack(self)
    }

    @IBAction func continueButtonPressed(_ sender: Any) {
        delegate?.didTapContinue(self, action: continueButton.titleLabel?.text ?? "")
    }

    func setupViews() {
        guard let selfieTips = viewModel?.kycData.value?.selfieTips else { return }

        headerLabel.font = CommonFontFamily.Jeko.bold.font(size: 32)
        headerLabel.textColor = CommonAsset.MayaColors.Content.contentPrimaryBlack.color
        headerLabel.text = selfieTips.header

        descriptionLabel.font = CommonFontFamily.CerebriSansPro.book.font(size: 14)
        descriptionLabel.textColor = CommonAsset.MayaColors.Content.contentGrey6.color
        descriptionLabel.text = selfieTips.description

        if let iconURL = URL(string: selfieTips.iconUrl) {
            selfieIconImageView.sd_setImage(with: iconURL, completed: nil)
        }
    }
}

// MARK: ViewModelBindable methods
extension MayaEKYCSelfieTipsViewController: ViewModelBindable {
    func binding(_ viewModel: MayaZolozEkycViewModel?) {
        guard let viewModel = viewModel else { return }
    }
}
