//
//  MayaZolozBenefitsAndStepsViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 11/07/2022.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import ConfigurationService
import Injector
import ReactiveSwift
import UIKit

protocol MayaZolozBenefitsAndStepsViewControllerDelegate: AnyObject {
    func didTapPresentAcceptedIDs(_ viewController: MayaZolozBenefitsAndStepsViewController, action: String)
    func didTapLetsGo(_ viewController: MayaZolozBenefitsAndStepsViewController, action: String)
    func didTapLearnMore(_ viewController: MayaZolozBenefitsAndStepsViewController)
    func didTapDataPrivacy(_ viewController: MayaZolozBenefitsAndStepsViewController, action: String)
    func didCancelEKYCBenefits()
    func didAppearEKYCBenefits()
    func didTapBenefitsToggle(action: String)
}

final class MayaZolozBenefitsAndStepsViewController: ViewController, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject private var configurationServiceV2: ConfigurationServiceV2

    @IBOutlet weak var contentScrollView: UIScrollView!
    @IBOutlet weak var benefitsCollectionView: UICollectionView!
    @IBOutlet weak var toggleExpandButton: UIButton!
    @IBOutlet weak var acceptedIDsButton: UIButton!
    @IBOutlet weak var benefitsCollectionHeightConstraint: NSLayoutConstraint!
    @IBOutlet weak var getIdentifiedEasierContainerView: UIView!
    @IBOutlet weak var dataPrivacyContainerView: UIView!
    @IBOutlet weak var dataPrivacyTitleLabel: UILabel!
    @IBOutlet weak var dataPrivacydescriptionLabel: LabelWithHighlight!

    @IBOutlet weak var letsgoButton: MayaButton!
    @IBOutlet weak var doItLaterButton: MayaButton!

    weak var delegate: MayaZolozBenefitsAndStepsViewControllerDelegate?

    // This property will contain triggering Module
    var analyticsModule: AnalyticsModule?

    override var shouldUseMayaNavigationBar: Bool { true }

    private let collectionViewCellHeight = 128.0

    private var isExpanded = false {
        didSet {
            toggleExpandButton.isSelected = isExpanded
            let newHeight = isExpanded ? benefitsCollectionView.collectionViewLayout.collectionViewContentSize.height : collectionViewCellHeight
            benefitsCollectionHeightConstraint.constant = newHeight
            view.setNeedsUpdateConstraints()
            UIView.animate(withDuration: 0.25) { [unowned self] in
                view.layoutIfNeeded()
            }
        }
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        benefitsCollectionView.reloadData()
        setupToggleExpandButton()
        setupGetIdenifiedEasierContainerView()
        setupDataPrivacyContainerView()
    }

    override func didTapBack(_ sender: Any) {
        MayaOnboardingUtility().resetOnboardingInfo()
        super.didTapBack(sender)
        delegate?.didCancelEKYCBenefits()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        delegate?.didAppearEKYCBenefits()
        setupSplitToggleExperiments()
    }

    @IBAction
    private func presentAcceptedIDs(_ sender: Any) {
        if let button = sender as? UIButton {
            delegate?.didTapPresentAcceptedIDs(self, action: button.titleLabel?.text ?? "")
        }
    }

    @IBAction
    private func didTapLetsGo(_ sender: Any) {
        guard let viewModel = viewModel else { return }
        if let button = sender as? UIButton {
            if viewModel.welcomeButtonTreatment != "" {
                sendSplitAnalytics()
            }
            delegate?.didTapLetsGo(self, action: button.titleLabel?.text ?? "")
        }
    }

    @IBAction func didTapDoItLater(_ sender: Any) {
        MayaOnboardingUtility().resetOnboardingInfo()
        super.didTapBack(sender)
    }

    @IBAction
    private func toggleExpandedState() {
        delegate?.didTapBenefitsToggle(action: toggleExpandButton.titleLabel?.text ?? "")
        isExpanded.toggle()
        resetContentScrollViewOffset()
    }

    func sendSplitAnalytics() {
        var attributes: [String: Any] = [String: Any]()
        attributes[MayaExperimentAttributedKey.osPlatform.rawValue] = SplitConstants.Attributes.Value.ios
        configurationServiceV2.track(for: .welcomeButtonLabel, eventName: Constants.EKYC.SplitKeys.Events.welcomeButtonTapped, attributes: attributes)
    }
}

private extension MayaZolozBenefitsAndStepsViewController {
    func setupToggleExpandButton() {
        toggleExpandButton.setTitle(L10n.EkycZoloz.BenefitsAndSteps.ShowBenefitsToggle.colapsedTitle, for: .normal)
        toggleExpandButton.setTitle(L10n.EkycZoloz.BenefitsAndSteps.ShowBenefitsToggle.expandedTitle, for: .selected)
        toggleExpandButton.setImage(CommonAsset.Images.Icons.iconChevronGreenDown.image, for: .normal)
        toggleExpandButton.setImage(CommonAsset.Images.Icons.iconChevronGreenUp.image, for: .selected)
    }

    func setupGetIdenifiedEasierContainerView() {
        let tapGestureRecognizer = UITapGestureRecognizer(target: self,
                                                          action: #selector(didTapGetIdentifiedEasierContainerView))
        getIdentifiedEasierContainerView.addGestureRecognizer(tapGestureRecognizer)
    }

    func setupDataPrivacyContainerView() {
        dataPrivacyTitleLabel.text = L10n.EkycZoloz.BenefitsAndSteps.DataPrivacy.title
        dataPrivacydescriptionLabel.text = L10n.EkycZoloz.BenefitsAndSteps.DataPrivacy.description
        dataPrivacydescriptionLabel.highlightPhrase = L10n.EkycZoloz.BenefitsAndSteps.DataPrivacy.highlightPhrase
        let tapGestureRecognizer = UITapGestureRecognizer(target: self,
                                                          action: #selector(didTapDataPrivacyContainerView))
        dataPrivacyContainerView.addGestureRecognizer(tapGestureRecognizer)
    }

    func setupSplitToggleExperiments() {
        guard let viewModel = viewModel else { return }

        if let leftBarButton = navigationItem.leftBarButtonItem,
           viewModel.changeWelcomeBackButtonEnabled {
            leftBarButton.image = CommonAsset.Images.Icons.iconSystemCross.image.withRenderingMode(.alwaysOriginal)
        }

        if viewModel.welcomeButtonTreatment == Constants.EKYC.SplitKeys.WelcomeScreen.upgradeForFree && viewModel.kycLevel != .one {
            self.letsgoButton.setTitle(L10n.Maya.Ekyc.Welcome.ButtonTitle.upgradeForFree, for: .normal)
        } else if viewModel.welcomeButtonTreatment == Constants.EKYC.SplitKeys.WelcomeScreen.verifyNow && viewModel.kycLevel != .one {
            self.letsgoButton.setTitle(L10n.Maya.Ekyc.Welcome.ButtonTitle.verifyNow, for: .normal)
        } else {
            self.letsgoButton.setTitle(L10n.Maya.Ekyc.Welcome.ButtonTitle.default, for: .normal)
        }

        self.doItLaterButton.isHidden = !viewModel.showWelcomeDoItLaterButtonEnabled
    }

    @objc
    func didTapGetIdentifiedEasierContainerView() {
        delegate?.didTapLearnMore(self)
    }

    func resetContentScrollViewOffset() {
        contentScrollView.contentOffset = CGPoint.zero
    }

    @objc
    func didTapDataPrivacyContainerView() {
        delegate?.didTapDataPrivacy(self, action: L10n.EkycZoloz.BenefitsAndSteps.DataPrivacy.highlightPhrase)
    }
}

// MARK: UICollectionViewDataSource Methods
extension MayaZolozBenefitsAndStepsViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return viewModel?.kycData.value?.benefits.count ?? 0
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let viewModel = viewModel,
              let cell = collectionView.dequeueReusableCell(withReuseIdentifier: MayaBenefitCell.defaultReuseIdentifier, for: indexPath) as? MayaBenefitCell,
              let benefit = viewModel.kycData.value?.orderedBenefits[indexPath.row]
        else {
            return UICollectionViewCell()
        }

        cell.configure(with: benefit)

        return cell
    }
}

// MARK: ViewModelBindable Methods
extension MayaZolozBenefitsAndStepsViewController: ViewModelBindable {
    func binding(_ viewModel: MayaZolozEkycViewModel?) {
        guard let viewModel = viewModel else { return }
        benefitsCollectionView.reloadData()
    }
}
