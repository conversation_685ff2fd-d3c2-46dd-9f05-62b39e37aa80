//
//  MayaDashboardViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 2/9/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import ConfigurationService
import Error
import Injector
import ReactiveSwift
import UIKit

private let alertSnackBarAnimationDuration: TimeInterval = 0.30
private let alertSnackBarDisplayDuration: TimeInterval = 3
private let walletBalanceStackViewSpacingWithCreditBanner: CGFloat = -22

protocol MayaDashboardMonitoringDelegate: AnyObject {
    func didFinishTTFD(_ viewController: ViewController)
}

protocol MayaDashboardViewControllerDelegate: AnyObject {
    func didTapCreditBalanceBanner(_ viewController: MayaDashboardViewController, account: MayaCreditAccount, feeRate: Float?)
    func didTapAdBanner(_ viewController: MayaDashboardViewController, link: URL)
    func didTapSeeAllActivities(_ viewController: MayaDashboardViewController)
    func didTapAddMoney(_ viewController: MayaDashboardViewController)
    func didTapMissions(_ viewController: MayaDashboardViewController)
    func didTapSendMoney(_ viewController: MayaDashboardViewController)
    func didTapVouchers(_ viewController: MayaDashboardViewController)
    func didTapActionCard(_ viewController: MayaDashboardViewController, url: URL)
    func didTapWalletService(_ viewController: MayaDashboardViewController, service: Service)
    func didTapMoreServices(_ viewController: MayaDashboardViewController)
    func didTapWalletSettingsService(_ viewController: MayaDashboardViewController)
    func didTapHelpCenter(_ viewController: MayaDashboardViewController, url: URL)
    func showReKYCPrompt(_ viewController: MayaDashboardViewController)
    func didTapMayaActivityDetails(_ viewController: MayaDashboardViewController, viewModel: MayaActivityDetailsViewModelProtocol)
}

class MayaDashboardViewController: ViewController, MayaAnalyticsDurationManagerProtocol, AnalyticsServiceProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager
    @Inject var analyticsService: AnalyticsService
    @Inject var googleInterstitialAdsHelper: GoogleInterstitialAdsHelper

    @IBOutlet weak var mainScrollView: UIScrollView!
    @IBOutlet weak var mainStackView: UIStackView!
    @IBOutlet weak var walletBalanceStackView: UIStackView!
    @IBOutlet weak var dashboardStackView: UIStackView!

    @IBOutlet weak var refreshControl: MayaRefreshControl!

    @IBOutlet weak var alertSnackBar: MayaSnackBarView!

    @IBOutlet weak var serverErrorView: MayaDefaultErrorView!
    @IBOutlet weak var walletBalanceView: UIView!
    @IBOutlet weak var easyCreditView: UIView!
    @IBOutlet weak var easyCreditApplicationView: UIView!
    @IBOutlet weak var tilesView: UIView!
    @IBOutlet weak var adCarouselView: UIView!
    @IBOutlet weak var latestActivitiesView: UIView!
    @IBOutlet weak var rewardsView: UIView!
    @IBOutlet weak var bspFooterView: MayaBSPView!

    @IBOutlet weak var walletBalanceHeightConstraint: NSLayoutConstraint!
    @IBOutlet weak var adCarouselViewHeightConstraint: NSLayoutConstraint!
    @IBOutlet weak var activityContainerViewHeightConstraint: NSLayoutConstraint!
    @IBOutlet weak var rewardsViewHeightConstraint: NSLayoutConstraint!

    private var alertSnackBarTimer: Timer?
    private var adCarouselViewController: MayaAdCarouselViewController?
    private var googleBannerAdsCarouselViewController: GoogleBannerAdsCarouselViewController?
    private var noActivityViewController: MayaNoActivityViewController?
    private var latestActivitiesViewController: MayaLatestActivitiesViewController?

    weak var delegate: MayaDashboardViewControllerDelegate?
    weak var monitoringDelegate: MayaDashboardMonitoringDelegate?

    let (clevertapBannerDisplayedSignal, clevertapBannerDisplayedObserver) = Signal<Void, Never>.pipe()

    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override var shouldUseMayaArrowBackButton: Bool {
        return false
    }

    override var shouldMonitorScreenPerformance: Bool {
        guard let viewModel = viewModel else { return false }
        return viewModel.shouldMonitorScreenPerformance
    }

    override var shouldOverrideTTFDMetric: Bool {
        return true
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        mainScrollView.refreshControl = refreshControl
        navigationBarHidden = true

        adCarouselViewController = StoryboardScene.Home.mayaAdCarouselViewController.instantiate()
        adCarouselViewController?.routeActionDelegate = routeActionDelegate
        adCarouselViewController?.delegate = self
        adCarouselViewController?.navigationBarHidden = true
        add(child: adCarouselViewController!, to: adCarouselView, with: adCarouselView.bounds)

        serverErrorView.delegate = self

        guard let viewModel = viewModel else { return }

        let balanceViewController = StoryboardScene.Home.mayaWalletBalanceViewController.instantiate()
        let balanceViewModel = viewModel.generateWalletBalanceViewModel()
        balanceViewController.delegate = self
        balanceViewController.routeActionDelegate = routeActionDelegate
        balanceViewController.navigationBarHidden = true
        balanceViewController.bind(balanceViewModel)
        add(child: balanceViewController, to: walletBalanceView, padding: 0)

        if viewModel.isEasyCreditBannerEnabled || viewModel.isEasyCreditBannerV2Enabled {
            walletBalanceStackView.spacing = walletBalanceStackViewSpacingWithCreditBanner
            let creditBalanceBannerViewController = StoryboardScene.Home.mayaCreditBalanceBannerViewController.instantiate()
            let creditBalanceBannerViewModel = viewModel.generateCreditBalanceBannerViewModel()
            creditBalanceBannerViewController.delegate = self
            creditBalanceBannerViewController.routeActionDelegate = routeActionDelegate
            creditBalanceBannerViewController.navigationBarHidden = true
            creditBalanceBannerViewController.bind(creditBalanceBannerViewModel)
            easyCreditView.isHidden = true
            add(child: creditBalanceBannerViewController, to: easyCreditView, padding: 0)

            let creditApplicationBannerViewController = StoryboardScene.Home.mayaCreditApplicationBannerViewController.instantiate()
            let creditApplicationBannerViewModel = viewModel.generateCreditApplicationBannerViewModel()
            creditApplicationBannerViewController.routeActionDelegate = routeActionDelegate
            creditApplicationBannerViewController.navigationBarHidden = true
            creditApplicationBannerViewController.bind(creditApplicationBannerViewModel)
            easyCreditApplicationView.isHidden = true
            add(child: creditApplicationBannerViewController, to: easyCreditApplicationView, padding: 0)
        } else {
            walletBalanceStackView.spacing = 0
        }

        let tilesViewController = StoryboardScene.Home.mayaWalletServicesViewController.instantiate()
        let tilesViewModel = viewModel.generateServicesViewModel()
        tilesViewController.delegate = self
        tilesViewController.routeActionDelegate = routeActionDelegate
        tilesViewController.navigationBarHidden = true
        tilesViewController.bind(tilesViewModel)
        add(child: tilesViewController, to: tilesView, with: tilesView.bounds)

        // Add Google Banner Ads Carousel (Proof of Concept)
        setupGoogleBannerAdsCarousel()

        let viewController = StoryboardScene.Home.mayaRewardsViewController.instantiate()
        viewController.routeActionDelegate = routeActionDelegate
        viewController.delegate = self
        viewController.navigationBarHidden = true
        viewController.bind(viewModel.generateRewardsDataModel())

        rewardsViewHeightConstraint.constant = 114
        add(child: viewController, to: rewardsView, with: rewardsView.bounds)

        googleInterstitialAdsHelper.setup(from: self)
        googleInterstitialAdsHelper.addTestButton(to: view) // Optional: for testing

        viewModel.removeReKYCNudge()
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.Wallet.exit)
        super.viewWillAppear(animated)

        /// Sharing Clevertap delagate ownership with MayaShopHomeViewController.
        /// Reclaim Clevertap delagate ownership to receive ad banners in this module.
        /// Do this before sending trigger event.
        adCarouselViewController?.viewModel?.initializeAdBannerService()
        analyticsService.logMayaEvents(action: .appear(Analytics.Wallet()))
        googleInterstitialAdsHelper.startAutomaticAds()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        guard let viewModel = viewModel else { return }
        if viewModel.isExpandedKYCEnabled && !(viewModel.shouldShowEDD) {
            // Force to show Re-KYC screen if needed
            if viewModel.getExpandedReKYCAction() != nil {
                delegate?.showReKYCPrompt(self)
            }
        } else if !(viewModel.shouldShowEDD) {
            // Force to show Re-KYC reminder bottom sheet when ReKYC status is tagged or rejected
            if viewModel.shouldShowReKYCBottomSheet() {
                delegate?.showReKYCPrompt(self)
            }
        }
    }

    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        googleInterstitialAdsHelper.stopAutomaticAds()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        easyCreditView.layer.zPosition = 1
        walletBalanceView.layer.zPosition = 2
    }

    deinit {
        alertSnackBarTimer?.invalidate()
    }

    // NOTE: Temporary workaround for dispatch once import contacts
    lazy var didImportContacts: Bool = {
        viewModel?.importContactsAction.apply().start()
        return true
    }()

    // MARK: - Google Banner Ads Carousel Setup (Proof of Concept)

    private func setupGoogleBannerAdsCarousel() {
        // Create the Google banner ads carousel view controller using factory method
        googleBannerAdsCarouselViewController = GoogleBannerAdsCarouselViewController.createForDashboard()
        googleBannerAdsCarouselViewController?.delegate = self
        googleBannerAdsCarouselViewController?.navigationBarHidden = true

        // Create a container view for the Google banner ads carousel
        let googleBannerContainer = UIView()
        googleBannerContainer.translatesAutoresizingMaskIntoConstraints = false
        googleBannerContainer.backgroundColor = .clear

        // Add the container to the main stack view (below the CleverTap carousel)
        if let stackView = view.subviews.first(where: { $0 is UIStackView }) as? UIStackView {
            // Find the ad carousel view in the stack and add our container after it
            if let adCarouselIndex = stackView.arrangedSubviews.firstIndex(of: adCarouselView) {
                stackView.insertArrangedSubview(googleBannerContainer, at: adCarouselIndex + 1)
            } else {
                // Fallback: add to the end of the stack
                stackView.addArrangedSubview(googleBannerContainer)
            }
        }

        // Set height constraint for the Google banner container
        let heightConstraint = googleBannerContainer.heightAnchor.constraint(equalToConstant: 100) // Standard banner height
        heightConstraint.isActive = true

        // Add the Google banner ads carousel view controller as a child
        guard let googleBannerViewController = googleBannerAdsCarouselViewController else { return }
        add(child: googleBannerViewController, to: googleBannerContainer, with: googleBannerContainer.bounds)

        print("[MayaDashboardViewController] Google Banner Ads Carousel setup completed (Proof of Concept)")
    }
}

// MARK: ViewModelBindable Methods
extension MayaDashboardViewController: ViewModelBindable {
    fileprivate func setupTTFDObserver() {
        guard
            let viewModel = viewModel,
            let importContactsAction = viewModel.importContactsAction,
            let refreshDashboardAction = viewModel.refreshDashboardAction,
            let getReKYCDataAction = viewModel.getReKYCDataAction,
            let userProfileAction = viewModel.userProfileAction
        else { return }

        let importContactsSignal = Signal.merge(importContactsAction.completed.signal, importContactsAction.errors.signal.map({ _ in return })).take(first: 1)
        let refreshDashboardSignal = Signal.merge(refreshDashboardAction.completed.signal, refreshDashboardAction.errors.signal.map({ _ in return })).take(first: 1)
        let getReKYCDataSignal = Signal.merge(getReKYCDataAction.completed.signal, getReKYCDataAction.errors.signal.map({ _ in return })).take(first: 1)
        let userProfileSignal = Signal.merge(userProfileAction.completed.signal, userProfileAction.errors.signal.map({ _ in return })).take(first: 1)
        let clevertapBannerDisplayedSignal = clevertapBannerDisplayedSignal.signal.take(first: 1)

        let dashboardActions = Signal.combineLatest(importContactsSignal, refreshDashboardSignal, getReKYCDataSignal, userProfileSignal, clevertapBannerDisplayedSignal)

        dashboardActions.observe(on: UIScheduler()).observeCompleted { [weak self] in
            self?.stopTTFD()
            guard let self = self else { return }
            self.stopTTFD()
            self.monitoringDelegate?.didFinishTTFD(self)
        }?.addToDisposeBag(disposeBag)
    }

    func binding(_ viewModel: MayaDashboardViewModelProtocol?) {
        guard let viewModel = viewModel else { return }

        let adCarouselViewModel = viewModel.generateAdCarouselViewModel()
        adCarouselViewController?.bind(adCarouselViewModel)
        adCarouselViewHeightConstraint.constant = CGFloat(adCarouselViewModel.adHeight)

        bspFooterView.delegate = self

        viewModel.importContactsAction.completed.observe(on: UIScheduler()).observeValues { [weak self] _ in
            guard let self = self else { return }
            self.updateActivityView()
        }?.addToDisposeBag(disposeBag)

        viewModel.refreshDashboardAction.isExecuting.signal.observe(on: UIScheduler()).observeValues({ [weak self] isExecuting in
            guard let self = self else { return }
            self.updateActivityView(isExecuting: isExecuting)
            if !isExecuting {
                self.refreshControl.endRefreshing()
            } else {
                self.showServerErrorView(isHidden: true)
            }
        })?.addToDisposeBag(disposeBag)

        viewModel.refreshDashboardAction.completed.observe(on: UIScheduler()).observeValues { [weak self] in
            guard let self = self else { return }
            _ = self.didImportContacts
            self.showServerErrorView(isHidden: true)
            self.updateActivityView()
        }?.addToDisposeBag(disposeBag)

        viewModel.refreshDashboardAction.errors.observe(on: UIScheduler()).observeValues { [weak self] error in
            guard let self = self else { return }
            switch error {
            case .activityError:
                self.showActivityAlertSnackBar()
            case .balanceAndActivityErrorWithCache:
                self.showBalanceAndActivityAlertSnackBar()
            case .balanceErrorWithCache:
                self.showBalanceAlertSnackBar()
            case .balanceErrorWithoutCache, .noInternetConnection:
                self.showServerErrorView(isHidden: false)
            }
            self.updateActivityView()
        }?.addToDisposeBag(disposeBag)

        if viewModel.isEasyCreditBannerEnabled || viewModel.isEasyCreditBannerV2Enabled {
            viewModel.creditBannerState.signal.observe(on: UIScheduler()).observeValues { [weak self] creditBannerState in
                guard let self else { return }

                switch creditBannerState {
                case .apply:
                    self.easyCreditView.isHidden = true
                    self.easyCreditApplicationView.isHidden = false
                case .availableCredit:
                    self.easyCreditView.isHidden = false
                    self.easyCreditApplicationView.isHidden = true
                case .hidden:
                    self.easyCreditView.isHidden = true
                    self.easyCreditApplicationView.isHidden = true
                case .none: break
                }

                self.walletBalanceStackView.setNeedsLayout()
            }?.addToDisposeBag(disposeBag)
        }

        refreshControl.addTarget(self, action: #selector(refreshDashboard), for: .valueChanged)

        activeSignal.observe(on: UIScheduler()).startWithValues { [weak self] active in
            guard let self = self, active else { return }
            self.refreshDashboard()
        }.addToDisposeBag(disposeBag)

        viewModel.showReKyc.signal.observe(on: UIScheduler()).observeValues { [weak self] showReKYC in
            guard let self = self, let showReKYC = showReKYC, showReKYC else { return }
            self.delegate?.showReKYCPrompt(self)
        }

        viewModel.showExpandedReKyc.signal.observe(on: UIScheduler()).observeValues { [weak self] showReKYC in
            guard let self = self, let showReKYC = showReKYC, showReKYC else { return }
            if !(viewModel.shouldShowEDD) {
                self.delegate?.showReKYCPrompt(self)
            }
        }

        viewModel.getReKYCDataAction.completed.observe(on: UIScheduler()).observeValues { [weak self] in
            guard let self = self else { return }
            self.viewModel?.userProfileAction.apply().start()
        }?.addToDisposeBag(disposeBag)

        if shouldMonitorScreenPerformance && shouldOverrideTTFDMetric {
            setupTTFDObserver()
        }
    }
}

// MARK: AdCarouselViewControllerDelegate Methods
extension MayaDashboardViewController: AdCarouselViewControllerDelegate {
    func didTapAdBanner(_ viewController: ViewController, link: URL) {
        delegate?.didTapAdBanner(self, link: link)
    }

    func didChangeBannerState(_ viewController: ViewController, state: AdBannerState) {
        if case .failed = state {
            if !adCarouselView.isHidden {
                adCarouselView.hide()
                view.setNeedsLayout()
            }
        } else {
            if adCarouselView.isHidden {
                adCarouselView.show(animated: true)
                view.setNeedsLayout()
            }
        }
        if state.didSucceedOrFail {
            clevertapBannerDisplayedObserver.send(value: ())
        }
    }
}

// MARK: - GoogleBannerAdsCarouselDelegate Methods
extension MayaDashboardViewController: GoogleBannerAdsCarouselViewController.GoogleBannerAdsCarouselDelegate {
    func didTapGoogleBanner(_ viewController: GoogleBannerAdsCarouselViewController, bannerView: GADBannerView) {
        // Handle Google banner tap - for now just log it
        print("[MayaDashboardViewController] Google banner tapped")
        // You can add analytics or navigation logic here
    }

    func didChangeBannerState(_ viewController: GoogleBannerAdsCarouselViewController, state: GoogleBannerState) {
        // Handle Google banner state changes
        print("[MayaDashboardViewController] Google banner state changed to: \(state)")
        // You can add UI updates or error handling here
    }
}

// MARK: MayaDefaultErrorViewDelegate Methods
extension MayaDashboardViewController: MayaDefaultErrorViewDelegate {
    func didTapRetry(_ view: MayaDefaultErrorView) {
        refreshDashboard()
    }
}

// MARK: UIScrollViewDelegate Methods
extension MayaDashboardViewController: UIScrollViewDelegate {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        refreshControl.containingScrollDidScroll(scrollView)
    }
}

// MARK: Private Methods
fileprivate extension MayaDashboardViewController {
    @objc func refreshDashboard() {
        guard let viewModel = viewModel else { return }
        if viewModel.isReachable(), viewModel.isLoggedIn {
            if adCarouselView.isHidden {
                adCarouselView.show(animated: true)
            }
            adCarouselViewController?.refreshBanner()
            viewModel.refreshDashboardAction.apply().start()

            if viewModel.isActionCardsEnabled {
                viewModel.getActionCardsAction.apply().start()
            }

            if viewModel.isExpandedKYCEnabled {
                viewModel.getReKYCDataAction.apply().start()
            } else {
                viewModel.userProfileAction.apply().start()
            }

            if viewModel.isEasyCreditBannerV2Enabled {
                let rrn = UUID().uuidString.lowercased()
                viewModel.getMayaCreditAccountStatusAction.apply(rrn).start()
            } else if viewModel.isEasyCreditBannerEnabled {
                viewModel.getMayaCreditLoanApplicationAction.apply().start()
            }
        } else {
            refreshControl.endRefreshing()
            if viewModel.balanceProperty.value != nil {
                showServerErrorView(isHidden: true)
                showBalanceAndActivityAlertSnackBar()
            } else {
                showServerErrorView(isHidden: false)
            }
        }
    }

    func updateActivityView(isExecuting: Bool = false) {
        guard let viewModel = viewModel else { return }
        if viewModel.hasNoActivities() && !isExecuting {
            showNoActivityView()
        } else {
            hideNoActivityView()
            if let latestActivitiesViewController = latestActivitiesViewController {
                latestActivitiesViewController.viewModel?.updateActivityListGroup()
            }
        }
    }

    func showNoActivityView() {
        if let latestActivitiesViewController = latestActivitiesViewController {
            remove(child: latestActivitiesViewController)
            latestActivitiesView.removeSubViews()
            self.latestActivitiesViewController = nil
        }

        if noActivityViewController == nil {
            noActivityViewController = StoryboardScene.Home.mayaNoActivityViewController.instantiate()
            noActivityViewController?.routeActionDelegate = routeActionDelegate
            noActivityViewController?.delegate = self
            noActivityViewController?.navigationBarHidden = true
            add(child: noActivityViewController!, to: latestActivitiesView, with: latestActivitiesView.bounds)
            activityContainerViewHeightConstraint.constant = 162.0
        }
    }

    func hideNoActivityView() {
        if let noActivityViewController = noActivityViewController {
            remove(child: noActivityViewController)
            latestActivitiesView.removeSubViews()
            self.noActivityViewController = nil
        }

        if latestActivitiesViewController == nil {
            latestActivitiesViewController = StoryboardScene.Home.mayaLatestActivitiesViewController.instantiate()
            latestActivitiesViewController?.routeActionDelegate = routeActionDelegate
            latestActivitiesViewController?.delegate = self
            latestActivitiesViewController?.navigationBarHidden = true
            if let viewModel = viewModel {
                latestActivitiesViewController?.bind(viewModel.generateMayaActivitiesViewModel())
            }
            add(child: latestActivitiesViewController!, to: latestActivitiesView, with: latestActivitiesView.bounds)
            activityContainerViewHeightConstraint.constant = latestActivitiesViewController!.activitiesTableViewHeightConstraint.constant + 68.5
        }
    }

    func showServerErrorView(isHidden: Bool) {
        serverErrorView.isHidden = isHidden
        dashboardStackView.isHidden = !isHidden
    }

    func showActivityAlertSnackBar() {
        alertSnackBar.set(message: L10n.Dashboard.ErrorSpiel.activity)
        showAlertSnackBar()
    }

    func showBalanceAlertSnackBar() {
        alertSnackBar.set(message: L10n.Dashboard.ErrorSpiel.balance)
        showAlertSnackBar()
    }

    func showBalanceAndActivityAlertSnackBar() {
        alertSnackBar.set(message: L10n.Dashboard.ErrorSpiel.balanceAndActivity)
        showAlertSnackBar()
    }

    func showAlertSnackBar() {
        if alertSnackBar.isHidden {
            alertSnackBar.alpha = 1
            UIView.animate(withDuration: alertSnackBarAnimationDuration, animations: {
                self.alertSnackBar.isHidden = false
                self.mainStackView.layoutIfNeeded()
            }, completion: { _ in
                self.alertSnackBarTimer = Timer.scheduledTimer(timeInterval: alertSnackBarDisplayDuration, target: self, selector: #selector(MayaDashboardViewController.hideAlertSnackBar), userInfo: nil, repeats: false)
            })
        }
    }

    @objc func hideAlertSnackBar() {
        alertSnackBarTimer?.invalidate()
        if !alertSnackBar.isHidden {
            UIView.animate(withDuration: alertSnackBarAnimationDuration, animations: {
                self.alertSnackBar.isHidden = true
                self.mainStackView.layoutIfNeeded()
            }, completion: { _ in
                self.alertSnackBar.alpha = 0
            })
        }
    }
}

// MARK: MayaLatestActivitiesViewControllerDelegate Methods
extension MayaDashboardViewController: MayaLatestActivitiesViewControllerDelegate {
    func didTapViewAll(_ viewController: MayaLatestActivitiesViewController) {
        analyticsService.logMayaEvents(action: .tap(Analytics.Wallet.item), attributes: AnalyticsConstants.Wallet.Item.seeAllTransaction.attributes.rawValue)

        delegate?.didTapSeeAllActivities(self)
    }

    func didLayoutActivities(_ viewController: MayaLatestActivitiesViewController, height: CGFloat) {
        // Height of header (48.5) + margin (20) = 68.5
        activityContainerViewHeightConstraint.constant = height + 68.5
    }

    func didTapMayaActivityDetails(_ viewController: MayaLatestActivitiesViewController, viewModel: MayaActivityDetailsViewModelProtocol) {
        delegate?.didTapMayaActivityDetails(self, viewModel: viewModel)
    }
}

// MARK: - NoActivitiesViewControllerDelegate Methods
extension MayaDashboardViewController: MayaNoActivityViewControllerDelegate {
    func didTapAddMoneyNow(_ viewController: MayaNoActivityViewController) {
        delegate?.didTapAddMoney(self)
    }
}

// MARK: - MayaRewardsViewControllerDelegate Methods
extension MayaDashboardViewController: MayaRewardsViewControllerDelegate {
    func didTapMissions(_ viewController: MayaRewardsViewController) {
        delegate?.didTapMissions(self)

        analyticsService.logMayaEvents(.tap(Analytics.Wallet.item), attributes: AnalyticsConstants.Wallet.Item.missions.attributes)
    }

    func didTapVouchers(_ viewController: MayaRewardsViewController) {
        delegate?.didTapVouchers(self)

        analyticsService.logMayaEvents(.tap(Analytics.Wallet.item), attributes: AnalyticsConstants.Wallet.Item.rewards.attributes)
    }
}

// MARK: - MayaBSPViewDelegate Methods
extension MayaDashboardViewController: MayaBSPViewDelegate {
    func didTapHelpCenter(_ view: MayaBSPView) {
        analyticsService.logMayaEvents(action: .tap(Analytics.Wallet.item), attributes: AnalyticsConstants.Wallet.Item.helpCenter.attributes.rawValue)

        if let viewModel = viewModel,
           let helpUrl = viewModel.helpUrl {
            delegate?.didTapHelpCenter(self, url: helpUrl)
        }
    }

    func didTapBSPWebsite(_ view: MayaBSPView, url: URL) {
        analyticsService.logMayaEvents(action: .tap(Analytics.Wallet.item), attributes: AnalyticsConstants.Wallet.Item.bsp.attributes.rawValue)

        routeActionDelegate?.didRequestWebView(self, with: url, willPresent: true, completion: nil)
    }

    func didTapMayaServiceAdvisories(_ view: MayaBSPView, url: URL) {
        analyticsService.logMayaEvents(action: .tap(Analytics.Wallet.item), attributes: AnalyticsConstants.Wallet.Item.serviceAdvisories.attributes.rawValue)

        routeActionDelegate?.didRequestWebView(self, with: url, willPresent: true, completion: nil)
    }
}

// MARK: - MayaWalletBalanceViewControllerDelegate Methods
extension MayaDashboardViewController: MayaWalletBalanceViewControllerDelegate {
    func didTapCashIn(_ viewController: MayaWalletBalanceViewController) {
        delegate?.didTapAddMoney(self)
    }

    func didTapSendMoney(_ viewController: MayaWalletBalanceViewController) {
        delegate?.didTapSendMoney(self)
    }

    func didTapWalletOptions(_ viewController: MayaWalletBalanceViewController) {
        delegate?.didTapWalletSettingsService(self)
    }

    func didTapActionCard(_ viewController: MayaWalletBalanceViewController, url: URL) {
        delegate?.didTapActionCard(self, url: url)
    }
}

// MARK: - MayaEasyCreditCardViewControllerDelegate Methods
extension MayaDashboardViewController: MayaCreditBalanceBannerViewControllerDelegate {
    func didTapCreditBalanceBanner(_ viewController: MayaCreditBalanceBannerViewController, account: MayaCreditAccount, feeRate: Float?) {
        delegate?.didTapCreditBalanceBanner(self, account: account, feeRate: feeRate)
    }
}

// MARK: - MayaWalletServicesViewControllerDelegate Methods
extension MayaDashboardViewController: MayaWalletServicesViewControllerDelegate {
    func didTapWalletService(_ viewController: MayaWalletServicesViewController, service: Service) {
        delegate?.didTapWalletService(self, service: service)
    }
}
