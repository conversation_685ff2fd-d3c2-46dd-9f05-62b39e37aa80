//
//  MayaAdCarouselViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 2/17/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import ConfigurationService
import Injector
import ReactiveSwift
import UIKit

enum AdBannerState {
    case success
    case failed
    case unloaded

    var didSucceedOrFail: Bool {
        return (self == .success) || (self == .failed)
    }
}

protocol AdCarouselViewControllerDelegate: AnyObject {
    func didTapAdBanner(_ viewController: ViewController, link: URL)
    func didChangeBannerState(_ viewController: ViewController, state: AdBannerState)
}

class MayaAdCarouselViewController: ViewController, AnalyticsProtocol {
    override var shouldUseMayaNavigationBar: Bool {
        return true
    }

    override var shouldUseMayaArrowBackButton: Bool {
        return false
    }

    @IBOutlet weak var collectionView: InfiniteCollectionView! {
        didSet {
            collectionView.infiniteDelegate = self
            collectionView.infiniteDataSource = self
            collectionView.autoscrollTimeInterval = viewModel?.rotationIntervalProperty.value ?? Constants.CleverTapRotationIntervalDefault
            collectionView.contentInset = UIEdgeInsets(top: 0, left: AdBannerProperties.defaultHorizontalPadding, bottom: 0, right: AdBannerProperties.defaultHorizontalPadding)
        }
    }

    @IBOutlet weak var pageControl: UIPageControl!
    @IBOutlet weak var pageControlHeightConstraint: NSLayoutConstraint!

    var timer: Timer?
    var isTimeoutElapsed: Bool = false

    var bannerBackgroundColor: UIColor = .clear
    var tapAnalyticsModule: AnalyticsModule = Analytics.Wallet.banner // Maya 2.0: Edit analytics to specified

    weak var delegate: AdCarouselViewControllerDelegate?

    var adBannerFrame: CGRect {
        guard let viewModel = viewModel else {
            return CGRect(origin: CGPoint.zero, size: CGSize(width: AdBannerProperties.defaultWidth, height: AdBannerProperties.defaultHeight))
        }
        return CGRect(origin: CGPoint.zero,
                    size: CGSize(width: viewModel.adWidth, height: viewModel.adHeight))
    }

    override class var instanceCount: Int {
        // FIXME: consider to depend on categoriesCount()
        return AdCarouselViewModel.baseAdModuleCount + AdCarouselViewModel.shopCategoriesInstanceCount
    }

    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        guard let viewModel = viewModel else { return }
        collectionView.isAutoscrollEnabled = viewModel.isAutoscrollEnabled
    }

    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        collectionView.isAutoscrollEnabled = false
    }

    deinit {
        timer?.invalidate()
    }

    override func viewDidLoad() {
        super.viewDidLoad()

        setupPageControl()
    }

    func refreshBanner() {
        timer?.invalidate()
        isTimeoutElapsed = false
        timer = Timer.scheduledTimer(withTimeInterval: 5, repeats: false, block: { [weak self] _ in
            self?.checkLoadingOfBanner(true)
        })
        checkLoadingOfBanner()
    }

    private func getCurrentCenterCellIndexPath() -> IndexPath? {
        let visibleRect = CGRect(origin: collectionView.contentOffset, size: collectionView.bounds.size)
        let visiblePoint = CGPoint(x: visibleRect.midX, y: visibleRect.midY)

        guard let indexPath = collectionView.indexPathForItem(at: visiblePoint) else {
            return nil
        }
        return indexPath
    }

    private func setupPageControl() {
        guard let module = viewModel?.module, module.hasPageIndicator else {
            showPageControl(show: false)
            return
        }
        pageControl.currentPage = 0
        showPageControl(show: true)
    }

    private func showPageControl(show: Bool) {
        guard let module = viewModel?.module, show, module.hasPageIndicator else {
            pageControl.isHidden = true
            pageControlHeightConstraint.constant = 0
            return
        }

        pageControlHeightConstraint.constant = 26
        pageControl.isHidden = false

        if #available(iOS 16.0, *) {
            let unselectedImage = Asset.MayaImages.Dashboard.pageControlUnselected.image
            let selectedImage = Asset.MayaImages.Dashboard.pageControlSelected.image

            pageControl.preferredIndicatorImage = unselectedImage
            pageControl.preferredCurrentPageIndicatorImage = selectedImage
        }
    }
}

// MARK: - ViewModelBindable Methods
extension MayaAdCarouselViewController: ViewModelBindable {
    func binding(_ viewModel: AdCarouselViewModelProtocol?) {
        guard let viewModel = viewModel else { return }
        viewModel.adBannersProperty.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] banners in
                guard let self = self else { return }
                self.checkLoadingOfBanner()
                if viewModel.module.hasPageIndicator {
                    if banners.count > 1 {
                        self.showPageControl(show: true)
                        self.pageControl?.numberOfPages = banners.count
                    } else {
                        self.showPageControl(show: false)
                    }
                }
            }?.addToDisposeBag(disposeBag)

        collectionView.reactive.reloadData <~ viewModel.adBannersProperty.signal.skipRepeats().observe(on: UIScheduler()).map { _ in }

        if viewModel.isCleverTapUnliBannersEnabled {
            viewModel.rotationIntervalProperty.signal
                .observe(on: UIScheduler())
                .observeValues { [weak self] interval in
                    guard let self = self else { return }
                    collectionView.autoscrollTimeInterval = interval
                    collectionView.stopAutoscrollTimer()
                    collectionView.isAutoscrollEnabled = true
                }?.addToDisposeBag(disposeBag)
        }
    }
}

// MARK: - Private Methods
fileprivate extension MayaAdCarouselViewController {
    static var bannerSize: CGSize {
        let scale = UIScreen.main.scale
        return CGSize(width: AdBannerProperties.defaultWidth * scale, height: AdBannerProperties.defaultHeight * scale)
    }

    static var bannerFrame: CGRect {
        return CGRect(x: 0, y: 0, width: AdBannerProperties.defaultWidth, height: AdBannerProperties.defaultHeight)
    }

    func checkLoadingOfBanner(_ triggeredFromTimer: Bool = false) {
        if !collectionView.isAutoscrollEnabled {
            dispatchToMain { [weak self] in
                self?.collectionView.reloadData()
            }
        }

        guard let viewModel = viewModel else { return }
        if triggeredFromTimer, viewModel.adBannersProperty.value.isEmpty {
            isTimeoutElapsed = true
        }
        if isTimeoutElapsed {
            delegate?.didChangeBannerState(self, state: .failed)
        } else if viewModel.adBannersProperty.value.isEmpty {
            delegate?.didChangeBannerState(self, state: .unloaded)
        } else {
            delegate?.didChangeBannerState(self, state: .success)
        }
    }
}

// MARK: - InfiniteCollectionViewDataSource Methods
extension MayaAdCarouselViewController: InfiniteCollectionViewDataSource {
    func numberOfItems(_ collectionView: UICollectionView) -> Int {
        guard let viewModel = viewModel else { return 0 }
        return viewModel.adBannersProperty.value.count > 0 ? viewModel.adBannersProperty.value.count : 1
    }

    func cellForItemAtIndexPath(_ collectionView: UICollectionView, dequeueIndexPath: IndexPath, usableIndexPath: IndexPath) -> UICollectionViewCell {
        guard let viewModel = viewModel else { return UICollectionViewCell() }
        var cell: MayaAdBannerCollectionViewCell?

        let adBanners = viewModel.adBannersProperty.value
        switch viewModel.module {
        case .creatorStore, .creatorStoreBannerV2:
            cell = collectionView.dequeueCell(of: MayaCreatorStoreAdBannerCollectionViewCell.self, for: usableIndexPath)
            if let banner = adBanners[safeIndex: usableIndexPath.row] {
                cell?.bind(banner)
            }
        default:
            cell = collectionView.dequeueReusableCell(withReuseIdentifier: viewModel.module.cellReuseIdentifier, for: dequeueIndexPath) as? MayaAdBannerCollectionViewCell
            if let banner = adBanners[safeIndex: usableIndexPath.row] {
                cell?.bind(banner)
                cell?.errorImage = viewModel.module.errorImage
            }
        }

        // (this is more like a hack) Setting a tag here because getting the exact page of the centered collection view cell in an infinite scrollview is, how do i say this, too tedious or too hard? believe me I've tried, too much math! besides this is accurate. i have the exact indexPath here, might as well use it.
        cell?.tag = usableIndexPath.row

        if adBanners.count > 0 {
            cell?.bannerView.hideSkeleton()
            cell?.bannerView.backgroundColor = bannerBackgroundColor
        } else {
            cell?.bannerView.showAnimatedGradientSkeleton()
        }
        return cell ?? UICollectionViewCell()
    }
}

// MARK: - InfiniteCollectionViewDataSource Methods
extension MayaAdCarouselViewController: InfiniteCollectionViewDelegate {
    func didSelectCellAtIndexPath(_ collectionView: UICollectionView, usableIndexPath: IndexPath) {
        guard let viewModel = viewModel else { return }
        guard let banner = viewModel.adBannersProperty.value[safeIndex: usableIndexPath.row] else { return }

        viewModel.sendAnalyticsForClickedEvent()

        var additionalAttributes: [AnalyticsAttributeKey: Any] = viewModel.additionalTapAnalyticsAttributes ?? [:]
        let initialAttributes: [AnalyticsAttributeKey: Any] = [ .numberOfBanners: viewModel.adBannersProperty.value.count,
                                                                .position: usableIndexPath.row + 1, // Starts at 1
                                                                .type: "CT\(banner.tag)"]
        var attributes = additionalAttributes.merged(with: initialAttributes)

        if let actionURLString = banner.actionURLString,
            let actionURL = URL(string: actionURLString) {
            attributes[.deeplink] = actionURLString

            if let analyticsAction = AnalyticsHelper.getAnalyticsAction(for: actionURL) {
                analyticsHelper.setAttribution(source: .ctNativeAdDashboard, for: analyticsAction)
            }
            delegate?.didTapAdBanner(self, link: actionURL)
        }

        if ContainerWrapper.shared.resolve(ConfigurationServiceV2.self).cleverTapUnliBannersEnabled.value {
            attributes[.name] = banner.name ?? banner.bannerKey ?? ""
            if let priority = banner.priority {
                attributes[.priority] = priority
            }
            if let url = banner.actionURLString {
                attributes[.url] = url
            }
        }

        analyticsUtils.logMayaEvents(.tap(tapAnalyticsModule), attributes: attributes)
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return adBannerFrame.size
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return viewModel?.module.adBannerSpacing ?? AdBannerProperties.defaultSpacing
    }

    func didSnapToNextElement(_ collectionView: UICollectionView) {
        guard let viewModel = viewModel, let indexPath = getCurrentCenterCellIndexPath(), viewModel.module.hasPageIndicator else { return }
        let cell = collectionView.cellForItem(at: indexPath)
        if let page = cell?.tag {
            pageControl.currentPage = page
        }
    }
}
