//
//  GoogleBannerAdsCarouselViewController.swift
//  PayMaya
//
//  Created by Augment Agent on 12/19/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import GoogleMobileAds
import ReactiveSwift
import UIKit
import Injector

/// View controller that displays multiple Google banner ads in a carousel format
/// Follows the same pattern as MayaAdCarouselViewController for CleverTap ads
class GoogleBannerAdsCarouselViewController: ViewController, ViewModelBindable {
    
    // MARK: - Constants
    private enum Constants {
        static let defaultScrollInterval: TimeInterval = 5.0
        static let animationDuration: TimeInterval = 0.25
    }
    
    // MARK: - Protocols
    protocol GoogleBannerAdsCarouselDelegate: AnyObject {
        func didTapGoogleBanner(_ viewController: GoogleBannerAdsCarouselViewController, bannerView: BannerView)
        func didChangeBannerState(_ viewController: GoogleBannerAdsCarouselViewController, state: GoogleBannerState)
    }
    
    // MARK: - Properties
    @Inject var googleInterstitialAdsHelper: GoogleInterstitialAdsHelper
    
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.translatesAutoresizingMaskIntoConstraints = false
        cv.delegate = self
        cv.dataSource = self
        cv.isPagingEnabled = true
        cv.showsHorizontalScrollIndicator = false
        cv.backgroundColor = .clear
        cv.register(GoogleBannerAdCollectionViewCell.self, forCellWithReuseIdentifier: GoogleBannerAdCollectionViewCell.identifier)
        return cv
    }()
    
    private var bannerViews: [BannerView] = []
    private var currentItem = 0
    private var autoScrollTimer: Timer?
    
    // Configuration properties
    private var autoScrollDelayMs: TimeInterval = Constants.defaultScrollInterval
    private var isAutoScrollEnabled = true
    private var endlessScrolling = true
    
    // Delegate
    weak var delegate: GoogleBannerAdsCarouselDelegate?

    // View Model
    private var viewModel: GoogleBannerAdsCarouselViewModelProtocol?
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupView()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        startAutoScrollIfNeeded()
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        stopAutoScroll()
    }
    
    deinit {
        stopAutoScroll()
    }
    
    // MARK: - Setup
    private func setupView() {
        view.addSubview(collectionView)
        NSLayoutConstraint.activate([
            collectionView.topAnchor.constraint(equalTo: view.topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
        
        setupGestureHandling()
    }
    
    private func setupGestureHandling() {
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePanGesture(_:)))
        collectionView.addGestureRecognizer(panGesture)
    }
    
    @objc private func handlePanGesture(_ gesture: UIPanGestureRecognizer) {
        switch gesture.state {
        case .began:
            stopAutoScroll()
            // Disable parent scroll view if needed
            if let scrollView = findParentScrollView() {
                scrollView.isScrollEnabled = false
            }
        case .ended, .cancelled:
            // Re-enable parent scroll view
            if let scrollView = findParentScrollView() {
                scrollView.isScrollEnabled = true
            }
            resetAutoScroll()
        default:
            break
        }
    }
    
    private func findParentScrollView() -> UIScrollView? {
        var currentView: UIView? = view.superview
        while let view = currentView {
            if let scrollView = view as? UIScrollView {
                return scrollView
            }
            currentView = view.superview
        }
        return nil
    }
    
    // MARK: - Public Methods
    
    /// Load multiple banner ads into the carousel
    /// - Parameters:
    ///   - adUnitIds: List of ad unit IDs to display in the carousel
    ///   - adSize: The size of the banner ads (default: GADAdSizeBanner)
    ///   - autoScrollDelay: Delay in seconds between auto-scrolling (default 5.0)
    func loadAds(
        adUnitIds: [String],
        adSize: AdSize = AdSizeBanner,
        autoScrollDelay: TimeInterval = Constants.defaultScrollInterval
    ) {
        // Clear existing ads
        bannerViews.removeAll()
        autoScrollDelayMs = autoScrollDelay
        
        var loadedCount = 0
        let totalCount = adUnitIds.count
        
        // Create banner views for each ad unit ID
        for adUnitId in adUnitIds {
            let bannerView = BannerView(adSize: adSize)
            bannerView.adUnitID = adUnitId
            bannerView.rootViewController = self
            bannerView.delegate = GoogleBannerAdDelegate(
                onAdLoaded: { [weak self] in
                    loadedCount += 1
                    if loadedCount == totalCount {
                        DispatchQueue.main.async {
                            self?.delegate?.didChangeBannerState(self!, state: .success)
                        }
                    }
                },
                onAdFailedToLoad: { [weak self] error in
                    DispatchQueue.main.async {
                        self?.delegate?.didChangeBannerState(self!, state: .failed)
                    }
                }
            )
            
            bannerViews.append(bannerView)
            
            // Load the ad
            let request = Request()
            bannerView.load(request)
        }
        
        // Reload collection view
        DispatchQueue.main.async { [weak self] in
            self?.collectionView.reloadData()
            self?.setupInfiniteScrolling()
            self?.startAutoScrollIfNeeded()
        }
    }
    
    /// Refresh the banner ads
    func refreshBanner() {
        // Reload all banner ads
        for bannerView in bannerViews {
            let request = Request()
            bannerView.load(request)
        }
    }
    
    /// Enable or disable auto-scrolling
    func setAutoScrollEnabled(_ enabled: Bool) {
        isAutoScrollEnabled = enabled
        if enabled {
            startAutoScroll()
        } else {
            stopAutoScroll()
        }
    }
    
    /// Set the delay between auto-scrolling transitions
    func setAutoScrollDelay(_ delaySeconds: TimeInterval) {
        autoScrollDelayMs = delaySeconds
        resetAutoScroll()
    }
    
    /// Enable or disable endless scrolling
    func setEndlessScrolling(_ enabled: Bool) {
        if endlessScrolling != enabled {
            endlessScrolling = enabled
            collectionView.reloadData()
        }
    }
    
    /// Get the number of banner ads
    func getBannerCount() -> Int {
        return bannerViews.count
    }

    // MARK: - ViewModelBindable
    func bind(_ viewModel: GoogleBannerAdsCarouselViewModelProtocol) {
        self.viewModel = viewModel

        guard viewModel.isEnabled else {
            delegate?.didChangeBannerState(self, state: .unloaded)
            return
        }

        loadAds(
            adUnitIds: viewModel.adUnitIds,
            adSize: viewModel.adSize,
            autoScrollDelay: viewModel.autoScrollDelay
        )
    }

    // MARK: - Private Methods

    private func setupInfiniteScrolling() {
        guard endlessScrolling && bannerViews.count > 1 else { return }

        // Start from a position that allows infinite scrolling both ways
        let middleIndex = (Int.max / 2 / bannerViews.count) * bannerViews.count
        currentItem = middleIndex

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            let indexPath = IndexPath(item: self.currentItem, section: 0)
            self.collectionView.scrollToItem(at: indexPath, at: .centeredHorizontally, animated: false)
        }
    }

    private func startAutoScrollIfNeeded() {
        guard isAutoScrollEnabled && bannerViews.count > 1 else { return }
        startAutoScroll()
    }

    private func startAutoScroll() {
        stopAutoScroll()

        autoScrollTimer = Timer.scheduledTimer(withTimeInterval: autoScrollDelayMs, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            self.scrollToNextItem()
        }
    }

    private func stopAutoScroll() {
        autoScrollTimer?.invalidate()
        autoScrollTimer = nil
    }

    private func resetAutoScroll() {
        if isAutoScrollEnabled {
            startAutoScroll()
        }
    }

    private func scrollToNextItem() {
        guard bannerViews.count > 1 else { return }

        currentItem += 1
        let indexPath = IndexPath(item: currentItem, section: 0)

        DispatchQueue.main.async { [weak self] in
            self?.collectionView.scrollToItem(at: indexPath, at: .centeredHorizontally, animated: true)
        }
    }
}

// MARK: - GoogleBannerState
enum GoogleBannerState {
    case success
    case failed
    case unloaded

    var didSucceedOrFail: Bool {
        return (self == .success) || (self == .failed)
    }
}

// MARK: - UICollectionViewDataSource
extension GoogleBannerAdsCarouselViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if endlessScrolling && bannerViews.count > 1 {
            return Int.max
        } else {
            return bannerViews.count
        }
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: GoogleBannerAdCollectionViewCell.identifier, for: indexPath) as! GoogleBannerAdCollectionViewCell

        let realPosition = endlessScrolling ? indexPath.item % bannerViews.count : indexPath.item

        if realPosition < bannerViews.count {
            let bannerView = bannerViews[realPosition]
            cell.configure(with: bannerView)
        }

        return cell
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension GoogleBannerAdsCarouselViewController: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return collectionView.bounds.size
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let realPosition = endlessScrolling ? indexPath.item % bannerViews.count : indexPath.item

        if realPosition < bannerViews.count {
            let bannerView = bannerViews[realPosition]
            delegate?.didTapGoogleBanner(self, bannerView: bannerView)
        }
    }
}

// MARK: - UIScrollViewDelegate
extension GoogleBannerAdsCarouselViewController: UIScrollViewDelegate {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        let pageWidth = scrollView.frame.width
        let currentPage = Int((scrollView.contentOffset.x + pageWidth / 2) / pageWidth)

        if currentPage != currentItem {
            currentItem = currentPage
        }
    }

    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        stopAutoScroll()
    }

    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if !decelerate {
            resetAutoScroll()
        }
    }

    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        resetAutoScroll()
    }
}

// MARK: - GoogleBannerAdCollectionViewCell
private class GoogleBannerAdCollectionViewCell: UICollectionViewCell {
    static let identifier = "GoogleBannerAdCollectionViewCell"

    private var bannerView: BannerView?

    override func prepareForReuse() {
        super.prepareForReuse()
        bannerView?.removeFromSuperview()
        bannerView = nil
    }

    func configure(with bannerView: BannerView) {
        // Remove from previous parent if necessary
        bannerView.removeFromSuperview()

        self.bannerView = bannerView
        contentView.addSubview(bannerView)

        bannerView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            bannerView.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            bannerView.centerYAnchor.constraint(equalTo: contentView.centerYAnchor),
            bannerView.widthAnchor.constraint(lessThanOrEqualTo: contentView.widthAnchor),
            bannerView.heightAnchor.constraint(lessThanOrEqualTo: contentView.heightAnchor)
        ])
    }
}

// MARK: - GoogleBannerAdDelegate
private class GoogleBannerAdDelegate: NSObject, BannerViewDelegate {
    private let onAdLoaded: () -> Void
    private let onAdFailedToLoad: (Error) -> Void

    init(onAdLoaded: @escaping () -> Void, onAdFailedToLoad: @escaping (Error) -> Void) {
        self.onAdLoaded = onAdLoaded
        self.onAdFailedToLoad = onAdFailedToLoad
        super.init()
    }

    func bannerViewDidReceiveAd(_ bannerView: BannerView) {
        onAdLoaded()
    }

    func bannerView(_ bannerView: BannerView, didFailToReceiveAdWithError error: Error) {
        onAdFailedToLoad(error)
    }

    func bannerViewDidRecordImpression(_ bannerView: BannerView) {
        // Handle impression if needed
    }

    func bannerViewWillPresentScreen(_ bannerView: BannerView) {
        // Handle screen presentation if needed
    }

    func bannerViewWillDismissScreen(_ bannerView: BannerView) {
        // Handle screen dismissal if needed
    }

    func bannerViewDidDismissScreen(_ bannerView: BannerView) {
        // Handle screen dismissed if needed
    }
}

// MARK: - Factory Methods
extension GoogleBannerAdsCarouselViewController {

    /// Create a Google banner ads carousel view controller for dashboard usage
    static func createForDashboard() -> GoogleBannerAdsCarouselViewController {
        let viewController = GoogleBannerAdsCarouselViewController()
        let viewModel = GoogleBannerAdsCarouselViewModel.forDashboard()
        viewController.bind(viewModel)
        return viewController
    }

    /// Create a Google banner ads carousel view controller with custom configuration
    static func createCustom(
        adUnitIds: [String],
        adSize: AdSize = AdSizeBanner,
        autoScrollDelay: TimeInterval = 5.0,
        isEnabled: Bool = true
    ) -> GoogleBannerAdsCarouselViewController {
        let viewController = GoogleBannerAdsCarouselViewController()
        let viewModel = GoogleBannerAdsCarouselViewModel.custom(
            adUnitIds: adUnitIds,
            adSize: adSize,
            autoScrollDelay: autoScrollDelay,
            isEnabled: isEnabled
        )
        viewController.bind(viewModel)
        return viewController
    }
}
