import GoogleMobileAds
import UIKit

// MARK: - Google Banner Ads Carousel
/// A carousel of adaptive Google Mobile Ads banner units.
///
/// This view controller hosts multiple `BannerPageViewController` instances inside
/// a `UIPageViewController` to present a horizontally scrolling sequence of
/// banner ads. It automatically advances to the next banner every few seconds
/// via a `Timer`, which is started in `viewWillAppear` and invalidated in
/// `viewWillDisappear` to conserve resources when the view is off-screen.
final class GoogleBannerAdsCarouselViewController: ViewController {
    // MARK: Dependencies
    private let viewModel: GoogleBannerAdsCarouselViewModelProtocol

    private lazy var pages: [BannerPageViewController] = {
        viewModel.adUnitIds.enumerated().map { idx, id in
            let page = BannerPageViewController(adUnitId: id, adSize: viewModel.adSize)
            page.view.tag = idx
            return page
        }
    }()

    // MARK: UI
    private var pageVC: UIPageViewController?

    // MARK: State
    private var currentIndex = 0
    private var autoScrollTimer: Timer?

    // MARK: Initializer
    init(viewModel: GoogleBannerAdsCarouselViewModelProtocol) {
        self.viewModel = viewModel
        super.init(nibName: nil, bundle: nil)
    }

    @available(*, unavailable)
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: Lifecycle

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        stopAutoScroll()
    }

    // MARK: Setup
    func loadAds() {
        let pageVC = UIPageViewController(
            transitionStyle: .scroll,
            navigationOrientation: .horizontal,
            options: nil
        )

        self.pageVC = pageVC

        pageVC.dataSource = self

        addChild(pageVC)
        view.addSubview(pageVC.view)
        pageVC.view.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            pageVC.view.widthAnchor.constraint(equalToConstant: viewModel.adSize.size.width),
            pageVC.view.heightAnchor.constraint(equalToConstant: viewModel.adSize.size.height),
            pageVC.view.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            pageVC.view.topAnchor.constraint(equalTo: view.topAnchor)
        ])
        pageVC.didMove(toParent: self)

        if let first = pages.first {
            pageVC.setViewControllers([first], direction: .forward, animated: false)
        }

        if viewModel.adUnitIds.count > 1 {
            startAutoScroll()
        }
    }

    // MARK: Auto-scroll
    private func startAutoScroll() {
        stopAutoScroll()
        autoScrollTimer = Timer.scheduledTimer(timeInterval: viewModel.autoScrollDelay,
                                               target: self,
                                               selector: #selector(moveToNextPage),
                                               userInfo: nil,
                                               repeats: true)
    }

    private func stopAutoScroll() {
        autoScrollTimer?.invalidate()
        autoScrollTimer = nil
    }

    @objc private func moveToNextPage() {
        guard !pages.isEmpty else { return }
        currentIndex = (currentIndex + 1) % pages.count
        let next = pages[currentIndex]
        pageVC?.setViewControllers([next], direction: .forward, animated: true)
    }

    // MARK: Helpers
    private func viewController(for index: Int) -> UIViewController? {
        guard index >= 0 && index < pages.count else { return nil }
        return pages[index]
    }
}

// MARK: - UIPageViewControllerDataSource
extension GoogleBannerAdsCarouselViewController: UIPageViewControllerDataSource {
    func pageViewController(_ pageViewController: UIPageViewController,
                            viewControllerBefore viewController: UIViewController) -> UIViewController? {
        let previous = (viewController.view.tag - 1 + pages.count) % pages.count
        return pages[previous]
    }

    func pageViewController(_ pageViewController: UIPageViewController,
                            viewControllerAfter viewController: UIViewController) -> UIViewController? {
        let next = (viewController.view.tag + 1) % pages.count
        return pages[next]
    }
}

// MARK: - BannerPageViewController
/// A single page inside the carousel that contains an adaptive `BannerView`.
///
/// The banner is padded horizontally by `horizontalPadding` so that it is
/// centred and always fits within the safe display area. The appropriate
/// adaptive size is recomputed in `viewDidLayoutSubviews` using
/// `currentOrientationAnchoredAdaptiveBanner(width:)` whenever the container
/// width changes (e.g. on rotation).
final class BannerPageViewController: UIViewController {
    private let adUnitId: String
    private var bannerView: BannerView!
    private let adSize: AdSize

    init(adUnitId: String, adSize: AdSize) {
        self.adUnitId = adUnitId
        self.adSize = adSize
        super.init(nibName: nil, bundle: nil)
    }

    @available(*, unavailable)
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()

        bannerView = BannerView()
        bannerView.adUnitID = adUnitId
        bannerView.rootViewController = self
        bannerView.translatesAutoresizingMaskIntoConstraints = false
        bannerView.adSize = adSize
        view.addSubview(bannerView)

        NSLayoutConstraint.activate([
            bannerView.widthAnchor.constraint(equalToConstant: adSize.size.width),
            bannerView.heightAnchor.constraint(equalToConstant: adSize.size.height),
            bannerView.centerYAnchor.constraint(equalTo: view.centerYAnchor)
        ])

        // Size will be set in viewDidLayoutSubviews once we know container width
        bannerView.load(Request())
    }
}
