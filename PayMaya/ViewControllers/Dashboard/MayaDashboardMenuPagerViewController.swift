//
//  MayaDashboardMenuPagerViewController.swift
//  PayMaya
//
//  Created by <PERSON><PERSON> on 2/8/22.
//  Copyright © 2022 PayMaya Philippines, Inc. All rights reserved.
//

import Analytics
import AssetProvider
import Error
import Injector
import ReactiveSwift
import UIKit

protocol MayaDashboardMenuPagerViewControllerDelegate: AnyObject {
    func willRequestForMenuPages(_ viewController: MayaDashboardMenuPagerViewController)
    func didTapProfile(_ viewController: MayaDashboardMenuPagerViewController)
    func didTapInbox(_ viewController: MayaDashboardMenuPagerViewController)
    func didTapInboxSupport(_ viewController: MayaDashboardMenuPagerViewController)
    func willRequestEDD(_ viewController: MayaDashboardMenuPagerViewController)
    func willRequestReferralInputCode(_ viewController: MayaDashboardMenuPagerViewController)
    func willUpgradeAccount(_ viewController: MayaDashboardMenuPagerViewController)
    func willShowDuplicateAccountAlert(_ viewController: MayaDashboardMenuPagerViewController)
    func willShowRestrictedLoginPrompt(_ viewController: MayaDashboardMenuPagerViewController)
}

class MayaDashboardMenuPagerViewController: MayaMenuPagerViewController, MayaTabBarChildViewControllerProtocol, MayaAnalyticsDurationManagerProtocol {
    @Inject var analyticsDurationManager: AnalyticsDurationManager

    @IBOutlet weak var profile: UIView!
    @IBOutlet weak var inboxWithoutNotification: UIView!
    @IBOutlet weak var inboxWithNotification: UIView!
    @IBOutlet weak var inboxNotificationUnreadIndicator: MayaInboxNotificationUnreadIndicator!
    @IBOutlet weak var inboxNotificationIconView: UIView!

    var usernameTitleView: MayaDashboardUsernameTitleView?
    weak var delegate: MayaDashboardMenuPagerViewControllerDelegate?

    let (childTTFDSignal, childTTFDObserver) = Signal<Void, Never>.pipe()

    override var shouldMonitorScreenPerformance: Bool { true }
    override var shouldOverrideTTFDMetric: Bool { true }

    override var shouldLogPreviousPage: Bool { true }
    override var shouldLogPageSwipe: Bool { true }
    override var shouldUseMayaNavigationBar: Bool { true }
    override var shouldUseMayaArrowBackButton: Bool { false }

    override var navigationBarBackgroundColor: UIColor { CommonAsset.MayaColors.Grey.grey1.color }

    private var unreadNotificationCount: Int = 0
    private var backgroundFrozenImageView: UIImageView?

    override func viewDidLoad() {
        super.viewDidLoad()
        delegate?.willRequestForMenuPages(self)
        menuBar.setScrollContentInset(UIEdgeInsets(top: 0, left: 24, bottom: 0, right: 24))
        menuBar.setScrollContentOffset(CGPoint(x: -24, y: 0))

        let barButtonItem = UIBarButtonItem(customView: profile)
        navigationItem.leftBarButtonItem = barButtonItem
        setRightBarButtonItem()
    }

    override func viewWillAppear(_ animated: Bool) {
        setModule(Analytics.Dashboard())
        super.viewWillAppear(animated)
        navigationController?.navigationBar.showShadow(show: true)

        setupFrozenBackground()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        guard let viewModel = viewModel else { return }

        if viewModel.isUserTaggedForRestrictedLogin() {
            delegate?.willShowRestrictedLoginPrompt(self)
        } else if viewModel.shouldShowUpgradeAccount() {
            viewModel.setUserDefaultShouldNotShowUpgradeAccount()
            delegate?.willUpgradeAccount(self)
        } else if viewModel.shouldShowReferralInputCode() {
            showReferralInputCode()
        } else if viewModel.isEddEnabled && viewModel.isTaggedForEDD() {
            delegate?.willRequestEDD(self)
        } else {
            handleDuplicateAccount()
        }
    }

    /// Updates unread counter of inbox icon
    /// Used by Inbox SDK
    func setUnreadIndicator(unreadCount: Int) {
        guard let viewModel,
              let customView = unreadCount > 0 ? inboxNotificationIconView : inboxWithoutNotification else { return }

        let unreadText = viewModel.formatUnreadCount(count: unreadCount)
        inboxNotificationUnreadIndicator.displayText(inputText: unreadText)

        let inboxButtonItem = UIBarButtonItem(customView: customView)
        navigationItem.rightBarButtonItem = inboxButtonItem
    }

    func toggleFrozenBackground(isFrozen: Bool) {
        if isFrozen { // show frozen background image
            guard let navigationBar = navigationController?.navigationBar else { return }

            let appearance = UINavigationBarAppearance()
            appearance.configureWithTransparentBackground()
            navigationBar.standardAppearance = appearance
            navigationBar.scrollEdgeAppearance = appearance

            guard backgroundFrozenImageView == nil else { return }
            backgroundFrozenImageView = UIImageView(image: Asset.MayaImages.Dashboard.backgroundFrozen.image)

            guard let backgroundFrozenImageView else { return }
            let safeAreaTop = view.safeAreaInsets.top
            let imageHeight = safeAreaTop + menuBar.frame.height + 40.0 // 40 - top and bottom spacing of `menuBar`
            backgroundFrozenImageView.frame = CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: imageHeight)

            view.addSubview(backgroundFrozenImageView)
            view.sendSubviewToBack(backgroundFrozenImageView)
            menuBar.backgroundColor = .clear
        } else if backgroundFrozenImageView != nil { // hide frozen background image
            setupNavigationBar()
            menuBar.backgroundColor = .grey1
            backgroundFrozenImageView?.removeFromSuperview()
            backgroundFrozenImageView = nil
        }
        menuBar.menuViews.forEach { $0.updateMenuBackgroundColor(isFrozen: isFrozen) }
    }

    func setupFrozenBackground() {
        // Frozen background gets covered every time `setupNavigationBar` is called
        // To ensure the frozen background remains visible,
        // call `setupFrozenBackground` here after the navigation bar is set up
        if let viewModel = viewModel,
           viewModel.isSecurityCenterEnabled,
           backgroundFrozenImageView != nil {
            toggleFrozenBackground(isFrozen: true)
        }
    }

    @IBAction func didTapProfile(sender: Any) {
        didTapProfile()
    }

    @IBAction func didTapInboxSupport(sender: Any) {
        let attributes = getNavigationItemAttributes(.helpCenter)
        analyticsService.logMayaEvents(action: .tap(Analytics.DashboardNavigation.item), attributes: attributes)
        delegate?.didTapInboxSupport(self)
    }

    @IBAction func didTapInboxNotification(sender: Any) {
        didTapInbox()
    }

    @IBAction func didTapinboxIconView(_ sender: UITapGestureRecognizer) {
        didTapInbox()
    }

    private func didTapInbox() {
        let attributes = getNavigationItemAttributes(.inbox)
        analyticsService.logMayaEvents(action: .tap(Analytics.DashboardNavigation.item), attributes: attributes)
        delegate?.didTapInbox(self)
    }

    private func didTapProfile() {
        let attributes = getNavigationItemAttributes(.profile)
        analyticsService.logMayaEvents(action: .tap(Analytics.DashboardNavigation.item), attributes: attributes)
        delegate?.didTapProfile(self)
    }
}

// MARK: ViewModelBindable
extension MayaDashboardMenuPagerViewController: ViewModelBindable {
    func binding(_ viewModel: MayaDashboardMenuPagerViewModelProtocol?) {
        guard let viewModel = viewModel else { return }

        viewModel.hasUnreadMessages.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] _ in
                guard let self = self else { return }
                self.setRightBarButtonItem()
            }?.addToDisposeBag(disposeBag)

        if viewModel.isMayaUsernameEnabled,
           let username = viewModel.getProfileUsername() {
            usernameTitleView = UINib(nibName: String(describing: MayaDashboardUsernameTitleView.self), bundle: nil).instantiate(withOwner: nil, options: nil)[0] as? MayaDashboardUsernameTitleView
            usernameTitleView?.usernameLabel.text = username
            usernameTitleView?.delegate = self
            if let usernameTitleView = usernameTitleView {
                navigationItem.leftBarButtonItem = UIBarButtonItem(customView: usernameTitleView)
            }
        }

        viewModel.signoutAction.isExecuting.signal
            .observe(on: UIScheduler())
            .observeValues { [weak self] executing in
                guard let self = self else { return }
                executing ? self.showMayaLoader() : self.hideMayaLoader()
            }?.addToDisposeBag(disposeBag)

        let didLogoutSignal = Signal.merge([viewModel.signoutAction.completed.map { _ in Error.none }, viewModel.signoutAction.errors])

        didLogoutSignal.observe(on: UIScheduler()).observeValues { [weak self] _ in guard let self = self else { return }
            self.routeActionDelegate?.didRequestSignout(self, viewModel: nil, signoutRoute: .landing, performAction: false, showAlert: true)
        }?.addToDisposeBag(disposeBag)

        setupTTFDObserver()
    }
}

// MARK: Private Methods
fileprivate extension MayaDashboardMenuPagerViewController {
    func setupTTFDObserver() {
        let childTTFDSignal = childTTFDSignal.signal.take(first: 1)

        childTTFDSignal.observe(on: UIScheduler()).observeCompleted { [weak self] in
            self?.stopTTFD()
        }?.addToDisposeBag(disposeBag)
    }

    func setRightBarButtonItem() {
        guard let viewModel = viewModel else { return }

        if viewModel.isMayaInboxFlutterEnabled {
            let inboxButtonItem = UIBarButtonItem(customView: inboxWithoutNotification)
            navigationItem.rightBarButtonItem = inboxButtonItem
        } else {
            let barButtonItem: UIBarButtonItem
            if viewModel.hasUnreadMessages.value {
                barButtonItem = UIBarButtonItem(customView: inboxWithNotification)
            } else {
                barButtonItem = UIBarButtonItem(customView: inboxWithoutNotification)
            }
            navigationItem.rightBarButtonItem = barButtonItem
        }
    }

    func showReferralInputCode() {
        guard let viewModel = viewModel else { return }
        viewModel.setUserDefaultShouldShowReferral()
        if viewModel.isRegistrationMGMEnabled {
            if let url = Constants.DeepLinkPath.submitInviteCode.url {
                routeActionDelegate?.didRequestStartDeepLink(self, with: url, completion: nil)
            }
        } else {
            delegate?.willRequestReferralInputCode(self)
        }
    }

    func handleDuplicateAccount() {
        guard presentedViewController == nil,
            navigationController?.presentedViewController == nil,
            let viewModel = viewModel,
            viewModel.isAccountLimited(),
            !viewModel.wasDuplicateAccountAlertShownForUser() else {
            return
        }

        delegate?.willShowDuplicateAccountAlert(self)
        viewModel.setUserDefaultForDuplicateAccountAlertShown()
    }

    func getNavigationItemAttributes(_ item: AnalyticsConstants.DashboardNavigation) -> [String: Any] {
        var attributes: [AnalyticsAttributeKey: Any] = item.attributes
        return attributes.merged(
            with: [AnalyticsAttributeKey.unreadMessages: unreadNotificationCount]
        ).rawValue
    }
}

extension MayaDashboardMenuPagerViewController: MayaDashboardUsernameTitleViewDelegate {
    func didTapUsername(_ view: MayaDashboardUsernameTitleView) {
        didTapProfile()
    }
}

extension MayaDashboardMenuPagerViewController: MayaDashboardMonitoringDelegate {
    func didFinishTTFD(_ viewController: ViewController) {
        childTTFDObserver.send(value: ())
    }
}
