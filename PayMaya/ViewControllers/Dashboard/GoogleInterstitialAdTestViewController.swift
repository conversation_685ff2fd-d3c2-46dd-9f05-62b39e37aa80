//
//  GoogleInterstitialAdTestViewController.swift
//  PayMaya
//
//  Created by Augment Agent on 12/19/24.
//  Copyright © 2024 PayMaya Philippines, Inc. All rights reserved.
//

import UIKit
import GoogleMobileAds

/// A dedicated view controller for testing Google Mobile Ads interstitial ads
/// This is completely separate from the existing CleverTap ad carousel functionality
class GoogleInterstitialAdTestViewController: UIViewController {
    
    // MARK: - IBOutlets
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var statusLabel: UILabel!
    @IBOutlet weak var showAdButton: UIButton!
    @IBOutlet weak var startAutoAdsButton: UIButton!
    @IBOutlet weak var stopAutoAdsButton: UIButton!
    @IBOutlet weak var resetCountButton: UIButton!
    @IBOutlet weak var configurationStackView: UIStackView!
    
    // MARK: - Properties
    private var interstitialAdManager: GoogleInterstitialAdManager?
    private var statusUpdateTimer: Timer?
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupInterstitialAdManager()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        startStatusUpdates()
        interstitialAdManager?.startAutomaticAds()
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        stopStatusUpdates()
        interstitialAdManager?.stopAutomaticAds()
    }
    
    deinit {
        stopStatusUpdates()
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        title = "Google Interstitial Ads Test"
        view.backgroundColor = .systemBackground
        
        // Configure labels
        titleLabel?.text = "Google Mobile Ads\nInterstitial Testing"
        titleLabel?.textAlignment = .center
        titleLabel?.numberOfLines = 0
        titleLabel?.font = .boldSystemFont(ofSize: 24)
        
        statusLabel?.text = "Initializing..."
        statusLabel?.textAlignment = .center
        statusLabel?.numberOfLines = 0
        statusLabel?.font = .systemFont(ofSize: 16)
        statusLabel?.textColor = .secondaryLabel
        
        // Configure buttons
        showAdButton?.setTitle("Show Interstitial Ad Now", for: .normal)
        showAdButton?.backgroundColor = .systemBlue
        showAdButton?.setTitleColor(.white, for: .normal)
        showAdButton?.layer.cornerRadius = 8
        
        startAutoAdsButton?.setTitle("Start Automatic Ads", for: .normal)
        startAutoAdsButton?.backgroundColor = .systemGreen
        startAutoAdsButton?.setTitleColor(.white, for: .normal)
        startAutoAdsButton?.layer.cornerRadius = 8
        
        stopAutoAdsButton?.setTitle("Stop Automatic Ads", for: .normal)
        stopAutoAdsButton?.backgroundColor = .systemRed
        stopAutoAdsButton?.setTitleColor(.white, for: .normal)
        stopAutoAdsButton?.layer.cornerRadius = 8
        
        resetCountButton?.setTitle("Reset Session Count", for: .normal)
        resetCountButton?.backgroundColor = .systemOrange
        resetCountButton?.setTitleColor(.white, for: .normal)
        resetCountButton?.layer.cornerRadius = 8
        
        #if !DEBUG
        // Hide test controls in non-debug builds
        configurationStackView?.isHidden = true
        showAdButton?.isHidden = true
        statusLabel?.text = "Interstitial ads are only available in debug builds"
        #endif
    }
    
    private func setupInterstitialAdManager() {
        interstitialAdManager = GoogleInterstitialAdManager()
        
        // Configure for testing
        interstitialAdManager?.configure(
            adUnitId: "ca-app-pub-3940256099942544/1033173712", // Google test ad unit
            showInterval: 30.0, // Show every 30 seconds
            maxAdsPerSession: 5 // Allow up to 5 ads per session for testing
        )
        
        // Initialize with consent request
        interstitialAdManager?.initialize(from: self)
        
        updateStatus()
    }
    
    private func startStatusUpdates() {
        statusUpdateTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateStatus()
        }
    }
    
    private func stopStatusUpdates() {
        statusUpdateTimer?.invalidate()
        statusUpdateTimer = nil
    }
    
    private func updateStatus() {
        #if DEBUG
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        statusLabel?.text = """
        Status: Ready for testing
        Last updated: \(timestamp)
        
        Instructions:
        • Tap 'Show Ad Now' for immediate testing
        • Use 'Start/Stop Auto Ads' to test automatic display
        • Ads show every 30 seconds when auto mode is on
        • Maximum 5 ads per session
        
        Note: Only works in DEBUG builds
        """
        #else
        statusLabel?.text = "Interstitial ads are only available in debug builds"
        #endif
    }
    
    // MARK: - IBActions
    @IBAction func showAdButtonTapped(_ sender: UIButton) {
        #if DEBUG
        print("[GoogleInterstitialAdTestViewController] Manual ad trigger requested")
        interstitialAdManager?.showInterstitialAdNow(from: self)
        #endif
    }
    
    @IBAction func startAutoAdsButtonTapped(_ sender: UIButton) {
        #if DEBUG
        print("[GoogleInterstitialAdTestViewController] Starting automatic ads")
        interstitialAdManager?.startAutomaticAds()
        
        let alert = UIAlertController(
            title: "Automatic Ads Started",
            message: "Interstitial ads will now show automatically every 30 seconds (max 5 per session)",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
        #endif
    }
    
    @IBAction func stopAutoAdsButtonTapped(_ sender: UIButton) {
        #if DEBUG
        print("[GoogleInterstitialAdTestViewController] Stopping automatic ads")
        interstitialAdManager?.stopAutomaticAds()
        
        let alert = UIAlertController(
            title: "Automatic Ads Stopped",
            message: "Automatic interstitial ads have been disabled",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
        #endif
    }
    
    @IBAction func resetCountButtonTapped(_ sender: UIButton) {
        #if DEBUG
        print("[GoogleInterstitialAdTestViewController] Resetting session count")
        interstitialAdManager?.resetSessionCount()
        
        let alert = UIAlertController(
            title: "Session Count Reset",
            message: "The session ad count has been reset. You can now see up to 5 more ads.",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
        #endif
    }
}

// MARK: - Static Factory Method
extension GoogleInterstitialAdTestViewController {
    /// Create an instance of the test view controller
    /// - Returns: Configured GoogleInterstitialAdTestViewController
    static func create() -> GoogleInterstitialAdTestViewController {
        let storyboard = UIStoryboard(name: "Main", bundle: nil)
        let viewController = GoogleInterstitialAdTestViewController()
        return viewController
    }
}
