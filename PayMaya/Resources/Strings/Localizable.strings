/*
  Localizable.strings
  PayMaya

  Created by <PERSON> on 27/10/2016.
  Copyright © 2016 PayMaya Philippines, Inc. All rights reserved.
*/

/* Tab Menu */
"tab.menu.home" = "Home";
"tab.menu.mycards" = "My Cards";
"tab.menu.shop" = "Shop";
"tab.menu.sendmoney" = "Send Money";
"tab.menu.paybills" = "Pay Bills";
"tab.menu.pay" = "Pay";
"tab.menu.freeze" = "Freeze";
"tab.menu.unfreeze" = "Unfreeze";
"tab.menu.services" = "Services";

/* Text Field Name */
"field.name.mobileNumber" = "Mobile Number";
"field.name.password" = "Password";
"field.name.firstName" = "First Name";
"field.name.lastName" = "Last Name";
"field.name.cvv" = "CVV";
"field.name.validity" = "Valid Thru (MM/YY)";
"field.name.currentPassword" = "Current Password";
"field.name.newPassword" = "New Password";
"field.name.amount" = "Amount";
"field.name.email" = "Email address";
"field.error.required" = "%@ is required";

/* KYC Text Field Name */
"kyc.field.name.natureOfWork" = "Nature of work";
"kyc.field.name.natureOfWorkOthers" = "Nature of work - Others";
"kyc.field.name.sourceOfIncome" = "Source of income";
"kyc.field.name.nameOfCompanyBusiness" = "Name of company / business";
"kyc.field.name.firstName" = "First name";
"kyc.field.name.middleName" = "Middle name";
"kyc.field.name.lastName" = "Last name";
"kyc.field.name.nationality" = "Nationality";
"kyc.field.name.lastName" = "Last name";
"kyc.field.name.dateOfBirth" = "Date of Birth";
"kyc.field.name.countryOfBirth" = "Country of birth";
"kyc.field.name.stateProvinceOfBirth" = "State / Province of birth";
"kyc.field.name.cityOfBirth" = "City of birth";
"kyc.field.name.country" = "Country";
"kyc.field.name.stateProvince" = "State / Province";
"kyc.field.name.city" = "City";
"kyc.field.name.barangay" = "Barangay";
"kyc.field.name.zipCode" = "Zip code";
"kyc.field.name.streetName" = "House / Unit Number, Building Name, Street Name";
"kyc.field.name.additionalInfo" = "Additional info (optional)";
"kyc.field.name.idNumber" = "ID number";
"kyc.field.name.expirationDate" = "Expiration date";

/* KYC */
"kyc.spiel.callToCompleteUpgrade" = "Give us a call to complete your upgrade.";
"kyc.spiel.sameAsPresentAddress" = "Same as Present Address";
"kyc.spiel.notApplicable" = "Not Applicable";
"kyc.spiel.resending" = "Resending";
"kyc.spiel.resendReferenceId.success.message" = "Reference ID Sent!";
"kyc.spiel.sendValidationCode" = "Send a copy of your validation code via SMS";
"kyc.spiel.submit.success.message" = "Just 1 last step to fully upgrade your account. Visit our online Guide for more information: https://paymaya.com/upgrade/.\nDon't forget to bring your Validation Code SMS, and a Valid ID.";
"kyc.spiel.submit.success.title" = "Upgrade PayMaya application submitted!";
"kyc.spiel.businessOwner" = "Owner of Business (SME) / Self Employed";

"kyc.spiel.required.firstName" = "Don't forget to fill in your First Name! Your details must be complete to complete your upgrade.";
"kyc.spiel.required.lastName" = "Don't forget to fill in your Last Name! Your details must be complete to complete your upgrade.";
"kyc.spiel.required.nationality" = "Don't forget to fill in your Nationality! Your details must be complete to complete your upgrade.";
"kyc.spiel.required.birthdate" = "Don't forget to fill in your Birth Date! Your details must be complete to complete your upgrade.";
"kyc.spiel.required.birthPlace" = "Don't forget to fill in your Place of Birth! Your details must be complete to complete your upgrade.";
"kyc.spiel.required.houseNumber" = "Don't forget to fill in your House No./Building No./Street No./Subdivision! Your details must be complete to complete your upgrade.";
"kyc.spiel.required.barangay" = "Don't forget to fill in your Barangay! Your details must be complete to complete your upgrade.";
"kyc.spiel.required.city" = "Don't forget to fill in your City! Your details must be complete to complete your upgrade.";
"kyc.spiel.required.province" = "Don't forget to fill in your State/Province! Your details must be complete to complete your upgrade.";
"kyc.spiel.required.zipcode" = "Don't forget to fill in your Zip Code! Your details must be complete to complete your upgrade.";
"kyc.spiel.required.permanentAddress" = "Don't forget to fill in your Permanent Address! Your details must be complete to complete your upgrade.\n\nIf you live permanently in your present address, please tap the \"Same as Present Address.\" If not, update your permanent address.";

"kyc.spiel.required.incomeSource" = "Don't forget to fill in your Source of Income! Your details must be complete to complete your upgrade.";
"kyc.spiel.required.workNature" = "Don't forget to fill in your Nature of Work! Your details must be complete to complete your upgrade.";
"kyc.spiel.required.businessName" = "Don't forget to fill in your Business Name! Your details must be complete to complete your upgrade.";
"kyc.spiel.required.idDetails" = "Don't forget to fill in your GSIS / SSS / TIN! Your details must be complete to complete your upgrade.";
"kyc.spiel.required.idPhoto" = "Don't forget to upload your ID photo! Please make sure you upload your ID to complete your upgrade.";
"kyc.id.card.required" = "Identification Card";
"kyc.id.card.optional" = "Identification Card (Optional)";

"kyc.spiel.videoCallEnabled.success.message" = "To finish your application, start a video call. Here’s your Validation Code: %@.";
"kyc.spiel.kycSuccess.validationCode" = "To finish your application, start a video call. Here’s your Validation Code: %@.";
"kyc.spiel.kycSuccess.formattedValidationCode" = "Validation Code: %@.";
"kyc.spiel.videoCall.call.later.ekyc" = "When you’re ready, just tap More > Check My Upgrade Status\n\n%@\n\nNOTE: Your upgrade application will only be completed after the video call. Approval confirmation to follow.";
"kyc.spiel.videoCall.call.later.title" = "Complete upgrade later?";
"kyc.spiel.videoCall.call.later" = "Call later";
"kyc.spiel.videoCall.validationCode" = "Here’s your validation code:\n%@";
"kyc.spiel.id.list" = "list";
"kyc.spiel.id.checklist" = "Check this list";
"kyc.spiel.download.form" = "Download form here.";
"kyc.spiel.no.internet.title" = "Internet Connection Issue";
"kyc.spiel.no.internet" = "It looks like you have slow or no internet connection. Please make sure you're online and try again.\n\nNote: Please make sure you call us back. Your upgrade will only be complete after successful video call.";
"kyc.spiel.no.internet.note" = "It looks like you have slow or no internet connection. Please make sure you're online and try again.";
"kyc.spiel.unable.connect.title" = "Unable to Connect";
"kyc.spiel.unable.connect" = "Uh-oh! Connection to the video call was lost. Please make sure you're online and try again.\n\nNote: Please make sure you call us back. Your upgrade will only be complete after successful video call.";
"kyc.spiel.unavailable.officers.title" = "Upgrade Officers Unavailable";
"kyc.spiel.unavailable.officers" = "It's a busy time, all our officers are helping other customers right now. Please try again soon.\n\nNote: Please make sure you call us back. Your upgrade will only be complete after successful video call.";

"kyc.title.valid.ids" = "List of Valid IDs";
"kyc.spiel.selection.others" = "Other/s, please specify";

"kyc.videocall.analytics.reason.userDisconnected" = "User Disconnected";
"kyc.videocall.analytics.reason.agenDisconnected" = "Agen Disconnected";
"kyc.videocall.analytics.reason.applicationTerminated" = "Application Terminated";
"kyc.videocall.analytics.reason.timeout" = "Timeout";
"kyc.videocall.analytics.reason.backgrounded" = "Application Backgrounded";
"kyc.videocall.analytics.reason.networkDisconnection" = "Network Disconnection";
"kyc.videocall.analytics.reason.insufficientNetwork" = "Insufficient Network";
"kyc.videocall.analytics.reason.invalidToken" = "Invalid Token";
"kyc.videocall.analytics.reason.unknown" = "Unknown Error";
"kyc.personal.title" = "Personal";
"kyc.address.title" = "Address";
"kyc.work.title" = "Work and ID";

"kyc.personalInfo.dropdown.birthCountryPlaceholder" = "Select country of birth";
"kyc.personalInfo.freeText.birthCityPlaceholder" = "Enter city of birth";
"kyc.personalInfo.dropdown.birthCityPlaceholder" = "Select city of birth";
"kyc.personalInfo.freeText.statePlaceholder" = "Enter state/province";
"kyc.personalInfo.dropdown.statePlaceholder" = "Select state / province";
"kyc.personalInfo.freeText.cityPlaceholder" = "Enter city";
"kyc.personalInfo.dropdown.cityPlaceholder" = "Select city";
"kyc.personalInfo.freeText.barangayPlaceholder" = "Enter barangay";
"kyc.personalInfo.dropdown.barangayPlaceholder" = "Select barangay";
"kyc.personalInfo.freeText.zipCodePlaceholder" = "Enter zip code";
"kyc.personalInfo.dropdown.zipCodePlaceholder" = "Select zip code";
"kyc.personalInfo.picker.noResult.title" = "No results found";
"kyc.personalInfo.picker.noResult.description" = "We could not find what you are looking for.\nPlease try again.";
"kyc.personalInfo.picker.search" = "Search";
"kyc.personalInfo.foreignCompliance.initialString" = "Your ";
"kyc.personalInfo.foreignCompliance.initial.boldStringCurrent" = "current address";
"kyc.personalInfo.foreignCompliance.initial.boldStringPermanent" = "permanent address";
"kyc.personalInfo.foreignCompliance.secondaryStringCurrent" = " needs to be in the ";
"kyc.personalInfo.foreignCompliance.secondaryStringPermanent" = " needs to be in ";
"kyc.personalInfo.foreignCompliance.secondary.boldStringCurrent" = "Philippines";
"kyc.personalInfo.foreignCompliance.secondary.boldStringPermanent" = "another country";


/* Pre KYC */
"prekyc.spiel.title" = "Let's take it all the way";
"prekyc.spiel.message" = "Upgrade your account now to enjoy all of Maya’s features. It takes just a few minutes!";
"prekyc.spiel.secondary.button" = "Maybe later";
"prekyc.spiel.primary.button" = "Upgrade now";

/* Re-KYC */
"rekyc.spiel.title" = "Update your account and enjoy all of Maya’s features";
"rekyc.spiel.message" = "Upon checking, we found that your information is in need of updating. In compliance with BSP Regulations and for your protection, please ensure your personal information is up-to-date.

Rest assured, your funds are secure and will be accessible after you update.";
"rekyc.spiel.feature.savings" = "Get a Savings Account";
"rekyc.spiel.feature.credit" = "Get credit fast";
"rekyc.spiel.feature.crypto" = "Invest in crypto";
"rekyc.spiel.secondary.button" = "I'll do it later";
"rekyc.spiel.primary.button" = "Update now";

"rekyc.under.review.spiel.title" = "Under review";
"rekyc.under.review.spiel.message" = "Please give us up to 24 hours to finish reviewing your submitted information.";
"rekyc.under.review.Secondary.button" = "View status";

"rekyc.otherId.spiel.title" = "Updating your account with Other IDs";
"rekyc.otherId.spiel.first" = "1";
"rekyc.otherId.spiel.first.title" = "Take a picture of any of the IDs below. Make sure to include the FRONT and BACK of your ID.";
"rekyc.otherId.spiel.first.note" = "One of the following:
  • UMID
  • Philippine Driver’s License
  • Philippine Passport
  • Postal ID
  • PRC ID
  • SSS
  • Foreign Passport
  • Philippine National ID / Phil ID
  • ePhil ID (Printed Phil ID)
  • NBI Clearance
  • ACR i-Card
  • Government Office / GOCC ID
  • IBP Card
  • School ID
Any 2 of the following:
  • PhilHealth ID
  • TIN ID
  • Voter's ID
  • Police Clearance
  • Senior Citizen’s Card
  • GSIS e-Card
  • OWWA ID/OFW e-Card";
"rekyc.otherId.spiel.second" = "2";
"rekyc.otherId.spiel.second.title" = "Email your <NAME_EMAIL>";
"rekyc.otherId.spiel.second.note" = "Email your ID with the subject:
Account Update for <your mobile number>
Include in the email the following :
  • Your full name
  • Mobile number you use in Maya
  • The type of ID you submitted
  • ID number
  • ID expiry date";
"rekyc.otherId.spiel.second.note.highlight" = "Account Update for <your mobile number>";
"rekyc.otherId.spiel.note" = "We’ll notify you once we receive and validate your IDs.";
"rekyc.otherId.spiel.button.title" = "Got it";

/* EKYC */
"ekyc.upgrade.title" = "Upgrade";
"ekyc.upgradeAccount.title" = "Upgrade my account";
"ekyc.personal.title" = "Personal";
"ekyc.address.title" = "Address";
"ekyc.identification.title" = "Identification";
"ekyc.idVerification.title" = "ID Verification";
"ekyc.faceVerification.title" = "Face Verification";
"ekyc.personalInfo.title" = "Personal Info";
"ekyc.personal.navigation.title" = "Personal Information";
"ekyc.address.navigation.title" = "Address";
"ekyc.identification.navigation.title" = "Identification";
"ekyc.identification.ids.title" = "ID Type";
"ekyc.middleName.optional.placeholder" = "Middle Name (Optional)";
"ekyc.middleName.mandatory.placeholder" = "Middle Name";
"ekyc.spiel.required.idType" = " Don't forget to select an ID to submit in order to complete your upgrade.";
"ekyc.spiel.required.idPhoto" = " Don't forget to upload a photo of your ID to complete your upgrade.";
"ekyc.spiel.required.backOfIdPhoto" = " Don't forget to upload a photo of the back of your ID to complete your upgrade.";
"ekyc.spiel.required.idNumber" = "Don't forget to provide your ID number to complete your upgrade.";
"ekyc.spiel.required.birthCertificate" = "Don't forget to upload a photo of your Birth Certificate to complete your upgrade.";
"ekyc.spiel.required.parentalConsent" = "Don't forget to upload a photo of your Parental Consent to complete your upgrade.";
"ekyc.spiel.required.parentId" = "Don't forget to upload a photo of your Parent’s/Guardian’s Primary ID to complete your upgrade.";
"ekyc.spiel.required.livenessClip" = "Don't forget to record and upload a liveness clip to complete your upgrade.";
"ekyc.liveness.spiel.preparing" = "Preparing...";
"ekyc.liveness.spiel.recording" = "Recording...";
"ekyc.liveness.spiel.nod" = "Nod your head up and down.";
"ekyc.liveness.spiel.sideways" = "Turn your head sideways.";
"ekyc.liveness.spiel.faceDetection.none" = "Sorry, we are unable to detect your face from the video. We recommend retaking it for faster upgrade approval.";
"ekyc.liveness.spiel.faceDetection.single" = "Before you submit, make sure the video is clear and you followed the instructions.";
"ekyc.liveness.spiel.faceDetection.multiple" = "It looks like there are multiple faces in your video. Tip: Make sure to upload a video containing your face only for faster upgrade approval.";
"ekyc.id.front.spiel" = "Take a photo of the FRONT of the ID";
"ekyc.id.back.spiel" = "Take a photo of the BACK of the ID";
"ekyc.id.clearance.spiel" = "Take a photo of your NBI Clearance";
"ekyc.id.passport.spiel" = "Take a photo of your Passport";
"ekyc.id.noFaceDetected.spiel" = "Sorry, we are unable to detect your face from your photo. Tip: Take a photo in good lighting and avoid glare on the ID.";
"ekyc.id.multipleFacesDetected.spiel" = "It looks like there are multiple faces included in your photo. Tip: Make sure to upload a photo containing your face only for faster upgrade approval.";
"ekyc.id.singleFaceDetected.spiel" = "Before you submit, make sure your ID image is clear and the information is the same as the information you entered.";
"ekyc.id.cancel.title" = "Cancel Photo Capture";
"ekyc.id.cancel.message" = "If you close this screen the image won’t be saved. Are you sure you want to cancel?";
"ekyc.id.cancel.yes" = "Yes, Cancel";
"ekyc.id.protip.spiel" = "<b>Pro Tip:</b><br><br><ul>Make sure it’s clear and the whole card is seen in the shot. Don’t forget to check its expiry date!</ul><br><ul>Make sure you’re in a well-lit room and place the ID on a plain dark surface.</ul>";
"ekyc.submit.error" = "We encountered an issue with your submission. Please resubmit after securing a stable internet connection.";
"ekyc.firstname.required" = "First Name is required";
"ekyc.middlename.required" = "Middle Name is required";
"ekyc.lastname.required" = "Last Name is required";
"ekyc.nationality.required" = "Nationality is required";
"ekyc.dateOfBirth.required" = "Date of Birth is required";
"ekyc.birthCity.required" = "Place of Birth (City) is required";
"ekyc.birthCountry.required" = "Country/Region of Birth is required";
"ekyc.sourceOfIncome.required" = "Source of Income is required";
"ekyc.natureOfWork.required" = "Nature of Work is required";
"ekyc.nameOfCompany.required" = "Name of Company/Business Name is required";
"ekyc.country.required" = "Country/Region is required";
"ekyc.city.required" = "City is required";
"ekyc.state.required" = "State/Province is required";
"ekyc.locality.required" = "Barangay is required";
"ekyc.zipCode.required" = "Zip Code is required";
"ekyc.field.required" = "This field is required";
"ekyc.idType.required" = "ID Type is required";
"ekyc.idNumber.required" = "ID Number is required";
"ekyc.id.classification.mismatch.spiel" = "This photo does not match with your selected ID. Please take a photo of your \“%@\”.";
"ekyc.id.classification.lowConfidence.spiel" = "To make your upgrade process better, make sure the photo you'll submit matches with the ID you selected in the previous screen.";
"ekyc.review.almostDone.spiel" = "You’re almost done! Before you submit, make sure that the information below is correct.";
"ekyc.review.underReview.spiel" = "We’ve got your application, and currently reviewing it. Kindly give us a maximum of 24 hours to review and evaluate your application.";
"ekyc.success.receipt.validationCode.copied" = "Validation Code Copied";

/* Maya EKYC */
"maya.ekyc.welcome.buttonTitle.default" = "Let's go";
"maya.ekyc.welcome.buttonTitle.upgradeForFree" = "Upgrade for free";
"maya.ekyc.welcome.buttonTitle.verifyNow" = "Verify now";
"maya.ekyc.steps.title" = "It takes just 3\neasy steps";
"maya.ekyc.reminders.note" = "Note: Please make sure you have a stable internet connection throughout the process. Standard telco rates apply if you’re using a data connection.";
"maya.ekyc.upgradeType.filipino.adult" = "I am a Filipino adult";
"maya.ekyc.upgradeType.filipino.minor" = "I am a Filipino below 18 years old";
"maya.ekyc.upgradeType.foreigner.adult" = "I am an adult foreigner";
"maya.ekyc.upgradeType.foreigner.minor" = "I am a foreigner below 18 years old";
"maya.ekyc.selectAnotherID.title" = "Select one more ID";
"maya.ekyc.selectAnotherID.subtitle" = "Your ID helps us confirm your identity and secure your account. Please submit a valid ID from the list below:";
"maya.ekyc.selectAnotherID.pickerLabel" = "Secondary IDs";
"maya.ekyc.newSelectAnotherID.title" = "Select another ID";
"maya.ekyc.newSelectAnotherID.subtitle" = "Your ID helps us confirm your identity. Please choose a valid ID to submit from the list below:";
"maya.ekyc.newSelectAnotherID.pickerLabel" = "SECONDARY ID";
"maya.ekyc.newSelectAnotherID.pickerSubLabel" = "Two IDs are required";
"maya.ekyc.alert.multipleSubmission.title" = "You may already have a KYC submission";
"maya.ekyc.alert.multipleSubmission.message" = "You can check your account status in the Profile page";

/* Maya EKYC - Common */
"maya.ekyc.common.service.title.wallet" = "Wallet";
"maya.ekyc.common.service.title.savings" = "Savings";
"maya.ekyc.common.service.title.credit" = "Credit";
"maya.ekyc.common.service.title.crypto" = "Crypto";
"maya.ekyc.common.continue" = "Continue";


/* Maya EKYC - Simple Capture */
"maya.ekyc.capture.frontID.title" = "Front of ID";
"maya.ekyc.capture.backID.title" = "Back of ID";
"maya.ekyc.capture.instruction" = "Position your ID in the frame";
"maya.ekyc.capture.focus.instruction" = "Focusing on ID";
"maya.ekyc.capture.takeIdPhoto" = "Take ID photo";
"maya.ekyc.capture.photo.livetip" = "  • %@";
"maya.ekyc.capture.photo.tips" = "Tips for taking a photo of your ID:
%@";
"maya.ekyc.idReview.allGoods.title" = "All good?";
"maya.ekyc.idReview.allGoods.subtitle" = "Make sure all information on your ID can be read easily.";
"maya.ekyc.idReview.blurry.title" = "Your picture isn't clear";
"maya.ekyc.idReview.blurry.subtitle" = "Make sure your whole ID is in the shot and in focus.";
"maya.ekyc.idReview.useThis.title" = "Use this photo";
"maya.ekyc.idReview.retake.title" = "Retake";
"maya.ekyc.idReview.replace.title" = "Replace";
"maya.ekyc.idReview.failed.title" = "This isn’t the right ID";
"maya.ekyc.idReview.failed.subtitle" = "This photo doesn’t match the type of ID you picked. Please take a photo of your %@.";
"maya.ekyc.idReview.failed.retakeTitle" = "Take a new photo";
"maya.ekyc.idReview.failed.chooseDifferentIdTitle" = "Choose a different ID";
"maya.ekyc.idReview.mismatch.title" = "Incorrect Second Image";
"maya.ekyc.idReview.mismatch.subtitle" = "You've already submitted the %@ of your %@. Now, please upload a clear photo of the %@ of your ID. The image you provided is not the %@ of your ID.";
"maya.ekyc.idReview.philsys.mismatch.capture.title" = "Only Physical IDs are allowed for camera capture";
"maya.ekyc.idReview.philsys.mismatch.capture.subtitle" = "Please take a photo of your Phil ID Card (Plastic Card) or ePhil ID (Paper ID)";
"maya.ekyc.idReview.philsys.mismatch.upload.title" = "Only digital IDs are allowed for upload.";
"maya.ekyc.idReview.philsys.mismatch.upload.subtitle" = "Please upload a screenshot of your Philippine National IDs from the eGov app or generated from the National ID Website";
"maya.ekyc.idReview.philsys.mismatch.upload.retakeTitle" = "Upload a new ID";
"maya.ekyc.tencent.selfie.error.default.title" = "Let’s try another take.";
"maya.ekyc.tencent.selfie.error.spiel.default" = "Something went wrong with your selfie. Please try again.";

/* Maya EKYC - Simple Capture V3*/
"maya.ekyc.captureV3.title" = "Take an ID photo";
"maya.ekyc.captureV3.scanType.title" = "Scan the _ of your";
"maya.ekyc.captureV3.tips.title" = "How to get a perfect picture";

/* Maya EKYC - Image-Quality Scenario */
"maya.ekyc.idReview.imageQuality.error.blurred.title" = "Your photo isn’t clear";
"maya.ekyc.idReview.imageQuality.error.blurred.subtitle" = "Please make sure your whole ID is in focus and all information can be read clearly.";
"maya.ekyc.idReview.imageQuality.error.cannotRead.id.title" = "Your ID cannot be read";
"maya.ekyc.idReview.imageQuality.error.cannotRead.id.subtitle" = "Please make sure your ID is in good condition and all information can be read clearly.";
"maya.ekyc.idReview.imageQuality.error.darkPhoto.title" = "Your photo is too dark";
"maya.ekyc.idReview.imageQuality.error.darkPhoto.subtitle" = "Please take a photo of your ID under better lighting and make sure all information can be read clearly";
"maya.ekyc.idReview.imageQuality.error.bright.title" = "Your photo is too bright";
"maya.ekyc.idReview.imageQuality.error.bright.subtitle" = "Please take a photo of your ID under better lighting and make sure all information can be read clearly";
"maya.ekyc.idReview.imageQuality.error.cropped.title" = "Parts of your ID are missing";
"maya.ekyc.idReview.imageQuality.error.cropped.subtitle" = "Please make sure your whole ID is within the indicated borders and all information can be read clearly.";
"maya.ekyc.idReview.imageQuality.error.moreThanOne.title" = "Multiple IDs detected";
"maya.ekyc.idReview.imageQuality.error.moreThanOne.subtitle" = "Please take a clear photo of 1 ID only.";
"maya.ekyc.idReview.imageQuality.error.noFaceDetected.title" = "Face on ID not detected";
"maya.ekyc.idReview.imageQuality.error.noFaceDetected.subtitle" = "Please make sure the face on your ID can be clearly seen in the photo.";
"maya.ekyc.idReview.imageQuality.error.notFlat.subtitle" = "Please take a photo of your ID on a flat surface.";
"maya.ekyc.idReview.imageQuality.error.notLive.subtitle" = "This photo appears to be a screenshot. Please take a photo of an actual ID.";
"maya.ekyc.idReview.imageQuality.error.reflectonHologram.subtitle" = "Please take a clear photo of your ID without any glare and make sure all information can be read clearly.";
"maya.ekyc.idReview.imageQuality.error.selfie.title" = "No ID detected";
"maya.ekyc.idReview.imageQuality.error.selfie.subtitle" = "Please take a photo of an actual ID.";
"maya.ekyc.idReview.imageQuality.error.paper.subtitle" = "Your ID appears to be printed on paper. Please secure a proper ID first and try again.";
"maya.ekyc.capture.imageQuality.error.title" = "That didn't load right.";
"maya.ekyc.capture.imageQuality.error.description" = "Something went wrong. Please try again in a bit.";

/* Maya EKYC - Maintenance */
"maya.ekyc.maintenance.title.kyc" = "Upgrade your account";
"maya.ekyc.maintenance.title.rekyc" = "Update your account";
"maya.ekyc.maintenance.header" = "Please try again later";
"maya.ekyc.maintenance.subheader" = "This feature is currently under maintenance and will be back soon";

/* Maya ReKYC */
"maya.rekyc.nudge.title" = "Please update your account";
"maya.rekyc.nudge.subtitle" = "In compliance with BSP regulations, please update your account to continue using these features:";
"maya.rekyc.nudge.primaryButton.title" = "Update now";
"maya.rekyc.nudge.secondaryButton.title" = "I'll do it later";
"maya.rekyc.underReview.title" = "We'll take it from here.";
"maya.rekyc.underReview.title.highlightedPhrase" = "here";
"maya.rekyc.underReview.subtitle" = "Thanks for submitting your additional information.";
"maya.rekyc.underReview.status.title" = "Account status:";
"maya.rekyc.underReview.status.subtitle" = "Your account update is under review. Successfully updated accounts will be able to enjoy all the features that Maya has to offer.";
"maya.rekyc.underReview.status.subtitle.highlightedPhrase" = "under review";
"maya.rekyc.underReview.status.referenceNumber" = "Reference Number: ";

/* Maya EKYC - Tencent */
"maya.ekyc.tencent.error.title" = "Let's try another take.";
"maya.ekyc.tencent.error.subtitle" = "Something went wrong with your selfie. Please try again.";
"maya.ekyc.tencent.error.action" = "Try again";
"maya.ekyc.tencent.error.severalFaces.subtitle" = "Please make sure there is no one else visible on your selfie";
"maya.ekyc.tencent.error.incompleteFace.subtitle" = "Please make sure your whole face is visible. Remove any headwear, face masks or glasses";


/* Compliance */
"compliance.dosri.relativeInformation.title" = "Relative's information";
"compliance.dosri.relativeInformation.firstName.title" = "First name";
"compliance.dosri.relativeInformation.firstName.placeholder" = "Enter first name";
"compliance.dosri.relativeInformation.firstName.error" = "First name is required";
"compliance.dosri.relativeInformation.middleName.title" = "Middle name";
"compliance.dosri.relativeInformation.middleName.placeholder" = "Enter middle name";
"compliance.dosri.relativeInformation.middleName.error" = "Middle name is required";
"compliance.dosri.relativeInformation.lastName.title" = "Last name";
"compliance.dosri.relativeInformation.lastName.placeholder" = "Enter last name";
"compliance.dosri.relativeInformation.lastName.error" = "Last name is required";
"compliance.dosri.relativeInformation.relationship.title" = "Relationship";
"compliance.dosri.relativeInformation.relationship.placeholder" = "Select your relationship";
"compliance.dosri.relativeInformation.relationship.error" = "Relationship is required";
"compliance.dosri.relativeInformation.addAnotherRelative.title" = "+ Add another relative";
"compliance.dosri.relativeInformation.relationship.cancel.title" = "Cancel";
"compliance.dosri.relativeInformation.relationship.sameRelative.error" = "Cannot declare the same person as a relative more than once";
"compliance.dosri.relativeInformation.relationship.sameUser.error"= "You must name another person as your relative";

/* EKYC-Zoloz */
"ekycZoloz.benefitsAndSteps.showBenefitsToggle.colapsedTitle" = "See all benefits";
"ekycZoloz.benefitsAndSteps.showBenefitsToggle.expandedTitle" = "Hide benefits";
"ekycZoloz.benefitsAndSteps.dataPrivacy.title" = "Data Privacy";
"ekycZoloz.benefitsAndSteps.dataPrivacy.description" = "To protect your information and safety on Maya, we conduct periodic updates to ensure that your data is up to date. Your personal data will always remain secured and processed with the set guidelines under the Data Privacy Act of 2012. Visit Maya’s Privacy Statement to learn more about how we process your data.";
"ekycZoloz.benefitsAndSteps.dataPrivacy.highlightPhrase" = "Privacy Statement";
"ekycZoloz.errorView.actionTitle" = "Got it";
"ekycZoloz.submitAnId.title" = "Submit an ID";
"ekycZoloz.submitAnId.subtitle" = "Your ID helps us confirm your identity. Please choose a valid ID to submit from the list below:";
"ekycZoloz.submitAnId.idListHeader.acceptedIds" = "ACCEPTED IDS";
"ekycZoloz.submitAnId.idListHeader.recommendedIds" = "RECOMMENDED PRIMARY ID";
"ekycZoloz.acceptId.title" = "Accepted IDs";
"ekycZoloz.acceptId.idListHeader.recommendedIds" = "Recommended Primary IDs";
"ekycZoloz.submitAnId.idListHeader.primaryIds" = "Primary IDs";
"ekycZoloz.submitAnId.idListHeader.secondaryIds" = "Secondary IDs";
"ekycZoloz.submitAnId.idListSubHeader.secondaryIds" = "Two IDs are required";
"ekycZoloz.submitAnId.showMoreIds.collapsedTitle" = "Show more IDs";
"ekycZoloz.submitAnId.showMoreIds.expandedTitle" = "Hide other IDs";
"ekycZoloz.otherId.title" = "Other ID";
"ekycZoloz.otherId.note.title" = "Select “Other ID” if you have the following:";
"ekycZoloz.otherId.note.message" = "NBI Clearance, ACR-I card, Government office (GOCC), Student ID, TIN ID, Voter’s ID, Senior Citizens card, Police Clearance, PhilHealh ID, GSIS e-card ePhil ID (Printed Phil ID) and OFW e-card (OWWA).";
"ekycZoloz.photoTips.title" = "Take an ID photo";
"ekycZoloz.photoTips.subtitle" = "Your ID helps us confirm your identity.";
"ekycZoloz.photoTips.tipListHeader" = "Tips for taking an ID photo";
"ekycZoloz.livenessTips.title" = "Take a video selfie";
"ekycZoloz.livenessTips.subtitle" = "We'll turn your camera on for a quick identity check. This will only be used to verify your ID.";
"ekycZoloz.livenessTips.tipListHeader" = "Tips for taking a selfie";
"ekycZoloz.flowProgressBar.progress" = "%@/3";
"ekycZoloz.zolozError.systemError.title" = "Error encountered";
"ekycZoloz.zolozError.systemError.message" = "Sorry about that! It looks like our systems are busy right now but you can try again later.";
"ekycZoloz.zolozError.systemError.action" = "Back to home";
"ekycZoloz.zolozError.deviceUnsupported.title" = "Device unsupported";
"ekycZoloz.zolozError.deviceUnsupported.message" = "Sorry, it appears that your device is not compatible with our identity verification services.";
"ekycZoloz.zolozError.updateRequired.title" = "Update required";
"ekycZoloz.zolozError.updateRequired.message" = "Please update your app in order to proceed with your video selfie.";
"ekycZoloz.zolozError.updateRequired.action" = "Update now";
"ekycZoloz.accountReview.validationCode.copied" = "Validation code copied";
"ekycZoloz.accountReview.validationCode.sent" = "Validation code sent";
"ekycZoloz.accountReview.sections.personalInformation.title" = "Personal information";
"ekycZoloz.accountReview.sections.personalInformation.fullName" = "Full name";
"ekycZoloz.accountReview.sections.personalInformation.nationality" = "Nationality";
"ekycZoloz.accountReview.sections.personalInformation.dateOfBirth" = "Date of birth";
"ekycZoloz.accountReview.sections.personalInformation.placeOfBirth" = "Place of birth";
"ekycZoloz.accountReview.sections.workInformation.title" = "Work information";
"ekycZoloz.accountReview.sections.workInformation.natureOfWork" = "Nature of work";
"ekycZoloz.accountReview.sections.workInformation.sourceOfIncome" = "Source of income";
"ekycZoloz.accountReview.sections.workInformation.nameOfCompany" = "Name of company / business";
"ekycZoloz.accountReview.sections.address.title" = "Address";
"ekycZoloz.accountReview.sections.address.present" = "Present address";
"ekycZoloz.accountReview.sections.address.permanent" = "Permanent address";
"ekycZoloz.accountReview.sections.identification.title" = "Identification";
"ekycZoloz.accountReview.sections.identification.type" = "ID type";
"ekycZoloz.accountReview.sections.identification.idNumber" = "ID number";
"ekycZoloz.accountReview.sections.identification.expirationDate" = "Expiration date";
"ekycZoloz.accountReview.sections.identification.idPhoto" = "ID photo";
"ekycZoloz.accountReview.sections.additionalDocuments.title" = "Additional Requirements";
"ekycZoloz.completingPersonalInformation.formValidationErrorMessage.nonPHProvince" = "You cannot declare a PH province for a non PH country";
"ekycZoloz.completingPersonalInformation.formValidationErrorMessage.fieldNameIsRequired" = "%@ is required";
"ekycZoloz.completingPersonalInformation.formValidationErrorMessage.isRequired" = "Required";
"ekycZoloz.completingPersonalInformation.formValidationErrorMessage.invalidId" = "Please use a Valid ID";
"ekycZoloz.completingPersonalInformation.formValidationErrorMessage.numbersOnly" = "Please enter numbers only";
"ekycZoloz.completingPersonalInformation.formValidationErrorMessage.charactersLimit" = "Please enter up to %@ characters only";
"ekycZoloz.completingPersonalInformation.formValidationErrorMessage.dateFormat" = "MM/DD/YYYY";
"ekycZoloz.completingPersonalInformation.formValidationErrorMessage.ageToLow" = "You need to be at least 7 years old to proceed";
"ekycZoloz.completingPersonalInformation.formValidationErrorMessage.threeLetters" = "Character repeated 3 or more times consecutively";
"ekycZoloz.completingPersonalInformation.selectorTitle" = "Select %@";
"ekycZoloz.completingPersonalInformation.nameRulesInformationText.description" = "You are only allowed to use your legal name during registration. Learn more";
"ekycZoloz.completingPersonalInformation.nameRulesInformationText.highlighted" = "Learn more";
"ekycZoloz.completingPersonalInformation.nameRulesInformationText.link" = "ekycZoloz/nameRules/learnMore";
"maya.ekyc.completingPersonalInformation.addressDropdown.error.title" = "That didn't load right.";
"maya.ekyc.completingPersonalInformation.addressDropdown.error.message" = "We ran into a connection issue. Please try again in a bit.";
"ekycZoloz.allGoods.alert.title" = "All good?";
"ekycZoloz.allGoods.alert.message" = "Please make sure everything's OK to keep the approval process buttery smooth.";
"ekycZoloz.allGoods.alert.additionalInfo.message" = "You can only have one Maya account at a time. By submitting, you allow us to restrict other accounts in your name.";
"ekycZoloz.allGoods.alert.continueButton.title" = "Continue";
"ekycZoloz.allGoods.alert.checkAgainButton.title" = "Check again";
"ekycZoloz.changedYourMind.alert.title" = "Changed your mind?";
"ekycZoloz.changedYourMind.alert.message" = "This will make you resubmit your ID.\nAre you sure you want to go back?";
"ekycZoloz.changedYourMind.alert.submitButton.title" = "Confirm";
"ekycZoloz.changedYourMind.alert.checkAgainButton.title" = "Cancel";
"ekycZoloz.additionalDocuments.takePhoto.tap" = "Tap to ";
"ekycZoloz.additionalDocuments.takePhoto.take" = "take";
"ekycZoloz.additionalDocuments.takePhoto.photo" = " a photo";

/* Error Modal Title */
"error.password.title" = "Password";
"error.login.title" = "Login Error";
"error.registration.title" = "Registration Error";
"error.verification.title" = "Verification Code Error";
"error.authenticate.title" = "Authentication Error";
"error.privacy.title" = "Data Privacy Error";
"error.change.password.title" = "Change Password Error";
"error.activate.card.title" = "Activate Virtual Card Error";
"error.resendReferenceId.title" = "Resend Reference ID Error";
"error.code.generation.title" = "Code Generation Error";
"error.merchant.payment.title" = "Merchant Payment Error";
"error.upgrade.account.title" = "PayMaya Upgrade Error";
"error.sendmoney.title" = "Send Money Error";
"error.sendmoney.bank.title" = "Bank Transfer Error";
"error.shop.title" = "Shop Error";
"error.paybills.title" = "Pay Bills Error";
"error.cardrenewal.title" = "Card Renewal Error";
"error.westernUnion.title" = "Western Union Error";
"error.cashin.title" = "Cash In Error";
"error.inviteCode.title" = "Invite Code Error";
"error.missions.title" = "Missions Error";
"error.missions.message" = "We did not get a response from the server. \nPlease try again in a bit.";
"error.settings.biometrics.title" = "Change Settings Error";
"error.request.money.title" = "Request Money Error";
"error.vouchers.title" = "Vouchers Error";
"error.vouchersClaim.title" = "Vouchers Claim Error";
"error.voucherClaim.title" = "Voucher Claim Error";
"error.accountLimits.title" = "Account Limits Error";
"error.webview.title" = "The page did not load properly. Refresh Now";
"error.webview.title.actionTrigger" = "Refresh Now";
"error.receipt.title" = "Receipt Error";
"error.purchaseHistory.title" = "Purchase History";
"error.shop.timeout.action.title" = "Go back to home";
"error.transport.title" = "Purchase Error";
"error.set.public.name.title" = "Set Public Name Error";
"error.maya.credit.optin.title" = "Join Maya Easy Credit Error";
"error.maya.credit.title" = "Maya Easy Credit Error";
"error.join.maya.credit.title" = "Join Maya Easy Credit Error";
"error.faceVerification.title" = "Face Verification Error";
"error.new.maya.credit.title" = "That didn't work";
"error.maya.credit.close.button.title" = "Try again";
"error.shield.finger.printing.title" = "We couldn't log you in.";
"error.did.not.load.title" = "That didn't load right.";

/* Error Spiels */
"error.spiel.validation.message" = "%@ is required. \nPlease try again.";
"error.spiel.timeout" = "PayMaya is taking too long to respond to your request, please try again later.";
"error.spiel.invalid.link" = "The link is currently unavailable.";
"error.spiel.password" = "Please re-enter your password. It must be 8 characters long with at least one uppercase, one lowercase, one number, and has no spaces.";
"error.spiel.new.password.validation" = "Please re-enter your password. It must be 8 characters long and has no spaces.";
"error.spiel.login.forgotPassword" = "Forgot your password?";
"error.spiel.login.reset" = "Reset";
"error.invalid.amount" = "Cash in amount should be from %@ up to %@ only.";
"error.database.saving" = "Error in writing to database.";
"error.upgrade.camera.unavailable" = "Device has no camera.";
"error.upgrade.id.image.required" = "Any Government Issued ID is required.\nPlease try again.";
"error.verification.code.required" = "Verification Code is required.\nPlease try again.";
"error.spiel.otp.required" = "OTP Code is required.\nPlease try again.";
"error.spiel.retyped.address.required" = "Re-typed Recovery Email Address is required.\nPlease try again.";
"error.spiel.email.notMatch" = "Email addresses do not match.";
"error.spiel.amount.insufficient" = "Insufficient funds in your PayMaya account";
"error.spiel.inviteCode.required" = "Invite code is required.\nPlease try again.";
"error.spiel.invalid.maya.mtcn" = "You might have entered the wrong 10-digit  MTCN";
"error.spiel.invalid.smartPadalaNumber" = "Please enter a valid 16-Digit account number.";
"error.spiel.voucher.not.found.message" = "Voucher is unavailable.";
"error.spiel.confirmation.its.us.message" = " It's not you, it's us! We're experiencing an internal service issue. Please try again later.  ";
"error.spiel.shop.provider.not.found.message" = "This brand does not exist in Shop yet. Please feel free to explore other brands in Shop.";
"error.spiel.server.shop.home.buyAgain.message" = "We cannot load your recent purchase at the moment.";
"error.spiel.unable.to.load.page" = "Sorry, we are unable to load the page. Please try again.";
"error.spiel.faceVerification.message" = "Please follow the on-screen instructions to complete your Face Verification.";
"error.spiel.shield.fingerPrinting.standard.message" = "Visit our Help Center below for assistance.";
"error.spiel.shield.fingerPrinting.special.message" = "Visit our Help Center to get assistance or contact our support hotline.";

/* Error Field Validation Spiels */
"error.spiel.activation.birthdate" = "Don't forget to fill in your birthday! Your details must be complete to activate your card.";
"error.spiel.activation.address" = "Don't forget to fill in your address! Your details must be complete to activate your card.";
"error.spiel.activation.barangay" = "Don't forget to fill in your barangay! Your details must be complete to activate your card.";
"error.spiel.activation.city" = "Don't forget to fill in your city address! Your details must be complete to activate your card.";
"error.spiel.activation.state" = "Don't forget to fill in your state/province! Your details must be complete to activate your card.";
"error.spiel.activation.zipcode" = "Don't forget to fill in your zip code! Your details must be complete to activate your card.";

/* Introduction screen */
"introduction.page1.title" = "PayMaya is \nnow Maya.";
"introduction.page1.accent.text" = "Maya.";
"introduction.page1.description" = "We’ve gone beyond payments. Savings, wallet, crypto, credit, and more — welcome to your new all-in-one money app.\n\n​Enjoy this exclusive early access to Maya while we roll out the full experience. Let’s go!";
"introduction.page2.title" = "Maya is a wallet.\nAnd a bank!";
"introduction.page2.accent.text" = "bank!";
"introduction.page2.description" = "Start a secure savings account and earn some of the highest interest rates out there.\n\nThen you can pay your bills, send money, and shop cashless online or in real life. All that and then some.";
"introduction.page3.title" = "Maya puts your money to work.\nFast.";
"introduction.page3.accent.text" = "Fast.";
"introduction.page3.description" = "Dive into crypto. Get credit in a flash. Insure the things that matter. Everything you need to master your money.";
"introduction.save.title" = "Save";
"introduction.borrow.title" = "Borrow";
"introduction.grow.title" = "Grow";
"introduction.pay.title" = "Pay";
"introduction.send.title" = "Send";
"introduction.save.description" = "Earn high interest daily from PDIC-insured deposits in a BSP-licensed bank.​";
"introduction.borrow.description" = "Get cash quick for emergencies or fund your next big life move.​";
"introduction.grow.description" = "Explore your money’s potential with crypto. Stocks and funds coming soon!​";
"introduction.pay.description" = "Pay your bills, buy load, and scan a QR code to pay, including from other apps!​";
"introduction.send.description" = "Forget phone numbers and send money with a @username. Put it on your Maya card, too!​";

/* Registration screen*/
"registration.spiel.resendCode" = "Resend Code";
"registration.password.spiel.minlength" = "At least %@ characters and no spaces.";
"registration.firstname.field.name" = "First name";
"registration.firstname.field.placeholder" = "Enter first name";
"registration.firstname.required" = "First name is required";
"registration.middlename.field.name" = "Middle name";
"registration.middlename.field.placeholder" = "Enter middle name";
"registration.middlename.required" = "Middle name is required";
"registration.lastname.field.name" = "Last name";
"registration.lastname.field.placeholder" = "Enter last name";
"registration.lastname.required" = "Last name is required";
"registration.email.field.name" = "Email address (Optional)";
"registration.email.field.placeholder" = "Enter email address";
"registration.email.label.note" = "We’ll verify your email after you create an account.";
"registration.name.field.error.format.regex.invalid" = "Only letters, dashes, and apostrophes within 50 characters are allowed";
"registration.email.field.error.format.regex.invalid" = "A valid e-mail address is required";
"registration.name.field.error.format.repeating" = "Cannot have character repeated 3 or more times consecutively";
"registration.password.required" = "Password is required";
"registration.mobilenumber.required" = "Mobile Number is required";
"registration.first.spiel" = "Start an\naccount";
"registration.first.spiel.account" = "account";
"registration.second.spiel" = "Set your\nlogin details";
"registration.second.spiel.logindetails" = "login details";
"registration.second.inlinevalidation.validnumber" = "Please enter a valid mobile number";
"registration.second.tinbo.spiel" = "I don’t have a Philippine SIM\nGet a Tinbo SIM (PLDT Global) to use with Maya";
"registration.second.tinbo.urlSpiel" = "I don’t have a Philippine SIM";
"registration.second.tinbo.alert.title" = "Don’t have a Philippine SIM?";
"registration.second.tinbo.alert.message" = "Get a smart virtual number powered by Tinbo (from PLDT Global). You’ll be redirected to an external site by our third-party partner.";
"registration.second.tinbo.alert.button.title" = "Open tinbo.ph";
"registration.nomiddlename.checkbox.label" = "I have no legal middle name";

/* Shortened registration */
"shortened.registration.title" = "Create an account";
"shortened.registration.firstname.field.name" = "First name";
"shortened.registration.firstname.field.placeholder" = "Your first name";
"shortened.registration.middlename.field.name" = "Middle name";
"shortened.registration.middlename.field.placeholder" = "Your middle name";
"shortened.registration.no.legal.middlename" = "I have no legal middle name";
"shortened.registration.lastname.field.name" = "Last name";
"shortened.registration.lastname.field.placeholder" = "Your last name";
"shortened.registration.email.field.name" = "Email address (Optional)";
"shortened.registration.email.field.subtitle" = "We'll verify your email after you register";
"shortened.registration.email.field.placeholder" = "Your email address";
"shortened.registration.mobilenumber.field.name" = "Mobile number";
"shortened.registration.mobilenumber.field.placeholder" = "9 · ·  · · ·  · · · ·";
"shortened.registration.password.field.name" = "Password";
"shortened.registration.password.field.placeholder" = "Create a password";
"shortened.registration.data.personalization.title" = "Allow data use to improve our offers and services";
"shortened.registration.data.personalization.description" = "We’ll use your data to understand your needs, personalize features, and work with partners to offer relevant products";
"shortened.registration.continue" = "Continue";
"shortened.registration.flowProgressBar.progress" = "%@/2";
"shortened.registration.show.more.label" = "Show more";
"shortened.registration.show.less.label" = "Show less";
"shortened.registration.password.tips.label" = "👌 Strong password tips";

/* Forgot Password Screen */
"forgotPassword.title" = "Enter your number";
"forgotPassword.subtitle" = "Please use the number registered to your Maya account";
"forgotPassword.next.button.title" = "Next";
"forgotPassword.mobileNumber.placeholder" = "**********";
"forgotPassword.inlineValidation.number.invalid" = "Please enter a valid mobile number";
"forgotPassword.inlineValidation.number.required" = "Mobile Number is required";
"forgotPassword.min.verification.error.description" = "You have entered an Incorrect Number. Please try again.";
"forgotPassword.alert.button.retry" = "Retry";
"forgotPassword.alert.button.backToLogin" = "Back to Login";

/* Set Password Screen */
"setPassword.title" = "Set a new password";
"setPassword.subtitle" = "This will be used for login and account recovery";
"setPassword.tips" = "Tips for a secure password";
"setPassword.newPassword.title" = "New password";
"setPassword.newPassword.placeholder" = "Enter new password";
"setPassword.confirmPassword.title" = "Confirm password";

/* Password Changed Screen */
"passwordChanged.title" = "Password changed";
"passwordChanged.subtitle" = "You can now login with your new password";
"passwordChanged.tips.title" = "Remember this tip 💡";
"passwordChanged.tips.subtitle" = "Maya's official representatives will never ask for your:";
"passwordChanged.tips.never" = "never";
"passwordChanged.tips.list" = " •  Password
 •  16-digit card number or CVV
 •  One-Time Pin (OTP)";

/* Profile screen */
"profile.menu.title.authorizeRequests" = "Authorize requests";
"profile.menu.title.shop" = "Shop";
"profile.menu.title.cards" = "My Cards";
"profile.menu.title.profileqr" = "Receive Money";
"profile.menu.title.deals" = "Deals";
"profile.menu.title.inbox" = "Inbox";
"profile.menu.title.inviteCode" = "Submit Invite Code";
"profile.menu.title.guide" = "Quick Guide";
"profile.menu.title.favorites" = "My Favorites";
"profile.menu.title.mayaFavorites" = "Favorites";
"profile.menu.title.missions" = "Missions";
"profile.menu.title.settings" = "Settings";
"profile.menu.title.accountLimits" = "Account Limits";
"profile.menu.title.help" = "Get help";
"profile.menu.title.topup" = "Where to load up";
"profile.menu.title.howto" = "How to use";
"profile.menu.title.signout" = "Sign out";
"profile.menu.title.partnerMerchants" = "Partner Merchants";
"profile.menu.title.vouchers" = "Vouchers";
"profile.menu.title.submitInviteCode" = "Submit invite code";
"profile.menu.title.mayaGuide" = "Quick guide";
"profile.menu.title.securityCenter" = "Security center";
"profile.menu.title.mayaAccountLimits" = "Account limits";
"profile.menu.title.logout" = "Log out";
"profile.menu.title.updateProfile" = "Update Profile";
"profile.menu.title.workDetails" = "Work Details";
"profile.menu.title.contactReference" = "Contact Reference";
"profile.menu.title.updateProfileDeeplink" = "Update Profile (Deeplink)";
"profile.menu.title.workDetailsDeeplink" = "Work Details (Deeplink)";
"profile.menu.title.contactReferenceDeeplink" = "Contact Reference (Deeplink)";
"profile.menu.title.rateThisApp" = "Rate this app";
"profile.menu.title.inviteAFriend" = "Invite a friend";
"profile.logout.confirmation.message" = "Are you sure you want to log out?";
"profile.logout.confirmation.action.signout" = "Log Out";
"profile.signout.confirmation.message" = "Are you sure you want to sign out?";
"profile.signout.confirmation.action.signout" = "Sign Out";
"profile.signout.confirmation.action.cancel" = "Cancel";
"profile.kyc.button.upgrade" = "Upgrade my Account";
"profile.kyc.button.review" = "Review Application";
"profile.ekyc.button.checkApplicationStatus" = "Check Application Status";
"profile.ekyc.spiel.upgradeProcessingEdd" = "You account upgrade application is still under review.";
"profile.ekyc.spiel.upgradeProcessing" = "We are processing your upgrade.";
"profile.ekyc.spiel.upgrade" = "Upgrade now to enjoy more features and higher account limits!";
"profile.account.upgrade.title" = "Get more features";
"profile.account.rekyc.upgrade.title" = "Your personal info is outdated";
"profile.account.upgrade.spiel" = "Enjoy high-interest savings, access to loans, easy transfers with @username, and more when you upgrade your account.";
"profile.account.rekyc.upgrade.spiel" = "Some of your information may be outdated. Please update to access all of Maya's features.";
"profile.account.upgrade.button.title" = "Upgrade now for free";
"profile.account.review.title" = "Account under review";
"profile.account.review.spiel" = "Please give us up to 24 hours to review and update your account status.";
"profile.account.review.button.title" = "Check status";
"profile.duplicate.account.title" = "Duplicate account";
"profile.duplicate.account.spiel" = "Uh oh! You may already have a registered Maya account under a different number. Adding money to this account will be temporarily blocked.";
"profile.duplicate.account.visit.support.page" = "Visit support page";
"profile.account.verified.seller.about" = "Verified Sellers are legitimate small business owners that accept payments using Maya. As Verified Sellers, they get a badge that signals credibility and trustworthiness, and enjoy higher account limits and access to exclusive deals.\n
You become a Verified Seller if you:\n
∙  Have an upgraded Maya account
∙  Consistently receive payments in your PayMaya account
∙  Maintain a good track record with no customer complaints";

/* Profile QR */
"profile.qr.header.spiel" = "Send money to";
"profile.qr.scan.header.spiel" = "Just scan the QR Code below.";

/* Settings screen */
"settings.menu.title.privacy" = "Privacy Policy";
"settings.menu.title.terms" = "Terms of Service";
"settings.menu.title.tnc" = "Terms and Conditions";
"settings.menu.title.account.closure" = "Account Closure";
"settings.menu.title.mobileNumber" = "Mobile Number";
"settings.password.invalid.format" = "You have entered an invalid password. Please re-enter your PayMaya password.";
"settings.password.not.match" = "Passwords do not match";
"settings.version.number" = "Version %@ (%@)";
"settings.biometrics.default" = "Device Passcode";
"settings.biometrics.touchId" = "Touch ID";
"settings.biometrics.faceId" = "Face ID";
"settings.biometrics.disabled.confirmation.message" = "This will prevent you from using your phone lock credentials to login to PayMaya. Please enter your password to confirm";
"settings.biometrics.enabled.confirmation.message" = "This feature will allow you to use your phone lock credentials to login to PayMaya. Please enter your PayMaya password to confirm";
"settings.biometrics.error.message" = "There was a problem encountered processing this request";

/* Maya Settings Change Password */
"maya.settings.password.alert.settingsUpdated" = "Settings updated";
"maya.settings.password.alert.passwordChanged" = "Password changed!";
"maya.settings.password.alert.congratulations" = "Congratulations, you have successfully updated your preferred settings.";
"maya.settings.password.alert.description" = "You can update your password anytime by going to settings.";
"maya.settings.password.tips" = "•  Combine upper and lower case letters, numbers, and special characters (e.g., $, #, &, etc.).
•  Keep your password at least 6 to 12 characters long.
•  Avoid consecutive characters (e.g., 12345, abcde, qwerty, etc.) or repeating characters (e.g., 11111).
•  Avoid personal info like names of friends or relatives, your birthday, or your address.
•  Avoid common or obvious words (e.g., password, maya, bank, money, etc.).
•  Avoid using the same password from other accounts you own.";
"maya.settings.password.validation.not.match" = "Does not match your new password";
"maya.settings.password.validation.not.allowed" = "Please re-enter your password. It must be at least 6 characters long and has no spaces";
"maya.settings.biometrics.enable.generic.title" = "Enable %@ login";
"maya.settings.biometrics.enable.generic.description" = "Please enter your password to confirm that you are enabling %@ login";
"maya.settings.biometrics.disable.generic.title" = "Disable %@ login";
"maya.settings.biometrics.disable.generic.description" = "Please enter your password to confirm that you are disabling %@ login";
"maya.settings.biometrics.error.description" = "Whoops! Looks like your password is incorrect. Please try again.";

/* Licenses screen */

"licenses.title" = "Libraries We Use";
"licenses.subtitle" = "PayMaya uses the following open source libraries:";

/* Data Privacy screen */
"privacy.viewterms" = "View Terms and Conditions";
"privacy.title" = "Data Privacy Policy";
"privacy.title.with.version" = "Data Privacy Policy Version %@";
"privacy.disagree.confirmation" = "By disagreeing on our Terms and Conditions, your registration will be cancelled.";
"privacy.hasUpdate.title" = "Data Privacy Policy and Terms of Service Update Version";
"privacy.hasUpdate" = "We have updated our Data Privacy Policy and Terms of Service.";
"privacy.alert.disagree" = "Disagree";
"privacy.policy.title" = "Privacy Policy";
"privacy.terms.and.conditions.title" = "Terms and Conditions";

/* Authenticate (OTP) screen */
"authenticate.instruction" = "Please enter the One-Time PIN (OTP) we sent to %@";
"authenticate.new.instruction" = "Please enter the One-Time PIN (OTP) that we sent to %@";
"authenticate.oldmin.instruction" = "Please enter the One-Time PIN we sent to your current number: %@.";
"authenticate.newmin.instruction" = "Please enter the One-Time PIN we sent to your new number: %@.";
"authenticate.support" = "tel://(+632)88457788";
"authenticate.resendcode.spiel" = "Resend Code in %ld %@...";
"authenticate.resendcode.new.spiel" = "in %ld %@";
"authenticate.visit.help.center.spiel" = "Visit our Help Center to learn more";
"authenticate.help.center.spiel" = "Help Center";
"authenticate.otp.instructions.spiel" = "Please enter the one-time PIN (OTP) that we sent to %@";
"authenticate.otp.cta.title" = "Proceed";
"authenticate.registration.otp.cta.title" = "Verify";
"authenticate.registration.otp.screen.title" = "One-time PIN";
"authenticate.registration.otp.success.title" = "You’re in! Welcome to Maya.";
"authenticate.registration.otp.success.message" = "You now own a basic account. Upgrade for free now and enjoy everything Maya has to offer.";
"authenticate.registration.submitInviteCode.title" = "Got an invite code?";
"authenticate.registration.submitInviteCode.subtitle" = "Submit your friend’s invite code";
"authenticate.registration.submitInviteCode.inviteCode" = "invite code";
"authenticate.resendcode.new.spiel" = "Resend code in %ld %@...";

/* My Cards screen */
"mycards.paymaya.card.name" = "PayMaya Card";
"mycards.link.card.title" = "Link PayMaya Card";
"mycards.link.card.success.title" = "New PayMaya Card added!";
"mycards.activate.card.title" = "Your Online Payment Card Number is now activated!";
"mycards.activate.card.message" = "You can now receive money. Go to your nearest reloading stations to know more.";
"mycards.view.card.title" = "View Card";
"mycards.virtual.balanceTreshold.message" = "To view your virtual card, you must have at least Php100 in your PayMaya account.";
"mycards.card.masking.default" = "**** **** **** %@";
"mycards.card.masking.bancnet" = "***** ***** ***** %@";

/* Confirm Credentials */
"credential.authentication.reason" = "Login with your %@";
"credential.authentication.confirmation" = "Do you want to use %@ for user authentication?";

/* Forgot Password */
"forgot.password.title" = "Forgot Password";
"forgot.password.success" = "Password successfully changed.";

/* Activity Details */
"activity.detail.account.number" = "Account Number";
"activity.detail.merchant.id" = "Merchant ID";
"activity.detail.reference.id" = "Reference ID";
"activity.detail.reference.merchant.number" = "Card Transaction ID";
"activity.detail.receipt.number" = "Receipt No.";
"activity.detail.reason" = "Reason";
"activity.detail.recipient" = "Recipient";
"activity.detail.payment.id" = "Payment ID";
"activity.detail.amount" = "Amount";
"activity.detail.transaction.fee" = "Transaction Fee";
"activity.detail.amount.paid" = "Amount Paid";
"activity.detail.fund.source" = "Fund Source";
"activity.detail.pay.bills.authorized" = "Amount has been reserved for payment to biller.";
"activity.detail.pay.bills.posted" = "Amount has been sent to the biller.";
"activity.detail.pay.bills.rejected" = "Payment to this biller has failed. The bill amount and Biller Convenience Fee has been credited back to your account.";
"activity.detail.transaction.message.authorized" = "The reserved authorization amount may differ from the final amount once successfully settled with the merchant.";
"activity.detail.transaction.message.completed" = "The final amount has been sent to the merchant.";
"activity.detail.transaction.message.cancelled" = "This purchase has been cancelled and the amount has been credited back to your account.";
"activity.detail.transaction.instapay.message" = "You may confirm the status of your transaction with your recipient.";
"maya.activity.detail.invoice.number" = "Invoice number";
"maya.activity.detail.trace.number" = "Trace number";
"maya.activity.detail.purchase.date" = "Purchase date";
"maya.activity.detail.bank.name" = "Bank name";
"maya.activity.detail.amount.approved" = "Amount - Approved";
"maya.activity.detail.amount.cancelled" = "Amount - Cancelled";
"maya.activity.detail.account.type" = "Account type";
"maya.activity.detail.account.name" = "Account name";
"maya.activity.detail.account.number" = "Account number";
"maya.activity.detail.item" = "Product name";
"maya.activity.detail.fund.source" = "Fund source";
"maya.activity.detail.payment.reference" = "Payment reference";
"maya.activity.detail.withdrawal.date" = "Withdrawal date";
"maya.activity.detail.share.p2p.message" ="Sent %@ from my Maya Account to %@ with Ref# %@ on %@";
"maya.activity.detail.date" = "Date";

/* Dashboard */
"dashboard.errorSpiel.activity" = "Recent activities can’t be updated right now. Please try again later.";
"dashboard.errorSpiel.balance" = "Available balance can’t be updated right now. Please try again later.";
"dashboard.errorSpiel.balanceAndActivity" = "Available balance and recent activities can’t be updated right now. Please try again later.";
"dashboard.service.title.bankTransfer" = "Bank\ntransfer";
"dashboard.service.title.bills" = "Bills";
"dashboard.service.title.cards" = "Cards";
"dashboard.service.title.cashIn" = "Cash in";
"dashboard.service.title.creditCards" = "Credit Cards";
"dashboard.service.title.crypto" = "Crypto";
"dashboard.service.title.deals" = "Deals";
"dashboard.service.title.donate" = "Donate";
"dashboard.service.title.esims" = "eSIMs";
"dashboard.service.title.food" = "Food";
"dashboard.service.title.funds" = "Funds";
"dashboard.service.title.globalRemittance" = "Global remittance";
"dashboard.service.title.globalStocks" = "Global Stocks";
"dashboard.service.title.government" = "Government";
"dashboard.service.title.insurance" = "Insurance";
"dashboard.service.title.inviteAFriend" = "Invite a friend";
"dashboard.service.title.load" = "Load";
"dashboard.service.title.loans" = "Personal Loans";
"dashboard.service.title.lrt1" = "LRT-1";
"dashboard.service.title.luckyGames" = "Lucky Games";
"dashboard.service.title.mayaCredit" = "Maya Easy Credit"; // Duplicate
"dashboard.service.title.mayaCredit" = "Credit"; // Duplicate
"dashboard.service.title.mayaMall" = "Maya Mall";
"dashboard.service.title.merchantRewards" = "Merchant Rewards";
"dashboard.service.title.merchants" = "Merchants";
"dashboard.service.title.missions" = "Missions";
"dashboard.service.title.more" = "More";
"dashboard.service.title.myCards" = "Cards";
"dashboard.service.title.myQR" = "My QR";
"dashboard.service.title.pal" = "PAL";
"dashboard.service.title.payLater" = "PayLater";
"dashboard.service.title.pbb" = "PBB";
"dashboard.service.title.personalGoals" = "Personal Goals";
"dashboard.service.title.protect" = "Protect";
"dashboard.service.title.purchaseFinancing" = "Purchase Financing";
"dashboard.service.title.rafflePromo" = "Raffle Promo";
"dashboard.service.title.retail" = "Retail";
"dashboard.service.title.savings" = "Savings";
"dashboard.service.title.savingsToWallet" = "Savings To Wallet";
"dashboard.service.title.securityCenter" = "Security center";
"dashboard.service.title.sendMoney" = "Send\nmoney";
"dashboard.service.title.scanqr" = "Pay with QR";
"dashboard.service.title.scheduler" = "Schedules";
"dashboard.service.title.shop" = "Shop";
"dashboard.service.title.sparkHackathon" = "Spark Hackathon";
"dashboard.service.title.stocks" = "Stocks";
"dashboard.service.title.stocksGame" = "Stocks game";
"dashboard.service.title.stream" = "Stream";
"dashboard.service.title.timeDeposit" = "Time Deposit Plus";
"dashboard.service.title.travel" = "Travel";
"dashboard.service.title.voucher" = "Vouchers";
"dashboard.menu.title.wallet" = "Wallet";
"dashboard.menu.title.savings" = "Savings";
"dashboard.menu.title.credit" = "Credit";
"dashboard.menu.title.loans" = "Loans";
"dashboard.menu.title.cards" = "Cards";
"dashboard.rewards.missions.title" = "Missions";
"dashboard.rewards.missions.subtitle" = "Earn rewards for\ncompleting tasks";
"dashboard.rewards.vouchers.title" = "Vouchers";
"dashboard.rewards.vouchers.subtitle" = "Go claim them\nbefore they’re gone";
"dashboard.latest.activities.title" = "Transactions";
"dashboard.footer.message" = "Maya Philippines, Inc. is regulated by the Bangko Sentral ng Pilipinas – bsp.gov.ph.\nVisit our Help Center or check our service advisories for any concerns.";
"dashboard.footer.service.advisories" = "service advisories";
"dashboard.service.title.blackPinkGiveaway" = "Deadline\nTickets";

/* Scanner */
"scanner.instructions.default" = "Align QR Code to scan.";
"scanner.instructions.invalid.qr" = "QR Code Invalid. \nPlease scan another code.";
"scanner.instructions.invalid.photo.error.title" = "Import QR Error";
"scanner.instructions.invalid.photo.error.message" = "Uh oh! We can’t seem to read the QR you selected. Please try again with a clearer image or make sure to use a PayMaya QR code.";

/* Maya Scanner */
"maya.scanner.instructions.default" = "Align QR Code to scan.";
"maya.scanner.instructions.invalid.qr" = "QR Code Invalid. \nPlease scan another code.";
"maya.scanner.instructions.invalid.photo.error.title" = "Upload QR error";
"maya.scanner.instructions.invalid.photo.error.message" = "We are unable to read the QR you selected. Please upload a clearer image of the QR Ph code and try again.";
"maya.scanner.invalid.qr" = "Please scan a valid QR Code";
"maya.scanner.invalid.bills" = "Bills QR not supported";

/* Scanner */
"barcode.instructions.default" = "Align Barcode to scan.";
"barcode.instructions.invalid" = "Barcode Invalid. \nPlease try again.";
"barcode.detection.failed.reason" = "Not Supported";

/* Import Qr */
"import.detection.failed.reason" = "No qr detected";

/* Permissions */
"permission.camera.scanner.title" = "Allow PayMaya to Access the Camera";
"permission.camera.scanner.message" = "Your camera will be used to scan QR and barcodes, and take photos and videos of account upgrading requirements.";

/* P2M */
"p2m.cancel.transaction.title" = "Cancel Transaction";
"p2m.cancel.transaction.message" = "Are you sure you want to cancel this transaction?";
"p2m.cancel.transaction.approval" = "Yes, Cancel";
"p2m.maintenance.message" = "Non-Maya QR payment is currently under maintenance. Please check again later.";
"p2m.maintenance.screenTitle" = "Non-Maya QR Payment";
"p2m.maintenance.title" = "We'll be back shortly.";
"p2m.success.title" = "Payment Successful!";
"p2m.success.message" = "Payment Receipt";
"p2m.success.detail.title.merchant" = "Merchant";
"p2m.success.detail.title.amount" = "Amount";
"p2m.success.detail.title.referenceid" = "Reference ID";
"p2m.success.detail.title.updatedbalance" = "Updated Balance";
"p2m.success.back.to.merchant" = "Back to Merchant";
"p2m.success.back.to.merchant.bottomsheet.withCountdown" = "Back to merchant in %@";
"p2m.success.back.to.merchant.bottomsheet" = "Back to merchant";

/* Maya P2M */
"maya.p2m.paying.for.maximum.amount" = "Maximum amount of ₱%@";
"maya.p2m.flowProgressBar.progress" = "%@/2";
"maya.p2m.payment.failure.spiel" = "We tried but failed with your payment. No worries, refund is on its way.";

/* Bank Transfer */
"banktransfer.form.accountName" = "Account Name";
"banktransfer.form.amount.default" = "₱0";
"banktransfer.form.amount.info" = "You have %@ in your wallet.";
"banktransfer.form.amount.fees" = "Transaction fee may apply.";
"banktransfer.form.amount.feeApply" = "₱%@ transaction fee applies.";
"banktransfer.form.amount.insufficient" = "You only have %@ in your wallet.";
"banktransfer.form.amount.required" = "Amount is required. You have %@ in your wallet.";
"banktransfer.form.hasInvalidCharacters" = "%@ has invalid characters";
"banktransfer.form.purpose.required" = "Purpose is required";
"banktransfer.form.purpose" = "Purpose";
"banktransfer.form.purpose.limit" = "Please enter up to %@ characters only";
"banktransfer.form.accountNumber.required" = "Account number is required";
"banktransfer.form.accountName.required" = "Account name is required";
"banktransfer.bank.not.supported.spiel" = "Sorry, this bank is not yet supported.";
"banktransfer.confirmation.transferNow" = "Transfer now";
"banktransfer.confirmation.spiel" = "Is the receiver someone you know and trust? Once sent, Maya can no longer refund your money. Always check if the recipient, account details, and amount are entered correctly before continuing.";
"banktransfer.confirmation.spielHeader" = "Make sure this transaction is safe ✋";
"banktransfer.confirmation.correct" = "correct";
"banktransfer.confirmation.myWallet" = "My Wallet";
"banktransfer.confirmation.destination" = "Destination";
"banktransfer.confirmation.purpose" = "Purpose";
"banktransfer.confirmation.transferAmount" = "Transfer amount";
"banktransfer.success.title" = "You’re almost there!";
"banktransfer.success.message" = "We’re now processing this bank transfer to bank account ending %@.";
"banktransfer.favorites.update.success.spiel" = "Favorite's details have been saved";
"banktransfer.favorites.update.failed.spiel" = "Favorite's details failed to saved";
"banktransfer.favorites.save.spiel" = "Save details to favorites";
"banktransfer.favorites.details.spiel" = "Bank transfer details";
"banktransfer.favorites.success.message.spiel" = "You can now use Favorites for quick and easy bank transfers";

/* Send Money */
"sendmoney.recipientType.smartPadala" = "Smart Padala by PayMaya";
"sendmoney.recipientType.paymaya" = "PayMaya";
"sendmoney.spiel.from" = "- From";
"sendmoney.spiel.transferFee" = "Transfer Fee: PHP ";
"sendmoney.spiel.required.recipient" = "Name, Mobile or Account Number is required. Please try again.";
"sendmoney.spiel.required.bank" = "Bank is required. Please try again.";
"sendmoney.spiel.required.amount" = "Amount is required. Please try again.";
"sendmoney.spiel.required.accountNumber" = "Account number is required. Please try again.";
"sendmoney.spiel.required.firstName" = "First name is required. Please try again.";
"sendmoney.spiel.required.lastName" = "Last name is required. Please try again.";
"sendmoney.spiel.required.purpose" = "Purpose is required. Please try again.";
"sendmoney.spiel.required" = "This field is required";
"sendmoney.success.title" = "Send Money Successful!";
"sendmoney.success.detail.recipient" = "Recipient";
"sendmoney.success.detail.transferFee" = "Transfer Fee";
"sendmoney.success.detail.transferFee.colon" = "Transfer Fee:";
"sendmoney.addFavorites.alias.example.label" = "House, Postpaid";
"sendmoney.recipient.details" = "Recipient Details";
"sendmoney.bank.title" = "Bank Transfer";
"sendmoney.bank.details" = "Bank Details";
"sendmoney.bank.success.receipt.title" = "Processing Bank Transfer Transaction";
"sendmoney.bank.success.detail.accountType" = "Bank";
"sendmoney.bank.success.detail.accountNumber" = "Account Number";
"sendmoney.bank.success.detail.accountNo.colon" = "Account No.:";
"sendmoney.bank.success.detail.accountName" = "Account Name";
"sendmoney.bank.success.detail.mobile" = "Recipient Mobile Number";
"sendmoney.bank.success.detail.amount" = "Amount";
"sendmoney.bank.success.detail.fee" = "Transaction Fee";
"sendmoney.bank.success.detail.id" = "Reference ID";
"sendmoney.bank.success.detail.purpose" = "Purpose";
"sendmoney.bank.success.detail.hint" = "You may confirm the status of your transaction with your recipient.";
"sendmoney.bank.field.amount" = "Amount";
"sendmoney.bank.field.purpose" = "Purpose";
"sendmoney.bank.field.accountNumber" = "Account Number";
"sendmoney.bank.field.firstName" = "First Name";
"sendmoney.bank.field.lastName" = "Last Name";

"sendmoney.field.recipient" = "Name, Mobile or Account Number";
"sendmoney.field.amount" = "Amount";
"sendmoney.field.accountType" = "Account Type";
"sendmoney.field.message" = "Message";
"sendmoney.field.recipient.or.accountNumber" = "Recipient/Account Number";

"sendmoney.maya.message.signature" = "- from %@";

/* Maya Send Money */
"sendmoney.form.amount.default" = "₱0";
"sendmoney.form.amount.info" = "You have %@ in your wallet.";
"sendmoney.form.amount.fees" = "Transfer fees may apply.";
"sendmoney.form.amount.insufficient" = "You only have %@ in your wallet.";
"sendmoney.form.amount.required" = "Amount is required. You have %@ in your wallet.";
"sendmoney.title.accountType.maya" = "Maya";
"sendmoney.title.accountType.mayaCenter" = "Maya Center";
"sendmoney.success.bottomsheet.title" = "You've successfully sent %@";
"sendmoney.success.bottomsheet.message" = "From your wallet to %@";
"sendmoney.favorites.details.spiel" = "Send money details";
"sendmoney.favorites.success.message.spiel" = "You can now easily send money under favorites.";

/* Send Money Cards */
"sendmoney.card.receiver.shareMesage" = "You have received %@ on your PayMaya account from PayMaya user %@.";
"sendmoney.card.sender.shareMesage" = "You have successfully sent %@ from your PayMaya account to PayMaya user %@.";
"sendmoney.confirmation.button.sendNow" = "Send now";
"sendmoney.activity.title.sender" = "You have sent";
"sendmoney.activity.title.receiver" = "You have received";

/* Share */
"share.paybills.no.acct.number" = "Paid %@ %@ from my PayMaya Account to %@ with Ref# %@ on %@";
"share.maya.paybills.no.acct.number" = "Paid %@ %@ from my Maya Account to %@ with Ref# %@ on %@";

/* Cash In */
"cashin.screen.title" = "Cash In";
"cashin.screen.title.lowercase" = "Cash in";
"cashin.confirmation.title" = "Confirm transaction";
"cashin.maya.recommendedMethods.bankAccount.title" = "Bank account";
"cashin.maya.recommendedMethods.card.title" = "Debit or credit card";

"cashin.maya.walletSettingsMethods.title" = "Wallet options";
"cashin.maya.walletSettingsMethods.walletTransactionLimit.title" = "Wallet transaction limit";
"cashin.maya.walletSettingsMethods.autoCashin.title" = "Auto cash in";
"cashin.maya.walletSettingsMethods.instaFill.title" = "Insta fill";
"cashin.recommendedMethods.bankAccount.subtitle" = "Transfer funds directly from your bank account";
"cashin.recommendedMethods.card.subtitle" = "Cash In conveniently using your debit or credit card";

"cashin.recommendedMethods.mayaSavings.title" = "Maya Savings";


"cashin.maya.recommendedMethods.padala.title" = "Padala agents";
"cashin.maya.recommendedMethods.maya.center.title" = "Maya Center";
"cashin.recommendedMethods.padala.subtitle" = "Cash In at your nearest Smart Padala agent";
"cashin.cancel.code.title" = "Are you done using this Cash In Code?";
"cashin.code.expired.title" = "Cash In Code has expired";
"cashin.code.expired.message" = "Do you want to generate new code?";
"cashin.generate.new.code" = "Generate New Code";
"cashin.center.instructions.html" = "<body bgcolor=\"%@\"> <div style=\"font-family:-apple-system;color:#445157;margin-left:4px;margin-right:4px\"> %@ </div> </body>";
"cashin.westernUnion.faq" = "https://www.westernunion.com/us/en/what-is-mtcn.html";
"cashin.westernUnion.terms" = "https://www.westernunion.com/PH/en/terms-conditions.html";
"cashin.westernUnion.maya.noGovID" = "Remittance could not be credited to your account. You need to have a government ID linked to your Maya account. Please call (02) 845-7788 for more details.";
"cashin.via.western.union" = "via Western Union";
"cashin.success.title" = "Cash In successful!";
"cashin.success.maya.bottomsheet.title" = "You’ve successfully cashed in %@";
"cashin.success.topUp.title" = "Receive Money Successful!";
"cashin.success.detail.mtcn" = "MTCN";
"cashin.success.detail.sender" = "Sender";
"cashin.error.defaultMessage" = "There was a problem with the card you provided. Please contact your card issuer for further assistance.";
"cashin.spiel.mayaTerms" = "Maya’s Terms & Conditions";
"cashin.spiel.westernUnion" = "Western Union";
"cashin.spiel.westernUnionTerms" = "Western Union’s Terms and Conditions";
"cashin.spiel.western.union.confirmation.policy" = "By proceeding, you are accepting Maya’s Terms & Conditions and Privacy Policy and Western Union’s Terms and Conditions for the use of this service";
"cashin.viacard.form.hint" = "Choose from the recommended amounts below.";
"cashin.viacard.confirmation.button" = "Cash in now";
"cashin.viacard.confirmation.detail.source" = "Source";
"cashin.viacard.confirmation.detail.convenience" = "Convenience fee";
"cashin.viacard.confirmation.detail.totalAmount" = "Total amount to be deducted";
"cashin.viacard.confirmation.dontsave.header" = "Don’t save this Card";
"cashin.viacard.confirmation.dontsave.description" = "You have opted not to save this card";
"cashin.viacard.confirmation.info" = "Please contact your issuing bank or provider for any additional charges you may incur.";
"cashin.viacard.required.cardNumber" = "Card Number is required";
"cashin.viacard.required.cardNumber.new" = "Card number is required";
"cashin.viacard.required.cvv" = "CVV/CSC is required";
"cashin.viacard.required.cvv.new" = "CVV security code is required";
"cashin.viacard.required.expiry" = "Date is required";
"cashin.viacard.required.expiry.month" = "Month is required";
"cashin.viacard.required.expiry.year" = "Year is required";
"cashin.viacard.default.amount.note" = "Convenience fee may apply";
"cashin.viacard.termsAndConditions" = "Terms & Conditions";
"cashin.viacard.select.month" = "Select month";
"cashin.viacard.select.year" = "Select year";
"cashin.viacard.success.fundsource" = "Fund Source";
"cashin.3ds.leave.screen.title" = "Are you sure you want to leave?";
"cashin.3ds.leave.screen.message" = "Your progress will be lost";
"cashin.seven.amount.message.old" = "From %@ up to %@ only";
"cashin.seven.amount.message" = "Provide an amount between %@ up to %@. Convenience fee may apply.";
"cashin.seven.amount.error.message" = "Type a number between %@ up to %@";
"cashin.accountLimit.remaining.spiel" = "%@ remaining for cash in";
"cashin.accountLimit.learnMore.spiel" = "Limits will reset on the first day of the next month. Learn more about Account Limits";
"cashin.accountLimit.learnMore.new.spiel" = "Limits will reset on the first day of the next month. Learn more about account limits.";
"cashin.account.limits.text" = "Account Limits";
"cashin.account.limits.new.text" = "account limits";
"cashin.via.loadup.partner" = "Cash in via %@";
"cashin.from.loadup.partner" = "Cash in from %@";
"cashin.via.new.card" = "Cash in via new card";
"cashin.header.title" = "Cash In";
"cashin.amount.title" = "Cash in amount";
"cashin.amount.text" = "Amount";
"cashin.spiel.done" = "Are you done using cash in code?";
"cashin.spiel.generate.code" = "Generate Code";
"cashin.spiel.from.partner.to.wallet" = "From our partner to your wallet";
"cashin.spiel.successfully.received" = "You’ve successfully received %@";
"cashin.spiel.transaction.failed" = "Cash in transaction failed";
"cashin.spiel.something.went.wrong" = "Something went wrong. Please try again in a bit.";
"cashin.spiel.back.to.cashin" = "Back to cash in";
"cashin.spiel.wayfinder.padala" = "Find the nearest Padala agent around your area​";
"cashin.spiel.wayfinder.maya.center" = "Find the nearest Maya Centers around your area​";
"cashin.vaulted.card.disabled.spiel" =  "Sorry, temporarily we cannot proceed with your cash in transaction using this card.";
"cashin.remove.card.prompt.title" =  "Are you sure you want to remove your card?";
"cashin.remove.card.prompt.message" =  "Card number ending in %@";
"cashin.remove.card.success.title" =  "Card successfully removed";
"cashin.remove.card.success.message" =  "You have successfully deleted your %@ card from Maya.";
"cashin.maximum.limit.reached.title" = "Maximum limit reached";
"cashin.maximum.limit.reached.message" = "You can only use up to %d cards to cash in. Please delete an existing card to use a new card";
"cashin.confirm.transaction.title" = "You’re almost there!";
"cashin.confirm.transaction.message" = "We’re now processing this cash in transaction from your %@ ending %@.";
"cashin.failed.transaction.title" = "Cash in transaction failed";

/* Shop screen */
"shop.spiel.required.payments.accountNumber." = "%@ is required. Please try again.";
"shop.placeholder.cignal.accountNumber" = "Cignal Account Number";
"shop.placeholder.beep.accountNumber" = "Beep Card Account Number";
"shop.spiel.detail.title.recipient" = "Recipient";
"shop.spiel.detail.title.contact" = "Contact";
"shop.spiel.detail.title.source" = "Source";
"shop.spiel.detail.title.message" = "Message";
"shop.spiel.detail.placeholder.enter" = "Enter";
"shop.spiel.detail.title.message" = "Optional message";
"shop.spiel.detail.title.message.placeholder" = "Add a message";
"shop.spiel.detail.title.signature" = "Include signature:";

"shop.spiel.beep.confirmation" = "Your beep™ card balance will be updated once you tap your card on the beep™ e-Load/Ticket Vending Machine.";
"shop.search.error.message" = "Could not load more items. Please try again.";
"shop.product.disabled.price.message" = "Not Available";
"shop.notification.success" = "Successful Shop Purchase";
"shop.notification.success.details" = "Tap here to check the details";
"shop.purchase.title.buy" = "Buy";
"shop.payments.mobile.number.display.text" = "You (%@)";
"shop.payments.contact.mobile.number.display.text" = "%@ (%@)";
"shop.payments.signature.display.text" = "From %@";
"shop.payments.recipient.mobile.number.placeholder.text" = "Recipient's Mobile Number";
"shop.home.section.mostPopular.maya.title" = "MOST POPULAR";
"shop.home.section.load.maya.title" = "LOAD";
"shop.home.section.brandsForYou.maya.title" = "BRANDS FOR YOU";
"shop.home.section.deals.maya.title" = "DEALS & PROMOS";
"shop.home.section.buyAgain.maya.title" = "BUY AGAIN";

/* Maya Shop */
"maya.shop.search.loading.message" = "Please wait";
"maya.shop.product.disabled.price.message" = "Not Available";
"maya.shop.spiel.required.recipientMobileNumber" = "Please enter a valid mobile number";
"maya.shop.payment.confirmation.item.title" = "Product name";
"maya.shop.confirm.success.bottomsheet.title" = "You've purchased %@";
"maya.shop.confirm.success.bottomsheet.description" = "Thank you for always using Maya!";

/*Shop Purchase History*/
"shop.purchase.history.processing.title" = "Processing";
"shop.purchase.history.completed.title" = "Completed";
"shop.purchase.history.title" = "Purchase History";
"shop.purchase.history.account.number.title" = "Account Number";
"shop.purchase.history.processing.empty.default.spiel" = "Visit our shop and purchase from our wide variety of choices.";
"shop.purchase.history.processing.empty.final.spiel" = "High-five! All purchases have already been processed. Check the Completed tab for your full purchase history.";
"shop.purchase.history.failure.title" = "Something went wrong";
"shop.purchase.history.failure.spiel" = "We tried but failed with your purchase. No worries, refund is on its way.";

/* Pay bills */
"paybills.section.header.recentlyPaidBillers" = "Recently Paid Billers";
"paybills.section.header.otherBillers" = "Other Billers";
"paybills.section.header.categories" = "Categories";
"paybills.title.viewAll" = "View All";
"paybills.title.viewLess" = "View Less";
"paybills.spiel.required.accountNumber" = "Looks like you didn't enter the account number. Please enter the information to complete your payment.";
"paybills.spiel.required.amount" = "Looks like you didn't enter the amount. Please enter the information to complete your payment.";
"paybills.spiel.required.field" = "Looks like you didn't enter the %@. Please enter the information to complete your payment.";
"paybills.spiel.invalid.field" = "Looks like you didn't enter a valid %@. Please enter the information to complete your payment.";
"paybills.spiel.accountNumber.meralco" = "10-digit Customer Account Number found at the upper left portion of the SOA";
"paybills.spiel.amount.default" = "Biller Convenience Fee (BCF) may apply.";
"paybills.placeholder.default" = "Account Number";
"paybills.receipt.title.default" = "Account Number";
"paybills.activity.title.default" = "Account Number";
"paybills.spiel.processing.title" = "Processing Bills Payment Transaction";
"paybills.spiel.success.message" = "Payment Receipt";
"paybills.spiel.success.detail.biller" = "Biller";
"paybills.spiel.success.detail.amount" = "Amount";
"paybills.spiel.success.detail.bcf" = "Biller Convenience Fee";
"paybills.spiel.success.detail.accountNumber" = "Account Number";
"paybills.spiel.success.detail.referenceId" = "Reference ID";
"paybills.spiel.success.detail.updatedBalance" = "Updated Balance";
"paybills.addFavorites.alias.example.label" = "House, Postpaid";
"paybills.accountNumber.required" = "Account Number is required";
"paybills.amount.required" = "Amount is required";
"paybills.tin.required" = "TIN is required";
"paybills.field.required" = "%@ is required";
"paybills.field.invalid" = "%@ is invalid";
"paybills.search.placeholder" = "Search for billers or merchants";
"paybills.title.favorites" = "MY FAVORITES";
"paybills.categories.label" = "BILLER CATEGORIES";

/* Maya Pay bills */
"maya.paybills.form.amount.default" = "₱0";
"maya.paybills.biller.form.fields.placeholder" = "Enter %@";
"maya.paybills.biller.form.fields.select.placeholder" = "Select %@";
"maya.paybills.biller.form.fields.enter.tin.branch.code" = "Enter TIN branch code";
"maya.paybills.biller.form.fields.select.return.period.placeholder" = "Select a return date";
"maya.paybills.biller.form.fields.select.rdo.code.placeholder" = "Select RDO code";
"maya.paybills.biller.form.fields.for.maximum.amount" = "You only have %@ in your wallet.";
"maya.paybills.biller.form.fields.for.wrong.number.of.characters" = "Please enter a valid account number with 5 to 30 characters only.";
"maya.paybills.biller.form.fields.for.wrong.tin.number.of.characters" = "Please enter a valid TIN with 5 to 30 characters only.";
"maya.paybills.biller.form.fields.for.zero.amount" = "Amount must be greater than 0.";
"maya.paybills.biller.form.fields.for.bill.account.number" = "Account number";
"maya.paybills.biller.form.fields.for.bill.account.number.placeholder" = "Enter account number";
"maya.paybills.biller.form.fields.for.bill.customer.account.number" = "Customer account number";
"maya.paybills.biller.form.fields.for.bill.customer.account.number.placeholder" = "Enter customer account number";
"maya.paybills.biller.form.fields.for.bill.tin" = "TIN";
"maya.paybills.biller.form.fields.for.bill.tin.placeholder" = "Enter TIN";
"maya.paybills.biller.form.fields.for.bill.amount" = "Amount";
"maya.paybills.biller.form.fields.for.bill.amount.placeholder" = "Enter amount";
"maya.paybills.search.placeholder" = "Search for billers or merchants";
"maya.paybills.confirmation.success.bottomsheet.title" = "You’re almost there!";
"maya.paybills.confirmation.success.bottomsheet.subtitle" = "We’re now processing this bill payment to %@";
"maya.paybills.confirmation.success.bottomsheet.viewreceipt.button.title" = "View receipt";
"maya.paybills.confirmation.success.bottomsheet.done.button.title" = "Done";
"maya.paybills.confirmation.success.bottomsheet.saveToFavorites" = "Save details to favorites";
"maya.paybills.confirmation.bottomsheet.favorite.message" = "We’re now processing this bills payment to account ending %@.";
"maya.paybills.confirmation.bottomsheet.favorite.saved" = "Favorite's details have been saved";
"maya.paybills.confirmation.bottomsheet.favorite.failedUpdate" = "Favorite's details failed to update";
"maya.paybills.confirmation.viewinititialreceipt.button.title" = "View initial receipt";
"maya.paybills.biller.form.fields.customer.account.number.hint" = "Customer account number";
"maya.paybills.biller.confirmation.bill.ammount.placeholder" = "Amount";
"maya.paybills.biller.confirmation.bill.tin.placeholder" = "TIN";
"maya.paybills.biller.confirmation.bill.tin.branch.code.placeholder" = "TIN branch code";
"maya.paybills.biller.confirmation.bill.rdo.code.placeholder" = "RDO code";
"maya.paybills.savetofavorites.spiel" = "Pay bills details";
"maya.paybills.savetofavorites.success.title" = "Added to your favorites!";
"maya.paybills.savetofavorites.success.info" = "You can now easily pay bills under favorites.";
"maya.paybills.confirmation.title" = "Confirm payment";
"maya.paybills.confirmation.billAmount" = "Bill amount";
"maya.paybills.confirmation.billerConvenienceFee" = "Biller convenience fee";
"maya.paybills.biller.confirmation.pay.button.title" = "Pay";
"maya.paybills.receipt.billAmount" = "Bill amount";
"maya.paybills.receipt.billerConvenienceFee" = "Biller convenience fee";
"maya.paybills.receipt.billersFee" = "Biller's fee";
"maya.paybills.biller.subtext.mayaCredit" = "Accepts Maya Easy Credit";
"maya.paybills.biller.subtext.serviceUnavailable" = "Service temporarily unavailable";
"maya.paybills.sampleReminder.description" = "We will notify you to pay your bills on the day you select.";
"maya.paybills.featureBanner.spiel.mayaCredit" = "Pay your bills with Maya Easy Credit";
"maya.paybills.featureBanner.spiel.mayaCredit.soon" = "Pay your bills with Maya Easy Credit soon";
"maya.paybills.bottomsheet.title.mayaCredit" = "Pay your bills on time with Maya Easy Credit";
"maya.paybills.bottomsheet.info.mayaCredit" = "Don’t have Maya Easy Credit yet? Apply now and never miss any of your bills again!";
"maya.paybills.bottomsheet.info.mayaCredit.teaser" = "We’ll send you a notification as soon as this becomes a payment option for you.\n\nDon’t have Maya Easy Credit yet? Apply now and never miss any of your bills again!";
"maya.paybills.bottomsheet.info.mayaCredit.soon" = "Already have Maya Easy Credit? You're all set! We'll let you know once we've enabled paying bills with Maya Easy Credit! \nIf not, tap \"Activate now\" below to sign up for Maya Easy Credit.";
"maya.paybills.bottomsheet.action.mayaCredit.applyNow" = "Apply Now";
"maya.paybills.bottomsheet.close.mayaCredit" = "Close";
"maya.paybills.error.description" = "%@ is currently not available. Please check back again later or pay another bill in the meantime.";
"maya.paybills.reminder.payToday" = "PAY TODAY";
"maya.paybills.reminder.reminderSet" = "REMINDER SET";

/*Rewards and Missions*/
"missions.title.current.mission" = "Current Mission";
"missions.title.mission.duration" = "Mission Duration";
"missions.validity.duration.suffix" = "left";
"missions.default.reward.name" = "PayMaya Credits";
"missions.completed.section.title" = "Completed";
"missions.other.missions.section.title" = "Other Missions";
"missions.details.progress.aggregate.notarget" = "Your progress: %.2f";
"missions.details.progress.aggregate.withtarget" = "%.2f of %.2f";
"missions.details.progress.others.notarget" = "Your progress: %d";
"missions.details.progress.others.withtarget" = "%d of %d";

/* My QR */
"my.qr.screen.title" = "My QR";

/*Request Money*/
"request.money.required.amount" = "Please enter an amount to request.";
"request.money.maximum.amount" = "Maximum amount of PHP 500,000.00";
"request.money.remaining.characters" = "%@ characters remaining";
"request.money.remaining.character" = "%@ character remaining";
"request.money.account.limits.full.message" = "Make sure your account can still accommodate the amount of this request. Refer to your Account Limits for more details.";
"request.money.account.limits.text" = "Account Limits";
"request.money.share.generic" = "You have a money request from %@. Please scan the QR provided or tap the link below to proceed.\nhttps://www.paymaya.com/app/sendmoney?p=%@";

/* Bank Pull Funds */
"pullfunds.amount.maya.spiel" = "From %@ up to %@ per transaction only. Cash in up to %@ times per day.";
"pullfunds.amount.required" = "Amount is required. Please try again.";
"pullfunds.amount.title" = "Amount";
"pullfunds.amount.accountLinking" = "account linking";
"pullfunds.confirmation.continue.spiel" = "By clicking Continue, you will be directed to %@'s online portal to complete your cash in transaction.";
"pullfunds.linked.header.title" = "My linked bank accounts";
"pullfunds.default.header.title" = "Link a bank account to cash in with ease";
"pullfunds.error.maxLimit" = "Maximum limit has been reached";
"pullfunds.unlink.account" = "Unlink Account";
"pullfunds.linked.account.menu.title" = "Account (%@) options";
"pullfunds.confirmation.unlink.account.title" = "Are you sure you want to unlink your account?";
"pullfunds.confirmation.unlink.account.message" = "Account number %@ in %@";
"pullfunds.success.unlink.account.title" = "Account unlinking success";
"pullfunds.success.unlink.account.message" = "You have successfully unlinked your
%@ account from Maya.";
"pullfunds.confirm.transaction.title" = "You’re almost there!";
"pullfunds.confirm.transaction.message" = "We’re now processing this cash in transaction to your bank account.";
"pullfunds.spiel.transaction.failed.title" = "Cash in transaction failed";
"pullfunds.spiel.transaction.failed.message" = "Please contact your bank or try again with a different bank account.";
"pullfunds.redesign.confirmation.unlink.title" = "Unlink your account?";
"pullfunds.redesign.confirmation.unlink.message" = "You are about to unlink this account: \n**** **** %@\n%@";
"pullfunds.redesign.success.unlink.message" = "Account successfully unlinked";
"pullfunds.redesign.failed.unlink.message" = "Account unlinking failed";
"pullfunds.redesign.account.error.maxLimit.message" = "Maximum accounts have been reached";
"pullfunds.redesign.list.payment.options.title" = "Select a bank";

/* Vouchers */
"vouchers.state.available.title" = "Available";
"vouchers.state.claimed.title" = "Claimed";
"vouchers.state.expired.title" = "Expired";
"vouchers.available.spiel" = "Claim until %@";
"vouchers.maintenance.button.title" = "Shop Now";
"vouchers.maintenance.message" = "Buy load, data and other\ncool stuff on our Shop!";
"vouchers.reminder.scheduled.spiel" = "You have successfully set a reminder!";
"vouchers.reminder.scheduled.failed.spiel" = "We had trouble saving your reminder.";
"vouchers.reminder.cancelled.spiel" = "Notifications turned off for this voucher.";
"vouchers.reminder.denied.spiel" = "Turn on your notifications to receive reminders and more!";
"vouchers.receipt.terms.and.conditions.title" = "Terms and Conditions";
"vouchers.receipt.terms.and.conditions.description" = "1. This voucher can only be redeemed during the validity period\n2. This voucher is non-refundable and cannot be exchanged for cash in part or full and is valid for a single transaction only.";
"vouchers.receipt.success.copy.code.spiel" = "Voucher Code Copied!";
"vouchers.reminder.calendar.denied.access.spiel" = "You have declined access to your Calendar. To change this, go to Settings.";
"vouchers.reminder.calendar.settings.spiel" = "Go to Settings";
"vouchers.error.connection.issue.message" = "We ran into a connection issue. Please try again in a bit.";

/* Creator Store */
"creatorStore.eSims.title" = "eSIMs";
"creatorStore.food.title" = "Food";
"creatorStore.funds.title" = "Funds";
"creatorStore.globalRemittance.title" = "Global remittance";
"creatorStore.globalStocks.title" = "Global Stocks";
"creatorStore.government.title" = "Government";
"creatorStore.insurance.title" = "Insurance";
"creatorStore.interstitial.button.title" = "Get started";
"creatorStore.luckyGames.title" = "Lucky Games";
"creatorStore.games.title" = "Games";
"creatorStore.merchantRewards.title" = "Merchant Rewards";
"creatorStore.payLater.title" = "Maya Bank";
"creatorStore.retail.title" = "Retail";
"creatorStore.sparkHackathon.title" = "Spark Hackathon";
"creatorStore.stocks.title" = "Stocks";
"creatorStore.stocksGame.title" = "Stocks game";
"creatorStore.stream.title" = "Stream";
"creatorStore.error.restricted.title" = "This feature is unavailable to you";
"creatorStore.error.restricted.message" = "%@ may not be available to everyone.\nVisit our Help Center to learn how this feature can be enabled for you.";
"creatorStore.error.restricted.helpCenter" = "Help Center";

/* Stocks */
"stocks.interstitial.title" = "Step into the exciting world of stock trading";
"stocks.interstitial.message1" = "Trade stocks with SEC licensed brokers";
"stocks.interstitial.message2" = "Easily manage your stock portfolio";
"stocks.interstitial.message3" = "Use your Maya Wallet to fund your trades with each partner";

/* Global Stocks */
"globalStocks.interstitial.title" = "Unlock the world of global stock trading";
"globalStocks.interstitial.message1" = "Invest in the world’s largest companies like Google, Facebook, Tesla";
"globalStocks.interstitial.message2" = "Trade global stocks seamlessly with Maya's premier trading providers";
"globalStocks.interstitial.message3" = "Use your Maya Wallet to fund your USD wallet with each partner";

/* Global Remittance */
"globalRemittance.interstitial.title" = "Global remittance at your fingertips";
"globalRemittance.interstitial.message1" = "Conveniently claim your money from around the world using Maya";
"globalRemittance.interstitial.message2" = "Select from a range of partners like Western Union and more";
"globalRemittance.interstitial.message3" = "Receive the money in your Maya Wallet";

/* Insurance */
"insurance.interstitial.title" = "Stay covered in times of need";
"insurance.interstitial.message1" = "Choose among leading insurance providers in the Philippines";
"insurance.interstitial.message2" = "Explore insurance products from travel to health – and more";
"insurance.interstitial.message3" = "Use your Maya Wallet to pay insurance premiums from each partner";

/* Stocks Game */
"stocksGame.interstitial.title" = "Learn stock trading securely with Ztock";
"stocksGame.interstitial.message1" = "Discover what it’s like to trade stocks, ETFs, and more";
"stocksGame.interstitial.message2" = "Learn how to trade stocks without using any real money";
"stocksGame.interstitial.message3" = "Simulate trading with world’s best companies from Apple to Zoom";

/* Lucky Games */
"luckyGames.interstitial.title" = "Lucky games await!";
"luckyGames.interstitial.message1" = "Choose a PAGCOR-authorized game of your choice to be redirected to a third-party platform";
"luckyGames.interstitial.message2" = "Available to upgraded Maya users who are at least 21 years old and are not government employees";
"luckyGames.interstitial.message3" = "Maya is responsible for cash in and cash out services only. We are not liable for any losses you may experience in your transactions.";
"luckyGames.interstitial.termsAndConditions" = "By continuing, you fully understand the Terms and Conditions on the use of this service, accept all the risks involved, and acknowledge that profits and returns are not guaranteed.";
"luckyGames.interstitial.termsAndConditions.link" = "Terms and Conditions";
"luckyGames.interstitial.banner.title" = "Before you start";
"luckyGames.interstitial.banner.message1" = "Available to upgraded Maya users who are at least 21 years old and are not government employees";
"luckyGames.interstitial.banner.message2" = "All games are PAGCOR-authorized and will redirect you to a third-party platform";
"luckyGames.interstitial.banner.message3" = "Maya is responsible for cash in and cash out services only. We are not liable for any losses you may experience in your transactions.";
"luckyGames.interstitial.banner.termsAndConditions" = "By continuing, you fully understand the Terms and Conditions on the use of this service, accept all the risks involved, and acknowledge that profits and returns are not guaranteed";

/* Funds */
"funds.interstitial.title" = "Start investing with Maya Funds";
"funds.interstitial.message1" = "Set up your risk profile and get recommendations for funds";
"funds.interstitial.message2" = "Invest in funds for as low as ₱50";
"funds.interstitial.message3" = "Buy orders and receive your earnings through your Maya Wallet";

/* Stream */
"stream.interstitial.title" = "Your one-stop streaming hub";
"stream.interstitial.message1" = "Access a curated collection of streaming providers";
"stream.interstitial.message2" = "Stream movies, TV shows, and more from your favorite platforms";
"stream.interstitial.message3" = "Use your Maya Wallet for hassle-free and secure payments";

/* Spark Hackathon */
"sparkHackathon.interstitial.title" = "Powering the Future of Maya";
"sparkHackathon.interstitial.message1" = "Discover innovative solutions developed by Maya’s talented teams";
"sparkHackathon.interstitial.message2" = "Be the first to try new features and help shape the future of Maya products";
"sparkHackathon.interstitial.message3" = "Network with fellow innovators and learn about the latest trends in tech";

/* Philippine Airlines */
"philippineAirlines.interstitial.title" = "Fly with PAL, right here in Maya";
"philippineAirlines.interstitial.message1" = "Seamlessly link your Mabuhay Miles account in Maya";
"philippineAirlines.interstitial.message2" = "Instantly check your current Mabuhay Miles balance";
"philippineAirlines.interstitial.message3" = "Stay tuned for more features coming soon";

/* Maya Vouchers */
"maya.vouchers.available.spiel" = "Claim until:";
"maya.vouchers.expired.spiel" = "Expired on:";
"maya.vouchers.available.button.title" = "Claim";
"maya.vouchers.expired.button.title" = "Expired";
"maya.vouchers.claimed.button.title" = "Claimed";
"maya.vouchers.expired.cell.spiel" = "Expired on %@";
"maya.vouchers.receipt.state.claimed" = "Voucher successfully claimed";
"maya.vouchers.receipt.code.copied"= "Voucher code copied";
"maya.vouchers.receipt.code.title" = "Voucher code";
"maya.vouchers.receipt.details.description" = "Use this voucher to redeem PayMaya credits and more!";
"maya.vouchers.receipt.code.valid" = "Claim until:";
"maya.vouchers.redeemed.spiel" = "Claimed on %@";
"maya.vouchers.details.code.label" = "Reference ID %@";

/* Maya Shop */
"maya.shop.loadV2.banner.title" = "Load feature is relocated";
"maya.shop.loadV2.banner.subtitle" = "Click here to buy load";
"maya.shop.search.placeholder" = "Search shop";
"maya.shop.products.search.placeholder" = "Search for a product";
"maya.shop.history.shareTreats.label" = "Powered by Share Treats";

/* Account Limits */
"accountLimits.noDailyLimit" = "No daily amount limit";
"accountLimits.noMonthlyLimit" = "No monthly amount limit";
"accountLimits.amountRemaining" = "%@ %@ remaining";
"accountLimits.amountRange" = "%@ %@ used (max: %@ %@)";
"accountLimits.countLimit.singular" = "%@ transaction left (max: %@)";
"accountLimits.countLimit.plural" = "%@ transactions left (max: %@)";
"accountLimits.learnMore" = "Learn more about account limits.";
"accountLimits.learnMore.link" = "account limits";

/* Maya Account Limits */
"maya.accountLimits.segments.daily" = "Daily";
"maya.accountLimits.segments.monthly" = "Monthly";
"maya.accountLimits.amountRemaining" = "%@ remaining";
"maya.accountLimits.amountRange" = "%@ used (max: %@)";
"maya.accountLimits.countLimit.singular" = "%@ transaction left (max: %@)";
"maya.accountLimits.countLimit.plural" = "%@ transactions left (max: %@)";
"maya.accountLimits.learnMore" = "Limits will reset tomorrow. Learn more";
"maya.accountLimits.learnMore.monthly" = "Limits will reset on the first day next month. Learn more";
"maya.accountLimits.learnMore.link" = "Learn more";

/* My Favorites */
"myFavorites.update.success.message" = "Your favorite has been updated";
"myFavorites.update.error.message" = "Sorry, we failed to update your favorite.";
"myFavorites.empty.biller.spiel" = "Pay a bill to add it to your favorites";
"myFavorites.empty.bank.spiel" = "Complete a transaction to add it your favorites";
"myFavorites.empty.sendmoney.spiel" = "Complete a transaction to add it your favorites";
"myFavorites.title.uppercased" = "MY FAVORITES";

/* Add To Favorites */
"addToFavorites.error.title" = "Add To Favorites Error";
"addToFavorites.error.message" = "We could not add to your favorites at the moment. Please try again.";
"addToFavorites.error.tryAgain.spiel" = "Try Again";
"addFavorites.alias.label" = "Label (e.g. %@)";
"addFavorites.alias.enter.label" = "Enter Label (e.g. %@)";
"addFavorites.success.title.spiel" = "Added to Favorites";

/* Inbox */
"inbox.all" = "All";
"inbox.promos" = "Promos";
"inbox.updates" = "Updates";
"inbox.recent" = "Recent";
"inbox.earlier" = "Earlier";
"inbox.paymaya.deals" = "PayMaya Deals";
"inbox.option.markAllAsRead" = "Mark all as read";
"inbox.emptyView.message" = "All of your %@ will be shown here.";
"inbox.emptyView.title" = "You currently have no %@";
"inbox.maintenance.title" = "We’ll be back shortly.";
"inbox.maintenance.message" = "Inbox is currently under maintenance. Please check again later.";
"inbox.resumeChat.button" = "Resume chat";

/* EKYC Reminders */
"ekycReminders.list.ids.title" = "ID List";
"ekycReminders.list.ids.primary" = "Primary";
"ekycReminders.list.ids.secondary" = "Secondary";
"ekycReminders.list.prepareIDs" = "Prepare your IDs. Check the complete list here.";
"ekycReminders.list.prepareIDs.link" = "Check the complete list here.";
"ekycReminders.list.downloadForm" = "Download and have your parents sign the consent form. Download form here.";
"ekycReminders.list.downloadForm.link" = "Download form here.";
"ekycReminders.list.beReady" = "Be ready with any of their valid IDs from this list.";
"ekycReminders.list.beReady.link" = "list";

/* Change Min */
"changeMin.spiel.required.mobileNumber" = "Mobile number is required";
"changeMin.spiel.invalid.mobileNumber" = "Please provide a valid mobile number";
"changeMin.spiel.success.title" = "Got it! Your new PayMaya account number has been updated to %@.";
"changeMin.spiel.success.message" = "We will now redirect you to the login screen. You may use this number to login to your PayMaya account.";
"changeMin.leave.screen.title" = "Are you sure you want to leave?";
"changeMin.leave.screen.message" = "Your progress will be lost";
"changeMin.maya.reminders.tap.here" = "tap here";
"changeMin.maya.reminders.highlightedText" = "both your OLD and NEW";
"changeMin.maya.confirm.password.spiel" = "Please enter your password to confirm that you want to change your registered mobile number.";
"changeMin.maya.spiel.same.mobileNumber" = "Oops, that's your current number! Please enter a different mobile number.";
"changeMin.maya.success.title" = "New Maya number saved!";
"changeMin.maya.success.message" = "You will now be logged out. Please log in again using your new mobile number.";
"changeMin.maya.confirm.back.title" = "Changed your mind?";
"changeMin.maya.confirm.back.message" = "You'll lose your progress if you leave.";
"changeMin.maya.kyc.prompt.title" = "Upgrade your account to change your mobile number.";
"changeMin.maya.kyc.prompt.message" = "Get access to this feature and a bunch more when you upgrade your account. It’s free and takes just a few minutes!";

/* New Success */
"success.status.title.completed" = "COMPLETED";
"success.status.title.processing" = "PROCESSING";
"success.status.title.rejected" = "REJECTED";
"success.shop.couponButton.snackbar.message" = "Coupon code copied!";
"success.add.to.contacts.no.permission.title" = "Access to Contacts Needed";
"success.add.to.contacts.no.permission.message" = "Please allow PayMaya to access Contacts in the IOS settings to access this feature.";

/* CleverTap Notification */
"clevertap.action.back.title" = "Back";
"clevertap.action.next.title" = "Next";
"clevertap.action.learnMore.title" = "Learn More";

/* Session Timeout */
"sessiontimeout.login.biometry" = "Use %@ to Login";
"sessiontimeout.login.hello.user" = "Hello, %@";
"sessiontimeout.login.touchId.spiel" = "Log in with your fingerprint";
"sessiontimeout.login.faceId.spiel" = "Log in with Face ID";
"sessiontimeout.login.passcode.spiel" = "Log in with Device Passcode";

/* Instructional Overlay Spiels */
"instructional.overlay.default.title" = "What’s New!";
"instructional.overlay.default.message" = "Hi! We added new exciting PayMaya services just for you and you can access all these by tapping on the more icon.";
"instructional.overlay.custom.message" = "Hi %@! We added new exciting PayMaya services just for you and you can access all these by tapping on the more icon.";

/* Transport */
"transport.activity.type" = "Transport";
"transport.stops.destination.title" = "Destination";

/* EDD */
"edd.form.back.confirmation.title" = "Are you sure you want to leave?";
"edd.form.back.confirmation.message" = "Your progress will be lost";
"edd.field.required" = "This field is required.";
"edd.form.options.yes" = "Yes";
"edd.form.options.notApplicable" = "N/A (Not Applicable)";
"edd.form.options.alert.title" = "Are you sure you want to change?";
"edd.form.options.alert.message" = "Changing your selection will delete all the information you added below. Do you want to continue?";
"edd.title.primary.account.usage" = "Primary Account Usage";
"edd.title.source.of.income" = "Source of Income";
"edd.title.related.companies" = "Related Companies";
"edd.title.bank.information" = "Bank Information";
"edd.title.financial.documents" = "Financial Documents";
"edd.splash.title" = "Upgrade Application Status";
"edd.splash.header" = "Hello, %@!";
"edd.splash.alert.title" = "Are you sure you want to do this later?";
"edd.splash.alert.message" = "You will need to provide the required additional information before you can access the app again.\n\nYou will be redirected back to the login screen if you exit this step.";
"edd.spiel.selection.others" = "Other/s, please specify";
"edd.incomeSources.others.placeholder" = "e.g. Freelance Works";
"edd.related.companies.others.placeholder" = "e.g. PayMaya Phillippines Inc.";
"edd.banks.others.placeholder" = "Provide Bank Name";
"edd.financialDocs.proTip" = "Pro Tip: Make sure the photo is clear and all important information is captured in the photo.";
"edd.financialDocs.proTip.bold" = "Pro Tip:";
"edd.financialDocs.alert.title" = "Are you sure you want to delete?";
"edd.financialDocs.alert.message" = "This action is irreversible.";
"edd.review.not.applicable.spiel" = "N/A (Not Applicable)";
"edd.financialDocs.change.alert.title"  = "Are you sure you want to change?";
"edd.financialDocs.change.alert.message" = "Changing your selection will delete all the information you added below. Do you want to continue?";
"edd.financialDocs.change.alert.option" = "Change";
"edd.financialDocs.not.applicable.type" = "NOT_APPLICABLE";

/* Prominent Disclosure */
"disclosure.spiel.keyHighlights.title" = "Key Highlights";
"disclosure.spiel.dataPrivacy.title" = "Data Privacy";
"disclosure.spiel.installedApplications.title" = "Installed applications";
"disclosure.spiel.shareInformation.title" = "Who we share your information with";
"disclosure.spiel.deviceAttributes.title" = "Device attributes:";
"disclosure.spiel.deviceIdentifiers.title" = "Device identifiers:";
"disclosure.spiel.deviceSignals.title" = "Device signals:";
"disclosure.spiel.dataFromNetwork.title" = "Data from network connections:";
"disclosure.spiel.dataFromDevice.title" = "Data from device settings:";
"disclosure.spiel.agreeAndContinue.title" =  "“Agree and Continue”";
"document.capture.bottomSpiel" = "Take a photo of the document";

/* Referral Share Code */
"referral.myInviteCode.spiel" = "My Invite Code is %@";
"referral.myInviteCodeLink.spiel" = "My Invite Code is %@\nor click here to create an account now: %@";

/* Maya Easy Credit */
"maya.credit.dashboard.spiel.eligible.title" = "All Set!";
"maya.credit.dashboard.spiel.eligible.subtitle" = "Activate your own credit line now.";
"maya.credit.dashboard.spiel.eligible.cta.title" = "Activate Now";
"maya.credit.dashboard.spiel.ineligible.title" = "Thank you for signing up.";
"maya.credit.dashboard.spiel.ineligible.subtitle" = "Based on our checks, you are not eligible for the service at this time.";
"maya.credit.dashboard.spiel.optin.title" = "Get exclusive early access";
"maya.credit.dashboard.spiel.optin.subtitle" = "Open a low-cost credit line for your PayMaya transactions";
"maya.credit.dashboard.spiel.optin.cta.title" = "Join Now";
"maya.credit.dashboard.spiel.approved.title" = "Thanks for joining the early access.";
"maya.credit.dashboard.spiel.approved.subtitle" = "We're doing final checks and we'll notify you once we're ready.";
"maya.credit.dashboard.spiel.submitted.title" = "Thank you for signing up.";
"maya.credit.dashboard.spiel.submitted.subtitle" = "We're just doing final reviews. Please check back later.";
"maya.credit.dashboard.badge.newUpdate" = "New Update";
"maya.credit.dashboard.badge.exclusiveOffer" = "Exclusive Offer";
"maya.credit.dashboard.badge.nowAvailable" = "Now Available";
"maya.credit.dashboard.spiel.error.title" = "Error";
"maya.credit.dashboard.spiel.error.subtitle" = "We encountered an issue while retrieving your application status.";
"maya.credit.dashboard.spiel.booked.error.title" = "Failed to Load";
"maya.credit.dashboard.spiel.booked.error.subtitle" = "We encountered an issue loading Maya Easy Credit.";
"maya.credit.dashboard.spiel.error.cta.title" = "Tap to Retry";
"maya.credit.application.expires.on" = "Offer ends on %@";
"maya.credit.application.interest.rate" = "%@%% against used limit.";
"maya.credit.application.billing.due.date" = "%@ days after billing\nend date";
"maya.credit.application.billing.end.date" = "%@ of the month";
"maya.credit.application.select.day" = "Select Day";
"maya.credit.application.select.an.end.date" = "Select an end date";
"maya.credit.application.learn.more" = "Learn More";
"maya.credit.application.please.provide.email" = "Please provide and verify your email on the settings menu";
"maya.credit.application.day.unit" = "Day";
"maya.credit.settings.spiel.billing.end.date" = "Every %@ of the month";
"maya.credit.settings.spiel.service.fee" = "%@%% against used limit";
"maya.credit.settings.spiel.no.email" = "No verified email";
"maya.credit.transfer.spiel.exceeds.available.credit" = "Amount entered exceeds credit limit";
"maya.credit.transfer.spiel.exceeds.cashin.credit" = "Amount entered exceeds cash in limit";
"maya.credit.transfer.spiel.exceeds.available.and.cashin.credit" = "Amount entered exceeds cash in and credit limit";
"maya.credit.transfer.spiel.maximum.credit.limit.reached" = "Sorry, you have reached your maximum Maya Easy Credit limit.";
"maya.credit.transfer.spiel.your.mobile.number" = "You (%@)";
"maya.credit.transfer.spiel.slide.to.transfer" = "Slide to Transfer";
"maya.credit.transfer.spiel.confirm.transfer" = "Confirm Transfer";
"maya.credit.transfer.alert.spiel.overdue.title" = "Your account is about to be locked";
"maya.credit.transfer.alert.spiel.locked.title" = "Your account is locked";
"maya.credit.transfer.alert.spiel.locked.active.title" = "Account under review";
"maya.credit.transfer.alert.spiel.overdue.message" = "Your account will be locked in %@ days unless your overdue credit bill of ₱%@ is paid";
"maya.credit.transfer.alert.spiel.overdue.tomorrow.message" = "Your account will be locked tomorrow unless your overdue credit bill of ₱%@ is paid";
"maya.credit.transfer.alert.spiel.locked.message" = "Pay your overdue bill of ₱%@ to avoid further penalties. Once paid, we will review your account for unlocking.";
"maya.credit.transfer.alert.spiel.locked.active.message" = "Your account is currently being reviewed for unlocking. While waiting, you can still use your Maya wallet.";
"maya.credit.transfer.toWallet.title" = "Transfer to wallet";
"maya.credit.transfer.toWallet.accountNumber" = "•••• •••• %@";
"maya.credit.transfer.toWallet.serviceFee" = "Service fee (%@%)";
"maya.credit.transfer.toWallet.transferNow" = "Transfer now";
"maya.credit.transfer.toWallet.success.bottomsheet.title" = "You have transferred \n%@";
"maya.credit.transfer.toWallet.success.bottomsheet.subtitle" = "From Maya Easy Credit to your Wallet";
"maya.credit.transfer.toWallet.success.bottomsheet.button.title" = "Done";
"maya.credit.billing.statement.header" = "Billing Statement for %@";
"maya.credit.billing.statement.period" = "For billing period: %@ - %@";
"maya.credit.error.modal.spiel" = "We ran into an issue while getting your request. Please try again.";
"maya.credit.accountsummary.settings.verified" = "Verified";
"maya.credit.accountsummary.settings.unverified" = "Unverified";
"maya.credit.accountsummary.accountnumber.copied" = "Account number copied";
"maya.credit.accountsummary.update.add" = "Add";
"maya.credit.accountsummary.update.edit" = "Edit";
"maya.credit.accountsummary.placeholder.gender" = "Choose your gender";
"maya.credit.accountsummary.placeholder.maritalStatus" = "Choose your marital status";
"maya.credit.accountsummary.placeholder.firstname" = "Enter first name";
"maya.credit.accountsummary.placeholder.lastname" = "Enter last name";
"maya.credit.accountsummary.placeholder.contactRelationship" = "Select a relationship";
"maya.credit.accountsummary.placeholder.mobileNumber" = "Enter mobile number";
"maya.credit.dashboard.balance.limit" = "%@ limit";
"maya.credit.dashboard.balance.used" = "%@ used";
"maya.credit.discovery.statement.label" = "I have read and agreed to the Maya Data Privacy Policy.";
"maya.credit.discovery.statement.label.enhanced" = "By signing up, I have read and agreed to the Maya Data Privacy Policy.";
"maya.credit.discovery.statement.policy" = "Maya Data Privacy Policy";
"maya.credit.discovery.signup.button.title" = "Sign up now";
"maya.credit.discovery.signup.header" = "Your own low-cost credit line";
"maya.credit.discovery.signup.subheader" = "Cash in, pay your bills, buy load, and more!";
"maya.credit.discovery.signup.credit.features" = "• Up to ₱30,000 credit limit \n• Low service fees based on credit used within billing period \n• No additional documents needed";
"maya.credit.transfer.credit.limit" = "Your available credit limit is %@";
"maya.credit.transfer.wallet.limit" = "Your Wallet cash in limit is %@";
"maya.credit.transfer.fee.sample" = "Fees apply. View sample computation";
"maya.credit.transfer.fee.sample.green" = "View sample computation";
"maya.credit.bill.period.label" = "BILL STATEMENT FOR %@ %@";
"maya.credit.billing.statements.label" = "Billing Statements";
"maya.credit.billing.view.button.title" = "View";
"maya.credit.billing.paynow.button.title" = "Pay now";
"maya.credit.billing.outstanding.balance.label" = "Outstanding balance";
"maya.credit.billing.outstanding.balance.sub.label" = "Includes your total unpaid credit plus fees, documentary stamp tax, and any overdue penalties";
"maya.credit.billing.paynow.sub.label" = "Pay partially or in full anytime 😉";
"maya.credit.billing.latest.bill.label" = "Latest bill";
"maya.credit.billing.latest.bill.description.label" = "This amount is based on your latest billing statement. This does not include credit used (plus fees) after the coverage period.";
"maya.credit.billing.latest.bill.info.label" = "To avoid daily penalty fees, remember to pay in full on or before the due date.";
"maya.credit.billing.latest.bill.due" = "Due %@";
"maya.credit.billing.latest.bill.statement" = "Statement for %@";
"maya.credit.billing.view.statement.button.title" = "View statement";
"maya.credit.billing.paybill.button.title" = "Pay this bill";
"maya.credit.transfer.spiel.maximum.credit.limit.new.reached" = "Maximum credit limit reached";
"maya.credit.management.header.label" = "MANAGE MY CREDIT";
"maya.credit.recent.transactions.header.label" = "RECENT TRANSACTIONS";
"maya.credit.recent.transactions.information.label" = "This amount shows your latest used credit for this period, service fees and documentary tax (DST). ";
"maya.credit.recent.transactions.information.bold" = "You can pay this amount partially or in full anytime.";
"maya.credit.personalDetails.header.add" = "Add your \npersonal details";
"maya.credit.personalDetails.header.edit" = "Update your \npersonal details";
"maya.credit.personalDetails.header.signup" = "Update your \npersonal details";
"maya.credit.contactReference.header.add" = "Assign a contact reference";
"maya.credit.contactReference.header.edit" = "My contact reference";
"maya.credit.contactReference.header.signup" = "Assign a contact reference";
"maya.credit.signup.title" = "Sign up for Maya Easy Credit";
"maya.credit.signup.info" = "By signing up for Maya Easy Credit, you have read and understood our terms and conditions, disclosure statement, truth and transparency in lending, and agree to share your data with Maya Bank, Inc.";
"maya.credit.signup.termsandconditions" = "terms and conditions";
"maya.credit.signup.disclosurestatement" = "disclosure statement";
"maya.credit.signup.truthandtransparency" = "truth and transparency in lending";
"maya.credit.signup.acknowledge.button.title" = "Got it";
"maya.credit.signup.learnmore.title" = "Learn more";
"maya.credit.signup.lowservicefees.title" = "🙏  Low service fees";
"maya.credit.signup.lowservicefees.content" = "Your service fee is a variable amount added to your total Maya Easy Credit dues. This fee is computed based on the age of your Maya account, your transaction history, and other usage factors.";
"maya.credit.signup.lowservicefees.computation.link" = "View sample computation";
"maya.credit.signup.automatedpayments.title" = "💳  Automated payments";
"maya.credit.signup.automatedpayments.content" = "We automatically deduct your credit bill from your wallet balance upon reaching the due date. You may incur penalties if you don’t pay your bill on time.";
"maya.credit.signup.billing.end.date" = "Every %@ of the month";
"maya.credit.signup.contact.reference.title" = "Set up your credit";
"maya.credit.signup.contact.reference.subtitle" = "To enjoy Maya Easy Credit, please provide the following information";
"maya.credit.signup.contact.reference.section.billing.title" = "Billing details";
"maya.credit.signup.contact.reference.section.billing.limit" = "Credit limit";
"maya.credit.signup.contact.reference.section.billing.enddate" = "Billing end date";
"maya.credit.signup.contact.reference.section.billing.enddate.info" = "Billing end date is the cutoff for transactions included in your upcoming billing statement.";
"maya.credit.signup.contact.reference.section.billing.emailaddress" = "Billing email address";
"maya.credit.signup.contact.reference.section.billing.emailaddress.info" = "Your billing statement will be sent to this email";
"maya.credit.signup.contact.reference.section.billing.altmobile" = "Alternative mobile number (optional)";
"maya.credit.signup.contact.reference.section.billing.altmobile.label" = "Alternative mobile number";
"maya.credit.signup.contact.reference.section.billing.altmobile.info" = "Mobile and landline numbers are accepted Switch to landline number";
"maya.credit.signup.contact.reference.section.personal.title" = "Personal details";
"maya.credit.signup.contact.reference.automatedpayments.content" = "On your due date, the remaining balance from your total amount due is auto-deducted from your Maya Wallet to help you avoid getting late penalty fees.";
"maya.credit.signup.maidenName.FirstName.label" = "Mother’s maiden first name";
"maya.credit.signup.maidenName.MiddleName.label" = "Mother’s maiden middle name";
"maya.credit.signup.maidenName.LastName.label" = "Mother’s maiden last name";
"maya.credit.signup.allSet.title" = "Your offer is ready! ⚡";
"maya.credit.signup.allSet.description" = "Proceed with the offer below to start using Maya Easy Credit for payments and purchases";
"maya.credit.signup.consent.privacyNotice.title" = "Privacy notice";
"maya.credit.assign.contact.reference.title" = "Assign a contact reference";
"maya.credit.assign.contact.reference.subtitle" = "Add a person we can contact in case of urgent concerns about your Maya Easy Credit.";
"maya.credit.assign.contact.reference.contactRelationship" = "Contact relationship";
"maya.credit.signup.emailVerification.invalid.email" = "Please enter a valid email address";
"maya.credit.signup.mobileVerification.invalid.email" = "The mobile number you entered appears to be invalid. Please try again.";
"maya.credit.signup.mobileVerification.invalid.current" = "Alternative number can't be your current number";
"maya.credit.signup.nameVerification.invalid.characters" = "Please use only letters and hyphens";
"maya.credit.signup.nameVerification.invalid.characters.repeated" = "Repeated characters detected. Please check if you entered the correct name. ";
"maya.credit.signup.nameVerification.invalid.length.minimum" = "Please enter a name that is at least 2 characters long";
"maya.credit.signup.nameVerification.invalid.length.maximum" = "Please enter a name that is 50 characters or less";
"maya.credit.signup.partnerMerchant.success" = "Enjoy a %@ credit limit to cash in, pay your bills, buy load, and more! ";
"maya.credit.signup.interstitial.error.title." = "That didn’t load right";
"maya.credit.signup.interstitial.consent.error.message" = "Our privacy notice failed to load. Please try again to proceed with your application.";
"maya.credit.signup.interstitial.initialEligibility.error.message" = "We couldn’t check your eligibility. Please try again to proceed with your application.";
"maya.credit.confirmDetails.title" = "Confirm details";
"maya.credit.confirmDetails.section.billing" = "Billing details";
"maya.credit.confirmDetails.section.billing.info" = "Due date is 15 days after your billing end date";
"maya.credit.confirmDetails.section.personal" = "Personal details";
"maya.credit.confirmDetails.section.contact" = "Assigned contact reference";
"maya.credit.contacts.no.permission.title" = "Allow Contacts";
"maya.credit.contacts.no.permission.message" = "For more security and convenience in referring a mobile number, please open your settings and allow Maya access to your contacts.";
"maya.credit.contact.reference.required.message" = "To continue using Maya Easy Credit, \nassign a contact reference first.";
"maya.credit.contact.reference.required.link" = "assign a contact reference first.";
"maya.credit.disclosureStatement.label" = "Maya Bank, Inc. is regulated by the Bangko Sentral ng Pilipinas – bsp.gov.ph.\nChat with us for any concerns or send us an <NAME_EMAIL>.";
"maya.credit.helpCenter.label" = "Maya Bank, Inc. is regulated by the Bangko Sentral ng Pilipinas – bsp.gov.ph.\nVisit our Help Center or check our service advisories for any concerns.";
"maya.credit.chatWithUs.link" = "Chat with us";
"maya.credit.allset.billing.end.date" = "Every %@ of the month";
"maya.credit.allset.servicefee" = "%@%% + Documentary Stamp Tax";
"maya.credit.writtenOff.info" = "If you have any inquiries, you can visit our Help Center to learn more.";
"maya.credit.mfa.riskChallenge.error.title" = "Transfer unsuccessful";
"maya.credit.mfa.riskChallenge.error.message" = "We couldn’t complete your transfer. For more information, please visit our Help Center.";
"maya.credit.location.permission.needed.title" = "Location permission needed";
"maya.credit.location.permission.needed.message" = "In order to asses your application, we need permissions to access your location";
"maya.credit.location.try.again.title" = "Let's try that again";
"maya.credit.location.try.again.message" = "We couldn't get your location.\nPlease try again.";
"maya.credit.location.open.settings.title" = "“Maya” would like to access your contacts, calendars, photos, and installed apps";
"maya.credit.location.open.settings.message" = "Maya requires access to properly assess your credit score.";

/* Loans */
"loans.icl.dbl.upload.header" = "Add proof of income";
"loans.icl.dbl.upload.subheader" = "Uploading your proof of income helps us give you better lending offers";

/* Location Permission */
"location.permission.bottomsheet.title" = "Protect your account by sharing your location";
"location.permission.bottomsheet.message" = "Maya only uses your info to spot and prevent transactions that originate from unverified or potentially suspicious locations.";
"location.permission.bottomsheet.continue" = "Continue";

/* Maya Services */
"services.category.title.essentials" = "Essentials";
"services.category.title.moneyMakers" = "Money Makers";
"services.category.title.lifestyle" = "Lifestyle";
"services.category.title.payments" = "Payments";
"services.category.title.rewards" = "Rewards";

"services.actions.title.messages" = "Chat with Maya";
"services.actions.title.security" = "Security center";
"services.actions.title.help" = "Help Center";
"services.actions.title.profile" = "My profile";
"services.actions.title.settings" = "Settings";
"services.actions.title.about" = "About Maya";

"services.underage.title" = "21 and up only";
"services.underage.message" = "You do not currently meet the age required for this feature. For now, feel free to explore the rest of the app!";
"services.dynamicStatus.new.title" = "NEW";

/* Receive money */
"receiveMoney.form.specifyAmount" = "Specify amount";
"receiveMoney.form.includeMessage" = "Include message";
"receiveMoney.form.title" = "Generate custom QR code";
"receiveMoney.form.amount" = "Amount";
"receiveMoney.form.message" = "Message";
"receiveMoney.form.button" = "Generate";
"receiveMoney.form.message.placeholder" = "Enter message";
"receiveMoney.form.amount.placeholder" = "Enter amount";
"receiveMoney.maximum.amount" = "Maximum amount of ₱500,000.00";
"receiveMoney.title.custom" = "Custom QR code";

/* Maya Pay with QR */
"paywithqr.confirm.success.bottomsheet.title" = "You’ve successfully paid \n %@";
"paywithqr.confirm.success.bottomsheet.subtitle" = "From your wallet to %@";
"paywithqr.confirm.success.bottomsheet.viewreceipt.button.title" = "View receipt";

/* New Maya Easy Credit Activations */
"maya.new.credit.activation.expires.on" = "Offer ends %@";
"maya.new.credit.activation.interest.rate" = "%@%% against used limit + DST";
"maya.new.credit.activation.verified.email" = "Your billing statement will be sent to this email.";
"maya.new.credit.activation.notVerified.email" = "Verify your email so you can receive a copy of your billing statement to this email.";
"maya.new.credit.activation.no.email" = "Provide and verify your email so you can receive a copy of your billing statement.";
"maya.new.credit.activation.changeDate.title" = "Change date";
"maya.new.credit.activation.acknowledge.button.title" = "I agree and acknowledge";
"maya.new.credit.activation.identityStatus.verified" = "Verified";
"maya.new.credit.activation.identityStatus.unverified" = "Unverified";
"maya.new.credit.activation.identityStatus.noEmail" = "No email";
"maya.credit.confirm.change.billing.end.date" = "Every %@ of the month";

/* Maya Easy Credit Interest Details */
"maya.credit.interest.details.percent.limit.label" = "%@%% of used limit";

/* Maya Missions */
"maya.missions.list.no.inprogress.title" = "No missions in progress";
"maya.missions.list.no.completed.title" = "No completed missions";
"maya.missions.list.no.inprogress.info" = "All of your missions in-progress will be shown in this page.";
"maya.missions.list.no.completed.info" = "All of your completed missions will be shown in this page.";
"maya.missions.inprogress.section.title" = "In Progress";
"maya.missions.tasks.completed.spiel" = "%d of %d tasks completed";

/* Maya Components */
"maya.components.action.favorites" = "Save details to favorites";
"maya.components.action.share" = "Share or save this receipt";
"maya.components.action.report" = "Report an issue";

/* Maya Receipt */
"maya.receipt.title.banktransfer" = "Bank transfer to";
"maya.receipt.status.processing" = "Processing";
"maya.receipt.detail.source" = "Source";
"maya.receipt.detail.code" = "Code";
"maya.receipt.detail.payment.method" = "Payment method";
"maya.receipt.detail.powered.by.shareTreats" = "Powered by Share Treats";
"maya.receipt.detail.shareTreats" = "Share Treats";
"maya.receipt.detail.source.mayaCredit" = "Maya Easy Credit";
"maya.receipt.detail.source.payIn4Installments" = "Pay in 4 Installments";
"maya.receipt.detail.source.wallet" = "My Wallet";
"maya.receipt.detail.destination" = "Destination";
"maya.receipt.detail.purpose" = "Purpose";
"maya.receipt.detail.transaction.details" = "Transaction details";
"maya.receipt.detail.transaction.fee" = "Transaction fee";
"maya.receipt.detail.transaction.gateway" = "Gateway";
"maya.receipt.detail.transaction.paymentId" = "Payment ID";
"maya.receipt.detail.transaction.qrphInvoiceNumber" = "QR Ph Invoice No.";
"maya.receipt.detail.transaction.traceNumber" = "Trace number";
"maya.receipt.detail.transaction.referenceID" = "Reference ID";
"maya.receipt.detail.transaction.title" = "Purchased";

/* Activities */
"activities.error.spiel.fetchNextPage" = "Failed to load more transactions.\nSwipe up to try again.";

/* Maya Activity Detail */
"maya.activity.detail.travel.info" = "This ticket is valid for 24 hours since purchase.";

/* Maya Data Personalization */
"maya.privacy.consentChange.title" = "Save changes?";
"maya.privacy.consentChange.message" = "Do you want to save the changes you’ve made to your settings?";

/* Maya referral */
"maya.referral.shareCode.info" = "Get a ₱100.00 voucher each time a friend uses your invite code to join Maya, upgrades their account, and completes their first transaction.\n\nLearn more at maya.ph/refer";
"maya.referral.shareCode.copied" = "Invite code copied";
"maya.referral.insertCode.textField.helper" = "Invite code";
"maya.referral.insertCode.textField.placeholder" = "Enter invite code";
"maya.referral.insertCode.error.message" = "Whoops! Looks like your invite code is invalid. Please try again.";
"maya.referral.insertCode.success.title" = "Code looks good!";
"maya.referral.insertCode.success.description" = "Get your free reward by upgrading your account and completing the required tasks. Check the Missions page for more details.";
"maya.referral.insertCode.success.action.home" = "Go to home";
"maya.referral.insertCode.success.action.upgrade" = "Upgrade now";
"maya.referral.insertCode.submit" = "Submit";

/* Maya settings */
"maya.settings.section.display" = "DISPLAY";
"maya.settings.section.account" = "ACCOUNT";
"maya.settings.section.privacy" = "PRIVACY & SECURITY";
"maya.settings.section.about" = "ABOUT MAYA";

"maya.settings.menu.title.showName" = "Show my name";
"maya.settings.menu.title.manageNotifications" = "Manage notifications";
"maya.settings.menu.title.recovery" = "Account recovery";
"maya.settings.menu.title.changeNumber" = "Change mobile number";
"maya.settings.menu.title.data" = "Data personalization";
"maya.settings.menu.title.closeAccount" = "Close account";
"maya.settings.menu.title.closeAccount" = "Close account";
"maya.settings.menu.title.biometryLogin" = "%@ login";
"maya.settings.menu.title.changePassword" = "Change password";
"maya.settings.menu.title.privacy" = "Privacy policy";
"maya.settings.menu.title.terms" = "Terms & conditions";
"maya.settings.menu.title.licenses" = "Licenses";

"maya.settings.changePassword.title.currentPassword" = "Current password";
"maya.settings.changePassword.placeholder.currentPassword" = "Enter current password";
"maya.settings.changePassword.title.newPassword" = "New password";
"maya.settings.changePassword.placeholder.newPassword" = "Enter new password";
"maya.settings.changePassword.title.confirmNewPassword" = "Confirm new password";
"maya.settings.changePassword.placeholder.confirmNewPassword" = "Enter new password";
"maya.settings.changePassword.validation.newPasswordSameAsCurrent" = "Submitted password is the same as the current";
"maya.settings.changePassword.validation.retypedPasswordNotMatch" = "Does not match your new password";

/* Maya Travel */
"maya.travel.ticket.stopSelection.origin.picker.title" = "Select origin";
"maya.travel.ticket.stopSelection.destination.picker.title" = "Select destination";
"maya.travel.ticket.confirmation.title" = "Confirm payment";
"maya.travel.ticket.confirmation.continue.text" = "Pay now";
"maya.travel.ticket.confirmation.detail.myWallet" = "My Wallet";
"maya.travel.ticket.confirmation.detail.paymentMethod" = "Payment method";
"maya.travel.ticket.confirmation.detail.availableBalance" = "Available balance";
"maya.travel.ticket.confirmation.detail.operator" = "Operator";
"maya.travel.ticket.confirmation.detail.route" = "Route";
"maya.travel.ticket.confirmation.detail.origin" = "Origin";
"maya.travel.ticket.confirmation.detail.destination" = "Destination";
"maya.travel.ticket.confirmation.detail.fare" = "Fare";
"maya.travel.ticket.confirmation.detail.convenienceFee" = "Convenience fee";
"maya.travel.ticket.confirmation.info" = "QR ticket is valid for 24hrs from date of purchase.";

/* Push Approval */
"pushapproval.loading.message" = "Retrieving list";
"pushapproval.cell.date.label" = "Sent on %@";
"pushapproval.reviewRequest.timer.informationText" = "Please complete within %@ seconds.";
"pushapproval.reviewRequest.timer.informationText.highlightedPhrase" = "%@ seconds";
"pushapproval.reviewRequest.timer.informationText" = "Please complete within %@ seconds.";
"pushapproval.reviewRequest.timer.informationText.highlightedPhrase" = "%@ seconds";
"pushapproval.reviewRequest.alert.requestAuthorized.title" = "Request authorized";
"pushapproval.reviewRequest.alert.requestAuthorized.message" = "Please return to your other device to complete the transaction.";
"pushapproval.reviewRequest.alert.requestExpired.title" = "Request expired";
"pushapproval.reviewRequest.alert.requestExpired.message" = "Please make a new request by choosing Maya on your other device.";
"pushapproval.reviewRequest.alert.doneAction.title" = "Done";
"pushapproval.reviewRequest.alert.requestAuthorized.image.accessibilityIdentifier" = "pmios_requestAuthorizedAlert_image_view";
"pushapproval.reviewRequest.alert.requestAuthorized.title.accessibilityIdentifier" = "pmios_requestAuthorizedAlert_title_label";
"pushapproval.reviewRequest.alert.requestAuthorized.message.accessibilityIdentifier" = "pmios_requestAuthorizedAlert_message_label";
"pushapproval.reviewRequest.alert.requestExpired.image.accessibilityIdentifier" = "pmios_requestExpiredAlert_image_view";
"pushapproval.reviewRequest.alert.requestExpired.title.accessibilityIdentifier" = "pmios_requestExpiredAlert_title_label";
"pushapproval.reviewRequest.alert.requestExpired.message.accessibilityIdentifier" = "pmios_requestExpiredAlert_message_label";

/* BSP Footer */
"bsp.website.text" = "bsp.gov.ph";
"bsp.helpCenter.text" = "Help Center";

/* Update Profile */
"maya.updateProfile.personalDetails.title" = "Tell us about yourself";
"maya.updateProfile.personalDetails.subtitle" = "These details will help us verify your identity during the application process";
"maya.updateProfile.emailAdress.title" = "Email address";
"maya.updateProfile.emailAdress.placeholder" = "Enter your email address";
"maya.updateProfile.tin.title" = "Tax identification number (Optional)";
"maya.updateProfile.tin.placeholder" = "***********-000";
"maya.updateProfile.contactNumber.title" = "Alternative contact number";
"maya.updateProfile.contactNumber.placeholder" = "63********** or 81234567";
"maya.updateProfile.gender.title" = "Gender";
"maya.updateProfile.gender.placeholder" = "Select gender";
"maya.updateProfile.educational.attainment.title" = "Educational attainment";
"maya.updateProfile.educational.attainment.placeholder" = "Select educational attainment";
"maya.updateProfile.civilStatus.title" = "Civil status";
"maya.updateProfile.civilStatus.placeholder" = "Select civil status";
"maya.updateProfile.motherMaidenFirstName.title" = "Mother's maiden first name";
"maya.updateProfile.motherMaidenFirstName.placeholder" = "Enter mother's first name";
"maya.updateProfile.motherMaidenMiddleName.title" = "Mother's maiden middle name";
"maya.updateProfile.motherMaidenMiddleName.placeholder" = "Enter mother's middle name";
"maya.updateProfile.motherMaidenLastName.title" = "Mother's maiden last name";
"maya.updateProfile.motherMaidenLastName.placeholder" = "Enter mother's last name";
"maya.updateProfile.noLegalMiddleName.title" = "No legal middle name";
"maya.updateProfile.workDetails.title" = "Add your work details";
"maya.updateProfile.workDetails.subtitle" = "These details let us assess your eligibility for credit cards";
"maya.updateProfile.natureOfWork.title" = "Nature of work";
"maya.updateProfile.natureOfWork.placeholder" = "Select a nature of work";
"maya.updateProfile.jobTitle.title" = "Job title";
"maya.updateProfile.jobTitle.placeholder" = "Select a job title";
"maya.updateProfile.takeHomePay.title" = "Monthly take-home pay";
"maya.updateProfile.takeHomePay.placeholder" = "₱";
"maya.updateProfile.sourceOfIncome.title" = "Source of income";
"maya.updateProfile.sourceOfIncome.placeholder" = "Select a source of income";
"maya.updateProfile.nameOfCompany.title" = "Name of company/business";
"maya.updateProfile.nameOfCompany.placeholder" = "Enter name of company or business";
"maya.updateProfile.contactReference.title" = "Add a contact reference";
"maya.updateProfile.contactReference.subtitle" = "We will contact this person in case of urgent concerns about your account. Learn more";
"maya.updateProfile.contactReference.subtitle.highlighted" = "Learn more";
"maya.updateProfile.contactReference.firstName.title" = "Contact reference’s first name";
"maya.updateProfile.contactReference.firstName.placeholder" = "Enter their first name";
"maya.updateProfile.contactReference.middleName.title" = "Contact reference’s middle name";
"maya.updateProfile.contactReference.middleName.placeholder" = "Enter their middle name";
"maya.updateProfile.contactReference.noLegalMiddleName.title" = "No legal middle name";
"maya.updateProfile.contactReference.lastName.title" = "Contact reference’s last name";
"maya.updateProfile.contactReference.lastName.placeholder" = "Enter their last name";
"maya.updateProfile.contactReference.relationship.title" = "Relationship to you";
"maya.updateProfile.contactReference.relationship.placeholder" = "Select a relationship";
"maya.updateProfile.contactReference.mobileNumber.title" = "Contact reference’s mobile number";
"maya.updateProfile.contactReference.mobileNumber.placeholder" = "**********";
"maya.updateProfile.contactReference.save.title" = "Save";
"maya.updateProfile.contactReference.return.title" = "Return";
"maya.updateProfile.validation.field.this" = "This";
"maya.updateProfile.validation.field.isRequired" = "%@ is required";
"maya.updateProfile.validation.field.motherName.different.person" = "You must name a different person for Mother’s Maiden name";
"maya.updateProfile.validation.field.repeated.characters" = "Cannot have character repeated 3 or more times consecutively";
"maya.updateProfile.validation.field.contactReference.mobileNumber.isRequired" = "A valid 12 digit mobile number of your contact reference is required";
"maya.updateProfile.validation.field.contactNumber.cannot.same" = "Alternative contact number cannot be the same as your mobile number with Maya";
"maya.updateProfile.validation.field.contactReference.contactNumber.cannot.same" = "Contact reference’s mobile number cannot be the same as your mobile number with Maya";
"maya.updateProfile.validation.field.contactReference.different.person" = "You must name a different person as your contact reference";
"maya.updateProfile.validation.field.contactReference.same.altNumber" = "Contact reference mobile number cannot be the same as existing alternate contact number : %@";
"maya.updateProfile.validation.field.altNumber.same.contactReference" = "Alternate contact number cannot be the same as existing contact reference mobile number : %@";

/* MFA Framework */
"maya.mfa.common.thatsNotIt.title" = "That's not it";
"maya.mfa.retry.alertBox.title" = "Let's try another take";
"maya.mfa.retry.primaryButton.title" = "Take video selfie";
"maya.mfa.retry.secondaryButton.title" = "Close";
"maya.mfa.max.attempt.error.title" = "Max attempts reached";
"maya.mfa.fallback.alertBox.title" = "You're almost there!";
"maya.mfa.fallback.alertBox.message" = "Complete your verification with an OTP.";
"maya.mfa.fallback.noFaceData.message" = "We had a problem authenticating you.";
"maya.mfa.fallback.primaryButton.title" = "Verify via OTP";
"maya.mfa.fallback.button.send.me.the.otp.title" = "Send me the OTP";
"maya.mfa.fallback.button.take.me.back.title" = "Take me back";
"maya.mfa.mismatch.alertBox.message" = "We were unable to verify your identity. Please retry again or contact customer support for assistance.";
"maya.mfa.mismatch.primaryButton.title" = "Submit a support ticket";

/* MFA Otp Screen */
"maya.mfa.otp.title" = "Enter the OTP";
"maya.mfa.otp.subtitle.sms" = "Please enter the one-time PIN (OTP) sent to %@";
"maya.mfa.otp.subtitle.email" = "Please enter the one-time PIN (OTP) sent to your registered email %@";
"maya.mfa.otp.placeholder" = "000000";
"maya.mfa.otp.spiel.second" = "second";
"maya.mfa.otp.spiel.seconds" = "seconds";
"maya.mfa.otp.spiel.resendOTP" = "Resend OTP";
"maya.mfa.otp.spiel.resendOTPIn" = "Resend OTP in %ld %@";
"maya.mfa.otp.inlineValidation.invalid" = "This OTP is invalid. Please try again.";
"maya.mfa.otp.inlineValidation.limit" = "OTP limit reached. Please try again later.";
"maya.mfa.otp.maxAttempts.message" = "You have exceeded the attempts for authentication. Please retry.";

/* Auto Provisioning + Data Privacy */
"maya.auto.provisioning.title" = "Let’s get started";
"maya.auto.provisioning.subtitle" = "We’ll open the following accounts so you can start using them straight away";
"maya.auto.provisioning.disclaimer" = "By continuing, you consent to the following and certify that your information is true and complete ";
"maya.auto.provisioning.item.wallet.title" = "Wallet";
"maya.auto.provisioning.item.wallet.subtitle" = "Opened by default";
"maya.auto.provisioning.item.wallet.terms.spiel" = " • Maya Philippines terms and conditions";
"maya.auto.provisioning.item.wallet.privacy.spiel" = " • Maya Philippines privacy policy";
"maya.auto.provisioning.item.savings.title" = "Savings";
"maya.auto.provisioning.item.savings.subtitle" = "Earn up to 14%% as you use Maya";
"maya.auto.provisioning.item.savings.terms.spiel" = " • Maya Bank terms and conditions";
"maya.auto.provisioning.item.savings.privacy.spiel" = " • Maya Bank privacy policy";

/* Device Management */
"maya.deviceManagement.main.activeDevice.subtitle" = "This device";
"maya.deviceManagement.main.error.failedAPI.message" = "We ran into a connection issue. Please try again in a bit.";
"maya.deviceManagement.main.error.noDeviceLoaded.message" = "We couldn’t load your login history at the moment. Please login again to retry.";
"maya.deviceManagement.main.section.active" = "Active";
"maya.deviceManagement.main.section.loggedOut" = "Logged out";
"maya.deviceManagement.main.loginHistoryHeader.subtitle" = "See what devices have been used to log in to your Maya account in the last %@ days.";
"maya.deviceManagement.main.loginHistoryHeader.updateEmail" = "You'll get an email whenever your account is logged in from a new device.";

/* MGM 2.0 */
"maya.mgmV2.button.updateNow" = "Update now";
"maya.mgmV2.title.updateRequired" = "Update required";
"maya.mgmV2.title.inviteAFriend" = "Invite a friend";
"maya.mgmV2.spiel.forceUpdate" = "Update your app to enjoy the newest features and fixes.";

/* Maya Help Center */
"maya.helpCenter.reachHelpCenter.spiel" = "Reach us anytime in our Help Center";
