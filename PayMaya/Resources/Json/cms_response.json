{"data": {"intro": {"new_registration_upgrade_intro_header": "You're in!\nWelcome to Maya.", "new_registration_upgrade_intro_description": "You now own a <b>basic account.</b>", "kyc0_upgrade_intro_header": "Upgrade your Maya account", "kyc0_upgrade_intro_description": "Get access to everything Maya has to offer with an Upgraded account", "kyc1_upgrade_intro_header": "Update your Maya account", "kyc1_upgrade_intro_description": "Keeping your info up to date helps us protect your profile better", "invite_code_description": "🔑  Got an invite code? <a href=\"EnterCode\">Enter code</a>", "kyc0_upgrade_now_header": "Upgrade now for FREE", "kyc1_upgrade_now_header": "Update now for FREE", "upgrade_now_description": "It just takes 3 easy steps and all you need is just one valid ID", "upgrade_steps_guide": {"images": [{"id": 1, "instruction": "Submit an ID<br><a href=\"SeeIDs\">See accepted IDs</a>", "url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Phil_ID_Card_Front.png"}, {"id": 2, "instruction": "Record a video selfie", "url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Phil_ID_Card_Back.png"}, {"id": 3, "instruction": "Enter your information", "url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Phil_ID_Card_Back.png"}]}, "kyc0_unlock_everything_header": "Unlock everything on Maya", "kyc1_unlock_everything_header": "Continue using <PERSON>’s features", "unlock_everything_details": {"features": [{"id": 1, "instruction": "🐷  Open a Savings Account"}, {"id": 2, "instruction": "💳  Apply for a credit card"}, {"id": 3, "instruction": "⚡️  Loans up to ₱250k"}, {"id": 4, "instruction": "🤑  Increased wallet limits"}, {"id": 5, "instruction": "💸  Send money instantly"}, {"id": 6, "instruction": "🚀  Buy Crypto"}, {"id": 7, "instruction": "☂️  Get insurance coverage"}, {"id": 8, "instruction": "📈  Invest in stocks"}, {"id": 9, "instruction": "🌱  Buy Funds"}], "feature_icon_url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Phil_ID_Card_Back.png"}, "safe_transaction_header": "Make incoming transactions safer with an Upgraded account", "safe_transaction_description": "Once you have upgraded your account, others will be able to see your name by default everytime they transact with you.<br><a href=\"LearnMore\">Learn more</a>", "upgrade_btn_title_kyc0": "Upgrade now for free", "upgrade_btn_title_kyc1": "Update now", "do_it_later_btn_title": "Do it later"}, "review_screen": {"header": "Did we get it right?", "sub_header": "sasdadasd", "privacy_header": "Upgrading your Maya account...", "privacy_sub_header": "To keep things... /n <a href=\"LearnMore\">Learn More (Website)</a>.", "privacy_notice": "You can have only one..", "submit_btn_title": "Submit"}, "id": 1, "image_quality_max_retries": 4, "recommended_documents": [{"id": 3, "key": "PH_NATIONAL_ID", "description": "Philippine National ID", "subDescription": "Plastic card, Paper ID, App, Digital ID", "additionalDescription": null, "category": "ID", "expiryDate": false}, {"id": 1, "key": "DRIVERS_LICENSE", "description": "Philippine Driver’s License", "subDescription": null, "additionalDescription": null, "category": "ID", "expiryDate": true}, {"id": 2, "key": "UMID", "description": "UMID", "subDescription": null, "additionalDescription": null, "category": "ID", "expiryDate": false}, {"id": 4, "key": "PASSPORT", "description": "Philippine Passport", "subDescription": null, "additionalDescription": null, "category": "ID", "expiryDate": true}, {"id": 5, "key": "POSTAL_ID", "description": "Postal ID", "subDescription": null, "additionalDescription": null, "category": "ID", "expiryDate": true}, {"id": 6, "key": "PRC_ID", "description": "PRC ID", "subDescription": null, "additionalDescription": null, "category": "ID", "expiryDate": true}, {"id": 7, "key": "SSS_ID", "description": "SSS ID (digitized, with photo)", "subDescription": null, "additionalDescription": null, "category": "ID", "expiryDate": false}, {"id": 8, "key": "FOREIGN_PASSPORT", "description": "Foreign Passport", "subDescription": null, "additionalDescription": "FOR FOREIGNERS", "category": "ID", "expiryDate": true}], "other_primary_documents": [{"id": 13, "key": "ALIEN_CERTIFICATION", "description": "Alien Certificate of Registration (ACR) i-Card", "subDescription": null, "additionalDescription": "FOR FOREIGNERS", "category": "ID", "expiryDate": true}, {"id": 14, "key": "IBP_ID", "description": "Integrated Bar of the Philippines ID", "subDescription": null, "additionalDescription": null, "category": "ID", "expiryDate": true}, {"id": 16, "key": "SCHOOL_ID", "description": "School ID", "subDescription": null, "additionalDescription": "FOR 17 YEARS OLD AND BELOW", "category": "ID", "expiryDate": true}], "secondary_documents": [{"id": 18, "key": "PHILHEALTH_ID", "description": "PhilHealth ID (Digitized/PVC or Laminated Cardboard with Barcode)", "subDescription": null, "additionalDescription": null, "category": "ID", "expiryDate": false}, {"id": 20, "key": "TIN_ID", "description": "TIN ID (Laminated)", "subDescription": null, "additionalDescription": null, "category": "ID", "expiryDate": false}, {"id": 21, "key": "SENIOR_CITIZEN_CARD", "description": "Senior Citizen Card", "subDescription": null, "additionalDescription": null, "category": "ID", "expiryDate": true}], "capture_guides": [{"id": 1, "header": "Using your Philippine driver's license", "sub_header": "Follow these tips to get a clear ID photo", "is_allowed_to_upload": false, "document": {"id": 1, "key": "DRIVERS_LICENSE"}, "capture_methods": [{"id": 1, "method": "SIMPLE_ID_TENCENT_LIVENESS"}], "pages": [{"id": 1, "page": "FRONT"}, {"id": 2, "page": "BACK"}], "samples": [{"id": 1, "variant": "", "description": "✅ Take a photo of the front and back of your driver's license, with clear and readable details<br/><br/>✅ Use the official plastic card. Paper driver's license is not accepted. Check with your local LTO office for the availability of the card.", "additionalDescription": null, "images": [{"id": 1, "instruction": null, "url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Drivers_License_Front.png"}, {"id": 2, "instruction": null, "url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Drivers_License_Back.png"}]}, {"id": 2, "variant": "", "description": "❌ Make sure there is no glare or hologram on your driver's license. Do not cover any part of your ID.", "additionalDescription": null, "images": [{"id": 3, "instruction": null, "url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Drivers_License_Glare.png"}]}], "id_guides": [{"id": 1, "front": "DRIVERS_LICENSE", "back": null}]}, {"id": 2, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 2, "key": "UMID"}, "capture_methods": [{"id": 2, "method": "SIMPLE_ID_TENCENT_LIVENESS"}], "pages": [{"id": 3, "page": "FRONT"}, {"id": 4, "page": "BACK"}], "samples": [], "id_guides": [{"id": 2, "front": "UMID", "back": null}]}, {"id": 3, "header": "Using your Philippine National ID", "sub_header": "Follow the capture guide below based on your ID type", "is_allowed_to_upload": true, "document": {"id": 3, "key": "PH_NATIONAL_ID"}, "capture_methods": [{"id": 3, "method": "SIMPLE_ID_TENCENT_LIVENESS"}], "pages": [{"id": 5, "page": "FRONT"}, {"id": 6, "page": "BACK"}], "samples": [{"id": 1, "variant": "Phil ID Card (Plastic Card)", "description": "Capture both the front and back of the ID.", "additionalDescription": "If you have a <b>digital version of the Philippine National ID</b>, please see the instructions for <a href=\"eGovPH\">eGovPH app National ID</a> or <a href=\"DigitalID\">Digital National ID (Website)</a>.", "images": [{"id": 1, "instruction": null, "url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Phil_ID_Card_Front.png"}, {"id": 2, "instruction": null, "url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Phil_ID_Card_Back.png"}]}, {"id": 2, "variant": "ePHIL Paper", "description": "Capture both the front and back of the ID. Please make sure <b>the ID is cut accordingly like below</b>.", "additionalDescription": null, "images": [{"id": 3, "instruction": null, "url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Ephil_Paper_Front.png"}, {"id": 4, "instruction": null, "url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Ephil_Paper_Back.png"}]}, {"id": 3, "variant": "Digital National ID (eGov PH app)", "description": "<b>Upload a screenshot</b> of your National ID from the <b>eGov app</b>. Please do not use a printout of your ID.<br><br>Use the 16 digit number (found on top of your ID picture) as your ID number", "additionalDescription": null, "images": [{"id": 5, "instruction": "For the front of your ID, take a screenshot as shown below:", "url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/EGov_PH_App_Front.png"}, {"id": 6, "instruction": "For the back of your ID, tap on “Enlarge ID” then “Show back”. Take a screenshot as shown below:", "url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/EGov_PH_App_Back.png"}]}, {"id": 4, "variant": "Digital National ID (Website)", "description": "<b>Upload a separate screenshot</b> for the <b>front and back</b> from the <b>National ID website</b>. Please do not use a printout of your ID.<br><br>Use the 16 digit number (found on top of your ID picture) as your ID number", "additionalDescription": null, "images": [{"id": 7, "instruction": null, "url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/National_Digital_Website_Front.png"}, {"id": 8, "instruction": null, "url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/National_Digital_Website_Back.png"}]}], "id_guides": [{"id": 3, "front": "PHILSYS_DIGITAL", "back": "PHILSYS_BACK"}, {"id": 4, "front": "PH_NATIONAL_ID", "back": "PHILSYS_BACK"}, {"id": 5, "front": "EPHIL_ID", "back": "EPHIL_BACK"}]}, {"id": 4, "header": "Using your Philippine passport", "sub_header": "Follow these tips to get a clear ID photo", "is_allowed_to_upload": false, "document": {"id": 4, "key": "PASSPORT"}, "capture_methods": [{"id": 4, "method": "SIMPLE_ID_TENCENT_LIVENESS"}], "pages": [{"id": 7, "page": "FRONT"}], "samples": [{"id": 1, "variant": "", "description": "✅ Only show the full information page of your passport, with clear and readable details", "additionalDescription": null, "images": [{"id": 1, "instruction": null, "url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Passport_Front.png"}, {"id": 2, "instruction": "❌ Make sure there is no glare or hologram on your passport<br/><br/>❌ Do not cover any part of your ID", "url": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Passport_Glare.png"}]}], "id_guides": [{"id": 6, "front": "PASSPORT", "back": null}]}, {"id": 5, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 5, "key": "POSTAL_ID"}, "capture_methods": [{"id": 5, "method": "SIMPLE_ID_TENCENT_LIVENESS"}], "pages": [{"id": 8, "page": "FRONT"}], "samples": [], "id_guides": [{"id": 7, "front": "POSTAL_ID", "back": null}]}, {"id": 6, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 6, "key": "PRC_ID"}, "capture_methods": [{"id": 6, "method": "SIMPLE_ID_TENCENT_LIVENESS"}], "pages": [{"id": 9, "page": "FRONT"}, {"id": 10, "page": "BACK"}], "samples": [], "id_guides": [{"id": 8, "front": "PRC_ID", "back": null}]}, {"id": 7, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 7, "key": "SSS_ID"}, "capture_methods": [{"id": 7, "method": "SIMPLE_ID_TENCENT_LIVENESS"}], "pages": [{"id": 11, "page": "FRONT"}, {"id": 12, "page": "BACK"}], "samples": [], "id_guides": [{"id": 9, "front": "SSS_ID", "back": null}]}, {"id": 8, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 8, "key": "FOREIGN_PASSPORT"}, "capture_methods": [{"id": 8, "method": "SIMPLE_ID_TENCENT_LIVENESS"}], "pages": [{"id": 13, "page": "FRONT"}], "samples": [], "id_guides": [{"id": 10, "front": "FOREIGN_PASSPORT", "back": null}]}, {"id": 9, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 17, "key": "EPHIL_ID"}, "capture_methods": [{"id": 9, "method": "SIMPLE_ID_TENCENT_LIVENESS"}], "pages": [{"id": 14, "page": "FRONT"}, {"id": 15, "page": "BACK"}], "samples": [], "id_guides": [{"id": 11, "front": "EPHIL_ID", "back": "EPHIL_BACK"}]}, {"id": 10, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 12, "key": "NBI_CLEARANCE"}, "capture_methods": [{"id": 10, "method": "SIMPLE_ID_TENCENT_LIVENESS"}], "pages": [{"id": 16, "page": "FRONT"}], "samples": [], "id_guides": [{"id": 12, "front": "NBI_CLEARANCE", "back": null}]}, {"id": 11, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 13, "key": "ALIEN_CERTIFICATION"}, "capture_methods": [{"id": 11, "method": "SIMPLE_ID_TENCENT_LIVENESS"}], "pages": [{"id": 17, "page": "FRONT"}, {"id": 18, "page": "BACK"}], "samples": [], "id_guides": []}, {"id": 12, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 15, "key": "GOCC_ID"}, "capture_methods": [{"id": 12, "method": "SIMPLE_ID_TENCENT_LIVENESS"}], "pages": [{"id": 19, "page": "FRONT"}, {"id": 20, "page": "BACK"}], "samples": [], "id_guides": []}, {"id": 13, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 14, "key": "IBP_ID"}, "capture_methods": [{"id": 13, "method": "SIMPLE_ID_TENCENT_LIVENESS"}], "pages": [{"id": 21, "page": "FRONT"}], "samples": [], "id_guides": []}, {"id": 14, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 16, "key": "SCHOOL_ID"}, "capture_methods": [{"id": 14, "method": "SIMPLE_ID_TENCENT_LIVENESS"}], "pages": [{"id": 22, "page": "FRONT"}, {"id": 23, "page": "BACK"}], "samples": [], "id_guides": []}, {"id": 15, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 18, "key": "PHILHEALTH_ID"}, "capture_methods": [{"id": 15, "method": "SIMPLE_ID_TENCENT_LIVENESS"}, {"id": 16, "method": "SIMPLE_ID_ONLY"}], "pages": [{"id": 24, "page": "FRONT"}], "samples": [], "id_guides": [{"id": 13, "front": "PHILHEALTH_ID", "back": null}]}, {"id": 16, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 19, "key": "VOTERS_ID"}, "capture_methods": [{"id": 17, "method": "SIMPLE_ID_TENCENT_LIVENESS"}, {"id": 18, "method": "SIMPLE_ID_ONLY"}], "pages": [{"id": 25, "page": "FRONT"}], "samples": [], "id_guides": [{"id": 14, "front": "VOTERS_ID", "back": null}]}, {"id": 17, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 20, "key": "TIN_ID"}, "capture_methods": [{"id": 19, "method": "SIMPLE_ID_TENCENT_LIVENESS"}, {"id": 20, "method": "SIMPLE_ID_ONLY"}], "pages": [{"id": 26, "page": "FRONT"}], "samples": [], "id_guides": [{"id": 15, "front": "TIN_ID", "back": null}]}, {"id": 18, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 21, "key": "SENIOR_CITIZEN_CARD"}, "capture_methods": [{"id": 21, "method": "SIMPLE_ID_TENCENT_LIVENESS"}, {"id": 22, "method": "SIMPLE_ID_ONLY"}], "pages": [{"id": 27, "page": "FRONT"}, {"id": 28, "page": "BACK"}], "samples": [], "id_guides": []}, {"id": 19, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 22, "key": "POLICE_CLEARANCE"}, "capture_methods": [{"id": 23, "method": "SIMPLE_ID_TENCENT_LIVENESS"}, {"id": 24, "method": "SIMPLE_ID_ONLY"}], "pages": [{"id": 29, "page": "FRONT"}], "samples": [], "id_guides": [{"id": 16, "front": "POLICE_CLEARANCE", "back": null}]}, {"id": 20, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 23, "key": "GSIS_ID"}, "capture_methods": [{"id": 25, "method": "SIMPLE_ID_TENCENT_LIVENESS"}, {"id": 26, "method": "SIMPLE_ID_ONLY"}], "pages": [{"id": 30, "page": "FRONT"}, {"id": 31, "page": "BACK"}], "samples": [], "id_guides": []}, {"id": 21, "header": null, "sub_header": null, "is_allowed_to_upload": false, "document": {"id": 24, "key": "OWWA_ID"}, "capture_methods": [{"id": 27, "method": "SIMPLE_ID_TENCENT_LIVENESS"}, {"id": 28, "method": "SIMPLE_ID_ONLY"}], "pages": [{"id": 32, "page": "FRONT"}], "samples": [], "id_guides": []}], "image_quality_guides": [{"id": 10, "feedback": {"id": 10, "key": "PAPER"}, "confidence_thresholds": [{"id": 216, "threshold": 0, "decision": "PASSED"}], "allowed_ids": [{"id": 31, "key": "DRIVERS_LICENSE", "action": "ALLOW"}, {"id": 32, "key": "NBI_CLEARANCE", "action": "ALLOW"}, {"id": 33, "key": "POLICE_CLEARANCE", "action": "ALLOW"}, {"id": 34, "key": "VOTERS_ID", "action": "ALLOW"}, {"id": 35, "key": "TIN_ID", "action": "ALLOW"}, {"id": 36, "key": "PHILHEALTH_ID", "action": "ALLOW"}, {"id": 37, "key": "EPHIL_ID", "action": "ALLOW"}, {"id": 38, "key": "SENIOR_CITIZEN_CARD", "action": "ALLOW"}, {"id": 41, "key": "PH_NATIONAL_ID", "action": "ALLOW"}]}, {"id": 11, "feedback": {"id": 11, "key": "GOOD_QUALITY"}, "confidence_thresholds": [{"id": 217, "threshold": 90, "decision": "PASSED"}, {"id": 218, "threshold": 0, "decision": "FAILED"}], "allowed_ids": [{"id": 39, "key": "*", "action": "ALLOW"}]}, {"id": 12, "feedback": {"id": 12, "key": "FAKE_ID"}, "confidence_thresholds": [{"id": 219, "threshold": 0, "decision": "PASSED"}], "allowed_ids": [{"id": 40, "key": "*", "action": "ALLOW"}]}, {"id": 13, "feedback": {"id": 13, "key": "NOT_LIVE"}, "confidence_thresholds": [{"id": 220, "threshold": 0, "decision": "PASSED"}], "allowed_ids": [{"id": 42, "key": "PH_NATIONAL_ID", "action": "ALLOW"}]}], "work_natures": [{"id": 31, "name": "Lending and Financing", "requiresEmploymentDetails": true, "description": "Lending and Financing", "additionalInfoRequired": false, "prohibitedIncomeSources": []}, {"id": 22, "name": "Legal Practice (Firms)", "requiresEmploymentDetails": true, "description": "Legal Practice (Firms)", "additionalInfoRequired": false, "prohibitedIncomeSources": []}, {"id": 23, "name": "Students", "requiresEmploymentDetails": false, "description": "Students", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}, {"id": 2, "name": "Business Proceeds", "description": "Business Proceeds"}, {"id": 4, "name": "Pension", "description": "Pension"}, {"id": 9, "name": "Loan Proceeds", "description": "Loan Proceeds"}, {"id": 1, "name": "Salary", "description": "Salary"}, {"id": 11, "name": "Commission", "description": "Commission"}, {"id": 12, "name": "Online Business", "description": "Online Business"}, {"id": 14, "name": "Self Employed", "description": "Self Employed"}]}, {"id": 24, "name": "Housewife/Househus<PERSON>/Dependent", "requiresEmploymentDetails": false, "description": "Housewife/Househus<PERSON>/Dependent", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 1, "name": "Salary", "description": "Salary"}, {"id": 2, "name": "Business Proceeds", "description": "Business Proceeds"}, {"id": 4, "name": "Pension", "description": "Pension"}, {"id": 9, "name": "Loan Proceeds", "description": "Loan Proceeds"}, {"id": 11, "name": "Commission", "description": "Commission"}, {"id": 12, "name": "Online Business", "description": "Online Business"}, {"id": 14, "name": "Self Employed", "description": "Self Employed"}]}, {"id": 25, "name": "Freelance (e.g Writer, Buy and Sell)", "requiresEmploymentDetails": false, "description": "Freelance (e.g Writer, Buy and Sell)", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}, {"id": 4, "name": "Pension", "description": "Pension"}]}, {"id": 26, "name": "Doctor / Dentist/other Medical Professionals", "requiresEmploymentDetails": true, "description": "Doctor / Dentist/other Medical Professionals", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 27, "name": "Multi Level Marketing", "requiresEmploymentDetails": true, "description": "Multi Level Marketing", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 28, "name": "Real Estate", "requiresEmploymentDetails": true, "description": "Real Estate", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 29, "name": "Car Dealers", "requiresEmploymentDetails": true, "description": "Car Dealers", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 30, "name": "Insurance Sector", "requiresEmploymentDetails": true, "description": "Insurance Sector", "additionalInfoRequired": false, "prohibitedIncomeSources": []}, {"id": 21, "name": "Lawyers or Notaries", "requiresEmploymentDetails": true, "description": "Lawyers or Notaries", "additionalInfoRequired": false, "prohibitedIncomeSources": []}, {"id": 32, "name": "IT Companies", "requiresEmploymentDetails": true, "description": "IT Companies", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 33, "name": "BPO Companies", "requiresEmploymentDetails": true, "description": "BPO Companies", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 34, "name": "Precious Metals and Stones Business", "requiresEmploymentDetails": true, "description": "Precious Metals and Stones Business", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 35, "name": "Jewelry Business", "requiresEmploymentDetails": true, "description": "Jewelry Business", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 36, "name": "Transportation (Driver, Operator, etc.)", "requiresEmploymentDetails": true, "description": "Transportation (Driver, Operator, etc.)", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 37, "name": "Manufacturing", "requiresEmploymentDetails": true, "description": "Manufacturing", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 38, "name": "Construction", "requiresEmploymentDetails": true, "description": "Construction", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 39, "name": "Education (Teacher, Tutor, Professor, etc.)", "requiresEmploymentDetails": true, "description": "Education (Teacher, Tutor, Professor, etc.)", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 40, "name": "Other/s, please specify", "requiresEmploymentDetails": false, "description": "Other/s, please specify", "additionalInfoRequired": true, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}, {"id": 4, "name": "Pension", "description": "Pension"}]}, {"id": 11, "name": "Pawnshop", "requiresEmploymentDetails": true, "description": "Pawnshop", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 2, "name": "<PERSON><PERSON><PERSON>", "requiresEmploymentDetails": false, "description": "<PERSON><PERSON><PERSON>", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 9, "name": "Loan Proceeds", "description": "Loan Proceeds"}, {"id": 1, "name": "Salary", "description": "Salary"}, {"id": 2, "name": "Business Proceeds", "description": "Business Proceeds"}, {"id": 11, "name": "Commission", "description": "Commission"}, {"id": 12, "name": "Online Business", "description": "Online Business"}, {"id": 14, "name": "Self Employed", "description": "Self Employed"}]}, {"id": 3, "name": "Casinos/gaming clubs/lottery outlet", "requiresEmploymentDetails": true, "description": "Casinos/gaming clubs/lottery outlet", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 4, "name": "e-commerce/ online business", "requiresEmploymentDetails": true, "description": "e-commerce/ online business", "additionalInfoRequired": false, "prohibitedIncomeSources": []}, {"id": 5, "name": "Accountant", "requiresEmploymentDetails": true, "description": "Accountant", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 6, "name": "Banking", "requiresEmploymentDetails": true, "description": "Banking", "additionalInfoRequired": false, "prohibitedIncomeSources": []}, {"id": 7, "name": "Brokerage/ Securities Sector", "requiresEmploymentDetails": true, "description": "Brokerage/ Securities Sector", "additionalInfoRequired": false, "prohibitedIncomeSources": []}, {"id": 8, "name": "Financial Services (Non Stock Savings and Loans Associations (NSSLs))", "requiresEmploymentDetails": true, "description": "Financial Services (Non Stock Savings and Loans Associations (NSSLs))", "additionalInfoRequired": false, "prohibitedIncomeSources": []}, {"id": 9, "name": "FX Dealer / Money Changer", "requiresEmploymentDetails": true, "description": "FX Dealer / Money Changer", "additionalInfoRequired": false, "prohibitedIncomeSources": []}, {"id": 10, "name": "E-Money Issuers", "requiresEmploymentDetails": true, "description": "E-Money Issuers", "additionalInfoRequired": false, "prohibitedIncomeSources": []}, {"id": 1, "name": "OFW", "requiresEmploymentDetails": true, "description": "OFW", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 12, "name": "Remittance Agent", "requiresEmploymentDetails": true, "description": "Remittance Agent", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 13, "name": "Virtual Currencies", "requiresEmploymentDetails": true, "description": "Virtual Currencies", "additionalInfoRequired": false, "prohibitedIncomeSources": []}, {"id": 14, "name": "Embassies / Foreign Consulates", "requiresEmploymentDetails": true, "description": "Embassies / Foreign Consulates", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 15, "name": "Government Service (LGUs, etc.)", "requiresEmploymentDetails": true, "description": "Government Service (LGUs, etc.)", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 16, "name": "Government Employees", "requiresEmploymentDetails": true, "description": "Government Employees", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 17, "name": "Military and Police", "requiresEmploymentDetails": true, "description": "Military and Police", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 18, "name": "NGO/ Foundation/ Charities", "requiresEmploymentDetails": true, "description": "NGO/ Foundation/ Charities", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 19, "name": "Manning/Employment Agencies", "requiresEmploymentDetails": true, "description": "Manning/Employment Agencies", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}, {"id": 20, "name": "Religious Organizations", "requiresEmploymentDetails": true, "description": "Religious Organizations", "additionalInfoRequired": false, "prohibitedIncomeSources": [{"id": 15, "name": "Sales of Property and Assets (Real Estate, Shares, etc)", "description": "Sales of Property and Assets (Real Estate, Shares, etc)"}]}], "benefits": [{"id": 8, "key": "savingsAccount", "description": "Get a Savings Account", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Piggy_Bank_b04b5a3b21.png"}, {"id": 4, "key": "fastCredit", "description": "Get credit fast", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Coin_Bag_a3784a7c97.png"}, {"id": 3, "key": "crypto", "description": "Invest in crypto", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Crypto_bae36574ce.png"}, {"id": 9, "key": "sendMoney", "description": "Send money instantly", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Wallet_6440dc6033.png"}, {"id": 6, "key": "max<PERSON><PERSON><PERSON>", "description": "Max Wallet limit to ₱100k+", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Wallet_With_All_Apps_0356374024.png"}, {"id": 5, "key": "insurance", "description": "Get insurance coverage", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Insurance_d7c8748335.png"}, {"id": 1, "key": "betterOffers", "description": "Enjoy even better offers", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Shopping_Bag_6fc8db145f.png"}, {"id": 2, "key": "createUsername", "description": "Create a @username", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Avatar_With_Sign_6ddb4ca0a3.png"}, {"id": 7, "key": "mutualFunds", "description": "Invest in mutual funds", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Graph_7240725fee.png"}], "photo_tips": [{"id": 2, "key": "expiryDate", "description": "Check your ID’s expiry date and make sure it’s still valid", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Exclamation_671de3d870.png"}, {"id": 3, "key": "litRoom", "description": "Take the photo in a well-lit room and place your ID on a plain dark surface", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Lamp_e6d829954b.png"}, {"id": 1, "key": "clearId", "description": "Make sure your ID is clear and the whole card is seen in the shot", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Clear_Id_b258960e3e.png"}, {"id": 4, "key": "physicalId", "description": "Take a photo of your physical ID instead of a computer screen or digital copy", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Physical_Id_593c7bc324.png"}], "video_tips": [{"id": 2, "key": "litRoom", "description": "Go to a bright, evenly lit room", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Lamp_e6d829954b.png"}, {"id": 4, "key": "solidBackground", "description": "Find a plain, solid background", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Solid_Background_4565297d8b.png"}, {"id": 1, "key": "faceAccessories", "description": "Take off all your face accessories", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Accessories_a38cc6766b.png"}, {"id": 3, "key": "otherFaces", "description": "Keep other faces out of the shot", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/No_Other_Faces_af48c51a1a.png"}], "selfie_tips": {"iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Selfie_Tips_Icon.png", "header": "Get ready to take a selfie", "description": "Your camera will turn on next. We’ll only use this to verify your ID photo. Please remove any face accessories and go to a bright, evenly lit room."}, "validation_rules": [{"id": 1, "regex": "^[a-zA-ZñÑ][a-zA-ZñÑ' -]{0,49}$", "additionalRule": null, "message": "Only letters, dashes, and apostrophes within 50 characters are allowed", "validation_fields": [{"id": 1, "name": "firstName"}, {"id": 2, "name": "middleName"}, {"id": 3, "name": "lastName"}]}, {"id": 2, "regex": "^[a-zA-Z0-9]+[a-zA-Z0-9 -]*$", "additionalRule": null, "message": "Only letter, numbers, and dashes are allowed", "validation_fields": [{"id": 4, "name": "idNumber"}]}, {"id": 3, "regex": "^[a-zA-Z0-9]{1}[a-zA-Z0-9-]{0,9}$", "additionalRule": null, "message": "Only letter, numbers, and dashes are allowed", "validation_fields": [{"id": 5, "name": "zipCode"}]}, {"id": 4, "regex": "^[a-zA-Z0-9ñÑ]+[a-zA-Z0-9ñÑ'., -]*$", "additionalRule": null, "message": "Only letter, numbers, and dashes are allowed", "validation_fields": [{"id": 6, "name": "state"}, {"id": 7, "name": "city"}, {"id": 8, "name": "barangay"}, {"id": 13, "name": "workOthers"}]}, {"id": 5, "regex": null, "additionalRule": "afterNow", "message": "Please use a valid ID", "validation_fields": [{"id": 11, "name": "expiryDate"}]}, {"id": 6, "regex": "^[a-zA-Z0-9ñÑ]{1}[a-zA-Z0-9ñÑ'., -]{0,39}$", "additionalRule": null, "message": "Only letters, numbers, and dashes within 40 characters are allowed", "validation_fields": [{"id": 10, "name": "additional<PERSON>ddress"}, {"id": 9, "name": "fullAddress"}]}, {"id": 12, "regex": "^[a-zA-Z0-9ñÑ][a-zA-Z0-9ñÑ'., -]{0,254}$", "additionalRule": null, "message": "Only letters, dashes, and apostrophes within 255 characters are allowed", "validation_fields": [{"id": 12, "name": "companyName"}]}], "income_sources": [{"id": 1, "name": "Salary", "description": "Salary"}, {"id": 2, "name": "Business Proceeds", "description": "Business Proceeds"}, {"id": 3, "name": "Allowance", "description": "Allowance"}, {"id": 4, "name": "Pension", "description": "Pension"}, {"id": 5, "name": "Remittance", "description": "Remittance"}, {"id": 6, "name": "Donation", "description": "Donation"}, {"id": 7, "name": "Inheritance", "description": "Inheritance"}, {"id": 8, "name": "Government Aid", "description": "Government Aid"}, {"id": 9, "name": "Loan Proceeds", "description": "Loan Proceeds"}, {"id": 10, "name": "Interest on Savings, Placements, Investments", "description": "Interest on Savings, Placements, Investments"}, {"id": 11, "name": "Commission", "description": "Commission"}, {"id": 12, "name": "Online Business", "description": "Online Business"}, {"id": 14, "name": "Self Employed", "description": "Self Employed"}], "countries": [{"id": 1, "name": "Philippines", "code": "PH"}, {"id": 2, "name": "Afghanistan", "code": "AF"}, {"id": 3, "name": "Åland Islands", "code": "AX"}, {"id": 4, "name": "Albania", "code": "AL"}, {"id": 5, "name": "Algeria", "code": "DZ"}, {"id": 6, "name": "American Samoa", "code": "AS"}, {"id": 7, "name": "Andorra", "code": "AD"}, {"id": 9, "name": "<PERSON><PERSON><PERSON>", "code": "AI"}, {"id": 8, "name": "Angola", "code": "AO"}, {"id": 10, "name": "Antarctica", "code": "AQ"}, {"id": 11, "name": "Antigua and Barbuda", "code": "AG"}, {"id": 12, "name": "Argentina", "code": "AR"}, {"id": 13, "name": "Armenia", "code": "AM"}, {"id": 14, "name": "Aruba", "code": "AW"}, {"id": 15, "name": "Australia", "code": "AU"}, {"id": 16, "name": "Austria", "code": "AT"}, {"id": 17, "name": "Azerbaijan", "code": "AZ"}, {"id": 18, "name": "Bahamas", "code": "BS"}, {"id": 19, "name": "Bahrain", "code": "BH"}, {"id": 20, "name": "Bangladesh", "code": "BD"}, {"id": 21, "name": "Barbados", "code": "BB"}, {"id": 23, "name": "Belgium", "code": "BE"}, {"id": 24, "name": "Belize", "code": "BZ"}, {"id": 25, "name": "Benin", "code": "BJ"}, {"id": 26, "name": "Bermuda", "code": "BM"}, {"id": 28, "name": "Bolivia (Plurinational State)", "code": "BO"}, {"id": 27, "name": "Bhutan", "code": "BT"}, {"id": 29, "name": "Bonaire, Sint Eustatius and Saba", "code": "BQ"}, {"id": 30, "name": "Bosnia and Herzegovina", "code": "BA"}, {"id": 31, "name": "Botswana", "code": "BW"}, {"id": 32, "name": "Bouvet Island", "code": "BV"}, {"id": 33, "name": "Brazil", "code": "BR"}, {"id": 34, "name": "British Indian Ocean Territory", "code": "IO"}, {"id": 36, "name": "Bulgaria", "code": "BG"}, {"id": 22, "name": "Belarus", "code": "BY"}, {"id": 35, "name": "Brunei Darussalam", "code": "BN"}, {"id": 37, "name": "Burkina Faso", "code": "BF"}, {"id": 38, "name": "Burundi", "code": "BI"}, {"id": 39, "name": "Cabo Verde", "code": "CV"}, {"id": 40, "name": "Cambodia", "code": "KH"}, {"id": 41, "name": "Cameroon", "code": "CM"}, {"id": 42, "name": "Canada", "code": "CA"}, {"id": 43, "name": "Cayman Islands", "code": "KY"}, {"id": 44, "name": "Central African Republic", "code": "CF"}, {"id": 45, "name": "Chad", "code": "TD"}, {"id": 46, "name": "Chile", "code": "CL"}, {"id": 47, "name": "China", "code": "CN"}, {"id": 48, "name": "Christmas Island", "code": "CX"}, {"id": 49, "name": "Cocos (Keeling) Islands", "code": "CC"}, {"id": 50, "name": "Colombia", "code": "CO"}, {"id": 51, "name": "Comoros", "code": "KM"}, {"id": 52, "name": "Congo", "code": "CG"}, {"id": 53, "name": "Congo (Democratic Republic)", "code": "CD"}, {"id": 54, "name": "Cook Islands", "code": "CK"}, {"id": 55, "name": "Costa Rica", "code": "CR"}, {"id": 56, "name": "Côte d'Ivoire", "code": "CI"}, {"id": 57, "name": "Croatia", "code": "HR"}, {"id": 58, "name": "Cuba", "code": "CU"}, {"id": 59, "name": "Curaçao", "code": "CW"}, {"id": 60, "name": "Cyprus", "code": "CY"}, {"id": 61, "name": "Czechia", "code": "CZ"}, {"id": 62, "name": "Denmark", "code": "DK"}, {"id": 63, "name": "Djibouti", "code": "DJ"}, {"id": 64, "name": "Dominica", "code": "DM"}, {"id": 65, "name": "Dominican Republic", "code": "DO"}, {"id": 66, "name": "Ecuador", "code": "EC"}, {"id": 67, "name": "Egypt", "code": "EG"}, {"id": 68, "name": "El Salvador", "code": "SV"}, {"id": 69, "name": "Equatorial Guinea", "code": "GQ"}, {"id": 70, "name": "Eritrea", "code": "ER"}, {"id": 71, "name": "Estonia", "code": "EE"}, {"id": 72, "name": "Ethiopia", "code": "ET"}, {"id": 73, "name": "Falkland Islands [Malvinas]", "code": "FK"}, {"id": 74, "name": "Faroe Islands", "code": "FO"}, {"id": 75, "name": "Fiji", "code": "FJ"}, {"id": 76, "name": "Finland", "code": "FI"}, {"id": 77, "name": "France", "code": "FR"}, {"id": 78, "name": "French Guiana", "code": "GF"}, {"id": 79, "name": "French Polynesia", "code": "PF"}, {"id": 80, "name": "French Southern Territories", "code": "TF"}, {"id": 81, "name": "Gabon", "code": "GA"}, {"id": 82, "name": "Gambia", "code": "GM"}, {"id": 83, "name": "Georgia", "code": "GE"}, {"id": 84, "name": "Germany", "code": "DE"}, {"id": 85, "name": "Ghana", "code": "GH"}, {"id": 86, "name": "Gibraltar", "code": "GI"}, {"id": 87, "name": "Greece", "code": "GR"}, {"id": 88, "name": "Greenland", "code": "GL"}, {"id": 89, "name": "Grenada", "code": "GD"}, {"id": 90, "name": "Guadeloupe", "code": "GP"}, {"id": 91, "name": "Guam", "code": "GU"}, {"id": 92, "name": "Guatemala", "code": "GT"}, {"id": 93, "name": "Guernsey", "code": "GG"}, {"id": 94, "name": "Guinea", "code": "GN"}, {"id": 95, "name": "Guinea-Bissau", "code": "GW"}, {"id": 96, "name": "Guyana", "code": "GY"}, {"id": 97, "name": "Haiti", "code": "HT"}, {"id": 98, "name": "Heard Island and McDonald Islands", "code": "HM"}, {"id": 99, "name": "Holy See", "code": "VA"}, {"id": 100, "name": "Honduras", "code": "HN"}, {"id": 101, "name": "Hong Kong Special Administrative Region of China", "code": "HK"}, {"id": 102, "name": "Hungary", "code": "HU"}, {"id": 103, "name": "Iceland", "code": "IS"}, {"id": 104, "name": "India", "code": "IN"}, {"id": 105, "name": "Indonesia", "code": "ID"}, {"id": 106, "name": "Iran (Islamic Republic)", "code": "IR"}, {"id": 107, "name": "Iraq", "code": "IQ"}, {"id": 108, "name": "Ireland", "code": "IE"}, {"id": 109, "name": "Isle of Man", "code": "IM"}, {"id": 110, "name": "Israel", "code": "IL"}, {"id": 111, "name": "Italy", "code": "IT"}, {"id": 112, "name": "Jamaica", "code": "JM"}, {"id": 113, "name": "Japan", "code": "JP"}, {"id": 114, "name": "Jersey", "code": "JE"}, {"id": 115, "name": "Jordan", "code": "JO"}, {"id": 116, "name": "Kazakhstan", "code": "KZ"}, {"id": 117, "name": "Kenya", "code": "KE"}, {"id": 118, "name": "Kiribati", "code": "KI"}, {"id": 119, "name": "Korea (North)", "code": "KP"}, {"id": 120, "name": "Korea (South)", "code": "KR"}, {"id": 121, "name": "Kuwait", "code": "KW"}, {"id": 122, "name": "Kyrgyzstan", "code": "KG"}, {"id": 123, "name": "Lao People's Democratic Republic", "code": "LA"}, {"id": 124, "name": "Latvia", "code": "LV"}, {"id": 125, "name": "Lebanon", "code": "LB"}, {"id": 126, "name": "Lesotho", "code": "LS"}, {"id": 127, "name": "Liberia", "code": "LR"}, {"id": 128, "name": "Libya", "code": "LY"}, {"id": 129, "name": "Liechtenstein", "code": "LI"}, {"id": 130, "name": "Lithuania", "code": "LT"}, {"id": 131, "name": "Luxembourg", "code": "LU"}, {"id": 132, "name": "Macao Special Administrative Region of China", "code": "MO"}, {"id": 133, "name": "Macedonia (former Yugoslav Republic)", "code": "MK"}, {"id": 134, "name": "Madagascar", "code": "MG"}, {"id": 135, "name": "Malawi", "code": "MW"}, {"id": 136, "name": "Malaysia", "code": "MY"}, {"id": 137, "name": "Maldives", "code": "MV"}, {"id": 138, "name": "Mali", "code": "ML"}, {"id": 139, "name": "Malta", "code": "MT"}, {"id": 140, "name": "Marshall Islands", "code": "MH"}, {"id": 141, "name": "Martinique", "code": "MQ"}, {"id": 142, "name": "Mauritania", "code": "MR"}, {"id": 143, "name": "Mauritius", "code": "MU"}, {"id": 144, "name": "Mayotte", "code": "YT"}, {"id": 145, "name": "Mexico", "code": "MX"}, {"id": 146, "name": "Micronesia (Federated States)", "code": "FM"}, {"id": 147, "name": "Moldova (Republic)", "code": "MD"}, {"id": 148, "name": "Monaco", "code": "MC"}, {"id": 149, "name": "Mongolia", "code": "MN"}, {"id": 150, "name": "Montenegro", "code": "ME"}, {"id": 151, "name": "Montserrat", "code": "MS"}, {"id": 152, "name": "Morocco", "code": "MA"}, {"id": 153, "name": "Mozambique", "code": "MZ"}, {"id": 154, "name": "Myanmar", "code": "MM"}, {"id": 155, "name": "Namibia", "code": "NA"}, {"id": 156, "name": "Nauru", "code": "NR"}, {"id": 157, "name": "Nepal", "code": "NP"}, {"id": 158, "name": "Netherlands", "code": "NL"}, {"id": 159, "name": "New Caledonia", "code": "NC"}, {"id": 160, "name": "New Zealand", "code": "NZ"}, {"id": 161, "name": "Nicaragua", "code": "NI"}, {"id": 162, "name": "Niger", "code": "NE"}, {"id": 163, "name": "Nigeria", "code": "NG"}, {"id": 164, "name": "Niue", "code": "NU"}, {"id": 165, "name": "Norfolk Island", "code": "NF"}, {"id": 166, "name": "Northern Mariana Islands", "code": "MP"}, {"id": 167, "name": "Norway", "code": "NO"}, {"id": 168, "name": "Oman", "code": "OM"}, {"id": 169, "name": "Pakistan", "code": "PK"}, {"id": 170, "name": "<PERSON><PERSON>", "code": "PW"}, {"id": 171, "name": "Palestine, State of", "code": "PS"}, {"id": 172, "name": "Panama", "code": "PA"}, {"id": 173, "name": "Papua New Guinea", "code": "PG"}, {"id": 175, "name": "Paraguay", "code": "PY"}, {"id": 176, "name": "Peru", "code": "PE"}, {"id": 177, "name": "Pitcairn", "code": "PN"}, {"id": 178, "name": "Poland", "code": "PL"}, {"id": 179, "name": "Portugal", "code": "PT"}, {"id": 180, "name": "Puerto Rico", "code": "PR"}, {"id": 181, "name": "Qatar", "code": "QA"}, {"id": 182, "name": "Réunion", "code": "RE"}, {"id": 183, "name": "Romania", "code": "RO"}, {"id": 184, "name": "Russian Federation", "code": "RU"}, {"id": 185, "name": "Rwanda", "code": "RW"}, {"id": 186, "name": "<PERSON>", "code": "BL"}, {"id": 187, "name": "Saint Helena, Ascension and Tristan <PERSON>ha", "code": "SH"}, {"id": 188, "name": "Saint Kitts and Nevis", "code": "KN"}, {"id": 189, "name": "Saint Lucia", "code": "LC"}, {"id": 190, "name": "<PERSON> (French part)", "code": "MF"}, {"id": 191, "name": "Saint Pierre and Miquelon", "code": "PM"}, {"id": 192, "name": "Saint Vincent and the Grenadines", "code": "VC"}, {"id": 193, "name": "Samoa", "code": "WS"}, {"id": 194, "name": "San Marino", "code": "SM"}, {"id": 195, "name": "Sao Tome and Principe", "code": "ST"}, {"id": 196, "name": "Saudi Arabia", "code": "SA"}, {"id": 197, "name": "Senegal", "code": "SN"}, {"id": 198, "name": "Serbia", "code": "RS"}, {"id": 199, "name": "Seychelles", "code": "SC"}, {"id": 200, "name": "Sierra Leone", "code": "SL"}, {"id": 201, "name": "Singapore", "code": "SG"}, {"id": 202, "name": "<PERSON><PERSON> Ma<PERSON>n (Dutch part)", "code": "SX"}, {"id": 203, "name": "Slovakia", "code": "SK"}, {"id": 204, "name": "Slovenia", "code": "SI"}, {"id": 205, "name": "Solomon Islands", "code": "SB"}, {"id": 206, "name": "Somalia", "code": "SO"}, {"id": 207, "name": "South Africa", "code": "ZA"}, {"id": 208, "name": "South Georgia and the South Sandwich Islands", "code": "GS"}, {"id": 209, "name": "South Sudan", "code": "SS"}, {"id": 210, "name": "Spain", "code": "ES"}, {"id": 211, "name": "Sri Lanka", "code": "LK"}, {"id": 212, "name": "Sudan", "code": "SD"}, {"id": 213, "name": "Suriname", "code": "SR"}, {"id": 214, "name": "Svalbard and <PERSON>", "code": "SJ"}, {"id": 215, "name": "Swaziland / Eswatini", "code": "SZ"}, {"id": 216, "name": "Sweden", "code": "SE"}, {"id": 217, "name": "Switzerland", "code": "CH"}, {"id": 218, "name": "Syrian Arab Republic", "code": "SY"}, {"id": 219, "name": "Taiwan, Province of China", "code": "TW"}, {"id": 220, "name": "Tajikistan", "code": "TJ"}, {"id": 221, "name": "Tanzania, United Republic of", "code": "TZ"}, {"id": 222, "name": "Thailand", "code": "TH"}, {"id": 223, "name": "Timor-Leste", "code": "TL"}, {"id": 224, "name": "Togo", "code": "TG"}, {"id": 225, "name": "Tokelau", "code": "TK"}, {"id": 226, "name": "Tonga", "code": "TO"}, {"id": 227, "name": "Trinidad and Tobago", "code": "TT"}, {"id": 228, "name": "Tunisia", "code": "TN"}, {"id": 229, "name": "Turkey", "code": "TR"}, {"id": 230, "name": "Turkmenistan", "code": "TM"}, {"id": 231, "name": "Turks and Caicos Islands", "code": "TC"}, {"id": 232, "name": "Tuvalu", "code": "TV"}, {"id": 233, "name": "Uganda", "code": "UG"}, {"id": 234, "name": "Ukraine", "code": "UA"}, {"id": 235, "name": "United Arab Emirates", "code": "AE"}, {"id": 236, "name": "United Kingdom of Great Britain and Northern Ireland", "code": "GB"}, {"id": 237, "name": "United States Minor Outlying Islands", "code": "UM"}, {"id": 238, "name": "United States of America", "code": "US"}, {"id": 239, "name": "Uruguay", "code": "UY"}, {"id": 240, "name": "Uzbekistan", "code": "UZ"}, {"id": 241, "name": "Vanuatu", "code": "VU"}, {"id": 242, "name": "Venezuela (Bolivarian Republic)", "code": "VE"}, {"id": 243, "name": "Viet Nam", "code": "VN"}, {"id": 244, "name": "Virgin Islands (British)", "code": "VG"}, {"id": 245, "name": "Virgin Islands (U.S.)", "code": "VI"}, {"id": 246, "name": "Wallis and Futuna", "code": "WF"}, {"id": 247, "name": "Western Sahara", "code": "EH"}, {"id": 248, "name": "Yemen", "code": "YE"}, {"id": 249, "name": "Zambia", "code": "ZM"}, {"id": 250, "name": "Zimbabwe", "code": "ZW"}], "nationalities": [{"id": 1, "name": "Philippines", "code": "PH"}, {"id": 2, "name": "Afghanistan", "code": "AF"}, {"id": 3, "name": "Åland Islands", "code": "AX"}, {"id": 4, "name": "Albania", "code": "AL"}, {"id": 5, "name": "Algeria", "code": "DZ"}, {"id": 6, "name": "American Samoa", "code": "AS"}, {"id": 7, "name": "Andorra", "code": "AD"}, {"id": 8, "name": "Angola", "code": "AO"}, {"id": 9, "name": "<PERSON><PERSON><PERSON>", "code": "AI"}, {"id": 10, "name": "Antarctica", "code": "AQ"}, {"id": 11, "name": "Antigua and Barbuda", "code": "AG"}, {"id": 12, "name": "Argentina", "code": "AR"}, {"id": 13, "name": "Armenia", "code": "AM"}, {"id": 14, "name": "Aruba", "code": "AW"}, {"id": 15, "name": "Australia", "code": "AU"}, {"id": 16, "name": "Austria", "code": "AT"}, {"id": 17, "name": "Azerbaijan", "code": "AZ"}, {"id": 18, "name": "Bahamas", "code": "BS"}, {"id": 19, "name": "Bahrain", "code": "BH"}, {"id": 20, "name": "Bangladesh", "code": "BD"}, {"id": 21, "name": "Barbados", "code": "BB"}, {"id": 22, "name": "Belarus", "code": "BY"}, {"id": 23, "name": "Belgium", "code": "BE"}, {"id": 24, "name": "Belize", "code": "BZ"}, {"id": 25, "name": "Benin", "code": "BJ"}, {"id": 26, "name": "Bermuda", "code": "BM"}, {"id": 27, "name": "Bhutan", "code": "BT"}, {"id": 28, "name": "Bolivia (Plurinational State)", "code": "BO"}, {"id": 29, "name": "Bonaire, Sint Eustatius and Saba", "code": "BQ"}, {"id": 30, "name": "Bosnia and Herzegovina", "code": "BA"}, {"id": 31, "name": "Botswana", "code": "BW"}, {"id": 32, "name": "Bouvet Island", "code": "BV"}, {"id": 33, "name": "Brazil", "code": "BR"}, {"id": 34, "name": "British Indian Ocean Territory", "code": "IO"}, {"id": 35, "name": "Brunei Darussalam", "code": "BN"}, {"id": 36, "name": "Bulgaria", "code": "BG"}, {"id": 37, "name": "Burkina Faso", "code": "BF"}, {"id": 38, "name": "Burundi", "code": "BI"}, {"id": 39, "name": "Cabo Verde", "code": "CV"}, {"id": 40, "name": "Cambodia", "code": "KH"}, {"id": 41, "name": "Cameroon", "code": "CM"}, {"id": 42, "name": "Canada", "code": "CA"}, {"id": 43, "name": "Cayman Islands", "code": "KY"}, {"id": 44, "name": "Central African Republic", "code": "CF"}, {"id": 45, "name": "Chad", "code": "TD"}, {"id": 46, "name": "Chile", "code": "CL"}, {"id": 47, "name": "China", "code": "CN"}, {"id": 48, "name": "Christmas Island", "code": "CX"}, {"id": 49, "name": "Cocos (Keeling) Islands", "code": "CC"}, {"id": 50, "name": "Colombia", "code": "CO"}, {"id": 51, "name": "Comoros", "code": "KM"}, {"id": 52, "name": "Congo", "code": "CG"}, {"id": 53, "name": "Congo (Democratic Republic)", "code": "CD"}, {"id": 54, "name": "Cook Islands", "code": "CK"}, {"id": 55, "name": "Costa Rica", "code": "CR"}, {"id": 56, "name": "Côte d'Ivoire", "code": "CI"}, {"id": 57, "name": "Croatia", "code": "HR"}, {"id": 58, "name": "Cuba", "code": "CU"}, {"id": 59, "name": "Curaçao", "code": "CW"}, {"id": 60, "name": "Cyprus", "code": "CY"}, {"id": 61, "name": "Czechia", "code": "CZ"}, {"id": 62, "name": "Denmark", "code": "DK"}, {"id": 63, "name": "Djibouti", "code": "DJ"}, {"id": 64, "name": "Dominica", "code": "DM"}, {"id": 65, "name": "Dominican Republic", "code": "DO"}, {"id": 66, "name": "Ecuador", "code": "EC"}, {"id": 67, "name": "Egypt", "code": "EG"}, {"id": 68, "name": "El Salvador", "code": "SV"}, {"id": 69, "name": "Equatorial Guinea", "code": "GQ"}, {"id": 70, "name": "Eritrea", "code": "ER"}, {"id": 71, "name": "Estonia", "code": "EE"}, {"id": 72, "name": "Ethiopia", "code": "ET"}, {"id": 73, "name": "Falkland Islands [Malvinas]", "code": "FK"}, {"id": 74, "name": "Faroe Islands", "code": "FO"}, {"id": 75, "name": "Fiji", "code": "FJ"}, {"id": 76, "name": "Finland", "code": "FI"}, {"id": 77, "name": "France", "code": "FR"}, {"id": 78, "name": "French Guiana", "code": "GF"}, {"id": 79, "name": "French Polynesia", "code": "PF"}, {"id": 80, "name": "French Southern Territories", "code": "TF"}, {"id": 81, "name": "Gabon", "code": "GA"}, {"id": 82, "name": "Gambia", "code": "GM"}, {"id": 83, "name": "Georgia", "code": "GE"}, {"id": 84, "name": "Germany", "code": "DE"}, {"id": 85, "name": "Ghana", "code": "GH"}, {"id": 86, "name": "Gibraltar", "code": "GI"}, {"id": 87, "name": "Greece", "code": "GR"}, {"id": 88, "name": "Greenland", "code": "GL"}, {"id": 89, "name": "Grenada", "code": "GD"}, {"id": 90, "name": "Guadeloupe", "code": "GP"}, {"id": 91, "name": "Guam", "code": "GU"}, {"id": 92, "name": "Guatemala", "code": "GT"}, {"id": 93, "name": "Guernsey", "code": "GG"}, {"id": 94, "name": "Guinea", "code": "GN"}, {"id": 95, "name": "Guinea-Bissau", "code": "GW"}, {"id": 96, "name": "Guyana", "code": "GY"}, {"id": 97, "name": "Haiti", "code": "HT"}, {"id": 98, "name": "Heard Island and McDonald Islands", "code": "HM"}, {"id": 99, "name": "Holy See", "code": "VA"}, {"id": 100, "name": "Honduras", "code": "HN"}, {"id": 101, "name": "Hong Kong Special Administrative Region of China", "code": "HK"}, {"id": 102, "name": "Hungary", "code": "HU"}, {"id": 103, "name": "Iceland", "code": "IS"}, {"id": 104, "name": "India", "code": "IN"}, {"id": 105, "name": "Indonesia", "code": "ID"}, {"id": 106, "name": "Iran (Islamic Republic)", "code": "IR"}, {"id": 107, "name": "Iraq", "code": "IQ"}, {"id": 108, "name": "Ireland", "code": "IE"}, {"id": 109, "name": "Isle of Man", "code": "IM"}, {"id": 110, "name": "Israel", "code": "IL"}, {"id": 111, "name": "Italy", "code": "IT"}, {"id": 112, "name": "Jamaica", "code": "JM"}, {"id": 113, "name": "Japan", "code": "JP"}, {"id": 114, "name": "Jersey", "code": "JE"}, {"id": 115, "name": "Jordan", "code": "JO"}, {"id": 116, "name": "Kazakhstan", "code": "KZ"}, {"id": 117, "name": "Kenya", "code": "KE"}, {"id": 118, "name": "Kiribati", "code": "KI"}, {"id": 119, "name": "Korea (North)", "code": "KP"}, {"id": 120, "name": "Korea (South)", "code": "KR"}, {"id": 121, "name": "Kuwait", "code": "KW"}, {"id": 122, "name": "Kyrgyzstan", "code": "KG"}, {"id": 123, "name": "Lao People's Democratic Republic", "code": "LA"}, {"id": 124, "name": "Latvia", "code": "LV"}, {"id": 125, "name": "Lebanon", "code": "LB"}, {"id": 126, "name": "Lesotho", "code": "LS"}, {"id": 127, "name": "Liberia", "code": "LR"}, {"id": 128, "name": "Libya", "code": "LY"}, {"id": 129, "name": "Liechtenstein", "code": "LI"}, {"id": 130, "name": "Lithuania", "code": "LT"}, {"id": 131, "name": "Luxembourg", "code": "LU"}, {"id": 132, "name": "Macao Special Administrative Region of China", "code": "MO"}, {"id": 133, "name": "Macedonia (former Yugoslav Republic)", "code": "MK"}, {"id": 134, "name": "Madagascar", "code": "MG"}, {"id": 135, "name": "Malawi", "code": "MW"}, {"id": 136, "name": "Malaysia", "code": "MY"}, {"id": 137, "name": "Maldives", "code": "MV"}, {"id": 138, "name": "Mali", "code": "ML"}, {"id": 139, "name": "Malta", "code": "MT"}, {"id": 140, "name": "Marshall Islands", "code": "MH"}, {"id": 141, "name": "Martinique", "code": "MQ"}, {"id": 142, "name": "Mauritania", "code": "MR"}, {"id": 143, "name": "Mauritius", "code": "MU"}, {"id": 144, "name": "Mayotte", "code": "YT"}, {"id": 145, "name": "Mexico", "code": "MX"}, {"id": 146, "name": "Micronesia (Federated States)", "code": "FM"}, {"id": 147, "name": "Moldova (Republic)", "code": "MD"}, {"id": 148, "name": "Monaco", "code": "MC"}, {"id": 149, "name": "Mongolia", "code": "MN"}, {"id": 150, "name": "Montenegro", "code": "ME"}, {"id": 151, "name": "Montserrat", "code": "MS"}, {"id": 152, "name": "Morocco", "code": "MA"}, {"id": 153, "name": "Mozambique", "code": "MZ"}, {"id": 154, "name": "Myanmar", "code": "MM"}, {"id": 155, "name": "Namibia", "code": "NA"}, {"id": 156, "name": "Nauru", "code": "NR"}, {"id": 157, "name": "Nepal", "code": "NP"}, {"id": 158, "name": "Netherlands", "code": "NL"}, {"id": 159, "name": "New Caledonia", "code": "NC"}, {"id": 160, "name": "New Zealand", "code": "NZ"}, {"id": 161, "name": "Nicaragua", "code": "NI"}, {"id": 162, "name": "Niger", "code": "NE"}, {"id": 163, "name": "Nigeria", "code": "NG"}, {"id": 164, "name": "Niue", "code": "NU"}, {"id": 165, "name": "Norfolk Island", "code": "NF"}, {"id": 166, "name": "Northern Mariana Islands", "code": "MP"}, {"id": 167, "name": "Norway", "code": "NO"}, {"id": 168, "name": "Oman", "code": "OM"}, {"id": 169, "name": "Pakistan", "code": "PK"}, {"id": 170, "name": "<PERSON><PERSON>", "code": "PW"}, {"id": 171, "name": "Palestine, State of", "code": "PS"}, {"id": 172, "name": "Panama", "code": "PA"}, {"id": 173, "name": "Papua New Guinea", "code": "PG"}, {"id": 175, "name": "Paraguay", "code": "PY"}, {"id": 176, "name": "Peru", "code": "PE"}, {"id": 177, "name": "Pitcairn", "code": "PN"}, {"id": 178, "name": "Poland", "code": "PL"}, {"id": 179, "name": "Portugal", "code": "PT"}, {"id": 180, "name": "Puerto Rico", "code": "PR"}, {"id": 181, "name": "Qatar", "code": "QA"}, {"id": 182, "name": "Réunion", "code": "RE"}, {"id": 183, "name": "Romania", "code": "RO"}, {"id": 184, "name": "Russian Federation", "code": "RU"}, {"id": 185, "name": "Rwanda", "code": "RW"}, {"id": 186, "name": "<PERSON>", "code": "BL"}, {"id": 187, "name": "Saint Helena, Ascension and Tristan <PERSON>ha", "code": "SH"}, {"id": 188, "name": "Saint Kitts and Nevis", "code": "KN"}, {"id": 189, "name": "Saint Lucia", "code": "LC"}, {"id": 190, "name": "<PERSON> (French part)", "code": "MF"}, {"id": 191, "name": "Saint Pierre and Miquelon", "code": "PM"}, {"id": 192, "name": "Saint Vincent and the Grenadines", "code": "VC"}, {"id": 193, "name": "Samoa", "code": "WS"}, {"id": 194, "name": "San Marino", "code": "SM"}, {"id": 195, "name": "Sao Tome and Principe", "code": "ST"}, {"id": 196, "name": "Saudi Arabia", "code": "SA"}, {"id": 197, "name": "Senegal", "code": "SN"}, {"id": 198, "name": "Serbia", "code": "RS"}, {"id": 199, "name": "Seychelles", "code": "SC"}, {"id": 200, "name": "Sierra Leone", "code": "SL"}, {"id": 201, "name": "Singapore", "code": "SG"}, {"id": 202, "name": "<PERSON><PERSON> Ma<PERSON>n (Dutch part)", "code": "SX"}, {"id": 203, "name": "Slovakia", "code": "SK"}, {"id": 204, "name": "Slovenia", "code": "SI"}, {"id": 205, "name": "Solomon Islands", "code": "SB"}, {"id": 206, "name": "Somalia", "code": "SO"}, {"id": 207, "name": "South Africa", "code": "ZA"}, {"id": 208, "name": "South Georgia and the South Sandwich Islands", "code": "GS"}, {"id": 209, "name": "South Sudan", "code": "SS"}, {"id": 210, "name": "Spain", "code": "ES"}, {"id": 211, "name": "Sri Lanka", "code": "LK"}, {"id": 212, "name": "Sudan", "code": "SD"}, {"id": 213, "name": "Suriname", "code": "SR"}, {"id": 214, "name": "Svalbard and <PERSON>", "code": "SJ"}, {"id": 215, "name": "Swaziland / Eswatini", "code": "SZ"}, {"id": 216, "name": "Sweden", "code": "SE"}, {"id": 217, "name": "Switzerland", "code": "CH"}, {"id": 218, "name": "Syrian Arab Republic", "code": "SY"}, {"id": 219, "name": "Taiwan, Province of China", "code": "TW"}, {"id": 220, "name": "Tajikistan", "code": "TJ"}, {"id": 221, "name": "Tanzania, United Republic of", "code": "TZ"}, {"id": 222, "name": "Thailand", "code": "TH"}, {"id": 223, "name": "Timor-Leste", "code": "TL"}, {"id": 224, "name": "Togo", "code": "TG"}, {"id": 225, "name": "Tokelau", "code": "TK"}, {"id": 226, "name": "Tonga", "code": "TO"}, {"id": 227, "name": "Trinidad and Tobago", "code": "TT"}, {"id": 228, "name": "Tunisia", "code": "TN"}, {"id": 229, "name": "Turkey", "code": "TR"}, {"id": 230, "name": "Turkmenistan", "code": "TM"}, {"id": 231, "name": "Turks and Caicos Islands", "code": "TC"}, {"id": 232, "name": "Tuvalu", "code": "TV"}, {"id": 233, "name": "Uganda", "code": "UG"}, {"id": 234, "name": "Ukraine", "code": "UA"}, {"id": 235, "name": "United Arab Emirates", "code": "AE"}, {"id": 236, "name": "United Kingdom of Great Britain and Northern Ireland", "code": "GB"}, {"id": 237, "name": "United States Minor Outlying Islands", "code": "UM"}, {"id": 238, "name": "United States of America", "code": "US"}, {"id": 239, "name": "Uruguay", "code": "UY"}, {"id": 240, "name": "Uzbekistan", "code": "UZ"}, {"id": 241, "name": "Vanuatu", "code": "VU"}, {"id": 242, "name": "Venezuela (Bolivarian Republic)", "code": "VE"}, {"id": 243, "name": "Viet Nam", "code": "VN"}, {"id": 244, "name": "Virgin Islands (British)", "code": "VG"}, {"id": 245, "name": "Virgin Islands (U.S.)", "code": "VI"}, {"id": 246, "name": "Wallis and Futuna", "code": "WF"}, {"id": 247, "name": "Western Sahara", "code": "EH"}, {"id": 248, "name": "Yemen", "code": "YE"}, {"id": 249, "name": "Zambia", "code": "ZM"}, {"id": 250, "name": "Zimbabwe", "code": "ZW"}], "additional_document": {"id": 1, "consentUrl": "https://www.paymaya.com/downloads/ParentalConsentForm-KYCUpgrade.pdf"}, "all_good": {"id": 1, "info": "You can only have one Maya account at a time. By submitting, you allow us to restrict other accounts in your name."}, "basic_account": {"id": 1, "info": "Please give us up to 24 hours to review and update your account status.", "iconUrl": "https://kyc-cms-bucket-test.s3.ap-southeast-1.amazonaws.com/Steps_57c575b842.png"}, "review": {"id": 1, "info": "Please give us up to 24 hours to review and update your account status.", "customerServiceUrl": "https://support.maya.ph/"}, "rule": {"id": 1, "header": "About registering your name", "value": "Local rules and regulations require you to submit your legal name. In addition, please make sure to follow these guidelines:\n\nWhat am I allowed to enter?\n • All letters from A-Z, including Ñ and their lowercase versions\n • Dashes, periods, and spaces\n • Up to three consecutive i’s (i.e., III) with spaces before and after\n\nWhat’s not allowed?\n • Numbers and special characters (e.g., 104@!$%çéß)\n • Characters from non-English languages (e.g., Japanese, Greek)\n • Letters repeated 3 or more times consecutively (e.g., AAA, BBB, CCC)", "regex": "[\\w\"'ñÑ-]*", "validationMessage": "Only letters, dashes and apostrophes are allowed"}, "submit": {"id": 1, "info": "Your account update is under review. Successfully updated accounts will be able to enjoy all the features that Maya has to offer.", "header": "Account status", "highlightedPhrase": "under review"}, "live_photo_tips": [{"id": 5, "key": "idInClearLitPlace", "description": "Put your ID in a clear and well-lit place"}, {"id": 6, "key": "idFitFrame", "description": "Make the ID fit fully inside the frame"}, {"id": 7, "key": "tapToFocus", "description": "Tap on the capture area to focus"}, {"id": 8, "key": "holdTapCamera", "description": "Hold still and tap the camera button"}], "live_video_tips": [{"id": 1, "key": "litRoom", "description": "Go to a bright, evenly lit room"}, {"id": 2, "key": "solidBackground", "description": "Find a plain, solid background"}, {"id": 3, "key": "faceAccessories", "description": "Take off all your face accessories"}, {"id": 4, "key": "otherFaces", "description": "Keep other faces out of the shot"}], "upload_guides": {"id": 1, "header": "Upload your Philippine National ID", "sub_header": "Please upload the front and back your ID", "maximum_byte_size": 3145728, "maximum_byte_size_allowed": ********, "os_types": [{"id": 1, "type": "MOBILE_ANDROID", "upload_description": "JPEG, or PNG up to 10 MB in size", "mime_types": ["image/png", "image/jpeg", "image/jpg"], "error_spiels": [{"id": 1, "code": "ERROR_UPLOAD_EXCEED_LIMIT", "message": "File is larger than 10 MB"}, {"id": 2, "code": "ERROR_UPLOAD_WRONG_TYPE", "message": "Only JPEG, or PNG files are allowed"}]}, {"id": 2, "type": "MOBILE_IOS", "upload_description": "JPEG, PNG, or HEIC up to 10 MB in size", "mime_types": ["image/png", "image/jpeg", "image/jpg", "image/heic"], "error_spiels": [{"id": 3, "code": "ERROR_UPLOAD_EXCEED_LIMIT", "message": "File is larger than 10 MB"}, {"id": 4, "code": "ERROR_UPLOAD_WRONG_TYPE", "message": "Only JPEG, PNG or HEIC files are allowed"}]}]}}, "meta": {}}