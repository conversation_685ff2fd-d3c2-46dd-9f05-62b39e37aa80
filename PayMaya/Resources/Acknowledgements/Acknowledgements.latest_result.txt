name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ewor<PERSON>, nameSpecified: 
body: Copyright 2018 AppsF…
version: 6.10.0

name: Apps<PERSON>lyerFramework, nameSpecified: 
body: Copyright 2018 AppsF…
version: 6.10.0

name: Apps<PERSON><PERSON>erFramework, nameSpecified: 
body: Copyright 2018 AppsF…
version: 6.10.0

name: Apps<PERSON>lyerFramework, nameSpecified: 
body: Copyright 2018 AppsF…
version: 6.10.0

name: AppsFlyerFramework, nameSpecified: 
body: Copyright 2018 AppsF…
version: 6.10.0

name: A<PERSON><PERSON><PERSON>erFramework, nameSpecified: 
body: Copyright 2018 AppsF…
version: 6.10.0

name: CredoAppCalendarEvents, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.2.0

name: CredoAppCalendarEvents, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.2.0

name: CredoAppCalendarEvents, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.2.0

name: CredoAppCalendarEvents, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.2.0

name: CredoAppCalendarEvents, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.2.0

name: CredoAppCalendarEvents, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.2.0

name: CredoAppContacts, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.1.0

name: CredoAppContacts, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.1.0

name: CredoAppContacts, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.1.0

name: CredoAppContacts, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.1.0

name: CredoAppContacts, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.1.0

name: CredoAppContacts, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.1.0

name: CredoAppCore, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.4.0

name: CredoAppCore, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.4.0

name: CredoAppCore, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.4.0

name: CredoAppCore, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.4.0

name: CredoAppCore, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.4.0

name: CredoAppCore, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.4.0

name: CredoAppMedia, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.2.0

name: CredoAppMedia, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.2.0

name: CredoAppMedia, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.2.0

name: CredoAppMedia, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.2.0

name: CredoAppMedia, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.2.0

name: CredoAppMedia, nameSpecified: 
body: Copyright (c) 2022 m…
version: 3.2.0

name: CTNotificationContent, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.4

name: CTNotificationContent, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.4

name: CTNotificationContent, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.4

name: CTNotificationContent, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.4

name: CTNotificationContent, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.4

name: CTNotificationContent, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.4

name: CTNotificationContent, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.4

name: CTNotificationContent, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.4

name: CTNotificationContent, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.4

name: CTNotificationContent, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.4

name: CTNotificationService, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.2

name: CTNotificationService, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.2

name: CTNotificationService, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.2

name: CTNotificationService, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.2

name: CTNotificationService, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.2

name: CTNotificationService, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.2

name: CTNotificationService, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.2

name: CTNotificationService, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.2

name: CTNotificationService, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.2

name: CTNotificationService, nameSpecified: 
body: The MIT License (MIT…
version: 0.1.2

name: DebugSwift, nameSpecified: 
body: MIT License

Copyrig…
version: 0.3.6

name: DebugSwift, nameSpecified: 
body: MIT License

Copyrig…
version: 0.3.6

name: DebugSwift, nameSpecified: 
body: MIT License

Copyrig…
version: 0.3.6

name: DebugSwift, nameSpecified: 
body: MIT License

Copyrig…
version: 0.3.6

name: DebugSwift, nameSpecified: 
body: MIT License

Copyrig…
version: 0.3.6

name: Flutter, nameSpecified: 
body: Copyright 2014 The F…
version: 3.22.300

name: Flutter, nameSpecified: 
body: Copyright 2014 The F…
version: 3.22.300

name: Flutter, nameSpecified: 
body: Copyright 2014 The F…
version: 3.22.300

name: Flutter, nameSpecified: 
body: Copyright 2014 The F…
version: 3.22.300

name: Flutter, nameSpecified: 
body: Copyright 2014 The F…
version: 3.22.300

name: Flutter, nameSpecified: 
body: Copyright 2014 The F…
version: 3.22.300

name: Flutter, nameSpecified: 
body: Copyright 2014 The F…
version: 3.22.300

name: Flutter-Debug, nameSpecified: 
body: Copyright 2014 The F…
version: 3.22.300

name: Flutter-Debug, nameSpecified: 
body: Copyright 2014 The F…
version: 3.22.300

name: Flutter-Debug, nameSpecified: 
body: Copyright 2014 The F…
version: 3.22.300

name: Flutter-Debug, nameSpecified: 
body: Copyright 2014 The F…
version: 3.22.300

name: Flutter-Debug, nameSpecified: 
body: Copyright 2014 The F…
version: 3.22.300

name: Flutter-Debug, nameSpecified: 
body: Copyright 2014 The F…
version: 3.22.300

name: Flutter-Debug, nameSpecified: 
body: Copyright 2014 The F…
version: 3.22.300

name: LicensePlist, nameSpecified: 
body: MIT License

Copyrig…
version: 3.25.1

name: LicensePlist, nameSpecified: 
body: MIT License

Copyrig…
version: 3.25.1

name: LicensePlist, nameSpecified: 
body: MIT License

Copyrig…
version: 3.25.1

name: LicensePlist, nameSpecified: 
body: MIT License

Copyrig…
version: 3.25.1

name: LicensePlist, nameSpecified: 
body: MIT License

Copyrig…
version: 3.25.1

name: LicensePlist, nameSpecified: 
body: MIT License

Copyrig…
version: 3.25.1

name: MBProgressHUD, nameSpecified: 
body: Copyright © 2009-202…
version: 1.2.0

name: MBProgressHUD, nameSpecified: 
body: Copyright © 2009-202…
version: 1.2.0

name: MBProgressHUD, nameSpecified: 
body: Copyright © 2009-202…
version: 1.2.0

name: MBProgressHUD, nameSpecified: 
body: Copyright © 2009-202…
version: 1.2.0

name: MBProgressHUD, nameSpecified: 
body: Copyright © 2009-202…
version: 1.2.0

name: MBProgressHUD, nameSpecified: 
body: Copyright © 2009-202…
version: 1.2.0

name: ShieldFraud, nameSpecified: 
body: Copyright 2024 Shiel…
version: 1.5.49

name: ShieldFraud, nameSpecified: 
body: Copyright 2024 Shiel…
version: 1.5.49

name: ShieldFraud, nameSpecified: 
body: Copyright 2024 Shiel…
version: 1.5.49

name: ShieldFraud, nameSpecified: 
body: Copyright 2024 Shiel…
version: 1.5.49

name: ShieldFraud, nameSpecified: 
body: Copyright 2024 Shiel…
version: 1.5.49

name: ShieldFraud, nameSpecified: 
body: Copyright 2024 Shiel…
version: 1.5.49

name: ShieldFraud, nameSpecified: 
body: Copyright 2024 Shiel…
version: 1.5.49

name: SwiftGen, nameSpecified: 
body: MIT Licence

Copyrig…
version: 6.6.3

name: SwiftGen, nameSpecified: 
body: MIT Licence

Copyrig…
version: 6.6.3

name: SwiftGen, nameSpecified: 
body: MIT Licence

Copyrig…
version: 6.6.3

name: SwiftGen, nameSpecified: 
body: MIT Licence

Copyrig…
version: 6.6.3

name: SwiftGen, nameSpecified: 
body: MIT Licence

Copyrig…
version: 6.6.3

name: SwiftGen, nameSpecified: 
body: MIT Licence

Copyrig…
version: 6.6.3

name: SwiftLint, nameSpecified: 
body: The MIT License (MIT…
version: 0.56.2

name: SZTextView, nameSpecified: 
body: Copyright (c) 2013 g…
version: 1.3.0

name: SZTextView, nameSpecified: 
body: Copyright (c) 2013 g…
version: 1.3.0

name: SZTextView, nameSpecified: 
body: Copyright (c) 2013 g…
version: 1.3.0

name: SZTextView, nameSpecified: 
body: Copyright (c) 2013 g…
version: 1.3.0

name: SZTextView, nameSpecified: 
body: Copyright (c) 2013 g…
version: 1.3.0

name: SZTextView, nameSpecified: 
body: Copyright (c) 2013 g…
version: 1.3.0

name: TensorFlowLiteSwift, nameSpecified: 
body: Copyright 2019 The T…
version: 2.4.0

name: TensorFlowLiteSwift, nameSpecified: 
body: Copyright 2019 The T…
version: 2.4.0

name: TensorFlowLiteSwift, nameSpecified: 
body: Copyright 2019 The T…
version: 2.4.0

name: TensorFlowLiteSwift, nameSpecified: 
body: Copyright 2019 The T…
version: 2.4.0

name: TensorFlowLiteSwift, nameSpecified: 
body: Copyright 2019 The T…
version: 2.4.0

name: TensorFlowLiteSwift, nameSpecified: 
body: Copyright 2019 The T…
version: 2.4.0

name: TensorFlowLiteSwift, nameSpecified: 
body: Copyright 2019 The T…
version: 2.4.0

name: TTTAttributedLabel, nameSpecified: 
body: Copyright (c) 2011 M…
version: 2.0.0

name: TTTAttributedLabel, nameSpecified: 
body: Copyright (c) 2011 M…
version: 2.0.0

name: TTTAttributedLabel, nameSpecified: 
body: Copyright (c) 2011 M…
version: 2.0.0

name: TTTAttributedLabel, nameSpecified: 
body: Copyright (c) 2011 M…
version: 2.0.0

name: TTTAttributedLabel, nameSpecified: 
body: Copyright (c) 2011 M…
version: 2.0.0

name: TTTAttributedLabel, nameSpecified: 
body: Copyright (c) 2011 M…
version: 2.0.0

name: UIImageViewAlignedSwift, nameSpecified: 
body: Copyright (c) 2016-2…
version: 0.8.1

name: UIImageViewAlignedSwift, nameSpecified: 
body: Copyright (c) 2016-2…
version: 0.8.1

name: UIImageViewAlignedSwift, nameSpecified: 
body: Copyright (c) 2016-2…
version: 0.8.1

name: UIImageViewAlignedSwift, nameSpecified: 
body: Copyright (c) 2016-2…
version: 0.8.1

name: UIImageViewAlignedSwift, nameSpecified: 
body: Copyright (c) 2016-2…
version: 0.8.1

name: UIImageViewAlignedSwift, nameSpecified: 
body: Copyright (c) 2016-2…
version: 0.8.1

name: VoyagerUpdaterSDK, nameSpecified: 
body: All text and design …
version: 0.0.9

name: VoyagerUpdaterSDK, nameSpecified: 
body: All text and design …
version: 0.0.9

name: VoyagerUpdaterSDK, nameSpecified: 
body: All text and design …
version: 0.0.9

name: VoyagerUpdaterSDK, nameSpecified: 
body: All text and design …
version: 0.0.9

name: VoyagerUpdaterSDK, nameSpecified: 
body: All text and design …
version: 0.0.9

name: VoyagerUpdaterSDK, nameSpecified: 
body: All text and design …
version: 0.0.9

add-version-numbers: false

LicensePlist Version: 3.25.1