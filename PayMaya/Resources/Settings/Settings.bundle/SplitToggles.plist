<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>PreferenceSpecifiers</key>
	<array>
		<dict>
			<key>Title</key>
			<string>Base App</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_dashboardNavigationRefactor</string>
			<key>Title</key>
			<string>Dashboard Navigation Refactor</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_profileClevertapUnliBanners</string>
			<key>Title</key>
			<string>profileClevertapUnliBanners</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>Key</key>
			<string>non_prod_splitBlockingTimeout</string>
			<key>Title</key>
			<string>Split Blocking Timeout</string>
			<key>Type</key>
			<string>PSTextFieldSpecifier</string>
		</dict>
		<dict>
			<key>Title</key>
			<string>Lending</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_iclMfa</string>
			<key>Title</key>
			<string>ICL MFA</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_iclGeolocation</string>
			<key>Title</key>
			<string>ICL Geolocation</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_mecMfa</string>
			<key>Title</key>
			<string>MEC Transfer to Wallet MFA</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_pfMvp</string>
			<key>Title</key>
			<string>Purchase Financing</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>Title</key>
			<string>CMS Lynx</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_mayaBlackIntro</string>
			<key>Title</key>
			<string>Maya Black Intro</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_convertToInstallment</string>
			<key>Title</key>
			<string>Convert to Installment</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>Title</key>
			<string>Helix</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_mockCMSV8</string>
			<key>Title</key>
			<string>KYC CMS v8 mock</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>Title</key>
			<string>Anbu</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_autoDebitArrangementSdk</string>
			<key>Title</key>
			<string>Auto Debit Arrangement SDK</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_loadMinDetection</string>
			<key>Title</key>
			<string>Load SDK MIN Detection</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>Title</key>
			<string>Rubeus</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_mgmV2</string>
			<key>Title</key>
			<string>MGM 2.0</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>Title</key>
			<string>Inbox</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_inboxTicketing</string>
			<key>Title</key>
			<string>Inbox Ticketing</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
	</array>
	<key>StringsTable</key>
	<string>Root</string>
</dict>
</plist>
