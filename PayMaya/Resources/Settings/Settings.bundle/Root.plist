<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>PreferenceSpecifiers</key>
	<array>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>firebase_debug_view_enabled</string>
			<key>Title</key>
			<string>Firebase DebugView</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>debug_swift_network_enabled</string>
			<key>Title</key>
			<string>DebugSwift Network</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>MockLocation</string>
			<key>Title</key>
			<string>Mock Location</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>Title</key>
			<string>Feature Flags</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>configuration_service_logs_enabled</string>
			<key>Title</key>
			<string>Feature Flag Logs</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Maintenance</string>
			<key>Title</key>
			<string>Maintenance</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Toggles</string>
			<key>Title</key>
			<string>v1 (S3 + Remote Config)</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>SplitToggles</string>
			<key>Title</key>
			<string>v2 (Split)</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
	</array>
	<key>StringsTable</key>
	<string>Root</string>
</dict>
</plist>
