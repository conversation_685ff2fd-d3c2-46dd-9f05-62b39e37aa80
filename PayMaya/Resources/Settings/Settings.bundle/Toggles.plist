<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>PreferenceSpecifiers</key>
	<array>
		<dict>
			<key>Title</key>
			<string>Yasha</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_kycPhase1</string>
			<key>Title</key>
			<string>kycPhase1</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>Title</key>
			<string>Helix</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_reKYC10MinutesTest</string>
			<key>Title</key>
			<string>reKYC10MinutesTest</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_showIDValScore</string>
			<key>Title</key>
			<string>showIDValScore</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_showIDQualityScore</string>
			<key>Title</key>
			<string>showIDQualityScore</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_kycPersonalInfoV2</string>
			<key>Title</key>
			<string>kycPersonalInfoV2</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>Title</key>
			<string>EKYC-Zoloz</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_mockedPreSubmissionResuestData</string>
			<key>Title</key>
			<string>mockedPreSubmissionResuestData</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_skipZolozFlow</string>
			<key>Title</key>
			<string>skipZolozFlow</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>Title</key>
			<string>Velaryon</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_mayaCreditAccountSummaryAdd</string>
			<key>Title</key>
			<string>mayaCreditAccountSummaryAdd</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_mayaCreditPartnerMerchant</string>
			<key>Title</key>
			<string>mayaCreditPartnerMerchant</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>Title</key>
			<string>CHARMANDER</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_walletTransactionLimitWalletSettingsOption</string>
			<key>Title</key>
			<string>walletTransactionLimitWalletSettingsOption</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_instaFillWalletSettingsOption</string>
			<key>Title</key>
			<string>instaFillWalletSettingsOption</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>Title</key>
			<string>BFEAT</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_mayaPayBillsParsePaymentMethods</string>
			<key>Title</key>
			<string>mayaPayBillsParsePaymentMethods</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_billsPayInitialReceiptEntryPoint</string>
			<key>Title</key>
			<string>billsPayInitialReceiptEntryPoint</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
		<dict>
			<key>Title</key>
			<string>ECOMM</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>DefaultValue</key>
			<false/>
			<key>Key</key>
			<string>non_prod_freelancerHub</string>
			<key>Title</key>
			<string>freelancerHub</string>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
		</dict>
	</array>
	<key>StringsTable</key>
	<string>Root</string>
</dict>
</plist>
