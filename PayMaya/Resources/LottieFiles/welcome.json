{"nm": "Podium 1", "h": 445, "w": 445, "meta": {"g": "LottieFiles Figma v83"}, "layers": [{"ty": 4, "nm": "Fund your next big life move with Personal Loans", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [100.5, 39]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [222.5, 239]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 540}, {"s": [0], "t": 660}]}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.07, 21], [4.07, 4.22], [13.7, 4.22], [13.7, 6.62], [6.81, 6.62], [6.81, 11.42], [12.81, 11.42], [12.81, 13.8], [6.81, 13.8], [6.81, 21], [4.07, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.66], [0, 0], [0, 0], [0, 0], [-1.54, 0], [-0.02, 2.35], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.63, 0]], "o": [[-2.9, 0], [0, 0], [0, 0], [0, 0], [0, 1.78], [1.66, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.82, 1.08], [0, 0]], "v": [[19.08, 21.24], [14.64, 17.02], [14.64, 9.14], [17.25, 9.14], [17.25, 16.51], [19.84, 19.13], [22.84, 15.77], [22.84, 9.14], [25.46, 9.14], [25.46, 21], [22.84, 21], [22.84, 19.51], [19.08, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.54, 0], [0.19, -2.02], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.61, 0], [0, -2.66], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.78], [-1.54, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.08], [2.9, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36.11, 13.63], [33.52, 10.99], [30.54, 13.87], [30.54, 21], [27.93, 21], [27.93, 9.14], [30.54, 9.14], [30.54, 10.61], [34.29, 8.9], [38.73, 13.13], [38.73, 21], [36.11, 21], [36.11, 13.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.82], [-3.22, 0], [-0.74, -1.32], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.78, 0]], "o": [[-3.22, 0], [0, -3.79], [1.78, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.74, 1.32], [0, 0]], "v": [[46.26, 21.24], [40.52, 15.07], [46.26, 8.9], [50.32, 10.99], [50.32, 3], [52.93, 3], [52.93, 21], [50.32, 21], [50.32, 19.18], [46.26, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.11, 0], [0, 2.45], [2.09, 0], [0, -2.4]], "o": [[0, 2.45], [2.09, 0], [0, -2.4], [-2.11, 0], [0, 0]], "v": [[43.14, 15.07], [46.79, 19.06], [50.41, 15.07], [46.79, 11.09], [43.14, 15.07]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[61.84, 26.83], [64.58, 20.54], [59.73, 9.14], [62.56, 9.14], [65.92, 17.9], [69.31, 9.14], [72.16, 9.14], [64.7, 26.83], [61.84, 26.83]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[78.66, 21.24], [72.35, 15.05], [78.66, 8.9], [84.95, 15.05], [78.66, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[74.97, 15.05], [78.66, 19.13], [82.36, 15.05], [78.66, 10.99], [74.97, 15.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.66], [0, 0], [0, 0], [0, 0], [-1.54, 0], [-0.02, 2.35], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.63, 0]], "o": [[-2.9, 0], [0, 0], [0, 0], [0, 0], [0, 1.78], [1.66, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.82, 1.08], [0, 0]], "v": [[91.17, 21.24], [86.73, 17.02], [86.73, 9.14], [89.35, 9.14], [89.35, 16.51], [91.94, 19.13], [94.94, 15.77], [94.94, 9.14], [97.55, 9.14], [97.55, 21], [94.94, 21], [94.94, 19.51], [91.17, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[100, 21], [100, 9.14], [102.61, 9.14], [102.61, 11.11], [106.91, 8.9], [106.91, 11.33], [102.61, 15.48], [102.61, 21], [100, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.54, 0], [0.19, -2.02], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.61, 0], [0, -2.66], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.78], [-1.54, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.08], [2.9, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[121.99, 13.63], [119.4, 10.99], [116.42, 13.87], [116.42, 21], [113.8, 21], [113.8, 9.14], [116.42, 9.14], [116.42, 10.61], [120.16, 8.9], [124.6, 13.13], [124.6, 21], [121.99, 21], [121.99, 13.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[132.59, 21.24], [126.4, 15.07], [132.52, 8.9], [138.42, 15], [138.4, 15.6], [128.97, 15.6], [132.59, 19.18], [135.69, 17.23], [138.09, 17.59], [132.59, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[129.09, 13.63], [135.78, 13.63], [132.54, 10.97], [129.09, 13.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[138.53, 21], [143.47, 15], [138.67, 9.14], [141.74, 9.14], [145.08, 13.22], [148.39, 9.14], [151.49, 9.14], [146.69, 15], [151.61, 21], [148.39, 21], [145.08, 16.94], [141.74, 21], [138.53, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[157.77, 21], [154.31, 17.47], [154.31, 11.16], [151.93, 11.16], [151.93, 9.14], [153.06, 9.14], [154.6, 7.34], [154.6, 5.26], [156.93, 5.26], [156.93, 9.14], [159.66, 9.14], [159.66, 11.16], [156.93, 11.16], [156.93, 17.09], [158.63, 18.89], [159.66, 18.89], [159.66, 21], [157.77, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.74, 1.32], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.78, 0], [0, -3.79], [3.22, 0]], "o": [[-1.78, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.74, -1.32], [3.22, 0], [0, 3.82], [0, 0]], "v": [[173.59, 21.24], [169.53, 19.18], [169.53, 21], [166.91, 21], [166.91, 3], [169.53, 3], [169.53, 10.99], [173.59, 8.9], [179.32, 15.07], [173.59, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.09, 0], [0, 2.45], [2.11, 0], [0, -2.4]], "o": [[0, 2.45], [2.11, 0], [0, -2.4], [-2.09, 0], [0, 0]], "v": [[169.43, 15.07], [173.06, 19.06], [176.71, 15.07], [173.06, 11.09], [169.43, 15.07]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[181.21, 21], [181.21, 9.14], [183.83, 9.14], [183.83, 21], [181.21, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.89, 0], [0, -0.89], [0.89, 0], [0, 0.89]], "o": [[0, -0.89], [0.89, 0], [0, 0.89], [-0.89, 0], [0, 0]], "v": [[180.87, 5.16], [182.51, 3.55], [184.14, 5.16], [182.51, 6.79], [180.87, 5.16]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.82, 2.23], [0, 0], [-1.75, 0], [0, 2.42], [0, 0], [1.82, 0], [0, 3.6], [-3.07, 0], [-0.72, -1.25], [0, 0], [0, 0], [0, 0], [3.7, 0]], "o": [[-3.19, 0], [0, 0], [0.7, 1.66], [2.38, 0], [0, 0], [-0.7, 1.22], [-3.05, 0], [0, -3.58], [1.85, 0], [0, 0], [0, 0], [0, 0], [0, 4.25], [0, 0]], "v": [[191.79, 27], [185.89, 23.3], [188.07, 22.39], [191.7, 24.96], [195.2, 21], [195.2, 18.77], [191.17, 20.83], [185.67, 14.83], [191.19, 8.9], [195.2, 10.99], [195.2, 9.14], [197.82, 9.14], [197.82, 20.81], [191.79, 27]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.06, 0], [0, 2.26], [2.11, 0], [0, -2.23]], "o": [[0, 2.26], [2.09, 0], [0, -2.23], [-2.06, 0], [0, 0]], "v": [[188.19, 14.83], [191.77, 18.7], [195.37, 14.83], [191.77, 10.99], [188.19, 14.83]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[28.43, 47], [28.43, 29], [31.05, 29], [31.05, 47], [28.43, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[33.47, 47], [33.47, 35.14], [36.09, 35.14], [36.09, 47], [33.47, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.89, 0], [0, -0.89], [0.89, 0], [0, 0.89]], "o": [[0, -0.89], [0.89, 0], [0, 0.89], [-0.89, 0], [0, 0]], "v": [[33.13, 31.16], [34.77, 29.55], [36.4, 31.16], [34.77, 32.79], [33.13, 31.16]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.88, 0], [0, 0], [0, 0], [0, 0], [0, -1.92], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, -3.53], [0, 0], [0, 0], [0, 0], [-1.06, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[39.85, 33.44], [44.63, 29], [45.25, 29], [45.25, 31.09], [44.63, 31.09], [42.47, 33.44], [42.47, 35.14], [45.25, 35.14], [45.25, 37.16], [42.47, 37.16], [42.47, 47], [39.85, 47], [39.85, 37.16], [37.43, 37.16], [37.43, 35.14], [39.85, 35.14], [39.85, 33.44]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[51.93, 47.24], [45.74, 41.07], [51.86, 34.9], [57.76, 41], [57.74, 41.6], [48.31, 41.6], [51.93, 45.18], [55.03, 43.23], [57.43, 43.59], [51.93, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[48.43, 39.63], [55.12, 39.63], [51.88, 36.97], [48.43, 39.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.32, 0], [0, -2.26], [0, 0], [0, 0], [0, 0], [1.32, 0], [0.17, -1.82], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.46, 0], [-0.62, -1.1], [-1.66, 0], [0, -2.23], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.51], [-1.42, 0], [0, 0], [0, 0], [0, 0], [0, -1.51], [-1.3, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.74, -1.03], [1.7, 0], [0.84, -1.13], [2.5, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[79.99, 39.32], [77.73, 36.99], [75.19, 40.14], [75.19, 47], [72.57, 47], [72.57, 39.32], [70.29, 36.99], [67.77, 39.58], [67.77, 47], [65.16, 47], [65.16, 35.14], [67.77, 35.14], [67.77, 36.54], [71.18, 34.9], [74.76, 36.66], [78.6, 34.9], [82.61, 38.48], [82.61, 47], [79.99, 47], [79.99, 39.32]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[90.7, 47.24], [84.39, 41.05], [90.7, 34.9], [96.99, 41.05], [90.7, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[87, 41.05], [90.7, 45.13], [94.4, 41.05], [90.7, 36.99], [87, 41.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[102.26, 47], [97.22, 35.14], [100.05, 35.14], [103.43, 43.93], [106.79, 35.14], [109.65, 35.14], [104.61, 47], [102.26, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[116.06, 47.24], [109.87, 41.07], [115.99, 34.9], [121.89, 41], [121.87, 41.6], [112.43, 41.6], [116.06, 45.18], [119.15, 43.23], [121.55, 43.59], [116.06, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[112.55, 39.63], [119.25, 39.63], [116.01, 36.97], [112.55, 39.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[132.38, 47], [128.18, 35.14], [130.96, 35.14], [133.87, 43.9], [136.89, 35.14], [139.1, 35.14], [142.12, 43.9], [145.03, 35.14], [147.79, 35.14], [143.61, 47], [140.83, 47], [138, 38.29], [135.16, 47], [132.38, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[149.16, 47], [149.16, 35.14], [151.77, 35.14], [151.77, 47], [149.16, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.89, 0], [0, -0.89], [0.89, 0], [0, 0.89]], "o": [[0, -0.89], [0.89, 0], [0, 0.89], [-0.89, 0], [0, 0]], "v": [[148.82, 31.16], [150.45, 29.55], [152.09, 31.16], [150.45, 32.79], [148.82, 31.16]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[158.95, 47], [155.49, 43.47], [155.49, 37.16], [153.12, 37.16], [153.12, 35.14], [154.25, 35.14], [155.78, 33.34], [155.78, 31.26], [158.11, 31.26], [158.11, 35.14], [160.85, 35.14], [160.85, 37.16], [158.11, 37.16], [158.11, 43.09], [159.81, 44.89], [160.85, 44.89], [160.85, 47], [158.95, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.58, 0], [0.02, -2.4], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.73, 0], [0, -2.93], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.92], [-1.56, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.13], [2.76, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[170.8, 39.78], [168.18, 36.99], [165.2, 40.35], [165.2, 47], [162.59, 47], [162.59, 29], [165.2, 29], [165.2, 36.73], [169.12, 34.9], [173.41, 39.39], [173.41, 47], [170.8, 47], [170.8, 39.78]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [-1.03, -0.82], [0, -1.8], [1.66, -0.91], [1.25, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [2.28, 0], [1.03, 0.84], [0, 2.11], [-1.18, 0.62], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[23.81, 73], [23.81, 56.22], [29.16, 56.22], [33.86, 57.42], [35.49, 61.41], [33.05, 66.21], [29.16, 66.9], [26.52, 66.9], [26.52, 73], [23.81, 73]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-0.65, 0.43], [0, 1.22], [0.65, 0.48], [1.15, 0], [0, 0], [0, 0]], "o": [[0, 0], [1.03, 0], [0.79, -0.48], [0, -1.13], [-0.72, -0.5], [0, 0], [0, 0], [0, 0]], "v": [[26.52, 64.6], [28.8, 64.6], [31.58, 64.1], [32.78, 61.55], [31.75, 59.13], [28.8, 58.5], [26.52, 58.5], [26.52, 64.6]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[42.4, 73.24], [36.21, 67.07], [42.33, 60.9], [48.24, 67], [48.21, 67.6], [38.78, 67.6], [42.4, 71.18], [45.5, 69.23], [47.9, 69.59], [42.4, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[38.9, 65.63], [45.6, 65.63], [42.36, 62.97], [38.9, 65.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[50.12, 73], [50.12, 61.14], [52.74, 61.14], [52.74, 63.11], [57.04, 60.9], [57.04, 63.33], [52.74, 67.48], [52.74, 73], [50.12, 73]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.36, 1.94], [0, 0], [-1.54, 0], [0, 0.96], [1.63, 0.34], [0, 2.52], [-2.81, 0], [-0.53, -1.87], [0, 0], [1.13, 0], [0, -0.84], [-1.49, -0.29], [0, -2.64], [2.95, 0]], "o": [[-2.86, 0], [0, 0], [0.41, 1.27], [1.25, 0], [0, -1.13], [-1.9, -0.38], [0, -2.02], [2.5, 0], [0, 0], [-0.38, -0.98], [-1.2, 0], [0, 1.01], [1.92, 0.43], [0, 2.3], [0, 0]], "v": [[62.7, 73.24], [57.61, 69.86], [59.77, 69.28], [62.84, 71.27], [64.95, 69.74], [62.15, 68.01], [57.9, 64.38], [62.39, 60.9], [67.21, 63.83], [64.93, 64.43], [62.41, 62.87], [60.35, 64.19], [63.08, 65.8], [67.38, 69.47], [62.7, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[75.08, 73.24], [68.77, 67.05], [75.08, 60.9], [81.37, 67.05], [75.08, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[71.38, 67.05], [75.08, 71.13], [78.77, 67.05], [75.08, 62.99], [71.38, 67.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.54, 0], [0.19, -2.02], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.61, 0], [0, -2.66], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.78], [-1.54, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.08], [2.9, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[91.47, 65.63], [88.88, 62.99], [85.9, 65.87], [85.9, 73], [83.29, 73], [83.29, 61.14], [85.9, 61.14], [85.9, 62.61], [89.65, 60.9], [94.09, 65.13], [94.09, 73], [91.47, 73], [91.47, 65.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.26], [-2.54, 0.48], [0, 0], [0, 0.5], [1.34, 0], [0.17, -1.2], [0, 0], [-3.02, 0], [-0.5, -1.9], [0, -0.77], [0, 0], [-0.62, 0], [0, 0], [0, 0], [0, 0], [0, 1.18], [0, 0], [0, 0], [2.09, 0]], "o": [[-2.02, 0], [0, -2.4], [0, 0], [0.5, -0.1], [0, -1.39], [-1.63, 0], [0, 0], [0.41, -2.33], [2.59, 0], [0.14, 0.41], [0, 0], [0, 0.82], [0, 0], [0, 0], [0, 0], [-1.22, 0], [0, 0], [0, 0], [-0.74, 1.34], [0, 0]], "v": [[99.87, 73.24], [95.91, 70.02], [100.47, 66.21], [102.6, 65.8], [103.59, 65.01], [101.33, 62.97], [98.62, 65.01], [96.05, 64.65], [101.28, 60.9], [106.01, 63.81], [106.2, 65.49], [106.2, 69.81], [107.07, 70.94], [107.5, 70.94], [107.5, 73], [106.28, 73], [104.19, 71.32], [104.19, 71.06], [104, 71.06], [99.87, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.13, 0], [0, 2.35], [0, 0], [0, -0.96]], "o": [[0, 0.86], [2.02, 0], [0, 0], [-1.34, 0.24], [0, 0]], "v": [[98.52, 69.81], [100.3, 71.2], [103.59, 67.36], [100.59, 68.08], [98.52, 69.81]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[108.98, 73], [108.98, 55], [111.59, 55], [111.59, 73], [108.98, 73]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[119.67, 73], [119.67, 56.22], [122.4, 56.22], [122.4, 70.6], [128.14, 70.6], [128.14, 73], [119.67, 73]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[134.75, 73.24], [128.44, 67.05], [134.75, 60.9], [141.04, 67.05], [134.75, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[131.05, 67.05], [134.75, 71.13], [138.45, 67.05], [134.75, 62.99], [131.05, 67.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.26], [-2.54, 0.48], [0, 0], [0, 0.5], [1.34, 0], [0.17, -1.2], [0, 0], [-3.02, 0], [-0.5, -1.9], [0, -0.77], [0, 0], [-0.62, 0], [0, 0], [0, 0], [0, 0], [0, 1.18], [0, 0], [0, 0], [2.09, 0]], "o": [[-2.02, 0], [0, -2.4], [0, 0], [0.5, -0.1], [0, -1.39], [-1.63, 0], [0, 0], [0.41, -2.33], [2.59, 0], [0.14, 0.41], [0, 0], [0, 0.82], [0, 0], [0, 0], [0, 0], [-1.22, 0], [0, 0], [0, 0], [-0.74, 1.34], [0, 0]], "v": [[146.39, 73.24], [142.43, 70.02], [146.99, 66.21], [149.13, 65.8], [150.11, 65.01], [147.86, 62.97], [145.14, 65.01], [142.58, 64.65], [147.81, 60.9], [152.54, 63.81], [152.73, 65.49], [152.73, 69.81], [153.59, 70.94], [154.02, 70.94], [154.02, 73], [152.8, 73], [150.71, 71.32], [150.71, 71.06], [150.52, 71.06], [146.39, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.13, 0], [0, 2.35], [0, 0], [0, -0.96]], "o": [[0, 0.86], [2.02, 0], [0, 0], [-1.34, 0.24], [0, 0]], "v": [[145.05, 69.81], [146.82, 71.2], [150.11, 67.36], [147.11, 68.08], [145.05, 69.81]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.54, 0], [0.19, -2.02], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.61, 0], [0, -2.66], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.78], [-1.54, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.08], [2.9, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[163.71, 65.63], [161.11, 62.99], [158.14, 65.87], [158.14, 73], [155.52, 73], [155.52, 61.14], [158.14, 61.14], [158.14, 62.61], [161.88, 60.9], [166.32, 65.13], [166.32, 73], [163.71, 73], [163.71, 65.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.36, 1.94], [0, 0], [-1.54, 0], [0, 0.96], [1.63, 0.34], [0, 2.52], [-2.81, 0], [-0.53, -1.87], [0, 0], [1.13, 0], [0, -0.84], [-1.49, -0.29], [0, -2.64], [2.95, 0]], "o": [[-2.85, 0], [0, 0], [0.41, 1.27], [1.25, 0], [0, -1.13], [-1.9, -0.38], [0, -2.02], [2.5, 0], [0, 0], [-0.38, -0.98], [-1.2, 0], [0, 1.01], [1.92, 0.43], [0, 2.3], [0, 0]], "v": [[173.16, 73.24], [168.07, 69.86], [170.23, 69.28], [173.3, 71.27], [175.42, 69.74], [172.61, 68.01], [168.36, 64.38], [172.85, 60.9], [177.67, 63.83], [175.39, 64.43], [172.87, 62.87], [170.81, 64.19], [173.54, 65.8], [177.84, 69.47], [173.16, 73.24]]}}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 1}, {"ty": 4, "nm": "Supercharge", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [120, 26]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [223, 239]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 420}, {"s": [0], "t": 540}]}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.55, 3.05], [0, 0], [-2.14, 0], [0, 1.61], [2.14, 0.55], [0, 3.34], [-3.46, 0], [-0.77, -3.1], [0, 0], [2.04, 0], [0, -1.51], [-2.04, -0.5], [0, -3.58], [3.72, 0]], "o": [[-3.55, 0], [0, 0], [0.53, 2.09], [2.09, 0], [0, -1.82], [-2.57, -0.7], [0, -2.93], [3.02, 0], [0, 0], [-0.62, -1.85], [-1.92, 0], [0, 1.68], [2.64, 0.65], [0, 3.05], [0, 0]], "v": [[32.68, 21.31], [25.87, 16.54], [28.55, 15.96], [32.71, 19.06], [35.99, 16.49], [31.96, 13.63], [26.42, 8.76], [32.23, 3.91], [38.66, 8.26], [36.21, 8.83], [32.37, 6.17], [29.18, 8.62], [33.02, 11.23], [38.83, 16.3], [32.68, 21.31]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.66], [0, 0], [0, 0], [0, 0], [-1.54, 0], [-0.02, 2.35], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.63, 0]], "o": [[-2.9, 0], [0, 0], [0, 0], [0, 0], [0, 1.78], [1.66, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.82, 1.08], [0, 0]], "v": [[45.04, 21.24], [40.6, 17.02], [40.6, 9.14], [43.22, 9.14], [43.22, 16.51], [45.81, 19.13], [48.81, 15.77], [48.81, 9.14], [51.43, 9.14], [51.43, 21], [48.81, 21], [48.81, 19.51], [45.04, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-1.8, 0], [0, -3.79], [3.22, 0], [0.74, 1.32], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.74, -1.3], [3.22, 0], [0, 3.79], [-1.8, 0], [0, 0], [0, 0], [0, 0]], "v": [[53.87, 26.83], [53.87, 9.14], [56.49, 9.14], [56.49, 10.94], [60.55, 8.9], [66.28, 15.05], [60.55, 21.24], [56.49, 19.15], [56.49, 26.83], [53.87, 26.83]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.09, 0], [0, 2.42], [2.09, 0], [0, -2.42]], "o": [[0, 2.42], [2.09, 0], [0, -2.42], [-2.09, 0], [0, 0]], "v": [[56.39, 15.05], [60.02, 19.06], [63.67, 15.05], [60.02, 11.09], [56.39, 15.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[73.86, 21.24], [67.67, 15.07], [73.79, 8.9], [79.69, 15], [79.67, 15.6], [70.23, 15.6], [73.86, 19.18], [76.95, 17.23], [79.35, 17.59], [73.86, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[70.35, 13.63], [77.05, 13.63], [73.81, 10.97], [70.35, 13.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[81.58, 21], [81.58, 9.14], [84.19, 9.14], [84.19, 11.11], [88.49, 8.9], [88.49, 11.33], [84.19, 15.48], [84.19, 21], [81.58, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.65], [-3.65, 0], [-0.62, -2.66], [0, 0], [1.66, 0], [0, -2.38], [-2.14, 0], [-0.46, 1.56], [0, 0], [2.86, 0]], "o": [[-3.65, 0], [0, -3.62], [2.9, 0], [0, 0], [-0.46, -1.56], [-2.14, 0], [0, 2.42], [1.66, 0], [0, 0], [-0.62, 2.66], [0, 0]], "v": [[95, 21.24], [88.78, 15.05], [95, 8.9], [100.69, 13.1], [98.26, 13.37], [94.97, 10.97], [91.4, 15.05], [94.97, 19.18], [98.26, 16.75], [100.69, 17.02], [95, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.58, 0], [0.02, -2.4], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.73, 0], [0, -2.93], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.92], [-1.56, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.13], [2.76, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[110.69, 13.78], [108.07, 10.99], [105.1, 14.35], [105.1, 21], [102.48, 21], [102.48, 3], [105.1, 3], [105.1, 10.73], [109.01, 8.9], [113.31, 13.39], [113.31, 21], [110.69, 21], [110.69, 13.78]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.26], [-2.54, 0.48], [0, 0], [0, 0.5], [1.34, 0], [0.17, -1.2], [0, 0], [-3.02, 0], [-0.5, -1.9], [0, -0.77], [0, 0], [-0.62, 0], [0, 0], [0, 0], [0, 0], [0, 1.18], [0, 0], [0, 0], [2.09, 0]], "o": [[-2.02, 0], [0, -2.4], [0, 0], [0.5, -0.1], [0, -1.39], [-1.63, 0], [0, 0], [0.41, -2.33], [2.59, 0], [0.14, 0.41], [0, 0], [0, 0.82], [0, 0], [0, 0], [0, 0], [-1.22, 0], [0, 0], [0, 0], [-0.74, 1.34], [0, 0]], "v": [[119.09, 21.24], [115.13, 18.02], [119.69, 14.21], [121.82, 13.8], [122.81, 13.01], [120.55, 10.97], [117.84, 13.01], [115.27, 12.65], [120.5, 8.9], [125.23, 11.81], [125.42, 13.49], [125.42, 17.81], [126.29, 18.94], [126.72, 18.94], [126.72, 21], [125.5, 21], [123.41, 19.32], [123.41, 19.06], [123.22, 19.06], [119.09, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.13, 0], [0, 2.35], [0, 0], [0, -0.96]], "o": [[0, 0.86], [2.02, 0], [0, 0], [-1.34, 0.24], [0, 0]], "v": [[117.74, 17.81], [119.52, 19.2], [122.81, 15.36], [119.81, 16.08], [117.74, 17.81]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[128.19, 21], [128.19, 9.14], [130.81, 9.14], [130.81, 11.11], [135.1, 8.9], [135.1, 11.33], [130.81, 15.48], [130.81, 21], [128.19, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.82, 2.23], [0, 0], [-1.75, 0], [0, 2.42], [0, 0], [1.82, 0], [0, 3.6], [-3.07, 0], [-0.72, -1.25], [0, 0], [0, 0], [0, 0], [3.7, 0]], "o": [[-3.19, 0], [0, 0], [0.7, 1.66], [2.38, 0], [0, 0], [-0.7, 1.22], [-3.05, 0], [0, -3.58], [1.85, 0], [0, 0], [0, 0], [0, 0], [0, 4.25], [0, 0]], "v": [[141.52, 27], [135.62, 23.3], [137.8, 22.39], [141.42, 24.96], [144.93, 21], [144.93, 18.77], [140.9, 20.83], [135.4, 14.83], [140.92, 8.9], [144.93, 10.99], [144.93, 9.14], [147.54, 9.14], [147.54, 20.81], [141.52, 27]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.06, 0], [0, 2.26], [2.11, 0], [0, -2.23]], "o": [[0, 2.26], [2.09, 0], [0, -2.23], [-2.06, 0], [0, 0]], "v": [[137.92, 14.83], [141.5, 18.7], [145.1, 14.83], [141.5, 10.99], [137.92, 14.83]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[155.65, 21.24], [149.46, 15.07], [155.58, 8.9], [161.49, 15], [161.46, 15.6], [152.03, 15.6], [155.65, 19.18], [158.75, 17.23], [161.15, 17.59], [155.65, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[152.15, 13.63], [158.85, 13.63], [155.61, 10.97], [152.15, 13.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[169.84, 26.83], [172.58, 20.54], [167.73, 9.14], [170.56, 9.14], [173.92, 17.9], [177.31, 9.14], [180.16, 9.14], [172.7, 26.83], [169.84, 26.83]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[186.66, 21.24], [180.35, 15.05], [186.66, 8.9], [192.95, 15.05], [186.66, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[182.97, 15.05], [186.66, 19.13], [190.36, 15.05], [186.66, 10.99], [182.97, 15.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.66], [0, 0], [0, 0], [0, 0], [-1.54, 0], [-0.02, 2.35], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.63, 0]], "o": [[-2.9, 0], [0, 0], [0, 0], [0, 0], [0, 1.78], [1.66, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.82, 1.08], [0, 0]], "v": [[199.17, 21.24], [194.73, 17.02], [194.73, 9.14], [197.35, 9.14], [197.35, 16.51], [199.94, 19.13], [202.94, 15.77], [202.94, 9.14], [205.55, 9.14], [205.55, 21], [202.94, 21], [202.94, 19.51], [199.17, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[208, 21], [208, 9.14], [210.61, 9.14], [210.61, 11.11], [214.91, 8.9], [214.91, 11.33], [210.61, 15.48], [210.61, 21], [208, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.55, 3.05], [0, 0], [-2.14, 0], [0, 1.61], [2.14, 0.55], [0, 3.34], [-3.46, 0], [-0.77, -3.1], [0, 0], [2.04, 0], [0, -1.51], [-2.04, -0.5], [0, -3.58], [3.72, 0]], "o": [[-3.55, 0], [0, 0], [0.53, 2.09], [2.09, 0], [0, -1.82], [-2.57, -0.7], [0, -2.93], [3.02, 0], [0, 0], [-0.62, -1.85], [-1.92, 0], [0, 1.68], [2.64, 0.65], [0, 3.05], [0, 0]], "v": [[10.56, 47.31], [3.74, 42.54], [6.43, 41.96], [10.58, 45.06], [13.87, 42.49], [9.84, 39.63], [4.29, 34.76], [10.1, 29.91], [16.53, 34.26], [14.08, 34.83], [10.24, 32.17], [7.05, 34.62], [10.89, 37.23], [16.7, 42.3], [10.56, 47.31]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.26], [-2.54, 0.48], [0, 0], [0, 0.5], [1.34, 0], [0.17, -1.2], [0, 0], [-3.02, 0], [-0.5, -1.9], [0, -0.77], [0, 0], [-0.62, 0], [0, 0], [0, 0], [0, 0], [0, 1.18], [0, 0], [0, 0], [2.09, 0]], "o": [[-2.02, 0], [0, -2.4], [0, 0], [0.5, -0.1], [0, -1.39], [-1.63, 0], [0, 0], [0.41, -2.33], [2.59, 0], [0.14, 0.41], [0, 0], [0, 0.82], [0, 0], [0, 0], [0, 0], [-1.22, 0], [0, 0], [0, 0], [-0.74, 1.34], [0, 0]], "v": [[22.06, 47.24], [18.1, 44.02], [22.66, 40.21], [24.79, 39.8], [25.78, 39.01], [23.52, 36.97], [20.81, 39.01], [18.24, 38.65], [23.47, 34.9], [28.2, 37.81], [28.39, 39.49], [28.39, 43.81], [29.26, 44.94], [29.69, 44.94], [29.69, 47], [28.46, 47], [26.38, 45.32], [26.38, 45.06], [26.18, 45.06], [22.06, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.13, 0], [0, 2.35], [0, 0], [0, -0.96]], "o": [[0, 0.86], [2.02, 0], [0, 0], [-1.34, 0.24], [0, 0]], "v": [[20.71, 43.81], [22.49, 45.2], [25.78, 41.36], [22.78, 42.08], [20.71, 43.81]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[34.37, 47], [29.33, 35.14], [32.16, 35.14], [35.55, 43.93], [38.91, 35.14], [41.76, 35.14], [36.72, 47], [34.37, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[43.04, 47], [43.04, 35.14], [45.66, 35.14], [45.66, 47], [43.04, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.89, 0], [0, -0.89], [0.89, 0], [0, 0.89]], "o": [[0, -0.89], [0.89, 0], [0, 0.89], [-0.89, 0], [0, 0]], "v": [[42.71, 31.16], [44.34, 29.55], [45.97, 31.16], [44.34, 32.79], [42.71, 31.16]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.54, 0], [0.19, -2.02], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.61, 0], [0, -2.66], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.78], [-1.54, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.08], [2.9, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[56.27, 39.63], [53.68, 36.99], [50.7, 39.87], [50.7, 47], [48.08, 47], [48.08, 35.14], [50.7, 35.14], [50.7, 36.61], [54.44, 34.9], [58.88, 39.13], [58.88, 47], [56.27, 47], [56.27, 39.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.82, 2.23], [0, 0], [-1.75, 0], [0, 2.42], [0, 0], [1.82, 0], [0, 3.6], [-3.07, 0], [-0.72, -1.25], [0, 0], [0, 0], [0, 0], [3.7, 0]], "o": [[-3.19, 0], [0, 0], [0.7, 1.66], [2.38, 0], [0, 0], [-0.7, 1.22], [-3.05, 0], [0, -3.58], [1.85, 0], [0, 0], [0, 0], [0, 0], [0, 4.25], [0, 0]], "v": [[66.78, 53], [60.87, 49.3], [63.06, 48.39], [66.68, 50.96], [70.19, 47], [70.19, 44.77], [66.15, 46.83], [60.66, 40.83], [66.18, 34.9], [70.19, 36.99], [70.19, 35.14], [72.8, 35.14], [72.8, 46.81], [66.78, 53]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.06, 0], [0, 2.26], [2.11, 0], [0, -2.23]], "o": [[0, 2.26], [2.09, 0], [0, -2.23], [-2.06, 0], [0, 0]], "v": [[63.18, 40.83], [66.75, 44.7], [70.35, 40.83], [66.75, 36.99], [63.18, 40.83]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.36, 1.94], [0, 0], [-1.54, 0], [0, 0.96], [1.63, 0.34], [0, 2.52], [-2.81, 0], [-0.53, -1.87], [0, 0], [1.13, 0], [0, -0.84], [-1.49, -0.29], [0, -2.64], [2.95, 0]], "o": [[-2.86, 0], [0, 0], [0.41, 1.27], [1.25, 0], [0, -1.13], [-1.9, -0.38], [0, -2.02], [2.5, 0], [0, 0], [-0.38, -0.98], [-1.2, 0], [0, 1.01], [1.92, 0.43], [0, 2.3], [0, 0]], "v": [[79.76, 47.24], [74.67, 43.86], [76.83, 43.28], [79.9, 45.27], [82.02, 43.74], [79.21, 42.01], [74.96, 38.38], [79.45, 34.9], [84.27, 37.83], [81.99, 38.43], [79.47, 36.87], [77.41, 38.19], [80.14, 39.8], [84.44, 43.47], [79.76, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[94.99, 47], [90.79, 35.14], [93.57, 35.14], [96.47, 43.9], [99.5, 35.14], [101.71, 35.14], [104.73, 43.9], [107.63, 35.14], [110.39, 35.14], [106.22, 47], [103.43, 47], [100.6, 38.29], [97.77, 47], [94.99, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[111.76, 47], [111.76, 35.14], [114.38, 35.14], [114.38, 47], [111.76, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.89, 0], [0, -0.89], [0.89, 0], [0, 0.89]], "o": [[0, -0.89], [0.89, 0], [0, 0.89], [-0.89, 0], [0, 0]], "v": [[111.43, 31.16], [113.06, 29.55], [114.69, 31.16], [113.06, 32.79], [111.43, 31.16]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[121.56, 47], [118.1, 43.47], [118.1, 37.16], [115.72, 37.16], [115.72, 35.14], [116.85, 35.14], [118.39, 33.34], [118.39, 31.26], [120.72, 31.26], [120.72, 35.14], [123.45, 35.14], [123.45, 37.16], [120.72, 37.16], [120.72, 43.09], [122.42, 44.89], [123.45, 44.89], [123.45, 47], [121.56, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.58, 0], [0.02, -2.4], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.73, 0], [0, -2.93], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.92], [-1.56, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.13], [2.76, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[133.4, 39.78], [130.79, 36.99], [127.81, 40.35], [127.81, 47], [125.19, 47], [125.19, 29], [127.81, 29], [127.81, 36.73], [131.72, 34.9], [136.02, 39.39], [136.02, 47], [133.4, 47], [133.4, 39.78]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.74, 1.32], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.78, 0], [0, -3.79], [3.22, 0]], "o": [[-1.78, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.74, -1.32], [3.22, 0], [0, 3.82], [0, 0]], "v": [[150.52, 47.24], [146.47, 45.18], [146.47, 47], [143.85, 47], [143.85, 29], [146.47, 29], [146.47, 36.99], [150.52, 34.9], [156.26, 41.07], [150.52, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.09, 0], [0, 2.45], [2.11, 0], [0, -2.4]], "o": [[0, 2.45], [2.11, 0], [0, -2.4], [-2.09, 0], [0, 0]], "v": [[146.37, 41.07], [149.99, 45.06], [153.64, 41.07], [149.99, 37.09], [146.37, 41.07]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[163.91, 47.24], [157.59, 41.05], [163.91, 34.9], [170.2, 41.05], [163.91, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[160.21, 41.05], [163.91, 45.13], [167.6, 41.05], [163.91, 36.99], [160.21, 41.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[177.85, 47.24], [171.54, 41.05], [177.85, 34.9], [184.14, 41.05], [177.85, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[174.16, 41.05], [177.85, 45.13], [181.55, 41.05], [177.85, 36.99], [174.16, 41.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.36, 1.94], [0, 0], [-1.54, 0], [0, 0.96], [1.63, 0.34], [0, 2.52], [-2.81, 0], [-0.53, -1.87], [0, 0], [1.13, 0], [0, -0.84], [-1.49, -0.29], [0, -2.64], [2.95, 0]], "o": [[-2.86, 0], [0, 0], [0.41, 1.27], [1.25, 0], [0, -1.13], [-1.9, -0.38], [0, -2.02], [2.5, 0], [0, 0], [-0.38, -0.98], [-1.2, 0], [0, 1.01], [1.92, 0.43], [0, 2.3], [0, 0]], "v": [[190.55, 47.24], [185.46, 43.86], [187.62, 43.28], [190.69, 45.27], [192.81, 43.74], [190, 42.01], [185.75, 38.38], [190.24, 34.9], [195.06, 37.83], [192.78, 38.43], [190.26, 36.87], [188.2, 38.19], [190.93, 39.8], [195.23, 43.47], [190.55, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[201.78, 47], [198.33, 43.47], [198.33, 37.16], [195.95, 37.16], [195.95, 35.14], [197.08, 35.14], [198.61, 33.34], [198.61, 31.26], [200.94, 31.26], [200.94, 35.14], [203.68, 35.14], [203.68, 37.16], [200.94, 37.16], [200.94, 43.09], [202.65, 44.89], [203.68, 44.89], [203.68, 47], [201.78, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[210.76, 47.24], [204.56, 41.07], [210.68, 34.9], [216.59, 41], [216.56, 41.6], [207.13, 41.6], [210.76, 45.18], [213.85, 43.23], [216.25, 43.59], [210.76, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[207.25, 39.63], [213.95, 39.63], [210.71, 36.97], [207.25, 39.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[218.48, 47], [218.48, 35.14], [221.09, 35.14], [221.09, 37.11], [225.39, 34.9], [225.39, 37.33], [221.09, 41.48], [221.09, 47], [218.48, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.36, 1.94], [0, 0], [-1.54, 0], [0, 0.96], [1.63, 0.34], [0, 2.52], [-2.81, 0], [-0.53, -1.87], [0, 0], [1.13, 0], [0, -0.84], [-1.49, -0.29], [0, -2.64], [2.95, 0]], "o": [[-2.86, 0], [0, 0], [0.41, 1.27], [1.25, 0], [0, -1.13], [-1.9, -0.38], [0, -2.02], [2.5, 0], [0, 0], [-0.38, -0.98], [-1.2, 0], [0, 1.01], [1.92, 0.43], [0, 2.3], [0, 0]], "v": [[231.05, 47.24], [225.96, 43.86], [228.12, 43.28], [231.19, 45.27], [233.31, 43.74], [230.5, 42.01], [226.25, 38.38], [230.74, 34.9], [235.56, 37.83], [233.28, 38.43], [230.76, 36.87], [228.7, 38.19], [231.43, 39.8], [235.73, 43.47], [231.05, 47.24]]}}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 2}, {"ty": 4, "nm": "Grow your potential with Crypto, Funds and Stocks", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [120, 39]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [223, 239]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 660}, {"s": [0], "t": 720}]}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 5.18], [-4.99, 0], [-1.42, -2.52], [0, 0], [2.21, 0], [0, -3.7], [-3.53, 0], [-0.53, 2.9], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.93, 0]], "o": [[-4.92, 0], [0, -5.16], [3.29, 0], [0, 0], [-0.96, -1.78], [-3.41, 0], [0, 3.7], [2.74, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 2.06], [0, 0]], "v": [[23.93, 21.31], [15.58, 12.6], [24.07, 3.91], [31.42, 7.97], [29.04, 9.24], [24.07, 6.41], [18.29, 12.6], [24.07, 18.79], [29.35, 14.28], [24.17, 14.28], [24.17, 12.07], [32.02, 12.07], [32.02, 21], [29.86, 21], [29.86, 17.88], [23.93, 21.31]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[34.17, 21], [34.17, 9.14], [36.79, 9.14], [36.79, 11.11], [41.09, 8.9], [41.09, 11.33], [36.79, 15.48], [36.79, 21], [34.17, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[47.69, 21.24], [41.38, 15.05], [47.69, 8.9], [53.98, 15.05], [47.69, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[44, 15.05], [47.69, 19.13], [51.39, 15.05], [47.69, 10.99], [44, 15.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[58.46, 21], [54.26, 9.14], [57.04, 9.14], [59.95, 17.9], [62.97, 9.14], [65.18, 9.14], [68.2, 17.9], [71.11, 9.14], [73.87, 9.14], [69.69, 21], [66.91, 21], [64.07, 12.29], [61.24, 21], [58.46, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[81.7, 26.83], [84.44, 20.54], [79.59, 9.14], [82.42, 9.14], [85.78, 17.9], [89.17, 9.14], [92.02, 9.14], [84.56, 26.83], [81.7, 26.83]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[98.53, 21.24], [92.22, 15.05], [98.53, 8.9], [104.82, 15.05], [98.53, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[94.83, 15.05], [98.53, 19.13], [102.22, 15.05], [98.53, 10.99], [94.83, 15.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.66], [0, 0], [0, 0], [0, 0], [-1.54, 0], [-0.02, 2.35], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.63, 0]], "o": [[-2.9, 0], [0, 0], [0, 0], [0, 0], [0, 1.78], [1.66, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.82, 1.08], [0, 0]], "v": [[111.03, 21.24], [106.59, 17.02], [106.59, 9.14], [109.21, 9.14], [109.21, 16.51], [111.8, 19.13], [114.8, 15.77], [114.8, 9.14], [117.42, 9.14], [117.42, 21], [114.8, 21], [114.8, 19.51], [111.03, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[119.86, 21], [119.86, 9.14], [122.48, 9.14], [122.48, 11.11], [126.77, 8.9], [126.77, 11.33], [122.48, 15.48], [122.48, 21], [119.86, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-1.8, 0], [0, -3.79], [3.22, 0], [0.74, 1.32], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.74, -1.3], [3.22, 0], [0, 3.79], [-1.8, 0], [0, 0], [0, 0], [0, 0]], "v": [[133.64, 26.83], [133.64, 9.14], [136.26, 9.14], [136.26, 10.94], [140.32, 8.9], [146.05, 15.05], [140.32, 21.24], [136.26, 19.15], [136.26, 26.83], [133.64, 26.83]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.09, 0], [0, 2.42], [2.09, 0], [0, -2.42]], "o": [[0, 2.42], [2.09, 0], [0, -2.42], [-2.09, 0], [0, 0]], "v": [[136.16, 15.05], [139.79, 19.06], [143.44, 15.05], [139.79, 11.09], [136.16, 15.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[153.72, 21.24], [147.41, 15.05], [153.72, 8.9], [160.01, 15.05], [153.72, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[150.03, 15.05], [153.72, 19.13], [157.42, 15.05], [153.72, 10.99], [150.03, 15.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[166.38, 21], [162.92, 17.47], [162.92, 11.16], [160.55, 11.16], [160.55, 9.14], [161.68, 9.14], [163.21, 7.34], [163.21, 5.26], [165.54, 5.26], [165.54, 9.14], [168.28, 9.14], [168.28, 11.16], [165.54, 11.16], [165.54, 17.09], [167.24, 18.89], [168.28, 18.89], [168.28, 21], [166.38, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[175.35, 21.24], [169.16, 15.07], [175.28, 8.9], [181.19, 15], [181.16, 15.6], [171.73, 15.6], [175.35, 19.18], [178.45, 17.23], [180.85, 17.59], [175.35, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[171.85, 13.63], [178.55, 13.63], [175.31, 10.97], [171.85, 13.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.54, 0], [0.19, -2.02], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.61, 0], [0, -2.66], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.78], [-1.54, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.08], [2.9, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[191.28, 13.63], [188.69, 10.99], [185.71, 13.87], [185.71, 21], [183.1, 21], [183.1, 9.14], [185.71, 9.14], [185.71, 10.61], [189.46, 8.9], [193.9, 13.13], [193.9, 21], [191.28, 21], [191.28, 13.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[200.79, 21], [197.33, 17.47], [197.33, 11.16], [194.95, 11.16], [194.95, 9.14], [196.08, 9.14], [197.62, 7.34], [197.62, 5.26], [199.95, 5.26], [199.95, 9.14], [202.68, 9.14], [202.68, 11.16], [199.95, 11.16], [199.95, 17.09], [201.65, 18.89], [202.68, 18.89], [202.68, 21], [200.79, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[204.42, 21], [204.42, 9.14], [207.04, 9.14], [207.04, 21], [204.42, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.89, 0], [0, -0.89], [0.89, 0], [0, 0.89]], "o": [[0, -0.89], [0.89, 0], [0, 0.89], [-0.89, 0], [0, 0]], "v": [[204.09, 5.16], [205.72, 3.55], [207.35, 5.16], [205.72, 6.79], [204.09, 5.16]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.26], [-2.54, 0.48], [0, 0], [0, 0.5], [1.34, 0], [0.17, -1.2], [0, 0], [-3.02, 0], [-0.5, -1.9], [0, -0.77], [0, 0], [-0.62, 0], [0, 0], [0, 0], [0, 0], [0, 1.18], [0, 0], [0, 0], [2.09, 0]], "o": [[-2.02, 0], [0, -2.4], [0, 0], [0.5, -0.1], [0, -1.39], [-1.63, 0], [0, 0], [0.41, -2.33], [2.59, 0], [0.14, 0.41], [0, 0], [0, 0.82], [0, 0], [0, 0], [0, 0], [-1.22, 0], [0, 0], [0, 0], [-0.74, 1.34], [0, 0]], "v": [[212.9, 21.24], [208.94, 18.02], [213.5, 14.21], [215.63, 13.8], [216.62, 13.01], [214.36, 10.97], [211.65, 13.01], [209.08, 12.65], [214.31, 8.9], [219.04, 11.81], [219.23, 13.49], [219.23, 17.81], [220.1, 18.94], [220.53, 18.94], [220.53, 21], [219.3, 21], [217.22, 19.32], [217.22, 19.06], [217.02, 19.06], [212.9, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.13, 0], [0, 2.35], [0, 0], [0, -0.96]], "o": [[0, 0.86], [2.02, 0], [0, 0], [-1.34, 0.24], [0, 0]], "v": [[211.55, 17.81], [213.33, 19.2], [216.62, 15.36], [213.62, 16.08], [211.55, 17.81]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[222, 21], [222, 3], [224.62, 3], [224.62, 21], [222, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[24.58, 47], [20.38, 35.14], [23.16, 35.14], [26.07, 43.9], [29.09, 35.14], [31.3, 35.14], [34.32, 43.9], [37.23, 35.14], [39.99, 35.14], [35.81, 47], [33.03, 47], [30.2, 38.29], [27.36, 47], [24.58, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[41.36, 47], [41.36, 35.14], [43.97, 35.14], [43.97, 47], [41.36, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.89, 0], [0, -0.89], [0.89, 0], [0, 0.89]], "o": [[0, -0.89], [0.89, 0], [0, 0.89], [-0.89, 0], [0, 0]], "v": [[41.02, 31.16], [42.65, 29.55], [44.29, 31.16], [42.65, 32.79], [41.02, 31.16]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[51.15, 47], [47.69, 43.47], [47.69, 37.16], [45.32, 37.16], [45.32, 35.14], [46.45, 35.14], [47.98, 33.34], [47.98, 31.26], [50.31, 31.26], [50.31, 35.14], [53.05, 35.14], [53.05, 37.16], [50.31, 37.16], [50.31, 43.09], [52.01, 44.89], [53.05, 44.89], [53.05, 47], [51.15, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.58, 0], [0.02, -2.4], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.73, 0], [0, -2.93], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.92], [-1.56, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.13], [2.76, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[63, 39.78], [60.38, 36.99], [57.4, 40.35], [57.4, 47], [54.79, 47], [54.79, 29], [57.4, 29], [57.4, 36.73], [61.32, 34.9], [65.61, 39.39], [65.61, 47], [63, 47], [63, 39.78]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 5.18], [-4.99, 0], [-1.25, -3.02], [0, 0], [2.42, 0], [0, -3.7], [-3.41, 0], [-0.86, 2.09], [0, 0], [3.65, 0]], "o": [[-4.99, 0], [0, -5.16], [3.65, 0], [0, 0], [-0.86, -2.09], [-3.41, 0], [0, 3.72], [2.42, 0], [0, 0], [-1.25, 3.05], [0, 0]], "v": [[81.36, 47.31], [72.87, 38.6], [81.36, 29.91], [89.12, 34.86], [86.57, 35.79], [81.36, 32.41], [75.58, 38.6], [81.36, 44.79], [86.57, 41.43], [89.12, 42.34], [81.36, 47.31]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[90.72, 47], [90.72, 35.14], [93.33, 35.14], [93.33, 37.11], [97.63, 34.9], [97.63, 37.33], [93.33, 41.48], [93.33, 47], [90.72, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[100, 52.83], [102.73, 46.54], [97.89, 35.14], [100.72, 35.14], [104.08, 43.9], [107.46, 35.14], [110.32, 35.14], [102.85, 52.83], [100, 52.83]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-1.8, 0], [0, -3.79], [3.22, 0], [0.74, 1.32], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.74, -1.3], [3.22, 0], [0, 3.79], [-1.8, 0], [0, 0], [0, 0], [0, 0]], "v": [[111.6, 52.83], [111.6, 35.14], [114.22, 35.14], [114.22, 36.94], [118.27, 34.9], [124.01, 41.05], [118.27, 47.24], [114.22, 45.15], [114.22, 52.83], [111.6, 52.83]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.09, 0], [0, 2.42], [2.09, 0], [0, -2.42]], "o": [[0, 2.42], [2.09, 0], [0, -2.42], [-2.09, 0], [0, 0]], "v": [[114.12, 41.05], [117.74, 45.06], [121.39, 41.05], [117.74, 37.09], [114.12, 41.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[130.39, 47], [126.94, 43.47], [126.94, 37.16], [124.56, 37.16], [124.56, 35.14], [125.69, 35.14], [127.22, 33.34], [127.22, 31.26], [129.55, 31.26], [129.55, 35.14], [132.29, 35.14], [132.29, 37.16], [129.55, 37.16], [129.55, 43.09], [131.26, 44.89], [132.29, 44.89], [132.29, 47], [130.39, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[139.46, 47.24], [133.15, 41.05], [139.46, 34.9], [145.75, 41.05], [139.46, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[135.76, 41.05], [139.46, 45.13], [143.16, 41.05], [139.46, 36.99], [135.76, 41.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[147.36, 51.3], [148.82, 43.76], [151.49, 43.76], [149.28, 51.3], [147.36, 51.3]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[159.28, 47], [159.28, 30.22], [168.9, 30.22], [168.9, 32.62], [162.01, 32.62], [162.01, 37.42], [168.01, 37.42], [168.01, 39.8], [162.01, 39.8], [162.01, 47], [159.28, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.66], [0, 0], [0, 0], [0, 0], [-1.54, 0], [-0.02, 2.35], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.63, 0]], "o": [[-2.9, 0], [0, 0], [0, 0], [0, 0], [0, 1.78], [1.66, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.82, 1.08], [0, 0]], "v": [[174.28, 47.24], [169.84, 43.02], [169.84, 35.14], [172.46, 35.14], [172.46, 42.51], [175.05, 45.13], [178.05, 41.77], [178.05, 35.14], [180.66, 35.14], [180.66, 47], [178.05, 47], [178.05, 45.51], [174.28, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.54, 0], [0.19, -2.02], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.61, 0], [0, -2.66], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.78], [-1.54, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.08], [2.9, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[191.32, 39.63], [188.72, 36.99], [185.75, 39.87], [185.75, 47], [183.13, 47], [183.13, 35.14], [185.75, 35.14], [185.75, 36.61], [189.49, 34.9], [193.93, 39.13], [193.93, 47], [191.32, 47], [191.32, 39.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.82], [-3.22, 0], [-0.74, -1.32], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.78, 0]], "o": [[-3.22, 0], [0, -3.79], [1.78, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.74, 1.32], [0, 0]], "v": [[201.46, 47.24], [195.73, 41.07], [201.46, 34.9], [205.52, 36.99], [205.52, 29], [208.14, 29], [208.14, 47], [205.52, 47], [205.52, 45.18], [201.46, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.11, 0], [0, 2.45], [2.09, 0], [0, -2.4]], "o": [[0, 2.45], [2.09, 0], [0, -2.4], [-2.11, 0], [0, 0]], "v": [[198.34, 41.07], [201.99, 45.06], [205.62, 41.07], [201.99, 37.09], [198.34, 41.07]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.36, 1.94], [0, 0], [-1.54, 0], [0, 0.96], [1.63, 0.34], [0, 2.52], [-2.81, 0], [-0.53, -1.87], [0, 0], [1.13, 0], [0, -0.84], [-1.49, -0.29], [0, -2.64], [2.95, 0]], "o": [[-2.86, 0], [0, 0], [0.41, 1.27], [1.25, 0], [0, -1.13], [-1.9, -0.38], [0, -2.02], [2.5, 0], [0, 0], [-0.38, -0.98], [-1.2, 0], [0, 1.01], [1.92, 0.43], [0, 2.3], [0, 0]], "v": [[215.09, 47.24], [210, 43.86], [212.16, 43.28], [215.23, 45.27], [217.34, 43.74], [214.54, 42.01], [210.29, 38.38], [214.78, 34.9], [219.6, 37.83], [217.32, 38.43], [214.8, 36.87], [212.74, 38.19], [215.47, 39.8], [219.77, 43.47], [215.09, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.26], [-2.54, 0.48], [0, 0], [0, 0.5], [1.34, 0], [0.17, -1.2], [0, 0], [-3.02, 0], [-0.5, -1.9], [0, -0.77], [0, 0], [-0.62, 0], [0, 0], [0, 0], [0, 0], [0, 1.18], [0, 0], [0, 0], [2.09, 0]], "o": [[-2.02, 0], [0, -2.4], [0, 0], [0.5, -0.1], [0, -1.39], [-1.63, 0], [0, 0], [0.41, -2.33], [2.59, 0], [0.14, 0.41], [0, 0], [0, 0.82], [0, 0], [0, 0], [0, 0], [-1.22, 0], [0, 0], [0, 0], [-0.74, 1.34], [0, 0]], "v": [[65.86, 73.24], [61.9, 70.02], [66.46, 66.21], [68.6, 65.8], [69.58, 65.01], [67.32, 62.97], [64.61, 65.01], [62.04, 64.65], [67.28, 60.9], [72, 63.81], [72.2, 65.49], [72.2, 69.81], [73.06, 70.94], [73.49, 70.94], [73.49, 73], [72.27, 73], [70.18, 71.32], [70.18, 71.06], [69.99, 71.06], [65.86, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.13, 0], [0, 2.35], [0, 0], [0, -0.96]], "o": [[0, 0.86], [2.02, 0], [0, 0], [-1.34, 0.24], [0, 0]], "v": [[64.52, 69.81], [66.29, 71.2], [69.58, 67.36], [66.58, 68.08], [64.52, 69.81]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.54, 0], [0.19, -2.02], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.61, 0], [0, -2.66], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.78], [-1.54, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.08], [2.9, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[83.17, 65.63], [80.58, 62.99], [77.61, 65.87], [77.61, 73], [74.99, 73], [74.99, 61.14], [77.61, 61.14], [77.61, 62.61], [81.35, 60.9], [85.79, 65.13], [85.79, 73], [83.17, 73], [83.17, 65.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.82], [-3.22, 0], [-0.74, -1.32], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.78, 0]], "o": [[-3.22, 0], [0, -3.79], [1.78, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.74, 1.32], [0, 0]], "v": [[93.32, 73.24], [87.59, 67.07], [93.32, 60.9], [97.38, 62.99], [97.38, 55], [100, 55], [100, 73], [97.38, 73], [97.38, 71.18], [93.32, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.11, 0], [0, 2.45], [2.09, 0], [0, -2.4]], "o": [[0, 2.45], [2.09, 0], [0, -2.4], [-2.11, 0], [0, 0]], "v": [[90.2, 67.07], [93.85, 71.06], [97.48, 67.07], [93.85, 63.09], [90.2, 67.07]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.55, 3.05], [0, 0], [-2.14, 0], [0, 1.61], [2.14, 0.55], [0, 3.34], [-3.46, 0], [-0.77, -3.1], [0, 0], [2.04, 0], [0, -1.51], [-2.04, -0.5], [0, -3.58], [3.72, 0]], "o": [[-3.55, 0], [0, 0], [0.53, 2.09], [2.09, 0], [0, -1.82], [-2.57, -0.7], [0, -2.93], [3.02, 0], [0, 0], [-0.62, -1.85], [-1.92, 0], [0, 1.68], [2.64, 0.65], [0, 3.05], [0, 0]], "v": [[113.73, 73.31], [106.91, 68.54], [109.6, 67.96], [113.75, 71.06], [117.04, 68.49], [113.01, 65.63], [107.46, 60.76], [113.27, 55.91], [119.7, 60.26], [117.26, 60.83], [113.42, 58.17], [110.22, 60.62], [114.06, 63.23], [119.87, 68.3], [113.73, 73.31]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[126.36, 73], [122.9, 69.47], [122.9, 63.16], [120.53, 63.16], [120.53, 61.14], [121.66, 61.14], [123.19, 59.34], [123.19, 57.26], [125.52, 57.26], [125.52, 61.14], [128.26, 61.14], [128.26, 63.16], [125.52, 63.16], [125.52, 69.09], [127.22, 70.89], [128.26, 70.89], [128.26, 73], [126.36, 73]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[135.43, 73.24], [129.12, 67.05], [135.43, 60.9], [141.72, 67.05], [135.43, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[131.73, 67.05], [135.43, 71.13], [139.13, 67.05], [135.43, 62.99], [131.73, 67.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.65], [-3.65, 0], [-0.62, -2.66], [0, 0], [1.66, 0], [0, -2.38], [-2.14, 0], [-0.46, 1.56], [0, 0], [2.86, 0]], "o": [[-3.65, 0], [0, -3.62], [2.9, 0], [0, 0], [-0.46, -1.56], [-2.14, 0], [0, 2.42], [1.66, 0], [0, 0], [-0.62, 2.66], [0, 0]], "v": [[149.28, 73.24], [143.06, 67.05], [149.28, 60.9], [154.97, 65.1], [152.54, 65.37], [149.26, 62.97], [145.68, 67.05], [149.26, 71.18], [152.54, 68.75], [154.97, 69.02], [149.28, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[164.78, 73], [160.2, 67.77], [159.38, 67.77], [159.38, 73], [156.76, 73], [156.76, 55], [159.38, 55], [159.38, 65.94], [160.15, 65.94], [164.54, 61.14], [167.61, 61.14], [162.48, 66.76], [167.95, 73], [164.78, 73]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.36, 1.94], [0, 0], [-1.54, 0], [0, 0.96], [1.63, 0.34], [0, 2.52], [-2.81, 0], [-0.53, -1.87], [0, 0], [1.13, 0], [0, -0.84], [-1.49, -0.29], [0, -2.64], [2.95, 0]], "o": [[-2.86, 0], [0, 0], [0.41, 1.27], [1.25, 0], [0, -1.13], [-1.9, -0.38], [0, -2.02], [2.5, 0], [0, 0], [-0.38, -0.98], [-1.2, 0], [0, 1.01], [1.92, 0.43], [0, 2.3], [0, 0]], "v": [[173.39, 73.24], [168.31, 69.86], [170.47, 69.28], [173.54, 71.27], [175.65, 69.74], [172.84, 68.01], [168.59, 64.38], [173.08, 60.9], [177.91, 63.83], [175.63, 64.43], [173.11, 62.87], [171.04, 64.19], [173.78, 65.8], [178.07, 69.47], [173.39, 73.24]]}}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 3}, {"ty": 0, "nm": "Logo", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [120, 39]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [223, 239]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 660}, {"s": [100], "t": 720}]}}, "w": 445, "h": 445, "refId": "1", "ind": 4}, {"ty": 4, "nm": "Get most", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [120, 39]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [223, 239]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 300}, {"s": [0], "t": 420}]}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 5.18], [-4.99, 0], [-1.42, -2.52], [0, 0], [2.21, 0], [0, -3.7], [-3.53, 0], [-0.53, 2.9], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.93, 0]], "o": [[-4.92, 0], [0, -5.16], [3.29, 0], [0, 0], [-0.96, -1.78], [-3.41, 0], [0, 3.7], [2.74, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 2.06], [0, 0]], "v": [[21.35, 21.31], [13, 12.6], [21.49, 3.91], [28.84, 7.97], [26.46, 9.24], [21.49, 6.41], [15.71, 12.6], [21.49, 18.79], [26.77, 14.28], [21.59, 14.28], [21.59, 12.07], [29.44, 12.07], [29.44, 21], [27.28, 21], [27.28, 17.88], [21.35, 21.31]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[37.26, 21.24], [31.07, 15.07], [37.19, 8.9], [43.09, 15], [43.07, 15.6], [33.64, 15.6], [37.26, 19.18], [40.36, 17.23], [42.76, 17.59], [37.26, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[33.76, 13.63], [40.45, 13.63], [37.21, 10.97], [33.76, 13.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[49.5, 21], [46.04, 17.47], [46.04, 11.16], [43.66, 11.16], [43.66, 9.14], [44.79, 9.14], [46.33, 7.34], [46.33, 5.26], [48.66, 5.26], [48.66, 9.14], [51.39, 9.14], [51.39, 11.16], [48.66, 11.16], [48.66, 17.09], [50.36, 18.89], [51.39, 18.89], [51.39, 21], [49.5, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[63.4, 21], [59.94, 17.47], [59.94, 11.16], [57.56, 11.16], [57.56, 9.14], [58.69, 9.14], [60.23, 7.34], [60.23, 5.26], [62.56, 5.26], [62.56, 9.14], [65.29, 9.14], [65.29, 11.16], [62.56, 11.16], [62.56, 17.09], [64.26, 18.89], [65.29, 18.89], [65.29, 21], [63.4, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.58, 0], [0.02, -2.4], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.73, 0], [0, -2.93], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.92], [-1.56, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.13], [2.76, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.24, 13.78], [72.63, 10.99], [69.65, 14.35], [69.65, 21], [67.03, 21], [67.03, 3], [69.65, 3], [69.65, 10.73], [73.56, 8.9], [77.86, 13.39], [77.86, 21], [75.24, 21], [75.24, 13.78]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[85.85, 21.24], [79.65, 15.07], [85.77, 8.9], [91.68, 15], [91.65, 15.6], [82.22, 15.6], [85.85, 19.18], [88.94, 17.23], [91.34, 17.59], [85.85, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[82.34, 13.63], [89.04, 13.63], [85.8, 10.97], [82.34, 13.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.32, 0], [0, -2.26], [0, 0], [0, 0], [0, 0], [1.32, 0], [0.17, -1.82], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.46, 0], [-0.62, -1.1], [-1.66, 0], [0, -2.23], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.51], [-1.42, 0], [0, 0], [0, 0], [0, 0], [0, -1.51], [-1.3, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.74, -1.03], [1.7, 0], [0.84, -1.13], [2.5, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[113.91, 13.32], [111.65, 10.99], [109.11, 14.14], [109.11, 21], [106.49, 21], [106.49, 13.32], [104.21, 10.99], [101.69, 13.58], [101.69, 21], [99.07, 21], [99.07, 9.14], [101.69, 9.14], [101.69, 10.54], [105.1, 8.9], [108.67, 10.66], [112.51, 8.9], [116.52, 12.48], [116.52, 21], [113.91, 21], [113.91, 13.32]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[124.61, 21.24], [118.3, 15.05], [124.61, 8.9], [130.9, 15.05], [124.61, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[120.92, 15.05], [124.61, 19.13], [128.31, 15.05], [124.61, 10.99], [120.92, 15.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.36, 1.94], [0, 0], [-1.54, 0], [0, 0.96], [1.63, 0.34], [0, 2.52], [-2.81, 0], [-0.53, -1.87], [0, 0], [1.13, 0], [0, -0.84], [-1.49, -0.29], [0, -2.64], [2.95, 0]], "o": [[-2.86, 0], [0, 0], [0.41, 1.27], [1.25, 0], [0, -1.13], [-1.9, -0.38], [0, -2.02], [2.5, 0], [0, 0], [-0.38, -0.98], [-1.2, 0], [0, 1.01], [1.92, 0.43], [0, 2.3], [0, 0]], "v": [[137.31, 21.24], [132.22, 17.86], [134.38, 17.28], [137.46, 19.27], [139.57, 17.74], [136.76, 16.01], [132.51, 12.38], [137, 8.9], [141.82, 11.83], [139.54, 12.43], [137.02, 10.87], [134.96, 12.19], [137.7, 13.8], [141.99, 17.47], [137.31, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[148.54, 21], [145.09, 17.47], [145.09, 11.16], [142.71, 11.16], [142.71, 9.14], [143.84, 9.14], [145.38, 7.34], [145.38, 5.26], [147.7, 5.26], [147.7, 9.14], [150.44, 9.14], [150.44, 11.16], [147.7, 11.16], [147.7, 17.09], [149.41, 18.89], [150.44, 18.89], [150.44, 21], [148.54, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.36, 1.94], [0, 0], [-1.54, 0], [0, 0.96], [1.63, 0.34], [0, 2.52], [-2.81, 0], [-0.53, -1.87], [0, 0], [1.13, 0], [0, -0.84], [-1.49, -0.29], [0, -2.64], [2.95, 0]], "o": [[-2.86, 0], [0, 0], [0.41, 1.27], [1.25, 0], [0, -1.13], [-1.9, -0.38], [0, -2.02], [2.5, 0], [0, 0], [-0.38, -0.98], [-1.2, 0], [0, 1.01], [1.92, 0.43], [0, 2.3], [0, 0]], "v": [[162.18, 21.24], [157.09, 17.86], [159.25, 17.28], [162.32, 19.27], [164.43, 17.74], [161.63, 16.01], [157.38, 12.38], [161.87, 8.9], [166.69, 11.83], [164.41, 12.43], [161.89, 10.87], [159.83, 12.19], [162.56, 13.8], [166.86, 17.47], [162.18, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[174.46, 21.24], [168.27, 15.07], [174.39, 8.9], [180.3, 15], [180.27, 15.6], [170.84, 15.6], [174.46, 19.18], [177.56, 17.23], [179.96, 17.59], [174.46, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[170.96, 13.63], [177.66, 13.63], [174.42, 10.97], [170.96, 13.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.65], [-3.65, 0], [-0.62, -2.66], [0, 0], [1.66, 0], [0, -2.38], [-2.14, 0], [-0.46, 1.56], [0, 0], [2.86, 0]], "o": [[-3.65, 0], [0, -3.62], [2.9, 0], [0, 0], [-0.46, -1.56], [-2.14, 0], [0, 2.42], [1.66, 0], [0, 0], [-0.62, 2.66], [0, 0]], "v": [[187.85, 21.24], [181.63, 15.05], [187.85, 8.9], [193.53, 13.1], [191.11, 13.37], [187.82, 10.97], [184.25, 15.05], [187.82, 19.18], [191.11, 16.75], [193.53, 17.02], [187.85, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.66], [0, 0], [0, 0], [0, 0], [-1.54, 0], [-0.02, 2.35], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.63, 0]], "o": [[-2.9, 0], [0, 0], [0, 0], [0, 0], [0, 1.78], [1.66, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.82, 1.08], [0, 0]], "v": [[199.65, 21.24], [195.21, 17.02], [195.21, 9.14], [197.83, 9.14], [197.83, 16.51], [200.42, 19.13], [203.42, 15.77], [203.42, 9.14], [206.03, 9.14], [206.03, 21], [203.42, 21], [203.42, 19.51], [199.65, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[208.48, 21], [208.48, 9.14], [211.1, 9.14], [211.1, 11.11], [215.39, 8.9], [215.39, 11.33], [211.1, 15.48], [211.1, 21], [208.48, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[221.9, 21.24], [215.71, 15.07], [221.83, 8.9], [227.73, 15], [227.71, 15.6], [218.28, 15.6], [221.9, 19.18], [225, 17.23], [227.4, 17.59], [221.9, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[218.4, 13.63], [225.09, 13.63], [221.85, 10.97], [218.4, 13.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.65], [-3.65, 0], [-0.62, -2.66], [0, 0], [1.66, 0], [0, -2.38], [-2.14, 0], [-0.46, 1.56], [0, 0], [2.86, 0]], "o": [[-3.65, 0], [0, -3.62], [2.9, 0], [0, 0], [-0.46, -1.56], [-2.14, 0], [0, 2.42], [1.66, 0], [0, 0], [-0.62, 2.66], [0, 0]], "v": [[23.4, 47.24], [17.18, 41.05], [23.4, 34.9], [29.08, 39.1], [26.66, 39.37], [23.37, 36.97], [19.8, 41.05], [23.37, 45.18], [26.66, 42.75], [29.08, 43.02], [23.4, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.88, 47], [30.88, 35.14], [33.5, 35.14], [33.5, 37.11], [37.79, 34.9], [37.79, 37.33], [33.5, 41.48], [33.5, 47], [30.88, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[44.3, 47.24], [38.11, 41.07], [44.23, 34.9], [50.13, 41], [50.11, 41.6], [40.68, 41.6], [44.3, 45.18], [47.4, 43.23], [49.8, 43.59], [44.3, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[40.8, 39.63], [47.49, 39.63], [44.25, 36.97], [40.8, 39.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.82], [-3.22, 0], [-0.74, -1.32], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.78, 0]], "o": [[-3.22, 0], [0, -3.79], [1.78, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.74, 1.32], [0, 0]], "v": [[57.23, 47.24], [51.49, 41.07], [57.23, 34.9], [61.29, 36.99], [61.29, 29], [63.9, 29], [63.9, 47], [61.29, 47], [61.29, 45.18], [57.23, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.11, 0], [0, 2.45], [2.09, 0], [0, -2.4]], "o": [[0, 2.45], [2.09, 0], [0, -2.4], [-2.11, 0], [0, 0]], "v": [[54.11, 41.07], [57.76, 45.06], [61.38, 41.07], [57.76, 37.09], [54.11, 41.07]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[66.34, 47], [66.34, 35.14], [68.96, 35.14], [68.96, 47], [66.34, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.89, 0], [0, -0.89], [0.89, 0], [0, 0.89]], "o": [[0, -0.89], [0.89, 0], [0, 0.89], [-0.89, 0], [0, 0]], "v": [[66.01, 31.16], [67.64, 29.55], [69.27, 31.16], [67.64, 32.79], [66.01, 31.16]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[76.13, 47], [72.68, 43.47], [72.68, 37.16], [70.3, 37.16], [70.3, 35.14], [71.43, 35.14], [72.97, 33.34], [72.97, 31.26], [75.29, 31.26], [75.29, 35.14], [78.03, 35.14], [78.03, 37.16], [75.29, 37.16], [75.29, 43.09], [77, 44.89], [78.03, 44.89], [78.03, 47], [76.13, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.65], [-3.65, 0], [-0.62, -2.66], [0, 0], [1.66, 0], [0, -2.38], [-2.14, 0], [-0.46, 1.56], [0, 0], [2.86, 0]], "o": [[-3.65, 0], [0, -3.62], [2.9, 0], [0, 0], [-0.46, -1.56], [-2.14, 0], [0, 2.42], [1.66, 0], [0, 0], [-0.62, 2.66], [0, 0]], "v": [[90.92, 47.24], [84.7, 41.05], [90.92, 34.9], [96.61, 39.1], [94.18, 39.37], [90.9, 36.97], [87.32, 41.05], [90.9, 45.18], [94.18, 42.75], [96.61, 43.02], [90.92, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.26], [-2.54, 0.48], [0, 0], [0, 0.5], [1.34, 0], [0.17, -1.2], [0, 0], [-3.02, 0], [-0.5, -1.9], [0, -0.77], [0, 0], [-0.62, 0], [0, 0], [0, 0], [0, 0], [0, 1.18], [0, 0], [0, 0], [2.09, 0]], "o": [[-2.02, 0], [0, -2.4], [0, 0], [0.5, -0.1], [0, -1.39], [-1.63, 0], [0, 0], [0.41, -2.33], [2.59, 0], [0.14, 0.41], [0, 0], [0, 0.82], [0, 0], [0, 0], [0, 0], [-1.22, 0], [0, 0], [0, 0], [-0.74, 1.34], [0, 0]], "v": [[101.86, 47.24], [97.9, 44.02], [102.46, 40.21], [104.6, 39.8], [105.58, 39.01], [103.32, 36.97], [100.61, 39.01], [98.04, 38.65], [103.28, 34.9], [108, 37.81], [108.2, 39.49], [108.2, 43.81], [109.06, 44.94], [109.49, 44.94], [109.49, 47], [108.27, 47], [106.18, 45.32], [106.18, 45.06], [105.99, 45.06], [101.86, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.13, 0], [0, 2.35], [0, 0], [0, -0.96]], "o": [[0, 0.86], [2.02, 0], [0, 0], [-1.34, 0.24], [0, 0]], "v": [[100.52, 43.81], [102.29, 45.2], [105.58, 41.36], [102.58, 42.08], [100.52, 43.81]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[110.97, 47], [110.97, 35.14], [113.58, 35.14], [113.58, 37.11], [117.88, 34.9], [117.88, 37.33], [113.58, 41.48], [113.58, 47], [110.97, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.82], [-3.22, 0], [-0.74, -1.32], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.78, 0]], "o": [[-3.22, 0], [0, -3.79], [1.78, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.74, 1.32], [0, 0]], "v": [[123.93, 47.24], [118.2, 41.07], [123.93, 34.9], [127.99, 36.99], [127.99, 29], [130.6, 29], [130.6, 47], [127.99, 47], [127.99, 45.18], [123.93, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.11, 0], [0, 2.45], [2.09, 0], [0, -2.4]], "o": [[0, 2.45], [2.09, 0], [0, -2.4], [-2.11, 0], [0, 0]], "v": [[120.81, 41.07], [124.46, 45.06], [128.09, 41.07], [124.46, 37.09], [120.81, 41.07]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[141.65, 47], [137.45, 35.14], [140.23, 35.14], [143.14, 43.9], [146.16, 35.14], [148.37, 35.14], [151.39, 43.9], [154.3, 35.14], [157.06, 35.14], [152.88, 47], [150.1, 47], [147.26, 38.29], [144.43, 47], [141.65, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[158.43, 47], [158.43, 35.14], [161.04, 35.14], [161.04, 47], [158.43, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.89, 0], [0, -0.89], [0.89, 0], [0, 0.89]], "o": [[0, -0.89], [0.89, 0], [0, 0.89], [-0.89, 0], [0, 0]], "v": [[158.09, 31.16], [159.72, 29.55], [161.36, 31.16], [159.72, 32.79], [158.09, 31.16]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[168.22, 47], [164.76, 43.47], [164.76, 37.16], [162.39, 37.16], [162.39, 35.14], [163.51, 35.14], [165.05, 33.34], [165.05, 31.26], [167.38, 31.26], [167.38, 35.14], [170.12, 35.14], [170.12, 37.16], [167.38, 37.16], [167.38, 43.09], [169.08, 44.89], [170.12, 44.89], [170.12, 47], [168.22, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.58, 0], [0.02, -2.4], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.73, 0], [0, -2.93], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.92], [-1.56, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.13], [2.76, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[180.07, 39.78], [177.45, 36.99], [174.47, 40.35], [174.47, 47], [171.86, 47], [171.86, 29], [174.47, 29], [174.47, 36.73], [178.39, 34.9], [182.68, 39.39], [182.68, 47], [180.07, 47], [180.07, 39.78]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[195.27, 47], [191.81, 43.47], [191.81, 37.16], [189.43, 37.16], [189.43, 35.14], [190.56, 35.14], [192.1, 33.34], [192.1, 31.26], [194.43, 31.26], [194.43, 35.14], [197.16, 35.14], [197.16, 37.16], [194.43, 37.16], [194.43, 43.09], [196.13, 44.89], [197.16, 44.89], [197.16, 47], [195.27, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.58, 0], [0.02, -2.4], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.73, 0], [0, -2.93], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.92], [-1.56, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.13], [2.76, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[207.11, 39.78], [204.5, 36.99], [201.52, 40.35], [201.52, 47], [198.9, 47], [198.9, 29], [201.52, 29], [201.52, 36.73], [205.43, 34.9], [209.73, 39.39], [209.73, 47], [207.11, 47], [207.11, 39.78]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[217.72, 47.24], [211.53, 41.07], [217.65, 34.9], [223.55, 41], [223.53, 41.6], [214.09, 41.6], [217.72, 45.18], [220.81, 43.23], [223.21, 43.59], [217.72, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[214.21, 39.63], [220.91, 39.63], [217.67, 36.97], [214.21, 39.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.74, 1.32], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.78, 0], [0, -3.79], [3.22, 0]], "o": [[-1.78, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.74, -1.32], [3.22, 0], [0, 3.82], [0, 0]], "v": [[58.74, 73.24], [54.68, 71.18], [54.68, 73], [52.07, 73], [52.07, 55], [54.68, 55], [54.68, 62.99], [58.74, 60.9], [64.48, 67.07], [58.74, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.09, 0], [0, 2.45], [2.11, 0], [0, -2.4]], "o": [[0, 2.45], [2.11, 0], [0, -2.4], [-2.09, 0], [0, 0]], "v": [[54.59, 67.07], [58.21, 71.06], [61.86, 67.07], [58.21, 63.09], [54.59, 67.07]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[72.03, 73.24], [65.84, 67.07], [71.96, 60.9], [77.86, 67], [77.84, 67.6], [68.41, 67.6], [72.03, 71.18], [75.13, 69.23], [77.53, 69.59], [72.03, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[68.53, 65.63], [75.22, 65.63], [71.98, 62.97], [68.53, 65.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.36, 1.94], [0, 0], [-1.54, 0], [0, 0.96], [1.63, 0.34], [0, 2.52], [-2.81, 0], [-0.53, -1.87], [0, 0], [1.13, 0], [0, -0.84], [-1.49, -0.29], [0, -2.64], [2.95, 0]], "o": [[-2.86, 0], [0, 0], [0.41, 1.27], [1.25, 0], [0, -1.13], [-1.9, -0.38], [0, -2.02], [2.5, 0], [0, 0], [-0.38, -0.98], [-1.2, 0], [0, 1.01], [1.92, 0.43], [0, 2.3], [0, 0]], "v": [[84.26, 73.24], [79.17, 69.86], [81.33, 69.28], [84.4, 71.27], [86.52, 69.74], [83.71, 68.01], [79.46, 64.38], [83.95, 60.9], [88.77, 63.83], [86.49, 64.43], [83.97, 62.87], [81.91, 64.19], [84.64, 65.8], [88.94, 69.47], [84.26, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[95.49, 73], [92.04, 69.47], [92.04, 63.16], [89.66, 63.16], [89.66, 61.14], [90.79, 61.14], [92.32, 59.34], [92.32, 57.26], [94.65, 57.26], [94.65, 61.14], [97.39, 61.14], [97.39, 63.16], [94.65, 63.16], [94.65, 69.09], [96.36, 70.89], [97.39, 70.89], [97.39, 73], [95.49, 73]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[104.62, 73], [104.62, 61.14], [107.23, 61.14], [107.23, 63.11], [111.53, 60.9], [111.53, 63.33], [107.23, 67.48], [107.23, 73], [104.62, 73]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[118.04, 73.24], [111.85, 67.07], [117.97, 60.9], [123.87, 67], [123.85, 67.6], [114.41, 67.6], [118.04, 71.18], [121.13, 69.23], [123.53, 69.59], [118.04, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[114.53, 65.63], [121.23, 65.63], [117.99, 62.97], [114.53, 65.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[128.45, 73], [124.25, 61.14], [127.04, 61.14], [129.94, 69.9], [132.97, 61.14], [135.17, 61.14], [138.2, 69.9], [141.1, 61.14], [143.86, 61.14], [139.69, 73], [136.9, 73], [134.07, 64.29], [131.24, 73], [128.45, 73]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.26], [-2.54, 0.48], [0, 0], [0, 0.5], [1.34, 0], [0.17, -1.2], [0, 0], [-3.02, 0], [-0.5, -1.9], [0, -0.77], [0, 0], [-0.62, 0], [0, 0], [0, 0], [0, 0], [0, 1.18], [0, 0], [0, 0], [2.09, 0]], "o": [[-2.02, 0], [0, -2.4], [0, 0], [0.5, -0.1], [0, -1.39], [-1.63, 0], [0, 0], [0.41, -2.33], [2.59, 0], [0.14, 0.41], [0, 0], [0, 0.82], [0, 0], [0, 0], [0, 0], [-1.22, 0], [0, 0], [0, 0], [-0.74, 1.34], [0, 0]], "v": [[148.06, 73.24], [144.1, 70.02], [148.66, 66.21], [150.79, 65.8], [151.78, 65.01], [149.52, 62.97], [146.81, 65.01], [144.24, 64.65], [149.47, 60.9], [154.2, 63.81], [154.39, 65.49], [154.39, 69.81], [155.26, 70.94], [155.69, 70.94], [155.69, 73], [154.46, 73], [152.38, 71.32], [152.38, 71.06], [152.18, 71.06], [148.06, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.13, 0], [0, 2.35], [0, 0], [0, -0.96]], "o": [[0, 0.86], [2.02, 0], [0, 0], [-1.34, 0.24], [0, 0]], "v": [[146.71, 69.81], [148.49, 71.2], [151.78, 67.36], [148.78, 68.08], [146.71, 69.81]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[157.16, 73], [157.16, 61.14], [159.78, 61.14], [159.78, 63.11], [164.07, 60.9], [164.07, 63.33], [159.78, 67.48], [159.78, 73], [157.16, 73]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.82], [-3.22, 0], [-0.74, -1.32], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.78, 0]], "o": [[-3.22, 0], [0, -3.79], [1.78, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.74, 1.32], [0, 0]], "v": [[170.13, 73.24], [164.39, 67.07], [170.13, 60.9], [174.18, 62.99], [174.18, 55], [176.8, 55], [176.8, 73], [174.18, 73], [174.18, 71.18], [170.13, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.11, 0], [0, 2.45], [2.09, 0], [0, -2.4]], "o": [[0, 2.45], [2.09, 0], [0, -2.4], [-2.11, 0], [0, 0]], "v": [[167.01, 67.07], [170.66, 71.06], [174.28, 67.07], [170.66, 63.09], [167.01, 67.07]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.36, 1.94], [0, 0], [-1.54, 0], [0, 0.96], [1.63, 0.34], [0, 2.52], [-2.81, 0], [-0.53, -1.87], [0, 0], [1.13, 0], [0, -0.84], [-1.49, -0.29], [0, -2.64], [2.95, 0]], "o": [[-2.86, 0], [0, 0], [0.41, 1.27], [1.25, 0], [0, -1.13], [-1.9, -0.38], [0, -2.02], [2.5, 0], [0, 0], [-0.38, -0.98], [-1.2, 0], [0, 1.01], [1.92, 0.43], [0, 2.3], [0, 0]], "v": [[183.75, 73.24], [178.66, 69.86], [180.82, 69.28], [183.9, 71.27], [186.01, 69.74], [183.2, 68.01], [178.95, 64.38], [183.44, 60.9], [188.26, 63.83], [185.98, 64.43], [183.46, 62.87], [181.4, 64.19], [184.14, 65.8], [188.43, 69.47], [183.75, 73.24]]}}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 5}, {"ty": 4, "nm": "Pay with ease at your favorite stores and online", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [112, 39]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [223, 239]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 660}, {"s": [0], "t": 720}]}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [-1.03, -0.82], [0, -1.8], [1.66, -0.91], [1.25, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [2.28, 0], [1.03, 0.84], [0, 2.11], [-1.18, 0.62], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[27.97, 21], [27.97, 4.22], [33.32, 4.22], [38.03, 5.42], [39.66, 9.41], [37.21, 14.21], [33.32, 14.9], [30.68, 14.9], [30.68, 21], [27.97, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [-0.65, 0.43], [0, 1.22], [0.65, 0.48], [1.15, 0], [0, 0], [0, 0]], "o": [[0, 0], [1.03, 0], [0.79, -0.48], [0, -1.13], [-0.72, -0.5], [0, 0], [0, 0], [0, 0]], "v": [[30.68, 12.6], [32.96, 12.6], [35.75, 12.1], [36.95, 9.55], [35.91, 7.13], [32.96, 6.5], [30.68, 6.5], [30.68, 12.6]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.26], [-2.54, 0.48], [0, 0], [0, 0.5], [1.34, 0], [0.17, -1.2], [0, 0], [-3.02, 0], [-0.5, -1.9], [0, -0.77], [0, 0], [-0.62, 0], [0, 0], [0, 0], [0, 0], [0, 1.18], [0, 0], [0, 0], [2.09, 0]], "o": [[-2.02, 0], [0, -2.4], [0, 0], [0.5, -0.1], [0, -1.39], [-1.63, 0], [0, 0], [0.41, -2.33], [2.59, 0], [0.14, 0.41], [0, 0], [0, 0.82], [0, 0], [0, 0], [0, 0], [-1.22, 0], [0, 0], [0, 0], [-0.74, 1.34], [0, 0]], "v": [[44.48, 21.24], [40.52, 18.02], [45.08, 14.21], [47.21, 13.8], [48.2, 13.01], [45.94, 10.97], [43.23, 13.01], [40.66, 12.65], [45.89, 8.9], [50.62, 11.81], [50.81, 13.49], [50.81, 17.81], [51.68, 18.94], [52.11, 18.94], [52.11, 21], [50.89, 21], [48.8, 19.32], [48.8, 19.06], [48.61, 19.06], [44.48, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.13, 0], [0, 2.35], [0, 0], [0, -0.96]], "o": [[0, 0.86], [2.02, 0], [0, 0], [-1.34, 0.24], [0, 0]], "v": [[43.13, 17.81], [44.91, 19.2], [48.2, 15.36], [45.2, 16.08], [43.13, 17.81]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[53.86, 26.83], [56.6, 20.54], [51.75, 9.14], [54.58, 9.14], [57.94, 17.9], [61.33, 9.14], [64.18, 9.14], [56.72, 26.83], [53.86, 26.83]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[74.07, 21], [69.87, 9.14], [72.66, 9.14], [75.56, 17.9], [78.58, 9.14], [80.79, 9.14], [83.82, 17.9], [86.72, 9.14], [89.48, 9.14], [85.3, 21], [82.52, 21], [79.69, 12.29], [76.86, 21], [74.07, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[90.85, 21], [90.85, 9.14], [93.47, 9.14], [93.47, 21], [90.85, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.89, 0], [0, -0.89], [0.89, 0], [0, 0.89]], "o": [[0, -0.89], [0.89, 0], [0, 0.89], [-0.89, 0], [0, 0]], "v": [[90.51, 5.16], [92.15, 3.55], [93.78, 5.16], [92.15, 6.79], [90.51, 5.16]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[100.64, 21], [97.19, 17.47], [97.19, 11.16], [94.81, 11.16], [94.81, 9.14], [95.94, 9.14], [97.47, 7.34], [97.47, 5.26], [99.8, 5.26], [99.8, 9.14], [102.54, 9.14], [102.54, 11.16], [99.8, 11.16], [99.8, 17.09], [101.51, 18.89], [102.54, 18.89], [102.54, 21], [100.64, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.58, 0], [0.02, -2.4], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.73, 0], [0, -2.93], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.92], [-1.56, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.13], [2.76, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[112.49, 13.78], [109.87, 10.99], [106.9, 14.35], [106.9, 21], [104.28, 21], [104.28, 3], [106.9, 3], [106.9, 10.73], [110.81, 8.9], [115.1, 13.39], [115.1, 21], [112.49, 21], [112.49, 13.78]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[128.58, 21.24], [122.38, 15.07], [128.5, 8.9], [134.41, 15], [134.38, 15.6], [124.95, 15.6], [128.58, 19.18], [131.67, 17.23], [134.07, 17.59], [128.58, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[125.07, 13.63], [131.77, 13.63], [128.53, 10.97], [125.07, 13.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.26], [-2.54, 0.48], [0, 0], [0, 0.5], [1.34, 0], [0.17, -1.2], [0, 0], [-3.02, 0], [-0.5, -1.9], [0, -0.77], [0, 0], [-0.62, 0], [0, 0], [0, 0], [0, 0], [0, 1.18], [0, 0], [0, 0], [2.09, 0]], "o": [[-2.02, 0], [0, -2.4], [0, 0], [0.5, -0.1], [0, -1.39], [-1.63, 0], [0, 0], [0.41, -2.33], [2.59, 0], [0.14, 0.41], [0, 0], [0, 0.82], [0, 0], [0, 0], [0, 0], [-1.22, 0], [0, 0], [0, 0], [-0.74, 1.34], [0, 0]], "v": [[139.75, 21.24], [135.79, 18.02], [140.35, 14.21], [142.49, 13.8], [143.47, 13.01], [141.22, 10.97], [138.5, 13.01], [135.94, 12.65], [141.17, 8.9], [145.9, 11.81], [146.09, 13.49], [146.09, 17.81], [146.95, 18.94], [147.38, 18.94], [147.38, 21], [146.16, 21], [144.07, 19.32], [144.07, 19.06], [143.88, 19.06], [139.75, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.13, 0], [0, 2.35], [0, 0], [0, -0.96]], "o": [[0, 0.86], [2.02, 0], [0, 0], [-1.34, 0.24], [0, 0]], "v": [[138.41, 17.81], [140.18, 19.2], [143.47, 15.36], [140.47, 16.08], [138.41, 17.81]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.36, 1.94], [0, 0], [-1.54, 0], [0, 0.96], [1.63, 0.34], [0, 2.52], [-2.81, 0], [-0.53, -1.87], [0, 0], [1.13, 0], [0, -0.84], [-1.49, -0.29], [0, -2.64], [2.95, 0]], "o": [[-2.86, 0], [0, 0], [0.41, 1.27], [1.25, 0], [0, -1.13], [-1.9, -0.38], [0, -2.02], [2.5, 0], [0, 0], [-0.38, -0.98], [-1.2, 0], [0, 1.01], [1.92, 0.43], [0, 2.3], [0, 0]], "v": [[153.28, 21.24], [148.19, 17.86], [150.35, 17.28], [153.42, 19.27], [155.53, 17.74], [152.72, 16.01], [148.48, 12.38], [152.96, 8.9], [157.79, 11.83], [155.51, 12.43], [152.99, 10.87], [150.92, 12.19], [153.66, 13.8], [157.96, 17.47], [153.28, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.61, 0]], "o": [[-3.75, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[165.56, 21.24], [159.37, 15.07], [165.49, 8.9], [171.39, 15], [171.37, 15.6], [161.94, 15.6], [165.56, 19.18], [168.66, 17.23], [171.06, 17.59], [165.56, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[162.06, 13.63], [168.75, 13.63], [165.51, 10.97], [162.06, 13.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.26], [-2.54, 0.48], [0, 0], [0, 0.5], [1.34, 0], [0.17, -1.2], [0, 0], [-3.02, 0], [-0.5, -1.9], [0, -0.77], [0, 0], [-0.62, 0], [0, 0], [0, 0], [0, 0], [0, 1.18], [0, 0], [0, 0], [2.09, 0]], "o": [[-2.02, 0], [0, -2.4], [0, 0], [0.5, -0.1], [0, -1.39], [-1.63, 0], [0, 0], [0.41, -2.33], [2.59, 0], [0.14, 0.41], [0, 0], [0, 0.82], [0, 0], [0, 0], [0, 0], [-1.22, 0], [0, 0], [0, 0], [-0.74, 1.34], [0, 0]], "v": [[182.22, 21.24], [178.26, 18.02], [182.82, 14.21], [184.96, 13.8], [185.94, 13.01], [183.68, 10.97], [180.97, 13.01], [178.4, 12.65], [183.64, 8.9], [188.36, 11.81], [188.56, 13.49], [188.56, 17.81], [189.42, 18.94], [189.85, 18.94], [189.85, 21], [188.63, 21], [186.54, 19.32], [186.54, 19.06], [186.35, 19.06], [182.22, 21.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.13, 0], [0, 2.35], [0, 0], [0, -0.96]], "o": [[0, 0.86], [2.02, 0], [0, 0], [-1.34, 0.24], [0, 0]], "v": [[180.88, 17.81], [182.65, 19.2], [185.94, 15.36], [182.94, 16.08], [180.88, 17.81]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[195.73, 21], [192.27, 17.47], [192.27, 11.16], [189.9, 11.16], [189.9, 9.14], [191.02, 9.14], [192.56, 7.34], [192.56, 5.26], [194.89, 5.26], [194.89, 9.14], [197.62, 9.14], [197.62, 11.16], [194.89, 11.16], [194.89, 17.09], [196.59, 18.89], [197.62, 18.89], [197.62, 21], [195.73, 21]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[12.43, 52.83], [15.16, 46.54], [10.32, 35.14], [13.15, 35.14], [16.51, 43.9], [19.89, 35.14], [22.75, 35.14], [15.28, 52.83], [12.43, 52.83]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[29.25, 47.24], [22.94, 41.05], [29.25, 34.9], [35.54, 41.05], [29.25, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[25.55, 41.05], [29.25, 45.13], [32.95, 41.05], [29.25, 36.99], [25.55, 41.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.66], [0, 0], [0, 0], [0, 0], [-1.54, 0], [-0.02, 2.35], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.63, 0]], "o": [[-2.9, 0], [0, 0], [0, 0], [0, 0], [0, 1.78], [1.66, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.82, 1.08], [0, 0]], "v": [[41.76, 47.24], [37.32, 43.02], [37.32, 35.14], [39.93, 35.14], [39.93, 42.51], [42.52, 45.13], [45.52, 41.77], [45.52, 35.14], [48.14, 35.14], [48.14, 47], [45.52, 47], [45.52, 45.51], [41.76, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[50.58, 47], [50.58, 35.14], [53.2, 35.14], [53.2, 37.11], [57.5, 34.9], [57.5, 37.33], [53.2, 41.48], [53.2, 47], [50.58, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.88, 0], [0, 0], [0, 0], [0, 0], [0, -1.92], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, -3.53], [0, 0], [0, 0], [0, 0], [-1.06, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[65.73, 33.44], [70.51, 29], [71.13, 29], [71.13, 31.09], [70.51, 31.09], [68.35, 33.44], [68.35, 35.14], [71.13, 35.14], [71.13, 37.16], [68.35, 37.16], [68.35, 47], [65.73, 47], [65.73, 37.16], [63.31, 37.16], [63.31, 35.14], [65.73, 35.14], [65.73, 33.44]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.26], [-2.54, 0.48], [0, 0], [0, 0.5], [1.34, 0], [0.17, -1.2], [0, 0], [-3.02, 0], [-0.5, -1.9], [0, -0.77], [0, 0], [-0.62, 0], [0, 0], [0, 0], [0, 0], [0, 1.18], [0, 0], [0, 0], [2.09, 0]], "o": [[-2.02, 0], [0, -2.4], [0, 0], [0.5, -0.1], [0, -1.39], [-1.63, 0], [0, 0], [0.41, -2.33], [2.59, 0], [0.14, 0.41], [0, 0], [0, 0.82], [0, 0], [0, 0], [0, 0], [-1.22, 0], [0, 0], [0, 0], [-0.74, 1.34], [0, 0]], "v": [[75.79, 47.24], [71.83, 44.02], [76.39, 40.21], [78.53, 39.8], [79.51, 39.01], [77.25, 36.97], [74.54, 39.01], [71.97, 38.65], [77.21, 34.9], [81.93, 37.81], [82.13, 39.49], [82.13, 43.81], [82.99, 44.94], [83.42, 44.94], [83.42, 47], [82.2, 47], [80.11, 45.32], [80.11, 45.06], [79.92, 45.06], [75.79, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.13, 0], [0, 2.35], [0, 0], [0, -0.96]], "o": [[0, 0.86], [2.02, 0], [0, 0], [-1.34, 0.24], [0, 0]], "v": [[74.45, 43.81], [76.22, 45.2], [79.51, 41.36], [76.51, 42.08], [74.45, 43.81]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[88.11, 47], [83.07, 35.14], [85.9, 35.14], [89.28, 43.93], [92.64, 35.14], [95.5, 35.14], [90.46, 47], [88.11, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[102, 47.24], [95.69, 41.05], [102, 34.9], [108.29, 41.05], [102, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[98.3, 41.05], [102, 45.13], [105.7, 41.05], [102, 36.99], [98.3, 41.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[110.19, 47], [110.19, 35.14], [112.8, 35.14], [112.8, 37.11], [117.1, 34.9], [117.1, 37.33], [112.8, 41.48], [112.8, 47], [110.19, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[118.48, 47], [118.48, 35.14], [121.1, 35.14], [121.1, 47], [118.48, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.89, 0], [0, -0.89], [0.89, 0], [0, 0.89]], "o": [[0, -0.89], [0.89, 0], [0, 0.89], [-0.89, 0], [0, 0]], "v": [[118.15, 31.16], [119.78, 29.55], [121.41, 31.16], [119.78, 32.79], [118.15, 31.16]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[128.27, 47], [124.82, 43.47], [124.82, 37.16], [122.44, 37.16], [122.44, 35.14], [123.57, 35.14], [125.11, 33.34], [125.11, 31.26], [127.43, 31.26], [127.43, 35.14], [130.17, 35.14], [130.17, 37.16], [127.43, 37.16], [127.43, 43.09], [129.14, 44.89], [130.17, 44.89], [130.17, 47], [128.27, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[137.25, 47.24], [131.06, 41.07], [137.18, 34.9], [143.08, 41], [143.06, 41.6], [133.62, 41.6], [137.25, 45.18], [140.34, 43.23], [142.74, 43.59], [137.25, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[133.74, 39.63], [140.44, 39.63], [137.2, 36.97], [133.74, 39.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.36, 1.94], [0, 0], [-1.54, 0], [0, 0.96], [1.63, 0.34], [0, 2.52], [-2.81, 0], [-0.53, -1.87], [0, 0], [1.13, 0], [0, -0.84], [-1.49, -0.29], [0, -2.64], [2.95, 0]], "o": [[-2.86, 0], [0, 0], [0.41, 1.27], [1.25, 0], [0, -1.13], [-1.9, -0.38], [0, -2.02], [2.5, 0], [0, 0], [-0.38, -0.98], [-1.2, 0], [0, 1.01], [1.92, 0.43], [0, 2.3], [0, 0]], "v": [[154.96, 47.24], [149.88, 43.86], [152.04, 43.28], [155.11, 45.27], [157.22, 43.74], [154.41, 42.01], [150.16, 38.38], [154.65, 34.9], [159.48, 37.83], [157.2, 38.43], [154.68, 36.87], [152.61, 38.19], [155.35, 39.8], [159.64, 43.47], [154.96, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.21], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.42], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.03, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.18, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.94, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 1.22], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[166.2, 47], [162.74, 43.47], [162.74, 37.16], [160.36, 37.16], [160.36, 35.14], [161.49, 35.14], [163.03, 33.34], [163.03, 31.26], [165.36, 31.26], [165.36, 35.14], [168.09, 35.14], [168.09, 37.16], [165.36, 37.16], [165.36, 43.09], [167.06, 44.89], [168.09, 44.89], [168.09, 47], [166.2, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[175.27, 47.24], [168.95, 41.05], [175.27, 34.9], [181.55, 41.05], [175.27, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[171.57, 41.05], [175.27, 45.13], [178.96, 41.05], [175.27, 36.99], [171.57, 41.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.02, 0], [0, 0], [0, -3], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.72, -1.34], [0, 0], [-2.93, 0], [0, 0], [0, 0], [0, 0]], "v": [[183.45, 47], [183.45, 35.14], [186.07, 35.14], [186.07, 37.11], [190.36, 34.9], [190.36, 37.33], [186.07, 41.48], [186.07, 47], [183.45, 47]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[196.87, 47.24], [190.68, 41.07], [196.8, 34.9], [202.71, 41], [202.68, 41.6], [193.25, 41.6], [196.87, 45.18], [199.97, 43.23], [202.37, 43.59], [196.87, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[193.37, 39.63], [200.07, 39.63], [196.83, 36.97], [193.37, 39.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.36, 1.94], [0, 0], [-1.54, 0], [0, 0.96], [1.63, 0.34], [0, 2.52], [-2.81, 0], [-0.53, -1.87], [0, 0], [1.13, 0], [0, -0.84], [-1.49, -0.29], [0, -2.64], [2.95, 0]], "o": [[-2.86, 0], [0, 0], [0.41, 1.27], [1.25, 0], [0, -1.13], [-1.9, -0.38], [0, -2.02], [2.5, 0], [0, 0], [-0.38, -0.98], [-1.2, 0], [0, 1.01], [1.92, 0.43], [0, 2.3], [0, 0]], "v": [[209.1, 47.24], [204.02, 43.86], [206.18, 43.28], [209.25, 45.27], [211.36, 43.74], [208.55, 42.01], [204.3, 38.38], [208.79, 34.9], [213.62, 37.83], [211.34, 38.43], [208.82, 36.87], [206.75, 38.19], [209.49, 39.8], [213.78, 43.47], [209.1, 47.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.26], [-2.54, 0.48], [0, 0], [0, 0.5], [1.34, 0], [0.17, -1.2], [0, 0], [-3.02, 0], [-0.5, -1.9], [0, -0.77], [0, 0], [-0.62, 0], [0, 0], [0, 0], [0, 0], [0, 1.18], [0, 0], [0, 0], [2.09, 0]], "o": [[-2.02, 0], [0, -2.4], [0, 0], [0.5, -0.1], [0, -1.39], [-1.63, 0], [0, 0], [0.41, -2.33], [2.59, 0], [0.14, 0.41], [0, 0], [0, 0.82], [0, 0], [0, 0], [0, 0], [-1.22, 0], [0, 0], [0, 0], [-0.74, 1.34], [0, 0]], "v": [[62.07, 73.24], [58.11, 70.02], [62.67, 66.21], [64.8, 65.8], [65.79, 65.01], [63.53, 62.97], [60.82, 65.01], [58.25, 64.65], [63.48, 60.9], [68.21, 63.81], [68.4, 65.49], [68.4, 69.81], [69.27, 70.94], [69.7, 70.94], [69.7, 73], [68.48, 73], [66.39, 71.32], [66.39, 71.06], [66.2, 71.06], [62.07, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.13, 0], [0, 2.35], [0, 0], [0, -0.96]], "o": [[0, 0.86], [2.02, 0], [0, 0], [-1.34, 0.24], [0, 0]], "v": [[60.72, 69.81], [62.5, 71.2], [65.79, 67.36], [62.79, 68.08], [60.72, 69.81]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.54, 0], [0.19, -2.02], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.61, 0], [0, -2.66], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.78], [-1.54, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.08], [2.9, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[79.38, 65.63], [76.79, 62.99], [73.81, 65.87], [73.81, 73], [71.2, 73], [71.2, 61.14], [73.81, 61.14], [73.81, 62.61], [77.56, 60.9], [82, 65.13], [82, 73], [79.38, 73], [79.38, 65.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.82], [-3.22, 0], [-0.74, -1.32], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.78, 0]], "o": [[-3.22, 0], [0, -3.79], [1.78, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.74, 1.32], [0, 0]], "v": [[89.53, 73.24], [83.79, 67.07], [89.53, 60.9], [93.59, 62.99], [93.59, 55], [96.2, 55], [96.2, 73], [93.59, 73], [93.59, 71.18], [89.53, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.11, 0], [0, 2.45], [2.09, 0], [0, -2.4]], "o": [[0, 2.45], [2.09, 0], [0, -2.4], [-2.11, 0], [0, 0]], "v": [[86.41, 67.07], [90.06, 71.06], [93.68, 67.07], [90.06, 63.09], [86.41, 67.07]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.79], [-3.7, 0], [0, -3.74], [3.7, -0.02]], "o": [[-3.7, -0.02], [0, -3.74], [3.7, 0], [0, 3.79], [0, 0]], "v": [[109.89, 73.24], [103.58, 67.05], [109.89, 60.9], [116.18, 67.05], [109.89, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.21, 0], [0, 2.42], [2.18, 0], [0, -2.38]], "o": [[0, 2.42], [2.18, 0], [0, -2.38], [-2.21, 0], [0, 0]], "v": [[106.19, 67.05], [109.89, 71.13], [113.58, 67.05], [109.89, 62.99], [106.19, 67.05]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.54, 0], [0.19, -2.02], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.61, 0], [0, -2.66], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.78], [-1.54, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.08], [2.9, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[126.28, 65.63], [123.69, 62.99], [120.71, 65.87], [120.71, 73], [118.1, 73], [118.1, 61.14], [120.71, 61.14], [120.71, 62.61], [124.46, 60.9], [128.9, 65.13], [128.9, 73], [126.28, 73], [126.28, 65.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[131.22, 73], [131.22, 55], [133.84, 55], [133.84, 73], [131.22, 73]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[136.26, 73], [136.26, 61.14], [138.88, 61.14], [138.88, 73], [136.26, 73]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.89, 0], [0, -0.89], [0.89, 0], [0, 0.89]], "o": [[0, -0.89], [0.89, 0], [0, 0.89], [-0.89, 0], [0, 0]], "v": [[135.92, 57.16], [137.56, 55.55], [139.19, 57.16], [137.56, 58.79], [135.92, 57.16]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.54, 0], [0.19, -2.02], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.61, 0], [0, -2.66], [0, 0], [0, 0], [0, 0]], "o": [[0, -1.78], [-1.54, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.82, -1.08], [2.9, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[149.48, 65.63], [146.89, 62.99], [143.92, 65.87], [143.92, 73], [141.3, 73], [141.3, 61.14], [143.92, 61.14], [143.92, 62.61], [147.66, 60.9], [152.1, 65.13], [152.1, 73], [149.48, 73], [149.48, 65.63]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.77], [-3.6, 0], [0, -3.98], [0, 0], [0, 0], [-1.97, 0], [-0.55, 1.25], [0, 0], [2.62, 0]], "o": [[-3.74, 0], [0, -3.79], [3.38, 0], [0, 0], [0, 0], [0.14, 2.26], [1.44, 0], [0, 0], [-0.79, 2.3], [0, 0]], "v": [[160.09, 73.24], [153.9, 67.07], [160.02, 60.9], [165.92, 67], [165.9, 67.6], [156.46, 67.6], [160.09, 71.18], [163.18, 69.23], [165.58, 69.59], [160.09, 73.24]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [1.68, 0], [0.36, -1.8]], "o": [[0, 0], [-0.26, -1.75], [-1.82, 0], [0, 0]], "v": [[156.58, 65.63], [163.28, 65.63], [160.04, 62.97], [156.58, 65.63]]}}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 6}, {"ty": 4, "nm": "Overlay 1 (Border)", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [222.704, 145.5]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [222.5, 299.5]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [445.408, 0], [445.408, 291], [0, 291], [0, 0]]}}}, {"ty": "st", "nm": "", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 0}, "w": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [1], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [1], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [1], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [1], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [1], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [1], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [1], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [1], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [1], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [1], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [1], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [1], "t": 660}, {"s": [1], "t": 720}]}, "c": {"a": 0, "k": [0, 0, 0]}}], "ind": 7}, {"ty": 4, "nm": "Overlay 1 (Mask)", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "td": 1, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "", "it": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [445.408, 0], [445.408, 291], [0, 291], [0, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [445.408, 0], [445.408, 291], [0, 291], [0, 0]]}], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [445.408, 0], [445.408, 291], [0, 291], [0, 0]]}], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [445.408, 0], [445.408, 291], [0, 291], [0, 0]]}], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [445.408, 0], [445.408, 291], [0, 291], [0, 0]]}], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [445.408, 0], [445.408, 291], [0, 291], [0, 0]]}], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [445.408, 0], [445.408, 291], [0, 291], [0, 0]]}], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [445.408, 0], [445.408, 291], [0, 291], [0, 0]]}], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [445.408, 0], [445.408, 291], [0, 291], [0, 0]]}], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [445.408, 0], [445.408, 291], [0, 291], [0, 0]]}], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [445.408, 0], [445.408, 291], [0, 291], [0, 0]]}], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [445.408, 0], [445.408, 291], [0, 291], [0, 0]]}], "t": 660}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [445.408, 0], [445.408, 291], [0, 291], [0, 0]]}], "t": 720}]}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [1, 0, 0]}, "r": 2, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.704, 145.5], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.704, 145.5], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.704, 145.5], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.704, 145.5], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.704, 145.5], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.704, 145.5], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.704, 145.5], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.704, 145.5], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.704, 145.5], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.704, 145.5], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.704, 145.5], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.704, 145.5], "t": 660}, {"s": [222.704, 145.5], "t": 720}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.5, 299.5], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.5, 299.5], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.5, 299.5], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.5, 299.5], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.5, 299.5], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.5, 299.5], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.5, 299.5], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.5, 299.5], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.5, 299.5], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.5, 299.5], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.5, 299.5], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.5, 299.5], "t": 660}, {"s": [222.5, 299.5], "t": 720}]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 660}, {"s": [0], "t": 720}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 8}, {"ty": 2, "nm": "Overlay 1", "sr": 1, "st": 0, "op": 721, "ip": 0, "tt": 1, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [187.5, 122.5]}, "s": {"a": 0, "k": [118.776, 118.776]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [222.5, 299.5]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "ad62bbe3d1590e8307e6a8a95f33cf3a7ba3efdc", "ind": 9}, {"ty": 4, "nm": "card (Border)", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 660}, {"s": [40, 40], "t": 720}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34, 287.999], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34, 287.999], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 99], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 99], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.338, 281.999], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.338, 281.999], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.339, 349.999], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.339, 349.999], "t": 660}, {"s": [34.001, 287.998], "t": 720}]}, "r": {"a": 0, "k": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 660}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 720}]}}, {"ty": "st", "nm": "", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 0}, "w": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 660}, {"s": [0.25], "t": 720}]}, "c": {"a": 0, "k": [0, 0, 0]}}], "ind": 10}, {"ty": 4, "nm": "card (Mask)", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "td": 1, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "", "it": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 660}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 720}]}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [1, 0, 0]}, "r": 2, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 660}, {"s": [40, 40], "t": 720}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34, 287.999], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34, 287.999], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 99], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 99], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.338, 281.999], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.338, 281.999], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.339, 349.999], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.339, 349.999], "t": 660}, {"s": [34.001, 287.998], "t": 720}]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 660}, {"s": [10], "t": 720}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 11}, {"ty": 2, "nm": "card", "sr": 1, "st": 0, "op": 721, "ip": 0, "tt": 1, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [160.5, 160]}, "s": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [49.844, 50], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [49.844, 50], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 660}, {"s": [24.922, 25], "t": 720}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34, 287.999], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34, 287.999], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 99], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 99], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.338, 281.999], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.338, 281.999], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.339, 349.999], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.339, 349.999], "t": 660}, {"s": [34.001, 287.998], "t": 720}]}, "r": {"a": 0, "k": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "a52b507982fb96d507ba18c651078adf30f42e93", "ind": 12}, {"ty": 4, "nm": "lighting (Border)", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 660}, {"s": [40, 40], "t": 720}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324, 355.999], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324, 355.999], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 407.999], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 407.999], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120, 349.999], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120, 349.999], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34, 281.999], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34, 281.999], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.893, 107.676], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.893, 107.676], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [411.339, 281.338], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [411.339, 281.338], "t": 660}, {"s": [323.999, 355.998], "t": 720}]}, "r": {"a": 0, "k": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 660}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 720}]}}, {"ty": "st", "nm": "", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 0}, "w": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 660}, {"s": [0.25], "t": 720}]}, "c": {"a": 0, "k": [0, 0, 0]}}], "ind": 13}, {"ty": 4, "nm": "lighting (Mask)", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "td": 1, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "", "it": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 660}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 720}]}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [1, 0, 0]}, "r": 2, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 660}, {"s": [40, 40], "t": 720}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324, 355.999], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324, 355.999], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 407.999], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 407.999], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120, 349.999], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120, 349.999], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34, 281.999], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34, 281.999], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.893, 107.676], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.893, 107.676], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [411.339, 281.338], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [411.339, 281.338], "t": 660}, {"s": [323.999, 355.998], "t": 720}]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 660}, {"s": [10], "t": 720}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 14}, {"ty": 2, "nm": "lighting", "sr": 1, "st": 0, "op": 721, "ip": 0, "tt": 1, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [160.5, 160]}, "s": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [49.844, 50], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [49.844, 50], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 660}, {"s": [24.922, 25], "t": 720}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324, 355.999], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324, 355.999], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 407.999], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 407.999], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120, 349.999], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120, 349.999], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34, 281.999], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34, 281.999], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.893, 107.676], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.893, 107.676], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [411.339, 281.338], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [411.339, 281.338], "t": 660}, {"s": [323.999, 355.998], "t": 720}]}, "r": {"a": 0, "k": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "94c9bcf34420c82b674cf2870411ad22195e72a6", "ind": 15}, {"ty": 4, "nm": "pig (Border)", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 660}, {"s": [40, 40], "t": 720}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120, 355.999], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120, 355.999], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.001, 287.999], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.001, 287.999], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.338, 93], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.338, 93], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.339, 281.999], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.339, 281.999], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 660}, {"s": [120.001, 355.998], "t": 720}]}, "r": {"a": 0, "k": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 660}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 720}]}}, {"ty": "st", "nm": "", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 0}, "w": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 660}, {"s": [0.25], "t": 720}]}, "c": {"a": 0, "k": [0, 0, 0]}}], "ind": 16}, {"ty": 4, "nm": "pig (Mask)", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "td": 1, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "", "it": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 660}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 720}]}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [1, 0, 0]}, "r": 2, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 660}, {"s": [40, 40], "t": 720}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120, 355.999], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120, 355.999], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.001, 287.999], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.001, 287.999], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.338, 93], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.338, 93], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.339, 281.999], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.339, 281.999], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 660}, {"s": [120.001, 355.998], "t": 720}]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 660}, {"s": [10], "t": 720}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 17}, {"ty": 2, "nm": "pig", "sr": 1, "st": 0, "op": 721, "ip": 0, "tt": 1, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [160.5, 160]}, "s": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [49.844, 50], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [49.844, 50], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 660}, {"s": [24.922, 25], "t": 720}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120, 355.999], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120, 355.999], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.001, 287.999], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.001, 287.999], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.338, 93], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.338, 93], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.339, 281.999], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.339, 281.999], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 660}, {"s": [120.001, 355.998], "t": 720}]}, "r": {"a": 0, "k": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "1d87faf5d3ec1869a0b3ce813048a46f25fe9ced", "ind": 18}, {"ty": 4, "nm": "house (Border)", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 660}, {"s": [40, 40], "t": 720}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 407.999], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 407.999], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.001, 355.999], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.001, 355.999], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.34, 93], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.34, 93], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.339, 281.999], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.339, 281.999], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 660}, {"s": [222.001, 407.998], "t": 720}]}, "r": {"a": 0, "k": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 660}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 720}]}}, {"ty": "st", "nm": "", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 0}, "w": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 660}, {"s": [0.25], "t": 720}]}, "c": {"a": 0, "k": [0, 0, 0]}}], "ind": 19}, {"ty": 4, "nm": "house (Mask)", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "td": 1, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "", "it": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 660}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 720}]}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [1, 0, 0]}, "r": 2, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 660}, {"s": [40, 40], "t": 720}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 407.999], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 407.999], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.001, 355.999], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.001, 355.999], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.34, 93], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.34, 93], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.339, 281.999], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.339, 281.999], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 660}, {"s": [222.001, 407.998], "t": 720}]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 660}, {"s": [10], "t": 720}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 20}, {"ty": 2, "nm": "house", "sr": 1, "st": 0, "op": 721, "ip": 0, "tt": 1, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [160.5, 160]}, "s": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [49.844, 50], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [49.844, 50], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 660}, {"s": [24.922, 25], "t": 720}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 407.999], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222, 407.999], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.001, 355.999], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.001, 355.999], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.34, 93], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.34, 93], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.339, 281.999], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410.339, 281.999], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.339, 349.999], "t": 660}, {"s": [222.001, 407.998], "t": 720}]}, "r": {"a": 0, "k": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "ec83c619ceaa3689f8d8c65155d31e788e0fdea1", "ind": 21}, {"ty": 4, "nm": "bag (Border)", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 660}, {"s": [40, 40], "t": 720}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410, 287.999], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410, 287.999], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324, 355.999], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324, 355.999], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [112.339, 341.999], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [112.339, 341.999], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.34, 93], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.34, 93], "t": 660}, {"s": [410.001, 287.998], "t": 720}]}, "r": {"a": 0, "k": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 660}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 720}]}}, {"ty": "st", "nm": "", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 0}, "w": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 660}, {"s": [0.25], "t": 720}]}, "c": {"a": 0, "k": [0, 0, 0]}}], "ind": 22}, {"ty": 4, "nm": "bag (Mask)", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "td": 1, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "", "it": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 660}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 720}]}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [1, 0, 0]}, "r": 2, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 660}, {"s": [40, 40], "t": 720}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410, 287.999], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410, 287.999], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324, 355.999], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324, 355.999], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [112.339, 341.999], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [112.339, 341.999], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.34, 93], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.34, 93], "t": 660}, {"s": [410.001, 287.998], "t": 720}]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 660}, {"s": [10], "t": 720}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 23}, {"ty": 2, "nm": "bag", "sr": 1, "st": 0, "op": 721, "ip": 0, "tt": 1, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [160.5, 160]}, "s": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [49.844, 50], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [49.844, 50], "t": 660}, {"s": [24.922, 25], "t": 720}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410, 287.999], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410, 287.999], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324, 355.999], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324, 355.999], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [112.339, 341.999], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [112.339, 341.999], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.34, 93], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.34, 93], "t": 660}, {"s": [410.001, 287.998], "t": 720}]}, "r": {"a": 0, "k": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "38986fb172e6bca540d4f316f80de8b9228fd150", "ind": 24}, {"ty": 4, "nm": "heart (Border)", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 660}, {"s": [80, 80], "t": 720}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.001, 99], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.001, 99], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410, 287.999], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410, 287.999], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.338, 349.999], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.338, 349.999], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.339, 349.999], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.339, 349.999], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 660}, {"s": [222.002, 98.996], "t": 720}]}, "r": {"a": 0, "k": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 660}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 720}]}}, {"ty": "st", "nm": "", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 0}, "w": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.25], "t": 660}, {"s": [0.25], "t": 720}]}, "c": {"a": 0, "k": [0, 0, 0]}}], "ind": 25}, {"ty": 4, "nm": "heart (<PERSON>)", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "td": 1, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "nm": "", "it": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [80, 0], [80, 80], [0, 80], [0, 0]]}], "t": 660}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 0], [160, 0], [160, 160], [0, 160], [0, 0]]}], "t": 720}]}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [1, 0, 0]}, "r": 2, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [80, 80], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [40, 40], "t": 660}, {"s": [80, 80], "t": 720}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.001, 99], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.001, 99], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410, 287.999], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410, 287.999], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.338, 349.999], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.338, 349.999], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.339, 349.999], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.339, 349.999], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 660}, {"s": [222.002, 98.996], "t": 720}]}, "r": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [10], "t": 660}, {"s": [10], "t": 720}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 26}, {"ty": 2, "nm": "heart", "sr": 1, "st": 0, "op": 721, "ip": 0, "tt": 1, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [160.5, 160]}, "s": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [49.844, 50], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [49.844, 50], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [24.922, 25], "t": 660}, {"s": [49.844, 50], "t": 720}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.001, 99], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.001, 99], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410, 287.999], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [410, 287.999], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.338, 349.999], "t": 240}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [324.338, 349.999], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [222.339, 401.999], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.339, 349.999], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [120.339, 349.999], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [34.339, 281.999], "t": 660}, {"s": [222.002, 98.996], "t": 720}]}, "r": {"a": 0, "k": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "refId": "d8be59fb6128677ed1f8681653f4adbecc6b50fa", "ind": 27}, {"ty": 4, "nm": "Podium 1 Bg", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [222.5, 222.5]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [222.5, 222.5]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[445, 0], [445, 445], [0, 445], [0, 0]]}}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [0, 0, 0]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 28}], "v": "5.7.0", "fr": 60, "op": 720, "ip": 0, "assets": [{"nm": "[Asset] Logo", "id": "1", "layers": [{"ty": 4, "nm": "Vector", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [19.89, 19.2]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [191.11, 30.23]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 660}, {"s": [100], "t": 720}]}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.16, 0.29], [0.38, 0.88], [-0.09, 0.63], [-0.65, 0], [-1.84, -0.03], [0, -1.27], [0, -10.39], [0, -0.77], [1.25, 0], [1.67, 0], [-0.76, 1.35], [-0.48, 0.9], [-0.14, 0.11], [1.11, -0.93], [6.89, 5.69], [1.03, 3.92], [-1.47, 3.78], [-3.41, 2.19], [-4.04, -0.25], [-3.23, -3.86], [0, 0], [0, 0]], "o": [[-0.06, -0.35], [-0.45, -0.85], [-0.24, -0.59], [0.09, -0.68], [1.84, 0], [1.07, 0.02], [0, 10.4], [0, 0.77], [0, 1.28], [-1.67, 0], [-1.52, 0], [0.5, -0.89], [0.17, -0.31], [-1.1, 0.93], [-6.58, 5.51], [-3.14, -2.56], [-1.03, -3.92], [1.47, -3.78], [3.41, -2.19], [5.06, 0.31], [0, 0], [0, 0], [0, 0]], "v": [[33.35, 7.04], [33.15, 6], [31.7, 3.5], [31.47, 1.64], [32.83, 0.71], [38.36, 0.71], [39.78, 2.48], [39.78, 33.66], [39.78, 35.96], [38.12, 37.67], [33.1, 37.67], [31.59, 35.02], [33.16, 32.39], [33.43, 31.55], [30.12, 34.35], [7.04, 34.04], [0.63, 24.05], [1.31, 12.2], [8.83, 3.02], [20.3, 0.04], [32.64, 6.56], [33.07, 7.08], [33.35, 7.04]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.31, -0.55], [1, -1.01], [-0.01, -2.88], [-2.04, -2.03], [-2.87, 0.01], [0.02, 5.91], [2.04, 2.03], [2.88, 0.01], [0, 0]], "o": [[-1.42, 0], [-1.31, 0.55], [-2.02, 2.04], [0.01, 2.88], [2.04, 2.03], [5.95, -0.01], [-0.03, -2.89], [-2.04, -2.03], [0, 0], [0, 0]], "v": [[19.15, 8.33], [15.01, 9.17], [11.51, 11.53], [8.36, 19.21], [11.56, 26.87], [19.22, 30.02], [30.07, 19.19], [26.84, 11.51], [19.15, 8.33], [19.15, 8.33]]}}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [0.169, 0.859, 0.561]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 1}, {"ty": 4, "nm": "Vector", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [19.92, 19.24]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [107.9, 30.24]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 660}, {"s": [100], "t": 720}]}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.17, 0.32], [0.5, 0.72], [0.04, 0.4], [-0.17, 0.36], [-0.72, 0], [-1.59, 0], [0, -1.21], [0, -11.22], [1.2, -0.01], [1.7, 0], [-0.73, 1.29], [-0.49, 0.9], [-0.07, 0.38], [0, 0], [0.09, -0.14], [6.87, 5.72], [-2.03, 7.84], [-7.98, 1.45], [-4.37, -5.12], [0, 0], [0, 0]], "o": [[-0.07, -0.37], [-0.41, -0.78], [-0.24, -0.32], [-0.04, -0.4], [0.35, -0.78], [1.59, 0], [1.22, 0.01], [0, 11.22], [0, 1.22], [-1.7, 0], [-1.47, 0], [0.5, -0.89], [0.17, -0.32], [0, 0], [-0.05, 0.16], [-7.22, 9.16], [-6.22, -5.18], [2.03, -7.84], [6.63, -1.2], [0, 0], [0, 0], [0, 0]], "v": [[33.41, 7.08], [33.17, 5.99], [31.81, 3.73], [31.38, 2.63], [31.58, 1.48], [33.43, 0.74], [38.19, 0.74], [39.83, 2.38], [39.83, 36.04], [38.2, 37.7], [33.1, 37.7], [31.61, 35.09], [33.19, 32.46], [33.42, 31.35], [33.05, 31.13], [32.85, 31.59], [7.27, 34.18], [0.71, 14.43], [16.09, 0.32], [32.62, 6.52], [33.16, 7.15], [33.41, 7.08]]}}}, {"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2.03, -2.03], [0, -2.88], [-2.03, -2.03], [-2.87, 0], [-0.03, 5.93], [5.96, 0], [0, 0]], "o": [[-2.87, 0], [-2.03, 2.03], [0, 2.88], [2.03, 2.03], [5.96, 0], [0.03, -5.93], [0, 0], [0, 0]], "v": [[19.24, 8.36], [11.58, 11.54], [8.41, 19.2], [11.58, 26.87], [19.24, 30.05], [30.12, 19.26], [19.24, 8.36], [19.24, 8.36]]}}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [0.169, 0.859, 0.561]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 2}, {"ty": 4, "nm": "Vector", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [17.89, 27.63]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [149.96, 39.37]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 660}, {"s": [100], "t": 720}]}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.43, -0.31], [4.7, 10.07], [0, 2.65], [0, 6.11], [-1.18, 0.02], [-1.73, -0.02], [0, -1.25], [-0.02, -5.99], [-4.16, -1.16], [-0.45, 5.8], [0, 1.48], [0, 4.91], [-1.36, 0], [-1.64, 0], [0, -1.26], [0, -12.47], [2.34, -2.6], [6.16, -0.05], [0.93, 0.02], [0.01, 0.95], [-0.02, 1.88], [-0.99, 0.03], [-1.38, 0.14], [-1.38, 0.74], [-0.98, 1.22], [0.22, 1.73], [-0.96, 0.71], [0.28, 0.71]], "o": [[-0.44, 0.33], [-8.78, 6.3], [-1.13, -2.4], [0, -6.11], [0, -1.18], [1.73, -0.02], [1.24, 0], [0, 5.99], [0.02, 4.56], [5.57, 1.55], [0.12, -1.47], [0, -4.91], [0, -1.39], [1.65, 0], [1.3, 0], [0, 12.47], [0, 3.6], [-4.11, 4.55], [-0.93, 0], [-0.93, -0.02], [-0.02, -1.87], [0, -0.96], [1.39, -0.04], [1.56, -0.14], [1.38, -0.74], [1.1, -1.33], [-0.15, -1.18], [0.58, -0.44], [0, 0]], "v": [[28.81, 33.85], [27.52, 34.82], [1.73, 27.61], [0, 19.95], [0, 1.63], [1.59, 0.01], [6.77, 0.01], [8.45, 1.72], [8.45, 19.7], [15.48, 29.29], [27.27, 21.02], [27.33, 16.59], [27.33, 1.85], [29.14, 0.01], [34.08, 0.01], [35.78, 1.67], [35.78, 39.08], [31.92, 48.29], [16.52, 55.26], [13.71, 55.26], [12.3, 53.83], [12.3, 48.21], [13.74, 46.82], [17.9, 46.71], [22.35, 45.38], [25.94, 42.42], [27.4, 37.85], [28.56, 35.07], [28.81, 33.85]]}}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [0.169, 0.859, 0.561]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 3}, {"ty": 4, "nm": "Vector", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [27.91, 18.84]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [56.91, 29.88]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 660}, {"s": [100], "t": 720}]}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.1, 0.07], [-0.09, 0.11], [0.31, 0.48], [-0.42, 0.38], [-6.01, -3.66], [-0.05, -6.35], [0, -6.33], [1.48, 0], [1.65, 0], [0.12, 0.95], [-0.01, 0.28], [0, 6.22], [1.95, 1.71], [2.67, -0.91], [0.69, -2.99], [-0.02, -0.68], [0, -4.77], [0.03, -0.28], [0.87, 0], [1.61, 0], [0, 1.31], [0.06, 4.8], [0.38, 1.1], [4.33, -3.77], [0, -2.61], [-0.01, -6.31], [1.66, 0], [1.61, 0], [0.13, 0.9], [-0.01, 0.2], [-0.09, 6.3], [-5.51, 3.3], [-3.5, -4.88], [-0.2, -0.28]], "o": [[0.11, -0.05], [0.1, -0.1], [-0.27, -0.52], [-0.31, -0.48], [5.22, -4.68], [5.36, 3.27], [0.05, 6.35], [0, 1.49], [-1.64, 0], [-0.95, 0], [-0.03, -0.28], [0, -6.22], [0, -2.59], [-2.1, -1.84], [-2.9, 1], [-0.14, 0.67], [-0.02, 4.77], [0.01, 0.28], [-0.14, 0.85], [-1.62, 0], [-1.29, 0], [0, -4.8], [-0.01, -1.16], [-1.59, -4.35], [-1.97, 1.72], [-0.01, 6.31], [0, 1.69], [-1.61, 0], [-0.91, 0], [-0.02, -0.2], [0, -6.3], [0.09, -6.38], [8.33, -4.99], [0.17, 0.24], [0, 0]], "v": [[29.07, 7.27], [29.39, 7.08], [29.67, 6.76], [28.86, 5.24], [29.04, 4.1], [47.5, 2.31], [55.78, 16.75], [55.79, 35.75], [53.87, 37.68], [48.94, 37.68], [47.43, 36.34], [47.41, 35.49], [47.41, 16.83], [44.61, 10.32], [37.4, 8.79], [32.03, 14.87], [31.85, 16.9], [31.85, 31.22], [31.82, 32.07], [30.38, 33.28], [25.53, 33.28], [23.62, 31.36], [23.59, 16.95], [23, 13.55], [11.35, 10.16], [8.4, 16.7], [8.4, 35.62], [6.37, 37.69], [1.53, 37.69], [0.05, 36.41], [0.03, 35.81], [0.05, 16.9], [8.37, 2.19], [28.52, 6.51], [29.07, 7.27]]}}}, {"ty": "fl", "nm": "", "c": {"a": 0, "k": [0.169, 0.859, 0.561]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 4}, {"ty": 4, "nm": "Logo Bg", "sr": 1, "st": 0, "op": 721, "ip": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [120, 39]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [120, 39]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 180}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 300}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 360}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 420}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 480}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 540}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 600}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0], "t": 660}, {"s": [100], "t": 720}]}}, "shapes": [{"ty": "sh", "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[240, 0], [240, 78], [0, 78], [0, 0]]}}}], "ind": 5}]}, {"id": "ad62bbe3d1590e8307e6a8a95f33cf3a7ba3efdc", "e": 1, "w": 375, "h": 245, "p": "data:image/webp;base64,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", "u": ""}, {"id": "a52b507982fb96d507ba18c651078adf30f42e93", "e": 1, "w": 321, "h": 320, "p": "data:image/webp;base64,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", "u": ""}, {"id": "94c9bcf34420c82b674cf2870411ad22195e72a6", "e": 1, "w": 321, "h": 320, "p": "data:image/webp;base64,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", "u": ""}, {"id": "1d87faf5d3ec1869a0b3ce813048a46f25fe9ced", "e": 1, "w": 321, "h": 320, "p": "data:image/webp;base64,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", "u": ""}, {"id": "ec83c619ceaa3689f8d8c65155d31e788e0fdea1", "e": 1, "w": 321, "h": 320, "p": "data:image/webp;base64,UklGRjgYAABXRUJQVlA4WAoAAAAQAAAAQAEAPwEAQUxQSL8HAAAB8IBtmylH2/5VtREbk9x5grHxjG3btm3btn17bNs246Qn6gzi7l69qh5z1VX1MBExAajQ/4X+L/R/YZExxsJG5VulUbPmzdt26dCySUksRrRunt7ewSVqNGnRssvc70+evv4uITnZkpKWakl8vLe1HxYX2BgYVaLxkKmzNpy8//Tp66QsJ/2b5t0e6SIWtK4+fiHl6zYdvfOXXy+8tKRnFcgyIYT+PUne/kgRoDUYjb6NhoybuGTfuesPU5z0H5scDuUxrDUavYrVaNisdb+pG/f++DaP/lOTSy58pXIvWadZh+k7fz56711SavrnXInQf/qCvnrOUbsFRpSpULnZkCnTf3wcl5CS7aCKax2O+QX71N506Pyd5zHxiVYboYpNYstwi6n7xWzKxtNhfIL9dmYQysj85V6YR/z3OSk7s9eVVXEHjrhooyx1WC81MHFGhRt2ylrLSK7AZW5QBucPVHNExFnK5Jc1+MH/OGX0CR0vaNbnsiqxIicYB9kpq6WlZi5Qz0yn7E7ppeIAbadvlOEk+rfww+2TKNPJjXDwhd2ijHfO1wGv5D3K/NyxGHRlzjjZR+MqqwBX/K5EIXizItzKXJYpCB3nfKDmfkKiQJQnGGAWsFWiYLT0xBBzXVVAAfm6NMBCDkgUkuRSALjcVtkpLO0r3IBlXldAoZnZXwsq46ivFJzkZUMMKPVIK4XotSg46btlEZDIh0xg6ptAgeocqoWRrn8OgQr90g5Gdd9QuJL7IQBSV/tAAEOdewPAgys8prC1bTEAB1e9SaGbPUgDGlzpngM8NKmGCjI+h5wUvuREIGDMP+dQCDu2acHitVimMLa31gLFc7FEoZzSByZes7IomMmLKIio5uZRSJ/0gYeq8ycK6q/LNNDAbVMosDNbqIDRIZpAi34shyGBi8USCm7ykzckGj6iEHdu8YSD11UCMprWDQyRFwgFekJdIPjsdFCoyzd9QeC+0Ubh7tzuCgCvlfkU8l8maJhnmJpJYR9XRcU47fhcAjxyuQLjuiRR8DuPmFmmav2VcqC0wYVhjd5TLvzSVcMqVZ2XlBM/VGMULvuQ8ILjkJlNkQ8I5cfZJhaVukkoR6bPVLMn9IhMudJakTk+JyXKmU+LMsZlsp3ypnODninmA3mUPwuGaxniPcNJOZTE18XM0EzMp3x6pxhmhHr4Z8qp5PB3bMDNYim3OuYbmdDuFeEXau2hUj5cO5ZQnrWEKF/jt5RzzxdROHXUS5l3bCu1ylb2BuVfa2usYDjggMRBzoO+ChZ+WqI8nDVMuQJOOigXk/tqpXI9YKecnBKuUOa1MuXl3I5YmbolU262z9YoUkAc4Sey36hEutGEctQBkxJFXaMcLS3XKVG1tzyV2xkpcdMEnsqsqEgtEnnqnlaRar7jqOwRSJEjr/CTbZWXMhnmclPOnmCsTCjUykf2V8O9kFJr9su8Q+zZlucrK+iwYqHit/nm25Ofp7YKMWOk5Lj0JRu3RPcpH+lv1iLFx+ELEwifkL0mxEocNOiHt/kcIm/RMwMh5FJ78qm03AJJJlyxVseSP6nyq9aup5UnFqqZ88c4liOc0xGbY3hicqH/FGaS8JHGCx/HSOFT0F/45PUQPrmdhU9OW+GT1VT4fK4qfKxlGBXNEWlRjPrIExGMes8T4Yx6LX5eiJ8n4uex+Hkofm6Jn4vi56z4OSV+DomfH8TPXvGzVfysEz+rxM9S8bNE/CwQP3PFz2zxM138TBE/k/6d4JP4sfxG+CSFCZ/474RPXIjwiRE/0UWEz7tg4fM6UPg88xc+T3yFz0Mf4fPAW/jcFz93PIXPDXfhc9VN+FwwC5/TRuFzVMsqwg8/qBg1XuaHg0j4HGDVOPEzWuKH/awabucGMotVA/K5Ib8xqzp84wX5ZCirqlt4wT7JxKqysbyQVAKxOuwxJ9gWqZmlPsAJV8MQu/tzgfSkpJphlbM4wH6iDmK5y3Y79GzvJgWomIYqpQMvc0E5NWI87p4JNmJ/uKqlBwKgaVgMARiRMm5vbFtEj2CIG1xwQIt8PDerlgdGcNQUXfghh4BJzrLcXVQ9WI+AicO7rXicboNQ3seNPcrqEUixxrtopxFTf7qd8DVfBgopSDk0uWWoXoVAq9J7VGwxftvPr/PgQVJ+GVXNhBGYdaZiPWcfu/niCxDkT1fW1XVTIXBrAyMqtd8abSOyLePZw4ePHifkyGzKe7OxZbg7grt7mYoVyoRqEEIq76IVa41et/9udFIeO2TL8bEVXBBnakKKV+o+cvLBe++/ZEtE0UhWwq7RNTwQx7rWabtwx093UrIlokQk5+W2biGIe7FWb/IILNtr2t47T+IybUQ57Kk3FlQNNGDEzyqzb2jtnsvPvnUoQ+6D9S39dIjHdW4hVdru/fpPRlLOTqwcYMSI412Hp5F/HmfK7rG1fBH34+JLjz5NsHy1k38wZ9rdPR0CkSBUmcNLVWo1dPLyS9HxyV9ybeTvR3Ktr1fVD9ZiJCA1fnU7Ttp98sM3u5P8zYg94dcRpTASmNjgGVSuWvPJWw99zMh2Ogn5S4gs5cXs613RT4fEqC6oaIcJU7eef5Rqk2RZyk95dGr12E5BGiRYscHVt2yN2nXr1q5W2s9Njwr9X+j/Qv8X+v//PQUAVlA4IFIQAACQaQCdASpBAUABPm02lkgkIyenpdUZkPANiWVu5VARTgAjaCSjdL/VdqDJP0X9F+Uf5AfPVdH879++grOR2Y/rfvH+bP+/9bXmB/pl+qnX58wv7Xftn70vpj/wnqEfy7/fda16DP7QemZ+1nwlftl+6PtL//LOnv8p+IH61wY3iIDfEZf27Yl6hP8t/uXpVeub9zvYw/XYjSn1ygflwobUrYJPYG2+nTchT65QPy4UNqVsEnsDbfTpuQpqV9Q1bTyPUsW0j2/zfWQs27Db7MeZu9LeI4tFt9dsMkRIJhHq8fUJVsCwTBJ7A23cvBFtdtZQcHnTyp4GX6JsQebUrV5APP66Y9KKtFaku9rHaRVgdQUS4Zitg0PKWWU+uUB1hQbrJESgbY6sn79h+mR5bW0JQLj039f5hLOdiZGY182G6Gf0LjQzQQAt/PymHNDGTstThQ2o5WjBL5DrXu3w7YZ7yPkjuyZeo1syc4ew4Wi6iFu+PgfkWi5o6tH2ZzoUC1jIGUMhJYw/0Z07ToZswVrjh713sUc00IAGOhI1qp4zdjHuBMkudtbjzbUk+CZZNu1PIyW5FZT3Ejgb1kdar0e92B4T5t1RGFvCwJ7hMSohaegN515WnMr1z+JuZ4+cRrBigyC+mkt13oAIGeABnCVapd1KSeU5DywD6BjWlKZHsCLQqCyVRWLS6jnIug8LBlPYmU9p++g60/ZR6aCvQmTcR+XMclbEGxfs+jbSYSs/yzVKSef0oOJDWOkbX45jSH5KXJoftuYjFDmSirv8EtwtcHJLfg7iT4DWgpxJibFeTw2pDSa2oRccjnkUEknaZEfhGuGTYVhcxXSfnjKZsMu8WuOw3R8NqVZGRCS8AupeMxIMNeZ2sWO8/gRgcVBUmLJm5Msnw1lwoasABfOM9Ly9k1R0o96Qwm+uW0Xb9uud/yF38hTcdbgaeKGQV3kmkZ/1aYEzTGF4RHx//RrvzvMFYTDwn06GcaiFW3Zfy+ghnyTpBRjgU4cKKNzWFTxzhT4mL09zPOZHjjwRZ7VJrhhO95DBnTpuQ6LSDsITZpM41Fpy3lbByesNf2gAaU1tzrDaLs2SewNt9Om5Cn1ygflwobUrYJPYG2+nTchT65QPy4UNqVMAAP7/laIAAAAAAAVVSwM0Nh+wWz52SOAbjafh+FhNSUDMx5qASUfaBKEHASF+36dKiF1o2e2JfX9vZkyRwngkr7S7Xp+Ybrn62xXN39V3uUe09FpQtfmuuLUcNCLiHnVdpHATuamjyUXtxPF7Max5FwpScro1kZdLv2cM5jwgvqHxjY2fC5dyB8UtK9/VAL8VJtM4WghT+UUr1aHI+xQIlvAwvyvDbhvI/ikiac7B1tN9Z4FOqZclDV1LaszPdOwZOVXyJo7qJ7idNZxD1d1doZCW6DZZgqGx/PIpppA/A/jdqJH48swXEEudsMaNWGoatoqRbZoaZrxEJ6vJTDRr6LYUapGSTjBl2FTa3sDCY6s63aZPzMHxiL1eqMcrZR7OzmFAR+mti6SLVnlt8AN+4nz/qiEIupWqNAnBx1eDs3STsWQ0+EGbNLhWT78KlLhlIZk5jtPyAzjZzB0pIBokr/DNF1GTMyOMNp+GFdBaBV3Fvl0mpgGsyGBU+oXSi0vZ9Fx8GqmkAxedLWaSRAUXV79uczMT+XyGw6MIbaH4BDqdo9OIy5a+3V/KJrog+HA0/9Im6i9TeUI7VBakTvLTRJRLXcZqwFbgljBOt2k8up4aXJHI/u5w5X8aicdAy9/4CBa0ndXjFiJ2Jyh1//e9VG9JcB3Jm//KxBIbS0mkeq4C64dyH2db8Amdg+Vj57fKxDKe9tZLhMFplhz8WZEpS344yXJfiy7mEzZV8Tufn7DSIhfP5ijHEtX5vY/HSET9UyhfPNSdnn8ljF9R7VvSgpkHZm06QZRPCJcj6t274QL61/UKhbRwm8KqCTCHU878zLjRKsM9BienHodm+MAHtPLzXMqNMBqitP5TN/SfB1O90ToALtE/97NVDOH2dnP6UMPkt9quMRe27Q8cMWBkD0NMKjx6Pyd4RnGQspHBWwcHifezyJIL4xAu4G3BfD8eDthC9C9tbUrU4aA+hQagUTtLq/2zr5TD3XPidBzuD+cL5vHERnQUEcBiyCMnT1AGHvRd8fMAeiT0ql92de7tgtP8MFjzi4skw/EBQCDGv+iWtY6YrQm3O1OocIQ3+W9wrCkdd0sxPM7YdxWTGw+I94nmb2oIZqaz0DfUkvNki7mg98j9S+zIvF5fFbTMUSr4MIyZpF7Ve96I/iE1IKe8WL1h6zwsew6Y3Hs8juwjmuHV7ar1aP+WAr3QDz7mJ8iWOvDLJDRW/yypvHM+OrCmkJpvllOlfQrLBLwyotKtim8F6Fd6ssKQmQYL8noEEkHDPLJ4tK7yv0muW3QUK/7ZBpHnvlEtALfjT63vmnw44OoGYgT+LIBepbOgV75Y8FE4cSJL0Vfmav+nN4vF+dRJWbNyn2TxhbL59815FD1vnvTKmFBKb9KORVumxXjjLgEQktMBbQ5XLJe4RDCIU4ddGCBIFC5nFkvTv3n/uj1dfn3YPRAwcN5VbCBUv/0WDrVctpl9nmPgioUmGV6lorMwQETt67vvBsTJkpMHqrPDiq22iqtxGIh8/z16l7ykbUpItZsJ7xjKQVAoG35KTcB2Yw4re2X7u70LMMOm1xgsMaKqmYaVl3zOQc+DauNDVHy7XKOuljqmbz5UjHGffSr7brNhNgw9KwNLSMyUMMyrVG+ZrpG1TBl3DM4QhqQqp9YtpgJ0QciaAE+QnMJasklrmwV+HQeCk0vemrGw8QkgBLK4v+VfK7HHWBgqHCQE0dkPwskD1b5fG+PYCAd31k9xuZCtpX3/OK3plA8ZUwfd5C2sro/W9uSpO0uW+NRL9U619bSaEKsRc9EDIrcDkAuHNqjdOYybVCtqj5Pvz8tPqZPJ/UL1eUijRdcOVqaZflAlL+dFVbIqt6AzYso1S51Dm+JFZOBhNp4R/I0peg7n6d1L7KF/yRYFr7SvmzUkqqY4/DnKD8lL50hmn97EdcuEXFXt/zit8GBBmKfmluhHh02Si+D5F70nRvoCTZaHE40itoKuOo5qXOTww17/JrCzjGtB1PrY6BWVIry41U1YS+dA1F1ixs389Ty8+pkKNiAaVzRqhTQW4FQHJR/JDYoKE8Tw4ifw8j1eQ+AeZ+WM6f0WGtDP7W4FAPZUfcMNxX4mwMHijwb7pWnWLSSt1VcHqD82cTkv0PaxCkpAKXNvLYxJYMmj+Z48w2MbxPr2nBAQOLp1lzwiHk8bHBvfNHqIF/cNwwHl696NJlJcIRm2CI7VoNtJZqaVgxU7RXDWUXZM/ELls2R+mAcgc7kodS37/ouddDwfYTnWQdltEHi8RqVB5xreyStDcgyWqBoJ0JqUL1uKkd1sD6s0jM6GNLytlFM9Puo2bmui9moeT9uY56BI1eHsN10R0MqQIxvMDqc8iuaqVrNfjp8v36w3TbMNYq4k2siYXcEtoQzRSpX2qqWZESXYXe4A1dOhbl43p7oAg+YfWI9TKY1J7RiIsbET8fioKPVroAm8TLeIYyKAPec+ssxJikoHzRn4qOrBBuByGavc+VSWvss/QfLVP1WIa9MFYFg7R8ImMx0A2FbHD8to2QSBbTyh7Tvul+5JLVocXGGqu6oSqnjTc0DhrthwdklOcv/qKfHjiTmMY6TsO6y6veZjH6fUTBQfhkIemZs4wqz3IB9pBI5/YISRSCqnjsnQc6bpQkBgnXIwYwwLlZuwWiWWFWcKZEjap6h5JR8nb5x8xE8mGIkmBjto9E5sV1Vmiwg+8pCYeGO68PlmFkSyUMu4xn7PwLGpnGD1VDx+uSdwVppn287KnYYFB9wD9nDPXP+BZtZM07486oQUi9z7erU7rMJn7JATjM172vexPyI4skdj0AYe6NW7i1QFzzHzu8vAh34eXLMhWafbagDVWSH/vY0lQkLzV+9KC/zFHnCkTzHkaLXAorpOPQjZAq9LqTIXmu0CCqic9Y/zgxP0+xylTpyg/gorWhF8x47Kh+7+v2hignXovgW+QO+f6ZOYlDl9+D1vX3nrro6TmrLiz+KqtjU0IwtEyCAEj3jVWRKIArYSb/vINz/6uIcVERIjnyrVDfuvSkzWYoPiMB+NTVZkshBIQhIyi5EKFkSvoOvHo7W3vP9dJV6SUxRqipBo9AUUAG2eoUO59yDnLbhHGRAF0SMyPm7rgQpaIIcN0MSmAMYohc8ANHKd4j7N5UMwQ7Qqp2j5YuehR1HtaqcI/7blzbqkxDiJv2v/y8SQlDSpzdTYKFyth5i5TF4CgGT8/okfiUhhC02itke6/grMS1tdLRdkMZYLR/Xr1+vxz7Odnly38DgrPGpXozKsgaHFjav6SckCUNj4h3SjZ4MOsqAMPCCyYl12LdLgx8GhAHmjpDA/Y2KcfH57zdsJg+SFmDqrm7WZWs5eTZIq/LzT+mEFJHJdSwBqS5N6OJdPLAMPCTYVIy8vmD6A43ZgzG/TOQ1hMm3LP41onokh5/9vQ9S1vYsKj4FPdLj0k2TLMn0ElkU+jgi4M1knH75J3uvs1KKFaJqFWK9kX9SjepdQBSbUUfwOnMzPvmjLSCcL+RknSjyQ8SkuTzPkseaijbyp89InPpJqOhYxcgsC3+28M1E922UI9CiSXjmQ4Jas+iGUfYc8OVnviacCRhWTAJa/YJwMWOBO7B2dIxIQdaVlPy8NZHv6jc5m6/JvBEcQdUQGbFIdf0wK8/w7FVZ/ZstYaFSU4mGUOukA0K0M3gJhiBpF+vq/rYTlw69/hXhFnmRmP3vJ1VL4adgX5OYvKwKcMZYNAsOazuUP8SEaUBEv0rJaaO++09uEpjDrrNnL3XIHTGwcLZ6LcVZotB+Up75n91rUmR8S9vVWeNTMZgiMbcjXLBtDDhV8arbOFGVfSCabs5jNHSmNBUh14ddMPnhOiRHeG3y0Cb0lWAlWUThnHTfwpKwYyMIbKl/qGZORe1z8GgG8a8swX0vma6/CqOQh9KvEBsl/51ETfKmRZen68CoOJHs6RppvwelKfBZs+98NdzEYoKsNCkaOspWvKiyJ0ZNm2ueUb3w9PgHXLGE/isehshxn8GZGreTCs4PQ9ZnBFK38mWrP6SsZE/a004cA1/o7AYX3wTixWpLi4cUh9J8YD+k8ZaCOymMKfP11Z5uR3WGJL6UILzPkOWzMFhLp9IX6s9HRm3TfIYyJWphhq8UIKhtH3wjQdohZLf4tSfatqwDAzaFtZI5obJoe/xzoLLJaNR1jkf14dqwjnjiFsw7iyziGYfJX4+pIWpx9eqK2PhXMvdWDEQcdkkW/Vgxki8F1294M+4IAWUASaPPL2XIen4AACBWDV23ceeTVOQjkX2w4yVu+Lh+jwz63iiVQQF+fMmdNPJBj3weT7ZM6lSNbiAlnyMtFl2yt+2WjkbAoFRmh0Rf1HICRwvxk6KI2bJiCWl3DcQY1PAmE6JiH5b1hdGVZi2MT/QZXB4cnKgAAAAAAAAAAAAAAAA==", "u": ""}, {"id": "38986fb172e6bca540d4f316f80de8b9228fd150", "e": 1, "w": 321, "h": 320, "p": "data:image/webp;base64,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", "u": ""}, {"id": "d8be59fb6128677ed1f8681653f4adbecc6b50fa", "e": 1, "w": 321, "h": 320, "p": "data:image/webp;base64,UklGRmwbAABXRUJQVlA4WAoAAAAQAAAAQAEAPwEAQUxQSDMJAAAB8ID9nyHH/v+9qruHMTZrnZO1crK2be8e27Zt21jbtm0zq3AVc6anq95+JzOvqle/mYiYAKj0f6X/K/1f6f//LqoZhqEzWhie8OpJfQb83f6dE6O9ToaPMzqp/4hHP540ac6anXt2blw0ddKPb9w5pHfdME31WETywIcnzVlxKC3r72ambF008+1RPeoYmDib9P9gwaH0q3k+Lv5BqyT7StqG6Xd3imaq5qjb5fHp5zOLuShfM/vS7jcHN3QhEJV8/6LUPEtUoC9r37t9azDVckS0fX7urnRTVDC/cnTh552qGCFkRPZ4c01Kiah4K2PbT8PiNYWK6ff6slwugrVw2/sDYllouNu+vKJIBG/p5ne7etTI2eSRrbkBLoKYWwXbnmnjDTqt9i1zL5tcBDO3sicPSdCVh1V/fp9fhKCZ8lz9IPPcvMrPRfDzot/6amrjajr1oo+L0CxN/66DM2hY/EOHiiwRmmb+4sEedWE1vzhtiRA2z39RJ0g8j2zJEyHMs35tpCh6g2dTuAj1C68nsIrzjtkQEKGe8V5jFYkaubdMIOhfdou3glyJ318VCJq7bnephtbgtyKBpPldwwqJevmkQNL8pB5TCebotdEn0Cxa11EvN73Rr0UcC1GwooWmEPFvX+UCUZ72iKucHOOOWQJRfvoRXRmSF5cKZAt+SiyX+h/nCmQLv6+lBqznwYDAd3Xbcmi7ukSg659eXwVcd18QKKf01f8J573nBMbWwZa69BwjLwmkD7T5x5xDrwikV3WQnePT6wLtzL76PxD/oV+gndqNSS328TKOl3XqVv1vsZazygTefE8fJjHXC8UC9fSh+l+xNvu5wJwfS2LSMp7KFcif72sAaH1PcIG7tSlRVqzvRYH+1uYA7XZygX3gy2hJ9T3A8eMbE7ufNgX+ZW/GSanxES4kyHecElLMvl+TUM2lgpQ5t8pH/7iUFvxoXdloI0oEMfm0aMk03CLImXUXk4r7W04PcbQRk4hx2zVBUPOHKhJpcpBTRBQ9KA/9i4Cg6dnG0ki6IIha+rZDEuG/BKgi0lvJgfXMEWTlX3ikED5FEDa9uxSGZlJGfKdJgE2zSHMtUQKJ2YK0/DU3ep7XBXGPtEev0zHq+L5D71k/dcTpMOTiUgR9h+PGepkEmhSBmuMDQeBTDVFLPEgh/y2ojS2gkJilY/aVIHFaI8T0XTTK7YtYUjaN+FsaWuzhMhqJtXFoeadyImXdhFbkekFkazhaNS5TSTyPVk+LTPM0rF4XZN5XAynnV3TK6IhU1VV0KrjdwKnBITrxd1w4tThOJ/GRG6ceFwk1OQKn27MJtTsBp6dKCXWpOk4vmoTKrmX75NS2ffJb2D4l/W0f3y22j/8e28f8d6C7cHqyjFBlY3G6M49QRZ1x6ptKqLxEnJJPEyqnNk4NDxMqqyZOdbYS6mQCTtEzCbU+DiftI0J968UJniDUR26kmllkst52IVUrk0z5dxhIRa0mU2o7QDpsGqfS7mpYOV71UWmZhhUMyqXSZ4B2XAaVxuOlryJSTke84GWTRgerI9bjKon4t07Eqm8jkXkfIG58RKJryZjBoACFtsWiVjWDQi8D7p9Z9MnuhNyoDPpsr4NcrUX0ed+FnHEHp46VDNhHnaTObgd67COTNiUvA/7dMmizP0kCEb/Q5hW3BKCXjzIFzUCG4YstulhTQIr6+AK6ZA2QA4Sto8vqOEnAXblU8d0LskxYz2nCd1WXBoxNo0npA0we7rk02RoHEm2dQZHrE0Cm3p8IYs2qJRVoeJkeWe2ZXLTXfNQwv9NBsjfuosaZm0C22ohCWgSecUsHYlYGSJGSAPJl7Y9QIvsOkDG7x0eIGZFSgvBZFhWsg81BzqxnJhUyxjJJAXsyQAPrPSdIu84KGpxuCvJm7csokHuLITHQ3ruufub3TpB6wmz1298cJF/tmOrl9ALpP1KoeJPd8nO+xpVuUj1QwHorubpZKbWYCrDk0+qWPhjUULv1Mle04udcigBRP5SpmbUoHpQxdompYnxTIihks50qdm4IKGWzC+qV1l9TC/2OHNXKf9wJiul93a9WgTciQTnrrbVUyr8lAhS00SquUHuTQEVZmxOWKvFTPTQlAWNcniqdH2SAouqP5KvRlfE6KGvMz1yFSt+NAIX1zvKrT8m7TlDaxlO46gR+iAfFTdjL1cY33wPK2+6A0gS+rQ3qq427pjCBdbVBhV1PXVWWwPJGoMaeL3yKwne0AFV2v1egJOaquqDO1b73KYi1PIkpFNSerx7W4poMVFprmqIafFMSqHa3k2ph7bgRlFsfk84Vgq9uBgrufOiKOlg7moOa98ngiuCfkQCK7n2uTA34jpZM1cD5dpEKWMvrgcLHf2XKj69uCUofNdcnO//muqD49X4w5WZ+35CpHlRZLzX/sihQf9ZwIZdX6ddVgIKswS4uK/9X1YCGrMleLqfcdzxARdZjB5fRtSdigI6s3Q4un/QHdaCkPuAIlwwveDwcaKl1TONyybzDAGqywSdlwk8NcQE99V4ZXB6bOhlAUa3fEVmULbkBiKoNuyYHa11DRhVgA9Jk4P82AQjruOMiftf/iGKUAcfYbOyybo0C4mr3ncDtUD8NyOu9uRAxfn6QAQQ2Hs1Ey78lmQGJXY8XIWUtaQVUNh7ORqnwvXCgs/flPHx49gMRQOmIF3LROTFeA1rHfVKAi+/wcAOoHfmRiYm1rAkDeru/LsSj5Nt6DCge/TXHIv/NKCC658NiFHjqICeQvdrHAQwO3WYA4d0fFoVcyZJ6DEgf9WV+iBV8Xx2oH/62L6QyH3QD/V3vZoSOtWmgA+zAuGfLQsW/sqkO9qDrxSuhUfRGHbANvQ/khkLq026wEfWxqUFXuqGnBraiMfFskBVPqwu2Y9eDPIj4tafcYD8a/Q/woOEpd4aDHam12WUFiX9zRx3sSZa80AwGfu3V+mBf1p5bWnF8/zgd7Ez3Q1m8gnJ+rwk2Z8Tj+RVz/Z0azO4A1nt3BZTNaqmBDao3XnCtnPypjycwsEcjbz0ZKAee8XMy2Kes0Rfp/n8icOGnNmFgq4aPnJnL/4HA5UkDvWC3Mk/HZ6fuOXP5etqRNe89mBjFwI5lnrjGXXu2qxVpQKX/K/1f6f/KaAIAVlA4IBISAAAQeQCdASpBAUABPm02lkekIyIhJ5XpKIANiWNu/FNZUcV34BPArBnf2nfgfll7a1m/zf93/Y/9k92njXHj9jPn/zx1Tfux9wb+Gfz79duw35m/2j/aL3ovxA94P909Rb+ef9HrJvQw/XT01v3d+EL9wf3R9pD/8+wB6AHBAdcChr9wXe8X9PXfsC9F79wPZi/Yol7U5Oj6f9c/lQuFDhaepydH0/65/KhcKHC09Tk6Pp/1z+OTu4YkDx7lWKlU2m+wyyBXla7FxbTNKb808uAn1r+l8PwI+G1dlSBwaoepV3Fwqv6f9c4gqJFI4+nFREf40gqfZWpSfcXId7kncndjftjIXiOp4C3R6Ji59E4F8DogrQC2MslVh31l0GOWsdGgbKArzujPDvZXRrWl+VAoKJzPdIyKBN381adii5EV9YUfcrTKryYY53SOIA0hqgd6leT4yZ8ziCxNYEi5y/EAOGW1N9fycpXEfjny14T6ji0kDS02KLp5GiJ54tfp64huGhDNnQJ+JAVO4irGW9ksye2Rh9nKdoIFnNauoRBL7UIhIhKO2qWnf+bVG1kgi4y3cTY+yq14MfCrhYyZcBsYt3wBVnOHerK9ejNyfbZIYFaW3jwY0ZzrAo0q8WDLrMiCB+aQsdwyk9GEJuGmTx7DAsh9P6hGDgc4BJBH31AFIp9NDr+HKKopJU97w2SEhVAWV1rW+bT7opp6Z/ZflGGjUxTkWobIwWcIzWGk+xP+0B0VMtk38X3U2qD5WKOmnVHUghs0XTy3A08uck0hP5+PXolHmDBmQLY1fVScOX0Equ2gFAtq5ula09wqp0+In8PDSwcTN3w7CQXTpdXMiwuAWNPVrIkSOWkN+B4ATwIA3ObKTbhXTBFGun8qqTfT2hiof24sKDqgrr1J9d1oYv6GzL8kEMdcjFN/YQnujjJ1jOdEuQe46ivnShz5kIVa7ie2LwZkM/kDsJPtzVsdH/qViLQ8UwO/iet/VxQSyMWZH7iv1/1WBPHTbkRoc84LmuKtd9rYP59P+ucxvJUSbLqEaCYBOKvtQ0wO3hvtl4t43lyeiUdMC/vvIs+U8bE45r6nBqG15oWEOFp6nI4TGTu1SkbWX2jnvsDD7Nd5o8ndwvU2NqPtlVLWTCc72x6xfhIT9MNl+VC4UOEg+ZvtmCwvDxo9berv2L+tkLOvuhzFF2zXy/77Ys66E2kU5Oj6f9c/j2eIHfdZ+oLOFpMFoqvSoMQGkF07UJ7dUtvp/1z+VC3/+IUxLfKhcKHC09Tk6Pp/1z+VC4UOFp6nJ0fT/rmoAAD++ycAAAAAAAAQ/iXw2lfKV53wlW/YjHg9TBU+/RLs5Gy5iQQMf6mnuc8ea9zPegHFYjyuzLs/v0+KBoydKE6zBU2YBpAnxKV89DQ9KaMCZX2I1/GTBmrUEF9FYL4VQQl9EZCCf5C0YDi+Rr4PbDNB06FFOeTJXP7SPfBW/bC7DcCbeV5yfTWyhQ8hbdT+CGV7emDtyf5zLjz96IdDpEzyd/g/KkTIEE+TkFd6qiu+F234n4c/d4w3b+tfu5F+UN8fXqyx1G3FGteTKMeTPOvKdO3mYVBA2+bc8NaCvSTrFrPsk8oawdvTvUHkv1oZro5IhjRHDkS7mis6pueFgMzayHMoCfRTH9nFmJrfoN5UuC6H7omPmhBGKJNg9RYHIKdtsz+D6oyCULqSplAZORIUr97vZp/TtWvzG1X9VAKTeYko/tRQ60xp41oKj/z5UTPbCOpr3JsgVrncPJGTggf72pmYY8zKi2TezQPO3W3/v1vKgVK18fQumBv6/Qala27VNCCkmMBjHM09NiUVWfFif+bqm/RRu/H9nlwKXf3tQAvm83g7zlrP8rcUbXjPf48f6fieAroKv/KGsDTHiCVD17Fj+nmThVpVZfq5NOWqPur325JHnarbANg0uv5pmsMNy0nqAY5FcwhUTfU5aK4sjOglVjNpmN24NdmA6fzI5kJZf/Az/ThQKFz8z7Vkgubf7MFZcm83jCTdbwuIykK93ENCjJpcyBiddtxf2380K4rnY7/LwDzmZ4aIXBUTdSOJavd2C1QbGdeu0IOmy+9EHihCmNoC9HCGbwvX4BcsAw97xFPBrDb3ENYfQyGTgvSz+KB25VbkVTyg330bk9OESuVG3C8qaObgsNA9ieFjtj2IKg9HrcSpddYS1I5M4Z8Gg0vg+NENaKwYen5thjgllBOL2Yx9kWk5KMwsxDqEm8HYew6UmviZkbJCfxqqtzu2B4M/aRx6Y76KaWlBb5kXWMtmrsBC9fe8JPy3l2r3bAKO5wdLMGUvXo0n0gU9t10RtU/s/oyinZG/J0HZZgFCzjIdrGhIEaoXyWehfpx9THJgR9goVK5I8rE7t/Sc5dxl06pSQHP6DPNH3RHIVprB0TQx5VRT6ObXiZzMj25fxjfNl/E4IQRLyGS4uGfwJkuK1BUnFU+lNoITmIY+SAnD4rotUi0Voqe0Wn8Gww9G/6Jnx2WhwKVYoMqsJoBduX0cEclmtdcdyrT5FI1xdBHpxIBYE52xw0OXZeNNavVaERy2rUpx23A9hPUAksaAMcDgLdU6y/ZpTbXw66YcOyBgT4NTw100dpkaY07KB6lM0hVd5vVV5J2RoSdI7rqX4V9DyzfJe6ZUoKWLB5mWhS9RpsTXmrExWSWr77NH/w2ldS9RcLqD1+Nik6Hi2p2fwlvafS8X/QCOnKnXutRu8aIzpYmN+FbZE97dDqO0DrTICc9uv+u566intkcKFfCEWugRTMGsDI2Tc0Hd43syVCtZcNhMNZoX6Vo0yVyf9cf7jzOVpD2RgbMpwp9Rli7fbCn6tR9W2QxZCmzZcH+5p1BU/8+U2b9E2RcpXNc/g2EGFA6yFO/zKW3v7nDJslZq3+uMsbMCzwHWFkIhlmi5g5ut0SIytzTesaW5jbNIUNqfV1G7fPwe0Dy5BlWhFvWuvcsKURxUaYw84pbJOzzjkVPmoA4BDkvJid8Qufvj4zmW0bXVbwLxmr4oWYWLJShKpnP7ocIy2lU61MXMYc7JG17qaB9MQ0vSjWzOoG33v4FVNwpsoTH4zMPVy0eXamszw3oNUhAsgRFkEcHHi31H/b6Pref1mv/qZ15uxAjjw+V9WLYIZZmAzutk8No5rAYftnOv0ji7CSfVp1g38tIG/nnrsFETGwUrfCFwL886gTB8lCJIK4A4WyZcdCAepLcCE6ZZtB/UjSY5JFhyer47f2ohrkt1ut6fs8QMkrqns1+7HGVIbZ5fgy9HQfxttzLHwz81+KgChrFvMjhTbEaoQ+AU93yBDeYBpAPr4VREK6VnrilMeT/uhNV7VYmeIKAO3GLPnAL1+N4rEbriF/iJ+yggfPhftc48bSlS4Bh5bmGutdJt2vd0UE37KDk4COuEQjjF3UwVrovb6xVoOU5of9hdRPXSK6KzDHTq84a4rzamwb/vfX4yVr+uONStIh/ODpWy3XXDZFil+mQNmshzsVDV1ortVgHFsp8EoHCGiXrniHyTYiqa6oUUCY3u+dG7UzhSj0oTqM+0aLLikCRwu6cCpunHP5TZxdRxWLxMCFJ8Z86l+swuRS4lFsSLg3URM64AIwP4InmWx7G9KBsYtkqJtifhPn/kmQ8aBFIvP/qsUjccA9cawGLqwFubSfMnn1QC4L6wwOmEjGc89m9Yr1GQTqgU1Q284eV/haY+DA3b39JUZZPqkLH7tJKIp2AvcATlnCl6J6EgqkZWaGy9a/+IKL32nFYTAf3Pneb2QXIPpyQ9vrKviP+d3zhI+CogFhbweLDSJxrqUP8IBW/fsclWHKbc4dujQVq7lNMmw+zky6jc7wtTeBLwewWjxqL6OU4m1l6iXb8nkHfRm3xlmKWP4JiuLNz3ZqNX2UMtvehB+EYkNBSYbPct3S996FJ6/QBYyLFL/vemxnQu2m17M4JbpCk/eSX6dQV+Q/Bzyhk4ZNl0NXmu6CN+K9o/jAQzodbzp1+2mFHfD0jhNjnhZjcrHjCeJmv6CCADpG2GTzuU+qUu/SyC3galiyhq4QHZw8goGl+SsjM03B7Xj+ojIoisSMnAQdE+bE3SlnxK41RaMeY6Hpy2Fc5ba8SoMB6wBRbKdi+PfGRf54iP5XrqLKTbcsw8vNcisQ+wL5Q306Dz57aoWiOWSFeFJv57vVmCnMMDQ9bU/M4XBbyob968Re+VVyq/yOPXwHorECsw5Ch84eF5TLFnJRt8Pc+NWcquW8g0UkjyDz5n7IxEP5CNUssAP1+I2YE+joLEbuFTVMPX95KXAwk5xMulX0IYBH4YHZ+jvYtfSMlFwRyVHziyoujSi8hQ8QU/i2CR5SxDNLVz361TnmCqZ5LXbO4N25DV2oFrIZVm9oucGlqpIMM9Hr2J5InOhoScGOewm1ZZEvybvRZgKw2pCjV77HuFdk6++/pplx1nVpeDA0eKJhE0ITPN70lG4/lbe61xAPH8A2DOZvTsqSO03G+dmkAPrAxAEK0RhNI2wRXW1ojfnMcqRFnGFU4FIYBILgisDx2IyoPAP7T0e5M/L0oMPJg65xHWP0AYcHadbqWRfVqEnDL213vev4rvKUcmS8Ck9yOw/fmCe5TlMagmVz5wrCf0od5McDGSRLOcRt0V/HKvF1HxqNn3e8A6IBhymUX3guglso5GGIgbFeu0ThpLKoj25NQIP8fcTAunRvXv0aqdpV9Hq72m1WLpPLmSj00g6n3DdhfN0Z7Mn6iA+dI4BlMSJ00uzpN0IMnvbV7zBs72Seo7BeDhaWuhF8O7jkQ07zVedldGELaa8HuE+KQ/dugG+DpP9+pRgXjC6aD7CJ98pH9/GwAK6r/1hSaHFUSEKqofmjRcDIJsJCs0EeSL41Y8Y2Y9Pd239uXV7I8tDd7rQWzxxFkFDL0fzN8FTYs3+eD4+w+8HgW31dg2tE+58OoGvxwM4o0tdyGox10xHElINYy4HF/0q5o7JkjKIpd9dThM41TJFpoClQztLQ5lcP9J9kM6E8btVtOXFPOY0/dMNHnrzXs20d9eT317GrvCkboOdc76LiPKrxEB7Qyb47bhbZMTX85jVgOcDuNenWVq+ZavO16ygTxFF3JLqdb4pV9JhkJaSGSf1daXEr0c+AzWh0kX8AGxcQ4OSev3BOllwtoOcxUUYB5rSqtPX83XeYzkbEQ9O4zVoPOeAxP7CTerMnZJOfwx6Aqv1dRIjYJAJd/zLG86+7gEM2df4Oj7FIPtG1G6r9bz1riBhSPCtlEn5CGG4mQSmtK66u3er6zjFsmBquYy909FyQSQXIUmrp+9BnA8sqC0youepbJ+hhnWSFqSGC3+OP7xfeb2BE+WWU5SgnFieQxRAEHNYPclwdA7v4ZwPuBhY1PQwtZudY0qsK2cgwcXHkGRAY4Y70IV4qlCRtA+NN8tFor3U1eGADHvwjH54CmVf07lZ9ndyuT7VE/GvzDisEYVanLUsMgmAkG/QV6zng49ryrjbTeL6jc+AWgBCmhfM5f04K81CmsRDAFhkXwxCWAtMMgCBp3+u5iSp1J8wmROxis9CE51DCOEGmVPR4B/899Kkdh+cjgStEsdKywHEXJAhAIT2S2JdHoV9LXSDQdZJo94sQ6zdP7TbvgExndtUjPaSiBBfeB/6FGQIkhtCa5mftODpaneo4L8TcvlDEB4VJQ1iweIcED+ORMH1CWzHrIGKr53Wm1o+NeAARdVyL6lKCR/To1a1Jb4FsAABUl/GzEROULRECcPoA/CkcXbcpSexAAi91Ys5g1GzjuM8F5SbetToII/5vKfGGXpGvO/ygr36p7HlBNC2W/nES+ba2XuVJdsXrwtEAi/UZf8fo6mqWbDFadhgZ20reSopxBa7X7uMzXGvdYOj3chShATHI6zpxG+an8IWpSOqBXQsYASStNhwwHw2f8x0xt7WDpGmEA3kyO8Zi/ZLAAAFXrBaAtr49zUkqrPj9XnBQuvWJmmCaoRp765aEc+8FQBnLbH8PopEcMTz5clxRfEQZQKxi04153wG4LSNWW/sH+9aYF5pWIg9Out+HPgfQ2C06wJPyDLvUJr5/QY+lo1qz/2+IzTR/sfD7ndyXqd2u6bRtmcjGSDH5HZA9/gYAMn7VqaQmUgaz1MCxca8g5CY06hyC52uAAAAAAAAAAAAAA=", "u": ""}]}