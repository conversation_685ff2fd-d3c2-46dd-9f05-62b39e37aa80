<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="MayaServiceBannerTableViewCell" rowHeight="145" id="zVy-2h-pRt" customClass="MayaServiceBannerTableViewCell" customModule="PayMaya" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="421" height="145"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" ambiguous="YES" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="zVy-2h-pRt" id="v3l-d8-eUa">
                <rect key="frame" x="0.0" y="0.0" width="421" height="145"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="x5S-8L-G24">
                        <rect key="frame" x="0.0" y="6" width="421" height="120"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="120" id="Bme-g9-xFg"/>
                        </constraints>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="x5S-8L-G24" secondAttribute="trailing" id="SDF-22-vL7"/>
                    <constraint firstAttribute="bottom" secondItem="x5S-8L-G24" secondAttribute="bottom" constant="12" id="f4P-b8-JWP"/>
                    <constraint firstItem="x5S-8L-G24" firstAttribute="top" secondItem="v3l-d8-eUa" secondAttribute="top" constant="6" id="lHJ-eo-Uq7"/>
                    <constraint firstItem="x5S-8L-G24" firstAttribute="leading" secondItem="v3l-d8-eUa" secondAttribute="leading" id="zNP-dq-q3M"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="containerView" destination="x5S-8L-G24" id="3hS-Ti-SNf"/>
            </connections>
            <point key="canvasLocation" x="64.885496183206101" y="55.281690140845072"/>
        </tableViewCell>
    </objects>
</document>
