<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="CerebriSansPro-Book.otf">
            <string>CerebriSansPro-Book</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="MayaInterstitialMessageView" customModule="PayMaya" customModuleProvider="target">
            <connections>
                <outlet property="contentView" destination="iN0-l3-epB" id="dnb-2d-NJE"/>
                <outlet property="emojiLabel" destination="dXt-Sr-UpZ" id="fUC-LJ-Fg2"/>
                <outlet property="messageTextView" destination="779-Gy-CMo" id="eKh-OB-bOB"/>
                <outlet property="stackView" destination="zGs-u1-BST" id="Y4c-Zz-Skh"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="451" height="68"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <stackView opaque="NO" contentMode="scaleToFill" alignment="top" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="zGs-u1-BST">
                    <rect key="frame" x="0.0" y="0.0" width="451" height="68"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="GpS-F1-w82">
                            <rect key="frame" x="0.0" y="0.0" width="22" height="61"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="✅" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dXt-Sr-UpZ">
                                    <rect key="frame" x="0.0" y="8" width="22" height="21"/>
                                    <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="16"/>
                                    <color key="textColor" name="Grey6"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="22" id="EfB-fl-q8v"/>
                                <constraint firstItem="dXt-Sr-UpZ" firstAttribute="leading" secondItem="GpS-F1-w82" secondAttribute="leading" id="Gtl-fl-tX0"/>
                                <constraint firstAttribute="trailing" secondItem="dXt-Sr-UpZ" secondAttribute="trailing" id="vTk-GL-VP7"/>
                            </constraints>
                        </view>
                        <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" editable="NO" text="An iPod, a phone, an internet mobile communicator... these are NOT three separate devices! " textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="779-Gy-CMo">
                            <rect key="frame" x="38" y="0.0" width="413" height="58"/>
                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            <color key="textColor" name="Grey6"/>
                            <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="16"/>
                            <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                        </textView>
                    </subviews>
                    <constraints>
                        <constraint firstItem="dXt-Sr-UpZ" firstAttribute="firstBaseline" secondItem="779-Gy-CMo" secondAttribute="firstBaseline" id="H5m-Oh-9t9"/>
                    </constraints>
                </stackView>
            </subviews>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="zGs-u1-BST" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="1ds-8k-qjK"/>
                <constraint firstAttribute="bottom" secondItem="zGs-u1-BST" secondAttribute="bottom" id="SDp-bt-nS2"/>
                <constraint firstAttribute="trailing" secondItem="zGs-u1-BST" secondAttribute="trailing" id="bUi-N2-7Hj"/>
                <constraint firstItem="zGs-u1-BST" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="ksh-aE-lSY"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="-55.725190839694655" y="-3.5211267605633805"/>
        </view>
    </objects>
    <resources>
        <namedColor name="Grey6">
            <color red="0.33333333333333331" green="0.33725490196078434" blue="0.34901960784313724" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
