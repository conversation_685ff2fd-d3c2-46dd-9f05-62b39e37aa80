<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Jeko-Bold.otf">
            <string>Jeko-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="MayaHelpCenterCardView" customModule="PayMaya" customModuleProvider="target">
            <connections>
                <outlet property="contentView" destination="WJ9-ab-iGc" id="9o4-5U-4tT"/>
                <outlet property="emojiLabel" destination="KFO-lN-x3j" id="6Eu-lX-eta"/>
                <outlet property="helpCenterSpielTextView" destination="Rh4-Xo-wnv" id="zR0-l1-R4E"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="WJ9-ab-iGc" customClass="View" customModule="PayMaya" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="112"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="otc-tV-4ya">
                    <rect key="frame" x="0.0" y="12" width="414" height="20"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="☎️" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KFO-lN-x3j">
                            <rect key="frame" x="20" y="-0.33333333333333393" width="23" height="21"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Need help?" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="owu-mC-a8Q">
                            <rect key="frame" x="55" y="0.33333333333333393" width="88" height="19.333333333333329"/>
                            <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                            <color key="textColor" name="ContentPrimaryWhite"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" name="Grey8"/>
                    <constraints>
                        <constraint firstItem="owu-mC-a8Q" firstAttribute="centerY" secondItem="KFO-lN-x3j" secondAttribute="centerY" id="52L-mo-29R"/>
                        <constraint firstItem="owu-mC-a8Q" firstAttribute="leading" secondItem="KFO-lN-x3j" secondAttribute="trailing" constant="12" id="AlO-iF-UAD"/>
                        <constraint firstItem="KFO-lN-x3j" firstAttribute="leading" secondItem="otc-tV-4ya" secondAttribute="leading" constant="20" id="Kk6-5g-icR"/>
                        <constraint firstAttribute="height" constant="20" id="olM-sj-eev"/>
                        <constraint firstItem="KFO-lN-x3j" firstAttribute="centerY" secondItem="otc-tV-4ya" secondAttribute="centerY" id="pFd-Gs-NMt"/>
                    </constraints>
                </view>
                <textView clipsSubviews="YES" contentMode="scaleToFill" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" editable="NO" selectable="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Rh4-Xo-wnv">
                    <rect key="frame" x="49" y="26" width="341" height="78"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <color key="textColor" systemColor="labelColor"/>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                </textView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="9O1-ys-hxN"/>
            <color key="backgroundColor" name="Grey8"/>
            <constraints>
                <constraint firstItem="Rh4-Xo-wnv" firstAttribute="top" secondItem="otc-tV-4ya" secondAttribute="bottom" constant="-6" id="0hf-m6-dEW"/>
                <constraint firstItem="otc-tV-4ya" firstAttribute="top" secondItem="WJ9-ab-iGc" secondAttribute="top" constant="12" id="IRR-KW-RrQ"/>
                <constraint firstAttribute="trailing" secondItem="Rh4-Xo-wnv" secondAttribute="trailing" constant="24" id="Plo-dr-ws4"/>
                <constraint firstAttribute="bottom" secondItem="Rh4-Xo-wnv" secondAttribute="bottom" constant="8" id="QqH-bN-ijw"/>
                <constraint firstAttribute="trailing" secondItem="otc-tV-4ya" secondAttribute="trailing" id="VyR-br-ppm"/>
                <constraint firstItem="Rh4-Xo-wnv" firstAttribute="leading" secondItem="owu-mC-a8Q" secondAttribute="leading" constant="-6" id="v5u-MM-5gb"/>
                <constraint firstItem="otc-tV-4ya" firstAttribute="leading" secondItem="WJ9-ab-iGc" secondAttribute="leading" id="zs0-2I-f15"/>
            </constraints>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                    <real key="value" value="16"/>
                </userDefinedRuntimeAttribute>
                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                    <real key="value" value="1"/>
                </userDefinedRuntimeAttribute>
                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                    <color key="value" name="Grey8"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <point key="canvasLocation" x="-281.15942028985512" y="-292.63392857142856"/>
        </view>
    </objects>
    <resources>
        <namedColor name="ContentPrimaryWhite">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey8">
            <color red="0.16470588235294117" green="0.16862745098039217" blue="0.1803921568627451" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
