<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_0" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="CerebriSansPro-Book.otf">
            <string>CerebriSansPro-Book</string>
        </array>
        <array key="CerebriSansPro-Medium.otf">
            <string>CerebriSansPro-Medium</string>
        </array>
        <array key="Jeko-Bold.otf">
            <string>Jeko-Bold</string>
        </array>
        <array key="Jeko-SemiBold.otf">
            <string>Jeko-SemiBold</string>
        </array>
    </customFonts>
    <scenes>
        <!--Maya Licenses View Controller-->
        <scene sceneID="C3U-nE-GDU">
            <objects>
                <viewController storyboardIdentifier="MayaLicensesViewController" id="VfC-zp-zV7" customClass="MayaLicensesViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Csy-bF-p1Z">
                        <rect key="frame" x="0.0" y="0.0" width="390" height="844"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ad6-vx-iUA">
                                <rect key="frame" x="0.0" y="246.**************" width="390" height="597.33333333333326"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Licenses" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="t1I-V4-xRG">
                                        <rect key="frame" x="24" y="24.***************" width="342" height="29.***************"/>
                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                        <color key="textColor" name="PrimaryBlack"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="7ND-hg-lfy">
                                        <rect key="frame" x="24" y="69.333333333333371" width="342" height="408"/>
                                        <color key="backgroundColor" name="Grey1"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="408" id="1q1-ad-9cL"/>
                                        </constraints>
                                        <connections>
                                            <outlet property="dataSource" destination="VfC-zp-zV7" id="Mr1-NZ-eXU"/>
                                        </connections>
                                    </tableView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3ES-30-6Cx" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="24" y="501.33333333333337" width="342" height="56"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="GvO-cy-vOf"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Done"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="didTapDone" destination="VfC-zp-zV7" eventType="touchUpInside" id="ZLr-Mi-gre"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="3ES-30-6Cx" secondAttribute="trailing" constant="24" id="4i6-T5-jyn"/>
                                    <constraint firstItem="7ND-hg-lfy" firstAttribute="top" secondItem="t1I-V4-xRG" secondAttribute="bottom" constant="16" id="A4m-4T-bUD"/>
                                    <constraint firstItem="7ND-hg-lfy" firstAttribute="leading" secondItem="ad6-vx-iUA" secondAttribute="leading" constant="24" id="B5F-BO-CPn"/>
                                    <constraint firstItem="3ES-30-6Cx" firstAttribute="top" secondItem="7ND-hg-lfy" secondAttribute="bottom" constant="24" id="F3y-O3-8m8"/>
                                    <constraint firstAttribute="trailing" secondItem="t1I-V4-xRG" secondAttribute="trailing" constant="24" id="G3q-M4-wb7"/>
                                    <constraint firstAttribute="bottom" secondItem="3ES-30-6Cx" secondAttribute="bottom" constant="40" id="LrB-dp-Lda"/>
                                    <constraint firstItem="3ES-30-6Cx" firstAttribute="leading" secondItem="ad6-vx-iUA" secondAttribute="leading" constant="24" id="MnY-BP-W6x"/>
                                    <constraint firstItem="t1I-V4-xRG" firstAttribute="leading" secondItem="ad6-vx-iUA" secondAttribute="leading" constant="24" id="O02-l2-soc"/>
                                    <constraint firstItem="t1I-V4-xRG" firstAttribute="top" secondItem="ad6-vx-iUA" secondAttribute="top" constant="24" id="dmC-2C-DAG"/>
                                    <constraint firstAttribute="trailing" secondItem="7ND-hg-lfy" secondAttribute="trailing" constant="24" id="yY1-m9-iMK"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="hZG-Ey-vWu"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="ad6-vx-iUA" secondAttribute="bottom" id="4fQ-Yu-q53"/>
                            <constraint firstItem="ad6-vx-iUA" firstAttribute="top" relation="greaterThanOrEqual" secondItem="hZG-Ey-vWu" secondAttribute="top" id="5wH-i8-bVl"/>
                            <constraint firstItem="hZG-Ey-vWu" firstAttribute="leading" secondItem="ad6-vx-iUA" secondAttribute="leading" id="YLd-LL-AC8"/>
                            <constraint firstItem="ad6-vx-iUA" firstAttribute="trailing" secondItem="hZG-Ey-vWu" secondAttribute="trailing" id="rPH-St-YWQ"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isDimmerViewTappable" value="NO"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="contentView" destination="ad6-vx-iUA" id="nHt-a1-iEH"/>
                        <outlet property="tableView" destination="7ND-hg-lfy" id="RGb-oz-ZHW"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="LV8-p1-qgz" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="7648.8000000000002" y="2016.3418290854574"/>
        </scene>
        <!--Maya Profile View Controller-->
        <scene sceneID="wdF-FV-CiM">
            <objects>
                <viewController storyboardIdentifier="MayaProfileViewController" id="qRX-6a-6b2" userLabel="Maya Profile View Controller" customClass="MayaProfileViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" ambiguous="YES" id="tIk-rT-780">
                        <rect key="frame" x="0.0" y="0.0" width="390" height="844"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wsg-m2-SQK">
                                <rect key="frame" x="0.0" y="91" width="390" height="623"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lZK-el-Vuq" userLabel="Content View">
                                        <rect key="frame" x="0.0" y="0.0" width="390" height="490.**************"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="pJI-og-yd5">
                                                <rect key="frame" x="0.0" y="24.000000000000014" width="390" height="234.33333333333337"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="LZh-VN-Vvr">
                                                        <rect key="frame" x="0.0" y="0.0" width="390" height="122.**************"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="kdh-qd-Ct3">
                                                                <rect key="frame" x="0.0" y="0.0" width="390" height="122.**************"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconMayaProfile" translatesAutoresizingMaskIntoConstraints="NO" id="Wh2-vk-Geq">
                                                                        <rect key="frame" x="171" y="0.0" width="48" height="48"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="48" id="2ER-Ps-Dao"/>
                                                                            <constraint firstAttribute="width" constant="48" id="sTz-il-BUB"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="6NV-bO-lWT">
                                                                        <rect key="frame" x="38.**************3" y="64" width="313.66666666666663" height="58.**************3"/>
                                                                        <subviews>
                                                                            <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Maria Christina Santa Dela Cruz" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4eu-zL-Arp">
                                                                                <rect key="frame" x="0.0" y="0.0" width="313.**************" height="24.**************2"/>
                                                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="20"/>
                                                                                <nil key="textColor"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="@wwwwwwwwmmmmmmmmwwwwwwww" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8Ma-Z4-cgI">
                                                                                <rect key="frame" x="7.6666666666666572" y="24.**************3" width="298.33333333333337" height="17"/>
                                                                                <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                                                <color key="textColor" name="Grey6"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="+63 917 234 3298" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bch-G1-fCC">
                                                                                <rect key="frame" x="99.***************" y="41.**************3" width="115" height="17"/>
                                                                                <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                                                <color key="textColor" name="Grey6"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="Sbg-Zt-bqK">
                                                                                <rect key="frame" x="112.**************" y="58.**************3" width="88.**************3" height="0.0"/>
                                                                                <subviews>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Wallet ID: ---" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" id="VLB-Am-KC1">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="88.***************" height="0.0"/>
                                                                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                                                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                                                        <color key="textColor" name="Grey6"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                    </label>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Device ID: ---" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cbg-40-vjU">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="88.***************" height="0.0"/>
                                                                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                                                        <color key="textColor" name="Grey6"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                    </label>
                                                                                </subviews>
                                                                            </stackView>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="YBe-Xt-Xhd">
                                                        <rect key="frame" x="0.0" y="130.**************" width="390" height="0.0"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="q7p-1h-ew6">
                                                                <rect key="frame" x="0.0" y="0.0" width="390" height="0.0"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" id="xVS-IZ-Hk8"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                    </stackView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="v85-4B-975" customClass="MayaProfileDetailsView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="138.**************" width="390" height="80"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="80" id="AW8-8Q-0PX"/>
                                                        </constraints>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="4vp-cj-Uxu">
                                                        <rect key="frame" x="0.0" y="234.**************" width="390" height="0.0"/>
                                                        <viewLayoutGuide key="safeArea" id="AgS-y5-z6G"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" placeholder="YES" id="M2n-TE-cOc"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                            </stackView>
                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" scrollEnabled="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" estimatedSectionHeaderHeight="-1" sectionFooterHeight="28" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="3O4-yl-R0n">
                                                <rect key="frame" x="24" y="274.**************" width="342" height="200"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" priority="500" constant="200" id="YgF-63-8QN"/>
                                                </constraints>
                                                <prototypes>
                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" reuseIdentifier="MayaProfileHeaderView" rowHeight="44" id="bRa-VA-KGG" customClass="MayaProfileHeaderView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="50" width="342" height="44"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="bRa-VA-KGG" id="PD2-SE-Jtn">
                                                            <rect key="frame" x="0.0" y="0.0" width="342" height="44"/>
                                                            <autoresizingMask key="autoresizingMask"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" id="o7Q-DD-sGN">
                                                                    <rect key="frame" x="0.0" y="13.5" width="327" height="44"/>
                                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="PROFILE MENU" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="P3J-oH-XSZ">
                                                                            <rect key="frame" x="16" y="0.0" width="295" height="44"/>
                                                                            <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="12"/>
                                                                            <color key="textColor" name="Grey5"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="bottom" secondItem="P3J-oH-XSZ" secondAttribute="bottom" id="Kcw-uj-8aC"/>
                                                                        <constraint firstAttribute="trailing" secondItem="P3J-oH-XSZ" secondAttribute="trailing" constant="16" id="bov-Kf-h1o"/>
                                                                        <constraint firstItem="P3J-oH-XSZ" firstAttribute="leading" secondItem="o7Q-DD-sGN" secondAttribute="leading" constant="16" id="kN9-O2-D1W"/>
                                                                        <constraint firstItem="P3J-oH-XSZ" firstAttribute="top" secondItem="o7Q-DD-sGN" secondAttribute="top" id="t3v-zg-A3A"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                        </tableViewCellContentView>
                                                    </tableViewCell>
                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" reuseIdentifier="MayaProfileMenuTableViewCell" rowHeight="56" id="PmI-BD-NOQ" customClass="MayaProfileMenuTableViewCell" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="94" width="342" height="56"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="PmI-BD-NOQ" id="7lN-uu-ljc">
                                                            <rect key="frame" x="0.0" y="0.0" width="342" height="56"/>
                                                            <autoresizingMask key="autoresizingMask"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="8ag-qn-noq">
                                                                    <rect key="frame" x="20" y="15" width="302" height="26"/>
                                                                    <subviews>
                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="peg-kN-Fu9">
                                                                            <rect key="frame" x="0.0" y="0.0" width="24" height="26"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="24" id="2t9-ka-884"/>
                                                                                <constraint firstAttribute="width" constant="24" id="9pe-L3-UQ0"/>
                                                                            </constraints>
                                                                        </imageView>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KxH-3Y-MDe">
                                                                            <rect key="frame" x="40" y="0.0" width="222" height="26"/>
                                                                            <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="16"/>
                                                                            <nil key="textColor"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconChevronRightGray" translatesAutoresizingMaskIntoConstraints="NO" id="oLa-Tq-gX2">
                                                                            <rect key="frame" x="278" y="0.0" width="24" height="26"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="24" id="2wR-Gu-bhw"/>
                                                                                <constraint firstAttribute="width" constant="24" id="5Hm-Vc-1AH"/>
                                                                            </constraints>
                                                                        </imageView>
                                                                    </subviews>
                                                                </stackView>
                                                            </subviews>
                                                            <constraints>
                                                                <constraint firstAttribute="bottomMargin" secondItem="8ag-qn-noq" secondAttribute="bottom" constant="4" id="6kp-H5-KSv"/>
                                                                <constraint firstAttribute="trailingMargin" secondItem="8ag-qn-noq" secondAttribute="trailing" id="ADT-yZ-9Nj"/>
                                                                <constraint firstItem="8ag-qn-noq" firstAttribute="leading" secondItem="7lN-uu-ljc" secondAttribute="leadingMargin" id="D4W-Rf-uLT"/>
                                                                <constraint firstItem="8ag-qn-noq" firstAttribute="top" secondItem="7lN-uu-ljc" secondAttribute="topMargin" constant="4" id="TFg-lZ-V0X"/>
                                                            </constraints>
                                                        </tableViewCellContentView>
                                                        <connections>
                                                            <outlet property="menuImageView" destination="peg-kN-Fu9" id="zmi-ah-Hkr"/>
                                                            <outlet property="menuLabel" destination="KxH-3Y-MDe" id="8qr-u8-5PH"/>
                                                        </connections>
                                                    </tableViewCell>
                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" reuseIdentifier="MayaProfileButtonTableViewCell" rowHeight="72" id="Md1-dU-luP" customClass="MayaProfileButtonTableViewCell" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="150" width="342" height="72"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="Md1-dU-luP" id="wwt-QF-vqc">
                                                            <rect key="frame" x="0.0" y="0.0" width="342" height="72"/>
                                                            <autoresizingMask key="autoresizingMask"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZEp-2P-gxn" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                                    <rect key="frame" x="16" y="0.0" width="310" height="48"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gwy-Zc-QZ4">
                                                                            <rect key="frame" x="16" y="14.3**************" width="278" height="19.**************4"/>
                                                                            <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="16"/>
                                                                            <nil key="textColor"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" name="Grey3"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="trailing" secondItem="gwy-Zc-QZ4" secondAttribute="trailing" constant="16" id="HNV-fh-eHF"/>
                                                                        <constraint firstItem="gwy-Zc-QZ4" firstAttribute="leading" secondItem="ZEp-2P-gxn" secondAttribute="leading" constant="16" id="Kbu-4Q-s9L"/>
                                                                        <constraint firstAttribute="height" constant="48" id="aFQ-JZ-a6W"/>
                                                                        <constraint firstItem="gwy-Zc-QZ4" firstAttribute="centerY" secondItem="ZEp-2P-gxn" secondAttribute="centerY" id="fM5-sn-Iol"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                            <real key="value" value="16"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                            </subviews>
                                                            <constraints>
                                                                <constraint firstAttribute="trailing" secondItem="ZEp-2P-gxn" secondAttribute="trailing" constant="16" id="Ub1-4E-Dph"/>
                                                                <constraint firstItem="ZEp-2P-gxn" firstAttribute="leading" secondItem="wwt-QF-vqc" secondAttribute="leading" constant="16" id="fTU-uE-RxC"/>
                                                                <constraint firstItem="ZEp-2P-gxn" firstAttribute="top" secondItem="wwt-QF-vqc" secondAttribute="top" id="uPm-YK-nX1"/>
                                                                <constraint firstAttribute="bottom" secondItem="ZEp-2P-gxn" secondAttribute="bottom" constant="24" id="z2N-h2-UaI"/>
                                                            </constraints>
                                                        </tableViewCellContentView>
                                                        <connections>
                                                            <outlet property="menuLabel" destination="gwy-Zc-QZ4" id="YIa-8c-ZFJ"/>
                                                        </connections>
                                                    </tableViewCell>
                                                </prototypes>
                                                <connections>
                                                    <outlet property="dataSource" destination="qRX-6a-6b2" id="BLP-4C-jdX"/>
                                                    <outlet property="delegate" destination="qRX-6a-6b2" id="Aou-f8-0it"/>
                                                </connections>
                                            </tableView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="3O4-yl-R0n" firstAttribute="leading" secondItem="lZK-el-Vuq" secondAttribute="leading" constant="24" id="3Hz-CV-DQB"/>
                                            <constraint firstItem="pJI-og-yd5" firstAttribute="leading" secondItem="lZK-el-Vuq" secondAttribute="leading" id="Hom-95-T0D"/>
                                            <constraint firstAttribute="trailing" secondItem="3O4-yl-R0n" secondAttribute="trailing" constant="24" id="RMC-79-avI"/>
                                            <constraint firstItem="3O4-yl-R0n" firstAttribute="top" secondItem="pJI-og-yd5" secondAttribute="bottom" constant="16" id="dJ0-jt-b7h"/>
                                            <constraint firstItem="pJI-og-yd5" firstAttribute="top" secondItem="lZK-el-Vuq" secondAttribute="top" constant="24" id="gvP-gh-Jbh"/>
                                            <constraint firstAttribute="trailing" secondItem="pJI-og-yd5" secondAttribute="trailing" id="vUB-cY-ZYN"/>
                                            <constraint firstAttribute="bottom" secondItem="3O4-yl-R0n" secondAttribute="bottom" constant="16" id="ywW-C5-CHg"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="lZK-el-Vuq" firstAttribute="leading" secondItem="wsg-m2-SQK" secondAttribute="leading" id="1E4-AB-oyf"/>
                                    <constraint firstItem="lZK-el-Vuq" firstAttribute="trailing" secondItem="wsg-m2-SQK" secondAttribute="trailing" id="NLW-uC-bGR"/>
                                    <constraint firstItem="lZK-el-Vuq" firstAttribute="top" secondItem="wsg-m2-SQK" secondAttribute="top" id="Pst-9o-muX"/>
                                    <constraint firstAttribute="bottom" secondItem="lZK-el-Vuq" secondAttribute="bottom" id="rdm-Qd-7ba"/>
                                    <constraint firstAttribute="height" constant="623" id="tbW-gF-cHy"/>
                                    <constraint firstItem="lZK-el-Vuq" firstAttribute="width" secondItem="wsg-m2-SQK" secondAttribute="width" id="tnA-WN-qeP"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="zC1-hI-j6j"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="wsg-m2-SQK" firstAttribute="top" secondItem="zC1-hI-j6j" secondAttribute="top" id="1bK-9o-mlq"/>
                            <constraint firstItem="wsg-m2-SQK" firstAttribute="width" secondItem="tIk-rT-780" secondAttribute="width" id="2dk-1S-0Qr"/>
                            <constraint firstItem="wsg-m2-SQK" firstAttribute="bottom" secondItem="tIk-rT-780" secondAttribute="bottom" id="Tsf-ew-xaV"/>
                            <constraint firstItem="wsg-m2-SQK" firstAttribute="leading" secondItem="zC1-hI-j6j" secondAttribute="leading" id="ldY-zQ-nS7"/>
                            <constraint firstItem="zC1-hI-j6j" firstAttribute="trailing" secondItem="wsg-m2-SQK" secondAttribute="trailing" id="v22-3r-vV2"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="oqA-vR-fdn">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="1Fn-Sa-avP" userLabel="Back">
                            <inset key="imageInsets" minX="8" minY="0.0" maxX="0.0" maxY="0.0"/>
                            <color key="tintColor" name="PrimaryBlack"/>
                            <connections>
                                <action selector="didTapBack:" destination="qRX-6a-6b2" id="5UA-fy-5An"/>
                            </connections>
                        </barButtonItem>
                        <barButtonItem key="rightBarButtonItem" image="iconQR" id="UNE-gq-Reb">
                            <inset key="imageInsets" minX="0.0" minY="0.0" maxX="8" maxY="0.0"/>
                            <color key="tintColor" name="PrimaryBlack"/>
                            <connections>
                                <action selector="didTapProfileQrButton:" destination="qRX-6a-6b2" id="5F9-hL-90Z"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <connections>
                        <outlet property="accountStatusContainerStackView" destination="4vp-cj-Uxu" id="OzV-vv-muY"/>
                        <outlet property="accountStatusView" destination="MDq-lP-T37" id="F14-LY-izz"/>
                        <outlet property="cleverTapBannersContainerStackView" destination="YBe-Xt-Xhd" id="7AR-hi-ymc"/>
                        <outlet property="createUsernameView" destination="8ON-gf-I9a" id="2h0-Cm-Rgk"/>
                        <outlet property="deviceIDLabel" destination="cbg-40-vjU" id="Yrc-Bh-PeL"/>
                        <outlet property="fullNameLabel" destination="4eu-zL-Arp" id="Acv-WI-ucv"/>
                        <outlet property="internalIDsStackView" destination="Sbg-Zt-bqK" id="Zbs-Go-Yex"/>
                        <outlet property="inviteFriendsView" destination="xtM-j0-fsg" id="dqb-Yq-o69"/>
                        <outlet property="menuTableView" destination="3O4-yl-R0n" id="eM4-O3-dM7"/>
                        <outlet property="menuTableViewHeightConstraint" destination="YgF-63-8QN" id="4aC-de-LmB"/>
                        <outlet property="mobileNumberLabel" destination="bch-G1-fCC" id="XSi-pV-7Vr"/>
                        <outlet property="profileDetailsView" destination="v85-4B-975" id="05f-xq-ddn"/>
                        <outlet property="profileImageView" destination="Wh2-vk-Geq" id="Mkh-Fa-eRo"/>
                        <outlet property="profileStackView" destination="pJI-og-yd5" id="Jsj-6I-ddj"/>
                        <outlet property="userDetailsContainerStackView" destination="LZh-VN-Vvr" id="QQB-Cw-gOC"/>
                        <outlet property="userDetailsStackView" destination="kdh-qd-Ct3" id="nVR-i6-yiU"/>
                        <outlet property="usernameLabel" destination="8Ma-Z4-cgI" id="lST-dG-bPE"/>
                        <outlet property="walletIDLabel" destination="VLB-Am-KC1" id="uu0-ee-0ct"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="BMO-e6-3yg" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
                <view contentMode="scaleToFill" id="8ON-gf-I9a" customClass="MayaProfileCreateUsernameView" customModule="PayMaya" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="240" height="128"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <viewLayoutGuide key="safeArea" id="2QQ-6D-aIq"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                </view>
                <view contentMode="scaleToFill" id="xtM-j0-fsg" customClass="MayaProfileReferralView" customModule="PayMaya" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="240" height="141"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <viewLayoutGuide key="safeArea" id="iR1-ee-1tI"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                </view>
                <view contentMode="scaleToFill" id="MDq-lP-T37" customClass="MayaProfileAccountUpgradeView" customModule="PayMaya" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="240" height="141"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <viewLayoutGuide key="safeArea" id="V3Z-Zy-6tf"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                </view>
                <label hidden="YES" opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="deviceID" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" id="vu2-Zl-tIL">
                    <rect key="frame" x="0.0" y="0.0" width="58.***************" height="0.0"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                    <color key="textColor" name="Grey6"/>
                    <nil key="highlightedColor"/>
                </label>
            </objects>
            <point key="canvasLocation" x="8271.*************" y="623.**************"/>
        </scene>
        <!--Maya Account Limits -->
        <scene sceneID="E9e-RY-0TY">
            <objects>
                <viewController storyboardIdentifier="MayaAccountLimitsViewController" title="Account Limits" useStoryboardIdentifierAsRestorationIdentifier="YES" id="DIr-Ty-dCd" userLabel="Maya Account Limits " customClass="MayaAccountLimitsViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="9ne-GU-Xm2">
                        <rect key="frame" x="0.0" y="0.0" width="390" height="844"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="0DQ-vw-P6b">
                                <rect key="frame" x="0.0" y="47" width="390" height="763"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qu0-T7-KUg">
                                        <rect key="frame" x="0.0" y="0.0" width="390" height="46"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OQ6-Sb-tCw" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="0.0" width="342" height="46"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="VMA-qd-vL5" customClass="MayaMenuBar" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="6" y="6" width="330" height="34"/>
                                                        <color key="backgroundColor" name="Grey1"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="color" keyPath="accentBackgroundColor">
                                                                <color key="value" name="PrimaryWhite"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="color" keyPath="normalBackgroundColor">
                                                                <color key="value" name="Grey1"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="color" keyPath="normalColor">
                                                                <color key="value" name="Grey4"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="selectedItemTextPadding">
                                                                <real key="value" value="15"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <outlet property="delegate" destination="DIr-Ty-dCd" id="2kw-c4-Sqn"/>
                                                        </connections>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" name="BackgroundGrey1"/>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="VMA-qd-vL5" secondAttribute="trailing" constant="6" id="Km8-eI-X4m"/>
                                                    <constraint firstAttribute="bottom" secondItem="VMA-qd-vL5" secondAttribute="bottom" constant="6" id="LjE-4g-tXW"/>
                                                    <constraint firstItem="VMA-qd-vL5" firstAttribute="leading" secondItem="OQ6-Sb-tCw" secondAttribute="leading" constant="6" id="T9q-2v-hig"/>
                                                    <constraint firstItem="VMA-qd-vL5" firstAttribute="top" secondItem="OQ6-Sb-tCw" secondAttribute="top" constant="6" id="cOM-b9-2fE"/>
                                                    <constraint firstAttribute="height" constant="46" id="ddJ-H2-KB9"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                        <real key="value" value="23"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="OQ6-Sb-tCw" secondAttribute="trailing" constant="24" id="CtA-OA-elD"/>
                                            <constraint firstItem="OQ6-Sb-tCw" firstAttribute="top" secondItem="qu0-T7-KUg" secondAttribute="top" id="Pw0-2f-bf3"/>
                                            <constraint firstItem="OQ6-Sb-tCw" firstAttribute="leading" secondItem="qu0-T7-KUg" secondAttribute="leading" constant="24" id="V5f-Cv-pWG"/>
                                            <constraint firstAttribute="bottom" secondItem="OQ6-Sb-tCw" secondAttribute="bottom" id="sYz-0Q-bZx"/>
                                        </constraints>
                                    </view>
                                    <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="HMn-LH-bsq">
                                        <rect key="frame" x="0.0" y="46" width="390" height="717"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZPC-62-9Xp">
                                                <rect key="frame" x="0.0" y="0.0" width="390" height="717"/>
                                                <subviews>
                                                    <tableView clipsSubviews="YES" contentMode="scaleToFill" bounces="NO" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="1" sectionFooterHeight="1" translatesAutoresizingMaskIntoConstraints="NO" id="Pnc-PZ-jvM">
                                                        <rect key="frame" x="0.0" y="4" width="390" height="713"/>
                                                        <color key="backgroundColor" name="PrimaryWhite"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <outlet property="dataSource" destination="DIr-Ty-dCd" id="b9D-mZ-vP9"/>
                                                            <outlet property="delegate" destination="DIr-Ty-dCd" id="1V3-tQ-DPI"/>
                                                        </connections>
                                                    </tableView>
                                                </subviews>
                                                <color key="backgroundColor" name="PrimaryWhite"/>
                                                <constraints>
                                                    <constraint firstItem="Pnc-PZ-jvM" firstAttribute="leading" secondItem="ZPC-62-9Xp" secondAttribute="leading" id="5JH-Uo-wcS"/>
                                                    <constraint firstAttribute="bottom" secondItem="Pnc-PZ-jvM" secondAttribute="bottom" id="9qr-QT-XR0"/>
                                                    <constraint firstItem="Pnc-PZ-jvM" firstAttribute="top" secondItem="ZPC-62-9Xp" secondAttribute="top" constant="4" id="ehF-n6-TPg"/>
                                                    <constraint firstAttribute="trailing" secondItem="Pnc-PZ-jvM" secondAttribute="trailing" id="wIf-EO-9PG"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" name="PrimaryWhite"/>
                                        <constraints>
                                            <constraint firstItem="ZPC-62-9Xp" firstAttribute="width" secondItem="HMn-LH-bsq" secondAttribute="width" id="Hsq-aU-iyU"/>
                                            <constraint firstAttribute="bottom" secondItem="ZPC-62-9Xp" secondAttribute="bottom" id="ILA-lN-Ckg"/>
                                            <constraint firstItem="ZPC-62-9Xp" firstAttribute="leading" secondItem="HMn-LH-bsq" secondAttribute="leading" id="Jjw-Yp-UpY"/>
                                            <constraint firstItem="ZPC-62-9Xp" firstAttribute="height" secondItem="HMn-LH-bsq" secondAttribute="height" id="UtK-Nw-z3c"/>
                                            <constraint firstItem="ZPC-62-9Xp" firstAttribute="top" secondItem="HMn-LH-bsq" secondAttribute="top" id="e7I-HX-rGm"/>
                                            <constraint firstAttribute="trailing" secondItem="ZPC-62-9Xp" secondAttribute="trailing" id="p5B-of-4Eb"/>
                                        </constraints>
                                    </scrollView>
                                </subviews>
                                <color key="backgroundColor" name="PrimaryWhite"/>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="drJ-of-iSN"/>
                        <color key="backgroundColor" name="PrimaryWhite"/>
                        <constraints>
                            <constraint firstItem="drJ-of-iSN" firstAttribute="trailing" secondItem="0DQ-vw-P6b" secondAttribute="trailing" id="dLR-X4-aF6"/>
                            <constraint firstItem="0DQ-vw-P6b" firstAttribute="top" secondItem="drJ-of-iSN" secondAttribute="top" id="eaF-X6-yE6"/>
                            <constraint firstItem="0DQ-vw-P6b" firstAttribute="leading" secondItem="drJ-of-iSN" secondAttribute="leading" id="iOM-EO-nCN"/>
                            <constraint firstItem="drJ-of-iSN" firstAttribute="bottom" secondItem="0DQ-vw-P6b" secondAttribute="bottom" id="yGu-qD-IQs"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="Account Limits" id="5rE-ag-xAZ">
                        <barButtonItem key="leftBarButtonItem" title="Back" id="0aa-nw-iNP">
                            <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <connections>
                                <action selector="didTapBack:" destination="DIr-Ty-dCd" id="nAl-2V-5Wp"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="accountLimitsTableView" destination="Pnc-PZ-jvM" id="BEL-ec-fpQ"/>
                        <outlet property="baseContainerView" destination="ZPC-62-9Xp" id="GSZ-zE-Kqu"/>
                        <outlet property="errorView" destination="9wR-BA-RZV" id="OA0-yd-Zmg"/>
                        <outlet property="footerView" destination="qBY-DJ-cOt" id="J0A-G6-5Sx"/>
                        <outlet property="learnMoreTextView" destination="bTa-FG-SG4" id="qW5-nk-OCv"/>
                        <outlet property="menuBar" destination="VMA-qd-vL5" id="ZlG-YR-9v8"/>
                        <outlet property="scrollView" destination="HMn-LH-bsq" id="AeX-Cb-oYV"/>
                        <outlet property="upgradeAccountView" destination="UXN-Mw-ISD" id="0db-rf-fPY"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="OeJ-yo-off" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <view contentMode="scaleToFill" id="qBY-DJ-cOt">
                    <rect key="frame" x="0.0" y="0.0" width="336" height="244"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" spacing="32" translatesAutoresizingMaskIntoConstraints="NO" id="ePC-7s-6DT">
                            <rect key="frame" x="24" y="6" width="288" height="218"/>
                            <subviews>
                                <view contentMode="scaleToFill" verticalHuggingPriority="252" verticalCompressionResistancePriority="751" translatesAutoresizingMaskIntoConstraints="NO" id="aZg-G7-tGz">
                                    <rect key="frame" x="0.0" y="0.0" width="288" height="55.**************4"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconInfo" translatesAutoresizingMaskIntoConstraints="NO" id="kup-43-5HX">
                                            <rect key="frame" x="0.0" y="0.0" width="16" height="16"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="16" id="L2c-7N-V97"/>
                                                <constraint firstAttribute="width" constant="16" id="XWV-xy-fQ2"/>
                                            </constraints>
                                        </imageView>
                                        <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" editable="NO" text="Limits will reset tomorrow. Learn more" translatesAutoresizingMaskIntoConstraints="NO" id="bTa-FG-SG4">
                                            <rect key="frame" x="22" y="0.0" width="266" height="55.**************4"/>
                                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                            <color key="textColor" name="ContentGrey5"/>
                                            <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="12"/>
                                            <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                            <dataDetectorType key="dataDetectorTypes" link="YES"/>
                                            <connections>
                                                <outlet property="delegate" destination="DIr-Ty-dCd" id="cVh-fu-4f1"/>
                                            </connections>
                                        </textView>
                                    </subviews>
                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    <constraints>
                                        <constraint firstAttribute="bottom" secondItem="bTa-FG-SG4" secondAttribute="bottom" id="CFK-l3-JxH"/>
                                        <constraint firstItem="bTa-FG-SG4" firstAttribute="top" secondItem="aZg-G7-tGz" secondAttribute="top" id="CLm-f6-YdP"/>
                                        <constraint firstAttribute="trailing" secondItem="bTa-FG-SG4" secondAttribute="trailing" id="Dcq-bi-KYr"/>
                                        <constraint firstItem="kup-43-5HX" firstAttribute="top" secondItem="aZg-G7-tGz" secondAttribute="top" id="FB2-cY-Xle"/>
                                        <constraint firstItem="kup-43-5HX" firstAttribute="leading" secondItem="aZg-G7-tGz" secondAttribute="leading" id="IT2-6C-y17"/>
                                        <constraint firstItem="bTa-FG-SG4" firstAttribute="leading" secondItem="kup-43-5HX" secondAttribute="trailing" constant="6" id="sfH-gQ-jV3"/>
                                    </constraints>
                                </view>
                                <imageView clipsSubviews="YES" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="imageUpgradeMyAccount" translatesAutoresizingMaskIntoConstraints="NO" id="UXN-Mw-ISD">
                                    <rect key="frame" x="0.0" y="87.***************" width="288" height="130.**************"/>
                                    <constraints>
                                        <constraint firstAttribute="width" secondItem="UXN-Mw-ISD" secondAttribute="height" multiplier="327:148" id="dyx-FF-UcU"/>
                                    </constraints>
                                </imageView>
                            </subviews>
                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            <constraints>
                                <constraint firstItem="UXN-Mw-ISD" firstAttribute="width" secondItem="ePC-7s-6DT" secondAttribute="width" id="oKA-zR-EiA"/>
                            </constraints>
                        </stackView>
                    </subviews>
                    <viewLayoutGuide key="safeArea" id="aJr-XP-Rph"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="ePC-7s-6DT" secondAttribute="trailing" constant="24" id="J2T-0a-4Xd"/>
                        <constraint firstItem="ePC-7s-6DT" firstAttribute="leading" secondItem="qBY-DJ-cOt" secondAttribute="leading" constant="24" id="s3J-q4-4XB"/>
                        <constraint firstItem="ePC-7s-6DT" firstAttribute="top" secondItem="qBY-DJ-cOt" secondAttribute="top" constant="6" id="urt-xC-0Qn"/>
                        <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="ePC-7s-6DT" secondAttribute="bottom" priority="999" constant="20" id="zMV-Nz-SOC"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" id="9wR-BA-RZV" customClass="MayaDefaultErrorView" customModule="PayMaya" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="361" height="523"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <viewLayoutGuide key="safeArea" id="mJ7-eI-TCf"/>
                    <color key="backgroundColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="calibratedRGB"/>
                </view>
            </objects>
            <point key="canvasLocation" x="4697" y="1334"/>
        </scene>
        <!--Maya Account Limits Empty-->
        <scene sceneID="1a4-GB-LwP">
            <objects>
                <viewController storyboardIdentifier="MayaAccountLimitsEmptyViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="2S6-h3-5XI" userLabel="Maya Account Limits Empty" customClass="MayaAccountLimitsEmptyViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="hGJ-vu-tm5">
                        <rect key="frame" x="0.0" y="0.0" width="390" height="753"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="zKs-lg-Eya">
                                <rect key="frame" x="48" y="0.0" width="294" height="753"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wVf-2r-VSP" userLabel="Top Spacer View">
                                        <rect key="frame" x="0.0" y="0.0" width="294" height="181.**************"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" relation="greaterThanOrEqual" id="jbP-zo-RsQ"/>
                                        </constraints>
                                    </view>
                                    <stackView opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" axis="vertical" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="B6y-xE-aKU">
                                        <rect key="frame" x="0.0" y="181.**************" width="294" height="364.66666666666674"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" image="imageWalletBalance" translatesAutoresizingMaskIntoConstraints="NO" id="eAW-oF-0Gb">
                                                <rect key="frame" x="47.000000000000014" y="0.0" width="200.33333333333337" height="200"/>
                                            </imageView>
                                            <stackView opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" axis="vertical" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="VP1-MW-E2f">
                                                <rect key="frame" x="0.0" y="200" width="294" height="164.66666666666663"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="NUe-is-zuW">
                                                        <rect key="frame" x="0.0" y="0.0" width="294" height="84.***************"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" text="We can’t show your limits yet" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yS2-U7-QcT">
                                                                <rect key="frame" x="0.0" y="0.0" width="294" height="58.**************6"/>
                                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                                                <color key="textColor" name="ContentPrimaryBlack"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" text="Cash in to view your account limits" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mxP-Bh-AQn">
                                                                <rect key="frame" x="0.0" y="66.***************" width="294" height="18.***************"/>
                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                                <color key="textColor" name="ContentGrey6"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="UXI-kN-qph" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="108.**************" width="294" height="56"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_account_limits_empty_add_money_button"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="56" id="1EH-pe-ZVi"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                        <state key="normal" title="Cash in"/>
                                                        <connections>
                                                            <action selector="didTapCashIn:" destination="2S6-h3-5XI" eventType="touchUpInside" id="IUz-We-kIH"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="VP1-MW-E2f" firstAttribute="leading" secondItem="B6y-xE-aKU" secondAttribute="leading" id="SdS-IL-flY"/>
                                            <constraint firstAttribute="trailing" secondItem="VP1-MW-E2f" secondAttribute="trailing" id="Xav-CT-5v0"/>
                                        </constraints>
                                    </stackView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Vgd-t5-Nby" userLabel="Bottom Spacer View">
                                        <rect key="frame" x="0.0" y="546.33333333333337" width="294" height="206.66666666666663"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="Vgd-t5-Nby" firstAttribute="height" secondItem="wVf-2r-VSP" secondAttribute="height" constant="25" id="zxd-hT-6Np"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="5r2-fv-qwY"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="zKs-lg-Eya" firstAttribute="leading" secondItem="5r2-fv-qwY" secondAttribute="leading" constant="48" id="3Lh-zS-OTs"/>
                            <constraint firstItem="5r2-fv-qwY" firstAttribute="trailing" secondItem="zKs-lg-Eya" secondAttribute="trailing" constant="48" id="EeB-e0-BL1"/>
                            <constraint firstAttribute="bottom" secondItem="zKs-lg-Eya" secondAttribute="bottom" id="Hh5-gZ-H8I"/>
                            <constraint firstItem="zKs-lg-Eya" firstAttribute="top" secondItem="hGJ-vu-tm5" secondAttribute="top" id="lLs-hf-Yfv"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="Account Limits" id="MyX-iR-kVt">
                        <barButtonItem key="leftBarButtonItem" title="Back" id="VHb-W3-uRx">
                            <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <connections>
                                <action selector="didTapBack:" destination="2S6-h3-5XI" id="zMC-54-hVg"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" translucent="NO" prompted="NO"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dyb-nL-Edb" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="6233" y="1334"/>
        </scene>
        <!--Maya Change Password-->
        <scene sceneID="O6I-Z9-M2g">
            <objects>
                <viewController storyboardIdentifier="MayaChangePasswordViewController" title="Maya Change Password" useStoryboardIdentifierAsRestorationIdentifier="YES" id="OVy-cR-R7w" customClass="MayaChangePasswordViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="E4Z-gR-Pdq">
                        <rect key="frame" x="0.0" y="0.0" width="390" height="753"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZHj-B2-JsG">
                                <rect key="frame" x="0.0" y="0.0" width="390" height="615"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dHk-2b-knj">
                                        <rect key="frame" x="0.0" y="0.0" width="390" height="206"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="egA-GZ-9Ff">
                                                <rect key="frame" x="16" y="16" width="358" height="174"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JD1-kt-myQ" customClass="MayaValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="358" height="50"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_change_password_current_password_text_field">
                                                            <bool key="isElement" value="YES"/>
                                                        </accessibility>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="hintText" value="Current password"/>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="placeholderText" value="Enter current password"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OzM-Ws-U3F" customClass="MayaValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="62" width="358" height="50"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_change_password_new_password_text_field">
                                                            <bool key="isElement" value="YES"/>
                                                        </accessibility>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="hintText" value="New password"/>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="placeholderText" value="Enter new password"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="RzW-hf-Mv9" customClass="MayaValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="124" width="358" height="50"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_change_password_retype_new_password_text_field">
                                                            <bool key="isElement" value="YES"/>
                                                        </accessibility>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="hintText" value="Confirm new password"/>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="placeholderText" value="Enter new password"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                        <constraints>
                                            <constraint firstItem="egA-GZ-9Ff" firstAttribute="top" secondItem="dHk-2b-knj" secondAttribute="top" constant="16" id="0ip-Yz-4gT"/>
                                            <constraint firstItem="egA-GZ-9Ff" firstAttribute="leading" secondItem="dHk-2b-knj" secondAttribute="leading" constant="16" id="5Gd-u5-QZ9"/>
                                            <constraint firstAttribute="trailing" secondItem="egA-GZ-9Ff" secondAttribute="trailing" constant="16" id="WDZ-WF-ADa"/>
                                            <constraint firstAttribute="bottom" secondItem="egA-GZ-9Ff" secondAttribute="bottom" constant="16" id="bd1-JW-JnV"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="dHk-2b-knj" firstAttribute="leading" secondItem="ZHj-B2-JsG" secondAttribute="leading" id="NnC-mJ-xRk"/>
                                    <constraint firstAttribute="trailing" secondItem="dHk-2b-knj" secondAttribute="trailing" id="QUN-F7-TyY"/>
                                    <constraint firstItem="dHk-2b-knj" firstAttribute="top" secondItem="ZHj-B2-JsG" secondAttribute="top" id="bxu-xX-ubz"/>
                                    <constraint firstAttribute="bottom" secondItem="dHk-2b-knj" secondAttribute="bottom" id="cra-yi-zQc"/>
                                </constraints>
                            </scrollView>
                            <view contentMode="scaleToFill" placeholderIntrinsicWidth="infinite" placeholderIntrinsicHeight="104" translatesAutoresizingMaskIntoConstraints="NO" id="DaW-TB-S5r" customClass="MayaConfirmationView" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="8" y="615" width="374" height="104"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="bUg-c0-b6L"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                        <gestureRecognizers/>
                        <constraints>
                            <constraint firstItem="ZHj-B2-JsG" firstAttribute="leading" secondItem="bUg-c0-b6L" secondAttribute="leading" id="2X2-Pc-dBO"/>
                            <constraint firstItem="bUg-c0-b6L" firstAttribute="trailing" secondItem="ZHj-B2-JsG" secondAttribute="trailing" id="86F-ug-b5l"/>
                            <constraint firstItem="DaW-TB-S5r" firstAttribute="leading" secondItem="ZHj-B2-JsG" secondAttribute="leadingMargin" id="CIk-AU-DeY"/>
                            <constraint firstItem="bUg-c0-b6L" firstAttribute="bottom" secondItem="DaW-TB-S5r" secondAttribute="bottom" id="QSl-wR-647"/>
                            <constraint firstItem="ZHj-B2-JsG" firstAttribute="top" secondItem="bUg-c0-b6L" secondAttribute="top" id="ZOd-2V-7No"/>
                            <constraint firstItem="dHk-2b-knj" firstAttribute="width" secondItem="E4Z-gR-Pdq" secondAttribute="width" id="b2m-yY-jku"/>
                            <constraint firstItem="DaW-TB-S5r" firstAttribute="trailing" secondItem="ZHj-B2-JsG" secondAttribute="trailingMargin" id="cFc-UG-pHs"/>
                            <constraint firstItem="ZHj-B2-JsG" firstAttribute="bottom" secondItem="DaW-TB-S5r" secondAttribute="top" id="dpk-MF-ZBa"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="isMayaForm" value="YES"/>
                        </userDefinedRuntimeAttributes>
                        <connections>
                            <outletCollection property="gestureRecognizers" destination="f6O-HQ-w9L" appends="YES" id="Mtf-gw-rnZ"/>
                        </connections>
                    </view>
                    <extendedEdge key="edgesForExtendedLayout"/>
                    <navigationItem key="navigationItem" title="Change password" id="SjI-u8-XxC">
                        <barButtonItem key="leftBarButtonItem" title="Back" id="Qno-1F-Yoc">
                            <color key="tintColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                            <connections>
                                <action selector="didTapBack:" destination="OVy-cR-R7w" id="21o-QS-Fyf"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" translucent="NO" prompted="NO"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isMayaForm" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="confirmationView" destination="DaW-TB-S5r" id="3dP-S7-kLF"/>
                        <outlet property="currentPasswordView" destination="JD1-kt-myQ" id="nQT-oF-ZUw"/>
                        <outlet property="newPasswordView" destination="OzM-Ws-U3F" id="EGP-57-1NP"/>
                        <outlet property="retypePasswordView" destination="RzW-hf-Mv9" id="vX7-Et-4Jf"/>
                        <outlet property="scrollView" destination="ZHj-B2-JsG" id="cDb-rD-SBa"/>
                        <outlet property="stickyViewBottomConstraint" destination="QSl-wR-647" id="D93-ok-MNE"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="6Dc-8O-FdR" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <tapGestureRecognizer id="f6O-HQ-w9L">
                    <connections>
                        <action selector="dismissKeyboard:" destination="OVy-cR-R7w" id="3Ae-wP-q4O"/>
                    </connections>
                </tapGestureRecognizer>
            </objects>
            <point key="canvasLocation" x="5453" y="2018"/>
        </scene>
        <!--Maya Change Password Tips View Controller-->
        <scene sceneID="cta-od-2qs">
            <objects>
                <viewController storyboardIdentifier="MayaChangePasswordTipsViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="2LO-GE-Bt1" customClass="MayaChangePasswordTipsViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="7iI-Qr-IUc">
                        <rect key="frame" x="0.0" y="0.0" width="390" height="844"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FBt-MH-jRb">
                                <rect key="frame" x="0.0" y="592" width="390" height="252"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="7jW-pd-lwo">
                                        <rect key="frame" x="24" y="40" width="342" height="20"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconInfo" translatesAutoresizingMaskIntoConstraints="NO" id="ofX-tD-RFU">
                                                <rect key="frame" x="0.0" y="0.0" width="20" height="20"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="20" id="f4f-dK-UeZ"/>
                                                    <constraint firstAttribute="width" secondItem="ofX-tD-RFU" secondAttribute="height" id="zsv-En-Ag2"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Tips for a secure password" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="w2P-nQ-5Di">
                                                <rect key="frame" x="28" y="0.33333333333337123" width="314" height="19.**************8"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                <color key="textColor" name="PrimaryBlack"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                    <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" editable="NO" usesAttributedText="YES" selectable="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eL4-Fn-jok">
                                        <rect key="frame" x="24" y="68" width="342" height="33"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <attributedString key="attributedText">
                                            <fragment content="I">
                                                <attributes>
                                                    <color key="NSColor" name="Grey6"/>
                                                    <font key="NSFont" metaFont="system" size="14"/>
                                                    <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                </attributes>
                                            </fragment>
                                        </attributedString>
                                        <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                    </textView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="siu-J9-XNp" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="24" y="125" width="342" height="56"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="Q7p-b8-tuY"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Continue"/>
                                        <connections>
                                            <action selector="didTapContinue:" destination="2LO-GE-Bt1" eventType="touchUpInside" id="Tpf-j3-tw1"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" name="PrimaryWhite"/>
                                <constraints>
                                    <constraint firstItem="7jW-pd-lwo" firstAttribute="leading" secondItem="FBt-MH-jRb" secondAttribute="leading" constant="24" id="0fX-nY-Ulj"/>
                                    <constraint firstAttribute="trailing" secondItem="eL4-Fn-jok" secondAttribute="trailing" constant="24" id="2Oj-tz-oHO"/>
                                    <constraint firstItem="7jW-pd-lwo" firstAttribute="top" secondItem="FBt-MH-jRb" secondAttribute="top" constant="40" id="5Ah-4T-HJ8"/>
                                    <constraint firstItem="siu-J9-XNp" firstAttribute="top" secondItem="eL4-Fn-jok" secondAttribute="bottom" constant="24" id="Fun-3J-cdz"/>
                                    <constraint firstAttribute="trailing" secondItem="siu-J9-XNp" secondAttribute="trailing" constant="24" id="LtF-5f-ZDF"/>
                                    <constraint firstItem="eL4-Fn-jok" firstAttribute="leading" secondItem="FBt-MH-jRb" secondAttribute="leading" constant="24" id="Mtv-H4-ov4"/>
                                    <constraint firstAttribute="trailing" secondItem="7jW-pd-lwo" secondAttribute="trailing" constant="24" id="cBQ-OX-dMh"/>
                                    <constraint firstItem="siu-J9-XNp" firstAttribute="leading" secondItem="FBt-MH-jRb" secondAttribute="leading" constant="24" id="lpf-aX-9Z8"/>
                                    <constraint firstItem="eL4-Fn-jok" firstAttribute="top" secondItem="7jW-pd-lwo" secondAttribute="bottom" constant="8" id="o8U-xo-Xjy"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="5as-Ma-7Hf"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="FBt-MH-jRb" firstAttribute="top" relation="greaterThanOrEqual" secondItem="5as-Ma-7Hf" secondAttribute="top" id="1Ht-BZ-T6W"/>
                            <constraint firstAttribute="bottom" secondItem="FBt-MH-jRb" secondAttribute="bottom" id="7Yx-oJ-qdJ"/>
                            <constraint firstItem="5as-Ma-7Hf" firstAttribute="trailing" secondItem="FBt-MH-jRb" secondAttribute="trailing" id="JkC-by-HTR"/>
                            <constraint firstItem="5as-Ma-7Hf" firstAttribute="bottom" secondItem="siu-J9-XNp" secondAttribute="bottom" constant="37" id="d97-Lw-oAo"/>
                            <constraint firstItem="FBt-MH-jRb" firstAttribute="leading" secondItem="5as-Ma-7Hf" secondAttribute="leading" id="wVO-fk-TLR"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isDimmerViewTappable" value="NO"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="aboutTextView" destination="eL4-Fn-jok" id="Rgk-9i-Yb6"/>
                        <outlet property="contentView" destination="FBt-MH-jRb" id="HcF-2l-Oza"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="PYa-lU-epo" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1023" y="3193"/>
        </scene>
        <!--Maya Public Name Display View Controller Scene-->
        <scene sceneID="ck9-l3-odU">
            <objects>
                <viewController storyboardIdentifier="MayaPublicNameDisplayViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="8LN-uz-Xn1" userLabel="Maya Public Name Display View Controller Scene" customClass="MayaPublicNameDisplayViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="wVI-A2-qkh">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="909"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" bouncesZoom="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lbh-h0-rBl">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="875"/>
                                <subviews>
                                    <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="piE-OE-ncA" userLabel="Content View">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="544"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="1b3-UX-1Yu">
                                                <rect key="frame" x="24" y="24" width="327" height="78.***************"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Show my name" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yD8-oh-5S8">
                                                        <rect key="frame" x="0.0" y="0.0" width="327" height="39"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_cash_in_options_title"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="32"/>
                                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Allow others to see your name to ensure that they're transacting with you." lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qob-8W-JAh">
                                                        <rect key="frame" x="0.0" y="47" width="327" height="31.***************"/>
                                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="13"/>
                                                        <color key="textColor" name="ContentGrey5"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qmt-KP-tik" userLabel="Description View" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="142.**************" width="327" height="146.**************"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Shown to others as:" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="g8N-tB-OXh">
                                                        <rect key="frame" x="16" y="16" width="295" height="14.**************4"/>
                                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="12"/>
                                                        <color key="textColor" name="Grey5"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="tSD-Dx-IW5">
                                                        <rect key="frame" x="16" y="34.666666666666657" width="295" height="24"/>
                                                        <subviews>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="otherPublicName" translatesAutoresizingMaskIntoConstraints="NO" id="WSR-0l-V0l">
                                                                <rect key="frame" x="0.0" y="0.0" width="24" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="24" id="3Tt-Dz-0qh"/>
                                                                    <constraint firstAttribute="height" constant="24" id="O6H-6L-EZo"/>
                                                                </constraints>
                                                            </imageView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Maria Christina D." lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DrD-27-eDa">
                                                                <rect key="frame" x="32" y="0.0" width="263" height="24"/>
                                                                <accessibility key="accessibilityConfiguration" identifier="pmios_cash_in_options_title"/>
                                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="18"/>
                                                                <color key="textColor" name="ContentPrimaryBlack"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xNP-Wh-9hS">
                                                        <rect key="frame" x="16" y="77.666666666666657" width="295" height="1"/>
                                                        <color key="backgroundColor" name="Grey3"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="1" id="Cvz-Tq-Cc1"/>
                                                        </constraints>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="e2E-Y5-3nI">
                                                        <rect key="frame" x="16" y="100.**************" width="295" height="24"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Show my name" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FoF-cG-wNo">
                                                                <rect key="frame" x="0.0" y="0.0" width="243" height="24"/>
                                                                <accessibility key="accessibilityConfiguration" identifier="pmios_cash_in_options_title"/>
                                                                <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="16"/>
                                                                <color key="textColor" name="ContentPrimaryBlack"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FFZ-Xu-09k" customClass="MayaSwitch" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="251" y="0.0" width="44" height="24"/>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="24" id="56v-pW-Rjn"/>
                                                                    <constraint firstAttribute="width" constant="44" id="cgp-l2-BWU"/>
                                                                </constraints>
                                                                <connections>
                                                                    <action selector="didTapSwitch:" destination="8LN-uz-Xn1" eventType="valueChanged" id="lDs-d6-R2N"/>
                                                                </connections>
                                                            </view>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" name="PrimaryWhite"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_cash_in_options_partner_list_header_view">
                                                    <bool key="isElement" value="YES"/>
                                                </accessibility>
                                                <constraints>
                                                    <constraint firstItem="tSD-Dx-IW5" firstAttribute="top" secondItem="g8N-tB-OXh" secondAttribute="bottom" constant="4" id="330-Rn-prr"/>
                                                    <constraint firstAttribute="trailing" secondItem="e2E-Y5-3nI" secondAttribute="trailing" constant="16" id="7ff-5D-ehV"/>
                                                    <constraint firstAttribute="bottom" secondItem="e2E-Y5-3nI" secondAttribute="bottom" constant="22" id="E5G-lx-cmK"/>
                                                    <constraint firstItem="g8N-tB-OXh" firstAttribute="top" secondItem="qmt-KP-tik" secondAttribute="top" constant="16" id="Gk7-fY-tnN"/>
                                                    <constraint firstItem="e2E-Y5-3nI" firstAttribute="top" secondItem="xNP-Wh-9hS" secondAttribute="bottom" constant="22" id="M11-4s-ZDd"/>
                                                    <constraint firstItem="g8N-tB-OXh" firstAttribute="leading" secondItem="qmt-KP-tik" secondAttribute="leading" constant="16" id="Uux-DF-YVB"/>
                                                    <constraint firstItem="tSD-Dx-IW5" firstAttribute="leading" secondItem="qmt-KP-tik" secondAttribute="leading" constant="16" id="fUF-TB-99U"/>
                                                    <constraint firstAttribute="trailing" secondItem="g8N-tB-OXh" secondAttribute="trailing" constant="16" id="j52-il-cB6"/>
                                                    <constraint firstAttribute="trailing" secondItem="tSD-Dx-IW5" secondAttribute="trailing" constant="16" id="kvC-vS-RcV"/>
                                                    <constraint firstItem="xNP-Wh-9hS" firstAttribute="leading" secondItem="qmt-KP-tik" secondAttribute="leading" constant="16" id="l5s-4c-WJw"/>
                                                    <constraint firstAttribute="trailing" secondItem="xNP-Wh-9hS" secondAttribute="trailing" constant="16" id="qbo-do-5se"/>
                                                    <constraint firstItem="xNP-Wh-9hS" firstAttribute="top" secondItem="tSD-Dx-IW5" secondAttribute="bottom" constant="19" id="xI2-WE-d01"/>
                                                    <constraint firstItem="e2E-Y5-3nI" firstAttribute="leading" secondItem="qmt-KP-tik" secondAttribute="leading" constant="16" id="zp1-xG-SeA"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                        <real key="value" value="1"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                        <color key="value" name="Grey3"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                        <real key="value" value="16"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="W87-br-O3r" userLabel="Description View" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="313.**************" width="327" height="100.**************"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="How will it be shown?" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vmp-8u-w1X">
                                                        <rect key="frame" x="16" y="16" width="295" height="19.***************"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_cash_in_options_title"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" placeholderIntrinsicWidth="287" placeholderIntrinsicHeight="45" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" editable="NO" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="gmq-Lw-n5H">
                                                        <rect key="frame" x="16" y="39.666666666666686" width="287" height="45"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <string key="text">Here's an example of how your name will
be shown</string>
                                                        <color key="textColor" systemColor="labelColor"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                                    </textView>
                                                </subviews>
                                                <color key="backgroundColor" name="PrimaryWhite"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_cash_in_options_partner_list_header_view">
                                                    <bool key="isElement" value="YES"/>
                                                </accessibility>
                                                <constraints>
                                                    <constraint firstItem="gmq-Lw-n5H" firstAttribute="top" secondItem="vmp-8u-w1X" secondAttribute="bottom" constant="4" id="0my-by-FeC"/>
                                                    <constraint firstAttribute="trailing" secondItem="vmp-8u-w1X" secondAttribute="trailing" constant="16" id="269-BJ-Umn"/>
                                                    <constraint firstItem="gmq-Lw-n5H" firstAttribute="leading" secondItem="W87-br-O3r" secondAttribute="leading" constant="16" id="Kr2-G4-Y0x"/>
                                                    <constraint firstAttribute="bottom" secondItem="gmq-Lw-n5H" secondAttribute="bottom" constant="16" id="PYZ-DN-i1o"/>
                                                    <constraint firstItem="vmp-8u-w1X" firstAttribute="top" secondItem="W87-br-O3r" secondAttribute="top" constant="16" id="ZyA-sw-77q"/>
                                                    <constraint firstItem="vmp-8u-w1X" firstAttribute="leading" secondItem="W87-br-O3r" secondAttribute="leading" constant="16" id="eEw-Ah-pQ7"/>
                                                    <constraint firstAttribute="trailing" secondItem="gmq-Lw-n5H" secondAttribute="trailing" constant="24" id="x9p-35-CZP"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                        <real key="value" value="1"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                        <color key="value" name="Grey3"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                        <real key="value" value="16"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="qmt-KP-tik" secondAttribute="trailing" constant="24" id="Ctr-IO-AVB"/>
                                            <constraint firstItem="qmt-KP-tik" firstAttribute="top" secondItem="1b3-UX-1Yu" secondAttribute="bottom" constant="40" id="DCp-h8-VB5"/>
                                            <constraint firstAttribute="trailing" secondItem="W87-br-O3r" secondAttribute="trailing" constant="24" id="F76-jA-znh"/>
                                            <constraint firstItem="qmt-KP-tik" firstAttribute="leading" secondItem="piE-OE-ncA" secondAttribute="leading" constant="24" id="JP9-Wz-6KZ"/>
                                            <constraint firstItem="W87-br-O3r" firstAttribute="top" secondItem="qmt-KP-tik" secondAttribute="bottom" constant="24" id="OQU-Mc-okJ"/>
                                            <constraint firstAttribute="bottom" secondItem="W87-br-O3r" secondAttribute="bottom" constant="130" id="Xbs-PC-mfe"/>
                                            <constraint firstItem="W87-br-O3r" firstAttribute="leading" secondItem="piE-OE-ncA" secondAttribute="leading" constant="24" id="bFS-A1-Hbf"/>
                                            <constraint firstItem="1b3-UX-1Yu" firstAttribute="leading" secondItem="piE-OE-ncA" secondAttribute="leading" constant="24" id="eMz-xC-Se0"/>
                                            <constraint firstAttribute="trailing" secondItem="1b3-UX-1Yu" secondAttribute="trailing" constant="24" id="ljd-va-C9s"/>
                                            <constraint firstItem="1b3-UX-1Yu" firstAttribute="top" secondItem="piE-OE-ncA" secondAttribute="top" constant="24" id="ny7-ll-PS2"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="piE-OE-ncA" secondAttribute="trailing" id="284-Bx-psD"/>
                                    <constraint firstAttribute="trailing" secondItem="piE-OE-ncA" secondAttribute="trailing" id="8Ij-D8-gdj"/>
                                    <constraint firstItem="piE-OE-ncA" firstAttribute="leading" secondItem="lbh-h0-rBl" secondAttribute="leading" id="AZP-wy-GWl"/>
                                    <constraint firstItem="piE-OE-ncA" firstAttribute="width" secondItem="lbh-h0-rBl" secondAttribute="width" id="Bwh-ux-Pbc"/>
                                    <constraint firstItem="piE-OE-ncA" firstAttribute="top" secondItem="lbh-h0-rBl" secondAttribute="top" id="QBP-he-iuV"/>
                                    <constraint firstAttribute="bottom" secondItem="piE-OE-ncA" secondAttribute="bottom" id="aCh-TX-HuP"/>
                                    <constraint firstItem="piE-OE-ncA" firstAttribute="leading" secondItem="lbh-h0-rBl" secondAttribute="leading" id="qHB-dq-hTJ"/>
                                    <constraint firstItem="piE-OE-ncA" firstAttribute="top" secondItem="lbh-h0-rBl" secondAttribute="top" id="seN-cY-TaQ"/>
                                    <constraint firstAttribute="bottom" secondItem="piE-OE-ncA" secondAttribute="bottom" id="z5c-Wn-qtS"/>
                                </constraints>
                                <variation key="default">
                                    <mask key="constraints">
                                        <exclude reference="8Ij-D8-gdj"/>
                                        <exclude reference="qHB-dq-hTJ"/>
                                        <exclude reference="z5c-Wn-qtS"/>
                                        <exclude reference="seN-cY-TaQ"/>
                                    </mask>
                                </variation>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Ojh-X2-dEp"/>
                        <color key="backgroundColor" red="1" green="0.9999405146" blue="0.99998033050000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="lbh-h0-rBl" firstAttribute="leading" secondItem="Ojh-X2-dEp" secondAttribute="leading" id="DId-Uz-jYK"/>
                            <constraint firstItem="Ojh-X2-dEp" firstAttribute="trailing" secondItem="lbh-h0-rBl" secondAttribute="trailing" id="MeW-sa-vjA"/>
                            <constraint firstItem="lbh-h0-rBl" firstAttribute="top" secondItem="Ojh-X2-dEp" secondAttribute="top" id="SGb-Rl-R9g"/>
                            <constraint firstItem="Ojh-X2-dEp" firstAttribute="trailing" secondItem="lbh-h0-rBl" secondAttribute="trailing" id="c2b-J3-ebQ"/>
                            <constraint firstItem="Ojh-X2-dEp" firstAttribute="bottom" secondItem="lbh-h0-rBl" secondAttribute="bottom" id="cjr-sU-RDh"/>
                            <constraint firstItem="lbh-h0-rBl" firstAttribute="top" secondItem="wVI-A2-qkh" secondAttribute="top" id="dgx-cE-ARM"/>
                            <constraint firstItem="lbh-h0-rBl" firstAttribute="leading" secondItem="Ojh-X2-dEp" secondAttribute="leading" id="x49-r7-KP1"/>
                        </constraints>
                        <variation key="default">
                            <mask key="constraints">
                                <exclude reference="MeW-sa-vjA"/>
                                <exclude reference="dgx-cE-ARM"/>
                                <exclude reference="DId-Uz-jYK"/>
                            </mask>
                        </variation>
                    </view>
                    <navigationItem key="navigationItem" id="xDf-9p-yoA">
                        <barButtonItem key="leftBarButtonItem" title="Back" id="hQy-lH-UkP">
                            <color key="tintColor" red="1" green="0.9999405146" blue="0.99998033050000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <connections>
                                <action selector="didTapBack:" destination="8LN-uz-Xn1" id="W9G-Be-5i6"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" translucent="NO" prompted="NO"/>
                    <size key="freeformSize" width="375" height="1000"/>
                    <connections>
                        <outlet property="publicNameLabel" destination="DrD-27-eDa" id="ON5-YU-3id"/>
                        <outlet property="switchButton" destination="FFZ-Xu-09k" id="Y3Z-ly-CUS"/>
                        <outlet property="tipInfoTextView" destination="gmq-Lw-n5H" id="Snu-3s-6Vg"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dCy-Ln-6q1" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <tapGestureRecognizer id="gxO-zU-GHq">
                    <connections>
                        <action selector="didTapLearnMore:" destination="8LN-uz-Xn1" id="scA-VW-5sp"/>
                    </connections>
                </tapGestureRecognizer>
            </objects>
            <point key="canvasLocation" x="5452" y="4257.571214392804"/>
        </scene>
        <!--Maya Public Name Show Example View Controller-->
        <scene sceneID="arb-Cx-Ahf">
            <objects>
                <viewController storyboardIdentifier="MayaPublicNameShowExampleViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="V8T-Bc-uJb" customClass="MayaPublicNameShowExampleViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="K8g-1E-RAV">
                        <rect key="frame" x="0.0" y="0.0" width="390" height="844"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="LW4-Pg-dG4">
                                <rect key="frame" x="0.0" y="308.**************" width="390" height="535.33333333333326"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="wFz-jh-LJh" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="24" y="408.**************" width="342" height="56"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="4vm-7S-GNz"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Done"/>
                                        <connections>
                                            <action selector="didTapDone:" destination="V8T-Bc-uJb" eventType="touchUpInside" id="RoM-gz-cZa"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Example when sending money" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9gI-7b-fWg">
                                        <rect key="frame" x="32" y="40" width="326" height="24.***************"/>
                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="20"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="imageShowName" translatesAutoresizingMaskIntoConstraints="NO" id="f1o-yx-fVl">
                                        <rect key="frame" x="32" y="104.**************" width="326" height="288"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="288" id="Xeb-gI-eDh"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="f1o-yx-fVl" firstAttribute="top" secondItem="9gI-7b-fWg" secondAttribute="bottom" constant="40" id="3FJ-cy-3Uh"/>
                                    <constraint firstItem="wFz-jh-LJh" firstAttribute="top" secondItem="f1o-yx-fVl" secondAttribute="bottom" constant="16" id="BS7-et-olV"/>
                                    <constraint firstItem="9gI-7b-fWg" firstAttribute="leading" secondItem="LW4-Pg-dG4" secondAttribute="leading" constant="32" id="CWy-pe-ClJ"/>
                                    <constraint firstItem="wFz-jh-LJh" firstAttribute="leading" secondItem="LW4-Pg-dG4" secondAttribute="leading" constant="24" id="Gcy-vv-PLK"/>
                                    <constraint firstAttribute="trailing" secondItem="f1o-yx-fVl" secondAttribute="trailing" constant="32" id="NYn-xx-uho"/>
                                    <constraint firstItem="f1o-yx-fVl" firstAttribute="leading" secondItem="LW4-Pg-dG4" secondAttribute="leading" constant="32" id="VGf-gD-UWC"/>
                                    <constraint firstItem="9gI-7b-fWg" firstAttribute="top" secondItem="LW4-Pg-dG4" secondAttribute="top" constant="40" id="Xii-eC-hbU"/>
                                    <constraint firstAttribute="trailing" secondItem="9gI-7b-fWg" secondAttribute="trailing" constant="32" id="sPg-Ku-sMr"/>
                                    <constraint firstAttribute="trailing" secondItem="wFz-jh-LJh" secondAttribute="trailing" constant="24" id="svI-Zy-BAb"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="L4l-Kx-4nj"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="L4l-Kx-4nj" firstAttribute="bottom" secondItem="wFz-jh-LJh" secondAttribute="bottom" constant="37" id="7R1-g9-D6D"/>
                            <constraint firstItem="LW4-Pg-dG4" firstAttribute="leading" secondItem="L4l-Kx-4nj" secondAttribute="leading" id="F64-2m-0I4"/>
                            <constraint firstItem="L4l-Kx-4nj" firstAttribute="trailing" secondItem="LW4-Pg-dG4" secondAttribute="trailing" id="fiA-e8-AJu"/>
                            <constraint firstAttribute="bottom" secondItem="LW4-Pg-dG4" secondAttribute="bottom" id="lBX-0c-jW6"/>
                            <constraint firstItem="LW4-Pg-dG4" firstAttribute="top" relation="greaterThanOrEqual" secondItem="L4l-Kx-4nj" secondAttribute="top" id="uNT-Ak-cHe"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isDimmerViewTappable" value="NO"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="contentView" destination="LW4-Pg-dG4" id="7MI-Cr-77T"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="gHz-eg-4jt" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="4613.6000000000004" y="4320.9895052473767"/>
        </scene>
        <!--Settings-->
        <scene sceneID="cF4-g8-MYs">
            <objects>
                <viewController storyboardIdentifier="MayaSettingsViewController" id="LZj-gg-Teo" customClass="MayaSettingsViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="J26-wW-thU">
                        <rect key="frame" x="0.0" y="0.0" width="390" height="753"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" dataMode="prototypes" style="grouped" separatorStyle="none" rowHeight="52" sectionHeaderHeight="18" sectionFooterHeight="1" translatesAutoresizingMaskIntoConstraints="NO" id="kKI-1h-1AR">
                                <rect key="frame" x="0.0" y="0.0" width="390" height="753"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <color key="separatorColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <inset key="separatorInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                <sections/>
                                <connections>
                                    <outlet property="dataSource" destination="LZj-gg-Teo" id="Nn7-Nn-t8T"/>
                                    <outlet property="delegate" destination="LZj-gg-Teo" id="VSg-5E-dtB"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="ly4-JT-pG4"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                        <constraints>
                            <constraint firstItem="kKI-1h-1AR" firstAttribute="leading" secondItem="ly4-JT-pG4" secondAttribute="leading" id="3bU-uI-lX7"/>
                            <constraint firstItem="kKI-1h-1AR" firstAttribute="top" secondItem="ly4-JT-pG4" secondAttribute="top" id="Cdg-L5-ZtE"/>
                            <constraint firstAttribute="bottom" secondItem="kKI-1h-1AR" secondAttribute="bottom" id="Opy-yp-nQC"/>
                            <constraint firstItem="kKI-1h-1AR" firstAttribute="trailing" secondItem="ly4-JT-pG4" secondAttribute="trailing" id="p6h-7D-XMM"/>
                        </constraints>
                    </view>
                    <extendedEdge key="edgesForExtendedLayout"/>
                    <navigationItem key="navigationItem" title="Settings" id="fhV-z0-qqp">
                        <barButtonItem key="leftBarButtonItem" title="Back" id="XCp-3e-ine">
                            <color key="tintColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                            <connections>
                                <action selector="didTapBack:" destination="LZj-gg-Teo" id="t8y-wT-6GF"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" translucent="NO" prompted="NO"/>
                    <connections>
                        <outlet property="buildVersionLabel" destination="Avl-LB-xo8" id="Nkm-1k-aXC"/>
                        <outlet property="checkForUpdatesView" destination="rya-3I-idQ" id="bFa-Xj-ka2"/>
                        <outlet property="footerView" destination="uJV-LC-nqZ" id="zKK-ta-0tz"/>
                        <outlet property="settingsTableView" destination="kKI-1h-1AR" id="FRV-9M-nO8"/>
                        <outlet property="updateNowLabel" destination="rgs-vq-uLc" id="Uvi-X6-maC"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="y9A-lE-NvW" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <view contentMode="scaleToFill" id="uJV-LC-nqZ">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="323"/>
                    <autoresizingMask key="autoresizingMask"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="GCx-ws-fXp">
                            <rect key="frame" x="0.0" y="28" width="414" height="279"/>
                            <subviews>
                                <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rya-3I-idQ">
                                    <rect key="frame" x="0.0" y="0.0" width="414" height="0.0"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bKD-ws-5v6" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                            <rect key="frame" x="24" y="0.0" width="366" height="0.0"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="🤘  New update available!" textAlignment="natural" lineBreakMode="wordWrap" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="X8n-P6-LY8">
                                                    <rect key="frame" x="16" y="16" width="334" height="0.0"/>
                                                    <gestureRecognizers/>
                                                    <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                    <color key="textColor" name="PrimaryBlack"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Update now to get the best experience." lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rgs-vq-uLc">
                                                    <rect key="frame" x="46" y="-16" width="304" height="0.0"/>
                                                    <fontDescription key="fontDescription" name="CerebriSansPro-Medium" family="Cerebri Sans Pro" pointSize="14"/>
                                                    <color key="textColor" name="Grey5"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                            <constraints>
                                                <constraint firstItem="X8n-P6-LY8" firstAttribute="top" secondItem="bKD-ws-5v6" secondAttribute="top" constant="16" id="4zg-TB-1pm"/>
                                                <constraint firstItem="rgs-vq-uLc" firstAttribute="leading" secondItem="bKD-ws-5v6" secondAttribute="leading" constant="46" id="B2Q-rl-Jl8"/>
                                                <constraint firstAttribute="bottom" secondItem="rgs-vq-uLc" secondAttribute="bottom" constant="16" id="Jrd-rJ-CNP"/>
                                                <constraint firstAttribute="trailing" secondItem="X8n-P6-LY8" secondAttribute="trailing" constant="16" id="SZA-mU-E62"/>
                                                <constraint firstItem="X8n-P6-LY8" firstAttribute="leading" secondItem="bKD-ws-5v6" secondAttribute="leading" constant="16" id="kDe-ar-3y2"/>
                                                <constraint firstItem="rgs-vq-uLc" firstAttribute="top" secondItem="X8n-P6-LY8" secondAttribute="bottom" priority="999" id="nwQ-Bv-afw"/>
                                                <constraint firstAttribute="trailing" secondItem="rgs-vq-uLc" secondAttribute="trailing" constant="16" id="smw-Nq-cfs"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                    <integer key="value" value="16"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                    <real key="value" value="1"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                    <real key="value" value="16"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                    <color key="value" name="Grey3"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                            <connections>
                                                <outletCollection property="gestureRecognizers" destination="oQP-0Y-l0x" appends="YES" id="eSr-LY-LjF"/>
                                            </connections>
                                        </view>
                                    </subviews>
                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    <constraints>
                                        <constraint firstAttribute="bottom" secondItem="bKD-ws-5v6" secondAttribute="bottom" id="Sd8-Wq-5OY"/>
                                        <constraint firstItem="bKD-ws-5v6" firstAttribute="leading" secondItem="rya-3I-idQ" secondAttribute="leading" constant="24" id="TAC-MD-HEN"/>
                                        <constraint firstAttribute="trailing" secondItem="bKD-ws-5v6" secondAttribute="trailing" constant="24" id="ViO-Nc-2ch"/>
                                        <constraint firstItem="bKD-ws-5v6" firstAttribute="top" secondItem="rya-3I-idQ" secondAttribute="top" id="oG7-nY-WH7"/>
                                    </constraints>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yn2-lG-khT">
                                    <rect key="frame" x="0.0" y="0.0" width="414" height="279"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" ambiguous="YES" text="Version 1.1.5 (79)" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Avl-LB-xo8">
                                            <rect key="frame" x="0.0" y="52" width="414" height="131.5"/>
                                            <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="12"/>
                                            <color key="textColor" name="Grey4"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" ambiguous="YES" text="Made for 🇵🇭" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="h2p-9t-G7x">
                                            <rect key="frame" x="24" y="36" width="366" height="15"/>
                                            <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="12"/>
                                            <color key="textColor" name="Grey4"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" verticalHuggingPriority="1000" verticalCompressionResistancePriority="1000" image="imageMayaLogoSmall" translatesAutoresizingMaskIntoConstraints="NO" id="PQW-L1-gnd">
                                            <rect key="frame" x="165" y="0.0" width="84" height="24"/>
                                        </imageView>
                                    </subviews>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                    <constraints>
                                        <constraint firstAttribute="bottom" secondItem="Avl-LB-xo8" secondAttribute="bottom" id="3ea-nb-dQd"/>
                                        <constraint firstItem="PQW-L1-gnd" firstAttribute="top" secondItem="yn2-lG-khT" secondAttribute="top" id="4sH-dZ-a4L"/>
                                        <constraint firstItem="Avl-LB-xo8" firstAttribute="top" secondItem="h2p-9t-G7x" secondAttribute="bottom" constant="1" id="E5Y-FC-6SF"/>
                                        <constraint firstAttribute="trailing" secondItem="h2p-9t-G7x" secondAttribute="trailing" constant="24" id="Njf-4H-k5Q"/>
                                        <constraint firstItem="PQW-L1-gnd" firstAttribute="centerX" secondItem="yn2-lG-khT" secondAttribute="centerX" id="V34-UJ-Knr"/>
                                        <constraint firstItem="Avl-LB-xo8" firstAttribute="width" secondItem="yn2-lG-khT" secondAttribute="width" id="fWW-8r-EOi"/>
                                        <constraint firstItem="Avl-LB-xo8" firstAttribute="centerX" secondItem="yn2-lG-khT" secondAttribute="centerX" id="gHc-Ak-KKt"/>
                                        <constraint firstItem="h2p-9t-G7x" firstAttribute="top" secondItem="PQW-L1-gnd" secondAttribute="bottom" constant="12" id="kvS-yz-foU"/>
                                        <constraint firstItem="h2p-9t-G7x" firstAttribute="leading" secondItem="yn2-lG-khT" secondAttribute="leading" constant="24" id="ywa-Sd-O84"/>
                                    </constraints>
                                </view>
                            </subviews>
                        </stackView>
                    </subviews>
                    <viewLayoutGuide key="safeArea" id="BqL-pT-Wp1"/>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="GCx-ws-fXp" secondAttribute="trailing" id="HjG-4d-RDG"/>
                        <constraint firstItem="GCx-ws-fXp" firstAttribute="leading" secondItem="uJV-LC-nqZ" secondAttribute="leading" id="Kmf-Ka-yml"/>
                        <constraint firstAttribute="bottom" secondItem="GCx-ws-fXp" secondAttribute="bottom" constant="16" id="Qs8-3s-X4G"/>
                        <constraint firstItem="GCx-ws-fXp" firstAttribute="top" secondItem="uJV-LC-nqZ" secondAttribute="top" constant="28" id="XZe-ct-Fj6"/>
                    </constraints>
                </view>
                <tapGestureRecognizer id="oQP-0Y-l0x">
                    <connections>
                        <action selector="didTapUpdateNowButton:" destination="LZj-gg-Teo" id="hdK-Dq-pCO"/>
                        <outlet property="delegate" destination="bKD-ws-5v6" id="xTa-i7-6Aq"/>
                    </connections>
                </tapGestureRecognizer>
            </objects>
            <point key="canvasLocation" x="3892" y="2059"/>
        </scene>
        <!--Maya Change Min Mobile Number View Controller-->
        <scene sceneID="AQa-XQ-1Sf">
            <objects>
                <viewController storyboardIdentifier="MayaChangeMinMobileNumberViewController" id="HSY-PP-BkK" customClass="MayaChangeMinMobileNumberViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="41z-Ek-Rcj">
                        <rect key="frame" x="0.0" y="0.0" width="390" height="844"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Change mobile number" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="que-KU-ymQ">
                                <rect key="frame" x="24" y="63.000000000000007" width="342" height="29.**************6"/>
                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                <color key="textColor" name="ContentPrimaryBlack"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Your number will be used to identify your Maya account. We'll use this to send OTPs, service updates, and all the best deals." lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Jtt-rV-7Jy">
                                <rect key="frame" x="24" y="100.**************" width="342" height="50.***************"/>
                                <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                <color key="textColor" name="ContentGrey5"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="d2c-jX-ZRX" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="24" y="183.**************" width="342" height="76.***************"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Registered mobile number" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nXC-UI-VfG">
                                        <rect key="frame" x="16" y="16" width="310" height="14"/>
                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="12"/>
                                        <color key="textColor" name="ContentGrey5"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="+639123456789" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gAG-DL-KoA">
                                        <rect key="frame" x="48" y="37" width="278" height="22"/>
                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="18"/>
                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconChangeNumber" translatesAutoresizingMaskIntoConstraints="NO" id="ZuE-RC-Szb">
                                        <rect key="frame" x="16" y="36" width="24" height="24"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="24" id="0Xp-LU-vA9"/>
                                            <constraint firstAttribute="height" constant="24" id="6tq-v2-BDy"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="gAG-DL-KoA" firstAttribute="centerY" secondItem="ZuE-RC-Szb" secondAttribute="centerY" id="0Yj-YN-LOt"/>
                                    <constraint firstItem="gAG-DL-KoA" firstAttribute="trailing" secondItem="nXC-UI-VfG" secondAttribute="trailing" id="0f7-eC-gFF"/>
                                    <constraint firstAttribute="bottom" secondItem="ZuE-RC-Szb" secondAttribute="bottom" constant="16" id="2Sf-lk-PLO"/>
                                    <constraint firstItem="ZuE-RC-Szb" firstAttribute="leading" secondItem="nXC-UI-VfG" secondAttribute="leading" id="4Vk-sO-ngH"/>
                                    <constraint firstItem="gAG-DL-KoA" firstAttribute="leading" secondItem="ZuE-RC-Szb" secondAttribute="trailing" constant="8" id="p25-nL-bjG"/>
                                    <constraint firstItem="nXC-UI-VfG" firstAttribute="top" secondItem="d2c-jX-ZRX" secondAttribute="top" constant="16" id="uHT-f0-SaB"/>
                                    <constraint firstItem="nXC-UI-VfG" firstAttribute="leading" secondItem="d2c-jX-ZRX" secondAttribute="leading" constant="16" id="wJd-CY-N76"/>
                                    <constraint firstAttribute="trailing" secondItem="nXC-UI-VfG" secondAttribute="trailing" constant="16" id="wlL-pG-jB8"/>
                                    <constraint firstItem="ZuE-RC-Szb" firstAttribute="top" secondItem="nXC-UI-VfG" secondAttribute="bottom" constant="6" id="za3-r3-djm"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                        <real key="value" value="16"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                        <real key="value" value="1"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                        <color key="value" name="Grey3"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="B2p-id-MPq" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="24" y="275.**************" width="342" height="44"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="FxQ-Op-BLT"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="Change mobile number"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                        <integer key="value" value="1"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                        <integer key="value" value="0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="didTapChangeMin:" destination="HSY-PP-BkK" eventType="touchUpInside" id="i1s-mU-fin"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="m3L-0j-dLs" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="24" y="687.33333333333337" width="342" height="102.66666666666663"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Useful links:" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DWK-1Q-lui">
                                        <rect key="frame" x="16" y="16" width="310" height="19"/>
                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="I don’t have access to my mobile number" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2H7-BM-HhS">
                                        <rect key="frame" x="16" y="42.666666666666629" width="310" height="18.***************"/>
                                        <gestureRecognizers/>
                                        <fontDescription key="fontDescription" name="CerebriSansPro-Medium" family="Cerebri Sans Pro" pointSize="14"/>
                                        <color key="textColor" name="ContentPrimaryGreen"/>
                                        <nil key="highlightedColor"/>
                                        <connections>
                                            <outletCollection property="gestureRecognizers" destination="gN3-Tt-EoQ" appends="YES" id="Yfj-zK-TQO"/>
                                        </connections>
                                    </label>
                                    <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Guide on changing mobile number" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Hjs-Nh-8HT">
                                        <rect key="frame" x="16" y="68.333333333333258" width="310" height="18.***************"/>
                                        <gestureRecognizers/>
                                        <fontDescription key="fontDescription" name="CerebriSansPro-Medium" family="Cerebri Sans Pro" pointSize="14"/>
                                        <color key="textColor" name="ContentPrimaryGreen"/>
                                        <nil key="highlightedColor"/>
                                        <connections>
                                            <outletCollection property="gestureRecognizers" destination="KFi-kv-Bd7" appends="YES" id="COt-Xn-fEa"/>
                                        </connections>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="Hjs-Nh-8HT" firstAttribute="top" secondItem="2H7-BM-HhS" secondAttribute="bottom" constant="7.5" id="4Vi-EA-fil"/>
                                    <constraint firstItem="Hjs-Nh-8HT" firstAttribute="leading" secondItem="2H7-BM-HhS" secondAttribute="leading" id="C2S-Uo-odD"/>
                                    <constraint firstItem="DWK-1Q-lui" firstAttribute="top" secondItem="m3L-0j-dLs" secondAttribute="top" constant="16" id="O9f-Kc-emi"/>
                                    <constraint firstAttribute="trailing" secondItem="DWK-1Q-lui" secondAttribute="trailing" constant="16" id="W9K-5a-aAG"/>
                                    <constraint firstItem="DWK-1Q-lui" firstAttribute="leading" secondItem="m3L-0j-dLs" secondAttribute="leading" constant="16" id="bWX-Gv-dmn"/>
                                    <constraint firstItem="2H7-BM-HhS" firstAttribute="trailing" secondItem="DWK-1Q-lui" secondAttribute="trailing" id="ed2-Xk-t2N"/>
                                    <constraint firstAttribute="bottom" secondItem="Hjs-Nh-8HT" secondAttribute="bottom" constant="16" id="o02-7d-PTf"/>
                                    <constraint firstItem="2H7-BM-HhS" firstAttribute="top" secondItem="DWK-1Q-lui" secondAttribute="bottom" constant="7.5" id="uoI-Oh-INh"/>
                                    <constraint firstItem="Hjs-Nh-8HT" firstAttribute="trailing" secondItem="2H7-BM-HhS" secondAttribute="trailing" id="vO9-Vk-J8o"/>
                                    <constraint firstItem="2H7-BM-HhS" firstAttribute="leading" secondItem="DWK-1Q-lui" secondAttribute="leading" id="yj2-sk-s7E"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                        <real key="value" value="1"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                        <real key="value" value="16"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                        <color key="value" name="Grey3"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="iD4-x7-bGh"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="iD4-x7-bGh" firstAttribute="trailing" secondItem="m3L-0j-dLs" secondAttribute="trailing" constant="24" id="5iJ-kK-2Xq"/>
                            <constraint firstItem="iD4-x7-bGh" firstAttribute="trailing" secondItem="que-KU-ymQ" secondAttribute="trailing" constant="24" id="J4w-lL-vta"/>
                            <constraint firstItem="B2p-id-MPq" firstAttribute="trailing" secondItem="d2c-jX-ZRX" secondAttribute="trailing" id="Lj8-bQ-7FK"/>
                            <constraint firstItem="d2c-jX-ZRX" firstAttribute="top" secondItem="Jtt-rV-7Jy" secondAttribute="bottom" constant="32" id="NnZ-cT-36i"/>
                            <constraint firstItem="Jtt-rV-7Jy" firstAttribute="top" secondItem="que-KU-ymQ" secondAttribute="bottom" constant="8" id="U8W-ma-yiS"/>
                            <constraint firstItem="iD4-x7-bGh" firstAttribute="bottom" secondItem="m3L-0j-dLs" secondAttribute="bottom" constant="20" id="UpR-n9-GnE"/>
                            <constraint firstItem="d2c-jX-ZRX" firstAttribute="leading" secondItem="Jtt-rV-7Jy" secondAttribute="leading" id="VEU-oY-F8W"/>
                            <constraint firstItem="iD4-x7-bGh" firstAttribute="trailing" secondItem="Jtt-rV-7Jy" secondAttribute="trailing" constant="24" id="XNF-em-q3A"/>
                            <constraint firstItem="m3L-0j-dLs" firstAttribute="leading" secondItem="iD4-x7-bGh" secondAttribute="leading" constant="24" id="ZOd-w3-rvM"/>
                            <constraint firstItem="que-KU-ymQ" firstAttribute="top" secondItem="iD4-x7-bGh" secondAttribute="top" constant="16" id="c0v-Lp-yuS"/>
                            <constraint firstItem="que-KU-ymQ" firstAttribute="leading" secondItem="iD4-x7-bGh" secondAttribute="leading" constant="24" id="dSb-rF-p7u"/>
                            <constraint firstItem="d2c-jX-ZRX" firstAttribute="trailing" secondItem="Jtt-rV-7Jy" secondAttribute="trailing" id="onn-tv-uVa"/>
                            <constraint firstItem="Jtt-rV-7Jy" firstAttribute="leading" secondItem="iD4-x7-bGh" secondAttribute="leading" constant="24" id="p3A-gv-YxC"/>
                            <constraint firstItem="B2p-id-MPq" firstAttribute="top" secondItem="d2c-jX-ZRX" secondAttribute="bottom" constant="16" id="pfV-WI-C3H"/>
                            <constraint firstItem="B2p-id-MPq" firstAttribute="leading" secondItem="d2c-jX-ZRX" secondAttribute="leading" id="zut-T5-cTD"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="iDn-kJ-Ztf">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="0qN-8S-nCZ">
                            <connections>
                                <action selector="didTapBack:" destination="HSY-PP-BkK" id="LFo-W7-eGd"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="mobileNumberLabel" destination="gAG-DL-KoA" id="ujg-oc-Siu"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Jeq-Zj-86j" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
                <tapGestureRecognizer id="KFi-kv-Bd7">
                    <connections>
                        <action selector="didTapGuideToChangingMin:" destination="HSY-PP-BkK" id="hv8-OR-mbR"/>
                    </connections>
                </tapGestureRecognizer>
                <tapGestureRecognizer id="gN3-Tt-EoQ">
                    <connections>
                        <action selector="didTapNoAccessToMin:" destination="HSY-PP-BkK" id="LqY-ep-Mq2"/>
                    </connections>
                </tapGestureRecognizer>
            </objects>
            <point key="canvasLocation" x="5989.6000000000004" y="5483.2083958020994"/>
        </scene>
        <!--Maya Change Min Reminders View Controller-->
        <scene sceneID="fm1-6V-4sn">
            <objects>
                <viewController storyboardIdentifier="MayaChangeMinRemindersViewController" id="idz-g5-rxq" customClass="MayaChangeMinRemindersViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="hTt-Oq-4f0">
                        <rect key="frame" x="0.0" y="0.0" width="390" height="844"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rVR-4v-Zhl">
                                <rect key="frame" x="0.0" y="406" width="390" height="438"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconInfo" translatesAutoresizingMaskIntoConstraints="NO" id="1w5-eZ-6Qy">
                                        <rect key="frame" x="24" y="40" width="23" height="31"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="31" id="DNZ-4E-EY6"/>
                                            <constraint firstAttribute="width" constant="23" id="k2t-Ys-lAt"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Reminders" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="AfZ-0Q-CDQ">
                                        <rect key="frame" x="54.999999999999993" y="45.666666666666686" width="82.***************" height="19.***************"/>
                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="252" placeholderIntrinsicWidth="12.5" placeholderIntrinsicHeight="18.5" text="1." textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="55q-rY-y6h">
                                        <rect key="frame" x="28.000000000000004" y="79" width="12.**************8" height="18.***************"/>
                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                        <color key="textColor" name="ContentGrey6"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" placeholderIntrinsicWidth="302.5" placeholderIntrinsicHeight="91.5" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LLo-Zj-a4B">
                                        <rect key="frame" x="48.666666666666657" y="79" width="317.33333333333337" height="91.666666666666686"/>
                                        <string key="text">Should you wish to proceed, you need to have access to both your OLD and NEW registered mobile number. You will be receiving One-Time PINs (OTPs) from both numbers to verify the changes.</string>
                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                        <color key="textColor" name="ContentGrey6"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="252" text="2." textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Vue-Kk-HFO">
                                        <rect key="frame" x="27.999999999999996" y="171.66666666666663" width="12.**************2" height="18.**************3"/>
                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                        <color key="textColor" name="ContentGrey6"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" text="Your OLD number should be on standby to receive the OTP." lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jWv-eX-Vmj">
                                        <rect key="frame" x="48.**************3" y="171.66666666666663" width="317.66666666666663" height="36.666666666666657"/>
                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                        <color key="textColor" name="ContentGrey6"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="252" text="3." textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Tu3-4M-Dln">
                                        <rect key="frame" x="27.999999999999996" y="209.33333333333337" width="12.**************2" height="18.**************3"/>
                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                        <color key="textColor" name="ContentGrey6"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" text="Pending cashbacks and vouchers tagged under your OLD registered mobile number will be cleared out once you update." lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2qV-6a-6ty">
                                        <rect key="frame" x="48.**************3" y="209.33333333333337" width="317.66666666666663" height="55"/>
                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                        <color key="textColor" name="ContentGrey6"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" editable="NO" text="If you don’t have your old mobile number, tap here." translatesAutoresizingMaskIntoConstraints="NO" id="gSW-Dp-G43">
                                        <rect key="frame" x="28" y="291.66666666666663" width="338" height="34.***************"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <color key="textColor" name="ContentGrey6"/>
                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                        <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                        <dataDetectorType key="dataDetectorTypes" link="YES"/>
                                        <connections>
                                            <outlet property="delegate" destination="idz-g5-rxq" id="hH3-YZ-ayq"/>
                                        </connections>
                                    </textView>
                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="I15-p4-iPU">
                                        <rect key="frame" x="24" y="350" width="342" height="56"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="pip-qC-GNP" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="0.0" width="166" height="56"/>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="Cancel"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                        <integer key="value" value="1"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="didTapBack:" destination="idz-g5-rxq" eventType="touchUpInside" id="fk8-8M-O1Q"/>
                                                    <action selector="didTapCancel:" destination="idz-g5-rxq" eventType="touchUpInside" id="5vX-H6-mMi"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ph0-Ne-SZg" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="176" y="0.0" width="166" height="56"/>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="Continue"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="didTapContinue:" destination="idz-g5-rxq" eventType="touchUpInside" id="QH6-v8-Khn"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="WhG-Z8-0JV"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" name="BackgroundPrimaryWhite"/>
                                <constraints>
                                    <constraint firstItem="LLo-Zj-a4B" firstAttribute="top" secondItem="55q-rY-y6h" secondAttribute="top" id="08c-Cr-pbP"/>
                                    <constraint firstItem="Tu3-4M-Dln" firstAttribute="leading" secondItem="Vue-Kk-HFO" secondAttribute="leading" id="1Ff-nr-RFF"/>
                                    <constraint firstAttribute="trailing" secondItem="I15-p4-iPU" secondAttribute="trailing" constant="24" id="1Uz-v1-N8c"/>
                                    <constraint firstAttribute="trailing" secondItem="LLo-Zj-a4B" secondAttribute="trailing" constant="24" id="4Dh-8t-OW1"/>
                                    <constraint firstItem="gSW-Dp-G43" firstAttribute="trailing" secondItem="2qV-6a-6ty" secondAttribute="trailing" id="4zR-xZ-C7S"/>
                                    <constraint firstItem="Vue-Kk-HFO" firstAttribute="top" secondItem="LLo-Zj-a4B" secondAttribute="bottom" constant="1" id="9YO-i7-R0L"/>
                                    <constraint firstAttribute="bottom" secondItem="I15-p4-iPU" secondAttribute="bottom" constant="32" id="Ec3-lK-I4I"/>
                                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="AfZ-0Q-CDQ" secondAttribute="trailing" constant="20" symbolic="YES" id="HlW-fO-v42"/>
                                    <constraint firstItem="2qV-6a-6ty" firstAttribute="trailing" secondItem="jWv-eX-Vmj" secondAttribute="trailing" id="KYJ-0E-s4L"/>
                                    <constraint firstItem="2qV-6a-6ty" firstAttribute="top" secondItem="Tu3-4M-Dln" secondAttribute="top" id="O9u-px-3Xe"/>
                                    <constraint firstItem="AfZ-0Q-CDQ" firstAttribute="leading" secondItem="1w5-eZ-6Qy" secondAttribute="trailing" constant="8" symbolic="YES" id="Rme-ei-YFl"/>
                                    <constraint firstItem="1w5-eZ-6Qy" firstAttribute="top" secondItem="rVR-4v-Zhl" secondAttribute="top" constant="40" id="VEY-KM-9Wv"/>
                                    <constraint firstItem="AfZ-0Q-CDQ" firstAttribute="centerY" secondItem="1w5-eZ-6Qy" secondAttribute="centerY" id="Z0y-p4-svg"/>
                                    <constraint firstItem="I15-p4-iPU" firstAttribute="leading" secondItem="rVR-4v-Zhl" secondAttribute="leading" constant="24" id="b14-CI-cOF"/>
                                    <constraint firstItem="Tu3-4M-Dln" firstAttribute="top" secondItem="jWv-eX-Vmj" secondAttribute="bottom" constant="1" id="bNL-ID-Yfn"/>
                                    <constraint firstItem="gSW-Dp-G43" firstAttribute="top" secondItem="2qV-6a-6ty" secondAttribute="bottom" constant="27.5" id="cgN-Le-XJq"/>
                                    <constraint firstItem="gSW-Dp-G43" firstAttribute="leading" secondItem="Tu3-4M-Dln" secondAttribute="leading" id="dkT-hq-r1z"/>
                                    <constraint firstItem="1w5-eZ-6Qy" firstAttribute="leading" secondItem="rVR-4v-Zhl" secondAttribute="leading" constant="24" id="hTN-DG-9ft"/>
                                    <constraint firstItem="jWv-eX-Vmj" firstAttribute="top" secondItem="Vue-Kk-HFO" secondAttribute="top" id="khR-u9-BZg"/>
                                    <constraint firstItem="55q-rY-y6h" firstAttribute="top" secondItem="1w5-eZ-6Qy" secondAttribute="bottom" constant="8" symbolic="YES" id="oeK-CE-yZ5"/>
                                    <constraint firstItem="55q-rY-y6h" firstAttribute="leading" secondItem="1w5-eZ-6Qy" secondAttribute="leading" constant="4" id="pF7-Ld-aRX"/>
                                    <constraint firstItem="2qV-6a-6ty" firstAttribute="leading" secondItem="Tu3-4M-Dln" secondAttribute="trailing" constant="8" symbolic="YES" id="rWb-LX-NEm"/>
                                    <constraint firstItem="jWv-eX-Vmj" firstAttribute="trailing" secondItem="LLo-Zj-a4B" secondAttribute="trailing" id="sZy-yA-Dcc"/>
                                    <constraint firstItem="jWv-eX-Vmj" firstAttribute="leading" secondItem="Vue-Kk-HFO" secondAttribute="trailing" constant="8" symbolic="YES" id="thq-rn-1bY"/>
                                    <constraint firstItem="I15-p4-iPU" firstAttribute="top" secondItem="gSW-Dp-G43" secondAttribute="bottom" constant="24" id="vIA-gP-VcF"/>
                                    <constraint firstItem="Vue-Kk-HFO" firstAttribute="leading" secondItem="55q-rY-y6h" secondAttribute="leading" id="ybL-Pu-CZz"/>
                                    <constraint firstItem="LLo-Zj-a4B" firstAttribute="leading" secondItem="55q-rY-y6h" secondAttribute="trailing" constant="8" symbolic="YES" id="z5f-HI-lAv"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="YRN-p0-pw8"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="rVR-4v-Zhl" secondAttribute="bottom" id="D9X-eN-84G"/>
                            <constraint firstItem="rVR-4v-Zhl" firstAttribute="leading" secondItem="YRN-p0-pw8" secondAttribute="leading" id="FGj-ms-f21"/>
                            <constraint firstItem="rVR-4v-Zhl" firstAttribute="top" relation="greaterThanOrEqual" secondItem="YRN-p0-pw8" secondAttribute="top" id="LnI-sw-aVz"/>
                            <constraint firstItem="YRN-p0-pw8" firstAttribute="trailing" secondItem="rVR-4v-Zhl" secondAttribute="trailing" id="WTi-DI-n3v"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isDimmerViewTappable" value="NO"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="contentView" destination="rVR-4v-Zhl" id="tv5-Yh-fXz"/>
                        <outlet property="firstItemLabel" destination="LLo-Zj-a4B" id="LBE-Nb-sGC"/>
                        <outlet property="secondItemLabel" destination="jWv-eX-Vmj" id="zIR-XA-yiK"/>
                        <outlet property="tapHereTextView" destination="gSW-Dp-G43" id="8dD-Fu-7U7"/>
                        <outlet property="thirdItemLabel" destination="2qV-6a-6ty" id="aRb-9g-9Sq"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="PKH-2b-5V0" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="6629.6000000000004" y="5483.2083958020994"/>
        </scene>
        <!--Change mobile number-->
        <scene sceneID="AHT-sF-Yrz">
            <objects>
                <viewController storyboardIdentifier="MayaChangeMinInputNewMinViewController" id="fOY-Tr-INY" customClass="MayaChangeMinInputNewMinViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="FO3-GC-uNa">
                        <rect key="frame" x="0.0" y="0.0" width="390" height="753"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" bouncesZoom="NO" translatesAutoresizingMaskIntoConstraints="NO" id="90l-wN-8oN">
                                <rect key="frame" x="0.0" y="0.0" width="390" height="623"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2DN-DC-Orw">
                                        <rect key="frame" x="0.0" y="0.0" width="390" height="167"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="RIc-Ct-LRP">
                                                <rect key="frame" x="24" y="16" width="342" height="135"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" placeholderIntrinsicWidth="infinite" placeholderIntrinsicHeight="60" translatesAutoresizingMaskIntoConstraints="NO" id="5dw-Uz-ocf" customClass="MayaValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="342" height="60"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isAccessoryViewHiddenInitially" value="NO"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="YES"/>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="placeholderText" value="e.g. **********"/>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <outlet property="textFieldDelegate" destination="fOY-Tr-INY" id="Jds-1z-tWG"/>
                                                        </connections>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="8rd-sI-C5Y">
                                                        <rect key="frame" x="0.0" y="84" width="342" height="51"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" textAlignment="natural" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="uxb-DD-Ht6">
                                                                <rect key="frame" x="0.0" y="0.0" width="342" height="51"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <accessibility key="accessibilityConfiguration" identifier="pmios_account_limits_info_remaining_cash_in_label"/>
                                                                <string key="text">Please register using a new mobile number not linked to any existing Maya account. Once verified, this will be your Maya account number.</string>
                                                                <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                                <color key="textColor" name="ContentGrey5"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="RIc-Ct-LRP" secondAttribute="trailing" constant="24" id="FMM-PP-nPY"/>
                                            <constraint firstAttribute="bottom" secondItem="RIc-Ct-LRP" secondAttribute="bottom" constant="16" id="M6W-V3-n2E"/>
                                            <constraint firstItem="RIc-Ct-LRP" firstAttribute="top" secondItem="2DN-DC-Orw" secondAttribute="top" constant="16" id="Whs-it-OXB"/>
                                            <constraint firstItem="RIc-Ct-LRP" firstAttribute="leading" secondItem="2DN-DC-Orw" secondAttribute="leading" constant="24" id="kPR-od-mNK"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="2DN-DC-Orw" secondAttribute="bottom" id="2bJ-xM-Qjw"/>
                                    <constraint firstItem="2DN-DC-Orw" firstAttribute="top" secondItem="90l-wN-8oN" secondAttribute="top" id="6RF-53-QpK"/>
                                    <constraint firstItem="2DN-DC-Orw" firstAttribute="width" secondItem="90l-wN-8oN" secondAttribute="width" id="QZc-EK-oyQ"/>
                                    <constraint firstItem="2DN-DC-Orw" firstAttribute="leading" secondItem="90l-wN-8oN" secondAttribute="leading" id="cMJ-dU-A12"/>
                                    <constraint firstAttribute="trailing" secondItem="2DN-DC-Orw" secondAttribute="trailing" id="lHu-kv-ktW"/>
                                </constraints>
                            </scrollView>
                            <view contentMode="scaleToFill" placeholderIntrinsicWidth="infinite" placeholderIntrinsicHeight="96" translatesAutoresizingMaskIntoConstraints="NO" id="g5k-sP-hhF">
                                <rect key="frame" x="0.0" y="623" width="390" height="96"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="46H-57-AoX" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="24" y="24" width="342" height="56"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="c06-Tv-IEv"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Save"/>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="46H-57-AoX" firstAttribute="leading" secondItem="g5k-sP-hhF" secondAttribute="leading" constant="24" id="PXF-6b-Aow"/>
                                    <constraint firstItem="46H-57-AoX" firstAttribute="top" secondItem="g5k-sP-hhF" secondAttribute="top" constant="24" id="Sxz-Ip-s45"/>
                                    <constraint firstAttribute="bottom" secondItem="46H-57-AoX" secondAttribute="bottom" constant="16" id="XK3-S3-yl6"/>
                                    <constraint firstAttribute="trailing" secondItem="46H-57-AoX" secondAttribute="trailing" constant="24" id="s3t-EE-Z27"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Ayh-Pr-Dqa"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="90l-wN-8oN" firstAttribute="leading" secondItem="Ayh-Pr-Dqa" secondAttribute="leading" id="239-UH-J4A"/>
                            <constraint firstItem="90l-wN-8oN" firstAttribute="top" secondItem="Ayh-Pr-Dqa" secondAttribute="top" id="EXt-PZ-PEg"/>
                            <constraint firstItem="90l-wN-8oN" firstAttribute="bottom" secondItem="g5k-sP-hhF" secondAttribute="top" id="UHP-2e-DKl"/>
                            <constraint firstItem="Ayh-Pr-Dqa" firstAttribute="trailing" secondItem="g5k-sP-hhF" secondAttribute="trailing" id="jkT-G4-Dzx"/>
                            <constraint firstItem="Ayh-Pr-Dqa" firstAttribute="trailing" secondItem="90l-wN-8oN" secondAttribute="trailing" id="lFj-cw-GeB"/>
                            <constraint firstItem="g5k-sP-hhF" firstAttribute="leading" secondItem="Ayh-Pr-Dqa" secondAttribute="leading" id="vm3-R0-saD"/>
                            <constraint firstItem="Ayh-Pr-Dqa" firstAttribute="bottom" secondItem="g5k-sP-hhF" secondAttribute="bottom" id="zZ2-4A-lO3"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="Change mobile number" id="zBS-uu-cit">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="jFc-mb-7Zy">
                            <connections>
                                <action selector="didTapBack:" destination="fOY-Tr-INY" id="8KE-Ll-bAy"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" translucent="NO" prompted="NO"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isMayaForm" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="phoneNumberFieldView" destination="5dw-Uz-ocf" id="Yx5-oz-PO7"/>
                        <outlet property="saveButton" destination="46H-57-AoX" id="eOl-CM-h3K"/>
                        <outlet property="scrollView" destination="90l-wN-8oN" id="QQx-1y-OBE"/>
                        <outlet property="stickyViewBottomConstraint" destination="zZ2-4A-lO3" id="oCj-dx-Dex"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="2ig-02-sHn" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="7303" y="5483"/>
        </scene>
    </scenes>
    <resources>
        <image name="iconBack" width="22" height="17"/>
        <image name="iconChangeNumber" width="24" height="24"/>
        <image name="iconChevronRightGray" width="24" height="24"/>
        <image name="iconInfo" width="17" height="17"/>
        <image name="iconMayaProfile" width="48" height="48"/>
        <image name="iconQR" width="24" height="24"/>
        <image name="imageMayaLogoSmall" width="84" height="24"/>
        <image name="imageShowName" width="305" height="288"/>
        <image name="imageUpgradeMyAccount" width="327" height="148"/>
        <image name="imageWalletBalance" width="200.**************" height="200"/>
        <image name="otherPublicName" width="24" height="24"/>
        <namedColor name="BackgroundGrey1">
            <color red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="BackgroundPrimaryWhite">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentGrey5">
            <color red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentGrey6">
            <color red="0.333**************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentPrimaryBlack">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentPrimaryGreen">
            <color red="0.0" green="0.65098039215686276" blue="0.31764705882352939" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey1">
            <color red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey3">
            <color red="0.92549019607843142" green="0.92941176470588238" blue="0.93725490196078431" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey4">
            <color red="0.69411764705882351" green="0.70196078431372544" blue="0.70980392156862748" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey5">
            <color red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey6">
            <color red="0.333**************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryBlack">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryWhite">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
