<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="CerebriSansPro-Book.otf">
            <string>CerebriSansPro-Book</string>
        </array>
        <array key="CerebriSansPro-Medium.otf">
            <string>CerebriSansPro-Medium</string>
        </array>
        <array key="CerebriSansPro-Regular.otf">
            <string>CerebriSansPro-Regular</string>
        </array>
        <array key="Jeko-Bold.otf">
            <string>Jeko-Bold</string>
        </array>
        <array key="Jeko-Regular.otf">
            <string>Jeko-Regular</string>
        </array>
        <array key="Jeko-SemiBold.otf">
            <string>Jeko-SemiBold</string>
        </array>
        <array key="WorkSans-Medium.ttf">
            <string>WorkSans-Medium</string>
        </array>
        <array key="WorkSans-SemiBold.ttf">
            <string>WorkSans-SemiBold</string>
        </array>
    </customFonts>
    <scenes>
        <!--Error Alert View Controller-->
        <scene sceneID="dMu-XT-zVk">
            <objects>
                <viewController storyboardIdentifier="ErrorAlertViewController" id="h70-Qf-TCm" customClass="ErrorAlertViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="CPd-vV-vz8">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" verticalCompressionResistancePriority="250" placeholderIntrinsicWidth="infinite" placeholderIntrinsicHeight="20" translatesAutoresizingMaskIntoConstraints="NO" id="aHw-JB-AHb" userLabel="Spacer">
                                <rect key="frame" x="0.0" y="20" width="375" height="163"/>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="20" id="FHN-OC-cfp"/>
                                </constraints>
                            </view>
                            <view clipsSubviews="YES" contentMode="scaleToFill" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="tMP-l0-jJ1" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="10" y="183" width="355" height="157.5"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="PGj-z3-qyu">
                                        <rect key="frame" x="0.0" y="0.0" width="355" height="68"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="j6n-SO-AaM">
                                                <rect key="frame" x="0.0" y="0.0" width="355" height="68"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_error_modal_title_label"/>
                                                <string key="text">Send Money to Smart Money
Unavailable</string>
                                                <fontDescription key="fontDescription" type="boldSystem" pointSize="16"/>
                                                <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" red="0.83529412749999998" green="0.35686275360000003" blue="0.41176471110000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <constraints>
                                            <constraint firstItem="j6n-SO-AaM" firstAttribute="leading" secondItem="PGj-z3-qyu" secondAttribute="leading" id="1Xl-Q0-Fta"/>
                                            <constraint firstItem="j6n-SO-AaM" firstAttribute="top" secondItem="PGj-z3-qyu" secondAttribute="top" id="Okp-ax-BIh"/>
                                            <constraint firstAttribute="bottom" secondItem="j6n-SO-AaM" secondAttribute="bottom" id="SD2-Tf-6eg"/>
                                            <constraint firstAttribute="trailing" secondItem="j6n-SO-AaM" secondAttribute="trailing" id="Y2c-zE-1Am"/>
                                            <constraint firstAttribute="height" constant="68" id="nJX-1c-UaW"/>
                                        </constraints>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="SA2-yp-HbX">
                                        <rect key="frame" x="315" y="0.0" width="40" height="40"/>
                                        <accessibility key="accessibilityConfiguration" identifier="pmios_error_modal_close_button"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="O6O-Sc-3JA"/>
                                            <constraint firstAttribute="height" constant="40" id="vMo-nI-ZRU"/>
                                        </constraints>
                                        <inset key="contentEdgeInsets" minX="10" minY="10" maxX="10" maxY="10"/>
                                        <state key="normal" image="iconCloseWhite"/>
                                        <connections>
                                            <action selector="backToPreviousViewController:" destination="h70-Qf-TCm" eventType="touchUpInside" id="TNZ-L4-QOt"/>
                                        </connections>
                                    </button>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WLg-rS-vnA">
                                        <rect key="frame" x="16" y="84" width="323" height="57.5"/>
                                        <subviews>
                                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" horizontalCompressionResistancePriority="250" showsHorizontalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ed6-yZ-5P9">
                                                <rect key="frame" x="0.0" y="0.0" width="323" height="57.5"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="Xbo-by-51W" userLabel="Content View">
                                                        <rect key="frame" x="0.0" y="0.0" width="323" height="57.5"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="VCp-6Z-0Hk">
                                                                <rect key="frame" x="0.0" y="0.0" width="323" height="57.5"/>
                                                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                                                <accessibility key="accessibilityConfiguration" identifier="pmios_error_modal_message_label"/>
                                                                <string key="text">Send Money failed. We are currently
experiencing some connectivity problems.
Please try again later.</string>
                                                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                                <color key="textColor" red="0.2666666667" green="0.31764705879999999" blue="0.3411764706" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                                        <constraints>
                                                            <constraint firstAttribute="bottom" secondItem="VCp-6Z-0Hk" secondAttribute="bottom" id="GLy-Dk-NMe"/>
                                                            <constraint firstItem="VCp-6Z-0Hk" firstAttribute="top" secondItem="Xbo-by-51W" secondAttribute="top" id="O2p-r8-QM4"/>
                                                            <constraint firstItem="VCp-6Z-0Hk" firstAttribute="leading" secondItem="Xbo-by-51W" secondAttribute="leading" id="OxN-a1-Zx0"/>
                                                            <constraint firstAttribute="trailing" secondItem="VCp-6Z-0Hk" secondAttribute="trailing" id="PCk-k4-mKM"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="bottom" secondItem="Xbo-by-51W" secondAttribute="bottom" id="0Ml-El-wsi"/>
                                                    <constraint firstItem="Xbo-by-51W" firstAttribute="top" secondItem="Ed6-yZ-5P9" secondAttribute="top" id="SxP-mi-k8n"/>
                                                    <constraint firstItem="Xbo-by-51W" firstAttribute="leading" secondItem="Ed6-yZ-5P9" secondAttribute="leading" id="fVQ-tI-jKO"/>
                                                    <constraint firstAttribute="trailing" secondItem="Xbo-by-51W" secondAttribute="trailing" id="lIl-pY-3VB"/>
                                                </constraints>
                                            </scrollView>
                                        </subviews>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <constraints>
                                            <constraint firstItem="Ed6-yZ-5P9" firstAttribute="leading" secondItem="WLg-rS-vnA" secondAttribute="leading" id="6al-Ki-TkZ"/>
                                            <constraint firstAttribute="bottom" secondItem="Ed6-yZ-5P9" secondAttribute="bottom" id="KR9-Mn-Kvp"/>
                                            <constraint firstItem="Xbo-by-51W" firstAttribute="height" relation="greaterThanOrEqual" secondItem="WLg-rS-vnA" secondAttribute="height" id="adT-Jl-xTG"/>
                                            <constraint firstItem="Xbo-by-51W" firstAttribute="height" secondItem="WLg-rS-vnA" secondAttribute="height" priority="749" id="irN-n0-PQR"/>
                                            <constraint firstItem="Xbo-by-51W" firstAttribute="width" secondItem="WLg-rS-vnA" secondAttribute="width" id="mJv-wG-jma"/>
                                            <constraint firstItem="Ed6-yZ-5P9" firstAttribute="top" secondItem="WLg-rS-vnA" secondAttribute="top" id="tdy-9j-csc"/>
                                            <constraint firstAttribute="trailing" secondItem="Ed6-yZ-5P9" secondAttribute="trailing" id="xoO-u0-uOb"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pBa-Aq-KWe" customClass="TTTAttributedLabel">
                                        <rect key="frame" x="158" y="157.5" width="39.5" height="19.5"/>
                                        <accessibility key="accessibilityConfiguration" identifier="pmios_error_modal_action_label"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                        <color key="textColor" red="0.26666666666666666" green="0.31764705882352939" blue="0.3411764705882353" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="PGj-z3-qyu" firstAttribute="leading" secondItem="tMP-l0-jJ1" secondAttribute="leading" id="B58-lH-UPU"/>
                                    <constraint firstItem="SA2-yp-HbX" firstAttribute="top" secondItem="PGj-z3-qyu" secondAttribute="top" id="BeS-QZ-Pn8"/>
                                    <constraint firstAttribute="bottom" secondItem="pBa-Aq-KWe" secondAttribute="bottom" priority="250" constant="16" id="Fiy-w5-bAK"/>
                                    <constraint firstItem="WLg-rS-vnA" firstAttribute="top" secondItem="PGj-z3-qyu" secondAttribute="bottom" constant="16" id="IbL-q8-6lB"/>
                                    <constraint firstAttribute="bottom" secondItem="WLg-rS-vnA" secondAttribute="bottom" priority="750" constant="16" id="PEm-bw-Ev7"/>
                                    <constraint firstAttribute="trailing" secondItem="WLg-rS-vnA" secondAttribute="trailing" constant="16" id="RdV-JW-SQW"/>
                                    <constraint firstItem="pBa-Aq-KWe" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="tMP-l0-jJ1" secondAttribute="leading" constant="16" id="Y7J-FP-Oog"/>
                                    <constraint firstItem="WLg-rS-vnA" firstAttribute="leading" secondItem="tMP-l0-jJ1" secondAttribute="leading" constant="16" id="auU-Yb-9rH"/>
                                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="pBa-Aq-KWe" secondAttribute="trailing" constant="16" id="h7d-MZ-nYq"/>
                                    <constraint firstAttribute="trailing" secondItem="PGj-z3-qyu" secondAttribute="trailing" id="hDt-R5-Cgw"/>
                                    <constraint firstItem="PGj-z3-qyu" firstAttribute="top" secondItem="tMP-l0-jJ1" secondAttribute="top" id="hg1-Sp-xng"/>
                                    <constraint firstItem="SA2-yp-HbX" firstAttribute="trailing" secondItem="PGj-z3-qyu" secondAttribute="trailing" id="hg9-fS-cVy"/>
                                    <constraint firstItem="pBa-Aq-KWe" firstAttribute="top" secondItem="WLg-rS-vnA" secondAttribute="bottom" constant="16" id="kLV-t5-PgY"/>
                                    <constraint firstItem="pBa-Aq-KWe" firstAttribute="centerX" secondItem="tMP-l0-jJ1" secondAttribute="centerX" id="s66-bO-wfC"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                        <real key="value" value="1"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                        <real key="value" value="2"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                        <color key="value" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view contentMode="scaleToFill" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" verticalCompressionResistancePriority="249" translatesAutoresizingMaskIntoConstraints="NO" id="MGb-3j-jMO" userLabel="Spacer">
                                <rect key="frame" x="0.0" y="340.5" width="375" height="326.5"/>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="R83-TV-CH7"/>
                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.5" colorSpace="calibratedRGB"/>
                        <gestureRecognizers/>
                        <constraints>
                            <constraint firstItem="MGb-3j-jMO" firstAttribute="leading" secondItem="R83-TV-CH7" secondAttribute="leading" id="4uh-Fk-2GV"/>
                            <constraint firstItem="R83-TV-CH7" firstAttribute="trailing" secondItem="tMP-l0-jJ1" secondAttribute="trailing" constant="10" id="6Y7-b7-199"/>
                            <constraint firstItem="R83-TV-CH7" firstAttribute="trailing" secondItem="MGb-3j-jMO" secondAttribute="trailing" id="8eD-ZS-kuz"/>
                            <constraint firstItem="aHw-JB-AHb" firstAttribute="top" secondItem="R83-TV-CH7" secondAttribute="top" id="Re3-If-QOe"/>
                            <constraint firstItem="MGb-3j-jMO" firstAttribute="top" secondItem="tMP-l0-jJ1" secondAttribute="bottom" id="SEP-fx-71g"/>
                            <constraint firstItem="MGb-3j-jMO" firstAttribute="height" secondItem="aHw-JB-AHb" secondAttribute="height" multiplier="2" id="TDO-Dh-DAQ"/>
                            <constraint firstItem="tMP-l0-jJ1" firstAttribute="leading" secondItem="R83-TV-CH7" secondAttribute="leading" constant="10" id="cuQ-bf-84a"/>
                            <constraint firstItem="aHw-JB-AHb" firstAttribute="leading" secondItem="R83-TV-CH7" secondAttribute="leading" id="gi6-3H-CeX"/>
                            <constraint firstItem="R83-TV-CH7" firstAttribute="trailing" secondItem="aHw-JB-AHb" secondAttribute="trailing" id="iJV-dL-Wtu"/>
                            <constraint firstItem="R83-TV-CH7" firstAttribute="bottom" secondItem="MGb-3j-jMO" secondAttribute="bottom" id="lkT-2U-vF1"/>
                            <constraint firstItem="tMP-l0-jJ1" firstAttribute="top" secondItem="aHw-JB-AHb" secondAttribute="bottom" id="yrM-wM-CBo"/>
                        </constraints>
                        <connections>
                            <outletCollection property="gestureRecognizers" destination="8h3-7q-H8Y" appends="YES" id="Jjv-vj-yq2"/>
                        </connections>
                    </view>
                    <connections>
                        <outlet property="actionAttributedLabel" destination="pBa-Aq-KWe" id="C86-tD-8AZ"/>
                        <outlet property="closeButton" destination="SA2-yp-HbX" id="ox1-Ju-rEA"/>
                        <outlet property="detailsLabel" destination="VCp-6Z-0Hk" id="Vln-Op-Uk2"/>
                        <outlet property="hideActionButtonBottomConstraint" destination="PEm-bw-Ev7" id="wnW-Wa-MkF"/>
                        <outlet property="showActionButtonBottomConstraint" destination="Fiy-w5-bAK" id="6ES-FM-JRV"/>
                        <outlet property="titleLabel" destination="j6n-SO-AaM" id="dtn-gh-fq9"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="IMD-t1-gv7" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <tapGestureRecognizer cancelsTouchesInView="NO" id="8h3-7q-H8Y"/>
            </objects>
            <point key="canvasLocation" x="-1170" y="433"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="TKR-OC-ObI">
            <objects>
                <navigationController storyboardIdentifier="ErrorFullViewNavigationController" modalTransitionStyle="crossDissolve" id="q49-nl-B2s" sceneMemberID="viewController">
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iat-ZU-xX1">
                        <rect key="frame" x="0.0" y="20" width="375" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="hhl-C6-sAl" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1170" y="1124"/>
        </scene>
        <!--Camera Permission View Controller-->
        <scene sceneID="6Wx-DZ-rDi">
            <objects>
                <viewController storyboardIdentifier="CameraPermissionViewController" id="a0x-vM-ZlR" customClass="CameraPermissionViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="unT-bK-wuR">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="aIi-l3-MTE">
                                <rect key="frame" x="16" y="33" width="40" height="33"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_camera_permission_back_button"/>
                                <constraints>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="40" id="ddG-mS-yOP"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <state key="normal" title="Back">
                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                                </state>
                                <connections>
                                    <action selector="didTapBack:" destination="a0x-vM-ZlR" eventType="touchUpInside" id="QYY-Za-jaN"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="For scanning QR Code and taking photo of your ID." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IQS-xb-Vdj">
                                <rect key="frame" x="16" y="314.5" width="343" height="38.5"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_camera_permission_message_label"/>
                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Allow PayMaya to Access the Camera" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dOg-33-jnd">
                                <rect key="frame" x="16" y="280.5" width="341" height="24"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_camera_permission_title_label"/>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="20"/>
                                <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="93r-R3-5il">
                                <rect key="frame" x="120.5" y="383" width="134" height="32"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_camera_permission_enable_button"/>
                                <constraints>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="134" id="FWi-aw-8Oi"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                <state key="normal" title="Enable Camera"/>
                                <connections>
                                    <action selector="didTapEnableCamera:" destination="a0x-vM-ZlR" eventType="touchUpInside" id="paU-zu-A3Q"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="aVC-Z5-Eku"/>
                        <color key="backgroundColor" red="0.08235294118" green="0.08235294118" blue="0.08235294118" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="leadingMargin" secondItem="IQS-xb-Vdj" secondAttribute="leading" id="0x9-Af-1cx"/>
                            <constraint firstAttribute="leadingMargin" secondItem="dOg-33-jnd" secondAttribute="leading" id="1bZ-6s-rQX"/>
                            <constraint firstItem="93r-R3-5il" firstAttribute="centerX" secondItem="aVC-Z5-Eku" secondAttribute="centerX" id="NbZ-dq-kh1"/>
                            <constraint firstItem="IQS-xb-Vdj" firstAttribute="top" secondItem="dOg-33-jnd" secondAttribute="bottom" constant="10" id="T6d-a0-4j3"/>
                            <constraint firstAttribute="trailingMargin" secondItem="IQS-xb-Vdj" secondAttribute="trailing" id="Tfc-fw-RjI"/>
                            <constraint firstItem="aVC-Z5-Eku" firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="aIi-l3-MTE" secondAttribute="trailingMargin" id="WpW-dA-oxj"/>
                            <constraint firstItem="93r-R3-5il" firstAttribute="top" secondItem="IQS-xb-Vdj" secondAttribute="bottom" constant="30" id="cdM-ZC-cKs"/>
                            <constraint firstItem="aIi-l3-MTE" firstAttribute="top" secondItem="aVC-Z5-Eku" secondAttribute="top" constant="13" id="grZ-3J-GbK"/>
                            <constraint firstAttribute="trailingMargin" secondItem="dOg-33-jnd" secondAttribute="trailing" constant="2" id="kqz-jd-0sq"/>
                            <constraint firstItem="IQS-xb-Vdj" firstAttribute="centerY" secondItem="unT-bK-wuR" secondAttribute="centerY" id="vFM-GE-Nnl"/>
                            <constraint firstAttribute="leadingMargin" secondItem="aIi-l3-MTE" secondAttribute="leading" id="yEY-2m-Vib"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="permissionDescriptionLabel" destination="IQS-xb-Vdj" id="PVG-JW-5VG"/>
                        <outlet property="permissionTitleLabel" destination="dOg-33-jnd" id="xCV-u7-gi5"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="cY3-ZD-wXe" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1037" y="1807"/>
        </scene>
        <!--Maya Camera Permission View Controller-->
        <scene sceneID="V5v-Wa-zr4">
            <objects>
                <viewController storyboardIdentifier="MayaCameraPermissionViewController" id="8UX-Ci-Woe" customClass="MayaCameraPermissionViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="6fu-5a-BZQ">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="FEL-JD-SOn">
                                <rect key="frame" x="24" y="44" width="24" height="24"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_qr_scanner_back_button"/>
                                <state key="normal" image="iconBackWhite">
                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="didTapBack:" destination="8UX-Ci-Woe" eventType="touchUpInside" id="mzh-mE-Gb7"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Take a photo" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Md3-lc-NcG">
                                <rect key="frame" x="136.5" y="46.5" width="102.5" height="19"/>
                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                <color key="textColor" name="ContentPrimaryWhite"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Oz0-lW-kj2">
                                <rect key="frame" x="0.0" y="219" width="375" height="249.5"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Allow Maya to access the following:" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="EZA-ee-Z64">
                                        <rect key="frame" x="56.5" y="0.0" width="262.5" height="58.5"/>
                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                        <color key="textColor" name="ContentPrimaryWhite"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="DQT-W3-pSq">
                                        <rect key="frame" x="132.5" y="102.5" width="110.5" height="32"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconOtherCamera" translatesAutoresizingMaskIntoConstraints="NO" id="ukK-UG-iCo">
                                                <rect key="frame" x="0.0" y="0.0" width="32" height="32"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="32" id="CX1-V1-eP4"/>
                                                    <constraint firstAttribute="height" constant="32" id="O0j-KV-aGN"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Camera" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Jm1-dz-RbS">
                                                <rect key="frame" x="40" y="5" width="70.5" height="22"/>
                                                <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="18"/>
                                                <color key="textColor" name="ContentPrimaryWhite"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="Jm1-dz-RbS" secondAttribute="trailing" id="Ozc-WE-pTq"/>
                                            <constraint firstItem="ukK-UG-iCo" firstAttribute="leading" secondItem="DQT-W3-pSq" secondAttribute="leading" id="cGJ-hj-cn7"/>
                                            <constraint firstAttribute="bottom" secondItem="ukK-UG-iCo" secondAttribute="bottom" id="hQM-i6-BUn"/>
                                            <constraint firstItem="ukK-UG-iCo" firstAttribute="top" secondItem="DQT-W3-pSq" secondAttribute="top" id="ie8-YQ-w8c"/>
                                            <constraint firstItem="Jm1-dz-RbS" firstAttribute="centerY" secondItem="DQT-W3-pSq" secondAttribute="centerY" id="kXw-QN-BVT"/>
                                            <constraint firstItem="Jm1-dz-RbS" firstAttribute="leading" secondItem="ukK-UG-iCo" secondAttribute="trailing" constant="8" id="zoh-g2-5jz"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Scan QR codes and take photos or videos required to set up your account" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5oX-mn-fLF">
                                        <rect key="frame" x="37.5" y="146.5" width="300" height="34"/>
                                        <fontDescription key="fontDescription" name="Jeko-Regular" family="Jeko" pointSize="14"/>
                                        <color key="textColor" name="ContentGrey4"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9bX-Rq-ELd">
                                        <rect key="frame" x="116" y="220.5" width="143" height="29"/>
                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="14"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Open phone settings">
                                            <color key="titleColor" name="ContentPrimaryGreen"/>
                                        </state>
                                        <connections>
                                            <action selector="didTapEnableCamera:" destination="8UX-Ci-Woe" eventType="touchUpInside" id="yve-J2-kne"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="9bX-Rq-ELd" firstAttribute="centerX" secondItem="Oz0-lW-kj2" secondAttribute="centerX" id="2ju-Kz-HoB"/>
                                    <constraint firstItem="5oX-mn-fLF" firstAttribute="top" secondItem="DQT-W3-pSq" secondAttribute="bottom" constant="12" id="6iE-Cd-tya"/>
                                    <constraint firstItem="EZA-ee-Z64" firstAttribute="centerX" secondItem="Oz0-lW-kj2" secondAttribute="centerX" id="6yW-4D-NUD"/>
                                    <constraint firstItem="DQT-W3-pSq" firstAttribute="centerX" secondItem="Oz0-lW-kj2" secondAttribute="centerX" id="IBP-Wc-I0c"/>
                                    <constraint firstItem="EZA-ee-Z64" firstAttribute="width" secondItem="Oz0-lW-kj2" secondAttribute="width" multiplier="0.7" id="M4Y-Sn-uTz"/>
                                    <constraint firstItem="EZA-ee-Z64" firstAttribute="top" secondItem="Oz0-lW-kj2" secondAttribute="top" id="O61-JH-AvF"/>
                                    <constraint firstItem="5oX-mn-fLF" firstAttribute="centerX" secondItem="Oz0-lW-kj2" secondAttribute="centerX" id="Vec-yT-UOj"/>
                                    <constraint firstItem="5oX-mn-fLF" firstAttribute="width" secondItem="Oz0-lW-kj2" secondAttribute="width" multiplier="0.8" id="Vm0-dw-OBW"/>
                                    <constraint firstItem="9bX-Rq-ELd" firstAttribute="top" secondItem="5oX-mn-fLF" secondAttribute="bottom" constant="40" id="eXQ-xZ-6u5"/>
                                    <constraint firstItem="DQT-W3-pSq" firstAttribute="top" secondItem="EZA-ee-Z64" secondAttribute="bottom" constant="44" id="hzq-XM-JDt"/>
                                    <constraint firstAttribute="bottom" secondItem="9bX-Rq-ELd" secondAttribute="bottom" id="qCG-H3-zAs"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Cxi-h8-kg4"/>
                        <color key="backgroundColor" name="BackgroundGrey11"/>
                        <constraints>
                            <constraint firstItem="Oz0-lW-kj2" firstAttribute="centerY" secondItem="Cxi-h8-kg4" secondAttribute="centerY" id="7o4-2u-PjY"/>
                            <constraint firstItem="Oz0-lW-kj2" firstAttribute="centerX" secondItem="Cxi-h8-kg4" secondAttribute="centerX" id="C0O-oS-w7R"/>
                            <constraint firstItem="FEL-JD-SOn" firstAttribute="leading" secondItem="Cxi-h8-kg4" secondAttribute="leading" constant="24" id="MJr-Sf-uBq"/>
                            <constraint firstItem="Md3-lc-NcG" firstAttribute="centerY" secondItem="FEL-JD-SOn" secondAttribute="centerY" id="Ugq-M3-QEg"/>
                            <constraint firstItem="Md3-lc-NcG" firstAttribute="centerX" secondItem="Cxi-h8-kg4" secondAttribute="centerX" id="cYm-u5-0DW"/>
                            <constraint firstItem="FEL-JD-SOn" firstAttribute="top" secondItem="Cxi-h8-kg4" secondAttribute="top" constant="24" id="d60-BD-nSz"/>
                            <constraint firstItem="Oz0-lW-kj2" firstAttribute="width" secondItem="6fu-5a-BZQ" secondAttribute="width" id="se2-g0-PT4"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="headerLabel" destination="EZA-ee-Z64" id="qta-XM-2XD"/>
                        <outlet property="messageLabel" destination="5oX-mn-fLF" id="0xB-ro-pKY"/>
                        <outlet property="titleLabel" destination="Md3-lc-NcG" id="lsr-63-bIM"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ZdG-wk-u2F" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1739" y="1807"/>
        </scene>
        <!--Maya Password Prompt Bottom Sheet View Controller-->
        <scene sceneID="IHo-00-GJA">
            <objects>
                <viewController storyboardIdentifier="MayaPasswordPromptBottomSheetViewController" id="S0p-ll-ecZ" customClass="MayaPasswordPromptBottomSheetViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="VdL-ed-RB0">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="GDJ-cA-p6O">
                                <rect key="frame" x="0.0" y="340.5" width="375" height="326.5"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Enter your password" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QVg-Za-BTx">
                                        <rect key="frame" x="67" y="56" width="241" height="29.5"/>
                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Please enter your password to confirm that you want to change your registered mobile number." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0m3-50-xMu">
                                        <rect key="frame" x="24" y="93.5" width="327" height="37"/>
                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                        <color key="textColor" name="ContentGrey6"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pCp-VB-9Aq" customClass="MayaValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="24" y="162.5" width="327" height="60"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="height" priority="999" constant="60" id="hc5-O6-L3i"/>
                                        </constraints>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="hintText" value="Password"/>
                                            <userDefinedRuntimeAttribute type="string" keyPath="placeholderText" value="Enter password"/>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="DFn-4g-yXZ" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="24" y="246.5" width="158.5" height="56"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="2Iq-lC-YhS"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Cancel"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                <integer key="value" value="1"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="didTapCancel:" destination="S0p-ll-ecZ" eventType="touchUpInside" id="BM8-ZA-cHe"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="unI-zi-wlF" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="192.5" y="246.5" width="158.5" height="56"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Confirm"/>
                                        <connections>
                                            <action selector="didTapConfirm:" destination="S0p-ll-ecZ" eventType="touchUpInside" id="nqa-g1-KiJ"/>
                                        </connections>
                                    </button>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rJy-5S-L32" userLabel="Spacer View">
                                        <rect key="frame" x="0.0" y="326.5" width="375" height="0.0"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" id="4gQ-tV-8xH"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <gestureRecognizers/>
                                <constraints>
                                    <constraint firstItem="unI-zi-wlF" firstAttribute="width" secondItem="DFn-4g-yXZ" secondAttribute="width" id="1sR-lR-uRk"/>
                                    <constraint firstAttribute="trailing" secondItem="pCp-VB-9Aq" secondAttribute="trailing" constant="24" id="5ON-Pr-e8Z"/>
                                    <constraint firstAttribute="trailing" secondItem="unI-zi-wlF" secondAttribute="trailing" constant="24" id="6a2-Fs-dHR"/>
                                    <constraint firstItem="pCp-VB-9Aq" firstAttribute="top" secondItem="0m3-50-xMu" secondAttribute="bottom" constant="32" id="9Vg-Rs-ti5"/>
                                    <constraint firstItem="pCp-VB-9Aq" firstAttribute="leading" secondItem="GDJ-cA-p6O" secondAttribute="leading" constant="24" id="Bhe-Gu-73a"/>
                                    <constraint firstItem="rJy-5S-L32" firstAttribute="top" secondItem="DFn-4g-yXZ" secondAttribute="bottom" constant="24" id="Fp8-jZ-sbm"/>
                                    <constraint firstItem="unI-zi-wlF" firstAttribute="height" secondItem="DFn-4g-yXZ" secondAttribute="height" id="GPA-Fq-VLR"/>
                                    <constraint firstItem="DFn-4g-yXZ" firstAttribute="leading" secondItem="GDJ-cA-p6O" secondAttribute="leading" constant="24" id="HW8-Zy-7b1"/>
                                    <constraint firstItem="DFn-4g-yXZ" firstAttribute="top" secondItem="pCp-VB-9Aq" secondAttribute="bottom" constant="24" id="JgN-kF-zH9"/>
                                    <constraint firstItem="QVg-Za-BTx" firstAttribute="centerX" secondItem="GDJ-cA-p6O" secondAttribute="centerX" id="JoK-az-BfD"/>
                                    <constraint firstItem="0m3-50-xMu" firstAttribute="leading" secondItem="GDJ-cA-p6O" secondAttribute="leading" constant="24" id="Jzi-OD-455"/>
                                    <constraint firstItem="QVg-Za-BTx" firstAttribute="top" secondItem="GDJ-cA-p6O" secondAttribute="top" constant="56" id="RTM-ZA-BCw"/>
                                    <constraint firstItem="unI-zi-wlF" firstAttribute="leading" secondItem="DFn-4g-yXZ" secondAttribute="trailing" constant="10" id="VQB-P3-p0V"/>
                                    <constraint firstItem="unI-zi-wlF" firstAttribute="top" secondItem="DFn-4g-yXZ" secondAttribute="top" id="X2s-BP-Ebw"/>
                                    <constraint firstAttribute="bottom" secondItem="rJy-5S-L32" secondAttribute="bottom" id="ZPK-vA-LOD"/>
                                    <constraint firstItem="rJy-5S-L32" firstAttribute="centerX" secondItem="GDJ-cA-p6O" secondAttribute="centerX" id="cje-6b-qFc"/>
                                    <constraint firstItem="0m3-50-xMu" firstAttribute="top" secondItem="QVg-Za-BTx" secondAttribute="bottom" constant="8" id="k9O-P4-iOb"/>
                                    <constraint firstAttribute="trailing" secondItem="0m3-50-xMu" secondAttribute="trailing" constant="24" id="kAJ-Of-swR"/>
                                    <constraint firstItem="rJy-5S-L32" firstAttribute="width" secondItem="GDJ-cA-p6O" secondAttribute="width" id="sIK-FY-3yy"/>
                                </constraints>
                                <connections>
                                    <outletCollection property="gestureRecognizers" destination="BhJ-aT-mOo" appends="YES" id="tGA-h6-gBx"/>
                                </connections>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="jSI-Qd-enq"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="GDJ-cA-p6O" firstAttribute="top" relation="greaterThanOrEqual" secondItem="jSI-Qd-enq" secondAttribute="top" id="9My-he-fb4"/>
                            <constraint firstItem="GDJ-cA-p6O" firstAttribute="width" secondItem="VdL-ed-RB0" secondAttribute="width" id="CHb-yt-QBP"/>
                            <constraint firstAttribute="bottom" secondItem="GDJ-cA-p6O" secondAttribute="bottom" id="DdW-7n-BxU"/>
                            <constraint firstItem="GDJ-cA-p6O" firstAttribute="centerX" secondItem="jSI-Qd-enq" secondAttribute="centerX" id="zom-RW-gdb"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isDimmerViewTappable" value="NO"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="confirmButton" destination="unI-zi-wlF" id="dOT-SL-rcz"/>
                        <outlet property="contentView" destination="GDJ-cA-p6O" id="hke-GM-MoY"/>
                        <outlet property="messageLabel" destination="0m3-50-xMu" id="sq5-xh-PjH"/>
                        <outlet property="passwordFieldView" destination="pCp-VB-9Aq" id="yXe-2x-9Fb"/>
                        <outlet property="spacerViewHeightConstraint" destination="4gQ-tV-8xH" id="sgg-9E-fcl"/>
                        <outlet property="titleLabel" destination="QVg-Za-BTx" id="lDG-JN-QRC"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="upT-PL-R6g" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
                <tapGestureRecognizer id="BhJ-aT-mOo">
                    <connections>
                        <action selector="dismissKeyboard:" destination="S0p-ll-ecZ" id="sGc-HK-c50"/>
                    </connections>
                </tapGestureRecognizer>
            </objects>
            <point key="canvasLocation" x="3144" y="1809"/>
        </scene>
        <!--All contacts-->
        <scene sceneID="IwL-XJ-7g3">
            <objects>
                <viewController storyboardIdentifier="MayaContactsPermissionViewController" id="V2e-LW-FRD" customClass="MayaContactsPermissionViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="MmC-ud-7Zy">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" axis="vertical" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="jTE-3B-ft4">
                                <rect key="frame" x="24" y="142" width="327" height="383.5"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" image="Warning" translatesAutoresizingMaskIntoConstraints="NO" id="Cxk-Gp-JS7">
                                        <rect key="frame" x="63.5" y="0.0" width="200" height="200"/>
                                    </imageView>
                                    <stackView opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" axis="vertical" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="VNJ-XB-Mb1">
                                        <rect key="frame" x="0.0" y="200" width="327" height="183.5"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="FRx-i9-XlN">
                                                <rect key="frame" x="0.0" y="0.0" width="327" height="103.5"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" text="Allow Maya to access to your contacts" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aqW-Bp-8CD">
                                                        <rect key="frame" x="0.0" y="0.0" width="327" height="58.5"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" text="By allowing access, you can easily transact with any of your contacts." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="50D-Qi-033">
                                                        <rect key="frame" x="0.0" y="66.5" width="327" height="37"/>
                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                        <color key="textColor" name="ContentGrey6"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <button opaque="NO" contentMode="scaleToFill" verticalHuggingPriority="1000" verticalCompressionResistancePriority="1000" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LcE-bo-Bbj" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="127.5" width="327" height="56"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="rLf-jZ-LG9"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="Allow access"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="didTapAllowAccess:" destination="V2e-LW-FRD" eventType="touchUpInside" id="LaO-np-wKJ"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="VNJ-XB-Mb1" secondAttribute="trailing" id="dle-IX-TwK"/>
                                    <constraint firstItem="VNJ-XB-Mb1" firstAttribute="leading" secondItem="jTE-3B-ft4" secondAttribute="leading" id="fq1-22-lGR"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="3SF-fH-7WD"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="3SF-fH-7WD" firstAttribute="trailing" secondItem="jTE-3B-ft4" secondAttribute="trailing" constant="24" id="4vK-VR-0on"/>
                            <constraint firstItem="jTE-3B-ft4" firstAttribute="centerY" secondItem="MmC-ud-7Zy" secondAttribute="centerY" id="7LJ-HG-EDG"/>
                            <constraint firstItem="jTE-3B-ft4" firstAttribute="leading" secondItem="3SF-fH-7WD" secondAttribute="leading" constant="24" id="ibO-YW-UEV"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="All contacts" id="obV-tW-8PP">
                        <barButtonItem key="leftBarButtonItem" title="Back" id="W0z-hf-IJQ">
                            <connections>
                                <action selector="didTapBack:" destination="V2e-LW-FRD" id="FNL-LR-BVg"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="headerLabel" destination="aqW-Bp-8CD" id="QOy-lk-WfJ"/>
                        <outlet property="messageLabel" destination="50D-Qi-033" id="iOB-aw-OHW"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="K06-UP-hIK" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="3900" y="1809"/>
        </scene>
        <!--Maya Confirmation View Controller-->
        <scene sceneID="Id7-V9-9qt">
            <objects>
                <viewController storyboardIdentifier="MayaConfirmationViewController" id="7M1-Jj-DvY" customClass="MayaConfirmationViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="tp8-8B-rOO">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="csm-8a-nog">
                                <rect key="frame" x="0.0" y="64" width="375" height="523"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Maf-fY-wAY" userLabel="Content View">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="100"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="X48-9V-nTE">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="100"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="100" placeholder="YES" id="KtX-oB-GIC" userLabel="Removed at build time"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="X48-9V-nTE" secondAttribute="bottom" id="LqR-cT-vG7"/>
                                            <constraint firstItem="X48-9V-nTE" firstAttribute="leading" secondItem="Maf-fY-wAY" secondAttribute="leading" id="erH-Up-cZL"/>
                                            <constraint firstAttribute="trailing" secondItem="X48-9V-nTE" secondAttribute="trailing" id="hd2-fi-DDL"/>
                                            <constraint firstItem="X48-9V-nTE" firstAttribute="top" secondItem="Maf-fY-wAY" secondAttribute="top" id="mdl-yj-Z9w"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="Maf-fY-wAY" firstAttribute="leading" secondItem="csm-8a-nog" secondAttribute="leading" id="WPg-Xa-wRB"/>
                                    <constraint firstItem="Maf-fY-wAY" firstAttribute="top" secondItem="csm-8a-nog" secondAttribute="top" id="cnd-7M-Hea"/>
                                    <constraint firstAttribute="bottom" secondItem="Maf-fY-wAY" secondAttribute="bottom" id="cti-6H-PuX"/>
                                    <constraint firstAttribute="trailing" secondItem="Maf-fY-wAY" secondAttribute="trailing" id="g3N-cV-QBc"/>
                                    <constraint firstItem="Maf-fY-wAY" firstAttribute="width" secondItem="csm-8a-nog" secondAttribute="width" id="x4w-RU-eTg"/>
                                </constraints>
                            </scrollView>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="hFb-5u-tVP">
                                <rect key="frame" x="0.0" y="595" width="375" height="72"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nrn-Bc-Trr" userLabel="Button Container View">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="72"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="wF2-5w-y83" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="0.0" width="327" height="56"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_confirmation_continue_button"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="0ue-qW-XeB"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="Continue"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="wF2-5w-y83" secondAttribute="trailing" constant="24" id="Edx-Ew-zgm"/>
                                            <constraint firstItem="wF2-5w-y83" firstAttribute="top" secondItem="nrn-Bc-Trr" secondAttribute="top" id="FjS-FZ-R4C"/>
                                            <constraint firstItem="wF2-5w-y83" firstAttribute="leading" secondItem="nrn-Bc-Trr" secondAttribute="leading" constant="24" id="Geo-ys-rmm"/>
                                            <constraint firstAttribute="bottom" secondItem="wF2-5w-y83" secondAttribute="bottom" constant="16" id="oS9-rZ-nXq"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="P1R-KV-HeS"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="hFb-5u-tVP" firstAttribute="leading" secondItem="P1R-KV-HeS" secondAttribute="leading" id="Frm-jm-v0k"/>
                            <constraint firstItem="P1R-KV-HeS" firstAttribute="bottom" secondItem="hFb-5u-tVP" secondAttribute="bottom" id="JoX-aM-nCc"/>
                            <constraint firstItem="P1R-KV-HeS" firstAttribute="trailing" secondItem="csm-8a-nog" secondAttribute="trailing" id="Jwk-LW-63V"/>
                            <constraint firstItem="csm-8a-nog" firstAttribute="leading" secondItem="P1R-KV-HeS" secondAttribute="leading" id="KuK-s0-ffo"/>
                            <constraint firstItem="hFb-5u-tVP" firstAttribute="top" secondItem="csm-8a-nog" secondAttribute="bottom" constant="8" id="hVx-7N-Bze"/>
                            <constraint firstItem="csm-8a-nog" firstAttribute="top" secondItem="P1R-KV-HeS" secondAttribute="top" id="i8r-jk-bAp"/>
                            <constraint firstItem="hFb-5u-tVP" firstAttribute="trailing" secondItem="P1R-KV-HeS" secondAttribute="trailing" id="spJ-Qw-1h0"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="kSl-MK-gWc">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="tZH-2R-FFC">
                            <connections>
                                <action selector="didTapBack:" destination="7M1-Jj-DvY" id="dMz-dF-Ye2"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <connections>
                        <outlet property="bottomStackView" destination="hFb-5u-tVP" id="ycH-ZI-b11"/>
                        <outlet property="continueButton" destination="wF2-5w-y83" id="N1l-TM-ok1"/>
                        <outlet property="mainStackView" destination="X48-9V-nTE" id="dIf-vL-R9K"/>
                        <outlet property="scrollView" destination="csm-8a-nog" id="FxE-Wz-V6e"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="9mU-FX-lY1" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1823" y="1805"/>
        </scene>
        <!--Image Viewer View Controller-->
        <scene sceneID="Dgx-8E-zJp">
            <objects>
                <viewController storyboardIdentifier="ImageViewerViewController" id="cpC-2t-BlD" customClass="ImageViewerViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="BD1-36-u3c">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="5m5-1w-RXi">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <color key="backgroundColor" red="0.26666666666666666" green="0.31764705882352939" blue="0.3411764705882353" alpha="1" colorSpace="deviceRGB"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_image_viewer_view">
                                    <bool key="isElement" value="YES"/>
                                </accessibility>
                            </imageView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9i0-pT-ZL4">
                                <rect key="frame" x="319" y="28" width="40" height="40"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_image_viewer_back_button"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="5AS-8G-mSA"/>
                                    <constraint firstAttribute="width" constant="40" id="K40-9a-Qmh"/>
                                </constraints>
                                <state key="normal" image="iconCloseWhiteWithShadow"/>
                                <connections>
                                    <action selector="didTapBack:" destination="cpC-2t-BlD" eventType="touchUpInside" id="fSm-5g-rx9"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Lav-Ez-MrV"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="5m5-1w-RXi" firstAttribute="top" secondItem="BD1-36-u3c" secondAttribute="top" id="LRz-Gx-7sF"/>
                            <constraint firstItem="5m5-1w-RXi" firstAttribute="leading" secondItem="Lav-Ez-MrV" secondAttribute="leading" id="QfF-f0-6Hd"/>
                            <constraint firstItem="Lav-Ez-MrV" firstAttribute="trailing" secondItem="9i0-pT-ZL4" secondAttribute="trailing" constant="16" id="Sga-Vp-ky8"/>
                            <constraint firstAttribute="bottom" secondItem="5m5-1w-RXi" secondAttribute="bottom" id="ZAG-bb-ZJ3"/>
                            <constraint firstItem="Lav-Ez-MrV" firstAttribute="trailing" secondItem="5m5-1w-RXi" secondAttribute="trailing" id="cEf-SR-egi"/>
                            <constraint firstItem="9i0-pT-ZL4" firstAttribute="top" secondItem="Lav-Ez-MrV" secondAttribute="top" constant="8" id="j8n-42-gmi"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="imageView" destination="5m5-1w-RXi" id="4KL-gZ-rJW"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Rnv-4k-LXB" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1170" y="1806"/>
        </scene>
        <!--Success Alert View Controller-->
        <scene sceneID="lgZ-tJ-JsP">
            <objects>
                <viewController storyboardIdentifier="SuccessAlertViewController" id="oWR-MU-G8U" customClass="SuccessAlertViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="V2X-aj-Vfr">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" placeholderIntrinsicWidth="infinite" placeholderIntrinsicHeight="20" translatesAutoresizingMaskIntoConstraints="NO" id="QH2-7f-9Yx" userLabel="Spacer">
                                <rect key="frame" x="0.0" y="20" width="375" height="59"/>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="20" id="dKa-gK-CDr"/>
                                </constraints>
                            </view>
                            <view clipsSubviews="YES" contentMode="scaleToFill" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="EpX-72-rtT" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="24" y="79" width="327" height="470"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="y9l-Ko-EJL">
                                        <rect key="frame" x="287" y="0.0" width="40" height="40"/>
                                        <accessibility key="accessibilityConfiguration" identifier="pmios_success_alert_close_button"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="b6J-4w-X3k"/>
                                            <constraint firstAttribute="width" constant="40" id="scj-df-XgP"/>
                                        </constraints>
                                        <inset key="contentEdgeInsets" minX="10" minY="10" maxX="10" maxY="10"/>
                                        <state key="normal" image="iconCloseDark"/>
                                        <connections>
                                            <action selector="backToPreviousViewController:" destination="oWR-MU-G8U" eventType="touchUpInside" id="XTM-ON-CEp"/>
                                        </connections>
                                    </button>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="z9W-55-Rcf">
                                        <rect key="frame" x="16" y="40" width="295" height="414"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" image="iconCheckSuccess" translatesAutoresizingMaskIntoConstraints="NO" id="0D8-IX-7rc" customClass="ImageView" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="115" y="0.0" width="65" height="65"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_success_alert_status_image_view">
                                                    <bool key="isElement" value="YES"/>
                                                </accessibility>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="65" id="JVd-Po-OCk"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="32.5"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" text="Changed Password Successfully" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HwW-pe-Y8d">
                                                <rect key="frame" x="27" y="81" width="241.5" height="19.5"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_success_alert_title_label"/>
                                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                                <color key="textColor" red="0.18431372549019609" green="0.20000000000000001" blue="0.20392156862745098" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="Jta-UM-1Vy">
                                                <rect key="frame" x="0.0" y="116.5" width="295" height="297.5"/>
                                                <subviews>
                                                    <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NYB-2a-NmW">
                                                        <rect key="frame" x="0.0" y="0.0" width="295" height="241.5"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Gjs-v0-a4H" userLabel="Content View">
                                                                <rect key="frame" x="0.0" y="0.0" width="295" height="260.5"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="TcW-LD-1fM">
                                                                        <rect key="frame" x="0.0" y="0.0" width="295" height="260.5"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" text="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. " lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MiM-fW-7Ko">
                                                                                <rect key="frame" x="0.0" y="0.0" width="295" height="76.5"/>
                                                                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                <accessibility key="accessibilityConfiguration" identifier="pmios_success_alert_message_label"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                                                <color key="textColor" red="0.18431372549019609" green="0.20000000000000001" blue="0.20392156862745098" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="URR-vL-nU0">
                                                                                <rect key="frame" x="0.0" y="88.5" width="295" height="120"/>
                                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="120" id="Cj4-b7-M97"/>
                                                                                </constraints>
                                                                                <prototypes>
                                                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="SuccessAlertDetailTableViewCell" rowHeight="66" id="sH3-Sd-jE0" customClass="SuccessAlertDetailTableViewCell" customModule="PayMaya" customModuleProvider="target">
                                                                                        <rect key="frame" x="0.0" y="50" width="295" height="66"/>
                                                                                        <autoresizingMask key="autoresizingMask"/>
                                                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="sH3-Sd-jE0" id="BtC-yX-Whb">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="295" height="66"/>
                                                                                            <autoresizingMask key="autoresizingMask"/>
                                                                                            <subviews>
                                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ybb-85-EHu">
                                                                                                    <rect key="frame" x="8" y="0.0" width="279" height="1"/>
                                                                                                    <color key="backgroundColor" red="0.90980392160000001" green="0.90980392160000001" blue="0.90980392160000001" alpha="1" colorSpace="calibratedRGB"/>
                                                                                                    <constraints>
                                                                                                        <constraint firstAttribute="height" constant="1" id="1Qx-lB-rRO"/>
                                                                                                    </constraints>
                                                                                                </view>
                                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Title" textAlignment="natural" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="q4K-3s-Jwr">
                                                                                                    <rect key="frame" x="8" y="14" width="279" height="20"/>
                                                                                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                                                                                    <color key="textColor" red="0.67450980390000004" green="0.67450980390000004" blue="0.67450980390000004" alpha="1" colorSpace="calibratedRGB"/>
                                                                                                    <nil key="highlightedColor"/>
                                                                                                </label>
                                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="252" text="Detail" textAlignment="natural" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8sB-R4-QKE">
                                                                                                    <rect key="frame" x="8" y="37" width="279" height="19.5"/>
                                                                                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                                                                    <color key="textColor" red="0.2666666667" green="0.31764705879999999" blue="0.3411764706" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                                    <nil key="highlightedColor"/>
                                                                                                </label>
                                                                                            </subviews>
                                                                                            <constraints>
                                                                                                <constraint firstAttribute="bottom" secondItem="8sB-R4-QKE" secondAttribute="bottom" constant="9.5" id="1br-cI-Eoa"/>
                                                                                                <constraint firstItem="ybb-85-EHu" firstAttribute="top" secondItem="BtC-yX-Whb" secondAttribute="top" id="3Ko-xi-QGp"/>
                                                                                                <constraint firstAttribute="trailing" secondItem="ybb-85-EHu" secondAttribute="trailing" constant="8" id="5eR-gS-ztt"/>
                                                                                                <constraint firstAttribute="trailing" secondItem="q4K-3s-Jwr" secondAttribute="trailing" constant="8" id="5rO-FF-kDw"/>
                                                                                                <constraint firstItem="8sB-R4-QKE" firstAttribute="leading" secondItem="q4K-3s-Jwr" secondAttribute="leading" id="Dcy-ND-9L1"/>
                                                                                                <constraint firstItem="ybb-85-EHu" firstAttribute="leading" secondItem="BtC-yX-Whb" secondAttribute="leading" constant="8" id="E8w-WA-T1d"/>
                                                                                                <constraint firstItem="q4K-3s-Jwr" firstAttribute="top" secondItem="ybb-85-EHu" secondAttribute="bottom" constant="13" id="OwO-r0-beh"/>
                                                                                                <constraint firstItem="8sB-R4-QKE" firstAttribute="trailing" secondItem="q4K-3s-Jwr" secondAttribute="trailing" id="Q42-CD-bF7"/>
                                                                                                <constraint firstItem="8sB-R4-QKE" firstAttribute="top" secondItem="q4K-3s-Jwr" secondAttribute="bottom" constant="3" id="SRm-5N-DXl"/>
                                                                                                <constraint firstItem="q4K-3s-Jwr" firstAttribute="leading" secondItem="BtC-yX-Whb" secondAttribute="leading" constant="8" id="oF8-S8-ToH"/>
                                                                                            </constraints>
                                                                                        </tableViewCellContentView>
                                                                                        <inset key="separatorInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                                        <connections>
                                                                                            <outlet property="borderView" destination="ybb-85-EHu" id="x2y-qo-MBq"/>
                                                                                            <outlet property="detailLabel" destination="8sB-R4-QKE" id="73G-cC-WTy"/>
                                                                                            <outlet property="titleLabel" destination="q4K-3s-Jwr" id="paS-ox-cV0"/>
                                                                                        </connections>
                                                                                    </tableViewCell>
                                                                                </prototypes>
                                                                                <connections>
                                                                                    <outlet property="dataSource" destination="oWR-MU-G8U" id="6hJ-4x-saY"/>
                                                                                    <outlet property="delegate" destination="oWR-MU-G8U" id="l0u-9w-bkY"/>
                                                                                </connections>
                                                                            </tableView>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" translatesAutoresizingMaskIntoConstraints="NO" id="cFh-na-WDK">
                                                                                <rect key="frame" x="27.5" y="220.5" width="240" height="40"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="40" id="0Kd-tC-1vg"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                        </subviews>
                                                                        <constraints>
                                                                            <constraint firstItem="URR-vL-nU0" firstAttribute="width" secondItem="TcW-LD-1fM" secondAttribute="width" id="wos-Km-ouf"/>
                                                                        </constraints>
                                                                    </stackView>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                                                <constraints>
                                                                    <constraint firstItem="TcW-LD-1fM" firstAttribute="top" secondItem="Gjs-v0-a4H" secondAttribute="top" id="6Xf-ad-hBQ"/>
                                                                    <constraint firstAttribute="trailing" secondItem="TcW-LD-1fM" secondAttribute="trailing" id="cAS-2k-2nf"/>
                                                                    <constraint firstAttribute="bottom" secondItem="TcW-LD-1fM" secondAttribute="bottom" id="fxY-kY-CdI"/>
                                                                    <constraint firstItem="TcW-LD-1fM" firstAttribute="leading" secondItem="Gjs-v0-a4H" secondAttribute="leading" id="n6A-SV-sAj"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="bottom" secondItem="Gjs-v0-a4H" secondAttribute="bottom" id="BVZ-Qe-7t9"/>
                                                            <constraint firstItem="Gjs-v0-a4H" firstAttribute="leading" secondItem="NYB-2a-NmW" secondAttribute="leading" id="N7c-x9-zGh"/>
                                                            <constraint firstItem="Gjs-v0-a4H" firstAttribute="width" secondItem="NYB-2a-NmW" secondAttribute="width" id="YBh-8i-E7R"/>
                                                            <constraint firstAttribute="trailing" secondItem="Gjs-v0-a4H" secondAttribute="trailing" id="a5B-Fz-zg7"/>
                                                            <constraint firstAttribute="height" priority="100" constant="250" id="fdZ-aK-YCm"/>
                                                            <constraint firstItem="Gjs-v0-a4H" firstAttribute="top" secondItem="NYB-2a-NmW" secondAttribute="top" id="gff-Er-qHk"/>
                                                        </constraints>
                                                    </scrollView>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="riz-fd-TKE" customClass="Button" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="257.5" width="295" height="40"/>
                                                        <color key="backgroundColor" red="0.**********" green="0.63921568630000003" blue="0.87843137250000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="40" id="Ndb-Xf-Aos"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                                        <state key="normal" title="Action">
                                                            <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        </state>
                                                        <state key="highlighted">
                                                            <color key="titleColor" red="0.**********" green="0.63921568630000003" blue="0.87843137250000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        </state>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                <real key="value" value="2"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="didTapActionButton:" destination="oWR-MU-G8U" eventType="touchUpInside" id="hBx-nK-u73"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="Jta-UM-1Vy" secondAttribute="trailing" id="rTF-kI-iXd"/>
                                            <constraint firstItem="Jta-UM-1Vy" firstAttribute="leading" secondItem="z9W-55-Rcf" secondAttribute="leading" id="shz-r7-Jl7"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="z9W-55-Rcf" firstAttribute="top" secondItem="y9l-Ko-EJL" secondAttribute="bottom" id="1qN-Ue-z8K"/>
                                    <constraint firstAttribute="trailing" secondItem="z9W-55-Rcf" secondAttribute="trailing" constant="16" id="CS2-10-tSd"/>
                                    <constraint firstAttribute="bottom" secondItem="z9W-55-Rcf" secondAttribute="bottom" constant="16" id="CXa-Sm-XL9"/>
                                    <constraint firstItem="y9l-Ko-EJL" firstAttribute="top" secondItem="EpX-72-rtT" secondAttribute="top" id="fN1-TQ-3ps"/>
                                    <constraint firstAttribute="trailing" secondItem="y9l-Ko-EJL" secondAttribute="trailing" id="rc0-ZY-XdX"/>
                                    <constraint firstItem="z9W-55-Rcf" firstAttribute="leading" secondItem="EpX-72-rtT" secondAttribute="leading" constant="16" id="wDk-Gk-M7X"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                        <real key="value" value="1"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                        <color key="value" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view contentMode="scaleToFill" verticalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="xOM-B0-AFn" userLabel="Spacer">
                                <rect key="frame" x="0.0" y="549" width="375" height="118"/>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="SCh-Dc-wwX"/>
                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.5" colorSpace="calibratedRGB"/>
                        <gestureRecognizers/>
                        <constraints>
                            <constraint firstItem="SCh-Dc-wwX" firstAttribute="trailing" secondItem="xOM-B0-AFn" secondAttribute="trailing" id="4xq-BL-JEA"/>
                            <constraint firstItem="SCh-Dc-wwX" firstAttribute="trailing" secondItem="EpX-72-rtT" secondAttribute="trailing" constant="24" id="D0m-Jz-CsP"/>
                            <constraint firstItem="xOM-B0-AFn" firstAttribute="top" secondItem="EpX-72-rtT" secondAttribute="bottom" id="Fib-v7-wBS"/>
                            <constraint firstItem="EpX-72-rtT" firstAttribute="leading" secondItem="SCh-Dc-wwX" secondAttribute="leading" constant="24" id="G9J-Sf-zUE"/>
                            <constraint firstItem="xOM-B0-AFn" firstAttribute="leading" secondItem="SCh-Dc-wwX" secondAttribute="leading" id="WKH-Th-rru"/>
                            <constraint firstItem="QH2-7f-9Yx" firstAttribute="leading" secondItem="SCh-Dc-wwX" secondAttribute="leading" id="XGf-Ab-57x"/>
                            <constraint firstItem="EpX-72-rtT" firstAttribute="top" secondItem="QH2-7f-9Yx" secondAttribute="bottom" id="cy9-ot-4YH"/>
                            <constraint firstItem="SCh-Dc-wwX" firstAttribute="trailing" secondItem="QH2-7f-9Yx" secondAttribute="trailing" id="fwo-kO-ECG"/>
                            <constraint firstItem="SCh-Dc-wwX" firstAttribute="bottom" secondItem="xOM-B0-AFn" secondAttribute="bottom" id="h5j-S6-1iN"/>
                            <constraint firstItem="xOM-B0-AFn" firstAttribute="height" secondItem="QH2-7f-9Yx" secondAttribute="height" multiplier="2" id="lzQ-5b-3Ic"/>
                            <constraint firstItem="QH2-7f-9Yx" firstAttribute="top" secondItem="SCh-Dc-wwX" secondAttribute="top" id="tnx-7J-dnx"/>
                        </constraints>
                        <connections>
                            <outletCollection property="gestureRecognizers" destination="hA8-E2-mKh" appends="YES" id="CRO-nj-EOr"/>
                        </connections>
                    </view>
                    <connections>
                        <outlet property="actionButton" destination="riz-fd-TKE" id="AQL-jf-GS4"/>
                        <outlet property="contentView" destination="Gjs-v0-a4H" id="pMY-qY-HLw"/>
                        <outlet property="detailsTableHeightConstraint" destination="Cj4-b7-M97" id="anB-d9-h9w"/>
                        <outlet property="detailsTableView" destination="URR-vL-nU0" id="Q2F-IP-KnE"/>
                        <outlet property="iconImageView" destination="0D8-IX-7rc" id="Jnc-XW-vJ0"/>
                        <outlet property="messageLabel" destination="MiM-fW-7Ko" id="rsc-HX-bYF"/>
                        <outlet property="scrollHeightConstraint" destination="fdZ-aK-YCm" id="OBh-1M-0lf"/>
                        <outlet property="successImageHeightConstraint" destination="0Kd-tC-1vg" id="048-f4-27R"/>
                        <outlet property="successImageView" destination="cFh-na-WDK" id="DVd-ba-0LM"/>
                        <outlet property="titleLabel" destination="HwW-pe-Y8d" id="i6e-FB-NNz"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="OPG-Yf-JkX" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <tapGestureRecognizer id="hA8-E2-mKh"/>
            </objects>
            <point key="canvasLocation" x="-466" y="433"/>
        </scene>
        <!--Title-->
        <scene sceneID="dxc-Ws-sRa">
            <objects>
                <viewController storyboardIdentifier="WebViewParametersRequiringLandingPageViewController" id="YKr-c3-5oo" customClass="WebViewParametersRequiringLandingPageViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="AJf-uL-8mx">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <subviews>
                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="XEG-zh-WM2">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="mayaBirdPlug" translatesAutoresizingMaskIntoConstraints="NO" id="oyo-uZ-Gg7">
                                        <rect key="frame" x="77" y="107" width="221" height="171"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="221" id="nxB-iG-EeD"/>
                                            <constraint firstAttribute="height" constant="171" id="tc0-rJ-wUh"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Kindly bear with us" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gVa-dM-iud">
                                        <rect key="frame" x="117" y="314" width="141" height="19"/>
                                        <accessibility key="accessibilityConfiguration" identifier="pmios_webview_error_title_label"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                        <color key="textColor" red="0.18431372549019609" green="0.20000000000000001" blue="0.20392156862745098" alpha="0.84705882352941175" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ly9-IH-mAJ">
                                        <rect key="frame" x="187.5" y="355.5" width="0.0" height="0.0"/>
                                        <accessibility key="accessibilityConfiguration" identifier="pmios_webview_error_description_label"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="G85-tu-f6f" customClass="Button" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="16" y="388.5" width="343" height="40"/>
                                        <accessibility key="accessibilityConfiguration" identifier="pmios_webview_error_view_retry_button"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="mj7-GN-4i8"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                        <state key="normal" title="Tap to Retry">
                                            <color key="titleColor" red="0.**********" green="0.63921568630000003" blue="0.87843137250000003" alpha="1" colorSpace="calibratedRGB"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                <real key="value" value="4"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                <color key="value" red="0.**********" green="0.63921568630000003" blue="0.87843137250000003" alpha="1" colorSpace="calibratedRGB"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                <real key="value" value="1"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="retryButtonTapped" destination="YKr-c3-5oo" eventType="touchUpInside" id="wfM-qv-T3d"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" red="0.96078431372549022" green="0.96078431372549022" blue="0.96078431372549022" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstItem="gVa-dM-iud" firstAttribute="top" secondItem="oyo-uZ-Gg7" secondAttribute="bottom" constant="36" id="6Ui-g9-BbC"/>
                                    <constraint firstItem="G85-tu-f6f" firstAttribute="leading" secondItem="XEG-zh-WM2" secondAttribute="leading" constant="16" id="EeU-VW-BPc"/>
                                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="Ly9-IH-mAJ" secondAttribute="trailing" constant="20" id="KbQ-fE-A01"/>
                                    <constraint firstItem="Ly9-IH-mAJ" firstAttribute="top" secondItem="gVa-dM-iud" secondAttribute="bottom" constant="22.5" id="QYj-e0-5js"/>
                                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="gVa-dM-iud" secondAttribute="trailing" constant="20" id="TEa-qg-9y3"/>
                                    <constraint firstItem="gVa-dM-iud" firstAttribute="centerY" secondItem="XEG-zh-WM2" secondAttribute="centerY" id="YZC-CC-A8f"/>
                                    <constraint firstItem="G85-tu-f6f" firstAttribute="top" secondItem="Ly9-IH-mAJ" secondAttribute="bottom" constant="33" id="aTI-KV-TXe"/>
                                    <constraint firstItem="gVa-dM-iud" firstAttribute="centerX" secondItem="XEG-zh-WM2" secondAttribute="centerX" id="aYO-hA-I4Q"/>
                                    <constraint firstItem="Ly9-IH-mAJ" firstAttribute="centerX" secondItem="gVa-dM-iud" secondAttribute="centerX" id="gBU-FI-d94"/>
                                    <constraint firstItem="Ly9-IH-mAJ" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="XEG-zh-WM2" secondAttribute="leading" constant="20" id="hPB-MX-bEG"/>
                                    <constraint firstItem="oyo-uZ-Gg7" firstAttribute="centerX" secondItem="gVa-dM-iud" secondAttribute="centerX" id="ix2-JV-7GW"/>
                                    <constraint firstItem="gVa-dM-iud" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="XEG-zh-WM2" secondAttribute="leading" constant="20" id="mo8-dS-ovZ"/>
                                    <constraint firstAttribute="trailing" secondItem="G85-tu-f6f" secondAttribute="trailing" constant="16" id="wr3-Lf-vRj"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="nit-4w-jY7"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="XEG-zh-WM2" firstAttribute="top" secondItem="nit-4w-jY7" secondAttribute="top" id="3IM-Nl-tis"/>
                            <constraint firstItem="nit-4w-jY7" firstAttribute="bottom" secondItem="XEG-zh-WM2" secondAttribute="bottom" id="Dra-ER-SG1"/>
                            <constraint firstItem="XEG-zh-WM2" firstAttribute="leading" secondItem="nit-4w-jY7" secondAttribute="leading" id="YDO-cb-LPr"/>
                            <constraint firstItem="nit-4w-jY7" firstAttribute="trailing" secondItem="XEG-zh-WM2" secondAttribute="trailing" id="fgP-YE-L0b"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="Title" id="bTn-RZ-8xi">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="Xh4-x1-NYe">
                            <connections>
                                <action selector="didTapBack:" destination="YKr-c3-5oo" id="v7A-p1-hmU"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="errorMessageLabel" destination="Ly9-IH-mAJ" id="aLv-sy-CR3"/>
                        <outlet property="errorTitleLabel" destination="gVa-dM-iud" id="0cd-eb-QGW"/>
                        <outlet property="errorView" destination="XEG-zh-WM2" id="ay7-dP-0XN"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="E7z-6N-DKg" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="3609" y="-242"/>
        </scene>
        <!--Maya Web View Parameters Landing Page View Controller-->
        <scene sceneID="43R-ot-tkf">
            <objects>
                <viewController storyboardIdentifier="MayaWebViewParametersLandingPageViewController" id="8o5-mB-ecV" customClass="MayaWebViewParametersLandingPageViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="LPI-QG-HIp">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Vgc-NX-cKl">
                                <rect key="frame" x="0.0" y="20" width="375" height="72"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="FBk-uM-Mhm">
                                        <rect key="frame" x="24" y="24" width="24" height="24"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="24" id="7n9-Oa-o7J"/>
                                            <constraint firstAttribute="width" constant="24" id="NWN-LY-eUd"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" image="iconSystemCross"/>
                                        <connections>
                                            <action selector="didTapBack:" destination="8o5-mB-ecV" eventType="touchUpInside" id="OPk-3g-dfo"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text=" " lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dXr-Vc-bEo">
                                        <rect key="frame" x="72" y="24" width="143" height="24.5"/>
                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="20"/>
                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" name="ContentPrimaryWhite"/>
                                <constraints>
                                    <constraint firstItem="dXr-Vc-bEo" firstAttribute="centerY" secondItem="FBk-uM-Mhm" secondAttribute="centerY" id="FJK-gs-CG8"/>
                                    <constraint firstItem="FBk-uM-Mhm" firstAttribute="leading" secondItem="Vgc-NX-cKl" secondAttribute="leading" constant="24" id="UiW-Gb-3AS"/>
                                    <constraint firstItem="dXr-Vc-bEo" firstAttribute="leading" secondItem="FBk-uM-Mhm" secondAttribute="trailing" constant="24" id="a8T-j0-1s9"/>
                                    <constraint firstItem="FBk-uM-Mhm" firstAttribute="centerY" secondItem="Vgc-NX-cKl" secondAttribute="centerY" id="bz2-9F-kOM"/>
                                    <constraint firstAttribute="trailing" secondItem="dXr-Vc-bEo" secondAttribute="trailing" constant="160" id="nOd-7a-9UR"/>
                                    <constraint firstAttribute="height" constant="72" id="vgp-jt-91J"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OqT-G3-Eth" customClass="MayaDefaultErrorView" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="92" width="375" height="575"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="uyC-bA-IJi" customClass="MayaLoader" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="92" width="375" height="575"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="h6D-pw-UhU"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="uyC-bA-IJi" firstAttribute="height" secondItem="OqT-G3-Eth" secondAttribute="height" id="BcM-sk-CeX"/>
                            <constraint firstItem="uyC-bA-IJi" firstAttribute="centerY" secondItem="OqT-G3-Eth" secondAttribute="centerY" id="F5h-d7-l2M"/>
                            <constraint firstItem="h6D-pw-UhU" firstAttribute="bottom" secondItem="OqT-G3-Eth" secondAttribute="bottom" id="HIa-Vb-MPF"/>
                            <constraint firstItem="h6D-pw-UhU" firstAttribute="trailing" secondItem="OqT-G3-Eth" secondAttribute="trailing" id="MqP-97-TYo"/>
                            <constraint firstItem="Vgc-NX-cKl" firstAttribute="leading" secondItem="h6D-pw-UhU" secondAttribute="leading" id="QSb-Ha-v3F"/>
                            <constraint firstItem="uyC-bA-IJi" firstAttribute="width" secondItem="OqT-G3-Eth" secondAttribute="width" id="dSx-IX-cuw"/>
                            <constraint firstItem="OqT-G3-Eth" firstAttribute="leading" secondItem="h6D-pw-UhU" secondAttribute="leading" id="dp6-WZ-iMz"/>
                            <constraint firstItem="Vgc-NX-cKl" firstAttribute="top" secondItem="h6D-pw-UhU" secondAttribute="top" id="ewb-gv-coi"/>
                            <constraint firstItem="h6D-pw-UhU" firstAttribute="trailing" secondItem="Vgc-NX-cKl" secondAttribute="trailing" id="gz3-mM-b7H"/>
                            <constraint firstItem="uyC-bA-IJi" firstAttribute="centerX" secondItem="OqT-G3-Eth" secondAttribute="centerX" id="h3F-vP-clO"/>
                            <constraint firstItem="OqT-G3-Eth" firstAttribute="top" secondItem="Vgc-NX-cKl" secondAttribute="bottom" id="kRo-yt-GYS"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarBackButtonHidden" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="errorView" destination="OqT-G3-Eth" id="oBf-wn-oh4"/>
                        <outlet property="mayaLoader" destination="uyC-bA-IJi" id="DuZ-d9-UN2"/>
                        <outlet property="titleLabel" destination="dXr-Vc-bEo" id="qnv-5F-rhv"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="g3i-ih-ITG" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="4316" y="433"/>
        </scene>
        <!--Infinite Page View Controller-->
        <scene sceneID="UAm-3c-TQ0">
            <objects>
                <pageViewController storyboardIdentifier="InfinitePageViewController" autoresizesArchivedViewToFullSize="NO" transitionStyle="scroll" navigationOrientation="horizontal" spineLocation="none" id="Rnj-Oo-zJf" customClass="InfinitePageViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="zbc-rh-fJg" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1846" y="2500"/>
        </scene>
        <!--Page View Controller-->
        <scene sceneID="iif-id-4lZ">
            <objects>
                <pageViewController storyboardIdentifier="PageViewController" autoresizesArchivedViewToFullSize="NO" transitionStyle="scroll" navigationOrientation="horizontal" spineLocation="none" id="d4T-fT-iYq" customClass="PageViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="f3Y-JJ-XEm" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1169" y="2500"/>
        </scene>
        <!--Confirmation Bottom Sheet View Controller-->
        <scene sceneID="g1M-Gf-CBK">
            <objects>
                <viewController storyboardIdentifier="ConfirmationBottomSheetViewController" id="Xcg-hT-Oil" customClass="ConfirmationBottomSheetViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="pfj-b5-yIo">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lZX-xU-Oq1">
                                <rect key="frame" x="0.0" y="-183" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="osm-b3-nVy">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="850"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lSq-BK-pQH">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="850"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Confirm" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ejm-9q-PWA">
                                                        <rect key="frame" x="16" y="16" width="71.5" height="21"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_confirmation_detail_title_label"/>
                                                        <fontDescription key="fontDescription" name="WorkSans-SemiBold" family="Work Sans" pointSize="18"/>
                                                        <color key="textColor" red="0.11372549019607843" green="0.11764705882352941" blue="0.12941176470588234" alpha="1" colorSpace="calibratedRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="CKk-e0-MNX">
                                                        <rect key="frame" x="305" y="11" width="54" height="31"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_confirmation_detail_cancel_button"/>
                                                        <fontDescription key="fontDescription" name="WorkSans-SemiBold" family="Work Sans" pointSize="16"/>
                                                        <state key="normal" title="Cancel">
                                                            <color key="titleColor" red="0.0" green="0.70588235294117641" blue="0.39215686274509803" alpha="1" colorSpace="calibratedRGB"/>
                                                        </state>
                                                        <connections>
                                                            <action selector="didTapClose:" destination="Xcg-hT-Oil" eventType="touchUpInside" id="y07-7o-AI6"/>
                                                        </connections>
                                                    </button>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tVP-wW-OS5">
                                                        <rect key="frame" x="0.0" y="53" width="375" height="1"/>
                                                        <color key="backgroundColor" red="0.90196078431372551" green="0.90196078431372551" blue="0.90980392156862744" alpha="1" colorSpace="calibratedRGB"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="1" id="TIQ-aH-zCl"/>
                                                        </constraints>
                                                    </view>
                                                    <tableView clipsSubviews="YES" contentMode="scaleToFill" bounces="NO" bouncesZoom="NO" dataMode="prototypes" style="plain" separatorStyle="none" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="0tc-GG-MEv">
                                                        <rect key="frame" x="0.0" y="70" width="375" height="673"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="50" id="F8G-gz-hhZ"/>
                                                        </constraints>
                                                        <prototypes>
                                                            <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="ConfirmationBottomSheetDetailTableViewCell" id="W2i-bt-skd" customClass="ConfirmationBottomSheetDetailTableViewCell" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="50" width="375" height="24.5"/>
                                                                <autoresizingMask key="autoresizingMask"/>
                                                                <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="W2i-bt-skd" id="Mfj-l1-3gF">
                                                                    <rect key="frame" x="0.0" y="0.0" width="375" height="24.5"/>
                                                                    <autoresizingMask key="autoresizingMask"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="sAR-Ny-qEk">
                                                                            <rect key="frame" x="16" y="4" width="124" height="16.5"/>
                                                                            <accessibility key="accessibilityConfiguration" identifier="pmios_confirmation_detail_cell_title_label"/>
                                                                            <fontDescription key="fontDescription" name="WorkSans-SemiBold" family="Work Sans" pointSize="14"/>
                                                                            <color key="textColor" name="Gray100Base"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="10" translatesAutoresizingMaskIntoConstraints="NO" id="Sh5-ZP-3Qm">
                                                                            <rect key="frame" x="156" y="4" width="203" height="16.5"/>
                                                                            <accessibility key="accessibilityConfiguration" identifier="pmios_confirmation_detail_cell_value_label"/>
                                                                            <fontDescription key="fontDescription" name="WorkSans-Medium" family="Work Sans" pointSize="14"/>
                                                                            <color key="textColor" name="Gray200S"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <constraints>
                                                                        <constraint firstItem="sAR-Ny-qEk" firstAttribute="leading" secondItem="Mfj-l1-3gF" secondAttribute="leading" constant="16" id="BfT-H8-cQ8"/>
                                                                        <constraint firstAttribute="trailing" secondItem="Sh5-ZP-3Qm" secondAttribute="trailing" constant="16" id="Gq8-WL-hSk"/>
                                                                        <constraint firstItem="Sh5-ZP-3Qm" firstAttribute="leading" secondItem="sAR-Ny-qEk" secondAttribute="trailing" constant="16" id="JKn-5Q-935"/>
                                                                        <constraint firstItem="sAR-Ny-qEk" firstAttribute="width" secondItem="Mfj-l1-3gF" secondAttribute="width" multiplier="0.33" id="dDJ-SR-cm0"/>
                                                                        <constraint firstAttribute="bottom" secondItem="sAR-Ny-qEk" secondAttribute="bottom" constant="4" id="flz-hb-adY"/>
                                                                        <constraint firstItem="Sh5-ZP-3Qm" firstAttribute="centerY" secondItem="sAR-Ny-qEk" secondAttribute="centerY" id="oGR-XC-in6"/>
                                                                        <constraint firstItem="sAR-Ny-qEk" firstAttribute="top" secondItem="Mfj-l1-3gF" secondAttribute="top" constant="4" id="zIr-Dw-GiH"/>
                                                                    </constraints>
                                                                </tableViewCellContentView>
                                                                <connections>
                                                                    <outlet property="titleLabel" destination="sAR-Ny-qEk" id="3Wb-C5-RUM"/>
                                                                    <outlet property="valueLabel" destination="Sh5-ZP-3Qm" id="V4t-Mh-yJm"/>
                                                                </connections>
                                                            </tableViewCell>
                                                        </prototypes>
                                                        <connections>
                                                            <outlet property="dataSource" destination="Xcg-hT-Oil" id="xeL-f6-yEN"/>
                                                            <outlet property="delegate" destination="Xcg-hT-Oil" id="qd6-CC-URt"/>
                                                        </connections>
                                                    </tableView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WPm-YW-IW2">
                                                        <rect key="frame" x="0.0" y="759" width="375" height="1"/>
                                                        <color key="backgroundColor" red="0.90196078431372551" green="0.90196078431372551" blue="0.90980392156862744" alpha="1" colorSpace="calibratedRGB"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="1" id="Agf-ek-YDD"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="c22-MU-wS4" customClass="NewSlideToConfirmView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="16" y="776" width="343" height="58"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <accessibility key="accessibilityConfiguration">
                                                            <bool key="isElement" value="YES"/>
                                                        </accessibility>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="58" id="UcQ-LJ-qIH"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="mainText" value="Slide to buy"/>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="subText" value="You will be charged after this"/>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <outlet property="delegate" destination="Xcg-hT-Oil" id="u9Y-aP-T0O"/>
                                                        </connections>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="0tc-GG-MEv" secondAttribute="trailing" id="0rw-eW-vL6"/>
                                                    <constraint firstAttribute="trailing" secondItem="WPm-YW-IW2" secondAttribute="trailing" id="2hv-DF-aPA"/>
                                                    <constraint firstAttribute="bottom" secondItem="c22-MU-wS4" secondAttribute="bottom" constant="16" id="4Yc-6y-Jhj"/>
                                                    <constraint firstAttribute="trailing" secondItem="CKk-e0-MNX" secondAttribute="trailing" constant="16" id="73p-yw-z6g"/>
                                                    <constraint firstItem="0tc-GG-MEv" firstAttribute="leading" secondItem="lSq-BK-pQH" secondAttribute="leading" id="8Ld-ky-ceD"/>
                                                    <constraint firstAttribute="trailing" secondItem="c22-MU-wS4" secondAttribute="trailing" constant="16" id="9D4-uu-67I"/>
                                                    <constraint firstItem="0tc-GG-MEv" firstAttribute="top" secondItem="tVP-wW-OS5" secondAttribute="bottom" constant="16" id="Ekp-5e-fwU"/>
                                                    <constraint firstAttribute="trailing" secondItem="tVP-wW-OS5" secondAttribute="trailing" id="P6L-3c-G6V"/>
                                                    <constraint firstItem="WPm-YW-IW2" firstAttribute="top" secondItem="0tc-GG-MEv" secondAttribute="bottom" constant="16" id="U9Q-iB-eK5"/>
                                                    <constraint firstItem="Ejm-9q-PWA" firstAttribute="top" secondItem="lSq-BK-pQH" secondAttribute="top" constant="16" id="XIK-iP-EXJ"/>
                                                    <constraint firstItem="c22-MU-wS4" firstAttribute="leading" secondItem="lSq-BK-pQH" secondAttribute="leading" constant="16" id="buK-Wv-wOs"/>
                                                    <constraint firstItem="tVP-wW-OS5" firstAttribute="leading" secondItem="lSq-BK-pQH" secondAttribute="leading" id="dlb-xN-UWr"/>
                                                    <constraint firstItem="WPm-YW-IW2" firstAttribute="leading" secondItem="lSq-BK-pQH" secondAttribute="leading" id="eGE-bX-21C"/>
                                                    <constraint firstItem="Ejm-9q-PWA" firstAttribute="leading" secondItem="lSq-BK-pQH" secondAttribute="leading" constant="16" id="i0D-SF-pMJ"/>
                                                    <constraint firstItem="CKk-e0-MNX" firstAttribute="centerY" secondItem="Ejm-9q-PWA" secondAttribute="centerY" id="l4E-nE-9gK"/>
                                                    <constraint firstItem="c22-MU-wS4" firstAttribute="top" secondItem="WPm-YW-IW2" secondAttribute="bottom" constant="16" id="lCC-Vu-JZT"/>
                                                    <constraint firstItem="tVP-wW-OS5" firstAttribute="top" secondItem="Ejm-9q-PWA" secondAttribute="bottom" constant="16" id="qsT-Db-svN"/>
                                                    <constraint firstItem="CKk-e0-MNX" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="Ejm-9q-PWA" secondAttribute="trailing" constant="16" id="rBP-Li-d29"/>
                                                </constraints>
                                            </view>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sVm-6N-cA3">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="850"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconRedExclamation" translatesAutoresizingMaskIntoConstraints="NO" id="Wmo-jA-Ons">
                                                        <rect key="frame" x="151.5" y="40" width="72" height="72"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_confirmation_detail_error_icon_image_view">
                                                            <bool key="isElement" value="YES"/>
                                                        </accessibility>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="72" id="07I-m1-FLd"/>
                                                            <constraint firstAttribute="width" constant="72" id="Wep-dd-S7b"/>
                                                        </constraints>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="teO-eE-reJ">
                                                        <rect key="frame" x="40" y="128" width="295" height="601"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_confirmation_detail_error_message_label"/>
                                                        <fontDescription key="fontDescription" name="WorkSans-Medium" family="Work Sans" pointSize="14"/>
                                                        <color key="textColor" name="Gray200S"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="483-6M-5EV">
                                                        <rect key="frame" x="0.0" y="769" width="375" height="1"/>
                                                        <color key="backgroundColor" red="0.90196078430000004" green="0.90196078430000004" blue="0.90980392160000001" alpha="1" colorSpace="calibratedRGB"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="1" id="4Sf-qi-3cj"/>
                                                        </constraints>
                                                    </view>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="EJV-Pi-QdE" customClass="Button" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="16" y="786" width="343" height="48"/>
                                                        <color key="backgroundColor" name="Primary100Base"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_confirmation_detail_error_cta_button"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="48" id="GlA-tg-6LF"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="WorkSans-SemiBold" family="Work Sans" pointSize="18"/>
                                                        <state key="normal" title="Button">
                                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        </state>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                <real key="value" value="4"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </button>
                                                </subviews>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="EJV-Pi-QdE" secondAttribute="trailing" constant="16" id="2uU-yt-wLg"/>
                                                    <constraint firstItem="EJV-Pi-QdE" firstAttribute="top" secondItem="483-6M-5EV" secondAttribute="bottom" constant="16" id="3H7-QS-oIq"/>
                                                    <constraint firstItem="Wmo-jA-Ons" firstAttribute="centerX" secondItem="sVm-6N-cA3" secondAttribute="centerX" id="8xi-Nz-fXr"/>
                                                    <constraint firstItem="teO-eE-reJ" firstAttribute="top" secondItem="Wmo-jA-Ons" secondAttribute="bottom" constant="16" id="LIA-Vz-7q4"/>
                                                    <constraint firstAttribute="trailing" secondItem="teO-eE-reJ" secondAttribute="trailing" constant="40" id="Op3-1Q-sBY"/>
                                                    <constraint firstItem="483-6M-5EV" firstAttribute="top" secondItem="teO-eE-reJ" secondAttribute="bottom" constant="40" id="T6c-qq-15F"/>
                                                    <constraint firstItem="483-6M-5EV" firstAttribute="leading" secondItem="sVm-6N-cA3" secondAttribute="leading" id="YPn-e3-68y"/>
                                                    <constraint firstItem="EJV-Pi-QdE" firstAttribute="leading" secondItem="sVm-6N-cA3" secondAttribute="leading" constant="16" id="ZN3-JX-9lz"/>
                                                    <constraint firstAttribute="trailing" secondItem="483-6M-5EV" secondAttribute="trailing" id="bFP-Uf-hgJ"/>
                                                    <constraint firstItem="Wmo-jA-Ons" firstAttribute="top" secondItem="sVm-6N-cA3" secondAttribute="top" constant="40" id="c7D-fk-dDi"/>
                                                    <constraint firstAttribute="bottom" secondItem="EJV-Pi-QdE" secondAttribute="bottom" constant="16" id="nWo-gU-t93"/>
                                                    <constraint firstItem="teO-eE-reJ" firstAttribute="leading" secondItem="sVm-6N-cA3" secondAttribute="leading" constant="40" id="uK9-uR-1Ec"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="lSq-BK-pQH" firstAttribute="leading" secondItem="osm-b3-nVy" secondAttribute="leading" id="0Gb-5Y-etA"/>
                                            <constraint firstAttribute="bottom" secondItem="sVm-6N-cA3" secondAttribute="bottom" id="4h4-le-eAV"/>
                                            <constraint firstItem="sVm-6N-cA3" firstAttribute="top" secondItem="osm-b3-nVy" secondAttribute="top" id="d9c-2O-c5X"/>
                                            <constraint firstItem="lSq-BK-pQH" firstAttribute="top" secondItem="osm-b3-nVy" secondAttribute="top" id="fA9-r3-na0"/>
                                            <constraint firstAttribute="bottom" secondItem="lSq-BK-pQH" secondAttribute="bottom" id="hw6-6V-qro"/>
                                            <constraint firstAttribute="trailing" secondItem="lSq-BK-pQH" secondAttribute="trailing" id="lvJ-7K-Psg"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="sVm-6N-cA3" firstAttribute="leading" secondItem="lZX-xU-Oq1" secondAttribute="leading" id="8kX-tL-fSC"/>
                                    <constraint firstAttribute="trailing" secondItem="osm-b3-nVy" secondAttribute="trailing" id="NbV-St-4U0"/>
                                    <constraint firstItem="osm-b3-nVy" firstAttribute="top" secondItem="lZX-xU-Oq1" secondAttribute="top" id="cWW-uh-ogW"/>
                                    <constraint firstItem="osm-b3-nVy" firstAttribute="leading" secondItem="lZX-xU-Oq1" secondAttribute="leading" id="qMK-ob-45D"/>
                                    <constraint firstAttribute="trailing" secondItem="sVm-6N-cA3" secondAttribute="trailing" id="xVw-gK-EFd"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="zgh-XA-6b4"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="lZX-xU-Oq1" firstAttribute="height" secondItem="pfj-b5-yIo" secondAttribute="height" id="bV7-ek-Dlp"/>
                            <constraint firstAttribute="bottom" secondItem="lZX-xU-Oq1" secondAttribute="top" constant="850" id="jF4-T8-8HA"/>
                            <constraint firstItem="osm-b3-nVy" firstAttribute="bottom" secondItem="zgh-XA-6b4" secondAttribute="bottom" id="m9s-sj-nRP"/>
                            <constraint firstItem="lZX-xU-Oq1" firstAttribute="leading" secondItem="zgh-XA-6b4" secondAttribute="leading" id="ocG-Fr-IMw"/>
                            <constraint firstItem="zgh-XA-6b4" firstAttribute="trailing" secondItem="lZX-xU-Oq1" secondAttribute="trailing" id="xne-oQ-Igi"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isContentViewDraggable" value="NO"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="cancelButton" destination="CKk-e0-MNX" id="FR4-65-Ysm"/>
                        <outlet property="confirmLabel" destination="Ejm-9q-PWA" id="Zn6-oy-HP1"/>
                        <outlet property="confirmationView" destination="lSq-BK-pQH" id="l3P-He-yHY"/>
                        <outlet property="contentView" destination="lZX-xU-Oq1" id="ach-no-eLJ"/>
                        <outlet property="contentViewAnchorToBottomSafeAreaConstraint" destination="m9s-sj-nRP" id="ctS-NZ-1DW"/>
                        <outlet property="contentViewTopConstraint" destination="jF4-T8-8HA" id="RHM-hV-Ypn"/>
                        <outlet property="errorCTAButton" destination="EJV-Pi-QdE" id="8Zv-rX-cHs"/>
                        <outlet property="errorMessageLabel" destination="teO-eE-reJ" id="cnW-Su-fwh"/>
                        <outlet property="errorView" destination="sVm-6N-cA3" id="F60-w7-tc6"/>
                        <outlet property="slideToConfirmView" destination="c22-MU-wS4" id="sZH-iK-jcd"/>
                        <outlet property="tableView" destination="0tc-GG-MEv" id="0Vf-d0-tnI"/>
                        <outlet property="tableViewHeightConstraint" destination="F8G-gz-hhZ" id="BGS-Yf-ZSv"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="zjY-9K-PZ6" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-466" y="2499"/>
        </scene>
        <!--Maya Updater View Controller-->
        <scene sceneID="rvB-3O-eyk">
            <objects>
                <viewController storyboardIdentifier="MayaUpdaterViewController" id="i6P-br-vXa" customClass="MayaUpdaterViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="B2b-My-hEm">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="imageUpdater" translatesAutoresizingMaskIntoConstraints="NO" id="yp2-Vk-9wA">
                                <rect key="frame" x="63" y="174" width="249" height="279"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="imageMayaLogoMedium" translatesAutoresizingMaskIntoConstraints="NO" id="4GR-gs-oMu">
                                <rect key="frame" x="104" y="97" width="167" height="57"/>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Update required" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ivr-Ox-4yT">
                                <rect key="frame" x="24" y="463" width="327" height="29.5"/>
                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                <color key="textColor" name="UpdaterTitleTextColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="751" text="Update your app to enjoy the newest features and fixes." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontForContentSizeCategory="YES" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ync-n4-xMM">
                                <rect key="frame" x="24" y="500.5" width="327" height="37"/>
                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="14"/>
                                <color key="textColor" name="ContentGrey4"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="vyC-Cc-pw5" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="24" y="581" width="327" height="56"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="56" id="1sv-mH-N8B"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="Update now"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                        <integer key="value" value="0"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                        <integer key="value" value="0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="didTapUpdate:" destination="i6P-br-vXa" eventType="touchUpInside" id="Bgs-rT-lNF"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="pyi-7A-EzF"/>
                        <color key="backgroundColor" name="UpdaterBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="pyi-7A-EzF" firstAttribute="trailing" secondItem="vyC-Cc-pw5" secondAttribute="trailing" constant="24" id="4ry-W1-FU0"/>
                            <constraint firstItem="4GR-gs-oMu" firstAttribute="centerX" secondItem="yp2-Vk-9wA" secondAttribute="centerX" id="54I-HX-CQC"/>
                            <constraint firstItem="vyC-Cc-pw5" firstAttribute="top" relation="greaterThanOrEqual" secondItem="ync-n4-xMM" secondAttribute="bottom" constant="8" id="6En-EI-qBl"/>
                            <constraint firstItem="pyi-7A-EzF" firstAttribute="bottom" secondItem="vyC-Cc-pw5" secondAttribute="bottom" constant="30" id="9c4-Dc-7X0"/>
                            <constraint firstItem="yp2-Vk-9wA" firstAttribute="width" secondItem="B2b-My-hEm" secondAttribute="height" multiplier="28:75" id="BeT-hs-FDU"/>
                            <constraint firstItem="ivr-Ox-4yT" firstAttribute="leading" secondItem="pyi-7A-EzF" secondAttribute="leading" constant="24" id="D67-w1-d3Z"/>
                            <constraint firstItem="yp2-Vk-9wA" firstAttribute="centerX" secondItem="pyi-7A-EzF" secondAttribute="centerX" id="OFe-Ih-nLb"/>
                            <constraint firstItem="ync-n4-xMM" firstAttribute="trailing" secondItem="ivr-Ox-4yT" secondAttribute="trailing" id="OjP-Bf-F5a"/>
                            <constraint firstItem="ivr-Ox-4yT" firstAttribute="top" secondItem="yp2-Vk-9wA" secondAttribute="bottom" constant="10" id="Paj-th-Gdj"/>
                            <constraint firstItem="yp2-Vk-9wA" firstAttribute="centerY" secondItem="pyi-7A-EzF" secondAttribute="centerY" constant="-30" id="VDI-kU-uz4"/>
                            <constraint firstItem="pyi-7A-EzF" firstAttribute="trailing" secondItem="ivr-Ox-4yT" secondAttribute="trailing" constant="24" id="cH1-dI-N5N"/>
                            <constraint firstItem="vyC-Cc-pw5" firstAttribute="leading" secondItem="pyi-7A-EzF" secondAttribute="leading" constant="24" id="hvJ-vP-4gw"/>
                            <constraint firstItem="ync-n4-xMM" firstAttribute="leading" secondItem="ivr-Ox-4yT" secondAttribute="leading" id="k2G-LN-74y"/>
                            <constraint firstItem="yp2-Vk-9wA" firstAttribute="top" secondItem="4GR-gs-oMu" secondAttribute="bottom" constant="20" id="kw8-EQ-muc"/>
                            <constraint firstItem="ync-n4-xMM" firstAttribute="top" secondItem="ivr-Ox-4yT" secondAttribute="bottom" constant="8" id="llg-qo-F0e"/>
                            <constraint firstItem="4GR-gs-oMu" firstAttribute="width" secondItem="B2b-My-hEm" secondAttribute="height" multiplier="1:4" id="ud0-aN-qFx"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="statusBarStyle">
                            <integer key="value" value="1"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                    </userDefinedRuntimeAttributes>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="lKm-Ll-Fxl" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="693.75" y="-1871.8309859154929"/>
        </scene>
        <!--Maya Alert Modal View Controller-->
        <scene sceneID="rPm-Ul-wZB">
            <objects>
                <viewController storyboardIdentifier="MayaAlertModalViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="csD-Y9-Orc" customClass="MayaAlertModalViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="7WY-l2-OF5"/>
                        <viewControllerLayoutGuide type="bottom" id="jRy-pv-btB"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="S6J-tY-8ep">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lGU-lc-668" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="24" y="96.5" width="327" height="474"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="6X2-36-hDd">
                                        <rect key="frame" x="24" y="24" width="279" height="426"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="addMoneyBank" translatesAutoresizingMaskIntoConstraints="NO" id="b7N-QJ-5mH">
                                                <rect key="frame" x="0.0" y="0.0" width="279" height="63"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_alert_modal_image">
                                                    <bool key="isElement" value="YES"/>
                                                </accessibility>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Error title" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bbx-69-pnU">
                                                <rect key="frame" x="0.0" y="71" width="279" height="29.5"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_alert_modal_title_label"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                                <color key="textColor" name="ContentPrimaryBlack"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Error message" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Eqo-e9-zMB">
                                                <rect key="frame" x="0.0" y="108.5" width="279" height="18.5"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_alert_modal_message_label"/>
                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="14"/>
                                                <color key="textColor" name="ContentGrey6"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" editable="NO" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="5Eu-fr-WoC">
                                                <rect key="frame" x="0.0" y="135" width="279" height="250"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <string key="text">Lorem ipsum dolor sit er elit lamet, consectetaur cillium adipisicing pecu, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Nam liber te conscient to factor tum poen legum odioque civiuda.</string>
                                                <color key="textColor" systemColor="labelColor"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                                <dataDetectorType key="dataDetectorTypes" link="YES"/>
                                                <connections>
                                                    <outlet property="delegate" destination="csD-Y9-Orc" id="Ge7-0F-573"/>
                                                </connections>
                                            </textView>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1xP-XA-4uf">
                                                <rect key="frame" x="0.0" y="393" width="279" height="33"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dsT-fI-jWq">
                                                        <rect key="frame" x="0.0" y="16" width="279" height="1"/>
                                                        <color key="backgroundColor" name="ModalGrey3"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="1" id="KTX-jW-NAd"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstItem="dsT-fI-jWq" firstAttribute="leading" secondItem="1xP-XA-4uf" secondAttribute="leading" id="ONm-Zt-WW6"/>
                                                    <constraint firstItem="dsT-fI-jWq" firstAttribute="top" secondItem="1xP-XA-4uf" secondAttribute="top" constant="16" id="wuf-u1-jOQ"/>
                                                    <constraint firstAttribute="bottom" secondItem="dsT-fI-jWq" secondAttribute="bottom" constant="16" id="yUE-OE-a61"/>
                                                    <constraint firstAttribute="trailing" secondItem="dsT-fI-jWq" secondAttribute="trailing" id="z1f-d8-EUO"/>
                                                </constraints>
                                            </view>
                                            <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="YoE-0T-fVc">
                                                <rect key="frame" x="0.0" y="426" width="279" height="0.0"/>
                                            </stackView>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" name="ModalPrimaryWhite"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="6X2-36-hDd" secondAttribute="bottom" constant="24" id="LKa-YP-Gda"/>
                                    <constraint firstItem="6X2-36-hDd" firstAttribute="leading" secondItem="lGU-lc-668" secondAttribute="leading" constant="24" id="LsW-2o-SXC"/>
                                    <constraint firstItem="6X2-36-hDd" firstAttribute="top" secondItem="lGU-lc-668" secondAttribute="top" constant="24" id="aGI-IF-PuS"/>
                                    <constraint firstAttribute="trailing" secondItem="6X2-36-hDd" secondAttribute="trailing" constant="24" id="kCb-zB-NMf"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                        <real key="value" value="16"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="veb-KX-b1M"/>
                        <color key="backgroundColor" white="0.0" alpha="0.5" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <gestureRecognizers/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="lGU-lc-668" secondAttribute="trailing" constant="24" id="3cZ-Wv-0qC"/>
                            <constraint firstItem="lGU-lc-668" firstAttribute="centerY" secondItem="S6J-tY-8ep" secondAttribute="centerY" id="MSV-Al-kpC"/>
                            <constraint firstItem="lGU-lc-668" firstAttribute="leading" secondItem="S6J-tY-8ep" secondAttribute="leading" constant="24" id="RWR-9R-NtN"/>
                            <constraint firstItem="lGU-lc-668" firstAttribute="centerX" secondItem="S6J-tY-8ep" secondAttribute="centerX" id="x6l-Vh-6wn"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="buttonStack" destination="YoE-0T-fVc" id="1Q6-dw-Xo7"/>
                        <outlet property="image" destination="b7N-QJ-5mH" id="ZE6-xV-FCv"/>
                        <outlet property="messageLabel" destination="Eqo-e9-zMB" id="sFb-f8-EsI"/>
                        <outlet property="textView" destination="5Eu-fr-WoC" id="ykb-YE-7sa"/>
                        <outlet property="titleLabel" destination="bbx-69-pnU" id="5Sa-d4-0os"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="FsM-fJ-4gE" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
                <tapGestureRecognizer id="bek-cb-GoM">
                    <connections>
                        <action selector="didTapBack:" destination="csD-Y9-Orc" id="qNH-gT-GWD"/>
                    </connections>
                </tapGestureRecognizer>
            </objects>
            <point key="canvasLocation" x="-466" y="3188"/>
        </scene>
        <!--Maya Alert Bottom Sheet View Controller-->
        <scene sceneID="LYa-Np-6D8">
            <objects>
                <viewController storyboardIdentifier="MayaAlertBottomSheetViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="GUm-To-VHL" customClass="MayaAlertBottomSheetViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="avZ-sy-OE2">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="l6r-za-BgI" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="167" width="375" height="667"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="feX-Ca-1Lc">
                                        <rect key="frame" x="16" y="24" width="343" height="460"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="kycUpgradeRocket" translatesAutoresizingMaskIntoConstraints="NO" id="UC2-kS-ei3">
                                                <rect key="frame" x="0.0" y="0.0" width="343" height="198.5"/>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kgo-G8-DdE">
                                                <rect key="frame" x="0.0" y="206.5" width="343" height="29.5"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                                <color key="textColor" name="ContentPrimaryBlack"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="GOV-nz-em4">
                                                <rect key="frame" x="0.0" y="244" width="343" height="58.5"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RCE-k7-TXE">
                                                        <rect key="frame" x="0.0" y="0.0" width="343" height="18.5"/>
                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="14"/>
                                                        <color key="textColor" name="ContentGrey6"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="RCE-k7-TXE" secondAttribute="trailing" id="40I-iJ-9vo"/>
                                                    <constraint firstItem="RCE-k7-TXE" firstAttribute="top" secondItem="GOV-nz-em4" secondAttribute="top" id="OtU-Jf-PAe"/>
                                                    <constraint firstAttribute="bottom" secondItem="RCE-k7-TXE" secondAttribute="bottom" constant="40" id="cMm-mO-rIe"/>
                                                    <constraint firstItem="RCE-k7-TXE" firstAttribute="leading" secondItem="GOV-nz-em4" secondAttribute="leading" id="uWv-6y-uCl"/>
                                                </constraints>
                                            </view>
                                            <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="RS4-Wx-Ycx">
                                                <rect key="frame" x="0.0" y="306.5" width="343" height="184"/>
                                                <subviews>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Mlj-7D-R49" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="343" height="20"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="20" id="gJF-VR-RQu"/>
                                                        </constraints>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal" title="Button"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                <integer key="value" value="4"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </button>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3W5-7S-4yl" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="20" width="343" height="32"/>
                                                        <subviews>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="34M-QA-3Nk">
                                                                <rect key="frame" x="16" y="6" width="20" height="20"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="20" id="GST-MX-U5m"/>
                                                                    <constraint firstAttribute="height" constant="20" id="Mle-Fp-YqT"/>
                                                                </constraints>
                                                            </imageView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MCK-ue-dFs">
                                                                <rect key="frame" x="44" y="16" width="283" height="0.0"/>
                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Medium" family="Cerebri Sans Pro" pointSize="14"/>
                                                                <color key="textColor" name="ContentGrey6"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" name="Grey1"/>
                                                        <constraints>
                                                            <constraint firstAttribute="bottom" secondItem="MCK-ue-dFs" secondAttribute="bottom" constant="16" id="7yi-9i-rfF"/>
                                                            <constraint firstItem="34M-QA-3Nk" firstAttribute="leading" secondItem="3W5-7S-4yl" secondAttribute="leading" constant="16" id="LyM-cv-kRl"/>
                                                            <constraint firstItem="MCK-ue-dFs" firstAttribute="leading" secondItem="34M-QA-3Nk" secondAttribute="trailing" constant="8" id="Niw-r0-gEh"/>
                                                            <constraint firstItem="MCK-ue-dFs" firstAttribute="top" secondItem="3W5-7S-4yl" secondAttribute="top" constant="16" id="QQU-cl-1lX"/>
                                                            <constraint firstAttribute="trailing" secondItem="MCK-ue-dFs" secondAttribute="trailing" constant="16" id="WAK-C0-cqO"/>
                                                            <constraint firstItem="34M-QA-3Nk" firstAttribute="centerY" secondItem="MCK-ue-dFs" secondAttribute="centerY" id="ohk-Sd-BTd"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                <real key="value" value="16"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Z3V-Kt-d3b" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="52" width="343" height="36"/>
                                                        <subviews>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="WWe-dK-pSs">
                                                                <rect key="frame" x="16" y="16" width="24" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="24" id="9Yf-lV-vfN"/>
                                                                    <constraint firstAttribute="width" constant="24" id="QTs-zb-VYd"/>
                                                                </constraints>
                                                            </imageView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GDQ-HA-PAn">
                                                                <rect key="frame" x="56" y="16" width="271" height="0.0"/>
                                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                                <color key="textColor" name="PrimaryBlack"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hDJ-Px-fXg">
                                                                <rect key="frame" x="56" y="20" width="271" height="0.0"/>
                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                                <color key="textColor" name="Grey5"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="GDQ-HA-PAn" secondAttribute="trailing" constant="16" id="5wE-TW-Up9"/>
                                                            <constraint firstItem="WWe-dK-pSs" firstAttribute="leading" secondItem="Z3V-Kt-d3b" secondAttribute="leading" constant="16" id="9fp-QF-wLv"/>
                                                            <constraint firstItem="WWe-dK-pSs" firstAttribute="top" secondItem="GDQ-HA-PAn" secondAttribute="top" id="Chb-it-G9K"/>
                                                            <constraint firstItem="hDJ-Px-fXg" firstAttribute="top" secondItem="GDQ-HA-PAn" secondAttribute="bottom" constant="4" id="H6R-tW-3cv"/>
                                                            <constraint firstItem="hDJ-Px-fXg" firstAttribute="leading" secondItem="WWe-dK-pSs" secondAttribute="trailing" constant="16" id="ORG-Bv-nUV"/>
                                                            <constraint firstItem="GDQ-HA-PAn" firstAttribute="leading" secondItem="WWe-dK-pSs" secondAttribute="trailing" constant="16" id="TMf-fT-Jba"/>
                                                            <constraint firstAttribute="bottom" secondItem="hDJ-Px-fXg" secondAttribute="bottom" constant="16" id="XEA-1z-Ity"/>
                                                            <constraint firstAttribute="trailing" secondItem="hDJ-Px-fXg" secondAttribute="trailing" constant="16" id="cSv-lb-ngw"/>
                                                            <constraint firstItem="WWe-dK-pSs" firstAttribute="top" secondItem="Z3V-Kt-d3b" secondAttribute="top" constant="16" id="hHT-Nx-jei"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                <real key="value" value="16"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                                <real key="value" value="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                                <color key="value" name="Grey3"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sg4-Qh-dI3" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="88" width="343" height="72"/>
                                                        <subviews>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="gme-Kc-5WL">
                                                                <rect key="frame" x="18" y="24" width="16" height="16"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="16" id="7fq-2y-0mj"/>
                                                                    <constraint firstAttribute="height" constant="16" id="kxK-bu-1XZ"/>
                                                                </constraints>
                                                            </imageView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="38l-BP-GGq">
                                                                <rect key="frame" x="44" y="32" width="281" height="0.0"/>
                                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                                <color key="textColor" name="ContentPrimaryBlack"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MlI-jG-VTu">
                                                                <rect key="frame" x="16" y="48" width="311" height="0.0"/>
                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                                <color key="textColor" name="ContentGrey6"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="gme-Kc-5WL" firstAttribute="leading" secondItem="sg4-Qh-dI3" secondAttribute="leading" constant="18" id="2MQ-nZ-ciF"/>
                                                            <constraint firstItem="gme-Kc-5WL" firstAttribute="top" secondItem="sg4-Qh-dI3" secondAttribute="top" constant="24" id="2oX-Ig-bly"/>
                                                            <constraint firstAttribute="trailing" secondItem="38l-BP-GGq" secondAttribute="trailing" constant="18" id="GXe-bd-pAD"/>
                                                            <constraint firstItem="38l-BP-GGq" firstAttribute="centerY" secondItem="gme-Kc-5WL" secondAttribute="centerY" id="ITR-Ft-iwP"/>
                                                            <constraint firstItem="38l-BP-GGq" firstAttribute="leading" secondItem="gme-Kc-5WL" secondAttribute="trailing" constant="10" id="LxL-CT-yOV"/>
                                                            <constraint firstAttribute="trailing" secondItem="MlI-jG-VTu" secondAttribute="trailing" constant="16" id="M4f-9o-dSy"/>
                                                            <constraint firstAttribute="bottom" secondItem="MlI-jG-VTu" secondAttribute="bottom" constant="24" id="Tzy-vM-MX7"/>
                                                            <constraint firstItem="MlI-jG-VTu" firstAttribute="leading" secondItem="sg4-Qh-dI3" secondAttribute="leading" constant="16" id="XiQ-6Y-WPD"/>
                                                            <constraint firstItem="MlI-jG-VTu" firstAttribute="top" secondItem="38l-BP-GGq" secondAttribute="bottom" constant="16" id="lkc-Wh-Cio"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                <real key="value" value="16"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                                <real key="value" value="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                                <color key="value" name="Grey3"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dg1-xh-6tK">
                                                        <rect key="frame" x="0.0" y="160" width="343" height="24"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="24" id="QaX-nf-Rao"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="W0b-Ek-RfY">
                                                <rect key="frame" x="0.0" y="310.5" width="343" height="149.5"/>
                                            </stackView>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="feX-Ca-1Lc" secondAttribute="trailing" constant="16" id="pmT-ks-zrl"/>
                                    <constraint firstItem="feX-Ca-1Lc" firstAttribute="top" secondItem="l6r-za-BgI" secondAttribute="top" constant="24" id="vvG-K7-brL"/>
                                    <constraint firstItem="feX-Ca-1Lc" firstAttribute="leading" secondItem="l6r-za-BgI" secondAttribute="leading" constant="16" id="wM5-Gw-E45"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="XUZ-1v-fbf"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="XUZ-1v-fbf" firstAttribute="trailing" secondItem="l6r-za-BgI" secondAttribute="trailing" id="An7-e8-rjB"/>
                            <constraint firstItem="XUZ-1v-fbf" firstAttribute="bottom" secondItem="feX-Ca-1Lc" secondAttribute="bottom" constant="16" id="QjS-Q1-EkG"/>
                            <constraint firstItem="l6r-za-BgI" firstAttribute="height" secondItem="avZ-sy-OE2" secondAttribute="height" id="RpH-WJ-YpY"/>
                            <constraint firstItem="l6r-za-BgI" firstAttribute="top" secondItem="avZ-sy-OE2" secondAttribute="bottom" constant="-500" id="fLQ-1h-0DY"/>
                            <constraint firstItem="l6r-za-BgI" firstAttribute="leading" secondItem="XUZ-1v-fbf" secondAttribute="leading" id="qh2-e1-a7Q"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isDimmerViewTappable" value="NO"/>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="additionalCenterButton" destination="Mlj-7D-R49" id="nVN-ux-Mmy"/>
                        <outlet property="additionalInfoContainerView" destination="3W5-7S-4yl" id="i30-TZ-mZe"/>
                        <outlet property="additionalInfoCustomIconImageView" destination="WWe-dK-pSs" id="I31-L7-ATQ"/>
                        <outlet property="additionalInfoCustomView" destination="Z3V-Kt-d3b" id="J5J-uN-0XY"/>
                        <outlet property="additionalInfoIconImageView" destination="34M-QA-3Nk" id="VbM-Jl-ayM"/>
                        <outlet property="additionalInfoMessageLabel" destination="MCK-ue-dFs" id="4co-3q-mcd"/>
                        <outlet property="additionalInfoStackView" destination="RS4-Wx-Ycx" id="pTL-U0-DtY"/>
                        <outlet property="additionalInfoSubtitleLabel" destination="hDJ-Px-fXg" id="fEP-kl-XkF"/>
                        <outlet property="additionalInfoTextIconImageView" destination="gme-Kc-5WL" id="5Ne-kQ-Jvq"/>
                        <outlet property="additionalInfoTextTextLabel" destination="MlI-jG-VTu" id="Von-29-WH3"/>
                        <outlet property="additionalInfoTextTitleLabel" destination="38l-BP-GGq" id="wBl-CM-jaX"/>
                        <outlet property="additionalInfoTextView" destination="sg4-Qh-dI3" id="inG-rN-S4F"/>
                        <outlet property="additionalInfoTitleLabel" destination="GDQ-HA-PAn" id="08L-vD-edF"/>
                        <outlet property="buttonStack" destination="W0b-Ek-RfY" id="UDd-sK-bme"/>
                        <outlet property="contentView" destination="l6r-za-BgI" id="Cft-l6-3HU"/>
                        <outlet property="contentViewAnchorToBottomSafeAreaConstraint" destination="QjS-Q1-EkG" id="6bK-xe-BMp"/>
                        <outlet property="contentViewTopConstraint" destination="fLQ-1h-0DY" id="XFz-hZ-gEs"/>
                        <outlet property="image" destination="UC2-kS-ei3" id="c4t-oS-NxK"/>
                        <outlet property="mainStackView" destination="feX-Ca-1Lc" id="Omw-QY-3kl"/>
                        <outlet property="messageBottomConstraint" destination="cMm-mO-rIe" id="QhX-6p-J4q"/>
                        <outlet property="messageLabel" destination="RCE-k7-TXE" id="sz9-gi-hlw"/>
                        <outlet property="messageLabelContainer" destination="GOV-nz-em4" id="TTW-28-bmr"/>
                        <outlet property="titleLabel" destination="kgo-G8-DdE" id="qIZ-K8-qNE"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="sCc-Pp-MqP" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="257.97101449275362" y="3187.5000000000005"/>
        </scene>
        <!--Maya Alert Bottom SheetV2 View Controller-->
        <scene sceneID="NCq-wY-qBc">
            <objects>
                <viewController storyboardIdentifier="MayaAlertBottomSheetV2ViewController" id="gnp-QN-EjO" customClass="MayaAlertBottomSheetV2ViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="bf9-ZY-qEm">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="U9z-TP-Eov">
                                <rect key="frame" x="0.0" y="267.5" width="375" height="399.5"/>
                                <subviews>
                                    <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3b4-43-egp">
                                        <rect key="frame" x="16" y="24" width="343" height="295.5"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="Huf-bF-UpV">
                                                <rect key="frame" x="0.0" y="0.0" width="343" height="295.5"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="kycUpgradeRocket" translatesAutoresizingMaskIntoConstraints="NO" id="E3h-jx-Eh0">
                                                        <rect key="frame" x="0.0" y="0.0" width="343" height="200"/>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="20" translatesAutoresizingMaskIntoConstraints="NO" id="Zjz-4F-vmd">
                                                        <rect key="frame" x="0.0" y="208" width="343" height="29.5"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tKd-YT-7mC">
                                                        <rect key="frame" x="0.0" y="245.5" width="343" height="50"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bdU-wn-3gP">
                                                                <rect key="frame" x="0.0" y="0.0" width="343" height="42"/>
                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="14"/>
                                                                <color key="textColor" name="ContentGrey6"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="bdU-wn-3gP" firstAttribute="top" secondItem="tKd-YT-7mC" secondAttribute="top" id="8ay-tY-W8G"/>
                                                            <constraint firstItem="bdU-wn-3gP" firstAttribute="leading" secondItem="tKd-YT-7mC" secondAttribute="leading" id="Hpb-Gl-CW4"/>
                                                            <constraint firstAttribute="bottom" secondItem="bdU-wn-3gP" secondAttribute="bottom" constant="8" id="I2P-T5-y4S"/>
                                                            <constraint firstAttribute="trailing" secondItem="bdU-wn-3gP" secondAttribute="trailing" id="Ouh-Jh-AIl"/>
                                                        </constraints>
                                                    </view>
                                                    <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" translatesAutoresizingMaskIntoConstraints="NO" id="yCS-1I-489">
                                                        <rect key="frame" x="0.0" y="295.5" width="343" height="184"/>
                                                        <subviews>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="r5V-PT-0fX" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="343" height="20"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="iK4-fL-njT"/>
                                                                </constraints>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <state key="normal" title="Button"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                        <integer key="value" value="4"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </button>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Pb3-xH-pR3" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="20" width="343" height="32"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="6xc-tl-aIV">
                                                                        <rect key="frame" x="16" y="6" width="20" height="20"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="20" id="2JE-yN-Sad"/>
                                                                            <constraint firstAttribute="width" constant="20" id="NZJ-yR-ks8"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ulo-bA-0VD">
                                                                        <rect key="frame" x="44" y="16" width="283" height="0.0"/>
                                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Medium" family="Cerebri Sans Pro" pointSize="14"/>
                                                                        <color key="textColor" name="ContentGrey6"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" name="Grey1"/>
                                                                <constraints>
                                                                    <constraint firstItem="6xc-tl-aIV" firstAttribute="centerY" secondItem="Ulo-bA-0VD" secondAttribute="centerY" id="DMm-D5-dkl"/>
                                                                    <constraint firstItem="Ulo-bA-0VD" firstAttribute="top" secondItem="Pb3-xH-pR3" secondAttribute="top" constant="16" id="IUP-H9-dLG"/>
                                                                    <constraint firstAttribute="trailing" secondItem="Ulo-bA-0VD" secondAttribute="trailing" constant="16" id="Uzf-JS-OLq"/>
                                                                    <constraint firstItem="Ulo-bA-0VD" firstAttribute="leading" secondItem="6xc-tl-aIV" secondAttribute="trailing" constant="8" id="cLH-NQ-hLU"/>
                                                                    <constraint firstAttribute="bottom" secondItem="Ulo-bA-0VD" secondAttribute="bottom" constant="16" id="pIV-aU-ghC"/>
                                                                    <constraint firstItem="6xc-tl-aIV" firstAttribute="leading" secondItem="Pb3-xH-pR3" secondAttribute="leading" constant="16" id="w1m-j4-dtl"/>
                                                                </constraints>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                        <real key="value" value="16"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Bij-56-7ml" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="52" width="343" height="36"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="g92-qf-lRE">
                                                                        <rect key="frame" x="16" y="16" width="24" height="24"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="24" id="hZf-Bi-0AO"/>
                                                                            <constraint firstAttribute="width" constant="24" id="wP6-os-3FO"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="wun-RN-GgL">
                                                                        <rect key="frame" x="56" y="16" width="271" height="0.0"/>
                                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                                        <color key="textColor" name="PrimaryBlack"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ykM-xO-gWG">
                                                                        <rect key="frame" x="56" y="20" width="271" height="0.0"/>
                                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                                        <color key="textColor" name="Grey5"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <constraints>
                                                                    <constraint firstItem="ykM-xO-gWG" firstAttribute="top" secondItem="wun-RN-GgL" secondAttribute="bottom" constant="4" id="FiR-y5-oDY"/>
                                                                    <constraint firstAttribute="bottom" secondItem="ykM-xO-gWG" secondAttribute="bottom" constant="16" id="bpL-sb-uPI"/>
                                                                    <constraint firstItem="g92-qf-lRE" firstAttribute="leading" secondItem="Bij-56-7ml" secondAttribute="leading" constant="16" id="hwZ-n7-fKp"/>
                                                                    <constraint firstItem="wun-RN-GgL" firstAttribute="leading" secondItem="g92-qf-lRE" secondAttribute="trailing" constant="16" id="mQt-gb-Lal"/>
                                                                    <constraint firstItem="ykM-xO-gWG" firstAttribute="leading" secondItem="g92-qf-lRE" secondAttribute="trailing" constant="16" id="sjG-oi-uJy"/>
                                                                    <constraint firstAttribute="trailing" secondItem="ykM-xO-gWG" secondAttribute="trailing" constant="16" id="uq8-0X-I54"/>
                                                                    <constraint firstItem="g92-qf-lRE" firstAttribute="top" secondItem="Bij-56-7ml" secondAttribute="top" constant="16" id="wg2-q3-h3n"/>
                                                                    <constraint firstItem="g92-qf-lRE" firstAttribute="top" secondItem="wun-RN-GgL" secondAttribute="top" id="x6f-ap-kSW"/>
                                                                    <constraint firstAttribute="trailing" secondItem="wun-RN-GgL" secondAttribute="trailing" constant="16" id="zrf-fb-4Ot"/>
                                                                </constraints>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                        <real key="value" value="16"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                                        <real key="value" value="1"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                                        <color key="value" name="Grey3"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Uov-Yv-xem" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="88" width="343" height="72"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="cip-Z2-RVm">
                                                                        <rect key="frame" x="18" y="24" width="16" height="16"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="16" id="EqJ-8E-2zV"/>
                                                                            <constraint firstAttribute="height" constant="16" id="oso-xW-Jbr"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pFC-1r-XD9">
                                                                        <rect key="frame" x="44" y="32" width="281" height="0.0"/>
                                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="f04-Bd-p3O">
                                                                        <rect key="frame" x="16" y="48" width="311" height="0.0"/>
                                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                                        <color key="textColor" name="ContentGrey6"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <constraints>
                                                                    <constraint firstItem="pFC-1r-XD9" firstAttribute="centerY" secondItem="cip-Z2-RVm" secondAttribute="centerY" id="2g6-ms-Ayp"/>
                                                                    <constraint firstItem="pFC-1r-XD9" firstAttribute="leading" secondItem="cip-Z2-RVm" secondAttribute="trailing" constant="10" id="Aac-r0-CEm"/>
                                                                    <constraint firstItem="cip-Z2-RVm" firstAttribute="leading" secondItem="Uov-Yv-xem" secondAttribute="leading" constant="18" id="DUA-8C-BPP"/>
                                                                    <constraint firstAttribute="trailing" secondItem="f04-Bd-p3O" secondAttribute="trailing" constant="16" id="mQJ-sa-Hjl"/>
                                                                    <constraint firstItem="f04-Bd-p3O" firstAttribute="leading" secondItem="Uov-Yv-xem" secondAttribute="leading" constant="16" id="nd4-hd-J0n"/>
                                                                    <constraint firstAttribute="bottom" secondItem="f04-Bd-p3O" secondAttribute="bottom" constant="24" id="pQO-0g-dJk"/>
                                                                    <constraint firstItem="cip-Z2-RVm" firstAttribute="top" secondItem="Uov-Yv-xem" secondAttribute="top" constant="24" id="pe5-ye-Q0K"/>
                                                                    <constraint firstAttribute="trailing" secondItem="pFC-1r-XD9" secondAttribute="trailing" constant="18" id="tX2-Pt-xUI"/>
                                                                    <constraint firstItem="f04-Bd-p3O" firstAttribute="top" secondItem="pFC-1r-XD9" secondAttribute="bottom" constant="16" id="zFm-5R-HlS"/>
                                                                </constraints>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                        <real key="value" value="16"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                                        <real key="value" value="1"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                                        <color key="value" name="Grey3"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Sbb-qg-4bw">
                                                                <rect key="frame" x="0.0" y="160" width="343" height="24"/>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="24" id="IWu-VV-0va"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="Huf-bF-UpV" secondAttribute="bottom" id="A2O-HI-84o"/>
                                            <constraint firstItem="Huf-bF-UpV" firstAttribute="top" secondItem="3b4-43-egp" secondAttribute="top" id="EJS-4j-8He"/>
                                            <constraint firstItem="Huf-bF-UpV" firstAttribute="width" secondItem="3b4-43-egp" secondAttribute="width" id="LGc-eH-tlV"/>
                                            <constraint firstItem="Huf-bF-UpV" firstAttribute="leading" secondItem="3b4-43-egp" secondAttribute="leading" id="YR3-zr-vBR"/>
                                            <constraint firstItem="Huf-bF-UpV" firstAttribute="height" secondItem="3b4-43-egp" secondAttribute="height" id="Z44-Xf-8ep"/>
                                            <constraint firstAttribute="trailing" secondItem="Huf-bF-UpV" secondAttribute="trailing" id="sDR-Fo-Zbz"/>
                                        </constraints>
                                    </scrollView>
                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="rBg-tK-FxA">
                                        <rect key="frame" x="16" y="319.5" width="343" height="56"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="df5-id-Deq"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="rBg-tK-FxA" firstAttribute="top" secondItem="3b4-43-egp" secondAttribute="bottom" id="NJA-DK-wkU"/>
                                    <constraint firstItem="3b4-43-egp" firstAttribute="top" secondItem="U9z-TP-Eov" secondAttribute="top" constant="24" id="bFb-iO-ceO"/>
                                    <constraint firstAttribute="trailing" secondItem="3b4-43-egp" secondAttribute="trailing" constant="16" id="dYB-Z9-aUx"/>
                                    <constraint firstItem="3b4-43-egp" firstAttribute="leading" secondItem="U9z-TP-Eov" secondAttribute="leading" constant="16" id="kEZ-9H-T5S"/>
                                    <constraint firstItem="rBg-tK-FxA" firstAttribute="leading" secondItem="U9z-TP-Eov" secondAttribute="leading" constant="16" id="l6k-JZ-JEz"/>
                                    <constraint firstAttribute="trailing" secondItem="rBg-tK-FxA" secondAttribute="trailing" constant="16" id="mBv-gd-D0q"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="XyL-Su-nAP"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="XyL-Su-nAP" firstAttribute="trailing" secondItem="U9z-TP-Eov" secondAttribute="trailing" id="8ku-j2-ERf"/>
                            <constraint firstItem="XyL-Su-nAP" firstAttribute="bottom" secondItem="rBg-tK-FxA" secondAttribute="bottom" constant="24" id="G0p-vY-kjh"/>
                            <constraint firstItem="U9z-TP-Eov" firstAttribute="top" relation="greaterThanOrEqual" secondItem="XyL-Su-nAP" secondAttribute="top" id="J28-Mn-6rS"/>
                            <constraint firstAttribute="bottom" secondItem="U9z-TP-Eov" secondAttribute="bottom" id="YlM-60-Rc6"/>
                            <constraint firstItem="U9z-TP-Eov" firstAttribute="leading" secondItem="XyL-Su-nAP" secondAttribute="leading" id="Z86-fq-egr"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isDimmerViewTappable" value="NO"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="additionalCenterButton" destination="r5V-PT-0fX" id="iKh-DZ-3aU"/>
                        <outlet property="additionalInfoContainerView" destination="Pb3-xH-pR3" id="tz6-vh-pw8"/>
                        <outlet property="additionalInfoCustomIconImageView" destination="g92-qf-lRE" id="AOQ-RX-7yX"/>
                        <outlet property="additionalInfoCustomView" destination="Bij-56-7ml" id="474-SU-Eig"/>
                        <outlet property="additionalInfoIconImageView" destination="6xc-tl-aIV" id="VKP-bT-mRF"/>
                        <outlet property="additionalInfoMessageLabel" destination="Ulo-bA-0VD" id="dxl-yB-Rup"/>
                        <outlet property="additionalInfoStackView" destination="yCS-1I-489" id="rvX-oQ-90d"/>
                        <outlet property="additionalInfoSubtitleLabel" destination="ykM-xO-gWG" id="7jp-CN-oYu"/>
                        <outlet property="additionalInfoTextIconImageView" destination="cip-Z2-RVm" id="0vx-Wd-BOm"/>
                        <outlet property="additionalInfoTextTextLabel" destination="f04-Bd-p3O" id="Dse-li-AMw"/>
                        <outlet property="additionalInfoTextTitleLabel" destination="pFC-1r-XD9" id="yK6-LL-beN"/>
                        <outlet property="additionalInfoTextView" destination="Uov-Yv-xem" id="r6l-i4-P05"/>
                        <outlet property="additionalInfoTitleLabel" destination="wun-RN-GgL" id="24n-vq-tTv"/>
                        <outlet property="buttonStack" destination="rBg-tK-FxA" id="QeD-br-xeX"/>
                        <outlet property="buttonStackTopAnchor" destination="NJA-DK-wkU" id="LI3-eC-3WU"/>
                        <outlet property="contentView" destination="U9z-TP-Eov" id="smQ-D6-h7c"/>
                        <outlet property="image" destination="E3h-jx-Eh0" id="uqh-0r-7K4"/>
                        <outlet property="mainStackView" destination="Huf-bF-UpV" id="muC-e8-kd7"/>
                        <outlet property="messageLabel" destination="bdU-wn-3gP" id="b1q-z8-I0S"/>
                        <outlet property="messageLabelContainer" destination="tKd-YT-7mC" id="wTP-cV-9fN"/>
                        <outlet property="titleLabel" destination="Zjz-4F-vmd" id="vNE-Dg-4Q1"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="QUp-ga-jZl" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="259" y="3885"/>
        </scene>
        <!--Maya About Verified Seller View Controller-->
        <scene sceneID="JZJ-PM-xdO">
            <objects>
                <viewController storyboardIdentifier="MayaAboutVerifiedSellerViewController" id="UhR-Lc-Vf9" customClass="MayaAboutVerifiedSellerViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="eK2-nC-hms">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8Qk-gw-SWm">
                                <rect key="frame" x="0.0" y="282" width="375" height="385"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="xDg-bS-JKQ">
                                        <rect key="frame" x="24" y="40" width="327" height="20"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconInfo" translatesAutoresizingMaskIntoConstraints="NO" id="MMq-yZ-gGn">
                                                <rect key="frame" x="0.0" y="0.0" width="20" height="20"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" secondItem="MMq-yZ-gGn" secondAttribute="height" id="4u4-OT-tZ5"/>
                                                    <constraint firstAttribute="height" constant="20" id="NlS-EB-aM5"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="About Verified Sellers" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Dcs-Eg-GmN">
                                                <rect key="frame" x="28" y="0.5" width="299" height="19.5"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                <color key="textColor" name="PrimaryBlack"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                    <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" editable="NO" usesAttributedText="YES" selectable="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pLl-NS-pDN">
                                        <rect key="frame" x="24" y="68" width="327" height="200"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <attributedString key="attributedText">
                                            <fragment>
                                                <string key="content">Verified Sellers are legitimate small business owners that accept payments using Maya. As Verified Sellers, they get a badge that signals credibility and trustworthiness, and enjoy higher account limits and access to exclusive deals.
You become a Verified Seller if you:
∙  Have an upgraded Maya account
∙  Consistently receive payments in your PayMaya account
∙  Maintain a good track record with no customer complaints</string>
                                                <attributes>
                                                    <color key="NSColor" name="Grey6"/>
                                                    <font key="NSFont" metaFont="system" size="14"/>
                                                    <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                </attributes>
                                            </fragment>
                                        </attributedString>
                                        <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                    </textView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="8JA-UJ-JtO" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="24" y="292" width="327" height="56"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="Iwg-4M-rkT"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Got it"/>
                                        <connections>
                                            <action selector="didTapGotIt:" destination="UhR-Lc-Vf9" eventType="touchUpInside" id="MRe-3d-1OR"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="8JA-UJ-JtO" secondAttribute="trailing" constant="24" id="0wO-aQ-di7"/>
                                    <constraint firstItem="8JA-UJ-JtO" firstAttribute="top" secondItem="pLl-NS-pDN" secondAttribute="bottom" constant="24" id="8ue-vv-jN2"/>
                                    <constraint firstItem="xDg-bS-JKQ" firstAttribute="leading" secondItem="8Qk-gw-SWm" secondAttribute="leading" constant="24" id="9vr-nP-PN5"/>
                                    <constraint firstItem="8JA-UJ-JtO" firstAttribute="leading" secondItem="8Qk-gw-SWm" secondAttribute="leading" constant="24" id="O8X-44-J0T"/>
                                    <constraint firstItem="pLl-NS-pDN" firstAttribute="top" secondItem="xDg-bS-JKQ" secondAttribute="bottom" constant="8" id="QnO-Ka-WZX"/>
                                    <constraint firstItem="xDg-bS-JKQ" firstAttribute="top" secondItem="8Qk-gw-SWm" secondAttribute="top" constant="40" id="cd1-Lt-hfC"/>
                                    <constraint firstItem="pLl-NS-pDN" firstAttribute="leading" secondItem="8Qk-gw-SWm" secondAttribute="leading" constant="24" id="gTh-pW-2j3"/>
                                    <constraint firstAttribute="trailing" secondItem="xDg-bS-JKQ" secondAttribute="trailing" constant="24" id="sFm-KM-vyQ"/>
                                    <constraint firstAttribute="trailing" secondItem="pLl-NS-pDN" secondAttribute="trailing" constant="24" id="uh5-yP-RUR"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="gXa-Bz-B9o"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="8Qk-gw-SWm" secondAttribute="bottom" id="5Ml-ot-L1p"/>
                            <constraint firstItem="8Qk-gw-SWm" firstAttribute="top" relation="greaterThanOrEqual" secondItem="gXa-Bz-B9o" secondAttribute="top" id="Erz-Xr-mTo"/>
                            <constraint firstItem="8Qk-gw-SWm" firstAttribute="leading" secondItem="gXa-Bz-B9o" secondAttribute="leading" id="Kof-3a-AAQ"/>
                            <constraint firstItem="gXa-Bz-B9o" firstAttribute="trailing" secondItem="8Qk-gw-SWm" secondAttribute="trailing" id="W5B-ar-xo4"/>
                            <constraint firstItem="gXa-Bz-B9o" firstAttribute="bottom" secondItem="8JA-UJ-JtO" secondAttribute="bottom" constant="37" id="k1v-Ia-U4g"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="aboutTextView" destination="pLl-NS-pDN" id="5Wt-80-Mzz"/>
                        <outlet property="contentView" destination="8Qk-gw-SWm" id="55p-wk-Dwa"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="DZH-cn-KCb" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="993.75" y="3186.9718309859154"/>
        </scene>
        <!--Maya Picker View Controller-->
        <scene sceneID="kVm-uD-Ynb">
            <objects>
                <viewController storyboardIdentifier="MayaPickerViewController" modalPresentationStyle="overFullScreen" id="1U8-NK-oEg" customClass="MayaPickerViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="DDe-ZI-5g9">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jUk-7I-F5H" userLabel="Content View">
                                <rect key="frame" x="0.0" y="221" width="375" height="446"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rKJ-i6-mWf">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="446"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TFn-i5-hfU">
                                                <rect key="frame" x="20" y="20" width="335" height="22"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="18"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="5ka-MB-utR">
                                                <rect key="frame" x="21" y="58" width="334" height="372"/>
                                                <subviews>
                                                    <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="grouped" separatorStyle="none" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="18" estimatedSectionHeaderHeight="-1" sectionFooterHeight="18" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="Zow-Sq-syh" customClass="ContentSizedTableView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="334" height="300"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="300" placeholder="YES" id="t9P-hz-6gN"/>
                                                        </constraints>
                                                        <inset key="scrollIndicatorInsets" minX="0.0" minY="0.0" maxX="0.0" maxY="-1"/>
                                                        <connections>
                                                            <outlet property="dataSource" destination="1U8-NK-oEg" id="olt-a6-B5y"/>
                                                            <outlet property="delegate" destination="1U8-NK-oEg" id="ZBy-Er-bC3"/>
                                                        </connections>
                                                    </tableView>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="fbe-lR-059" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="316" width="334" height="56"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="56" id="Iqu-80-VsW"/>
                                                        </constraints>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal" title="Cancel"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                <integer key="value" value="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                                <integer key="value" value="0"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="didTapBack:" destination="1U8-NK-oEg" eventType="touchUpInside" id="IeI-4y-THb"/>
                                                            <action selector="didTapCancel" destination="1U8-NK-oEg" eventType="touchUpInside" id="Z7Y-iJ-Zy8"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="TFn-i5-hfU" firstAttribute="leading" secondItem="rKJ-i6-mWf" secondAttribute="leading" constant="20" id="0j1-Ll-oCw"/>
                                            <constraint firstItem="TFn-i5-hfU" firstAttribute="top" secondItem="rKJ-i6-mWf" secondAttribute="top" constant="20" id="7Hr-YC-qvE"/>
                                            <constraint firstItem="5ka-MB-utR" firstAttribute="leading" secondItem="rKJ-i6-mWf" secondAttribute="leading" constant="21" id="9cc-Sk-QHI"/>
                                            <constraint firstAttribute="trailing" secondItem="5ka-MB-utR" secondAttribute="trailing" constant="20" id="Ake-C0-Xw0"/>
                                            <constraint firstItem="5ka-MB-utR" firstAttribute="top" secondItem="TFn-i5-hfU" secondAttribute="bottom" constant="16" id="Ob4-xM-uvE"/>
                                            <constraint firstAttribute="bottom" secondItem="5ka-MB-utR" secondAttribute="bottom" constant="16" id="oY1-x3-GBg"/>
                                            <constraint firstAttribute="trailing" secondItem="TFn-i5-hfU" secondAttribute="trailing" constant="20" id="pl4-8h-mZ2"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" name="PrimaryWhite"/>
                                <constraints>
                                    <constraint firstItem="rKJ-i6-mWf" firstAttribute="leading" secondItem="jUk-7I-F5H" secondAttribute="leading" id="aPv-JZ-Op6"/>
                                    <constraint firstAttribute="trailing" secondItem="rKJ-i6-mWf" secondAttribute="trailing" id="fB7-Rp-UHg"/>
                                    <constraint firstItem="rKJ-i6-mWf" firstAttribute="top" secondItem="jUk-7I-F5H" secondAttribute="top" id="nsv-PC-3Bm"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Toj-ki-CLP"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="jUk-7I-F5H" firstAttribute="leading" secondItem="Toj-ki-CLP" secondAttribute="leading" id="CsE-ZP-w8X"/>
                            <constraint firstItem="Toj-ki-CLP" firstAttribute="trailing" secondItem="jUk-7I-F5H" secondAttribute="trailing" id="UDk-Bx-CCL"/>
                            <constraint firstItem="rKJ-i6-mWf" firstAttribute="bottom" secondItem="Toj-ki-CLP" secondAttribute="bottom" id="YoU-dr-zmR"/>
                            <constraint firstAttribute="bottom" secondItem="jUk-7I-F5H" secondAttribute="bottom" id="Z9K-zL-cH9"/>
                            <constraint firstItem="jUk-7I-F5H" firstAttribute="top" relation="greaterThanOrEqual" secondItem="Toj-ki-CLP" secondAttribute="top" id="iGo-WF-gN7"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="cancelButton" destination="fbe-lR-059" id="Rm6-uQ-nln"/>
                        <outlet property="contentView" destination="jUk-7I-F5H" id="krk-X5-JJA"/>
                        <outlet property="pickerTitleLabel" destination="TFn-i5-hfU" id="6Fj-43-bbr"/>
                        <outlet property="tableView" destination="Zow-Sq-syh" id="HgA-kD-JG8"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="p8j-qg-cel" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="272.46376811594206" y="2497.0108695652175"/>
        </scene>
        <!--Maya Bottom Sheet Wk Web View View Controller-->
        <scene sceneID="dx4-qf-g6d">
            <objects>
                <viewController storyboardIdentifier="MayaBottomSheetWkWebViewViewController" definesPresentationContext="YES" providesPresentationContextTransitionStyle="YES" modalPresentationStyle="overFullScreen" id="nYP-zb-rO0" customClass="MayaBottomSheetWkWebViewViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="H52-kc-W1B">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AJY-93-oUB">
                                <rect key="frame" x="0.0" y="100" width="375" height="567"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3J4-Vm-vtl">
                                        <rect key="frame" x="24" y="24" width="327" height="0.0"/>
                                        <accessibility key="accessibilityConfiguration" identifier="pmios_bottom_sheet_webview_title_label"/>
                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ucJ-GL-522">
                                        <rect key="frame" x="0.0" y="477" width="375" height="90"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Rkl-k0-oUE" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="10" width="327" height="56"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_bottom_sheet_webview_done_button"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="Tov-jM-uiI"/>
                                                </constraints>
                                                <state key="normal" title="Done"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="didTapBack:" destination="nYP-zb-rO0" eventType="touchUpInside" id="IRS-Ed-nLY"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="Rkl-k0-oUE" secondAttribute="bottom" constant="24" id="3os-kc-s0q"/>
                                            <constraint firstAttribute="trailing" secondItem="Rkl-k0-oUE" secondAttribute="trailing" constant="24" id="QJD-IF-r31"/>
                                            <constraint firstItem="Rkl-k0-oUE" firstAttribute="leading" secondItem="ucJ-GL-522" secondAttribute="leading" constant="24" id="tpQ-7m-HOb"/>
                                            <constraint firstItem="Rkl-k0-oUE" firstAttribute="top" secondItem="ucJ-GL-522" secondAttribute="top" constant="10" id="yN3-G4-kYk"/>
                                        </constraints>
                                    </view>
                                    <wkWebView contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vnR-px-NJe">
                                        <rect key="frame" x="24" y="56" width="327" height="417"/>
                                        <color key="backgroundColor" name="Grey1"/>
                                        <wkWebViewConfiguration key="configuration">
                                            <audiovisualMediaTypes key="mediaTypesRequiringUserActionForPlayback" none="YES"/>
                                            <wkPreferences key="preferences"/>
                                        </wkWebViewConfiguration>
                                    </wkWebView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="vnR-px-NJe" firstAttribute="centerX" secondItem="AJY-93-oUB" secondAttribute="centerX" id="57m-Wm-o65"/>
                                    <constraint firstItem="3J4-Vm-vtl" firstAttribute="leading" secondItem="AJY-93-oUB" secondAttribute="leading" constant="24" id="6Lc-hq-t6f"/>
                                    <constraint firstItem="ucJ-GL-522" firstAttribute="leading" secondItem="AJY-93-oUB" secondAttribute="leading" id="BQs-m2-oVY"/>
                                    <constraint firstItem="3J4-Vm-vtl" firstAttribute="top" secondItem="AJY-93-oUB" secondAttribute="top" constant="24" id="J8K-yW-pfI"/>
                                    <constraint firstAttribute="trailing" secondItem="ucJ-GL-522" secondAttribute="trailing" id="TZo-Bl-Qrc"/>
                                    <constraint firstItem="ucJ-GL-522" firstAttribute="top" secondItem="vnR-px-NJe" secondAttribute="bottom" constant="4" id="dRs-pa-uS7"/>
                                    <constraint firstItem="vnR-px-NJe" firstAttribute="top" secondItem="3J4-Vm-vtl" secondAttribute="bottom" constant="32" id="jMO-dg-IdW"/>
                                    <constraint firstAttribute="trailing" secondItem="3J4-Vm-vtl" secondAttribute="trailing" constant="24" id="qT7-az-3IA"/>
                                    <constraint firstAttribute="bottom" secondItem="ucJ-GL-522" secondAttribute="bottom" id="yVj-op-Tqk"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="hcr-Rr-d6O"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="vnR-px-NJe" firstAttribute="width" secondItem="H52-kc-W1B" secondAttribute="width" multiplier="0.872" id="1z0-i3-Kqj"/>
                            <constraint firstItem="AJY-93-oUB" firstAttribute="leading" secondItem="H52-kc-W1B" secondAttribute="leading" id="I7i-hI-EWl"/>
                            <constraint firstItem="AJY-93-oUB" firstAttribute="top" relation="greaterThanOrEqual" secondItem="hcr-Rr-d6O" secondAttribute="top" id="OEr-aX-8bl"/>
                            <constraint firstItem="hcr-Rr-d6O" firstAttribute="trailing" secondItem="AJY-93-oUB" secondAttribute="trailing" id="dR0-gE-7U3"/>
                            <constraint firstItem="vnR-px-NJe" firstAttribute="height" secondItem="H52-kc-W1B" secondAttribute="height" multiplier="500:800" id="n1N-F3-fcM"/>
                            <constraint firstAttribute="bottom" secondItem="AJY-93-oUB" secondAttribute="bottom" id="pwR-fy-Z9J"/>
                        </constraints>
                    </view>
                    <nil key="simulatedTopBarMetrics"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isDimmerViewTappable" value="NO"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="contentView" destination="AJY-93-oUB" id="hqX-bh-S6I"/>
                        <outlet property="titleLabel" destination="3J4-Vm-vtl" id="1Ej-Jq-OQJ"/>
                        <outlet property="webView" destination="vnR-px-NJe" id="JYY-lg-Mm1"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="9vN-Wt-Kmf" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-502" y="-242"/>
        </scene>
        <!--Maya Service Maintenance View Controller-->
        <scene sceneID="WSn-YD-two">
            <objects>
                <viewController storyboardIdentifier="MayaServiceMaintenanceViewController" id="A6j-yX-GIx" customClass="MayaServiceMaintenanceViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="AqV-oR-gao">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="603"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5Wk-VV-zy2">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="603"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OkE-qf-0Cc">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="603"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="CgP-0M-3YL">
                                                <rect key="frame" x="48" y="0.0" width="279" height="603"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="enX-52-ml8" userLabel="Top Spacer View">
                                                        <rect key="frame" x="0.0" y="0.0" width="279" height="61.5"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="24" id="3uM-5y-hCw"/>
                                                        </constraints>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" axis="vertical" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="eNg-HP-w9f">
                                                        <rect key="frame" x="0.0" y="61.5" width="279" height="465.5"/>
                                                        <subviews>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" image="imageMaintenance" translatesAutoresizingMaskIntoConstraints="NO" id="96O-jp-B6G">
                                                                <rect key="frame" x="0.0" y="0.0" width="279" height="279"/>
                                                                <accessibility key="accessibilityConfiguration">
                                                                    <bool key="isElement" value="YES"/>
                                                                </accessibility>
                                                            </imageView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="B5W-jL-Aq7">
                                                                <rect key="frame" x="0.0" y="303" width="279" height="82.5"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" text="We’ll be back shortly." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KuF-Qt-4uw">
                                                                        <rect key="frame" x="0.0" y="0.0" width="279" height="29.5"/>
                                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" text="We’re working our magic to make things better for you. Come back again in a while." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="L4M-uo-E1F">
                                                                        <rect key="frame" x="0.0" y="45.5" width="279" height="37"/>
                                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                                        <color key="textColor" name="ContentGrey6"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="DUQ-ZQ-kW4" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="409.5" width="279" height="56"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="56" id="iTi-Ws-4uy"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <state key="normal" title="Button"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                        <integer key="value" value="0"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                                        <integer key="value" value="0"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                                <connections>
                                                                    <action selector="didTapActionButton:" destination="A6j-yX-GIx" eventType="touchUpInside" id="sv0-1t-1MN"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="B5W-jL-Aq7" firstAttribute="leading" secondItem="eNg-HP-w9f" secondAttribute="leading" id="fa1-PC-vkw"/>
                                                            <constraint firstAttribute="trailing" secondItem="B5W-jL-Aq7" secondAttribute="trailing" id="ns6-X7-VUo"/>
                                                        </constraints>
                                                    </stackView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="KFc-4W-2Vc" userLabel="Bottom Spacer View">
                                                        <rect key="frame" x="0.0" y="527" width="279" height="76"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="KFc-4W-2Vc" firstAttribute="height" secondItem="enX-52-ml8" secondAttribute="height" constant="15" id="LP6-q1-sfo"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="CgP-0M-3YL" firstAttribute="top" secondItem="OkE-qf-0Cc" secondAttribute="top" id="EC4-K1-xX3"/>
                                            <constraint firstAttribute="bottom" secondItem="CgP-0M-3YL" secondAttribute="bottom" id="K7Y-Wg-EjU"/>
                                            <constraint firstItem="CgP-0M-3YL" firstAttribute="leading" secondItem="OkE-qf-0Cc" secondAttribute="leading" constant="48" id="gyT-eS-IPd"/>
                                            <constraint firstAttribute="trailing" secondItem="CgP-0M-3YL" secondAttribute="trailing" constant="48" id="qIx-jN-zOb"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="OkE-qf-0Cc" firstAttribute="width" secondItem="5Wk-VV-zy2" secondAttribute="width" id="9Kb-ZI-aNu"/>
                                    <constraint firstItem="OkE-qf-0Cc" firstAttribute="height" relation="greaterThanOrEqual" secondItem="5Wk-VV-zy2" secondAttribute="height" id="DCi-gC-44n"/>
                                    <constraint firstItem="OkE-qf-0Cc" firstAttribute="height" secondItem="5Wk-VV-zy2" secondAttribute="height" priority="750" id="Do5-fA-fZZ"/>
                                    <constraint firstAttribute="bottom" secondItem="OkE-qf-0Cc" secondAttribute="bottom" id="LcO-OL-rDu"/>
                                    <constraint firstItem="OkE-qf-0Cc" firstAttribute="top" secondItem="5Wk-VV-zy2" secondAttribute="top" id="hgp-rd-m6h"/>
                                    <constraint firstAttribute="trailing" secondItem="OkE-qf-0Cc" secondAttribute="trailing" id="jFq-j3-u4F"/>
                                    <constraint firstItem="OkE-qf-0Cc" firstAttribute="leading" secondItem="5Wk-VV-zy2" secondAttribute="leading" id="udz-hw-BJ1"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="axQ-Ml-1bk"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="axQ-Ml-1bk" firstAttribute="bottom" secondItem="5Wk-VV-zy2" secondAttribute="bottom" id="Acn-es-UC7"/>
                            <constraint firstItem="5Wk-VV-zy2" firstAttribute="trailing" secondItem="axQ-Ml-1bk" secondAttribute="trailing" id="Eur-Sq-CaO"/>
                            <constraint firstItem="5Wk-VV-zy2" firstAttribute="leading" secondItem="axQ-Ml-1bk" secondAttribute="leading" id="OFu-CB-kIv"/>
                            <constraint firstItem="5Wk-VV-zy2" firstAttribute="top" secondItem="axQ-Ml-1bk" secondAttribute="top" id="uHM-Kj-ZUf"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="cPH-Qt-wjL">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="gtl-du-Dhd">
                            <connections>
                                <action selector="didTapBack:" destination="A6j-yX-GIx" id="Cb5-OB-a9n"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" translucent="NO" prompted="NO"/>
                    <connections>
                        <outlet property="actionButton" destination="DUQ-ZQ-kW4" id="KZ2-6X-18r"/>
                        <outlet property="backButtonItem" destination="gtl-du-Dhd" id="6Aa-10-VLo"/>
                        <outlet property="bottomConstraint" destination="LP6-q1-sfo" id="ido-z9-LmF"/>
                        <outlet property="contentView" destination="eNg-HP-w9f" id="g4C-N4-HNf"/>
                        <outlet property="imageView" destination="96O-jp-B6G" id="VFM-Xm-GIZ"/>
                        <outlet property="messageLabel" destination="L4M-uo-E1F" id="TJL-HZ-b1l"/>
                        <outlet property="titleLabel" destination="KuF-Qt-4uw" id="HjZ-W8-Fxh"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="gpV-Ei-Mxy" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2882" y="-242"/>
        </scene>
        <!--Flutter Scroll View Controller Container-->
        <scene sceneID="WIu-mS-2K1">
            <objects>
                <viewController storyboardIdentifier="FlutterScrollViewControllerContainer" id="PpC-sj-3t9" customClass="FlutterScrollViewControllerContainer" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="BB4-1m-7LO">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="GlZ-IL-v5g">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" placeholderIntrinsicWidth="0.0" placeholderIntrinsicHeight="736" translatesAutoresizingMaskIntoConstraints="NO" id="f5h-IL-vWL">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="736"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="f5h-IL-vWL" firstAttribute="height" relation="greaterThanOrEqual" secondItem="GlZ-IL-v5g" secondAttribute="height" id="1Al-tB-zaT"/>
                                    <constraint firstAttribute="bottom" secondItem="f5h-IL-vWL" secondAttribute="bottom" id="4aU-ru-3XP"/>
                                    <constraint firstAttribute="trailing" secondItem="f5h-IL-vWL" secondAttribute="trailing" id="5Fu-ab-71Q"/>
                                    <constraint firstItem="f5h-IL-vWL" firstAttribute="leading" secondItem="GlZ-IL-v5g" secondAttribute="leading" id="Wgv-fQ-Gu3"/>
                                    <constraint firstItem="f5h-IL-vWL" firstAttribute="top" secondItem="GlZ-IL-v5g" secondAttribute="top" id="d7e-ns-lSb"/>
                                    <constraint firstItem="f5h-IL-vWL" firstAttribute="width" secondItem="GlZ-IL-v5g" secondAttribute="width" id="fGS-FK-y9q"/>
                                </constraints>
                                <connections>
                                    <outlet property="delegate" destination="PpC-sj-3t9" id="spM-Zq-b4s"/>
                                </connections>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="tQP-3f-O5G"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="GlZ-IL-v5g" firstAttribute="top" secondItem="BB4-1m-7LO" secondAttribute="top" id="3Uc-Mv-m2d"/>
                            <constraint firstItem="GlZ-IL-v5g" firstAttribute="leading" secondItem="BB4-1m-7LO" secondAttribute="leading" id="45N-vq-sBu"/>
                            <constraint firstAttribute="trailing" secondItem="GlZ-IL-v5g" secondAttribute="trailing" id="J7p-EZ-fku"/>
                            <constraint firstAttribute="bottom" secondItem="GlZ-IL-v5g" secondAttribute="bottom" id="wVa-NZ-NWt"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarBackButtonHidden" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="contentView" destination="f5h-IL-vWL" id="9x0-rr-pKX"/>
                        <outlet property="scrollView" destination="GlZ-IL-v5g" id="1sh-it-bYx"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="8EV-7K-aip" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2129" y="433"/>
        </scene>
        <!--MayaWK Web View Controller-->
        <scene sceneID="aDa-oi-2Pb">
            <objects>
                <viewController storyboardIdentifier="MayaWKWebViewController" id="5fR-dO-v6X" customClass="MayaWKWebViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="to3-Xk-eNp">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="unl-vO-zUJ">
                                <rect key="frame" x="0.0" y="20" width="375" height="72"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="X3W-af-DCN">
                                        <rect key="frame" x="24" y="24" width="24" height="24"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="24" id="5dw-wm-aQU"/>
                                            <constraint firstAttribute="width" constant="24" id="Vmd-ni-dVG"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" image="iconSystemCross"/>
                                        <connections>
                                            <action selector="didTapBack:" destination="5fR-dO-v6X" eventType="touchUpInside" id="5WX-zd-Vy5"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text=" " lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="I5B-dr-duY">
                                        <rect key="frame" x="72" y="24" width="143" height="24.5"/>
                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="20"/>
                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Dsb-Ha-79B">
                                        <rect key="frame" x="239" y="24" width="24" height="24"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="24" id="5h1-tv-m2Y"/>
                                            <constraint firstAttribute="height" constant="24" id="tbg-X7-yaP"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" image="iconSystemChevronLeft"/>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="tsj-G5-nSl">
                                        <rect key="frame" x="287" y="24" width="24" height="24"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="24" id="Hh2-oo-QU4"/>
                                            <constraint firstAttribute="width" constant="24" id="yXD-qz-QXz"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" image="iconSystemChevronRight"/>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Em8-Ga-8Ow">
                                        <rect key="frame" x="327" y="24" width="24" height="24"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="24" id="GhT-Iq-yg3"/>
                                            <constraint firstAttribute="width" constant="24" id="n9T-zO-Jnl"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Button" image="iconOtherReload"/>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" name="ContentPrimaryWhite"/>
                                <constraints>
                                    <constraint firstItem="Dsb-Ha-79B" firstAttribute="centerY" secondItem="X3W-af-DCN" secondAttribute="centerY" id="4gZ-aZ-MUA"/>
                                    <constraint firstItem="X3W-af-DCN" firstAttribute="centerY" secondItem="unl-vO-zUJ" secondAttribute="centerY" id="AOh-1R-PFJ"/>
                                    <constraint firstItem="tsj-G5-nSl" firstAttribute="centerY" secondItem="X3W-af-DCN" secondAttribute="centerY" id="Cje-t7-usW"/>
                                    <constraint firstItem="tsj-G5-nSl" firstAttribute="leading" secondItem="Dsb-Ha-79B" secondAttribute="trailing" constant="24" id="DYY-k9-mO1"/>
                                    <constraint firstItem="I5B-dr-duY" firstAttribute="centerY" secondItem="X3W-af-DCN" secondAttribute="centerY" id="QiC-1E-NXE"/>
                                    <constraint firstItem="I5B-dr-duY" firstAttribute="leading" secondItem="X3W-af-DCN" secondAttribute="trailing" constant="24" id="T6m-2m-6Qf"/>
                                    <constraint firstAttribute="height" constant="72" id="W7d-dJ-RJt"/>
                                    <constraint firstItem="Em8-Ga-8Ow" firstAttribute="centerY" secondItem="X3W-af-DCN" secondAttribute="centerY" id="dfT-Uc-zTg"/>
                                    <constraint firstItem="Dsb-Ha-79B" firstAttribute="leading" secondItem="I5B-dr-duY" secondAttribute="trailing" constant="24" id="u7s-L1-bYl"/>
                                    <constraint firstAttribute="trailing" secondItem="Em8-Ga-8Ow" secondAttribute="trailing" constant="24" id="uSc-Iq-3PP"/>
                                    <constraint firstItem="Em8-Ga-8Ow" firstAttribute="leading" secondItem="tsj-G5-nSl" secondAttribute="trailing" constant="16" id="wJc-s9-0V8"/>
                                    <constraint firstItem="X3W-af-DCN" firstAttribute="leading" secondItem="unl-vO-zUJ" secondAttribute="leading" constant="24" id="z2x-9p-n61"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jgL-9h-Hpz">
                                <rect key="frame" x="0.0" y="92" width="375" height="575"/>
                                <subviews>
                                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BBw-kx-Q8y" customClass="MayaDefaultErrorView" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="575"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="BBw-kx-Q8y" secondAttribute="trailing" id="1oy-Qd-NYf"/>
                                    <constraint firstAttribute="bottom" secondItem="BBw-kx-Q8y" secondAttribute="bottom" id="Gbx-xv-IIp"/>
                                    <constraint firstItem="BBw-kx-Q8y" firstAttribute="leading" secondItem="jgL-9h-Hpz" secondAttribute="leading" id="H12-Ff-e2Z"/>
                                    <constraint firstItem="BBw-kx-Q8y" firstAttribute="top" secondItem="jgL-9h-Hpz" secondAttribute="top" id="eBC-nh-oHk"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6fc-ab-fuJ" customClass="MayaWebViewProgressBar" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="92" width="375" height="3"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="3" id="YUi-9n-mol"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="color" keyPath="barColor">
                                        <color key="value" red="0.52156862749999999" green="0.75686274509999996" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="l2Y-6T-ABA"/>
                        <color key="backgroundColor" name="ContentPrimaryWhite"/>
                        <constraints>
                            <constraint firstItem="l2Y-6T-ABA" firstAttribute="trailing" secondItem="jgL-9h-Hpz" secondAttribute="trailing" id="2nX-Er-3q7"/>
                            <constraint firstItem="6fc-ab-fuJ" firstAttribute="leading" secondItem="l2Y-6T-ABA" secondAttribute="leading" id="Daf-WH-nTT"/>
                            <constraint firstItem="jgL-9h-Hpz" firstAttribute="leading" secondItem="l2Y-6T-ABA" secondAttribute="leading" id="Fvz-cF-Wl9"/>
                            <constraint firstItem="l2Y-6T-ABA" firstAttribute="trailing" secondItem="6fc-ab-fuJ" secondAttribute="trailing" id="Iwx-Gt-tYr"/>
                            <constraint firstItem="unl-vO-zUJ" firstAttribute="leading" secondItem="l2Y-6T-ABA" secondAttribute="leading" id="bHT-Sw-A92"/>
                            <constraint firstItem="l2Y-6T-ABA" firstAttribute="trailing" secondItem="unl-vO-zUJ" secondAttribute="trailing" id="dU4-Va-201"/>
                            <constraint firstItem="unl-vO-zUJ" firstAttribute="top" secondItem="l2Y-6T-ABA" secondAttribute="top" id="e0h-4W-Nln"/>
                            <constraint firstItem="6fc-ab-fuJ" firstAttribute="top" secondItem="unl-vO-zUJ" secondAttribute="bottom" id="eZA-XB-xAy"/>
                            <constraint firstItem="l2Y-6T-ABA" firstAttribute="bottom" secondItem="jgL-9h-Hpz" secondAttribute="bottom" id="uCC-zZ-tan"/>
                            <constraint firstItem="jgL-9h-Hpz" firstAttribute="top" secondItem="unl-vO-zUJ" secondAttribute="bottom" id="v7T-Jh-nTF"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarBackButtonHidden" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="appBarView" destination="unl-vO-zUJ" id="hWh-ze-hc0"/>
                        <outlet property="backButton" destination="Dsb-Ha-79B" id="SZb-h4-uhX"/>
                        <outlet property="closeButton" destination="X3W-af-DCN" id="dx2-dT-h92"/>
                        <outlet property="containerView" destination="jgL-9h-Hpz" id="3rG-VS-Bc3"/>
                        <outlet property="errorView" destination="BBw-kx-Q8y" id="J6i-6L-k63"/>
                        <outlet property="forwardButton" destination="tsj-G5-nSl" id="UhU-4O-3ES"/>
                        <outlet property="progressView" destination="6fc-ab-fuJ" id="WtK-Vc-WLn"/>
                        <outlet property="reloadButton" destination="Em8-Ga-8Ow" id="yJM-ty-yZn"/>
                        <outlet property="titleLabel" destination="I5B-dr-duY" id="oEc-UT-CGT"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Us6-5m-qkk" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2880.8000000000002" y="432.23388305847078"/>
        </scene>
        <!--Maya Custom Picker View Controller-->
        <scene sceneID="Zc8-6k-A2Q">
            <objects>
                <viewController storyboardIdentifier="MayaCustomPickerViewController" id="HXL-hi-wms" customClass="MayaCustomPickerViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="iE6-hv-ZR3">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Jrb-JM-ngF" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="300.5" width="375" height="366.5"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="pCE-YT-7Bb">
                                        <rect key="frame" x="24" y="32" width="327" height="302.5"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" text="Select month" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FgP-e5-WsE">
                                                <rect key="frame" x="0.0" y="0.0" width="327" height="24.5"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="20"/>
                                                <color key="textColor" name="ContentPrimaryBlack"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <pickerView contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="t4S-UC-5V9">
                                                <rect key="frame" x="0.0" y="40.5" width="327" height="190"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_maya_cedit_billing_end_date_selector_picker_view">
                                                    <bool key="isElement" value="YES"/>
                                                </accessibility>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="190" id="CZD-5a-joF"/>
                                                </constraints>
                                                <connections>
                                                    <outlet property="dataSource" destination="HXL-hi-wms" id="fdm-AN-iAg"/>
                                                    <outlet property="delegate" destination="HXL-hi-wms" id="X1c-UB-DFI"/>
                                                </connections>
                                            </pickerView>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="ZIB-Hp-TDy">
                                                <rect key="frame" x="0.0" y="246.5" width="327" height="56"/>
                                                <subviews>
                                                    <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="8p7-Ag-NW5" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="158.5" height="56"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_maya_credit_transfer_to_wallet_button"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="56" id="5h8-bm-3rm"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal" title="Cancel"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                <integer key="value" value="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                                <integer key="value" value="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="didTapCancel:" destination="HXL-hi-wms" eventType="touchUpInside" id="d7B-RZ-eTf"/>
                                                        </connections>
                                                    </button>
                                                    <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="has-qX-dNV" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="168.5" y="0.0" width="158.5" height="56"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_maya_credit_transfer_to_wallet_button"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="56" id="IFS-fN-hpG"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal" title="Confirm"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                <integer key="value" value="0"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                                <integer key="value" value="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="didTapConfirm:" destination="HXL-hi-wms" eventType="touchUpInside" id="WoM-Q3-NYf"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="pCE-YT-7Bb" firstAttribute="top" secondItem="Jrb-JM-ngF" secondAttribute="top" constant="32" id="7V4-If-K1M"/>
                                    <constraint firstAttribute="trailing" secondItem="pCE-YT-7Bb" secondAttribute="trailing" constant="24" id="GEO-GF-Coa"/>
                                    <constraint firstItem="pCE-YT-7Bb" firstAttribute="leading" secondItem="Jrb-JM-ngF" secondAttribute="leading" constant="24" id="mAx-Xd-JjU"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                        <real key="value" value="16"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="y1e-Ge-qkp"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="Jrb-JM-ngF" secondAttribute="bottom" id="mmX-sk-Zg7"/>
                            <constraint firstItem="Jrb-JM-ngF" firstAttribute="top" relation="greaterThanOrEqual" secondItem="y1e-Ge-qkp" secondAttribute="top" id="mni-d3-QJO"/>
                            <constraint firstItem="y1e-Ge-qkp" firstAttribute="trailing" secondItem="Jrb-JM-ngF" secondAttribute="trailing" id="pl5-fz-Ob6"/>
                            <constraint firstItem="Jrb-JM-ngF" firstAttribute="leading" secondItem="y1e-Ge-qkp" secondAttribute="leading" id="w5h-8z-14W"/>
                            <constraint firstItem="y1e-Ge-qkp" firstAttribute="bottom" secondItem="pCE-YT-7Bb" secondAttribute="bottom" constant="32" id="whC-HN-vPc"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isDimmerViewTappable" value="NO"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="contentView" destination="Jrb-JM-ngF" id="4h6-3V-90J"/>
                        <outlet property="pickerView" destination="t4S-UC-5V9" id="i8a-VM-DtM"/>
                        <outlet property="titleLabel" destination="FgP-e5-WsE" id="prc-s7-EOW"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="kvP-MQ-2ra" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1740" y="3187"/>
        </scene>
        <!--Maya Custom Date Picker View Controller-->
        <scene sceneID="zFY-IW-jDN">
            <objects>
                <viewController storyboardIdentifier="MayaCustomDatePickerViewController" id="74H-TY-WGv" customClass="MayaCustomDatePickerViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="QKX-Aj-F6j">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8pP-6c-faW" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="300.5" width="375" height="366.5"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="Kad-GT-qFd">
                                        <rect key="frame" x="24" y="32" width="327" height="302.5"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" text="Select date" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gyy-1I-acL">
                                                <rect key="frame" x="0.0" y="0.0" width="327" height="24.5"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="20"/>
                                                <color key="textColor" name="ContentPrimaryBlack"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <datePicker contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" datePickerMode="dateAndTime" minuteInterval="1" translatesAutoresizingMaskIntoConstraints="NO" id="Dyb-IP-9w5">
                                                <rect key="frame" x="0.0" y="40.5" width="327" height="190"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="190" id="Phg-a1-X6d"/>
                                                </constraints>
                                                <connections>
                                                    <action selector="pickerView:" destination="6bO-o9-wsc" eventType="valueChanged" id="Jm9-xf-sWH"/>
                                                </connections>
                                            </datePicker>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="NC0-v3-b9J">
                                                <rect key="frame" x="0.0" y="246.5" width="327" height="56"/>
                                                <subviews>
                                                    <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="DWd-ID-woO" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="158.5" height="56"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_maya_credit_transfer_to_wallet_button"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="56" id="0Qq-CV-bCB"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal" title="Cancel"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                <integer key="value" value="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                                <integer key="value" value="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="didTapCancel:" destination="74H-TY-WGv" eventType="touchUpInside" id="AVX-tb-bWr"/>
                                                        </connections>
                                                    </button>
                                                    <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="1000" verticalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Pvg-gc-XxC" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="168.5" y="0.0" width="158.5" height="56"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_maya_credit_transfer_to_wallet_button"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="56" id="OTC-SV-HJt"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal" title="Confirm"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                <integer key="value" value="0"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                                <integer key="value" value="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="didTapConfirm:" destination="74H-TY-WGv" eventType="touchUpInside" id="HAV-lO-hXi"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="Kad-GT-qFd" firstAttribute="leading" secondItem="8pP-6c-faW" secondAttribute="leading" constant="24" id="ULT-Ab-LUC"/>
                                    <constraint firstItem="Kad-GT-qFd" firstAttribute="top" secondItem="8pP-6c-faW" secondAttribute="top" constant="32" id="lNj-4v-8em"/>
                                    <constraint firstAttribute="trailing" secondItem="Kad-GT-qFd" secondAttribute="trailing" constant="24" id="uNF-IT-FTb"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                        <real key="value" value="16"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="oab-Yj-SM4"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="8pP-6c-faW" firstAttribute="leading" secondItem="oab-Yj-SM4" secondAttribute="leading" id="7Af-lb-nKh"/>
                            <constraint firstAttribute="bottom" secondItem="8pP-6c-faW" secondAttribute="bottom" id="D4d-DG-eba"/>
                            <constraint firstItem="8pP-6c-faW" firstAttribute="top" relation="greaterThanOrEqual" secondItem="oab-Yj-SM4" secondAttribute="top" id="EaU-k7-PZi"/>
                            <constraint firstItem="oab-Yj-SM4" firstAttribute="trailing" secondItem="8pP-6c-faW" secondAttribute="trailing" id="Ks4-eW-gPa"/>
                            <constraint firstItem="oab-Yj-SM4" firstAttribute="bottom" secondItem="Kad-GT-qFd" secondAttribute="bottom" constant="32" id="Mwd-om-Khr"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isDimmerViewTappable" value="NO"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="contentView" destination="8pP-6c-faW" id="TFJ-1k-hhE"/>
                        <outlet property="pickerView" destination="Dyb-IP-9w5" id="Dgx-KO-Ah2"/>
                        <outlet property="titleLabel" destination="gyy-1I-acL" id="t7Z-qK-z19"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="VsH-zB-WQQ" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
                <exit id="6bO-o9-wsc" userLabel="Exit" sceneMemberID="exit"/>
            </objects>
            <point key="canvasLocation" x="2504" y="3188"/>
        </scene>
        <!--Maya Receipt View Controller-->
        <scene sceneID="szD-ul-cgm">
            <objects>
                <viewController storyboardIdentifier="MayaReceiptViewController" id="mUj-KT-dPE" customClass="MayaReceiptViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="lSz-Ng-Uvy">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pYp-uC-OSG">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jLr-Si-uzk" userLabel="Content View">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="100"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="h3u-xL-u6i">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="100"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="100" placeholder="YES" id="JTO-6L-SjY" userLabel="Removed at build time"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="h3u-xL-u6i" secondAttribute="bottom" id="QgI-lu-i7i"/>
                                            <constraint firstItem="h3u-xL-u6i" firstAttribute="top" secondItem="jLr-Si-uzk" secondAttribute="top" id="dHX-M4-4my"/>
                                            <constraint firstItem="h3u-xL-u6i" firstAttribute="leading" secondItem="jLr-Si-uzk" secondAttribute="leading" id="iKj-S8-sA7"/>
                                            <constraint firstAttribute="trailing" secondItem="h3u-xL-u6i" secondAttribute="trailing" id="wLv-OX-X2w"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="jLr-Si-uzk" firstAttribute="width" secondItem="pYp-uC-OSG" secondAttribute="width" id="7uH-J1-cf9"/>
                                    <constraint firstAttribute="bottom" secondItem="jLr-Si-uzk" secondAttribute="bottom" id="Ap4-wt-QnD"/>
                                    <constraint firstAttribute="trailing" secondItem="jLr-Si-uzk" secondAttribute="trailing" id="iTX-sY-meS"/>
                                    <constraint firstItem="jLr-Si-uzk" firstAttribute="leading" secondItem="pYp-uC-OSG" secondAttribute="leading" id="pcx-c7-MWS"/>
                                    <constraint firstItem="jLr-Si-uzk" firstAttribute="top" secondItem="pYp-uC-OSG" secondAttribute="top" id="x4G-Le-RON"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="bnw-tG-thF"/>
                        <color key="backgroundColor" name="BackgroundPrimaryWhite"/>
                        <constraints>
                            <constraint firstItem="pYp-uC-OSG" firstAttribute="top" secondItem="bnw-tG-thF" secondAttribute="top" id="X42-C3-Xq9"/>
                            <constraint firstItem="bnw-tG-thF" firstAttribute="trailing" secondItem="pYp-uC-OSG" secondAttribute="trailing" id="dJc-rg-YT8"/>
                            <constraint firstItem="bnw-tG-thF" firstAttribute="bottom" secondItem="pYp-uC-OSG" secondAttribute="bottom" id="l9X-h7-6sB"/>
                            <constraint firstItem="pYp-uC-OSG" firstAttribute="leading" secondItem="bnw-tG-thF" secondAttribute="leading" id="wXl-eC-4V3"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="mainStackView" destination="h3u-xL-u6i" id="LFJ-jB-vHb"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="7q6-MR-dtz" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-2446" y="488"/>
        </scene>
        <!--All contacts-->
        <scene sceneID="T6y-sY-Oor">
            <objects>
                <viewController storyboardIdentifier="MayaContactsViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="Nb8-aY-dEk" customClass="MayaContactsViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="B7A-j5-0Af"/>
                        <viewControllerLayoutGuide type="bottom" id="yIf-ZV-yXP"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="XuX-HY-nif">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="grouped" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="36" sectionFooterHeight="1" translatesAutoresizingMaskIntoConstraints="NO" id="6WD-Ax-33v">
                                <rect key="frame" x="0.0" y="36" width="375" height="631"/>
                                <color key="backgroundColor" name="PrimaryWhite"/>
                                <connections>
                                    <outlet property="dataSource" destination="Nb8-aY-dEk" id="Gej-tx-7Aw"/>
                                    <outlet property="delegate" destination="Nb8-aY-dEk" id="9iC-fv-tMU"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="wDv-N2-8C5"/>
                        <color key="backgroundColor" name="PrimaryWhite"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="6WD-Ax-33v" secondAttribute="trailing" id="NmG-rX-KyW"/>
                            <constraint firstItem="6WD-Ax-33v" firstAttribute="leading" secondItem="XuX-HY-nif" secondAttribute="leading" id="a5k-Zo-7Q9"/>
                            <constraint firstItem="6WD-Ax-33v" firstAttribute="top" secondItem="B7A-j5-0Af" secondAttribute="bottom" constant="16" id="ijV-84-kTI"/>
                            <constraint firstItem="6WD-Ax-33v" firstAttribute="bottom" secondItem="wDv-N2-8C5" secondAttribute="bottom" id="msR-eU-2Zq"/>
                            <constraint firstItem="6WD-Ax-33v" firstAttribute="leading" secondItem="XuX-HY-nif" secondAttribute="leading" id="slH-I9-LeN"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="All contacts" id="eZT-0q-FZs">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="1wp-PJ-u2W">
                            <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <connections>
                                <action selector="didTapBack:" destination="Nb8-aY-dEk" id="72H-uE-bNR"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="tableView" destination="6WD-Ax-33v" id="iEw-h9-MgQ"/>
                        <outlet property="view" destination="XuX-HY-nif" id="0EQ-XU-QuW"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="EFZ-4h-LEi" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-5568" y="1807"/>
        </scene>
        <!--MayaQR Scanner View Controller-->
        <scene sceneID="Enj-7G-Wfm">
            <objects>
                <viewController storyboardIdentifier="MayaQRScannerViewController" id="Ute-Vh-WfN" customClass="MayaQRScannerViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" ambiguous="YES" id="HIg-G0-SNC">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="ZDL-ga-iaz" customClass="RecordPreviewView" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="n8u-Cm-d1z" userLabel="Top spacer View">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="170"/>
                                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="sRGB"/>
                                    </view>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="mWc-Oy-4e9" userLabel="Bottom spacer View">
                                        <rect key="frame" x="0.0" y="497" width="375" height="170"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Please scan a valid QR code" textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uz2-Gy-bGj">
                                                <rect key="frame" x="24" y="8" width="327" height="24.5"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="20"/>
                                                <color key="textColor" name="SystemError"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="sRGB"/>
                                        <constraints>
                                            <constraint firstItem="uz2-Gy-bGj" firstAttribute="leading" secondItem="mWc-Oy-4e9" secondAttribute="leading" constant="24" id="Y73-Q7-AfE"/>
                                            <constraint firstAttribute="trailing" secondItem="uz2-Gy-bGj" secondAttribute="trailing" constant="24" id="ayH-xm-7Il"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1dt-bT-MVw" userLabel="Left spacer View">
                                        <rect key="frame" x="0.0" y="170" width="24" height="327"/>
                                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="sRGB"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="24" id="TLR-Az-rUI"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1ru-9v-6EI" userLabel="Right spacer View">
                                        <rect key="frame" x="351" y="170" width="24" height="327"/>
                                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="sRGB"/>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="vUU-oB-pIN">
                                        <rect key="frame" x="24" y="40" width="24" height="24"/>
                                        <accessibility key="accessibilityConfiguration" identifier="pmios_qr_scanner_back_button"/>
                                        <state key="normal" image="iconBackWhite">
                                            <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <connections>
                                            <action selector="didTapBack:" destination="Ute-Vh-WfN" eventType="touchUpInside" id="bwY-LW-Y25"/>
                                        </connections>
                                    </button>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Uy3-jQ-ote" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="24" y="104" width="327" height="36"/>
                                        <color key="backgroundColor" name="OpacityWhite90"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                <real key="value" value="16"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </view>
                                    <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="aVE-Cf-1HW">
                                        <rect key="frame" x="24" y="60" width="327" height="80"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="88Q-R3-R70">
                                                <rect key="frame" x="0.0" y="0.0" width="327" height="34"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="UQf-eC-yKC">
                                                        <rect key="frame" x="0.0" y="0.0" width="327" height="34"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Scan a QR code" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IUh-eI-ef1">
                                                                <rect key="frame" x="85.5" y="5" width="156" height="24.5"/>
                                                                <accessibility key="accessibilityConfiguration" identifier="pmios_qr_scanner_title_label"/>
                                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="20"/>
                                                                <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstItem="IUh-eI-ef1" firstAttribute="centerY" secondItem="UQf-eC-yKC" secondAttribute="centerY" id="1CD-wC-Ptc"/>
                                                            <constraint firstAttribute="height" constant="34" id="Qhj-Cb-O1k"/>
                                                            <constraint firstItem="IUh-eI-ef1" firstAttribute="centerX" secondItem="UQf-eC-yKC" secondAttribute="centerX" id="TUB-aA-wuF"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" translatesAutoresizingMaskIntoConstraints="NO" id="PYD-cK-YhF">
                                                <rect key="frame" x="0.0" y="44" width="327" height="36"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2Ku-0v-OSi">
                                                        <rect key="frame" x="0.0" y="0.0" width="10" height="36"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="10" id="pSG-sQ-zVd"/>
                                                        </constraints>
                                                    </view>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconMayaLogoExtraSmall" translatesAutoresizingMaskIntoConstraints="NO" id="ekQ-7a-1Rh">
                                                        <rect key="frame" x="13" y="0.0" width="72" height="36"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="36" id="nGm-Dm-CWB"/>
                                                        </constraints>
                                                    </imageView>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconGcashLogoSmall" translatesAutoresizingMaskIntoConstraints="NO" id="UsN-MO-saE">
                                                        <rect key="frame" x="87.5" y="0.0" width="72" height="36"/>
                                                    </imageView>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconQRPhLogoSmall" translatesAutoresizingMaskIntoConstraints="NO" id="fp2-Jt-PSr">
                                                        <rect key="frame" x="162.5" y="0.0" width="72" height="36"/>
                                                    </imageView>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconInstaPayLogoSmall" translatesAutoresizingMaskIntoConstraints="NO" id="Qx5-tz-tk5">
                                                        <rect key="frame" x="237" y="0.0" width="72" height="36"/>
                                                    </imageView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hKs-kQ-klU">
                                                        <rect key="frame" x="312" y="0.0" width="15" height="36"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="15" id="MbW-Yu-ZPH"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="UsN-MO-saE" firstAttribute="height" secondItem="ekQ-7a-1Rh" secondAttribute="height" id="19A-4i-N3D"/>
                                                    <constraint firstItem="Qx5-tz-tk5" firstAttribute="height" secondItem="ekQ-7a-1Rh" secondAttribute="height" id="K64-LJ-2BX"/>
                                                    <constraint firstItem="fp2-Jt-PSr" firstAttribute="height" secondItem="ekQ-7a-1Rh" secondAttribute="height" id="noP-DC-8nB"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                    </stackView>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="7KM-us-Mgu" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="24" y="170" width="327" height="327"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" secondItem="7KM-us-Mgu" secondAttribute="height" multiplier="1:0.9" priority="998" id="fWi-FG-5ug"/>
                                            <constraint firstAttribute="width" secondItem="7KM-us-Mgu" secondAttribute="height" multiplier="1:1" id="ihC-QZ-1wE"/>
                                        </constraints>
                                    </view>
                                    <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="RnM-Y9-Wdd">
                                        <rect key="frame" x="25" y="573" width="325" height="56"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Cv8-k6-31r" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="0.0" width="157.5" height="56"/>
                                                <color key="backgroundColor" name="PrimaryBlack"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_registration_page1_continue_button"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="HHc-WK-68l"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="-15" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="Upload QR" image="iconNewUploadQR">
                                                    <color key="titleColor" name="PrimaryWhite"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                        <integer key="value" value="3"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="didTapImportQrFromPhotos:" destination="Ute-Vh-WfN" eventType="touchUpInside" id="FVV-yS-h7o"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="PJj-tv-WGQ" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="167.5" y="0.0" width="157.5" height="56"/>
                                                <color key="backgroundColor" name="PrimaryBlack"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_registration_page1_continue_button"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="bXV-ZW-4Sx"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="-15" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="Show my QR" image="iconShowMyQR">
                                                    <color key="titleColor" name="PrimaryWhite"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                        <integer key="value" value="3"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="showMyQRTappedWithSender:" destination="Ute-Vh-WfN" eventType="touchUpInside" id="Lwh-yl-MvT"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="7KM-us-Mgu" firstAttribute="centerX" secondItem="ZDL-ga-iaz" secondAttribute="centerX" id="147-dA-mdL"/>
                                    <constraint firstItem="1ru-9v-6EI" firstAttribute="height" secondItem="1dt-bT-MVw" secondAttribute="height" id="4Gc-PH-ehg"/>
                                    <constraint firstItem="aVE-Cf-1HW" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="ZDL-ga-iaz" secondAttribute="leading" constant="24" id="6ld-MM-PeE"/>
                                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="vUU-oB-pIN" secondAttribute="trailing" constant="20" symbolic="YES" id="CVS-d4-1VY"/>
                                    <constraint firstItem="7KM-us-Mgu" firstAttribute="centerY" secondItem="ZDL-ga-iaz" secondAttribute="centerY" id="EWU-df-OeH"/>
                                    <constraint firstItem="1dt-bT-MVw" firstAttribute="leading" secondItem="n8u-Cm-d1z" secondAttribute="leading" id="HhR-8N-ahp"/>
                                    <constraint firstItem="Uy3-jQ-ote" firstAttribute="top" secondItem="PYD-cK-YhF" secondAttribute="top" id="IvA-vd-s5E"/>
                                    <constraint firstItem="mWc-Oy-4e9" firstAttribute="width" secondItem="n8u-Cm-d1z" secondAttribute="width" id="JnV-qh-Q2i"/>
                                    <constraint firstItem="mWc-Oy-4e9" firstAttribute="height" secondItem="n8u-Cm-d1z" secondAttribute="height" id="Nl9-DZ-W9v"/>
                                    <constraint firstItem="n8u-Cm-d1z" firstAttribute="leading" secondItem="ZDL-ga-iaz" secondAttribute="leading" id="PrC-HH-boQ"/>
                                    <constraint firstItem="Uy3-jQ-ote" firstAttribute="leading" secondItem="PYD-cK-YhF" secondAttribute="leading" id="Pzd-gm-Cv8"/>
                                    <constraint firstItem="aVE-Cf-1HW" firstAttribute="centerX" secondItem="ZDL-ga-iaz" secondAttribute="centerX" id="U1X-af-r2E"/>
                                    <constraint firstItem="Uy3-jQ-ote" firstAttribute="bottom" secondItem="PYD-cK-YhF" secondAttribute="bottom" id="Y3H-iK-yRG"/>
                                    <constraint firstItem="1ru-9v-6EI" firstAttribute="trailing" secondItem="n8u-Cm-d1z" secondAttribute="trailing" id="Y4w-M9-NtA"/>
                                    <constraint firstAttribute="trailing" secondItem="7KM-us-Mgu" secondAttribute="trailing" constant="24" id="YXQ-1T-fDg"/>
                                    <constraint firstItem="uz2-Gy-bGj" firstAttribute="top" secondItem="7KM-us-Mgu" secondAttribute="bottom" constant="8" id="arR-LW-SBd"/>
                                    <constraint firstAttribute="bottom" secondItem="RnM-Y9-Wdd" secondAttribute="bottom" constant="38" id="g5u-Yp-mig"/>
                                    <constraint firstAttribute="bottom" secondItem="mWc-Oy-4e9" secondAttribute="bottom" constant="-5" id="gdw-ac-aGC">
                                        <variation key="heightClass=regular-widthClass=compact" constant="0.0"/>
                                    </constraint>
                                    <constraint firstItem="1dt-bT-MVw" firstAttribute="top" secondItem="n8u-Cm-d1z" secondAttribute="bottom" constant="1" id="hNF-I3-ksL">
                                        <variation key="heightClass=regular-widthClass=compact" constant="0.0"/>
                                    </constraint>
                                    <constraint firstItem="mWc-Oy-4e9" firstAttribute="leading" secondItem="n8u-Cm-d1z" secondAttribute="leading" id="mdh-In-7DL"/>
                                    <constraint firstItem="7KM-us-Mgu" firstAttribute="leading" secondItem="ZDL-ga-iaz" secondAttribute="leading" constant="24" id="o43-rq-j6t"/>
                                    <constraint firstItem="7KM-us-Mgu" firstAttribute="top" secondItem="n8u-Cm-d1z" secondAttribute="bottom" id="oR4-Ch-tqT"/>
                                    <constraint firstItem="RnM-Y9-Wdd" firstAttribute="leading" secondItem="ZDL-ga-iaz" secondAttribute="leading" constant="25" id="qRk-fj-g9T"/>
                                    <constraint firstItem="1ru-9v-6EI" firstAttribute="top" secondItem="n8u-Cm-d1z" secondAttribute="bottom" constant="1" id="qn6-s0-6MB">
                                        <variation key="heightClass=regular-widthClass=compact" constant="0.0"/>
                                    </constraint>
                                    <constraint firstAttribute="trailing" secondItem="RnM-Y9-Wdd" secondAttribute="trailing" constant="25" id="rWh-JG-dbh"/>
                                    <constraint firstAttribute="trailing" secondItem="n8u-Cm-d1z" secondAttribute="trailing" id="rrw-Jc-VGF"/>
                                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="aVE-Cf-1HW" secondAttribute="trailing" constant="24" id="rxO-2V-SZK"/>
                                    <constraint firstItem="vUU-oB-pIN" firstAttribute="leading" secondItem="ZDL-ga-iaz" secondAttribute="leading" constant="24" id="s8N-1t-U9U"/>
                                    <constraint firstItem="mWc-Oy-4e9" firstAttribute="top" secondItem="1dt-bT-MVw" secondAttribute="bottom" constant="-5" id="sfV-o8-bci">
                                        <variation key="heightClass=regular-widthClass=compact" constant="0.0"/>
                                    </constraint>
                                    <constraint firstItem="n8u-Cm-d1z" firstAttribute="top" secondItem="ZDL-ga-iaz" secondAttribute="top" id="x3c-GH-Ngb"/>
                                    <constraint firstItem="7KM-us-Mgu" firstAttribute="top" secondItem="aVE-Cf-1HW" secondAttribute="bottom" constant="30" id="xBF-GD-xbZ"/>
                                    <constraint firstItem="1ru-9v-6EI" firstAttribute="width" secondItem="1dt-bT-MVw" secondAttribute="width" id="xZR-oM-VDg"/>
                                    <constraint firstItem="Uy3-jQ-ote" firstAttribute="trailing" secondItem="PYD-cK-YhF" secondAttribute="trailing" id="zoF-2a-EJW"/>
                                </constraints>
                                <variation key="default">
                                    <mask key="subviews">
                                        <exclude reference="n8u-Cm-d1z"/>
                                        <exclude reference="mWc-Oy-4e9"/>
                                        <exclude reference="1dt-bT-MVw"/>
                                        <exclude reference="1ru-9v-6EI"/>
                                        <exclude reference="vUU-oB-pIN"/>
                                    </mask>
                                    <mask key="constraints">
                                        <exclude reference="rrw-Jc-VGF"/>
                                        <exclude reference="PrC-HH-boQ"/>
                                        <exclude reference="x3c-GH-Ngb"/>
                                        <exclude reference="s8N-1t-U9U"/>
                                        <exclude reference="HhR-8N-ahp"/>
                                        <exclude reference="hNF-I3-ksL"/>
                                        <exclude reference="Y4w-M9-NtA"/>
                                        <exclude reference="xZR-oM-VDg"/>
                                        <exclude reference="qn6-s0-6MB"/>
                                        <exclude reference="4Gc-PH-ehg"/>
                                        <exclude reference="mdh-In-7DL"/>
                                        <exclude reference="JnV-qh-Q2i"/>
                                        <exclude reference="gdw-ac-aGC"/>
                                        <exclude reference="sfV-o8-bci"/>
                                        <exclude reference="Nl9-DZ-W9v"/>
                                    </mask>
                                </variation>
                                <variation key="heightClass=regular-widthClass=compact">
                                    <mask key="subviews">
                                        <include reference="n8u-Cm-d1z"/>
                                        <include reference="mWc-Oy-4e9"/>
                                        <include reference="1dt-bT-MVw"/>
                                        <include reference="1ru-9v-6EI"/>
                                        <include reference="vUU-oB-pIN"/>
                                    </mask>
                                    <mask key="constraints">
                                        <include reference="rrw-Jc-VGF"/>
                                        <include reference="PrC-HH-boQ"/>
                                        <include reference="x3c-GH-Ngb"/>
                                        <include reference="s8N-1t-U9U"/>
                                        <include reference="HhR-8N-ahp"/>
                                        <include reference="hNF-I3-ksL"/>
                                        <include reference="Y4w-M9-NtA"/>
                                        <include reference="xZR-oM-VDg"/>
                                        <include reference="qn6-s0-6MB"/>
                                        <include reference="4Gc-PH-ehg"/>
                                        <include reference="mdh-In-7DL"/>
                                        <include reference="JnV-qh-Q2i"/>
                                        <include reference="gdw-ac-aGC"/>
                                        <include reference="sfV-o8-bci"/>
                                        <include reference="Nl9-DZ-W9v"/>
                                    </mask>
                                </variation>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="lDA-fU-QNY"/>
                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="aVE-Cf-1HW" firstAttribute="top" relation="greaterThanOrEqual" secondItem="lDA-fU-QNY" secondAttribute="top" constant="44" id="8aZ-h1-Zvq"/>
                            <constraint firstItem="ZDL-ga-iaz" firstAttribute="top" secondItem="HIg-G0-SNC" secondAttribute="top" id="Gkv-1z-1Kp"/>
                            <constraint firstItem="ZDL-ga-iaz" firstAttribute="leading" secondItem="lDA-fU-QNY" secondAttribute="leading" id="asB-Tw-APN"/>
                            <constraint firstItem="vUU-oB-pIN" firstAttribute="top" secondItem="lDA-fU-QNY" secondAttribute="top" constant="20" id="dHs-9b-zQD"/>
                            <constraint firstAttribute="bottom" secondItem="ZDL-ga-iaz" secondAttribute="bottom" id="e7D-9O-HBw"/>
                            <constraint firstItem="lDA-fU-QNY" firstAttribute="trailing" secondItem="ZDL-ga-iaz" secondAttribute="trailing" id="iKs-6h-Yxn"/>
                        </constraints>
                        <variation key="default">
                            <mask key="subviews">
                                <exclude reference="ZDL-ga-iaz"/>
                            </mask>
                            <mask key="constraints">
                                <exclude reference="asB-Tw-APN"/>
                                <exclude reference="e7D-9O-HBw"/>
                                <exclude reference="Gkv-1z-1Kp"/>
                                <exclude reference="iKs-6h-Yxn"/>
                            </mask>
                        </variation>
                        <variation key="heightClass=regular-widthClass=compact">
                            <mask key="subviews">
                                <include reference="ZDL-ga-iaz"/>
                            </mask>
                            <mask key="constraints">
                                <include reference="asB-Tw-APN"/>
                                <include reference="e7D-9O-HBw"/>
                                <include reference="Gkv-1z-1Kp"/>
                                <include reference="iKs-6h-Yxn"/>
                            </mask>
                        </variation>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                        <userDefinedRuntimeAttribute type="number" keyPath="statusBarStyle">
                            <integer key="value" value="1"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="arrowBackButton" destination="vUU-oB-pIN" id="vJw-gG-yhk"/>
                        <outlet property="bankLogosBottomConstraint" destination="xBF-GD-xbZ" id="kxy-b3-K5V"/>
                        <outlet property="bankLogosHeightConstraint" destination="nGm-Dm-CWB" id="LzA-xi-POM"/>
                        <outlet property="bankLogosStackView" destination="PYD-cK-YhF" id="FIc-oG-A9D"/>
                        <outlet property="cameraScanView" destination="ZDL-ga-iaz" id="LTp-oI-BrM"/>
                        <outlet property="errorInstructionLabel" destination="uz2-Gy-bGj" id="MOy-b6-bCX"/>
                        <outlet property="scannerFrameView" destination="7KM-us-Mgu" id="j3c-PL-xLL"/>
                        <outlet property="scannerViewHeightConstraint" destination="ihC-QZ-1wE" id="fAQ-s1-wiE"/>
                        <outlet property="scannerViewHeightConstraintForIphoneSE" destination="fWi-FG-5ug" id="Gfb-wi-lBX"/>
                        <outlet property="showQRButton" destination="PJj-tv-WGQ" id="Z8L-VJ-SUV"/>
                        <outlet property="uploadQRButton" destination="Cv8-k6-31r" id="Wpl-Gq-iFS"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Jxm-N5-UaD" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <tapGestureRecognizer id="idX-UQ-pn0">
                    <connections>
                        <action selector="didTapImportQrFromPhotos:" destination="Ute-Vh-WfN" id="tlH-ok-MaD"/>
                    </connections>
                </tapGestureRecognizer>
            </objects>
            <point key="canvasLocation" x="2411.594202898551" y="1806.521739130435"/>
        </scene>
        <!--Maya Web View Parameters Landing Error View Controller-->
        <scene sceneID="fpH-Nt-IGf">
            <objects>
                <viewController storyboardIdentifier="MayaWebViewParametersLandingErrorViewController" id="rDQ-Je-FC8" customClass="MayaWebViewParametersLandingErrorViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="xPb-ox-uah">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JRk-jE-rMY">
                                <rect key="frame" x="0.0" y="20" width="375" height="72"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qXI-Nn-aTK">
                                        <rect key="frame" x="24" y="24" width="24" height="24"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="24" id="T9e-bG-4nt"/>
                                            <constraint firstAttribute="height" constant="24" id="Tr3-PD-ebt"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" image="iconSystemCross"/>
                                        <connections>
                                            <action selector="didTapBack:" destination="rDQ-Je-FC8" eventType="touchUpInside" id="9nl-gx-rFu"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text=" " lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SDt-39-5nC">
                                        <rect key="frame" x="72" y="24" width="143" height="24.5"/>
                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="20"/>
                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" name="ContentPrimaryWhite"/>
                                <constraints>
                                    <constraint firstItem="SDt-39-5nC" firstAttribute="centerY" secondItem="qXI-Nn-aTK" secondAttribute="centerY" id="6fS-7F-EhF"/>
                                    <constraint firstItem="SDt-39-5nC" firstAttribute="leading" secondItem="qXI-Nn-aTK" secondAttribute="trailing" constant="24" id="9Ae-wa-Yn4"/>
                                    <constraint firstAttribute="trailing" secondItem="SDt-39-5nC" secondAttribute="trailing" constant="160" id="FpZ-Bt-h55"/>
                                    <constraint firstItem="qXI-Nn-aTK" firstAttribute="centerY" secondItem="JRk-jE-rMY" secondAttribute="centerY" id="MtN-hB-baW"/>
                                    <constraint firstAttribute="height" constant="72" id="QYZ-h3-m7b"/>
                                    <constraint firstItem="qXI-Nn-aTK" firstAttribute="leading" secondItem="JRk-jE-rMY" secondAttribute="leading" constant="24" id="whB-of-kuB"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Tes-hq-Wne">
                                <rect key="frame" x="0.0" y="92" width="375" height="575"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fbP-Yb-t4g">
                                        <rect key="frame" x="0.0" y="96" width="375" height="383.5"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Warning" translatesAutoresizingMaskIntoConstraints="NO" id="2Dq-8B-zqH">
                                                <rect key="frame" x="87.5" y="0.0" width="200" height="200"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" secondItem="2Dq-8B-zqH" secondAttribute="height" multiplier="1:1" id="Xjm-da-sub"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Age requirement not met" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JP7-HH-hKD">
                                                <rect key="frame" x="48" y="200" width="279" height="58.5"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                                <color key="textColor" name="ContentPrimaryBlack"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="It appears that you do not currently meet the age required to access this feature." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5Ri-Y1-OiP">
                                                <rect key="frame" x="48" y="266.5" width="279" height="37"/>
                                                <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                <color key="textColor" name="ContentGrey6"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="OMk-X4-mTK" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="48" y="327.5" width="279" height="56"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="bry-yO-KGJ"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="Back to Home"/>
                                                <connections>
                                                    <action selector="didTapBackToHome:" destination="rDQ-Je-FC8" eventType="touchUpInside" id="I30-nY-oZx"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="5Ri-Y1-OiP" firstAttribute="top" secondItem="JP7-HH-hKD" secondAttribute="bottom" constant="8" id="DVd-kY-qWw"/>
                                            <constraint firstItem="JP7-HH-hKD" firstAttribute="leading" secondItem="fbP-Yb-t4g" secondAttribute="leading" constant="48" id="F7F-kr-FCh"/>
                                            <constraint firstAttribute="trailing" secondItem="JP7-HH-hKD" secondAttribute="trailing" constant="48" id="GWQ-zk-iVu"/>
                                            <constraint firstItem="2Dq-8B-zqH" firstAttribute="top" secondItem="fbP-Yb-t4g" secondAttribute="top" id="IWv-dk-Yey"/>
                                            <constraint firstAttribute="trailing" secondItem="OMk-X4-mTK" secondAttribute="trailing" constant="48" id="NKS-zY-cuz"/>
                                            <constraint firstItem="2Dq-8B-zqH" firstAttribute="centerX" secondItem="fbP-Yb-t4g" secondAttribute="centerX" id="P9O-B6-GaT"/>
                                            <constraint firstItem="5Ri-Y1-OiP" firstAttribute="leading" secondItem="fbP-Yb-t4g" secondAttribute="leading" constant="48" id="WuB-hT-1Xb"/>
                                            <constraint firstAttribute="bottom" secondItem="OMk-X4-mTK" secondAttribute="bottom" id="hgW-XO-hXe"/>
                                            <constraint firstItem="JP7-HH-hKD" firstAttribute="top" secondItem="2Dq-8B-zqH" secondAttribute="bottom" id="kdX-Bm-06P"/>
                                            <constraint firstAttribute="trailing" secondItem="5Ri-Y1-OiP" secondAttribute="trailing" constant="48" id="udd-pc-BlM"/>
                                            <constraint firstItem="OMk-X4-mTK" firstAttribute="leading" secondItem="fbP-Yb-t4g" secondAttribute="leading" constant="48" id="xXZ-Ke-i2g"/>
                                            <constraint firstItem="OMk-X4-mTK" firstAttribute="top" secondItem="5Ri-Y1-OiP" secondAttribute="bottom" constant="24" id="xl9-Sc-vOO"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="fbP-Yb-t4g" firstAttribute="centerY" secondItem="Tes-hq-Wne" secondAttribute="centerY" id="ddn-jU-v1Y"/>
                                    <constraint firstItem="fbP-Yb-t4g" firstAttribute="width" secondItem="Tes-hq-Wne" secondAttribute="width" id="k3H-16-F7k"/>
                                    <constraint firstItem="fbP-Yb-t4g" firstAttribute="centerX" secondItem="Tes-hq-Wne" secondAttribute="centerX" id="wa3-Fn-GaI"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="qHp-Wn-KlY"/>
                        <color key="backgroundColor" name="BackgroundPrimaryWhite"/>
                        <constraints>
                            <constraint firstItem="qHp-Wn-KlY" firstAttribute="bottom" secondItem="Tes-hq-Wne" secondAttribute="bottom" id="Ih6-gb-Vqf"/>
                            <constraint firstItem="JRk-jE-rMY" firstAttribute="leading" secondItem="qHp-Wn-KlY" secondAttribute="leading" id="OoM-kw-thO"/>
                            <constraint firstItem="qHp-Wn-KlY" firstAttribute="trailing" secondItem="Tes-hq-Wne" secondAttribute="trailing" id="SP1-bD-LGd"/>
                            <constraint firstItem="Tes-hq-Wne" firstAttribute="top" secondItem="JRk-jE-rMY" secondAttribute="bottom" id="ZpV-LR-evu"/>
                            <constraint firstItem="qHp-Wn-KlY" firstAttribute="trailing" secondItem="JRk-jE-rMY" secondAttribute="trailing" id="u47-IN-vDU"/>
                            <constraint firstItem="JRk-jE-rMY" firstAttribute="top" secondItem="qHp-Wn-KlY" secondAttribute="top" id="vvl-4F-05I"/>
                            <constraint firstItem="Tes-hq-Wne" firstAttribute="leading" secondItem="qHp-Wn-KlY" secondAttribute="leading" id="yIU-8y-lSa"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarBackButtonHidden" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="headerLabel" destination="JP7-HH-hKD" id="3iw-Cy-Z8B"/>
                        <outlet property="imageView" destination="2Dq-8B-zqH" id="3gu-kd-Eiv"/>
                        <outlet property="messageLabel" destination="5Ri-Y1-OiP" id="MSF-j2-cKR"/>
                        <outlet property="titleLabel" destination="SDt-39-5nC" id="Pxj-xb-9L6"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="DnF-oz-uo7" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="5019" y="-242"/>
        </scene>
        <!--Maya Feature Sheet View Controller-->
        <scene sceneID="NM4-Ox-WQV">
            <objects>
                <viewController storyboardIdentifier="MayaFeatureSheetViewController" id="MbA-dA-abJ" customClass="MayaFeatureSheetViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="5x6-8V-hPa">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mFz-9O-sIS">
                                <rect key="frame" x="0.0" y="188" width="375" height="479"/>
                                <subviews>
                                    <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="oow-kt-mnF">
                                        <rect key="frame" x="24" y="32" width="327" height="367"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="3t7-uC-aPT">
                                                <rect key="frame" x="0.0" y="0.0" width="327" height="367"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="20" translatesAutoresizingMaskIntoConstraints="NO" id="yiB-M1-r12">
                                                        <rect key="frame" x="0.0" y="0.0" width="327" height="24.5"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="20"/>
                                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="useFontLineHeight" value="YES"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="EnF-Nx-oh6">
                                                        <rect key="frame" x="0.0" y="32.5" width="327" height="17"/>
                                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                        <color key="textColor" name="ContentGrey5"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="useFontLineHeight" value="YES"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ckj-pi-g8y">
                                                        <rect key="frame" x="0.0" y="57.5" width="327" height="243"/>
                                                        <subviews>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="kycUpgradeRocket" translatesAutoresizingMaskIntoConstraints="NO" id="eZj-cM-pLQ">
                                                                <rect key="frame" x="63.5" y="14" width="200" height="215"/>
                                                            </imageView>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="eZj-cM-pLQ" firstAttribute="centerX" secondItem="ckj-pi-g8y" secondAttribute="centerX" id="H0h-fv-54t"/>
                                                            <constraint firstItem="eZj-cM-pLQ" firstAttribute="centerY" secondItem="ckj-pi-g8y" secondAttribute="centerY" id="RlZ-gV-Jir"/>
                                                            <constraint firstAttribute="height" secondItem="eZj-cM-pLQ" secondAttribute="height" multiplier="1.055" constant="16" id="rhQ-et-MWA"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ep7-aI-a7T">
                                                        <rect key="frame" x="0.0" y="308.5" width="327" height="58.5"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nsi-uT-5kZ">
                                                                <rect key="frame" x="0.0" y="4" width="327" height="18.5"/>
                                                                <attributedString key="attributedText">
                                                                    <fragment content="Label">
                                                                        <attributes>
                                                                            <color key="NSColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                                                            <font key="NSFont" metaFont="system" size="14"/>
                                                                            <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="truncatingTail" baseWritingDirection="natural" lineSpacing="1" paragraphSpacing="12" lineHeightMultiple="1" tighteningFactorForTruncation="0.0"/>
                                                                        </attributes>
                                                                    </fragment>
                                                                </attributedString>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="useFontLineHeight" value="YES"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="nsi-uT-5kZ" firstAttribute="top" secondItem="Ep7-aI-a7T" secondAttribute="top" constant="4" id="08y-lq-coI"/>
                                                            <constraint firstAttribute="bottom" secondItem="nsi-uT-5kZ" secondAttribute="bottom" constant="36" id="G0S-SS-Hz9"/>
                                                            <constraint firstItem="nsi-uT-5kZ" firstAttribute="leading" secondItem="Ep7-aI-a7T" secondAttribute="leading" id="Uzp-VK-tHh"/>
                                                            <constraint firstAttribute="trailing" secondItem="nsi-uT-5kZ" secondAttribute="trailing" id="aCg-mt-rRp"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="EnF-Nx-oh6" firstAttribute="firstBaseline" secondItem="yiB-M1-r12" secondAttribute="baseline" constant="24" id="rit-WP-oQO"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="3t7-uC-aPT" firstAttribute="width" secondItem="oow-kt-mnF" secondAttribute="width" id="182-F6-RpX"/>
                                            <constraint firstItem="3t7-uC-aPT" firstAttribute="leading" secondItem="oow-kt-mnF" secondAttribute="leading" id="ESI-rg-i89"/>
                                            <constraint firstItem="3t7-uC-aPT" firstAttribute="height" secondItem="oow-kt-mnF" secondAttribute="height" id="IxQ-hF-GCi"/>
                                            <constraint firstAttribute="bottom" secondItem="3t7-uC-aPT" secondAttribute="bottom" id="Njd-Pn-aDh"/>
                                            <constraint firstItem="3t7-uC-aPT" firstAttribute="top" secondItem="oow-kt-mnF" secondAttribute="top" id="Qyu-uh-h7g"/>
                                            <constraint firstAttribute="trailing" secondItem="3t7-uC-aPT" secondAttribute="trailing" id="XbZ-LH-bXG"/>
                                        </constraints>
                                    </scrollView>
                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="1o7-Bs-T9B">
                                        <rect key="frame" x="16" y="399" width="343" height="56"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="D7z-cI-Yc5"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="oow-kt-mnF" firstAttribute="leading" secondItem="mFz-9O-sIS" secondAttribute="leading" constant="24" id="G56-B2-6gL"/>
                                    <constraint firstItem="1o7-Bs-T9B" firstAttribute="leading" secondItem="mFz-9O-sIS" secondAttribute="leading" constant="16" id="hUZ-Ix-qHE"/>
                                    <constraint firstAttribute="trailing" secondItem="1o7-Bs-T9B" secondAttribute="trailing" constant="16" id="i0j-sz-74z"/>
                                    <constraint firstItem="oow-kt-mnF" firstAttribute="bottom" secondItem="1o7-Bs-T9B" secondAttribute="top" id="qUu-TN-GOO"/>
                                    <constraint firstAttribute="trailing" secondItem="oow-kt-mnF" secondAttribute="trailing" constant="24" id="teL-40-Lbq"/>
                                    <constraint firstItem="oow-kt-mnF" firstAttribute="top" secondItem="mFz-9O-sIS" secondAttribute="top" constant="32" id="wMs-2L-wOw"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="siq-Yr-7KO"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="mFz-9O-sIS" secondAttribute="bottom" id="aQn-Zq-s1x"/>
                            <constraint firstItem="siq-Yr-7KO" firstAttribute="trailing" secondItem="mFz-9O-sIS" secondAttribute="trailing" id="fzJ-hO-dIe"/>
                            <constraint firstItem="mFz-9O-sIS" firstAttribute="top" relation="greaterThanOrEqual" secondItem="siq-Yr-7KO" secondAttribute="top" id="yHU-sO-aUR"/>
                            <constraint firstItem="siq-Yr-7KO" firstAttribute="bottom" secondItem="1o7-Bs-T9B" secondAttribute="bottom" constant="24" id="yh7-fc-DjV"/>
                            <constraint firstItem="mFz-9O-sIS" firstAttribute="leading" secondItem="siq-Yr-7KO" secondAttribute="leading" id="zxp-MD-Fzd"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isDimmerViewTappable" value="NO"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="buttonStack" destination="1o7-Bs-T9B" id="5SX-Ns-ujX"/>
                        <outlet property="contentView" destination="mFz-9O-sIS" id="Igd-cC-drI"/>
                        <outlet property="image" destination="eZj-cM-pLQ" id="R73-2M-goO"/>
                        <outlet property="mainStackView" destination="3t7-uC-aPT" id="Hs9-FA-N9O"/>
                        <outlet property="messageLabel" destination="nsi-uT-5kZ" id="Cb2-pw-N2A"/>
                        <outlet property="messageLabelContainer" destination="Ep7-aI-a7T" id="yz5-au-AAt"/>
                        <outlet property="subheaderLabel" destination="EnF-Nx-oh6" id="bCO-AX-4Sk"/>
                        <outlet property="titleLabel" destination="yiB-M1-r12" id="KFe-Cx-rC2"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="6ha-0O-RSp" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="992.30769230769226" y="3884.3601895734596"/>
        </scene>
        <!--Maya Maintenance View Controller-->
        <scene sceneID="agT-Tx-IPU">
            <objects>
                <viewController storyboardIdentifier="MayaMaintenanceViewController" id="p2l-dB-62F" customClass="MayaMaintenanceViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8aH-yQ-uRQ">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jNc-xs-Qfo">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <view verifyAmbiguity="off" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="W2V-yS-xeG">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="imageMayaLogoMedium" translatesAutoresizingMaskIntoConstraints="NO" id="feZ-OR-vcs">
                                        <rect key="frame" x="137" y="49" width="101" height="29"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="29" id="10O-jj-GRd"/>
                                            <constraint firstAttribute="width" constant="101" id="VQI-6O-Zqd"/>
                                        </constraints>
                                    </imageView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="oSp-k1-Gaw">
                                        <rect key="frame" x="0.0" y="126" width="375" height="415.5"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="imageUpdater" translatesAutoresizingMaskIntoConstraints="NO" id="g9M-DO-k0h">
                                                <rect key="frame" x="28.5" y="0.0" width="318" height="318"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="318" id="N4q-8Y-oIU"/>
                                                    <constraint firstAttribute="width" secondItem="g9M-DO-k0h" secondAttribute="height" multiplier="1:1" id="OdU-IB-nBz"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="We’ll be right back!" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jMT-74-pfu">
                                                <rect key="frame" x="24" y="334" width="327" height="39"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="32"/>
                                                <color key="textColor" name="PrimaryWhite"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="751" scrollEnabled="NO" editable="NO" text="Lorem ipsum dolor sit er elit lamet" textAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="hJg-Rz-bKV">
                                                <rect key="frame" x="24" y="381" width="327" height="34.5"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <color key="textColor" name="Grey4"/>
                                                <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                                <connections>
                                                    <outlet property="delegate" destination="p2l-dB-62F" id="ika-Jm-pAD"/>
                                                </connections>
                                            </textView>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="g9M-DO-k0h" firstAttribute="centerX" secondItem="oSp-k1-Gaw" secondAttribute="centerX" id="7eU-Kc-aMw"/>
                                            <constraint firstItem="hJg-Rz-bKV" firstAttribute="leading" secondItem="jMT-74-pfu" secondAttribute="leading" id="RzN-ki-LYv"/>
                                            <constraint firstItem="jMT-74-pfu" firstAttribute="top" secondItem="g9M-DO-k0h" secondAttribute="bottom" constant="16" id="SFM-Xl-2yJ"/>
                                            <constraint firstItem="hJg-Rz-bKV" firstAttribute="top" secondItem="jMT-74-pfu" secondAttribute="bottom" constant="8" id="V2L-au-GuG"/>
                                            <constraint firstItem="g9M-DO-k0h" firstAttribute="top" secondItem="oSp-k1-Gaw" secondAttribute="top" id="YgX-0J-97t"/>
                                            <constraint firstItem="hJg-Rz-bKV" firstAttribute="trailing" secondItem="jMT-74-pfu" secondAttribute="trailing" id="hmz-e9-4zX"/>
                                            <constraint firstItem="jMT-74-pfu" firstAttribute="leading" secondItem="oSp-k1-Gaw" secondAttribute="leading" constant="24" id="jaZ-gW-H8h"/>
                                            <constraint firstAttribute="bottom" secondItem="hJg-Rz-bKV" secondAttribute="bottom" id="ktj-s9-BlV"/>
                                            <constraint firstAttribute="trailing" secondItem="jMT-74-pfu" secondAttribute="trailing" constant="24" id="zfW-ne-TqJ"/>
                                        </constraints>
                                    </view>
                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Gkc-sf-a7V" customClass="MayaHelpCenterCardView" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="24" y="565" width="327" height="68"/>
                                        <color key="backgroundColor" name="Grey8"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="68" id="nbU-kA-D9C"/>
                                        </constraints>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                <real key="value" value="16"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                <color key="value" name="Grey8"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="oSp-k1-Gaw" secondAttribute="trailing" id="2nV-U7-2jn"/>
                                    <constraint firstItem="feZ-OR-vcs" firstAttribute="centerX" secondItem="W2V-yS-xeG" secondAttribute="centerX" id="Dfd-mh-JiS"/>
                                    <constraint firstAttribute="trailing" secondItem="oSp-k1-Gaw" secondAttribute="trailing" id="UJE-wz-MIC"/>
                                    <constraint firstItem="oSp-k1-Gaw" firstAttribute="centerY" secondItem="W2V-yS-xeG" secondAttribute="centerY" id="VR6-eb-6Qb"/>
                                    <constraint firstItem="oSp-k1-Gaw" firstAttribute="leading" secondItem="W2V-yS-xeG" secondAttribute="leading" id="Zde-FA-I0y"/>
                                    <constraint firstItem="Gkc-sf-a7V" firstAttribute="leading" secondItem="W2V-yS-xeG" secondAttribute="leading" constant="24" id="dIF-ZA-YkW"/>
                                    <constraint firstAttribute="bottom" secondItem="Gkc-sf-a7V" secondAttribute="bottom" constant="34" id="fOg-1L-bmI"/>
                                    <constraint firstItem="feZ-OR-vcs" firstAttribute="top" secondItem="W2V-yS-xeG" secondAttribute="top" constant="49" id="h4i-Yg-Fkd"/>
                                    <constraint firstItem="oSp-k1-Gaw" firstAttribute="leading" secondItem="W2V-yS-xeG" secondAttribute="leading" id="jCS-2D-aw9"/>
                                    <constraint firstAttribute="trailing" secondItem="Gkc-sf-a7V" secondAttribute="trailing" constant="24" id="jaF-aP-sSw"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Ikc-Hq-C6b"/>
                        <color key="backgroundColor" name="UpdaterBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="jNc-xs-Qfo" secondAttribute="trailing" id="55g-OS-QZL"/>
                            <constraint firstItem="W2V-yS-xeG" firstAttribute="top" secondItem="Ikc-Hq-C6b" secondAttribute="top" constant="-20" id="9hE-NS-L71"/>
                            <constraint firstItem="jNc-xs-Qfo" firstAttribute="top" secondItem="8aH-yQ-uRQ" secondAttribute="top" id="HKQ-bz-sJR"/>
                            <constraint firstItem="W2V-yS-xeG" firstAttribute="trailing" secondItem="Ikc-Hq-C6b" secondAttribute="trailing" id="N2y-zG-gUc"/>
                            <constraint firstItem="jNc-xs-Qfo" firstAttribute="leading" secondItem="8aH-yQ-uRQ" secondAttribute="leading" id="RGH-pb-n34"/>
                            <constraint firstItem="W2V-yS-xeG" firstAttribute="leading" secondItem="Ikc-Hq-C6b" secondAttribute="leading" id="alB-ex-qjx"/>
                            <constraint firstItem="W2V-yS-xeG" firstAttribute="bottom" secondItem="Ikc-Hq-C6b" secondAttribute="bottom" id="gly-gh-QRv"/>
                            <constraint firstAttribute="bottom" secondItem="jNc-xs-Qfo" secondAttribute="bottom" id="oQh-Y3-JdQ"/>
                            <constraint firstItem="W2V-yS-xeG" firstAttribute="width" secondItem="8aH-yQ-uRQ" secondAttribute="width" id="sb2-Xh-QLP"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="statusBarStyle">
                            <integer key="value" value="1"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="backgroundView" destination="jNc-xs-Qfo" id="k3D-uN-4Ux"/>
                        <outlet property="containerView" destination="W2V-yS-xeG" id="s7G-Si-uRP"/>
                        <outlet property="helpCenterCardView" destination="Gkc-sf-a7V" id="eDJ-eH-q6q"/>
                        <outlet property="iconWidthConstaint" destination="N4q-8Y-oIU" id="mlj-Ma-Aft"/>
                        <outlet property="logoWidthConstraint" destination="VQI-6O-Zqd" id="W5q-My-3VQ"/>
                        <outlet property="messageTextView" destination="hJg-Rz-bKV" id="U1S-Bj-AjR"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="VcF-F3-EPx" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1433.*************" y="-1873.*************"/>
        </scene>
        <!--Maya Send Money Bank Transfer Confirmation View Controller-->
        <scene sceneID="0Xr-lb-KXO">
            <objects>
                <viewController storyboardIdentifier="MayaSendMoneyBankTransferConfirmationViewController" id="2xz-sT-V2m" customClass="MayaSendMoneyBankTransferConfirmationViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="x8D-ih-uFg">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2Lq-1s-8Sj">
                                <rect key="frame" x="0.0" y="64" width="375" height="523"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qQH-ip-EDF" userLabel="Content View">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="100"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="xZ3-b7-roS">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="100"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="100" placeholder="YES" id="wFK-li-nmO" userLabel="Removed at build time"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="xZ3-b7-roS" firstAttribute="top" secondItem="qQH-ip-EDF" secondAttribute="top" id="PVa-4Q-mqG"/>
                                            <constraint firstAttribute="bottom" secondItem="xZ3-b7-roS" secondAttribute="bottom" id="ed6-ti-09U"/>
                                            <constraint firstAttribute="trailing" secondItem="xZ3-b7-roS" secondAttribute="trailing" id="r7M-Kc-7pt"/>
                                            <constraint firstItem="xZ3-b7-roS" firstAttribute="leading" secondItem="qQH-ip-EDF" secondAttribute="leading" id="xLz-kh-Idq"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="qQH-ip-EDF" firstAttribute="leading" secondItem="2Lq-1s-8Sj" secondAttribute="leading" id="5hl-zN-fx5"/>
                                    <constraint firstItem="qQH-ip-EDF" firstAttribute="width" secondItem="2Lq-1s-8Sj" secondAttribute="width" id="IPO-3z-bmz"/>
                                    <constraint firstAttribute="trailing" secondItem="qQH-ip-EDF" secondAttribute="trailing" id="U6G-9z-kgo"/>
                                    <constraint firstItem="qQH-ip-EDF" firstAttribute="top" secondItem="2Lq-1s-8Sj" secondAttribute="top" id="bpF-yE-Gbe"/>
                                    <constraint firstAttribute="bottom" secondItem="qQH-ip-EDF" secondAttribute="bottom" id="ji7-N7-2qW"/>
                                </constraints>
                            </scrollView>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="sTn-b4-xhf">
                                <rect key="frame" x="0.0" y="595" width="375" height="72"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zdB-Sy-sdK" userLabel="Button Container View">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="72"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bhW-xO-P3A" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="0.0" width="327" height="56"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_confirmation_continue_button"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="Rf9-ly-nAF"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="Continue"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="bhW-xO-P3A" firstAttribute="top" secondItem="zdB-Sy-sdK" secondAttribute="top" id="F5c-OC-Wr9"/>
                                            <constraint firstItem="bhW-xO-P3A" firstAttribute="leading" secondItem="zdB-Sy-sdK" secondAttribute="leading" constant="24" id="JAq-W0-1sg"/>
                                            <constraint firstAttribute="trailing" secondItem="bhW-xO-P3A" secondAttribute="trailing" constant="24" id="TcT-6a-Atk"/>
                                            <constraint firstAttribute="bottom" secondItem="bhW-xO-P3A" secondAttribute="bottom" constant="16" id="sGV-Qw-udt"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="jID-7Q-cEo"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="jID-7Q-cEo" firstAttribute="trailing" secondItem="2Lq-1s-8Sj" secondAttribute="trailing" id="Dq6-qA-3wF"/>
                            <constraint firstItem="2Lq-1s-8Sj" firstAttribute="top" secondItem="jID-7Q-cEo" secondAttribute="top" id="Pcn-A0-C59"/>
                            <constraint firstItem="2Lq-1s-8Sj" firstAttribute="leading" secondItem="jID-7Q-cEo" secondAttribute="leading" id="VBn-TI-ars"/>
                            <constraint firstItem="sTn-b4-xhf" firstAttribute="trailing" secondItem="jID-7Q-cEo" secondAttribute="trailing" id="dei-JY-hBh"/>
                            <constraint firstItem="sTn-b4-xhf" firstAttribute="leading" secondItem="jID-7Q-cEo" secondAttribute="leading" id="g27-jC-VUf"/>
                            <constraint firstItem="jID-7Q-cEo" firstAttribute="bottom" secondItem="sTn-b4-xhf" secondAttribute="bottom" id="iJZ-hZ-tiV"/>
                            <constraint firstItem="sTn-b4-xhf" firstAttribute="top" secondItem="2Lq-1s-8Sj" secondAttribute="bottom" constant="8" id="zH2-8R-bVy"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="MAu-FG-ZId">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="gjr-s1-308">
                            <connections>
                                <action selector="didTapBack:" destination="2xz-sT-V2m" id="q9D-lj-H0e"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <connections>
                        <outlet property="bottomStackView" destination="sTn-b4-xhf" id="t4R-5Z-beP"/>
                        <outlet property="continueButton" destination="bhW-xO-P3A" id="h0i-lj-5w4"/>
                        <outlet property="mainStackView" destination="xZ3-b7-roS" id="gcP-ei-88X"/>
                        <outlet property="scrollView" destination="2Lq-1s-8Sj" id="7ga-1D-Ekl"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="u27-bO-dbc" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1823" y="1805"/>
        </scene>
    </scenes>
    <resources>
        <image name="Warning" width="200" height="200"/>
        <image name="addMoneyBank" width="60" height="63"/>
        <image name="iconBack" width="22" height="17"/>
        <image name="iconBackWhite" width="24" height="24"/>
        <image name="iconCheckSuccess" width="65" height="65"/>
        <image name="iconCloseDark" width="24" height="24"/>
        <image name="iconCloseWhite" width="15" height="15"/>
        <image name="iconCloseWhiteWithShadow" width="20" height="20"/>
        <image name="iconGcashLogoSmall" width="72" height="28"/>
        <image name="iconInfo" width="17" height="17"/>
        <image name="iconInstaPayLogoSmall" width="72" height="28"/>
        <image name="iconMayaLogoExtraSmall" width="72" height="28"/>
        <image name="iconNewUploadQR" width="20.5" height="20"/>
        <image name="iconOtherCamera" width="40" height="40"/>
        <image name="iconOtherReload" width="24" height="24"/>
        <image name="iconQRPhLogoSmall" width="72" height="28"/>
        <image name="iconRedExclamation" width="72" height="72"/>
        <image name="iconShowMyQR" width="20.5" height="20"/>
        <image name="iconSystemChevronLeft" width="24" height="24"/>
        <image name="iconSystemChevronRight" width="24" height="24"/>
        <image name="iconSystemCross" width="24" height="24"/>
        <image name="imageMaintenance" width="279" height="279"/>
        <image name="imageMayaLogoMedium" width="198" height="57"/>
        <image name="imageUpdater" width="279" height="279"/>
        <image name="kycUpgradeRocket" width="200" height="200"/>
        <image name="mayaBirdPlug" width="204" height="155"/>
        <namedColor name="BackgroundGrey11">
            <color red="0.043137254901960784" green="0.043137254901960784" blue="0.043137254901960784" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="BackgroundPrimaryWhite">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentGrey4">
            <color red="0.69411764705882351" green="0.70196078431372544" blue="0.70980392156862748" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentGrey5">
            <color red="0.50196078431372548" green="0.50196078431372548" blue="0.50196078431372548" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentGrey6">
            <color red="0.33333333333333331" green="0.33725490196078434" blue="0.34901960784313724" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentPrimaryBlack">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentPrimaryGreen">
            <color red="0.0" green="0.65098039215686276" blue="0.31764705882352939" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentPrimaryWhite">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Gray100Base">
            <color red="0.63899999856948853" green="0.6470000147819519" blue="0.67100000381469727" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Gray200S">
            <color red="0.11400000005960464" green="0.11800000071525574" blue="0.1289999932050705" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey1">
            <color red="0.97647058823529409" green="0.97647058823529409" blue="0.98039215686274506" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey3">
            <color red="0.92549019607843142" green="0.92941176470588238" blue="0.93725490196078431" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey4">
            <color red="0.69411764705882351" green="0.70196078431372544" blue="0.70980392156862748" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey5">
            <color red="0.50196078431372548" green="0.50196078431372548" blue="0.50196078431372548" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey6">
            <color red="0.33333333333333331" green="0.33725490196078434" blue="0.34901960784313724" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey8">
            <color red="0.16470588235294117" green="0.16862745098039217" blue="0.1803921568627451" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ModalGrey3">
            <color red="0.92549019607843142" green="0.92941176470588238" blue="0.93725490196078431" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ModalPrimaryWhite">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="OpacityWhite90">
            <color red="1" green="1" blue="1" alpha="0.89999997615814209" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Primary100Base">
            <color red="0.0" green="0.70599997043609619" blue="0.3919999897480011" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryBlack">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryWhite">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="SystemError">
            <color red="0.9882352941176471" green="0.45098039215686275" blue="0.47843137254901963" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="UpdaterBackgroundColor">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="UpdaterTitleTextColor">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
