<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="CerebriSansPro-Bold.otf">
            <string>CerebriSansPro-Bold</string>
        </array>
        <array key="CerebriSansPro-Book.otf">
            <string>CerebriSansPro-Book</string>
        </array>
        <array key="CerebriSansPro-Regular.otf">
            <string>CerebriSansPro-Regular</string>
        </array>
        <array key="Jeko-Bold.otf">
            <string>Jeko-Bold</string>
        </array>
        <array key="Jeko-Regular.otf">
            <string>Jeko-Regular</string>
        </array>
        <array key="Jeko-SemiBold.otf">
            <string>Jeko-SemiBold</string>
        </array>
    </customFonts>
    <scenes>
        <!--Navigation Controller-->
        <scene sceneID="O6i-AA-pyt">
            <objects>
                <navigationController id="QtS-Mt-FUu" sceneMemberID="viewController">
                    <navigationBar key="navigationBar" contentMode="scaleToFill" misplaced="YES" translucent="NO" id="C5J-g1-l7T">
                        <rect key="frame" x="0.0" y="20" width="375" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <color key="barTintColor" red="0.*****************" green="0.****************" blue="0.*****************" alpha="1" colorSpace="calibratedRGB"/>
                    </navigationBar>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="5QS-pP-hTe" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="578" y="1259"/>
        </scene>
        <!--Start an account-->
        <scene sceneID="L8x-uH-COs">
            <objects>
                <viewController storyboardIdentifier="MayaRegistrationFormFirstViewController" id="3bZ-lO-Yxa" customClass="MayaRegistrationFormFirstViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="kDy-DL-yfX">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FES-4E-NIv">
                                <rect key="frame" x="0.0" y="20" width="375" height="535"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cIf-6L-H3w">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="523"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="253" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XUL-uR-OVq">
                                                <rect key="frame" x="25" y="30" width="325" height="115"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_registration_page1_title_label"/>
                                                <attributedString key="attributedText">
                                                    <fragment content="Start an ">
                                                        <attributes>
                                                            <color key="NSColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <font key="NSFont" metaFont="system" size="48"/>
                                                            <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                        </attributes>
                                                    </fragment>
                                                    <fragment content="account">
                                                        <attributes>
                                                            <color key="NSColor" red="0.0" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <font key="NSFont" metaFont="system" size="48"/>
                                                            <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                        </attributes>
                                                    </fragment>
                                                </attributedString>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="yLL-bR-yLz">
                                                <rect key="frame" x="24" y="169" width="327" height="330"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MYK-Q4-Pdj" customClass="MayaValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="327" height="50"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="hintText" value="First name"/>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="placeholderText" value="Enter first name"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="NO"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="useBottomErrorIconImageView" value="YES"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnEndEditing" value="YES"/>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <outlet property="textFieldDelegate" destination="3bZ-lO-Yxa" id="PFw-uQ-aPc"/>
                                                        </connections>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="TaJ-gV-y8y" userLabel="Middle Name Stack View">
                                                        <rect key="frame" x="0.0" y="62" width="327" height="86"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="y7e-Y8-R6Y" customClass="MayaValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="327" height="50"/>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="hintText" value="Middle name"/>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="placeholderText" value="Enter middle name"/>
                                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="NO"/>
                                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="useBottomErrorIconImageView" value="YES"/>
                                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnEndEditing" value="YES"/>
                                                                </userDefinedRuntimeAttributes>
                                                                <connections>
                                                                    <outlet property="textFieldDelegate" destination="3bZ-lO-Yxa" id="TqJ-Mj-IG6"/>
                                                                </connections>
                                                            </view>
                                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="maw-WB-ATB" userLabel="Middle Name Checkbox Stack View">
                                                                <rect key="frame" x="0.0" y="62" width="327" height="24"/>
                                                                <subviews>
                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4Yu-PJ-wYC" customClass="MayaCheckBox" customModule="PayMaya" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="0.0" width="24" height="24"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="24" id="KlT-Zd-cFN"/>
                                                                            <constraint firstAttribute="width" constant="24" id="gQh-GP-Vz7"/>
                                                                        </constraints>
                                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                        <connections>
                                                                            <action selector="didTapHasNoMiddleNameCheckBox:" destination="3bZ-lO-Yxa" eventType="touchUpInside" id="HOA-9h-X0o"/>
                                                                        </connections>
                                                                    </button>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="No legal middle name" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RKe-hQ-dsB">
                                                                        <rect key="frame" x="36" y="0.0" width="291" height="24"/>
                                                                        <fontDescription key="fontDescription" name="Jeko-Regular" family="Jeko" pointSize="14"/>
                                                                        <color key="textColor" name="Grey5"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <directionalEdgeInsets key="directionalLayoutMargins" top="0.0" leading="0.0" bottom="0.0" trailing="0.0"/>
                                                            </stackView>
                                                        </subviews>
                                                    </stackView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ogs-mg-nTm" customClass="MayaValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="160" width="327" height="50"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="hintText" value="Last name"/>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="placeholderText" value="Enter last name"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="NO"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="useBottomErrorIconImageView" value="YES"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnEndEditing" value="YES"/>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <outlet property="textFieldDelegate" destination="3bZ-lO-Yxa" id="aqv-fs-JcN"/>
                                                        </connections>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="AKX-VV-wam" userLabel="Email Stack View">
                                                        <rect key="frame" x="0.0" y="222" width="327" height="108"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="IIg-Rn-SLs" customClass="MayaValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="327" height="50"/>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="hintText" value="Email address (Optional)"/>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="placeholderText" value="Enter email address"/>
                                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="NO"/>
                                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="useBottomErrorIconImageView" value="YES"/>
                                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnEndEditing" value="YES"/>
                                                                </userDefinedRuntimeAttributes>
                                                                <connections>
                                                                    <outlet property="textFieldDelegate" destination="3bZ-lO-Yxa" id="3ZL-KX-Gn4"/>
                                                                </connections>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FeY-h8-BzP">
                                                                <rect key="frame" x="0.0" y="58" width="327" height="50"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconInfoFilled" translatesAutoresizingMaskIntoConstraints="NO" id="xDx-nU-vKC">
                                                                        <rect key="frame" x="0.0" y="18.5" width="13" height="13"/>
                                                                        <color key="tintColor" name="PrimaryGrownGreen"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="13" id="jRG-9Q-rOm"/>
                                                                            <constraint firstAttribute="width" constant="13" id="t0G-3S-Yx0"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="We’ll verify your email after you create an account." lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9Vn-Mm-Jdb">
                                                                        <rect key="frame" x="20" y="0.0" width="307" height="50"/>
                                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_registration_bottom_description_label"/>
                                                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="12"/>
                                                                        <color key="textColor" name="Grey5"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="bottom" secondItem="9Vn-Mm-Jdb" secondAttribute="bottom" id="BYJ-T9-aza"/>
                                                                    <constraint firstItem="9Vn-Mm-Jdb" firstAttribute="leading" secondItem="xDx-nU-vKC" secondAttribute="trailing" constant="7" id="Pso-ev-ein"/>
                                                                    <constraint firstItem="xDx-nU-vKC" firstAttribute="centerY" secondItem="FeY-h8-BzP" secondAttribute="centerY" id="RR0-1Q-rMn"/>
                                                                    <constraint firstItem="xDx-nU-vKC" firstAttribute="leading" secondItem="FeY-h8-BzP" secondAttribute="leading" id="XrT-rN-1up"/>
                                                                    <constraint firstItem="9Vn-Mm-Jdb" firstAttribute="top" secondItem="FeY-h8-BzP" secondAttribute="top" id="dW3-dl-vef"/>
                                                                    <constraint firstAttribute="trailing" secondItem="9Vn-Mm-Jdb" secondAttribute="trailing" id="dlk-yi-NfN"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="XUL-uR-OVq" secondAttribute="trailing" constant="25" id="9ZQ-u3-OwI"/>
                                            <constraint firstItem="yLL-bR-yLz" firstAttribute="top" secondItem="XUL-uR-OVq" secondAttribute="bottom" constant="24" id="E2c-rV-mG7"/>
                                            <constraint firstAttribute="bottom" secondItem="yLL-bR-yLz" secondAttribute="bottom" constant="24" id="HCj-aq-ZjP"/>
                                            <constraint firstItem="XUL-uR-OVq" firstAttribute="top" secondItem="cIf-6L-H3w" secondAttribute="top" constant="30" id="UZC-Zy-KwF"/>
                                            <constraint firstItem="yLL-bR-yLz" firstAttribute="leading" secondItem="cIf-6L-H3w" secondAttribute="leading" constant="24" id="YRw-wU-Q99"/>
                                            <constraint firstItem="XUL-uR-OVq" firstAttribute="leading" secondItem="cIf-6L-H3w" secondAttribute="leading" constant="25" id="lVF-Qs-Y8N"/>
                                            <constraint firstAttribute="trailing" secondItem="yLL-bR-yLz" secondAttribute="trailing" constant="24" id="mtq-Ld-vJs"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="cIf-6L-H3w" secondAttribute="trailing" id="Fkl-yY-H3y"/>
                                    <constraint firstItem="cIf-6L-H3w" firstAttribute="top" secondItem="FES-4E-NIv" secondAttribute="top" id="Gvf-UL-0Tl"/>
                                    <constraint firstItem="cIf-6L-H3w" firstAttribute="width" secondItem="FES-4E-NIv" secondAttribute="width" id="TDT-hS-eeg"/>
                                    <constraint firstAttribute="bottom" secondItem="cIf-6L-H3w" secondAttribute="bottom" id="Z9v-cs-K26"/>
                                    <constraint firstItem="cIf-6L-H3w" firstAttribute="leading" secondItem="FES-4E-NIv" secondAttribute="leading" id="vdw-Vt-AJi"/>
                                </constraints>
                            </scrollView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="iBy-Qr-fsY" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="24" y="563" width="327" height="56"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_registration_page1_continue_button"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="56" id="QVF-GW-bjy"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="Continue"/>
                                <connections>
                                    <action selector="didTapContinueButton:" destination="3bZ-lO-Yxa" eventType="touchUpInside" id="8Ts-hV-ZBZ"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Nky-Qb-oea" customClass="Button" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="24" y="619" width="327" height="40"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_registration_login_to_existing_button"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="KuK-lR-AIN"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="Log in to your existing account">
                                    <color key="titleColor" name="ButtonPrimaryGreen"/>
                                </state>
                                <connections>
                                    <action selector="didTapLoginButton:" destination="3bZ-lO-Yxa" eventType="touchUpInside" id="HuT-jS-D4h"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="w8l-t2-MQY"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="FES-4E-NIv" firstAttribute="top" secondItem="w8l-t2-MQY" secondAttribute="top" id="8Av-C1-Qdj"/>
                            <constraint firstItem="w8l-t2-MQY" firstAttribute="trailing" secondItem="Nky-Qb-oea" secondAttribute="trailing" constant="24" id="KgC-ir-vCf"/>
                            <constraint firstItem="FES-4E-NIv" firstAttribute="width" secondItem="kDy-DL-yfX" secondAttribute="width" id="TBZ-oQ-NWa"/>
                            <constraint firstItem="iBy-Qr-fsY" firstAttribute="leading" secondItem="w8l-t2-MQY" secondAttribute="leading" constant="24" id="Y27-ja-DYL"/>
                            <constraint firstItem="w8l-t2-MQY" firstAttribute="trailing" secondItem="iBy-Qr-fsY" secondAttribute="trailing" constant="24" id="aFI-7d-9Z0"/>
                            <constraint firstItem="FES-4E-NIv" firstAttribute="centerX" secondItem="w8l-t2-MQY" secondAttribute="centerX" id="aHx-mi-vPH"/>
                            <constraint firstItem="Nky-Qb-oea" firstAttribute="top" secondItem="iBy-Qr-fsY" secondAttribute="bottom" id="f2e-RT-Tnw"/>
                            <constraint firstItem="iBy-Qr-fsY" firstAttribute="top" secondItem="FES-4E-NIv" secondAttribute="bottom" constant="8" id="gUJ-6x-IhH"/>
                            <constraint firstItem="Nky-Qb-oea" firstAttribute="leading" secondItem="w8l-t2-MQY" secondAttribute="leading" constant="24" id="oAB-M9-ngx"/>
                            <constraint firstItem="w8l-t2-MQY" firstAttribute="bottom" secondItem="Nky-Qb-oea" secondAttribute="bottom" constant="8" id="tAb-Jp-gUk"/>
                        </constraints>
                        <connections>
                            <outletCollection property="gestureRecognizers" destination="epM-cw-bIe" appends="YES" id="0j8-qT-YaV"/>
                        </connections>
                    </view>
                    <navigationItem key="navigationItem" title="Start an account" id="O2s-bG-Ctt">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="bSd-op-Z0E">
                            <connections>
                                <action selector="didTapBack:" destination="3bZ-lO-Yxa" id="gnZ-xV-KnX"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isMayaForm" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="accountLabel" destination="XUL-uR-OVq" id="7dz-FJ-QDQ"/>
                        <outlet property="continueButton" destination="iBy-Qr-fsY" id="BkV-bD-HxL"/>
                        <outlet property="emailAddressView" destination="IIg-Rn-SLs" id="Elb-yY-P9n"/>
                        <outlet property="emailNoteLabel" destination="9Vn-Mm-Jdb" id="6ug-Jc-1uk"/>
                        <outlet property="firstNameView" destination="MYK-Q4-Pdj" id="C5S-sB-Nau"/>
                        <outlet property="hasNoMiddleNameCheckBox" destination="4Yu-PJ-wYC" id="O2c-8j-V21"/>
                        <outlet property="hasNoMoreMiddleNameLabel" destination="RKe-hQ-dsB" id="0Xh-2q-tQJ"/>
                        <outlet property="hasNoMoreMiddleNameStackView" destination="maw-WB-ATB" id="Tao-qQ-UUe"/>
                        <outlet property="lastNameView" destination="ogs-mg-nTm" id="Ja3-Vb-5KD"/>
                        <outlet property="middleNameStackView" destination="TaJ-gV-y8y" id="cRV-SG-j4p"/>
                        <outlet property="middleNameView" destination="y7e-Y8-R6Y" id="CQ6-bo-Aho"/>
                        <outlet property="scrollView" destination="FES-4E-NIv" id="TCi-Z9-CHR"/>
                        <outlet property="stickyViewBottomConstraint" destination="tAb-Jp-gUk" id="ZUH-6H-o9g"/>
                    </connections>
                </viewController>
                <tapGestureRecognizer id="epM-cw-bIe">
                    <connections>
                        <action selector="dismissKeyboard:" destination="3bZ-lO-Yxa" id="U1H-DU-ibA"/>
                    </connections>
                </tapGestureRecognizer>
                <placeholder placeholderIdentifier="IBFirstResponder" id="vgV-Sh-nBS" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1935.2" y="6397.1514242878566"/>
        </scene>
        <!--Set your login details-->
        <scene sceneID="NB0-G6-bp2">
            <objects>
                <viewController storyboardIdentifier="MayaRegistrationFormSecondViewController" id="AHD-IT-7lZ" customClass="MayaRegistrationFormSecondViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="OU8-8o-Igz">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OlU-ca-2c2">
                                <rect key="frame" x="0.0" y="20" width="375" height="509"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="r2v-wP-he7">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="437"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" minimumFontSize="36" translatesAutoresizingMaskIntoConstraints="NO" id="Gio-Ap-34x">
                                                <rect key="frame" x="25" y="30" width="325" height="115"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_registration_page2_title_label"/>
                                                <attributedString key="attributedText">
                                                    <fragment content="Set your">
                                                        <attributes>
                                                            <color key="NSColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <font key="NSFont" metaFont="system" size="48"/>
                                                            <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="truncatingTail" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                        </attributes>
                                                    </fragment>
                                                    <fragment content=" ">
                                                        <attributes>
                                                            <font key="NSFont" metaFont="system" size="48"/>
                                                            <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="truncatingTail" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                        </attributes>
                                                    </fragment>
                                                    <fragment content="login details">
                                                        <attributes>
                                                            <color key="NSColor" red="0.0" green="0.65098039220000004" blue="0.31764705879999999" alpha="1" colorSpace="calibratedRGB"/>
                                                            <font key="NSFont" metaFont="system" size="48"/>
                                                            <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="truncatingTail" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                        </attributes>
                                                    </fragment>
                                                </attributedString>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="eoy-NF-dEk">
                                                <rect key="frame" x="25" y="169" width="325" height="268"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Coj-qd-guG" customClass="MayaValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="325" height="128"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="placeholderText" value="Phone number"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="YES"/>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <outlet property="textFieldDelegate" destination="AHD-IT-7lZ" id="sc8-Bh-2Ex"/>
                                                        </connections>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CpH-tQ-PLm" customClass="MayaValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="140" width="325" height="128"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="hintText" value="Password"/>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="placeholderText" value="Enter password"/>
                                                            <userDefinedRuntimeAttribute type="image" keyPath="firstActionImage" value="iconInfo"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="YES"/>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <outlet property="textFieldDelegate" destination="AHD-IT-7lZ" id="YzL-ix-Wjf"/>
                                                        </connections>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="eoy-NF-dEk" firstAttribute="leading" secondItem="Gio-Ap-34x" secondAttribute="leading" id="2eS-1e-hJO"/>
                                            <constraint firstItem="eoy-NF-dEk" firstAttribute="trailing" secondItem="Gio-Ap-34x" secondAttribute="trailing" id="94X-Qo-Lay"/>
                                            <constraint firstAttribute="bottom" secondItem="eoy-NF-dEk" secondAttribute="bottom" id="FIk-bz-c6M"/>
                                            <constraint firstItem="Gio-Ap-34x" firstAttribute="top" secondItem="r2v-wP-he7" secondAttribute="top" constant="30" id="FsJ-CG-szA"/>
                                            <constraint firstItem="Gio-Ap-34x" firstAttribute="leading" secondItem="r2v-wP-he7" secondAttribute="leading" constant="25" id="md4-Yj-qZ8"/>
                                            <constraint firstAttribute="trailing" secondItem="Gio-Ap-34x" secondAttribute="trailing" constant="25" id="uOj-EL-n0p"/>
                                            <constraint firstItem="eoy-NF-dEk" firstAttribute="top" secondItem="Gio-Ap-34x" secondAttribute="bottom" constant="24" id="yGG-et-0Xo"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="r2v-wP-he7" firstAttribute="width" secondItem="OlU-ca-2c2" secondAttribute="width" id="DSP-lM-hBl"/>
                                    <constraint firstItem="r2v-wP-he7" firstAttribute="top" secondItem="OlU-ca-2c2" secondAttribute="top" id="X3b-Cs-WY2"/>
                                    <constraint firstItem="r2v-wP-he7" firstAttribute="leading" secondItem="OlU-ca-2c2" secondAttribute="leading" id="r5J-pn-W4Y"/>
                                    <constraint firstAttribute="trailing" secondItem="r2v-wP-he7" secondAttribute="trailing" id="tYA-yy-6cF"/>
                                    <constraint firstAttribute="bottom" secondItem="r2v-wP-he7" secondAttribute="bottom" id="x7d-ys-hxK"/>
                                </constraints>
                                <connections>
                                    <outletCollection property="gestureRecognizers" destination="hfR-hF-mSb" appends="YES" id="naB-j3-3DD"/>
                                </connections>
                            </scrollView>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="Mjl-C7-Khy">
                                <rect key="frame" x="0.0" y="537" width="375" height="130"/>
                                <subviews>
                                    <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="OyX-dc-nS4">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="50"/>
                                        <subviews>
                                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" editable="NO" textAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="jKa-Km-pEw">
                                                <rect key="frame" x="24" y="0.0" width="327" height="50"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_add_money_via_card_save_card_info_text_view"/>
                                                <color key="textColor" name="Grey6"/>
                                                <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                            </textView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="jKa-Km-pEw" secondAttribute="bottom" id="FO3-05-P0w"/>
                                            <constraint firstAttribute="trailing" secondItem="jKa-Km-pEw" secondAttribute="trailing" constant="24" id="M8Y-Jo-e1g"/>
                                            <constraint firstItem="jKa-Km-pEw" firstAttribute="leading" secondItem="OyX-dc-nS4" secondAttribute="leading" constant="24" id="b6o-S0-Ujl"/>
                                            <constraint firstItem="jKa-Km-pEw" firstAttribute="top" secondItem="OyX-dc-nS4" secondAttribute="top" id="nbY-iS-MMt"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" verticalHuggingPriority="251" verticalCompressionResistancePriority="751" translatesAutoresizingMaskIntoConstraints="NO" id="P4t-pd-pJU">
                                        <rect key="frame" x="0.0" y="66" width="375" height="64"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="wUX-8p-eon" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="0.0" width="327" height="56"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_registration_page2_continue_button"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="1XH-ub-HPq"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="Continue"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="didTapContinueButton:" destination="AHD-IT-7lZ" eventType="touchUpInside" id="YzD-Gd-TdT"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="wUX-8p-eon" firstAttribute="leading" secondItem="P4t-pd-pJU" secondAttribute="leading" constant="24" id="MWJ-AU-Hjr"/>
                                            <constraint firstItem="wUX-8p-eon" firstAttribute="top" secondItem="P4t-pd-pJU" secondAttribute="top" id="Rfu-KG-mJ4"/>
                                            <constraint firstAttribute="trailing" secondItem="wUX-8p-eon" secondAttribute="trailing" constant="24" id="in8-ji-pjl"/>
                                            <constraint firstAttribute="bottom" secondItem="wUX-8p-eon" secondAttribute="bottom" constant="8" id="uOP-OR-5k9"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="dTR-Ll-AC7"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="OlU-ca-2c2" firstAttribute="width" secondItem="OU8-8o-Igz" secondAttribute="width" id="Ggw-k0-0HX"/>
                            <constraint firstItem="OlU-ca-2c2" firstAttribute="top" secondItem="dTR-Ll-AC7" secondAttribute="top" id="hZ8-Ua-Il7"/>
                            <constraint firstItem="Mjl-C7-Khy" firstAttribute="top" secondItem="OlU-ca-2c2" secondAttribute="bottom" constant="8" id="kEc-vG-axg"/>
                            <constraint firstItem="Mjl-C7-Khy" firstAttribute="leading" secondItem="OlU-ca-2c2" secondAttribute="leading" id="pjQ-go-GyW"/>
                            <constraint firstItem="dTR-Ll-AC7" firstAttribute="bottom" secondItem="Mjl-C7-Khy" secondAttribute="bottom" id="u5U-3K-RIK"/>
                            <constraint firstItem="Mjl-C7-Khy" firstAttribute="trailing" secondItem="OlU-ca-2c2" secondAttribute="trailing" id="uST-pP-1wd"/>
                            <constraint firstItem="OlU-ca-2c2" firstAttribute="centerX" secondItem="dTR-Ll-AC7" secondAttribute="centerX" id="vtr-Me-SLz"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="Set your login details" id="WnS-N5-pvg">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="GOp-qf-z6J">
                            <connections>
                                <action selector="didTapBack:" destination="AHD-IT-7lZ" id="LFw-2O-zgP"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isMayaForm" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="continueButton" destination="wUX-8p-eon" id="7cj-yz-3os"/>
                        <outlet property="numberView" destination="Coj-qd-guG" id="GhM-mi-bqm"/>
                        <outlet property="passwordView" destination="CpH-tQ-PLm" id="cg9-iC-Z9f"/>
                        <outlet property="scrollView" destination="OlU-ca-2c2" id="zWR-t0-fen"/>
                        <outlet property="stickyViewBottomConstraint" destination="u5U-3K-RIK" id="FDL-ve-65d"/>
                        <outlet property="tinboContainerView" destination="OyX-dc-nS4" id="6Zn-dA-VCV"/>
                        <outlet property="tinboTextView" destination="jKa-Km-pEw" id="rkc-CW-f9N"/>
                        <outlet property="titleLabel" destination="Gio-Ap-34x" id="Xim-7M-g5q"/>
                    </connections>
                </viewController>
                <tapGestureRecognizer id="hfR-hF-mSb">
                    <connections>
                        <action selector="dismissKeyboard:" destination="AHD-IT-7lZ" id="FsE-N7-rJd"/>
                    </connections>
                </tapGestureRecognizer>
                <placeholder placeholderIdentifier="IBFirstResponder" id="TDt-Lr-CzQ" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-943.20000000000005" y="6396.2518740629694"/>
        </scene>
        <!--MayaOTPViewController-->
        <scene sceneID="lNh-4g-wQZ">
            <objects>
                <viewController storyboardIdentifier="MayaOTPViewController" automaticallyAdjustsScrollViewInsets="NO" id="IQB-Eb-sh7" userLabel="MayaOTPViewController" customClass="MayaOTPViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Fkq-cJ-agv">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" contentInsetAdjustmentBehavior="never" translatesAutoresizingMaskIntoConstraints="NO" id="V6C-2g-hI8">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <subviews>
                                    <view contentMode="scaleToFill" placeholderIntrinsicWidth="infinite" placeholderIntrinsicHeight="667" translatesAutoresizingMaskIntoConstraints="NO" id="8lj-f4-BgT">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="One-time PIN" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cT2-Du-cdu">
                                                <rect key="frame" x="24" y="80" width="327" height="58.5"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_otp_title_label"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="48"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Please enter the one-time PIN (OTP) that we sent to +63 918 1234567" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="F7V-95-Mf1">
                                                <rect key="frame" x="24" y="146.5" width="327" height="34"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_otp_description_label"/>
                                                <fontDescription key="fontDescription" name="Jeko-Regular" family="Jeko" pointSize="14"/>
                                                <color key="textColor" name="ContentGrey6"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="bbz-Me-Jyq">
                                                <rect key="frame" x="24" y="204.5" width="327" height="56"/>
                                                <subviews>
                                                    <view tag="1" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="UiN-kX-XWr" customClass="MayaSingleCharacterInputView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="51" height="56"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <connections>
                                                            <outlet property="textFieldDelegate" destination="IQB-Eb-sh7" id="vj1-2i-J4D"/>
                                                        </connections>
                                                    </view>
                                                    <view tag="2" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="N1y-lY-tek" customClass="MayaSingleCharacterInputView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="55" y="0.0" width="51.5" height="56"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <connections>
                                                            <outlet property="textFieldDelegate" destination="IQB-Eb-sh7" id="oOh-Nc-mcc"/>
                                                        </connections>
                                                    </view>
                                                    <view tag="3" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="G4A-4R-jfe" customClass="MayaSingleCharacterInputView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="110.5" y="0.0" width="51" height="56"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <connections>
                                                            <outlet property="textFieldDelegate" destination="IQB-Eb-sh7" id="GnB-ei-MOr"/>
                                                        </connections>
                                                    </view>
                                                    <view tag="4" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ze6-G4-y1G" customClass="MayaSingleCharacterInputView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="165.5" y="0.0" width="51" height="56"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <connections>
                                                            <outlet property="textFieldDelegate" destination="IQB-Eb-sh7" id="jlM-Hc-xsW"/>
                                                        </connections>
                                                    </view>
                                                    <view tag="5" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nR8-os-bbK" customClass="MayaSingleCharacterInputView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="220.5" y="0.0" width="51.5" height="56"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <connections>
                                                            <outlet property="textFieldDelegate" destination="IQB-Eb-sh7" id="qFd-hE-xjM"/>
                                                        </connections>
                                                    </view>
                                                    <view tag="6" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="iDw-Lm-hrk" customClass="MayaSingleCharacterInputView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="276" y="0.0" width="51" height="56"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <connections>
                                                            <outlet property="textFieldDelegate" destination="IQB-Eb-sh7" id="YRp-TV-b9a"/>
                                                        </connections>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="h3b-RK-Um8"/>
                                                </constraints>
                                            </stackView>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="l8C-7q-XUC" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="284.5" width="327" height="56"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_otp_proceed_button"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="Fkb-EL-zOO"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="Verify"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </button>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="Mdc-nv-pvU">
                                                <rect key="frame" x="24" y="356.5" width="327" height="46"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Resend code in 30 seconds" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WoT-VK-YnC">
                                                        <rect key="frame" x="0.0" y="0.0" width="327" height="17"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_otp_resend_countdown_label"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Regular" family="Jeko" pointSize="14"/>
                                                        <color key="textColor" name="ContentGrey4"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="dV4-Qk-qh0">
                                                        <rect key="frame" x="0.0" y="17" width="327" height="29"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_otp_resend_button"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Regular" family="Jeko" pointSize="14"/>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal" title="Resend code">
                                                            <color key="titleColor" name="ContentPrimaryGreen"/>
                                                        </state>
                                                        <connections>
                                                            <action selector="didTapResendCode:" destination="IQB-Eb-sh7" eventType="touchUpInside" id="VM7-pn-1W7"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="njF-Sx-w8l" customClass="MayaHelpCenterView" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="583" width="327" height="60"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_otp_help_view">
                                                    <bool key="isElement" value="YES"/>
                                                </accessibility>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="60" placeholder="YES" id="3QX-YA-75u"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <gestureRecognizers/>
                                        <constraints>
                                            <constraint firstItem="Mdc-nv-pvU" firstAttribute="top" secondItem="l8C-7q-XUC" secondAttribute="bottom" constant="16" id="1AQ-bP-rMp"/>
                                            <constraint firstAttribute="bottom" secondItem="njF-Sx-w8l" secondAttribute="bottom" constant="24" id="B4I-8Q-L7d"/>
                                            <constraint firstItem="cT2-Du-cdu" firstAttribute="leading" secondItem="8lj-f4-BgT" secondAttribute="leading" constant="24" id="EXq-3P-pX9"/>
                                            <constraint firstItem="bbz-Me-Jyq" firstAttribute="leading" secondItem="F7V-95-Mf1" secondAttribute="leading" id="F7E-MJ-HUN"/>
                                            <constraint firstItem="F7V-95-Mf1" firstAttribute="leading" secondItem="cT2-Du-cdu" secondAttribute="leading" id="FMh-of-17h"/>
                                            <constraint firstItem="njF-Sx-w8l" firstAttribute="top" relation="greaterThanOrEqual" secondItem="Mdc-nv-pvU" secondAttribute="bottom" constant="32" id="I18-Uq-sJk"/>
                                            <constraint firstItem="F7V-95-Mf1" firstAttribute="top" secondItem="cT2-Du-cdu" secondAttribute="bottom" constant="8" id="MgM-KO-njR"/>
                                            <constraint firstItem="Mdc-nv-pvU" firstAttribute="leading" secondItem="l8C-7q-XUC" secondAttribute="leading" id="dDQ-VX-YXS"/>
                                            <constraint firstItem="l8C-7q-XUC" firstAttribute="trailing" secondItem="bbz-Me-Jyq" secondAttribute="trailing" id="dtY-zR-jCO"/>
                                            <constraint firstItem="Mdc-nv-pvU" firstAttribute="trailing" secondItem="l8C-7q-XUC" secondAttribute="trailing" id="e9e-wl-nxt"/>
                                            <constraint firstItem="njF-Sx-w8l" firstAttribute="leading" secondItem="Mdc-nv-pvU" secondAttribute="leading" id="hAZ-0a-1gS"/>
                                            <constraint firstItem="l8C-7q-XUC" firstAttribute="leading" secondItem="bbz-Me-Jyq" secondAttribute="leading" id="kuX-bF-g3k"/>
                                            <constraint firstItem="l8C-7q-XUC" firstAttribute="top" secondItem="bbz-Me-Jyq" secondAttribute="bottom" constant="24" id="nqs-v8-Zj3"/>
                                            <constraint firstItem="njF-Sx-w8l" firstAttribute="trailing" secondItem="Mdc-nv-pvU" secondAttribute="trailing" id="pVG-Qi-dam"/>
                                            <constraint firstItem="F7V-95-Mf1" firstAttribute="trailing" secondItem="cT2-Du-cdu" secondAttribute="trailing" id="pc9-JJ-ZTm"/>
                                            <constraint firstItem="bbz-Me-Jyq" firstAttribute="trailing" secondItem="F7V-95-Mf1" secondAttribute="trailing" id="ukq-dZ-duz"/>
                                            <constraint firstItem="bbz-Me-Jyq" firstAttribute="top" secondItem="F7V-95-Mf1" secondAttribute="bottom" constant="24" id="xOB-tR-5BV"/>
                                            <constraint firstItem="cT2-Du-cdu" firstAttribute="top" secondItem="8lj-f4-BgT" secondAttribute="top" constant="80" id="xUX-Q9-86K"/>
                                            <constraint firstAttribute="trailing" secondItem="cT2-Du-cdu" secondAttribute="trailing" constant="24" id="xrc-0l-4mr"/>
                                        </constraints>
                                        <connections>
                                            <outletCollection property="gestureRecognizers" destination="ucK-TQ-rdG" appends="YES" id="fq6-qg-AtT"/>
                                        </connections>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="8lj-f4-BgT" firstAttribute="width" secondItem="V6C-2g-hI8" secondAttribute="width" id="T28-gp-cV0"/>
                                    <constraint firstItem="8lj-f4-BgT" firstAttribute="leading" secondItem="V6C-2g-hI8" secondAttribute="leading" id="arz-jE-6bw"/>
                                    <constraint firstAttribute="trailing" secondItem="8lj-f4-BgT" secondAttribute="trailing" id="ce4-oX-uAx"/>
                                    <constraint firstItem="8lj-f4-BgT" firstAttribute="top" secondItem="V6C-2g-hI8" secondAttribute="top" id="db7-g7-a90"/>
                                    <constraint firstAttribute="bottom" secondItem="8lj-f4-BgT" secondAttribute="bottom" id="el3-NE-09Q"/>
                                    <constraint firstItem="8lj-f4-BgT" firstAttribute="height" relation="greaterThanOrEqual" secondItem="V6C-2g-hI8" secondAttribute="height" id="ykh-hb-mGq"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="OlL-Sj-afa"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="OlL-Sj-afa" firstAttribute="bottom" secondItem="V6C-2g-hI8" secondAttribute="bottom" id="9ye-hH-9eq"/>
                            <constraint firstItem="V6C-2g-hI8" firstAttribute="top" secondItem="OlL-Sj-afa" secondAttribute="top" id="HjB-F7-h9D"/>
                            <constraint firstItem="OlL-Sj-afa" firstAttribute="trailing" secondItem="V6C-2g-hI8" secondAttribute="trailing" id="k5h-ON-nyo"/>
                            <constraint firstItem="V6C-2g-hI8" firstAttribute="leading" secondItem="OlL-Sj-afa" secondAttribute="leading" id="ywq-rb-Ggy"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="One-Time PIN" id="XuV-9F-FvQ">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="dk6-Cf-DkA">
                            <connections>
                                <action selector="didTapBack:" destination="IQB-Eb-sh7" id="pbT-FW-nRd"/>
                            </connections>
                        </barButtonItem>
                        <connections>
                            <outlet property="backBarButtonItem" destination="dk6-Cf-DkA" id="Akr-72-SYR"/>
                        </connections>
                    </navigationItem>
                    <connections>
                        <outlet property="fifthOTPInputView" destination="nR8-os-bbK" id="AYS-I1-QNx"/>
                        <outlet property="firstOTPInputView" destination="UiN-kX-XWr" id="TrE-2D-wQb"/>
                        <outlet property="fourthOTPInputView" destination="Ze6-G4-y1G" id="B1O-ef-GUc"/>
                        <outlet property="helpCenterView" destination="njF-Sx-w8l" id="zeb-jQ-3Uq"/>
                        <outlet property="otpDescriptionLabel" destination="F7V-95-Mf1" id="NfJ-OV-S6c"/>
                        <outlet property="proceedButton" destination="l8C-7q-XUC" id="Jb9-zV-nkg"/>
                        <outlet property="resendButton" destination="dV4-Qk-qh0" id="hoV-xu-8cB"/>
                        <outlet property="resendCountdownLabel" destination="WoT-VK-YnC" id="1bb-bF-XMf"/>
                        <outlet property="scrollView" destination="V6C-2g-hI8" id="vV4-yX-duZ"/>
                        <outlet property="secondOTPInputView" destination="N1y-lY-tek" id="uat-xC-gw8"/>
                        <outlet property="sixthOTPInputView" destination="iDw-Lm-hrk" id="CNf-0x-PO0"/>
                        <outlet property="thirdOTPInputView" destination="G4A-4R-jfe" id="1Z5-c4-oep"/>
                        <outlet property="titleLabel" destination="cT2-Du-cdu" id="rjQ-60-hTv"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="oE9-QB-d4e" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <tapGestureRecognizer id="ucK-TQ-rdG">
                    <connections>
                        <action selector="dismissKeyboard:" destination="IQB-Eb-sh7" id="XdJ-uY-mUB"/>
                    </connections>
                </tapGestureRecognizer>
            </objects>
            <point key="canvasLocation" x="-250" y="6397"/>
        </scene>
        <!--Maya Registration Password Info View Controller-->
        <scene sceneID="72E-UC-Qgr">
            <objects>
                <viewController storyboardIdentifier="MayaRegistrationPasswordInfoViewController" id="AuX-cq-bye" customClass="MayaRegistrationPasswordInfoViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="QTe-iS-MHe">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qe3-JB-Zwr" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="189" width="375" height="478"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="76m-Ee-ZUC">
                                        <rect key="frame" x="24" y="54" width="327" height="310"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="xa2-6b-zrZ">
                                                <rect key="frame" x="0.0" y="0.0" width="327" height="29"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconInfo" translatesAutoresizingMaskIntoConstraints="NO" id="var-ms-CbD">
                                                        <rect key="frame" x="0.0" y="0.0" width="26" height="29"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="29" id="Ph3-ve-asc"/>
                                                            <constraint firstAttribute="width" constant="26" id="yLb-RY-kqu"/>
                                                        </constraints>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Tips for a strong password" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qcz-Wg-Nzf">
                                                        <rect key="frame" x="36" y="0.0" width="291" height="29"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_registration_password_info_title_label"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" editable="NO" textAlignment="natural" selectable="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bjZ-b6-01Z">
                                                <rect key="frame" x="0.0" y="37" width="327" height="273"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_registration_password_info_description_textview"/>
                                                <string key="text">∙  Combine upper and lower case letters, numbers, and special characters (e.g., $, #, &amp;, etc.).
∙  Keep your password at least 8 to 12 characters long.
∙  Avoid consecutive characters (e.g., 12345, abcde, qwerty, etc.) or repeating characters (e.g., 11111).
∙  Avoid personal info like names of friends or relatives, your birthday, or your address.
∙  Avoid common or obvious words (e.g., password, maya, bank, money, etc.).
∙  Avoid using the same password from other accounts you own.</string>
                                                <color key="textColor" name="ContentGrey6"/>
                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                            </textView>
                                        </subviews>
                                    </stackView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mLJ-Fm-LBx">
                                        <rect key="frame" x="0.0" y="396" width="375" height="82"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LJf-1K-hXX" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="10" width="327" height="48"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_registration_password_info_continue_button"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="xFa-ML-OZA"/>
                                                </constraints>
                                                <state key="normal" title="Continue"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="didTapContinue:" destination="AuX-cq-bye" eventType="touchUpInside" id="PZZ-TS-Ru1"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="LJf-1K-hXX" secondAttribute="trailing" constant="24" id="3rK-vs-xLt"/>
                                            <constraint firstItem="LJf-1K-hXX" firstAttribute="leading" secondItem="mLJ-Fm-LBx" secondAttribute="leading" constant="24" id="Bno-Nr-rih"/>
                                            <constraint firstItem="LJf-1K-hXX" firstAttribute="top" secondItem="mLJ-Fm-LBx" secondAttribute="top" constant="10" id="Pqt-kv-EZa"/>
                                            <constraint firstAttribute="bottom" secondItem="LJf-1K-hXX" secondAttribute="bottom" constant="24" id="lVK-o4-Olr"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="mLJ-Fm-LBx" firstAttribute="top" secondItem="76m-Ee-ZUC" secondAttribute="bottom" constant="32" id="2jR-Fb-LUA"/>
                                    <constraint firstAttribute="trailing" secondItem="mLJ-Fm-LBx" secondAttribute="trailing" id="Bec-fi-6Dg"/>
                                    <constraint firstAttribute="trailing" secondItem="76m-Ee-ZUC" secondAttribute="trailing" constant="24" id="Hew-hN-LDH"/>
                                    <constraint firstItem="mLJ-Fm-LBx" firstAttribute="leading" secondItem="qe3-JB-Zwr" secondAttribute="leading" id="QRQ-2A-Uut"/>
                                    <constraint firstItem="76m-Ee-ZUC" firstAttribute="leading" secondItem="qe3-JB-Zwr" secondAttribute="leading" constant="24" id="T46-GX-ux3"/>
                                    <constraint firstItem="76m-Ee-ZUC" firstAttribute="top" secondItem="qe3-JB-Zwr" secondAttribute="top" constant="54" id="tDy-OP-xGy"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="ib3-9r-SSK"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="qe3-JB-Zwr" firstAttribute="top" relation="greaterThanOrEqual" secondItem="ib3-9r-SSK" secondAttribute="top" id="2FS-hB-Fxr"/>
                            <constraint firstItem="qe3-JB-Zwr" firstAttribute="leading" secondItem="ib3-9r-SSK" secondAttribute="leading" id="T5O-d1-yxW"/>
                            <constraint firstAttribute="bottom" secondItem="qe3-JB-Zwr" secondAttribute="bottom" id="b5H-MG-Eb1"/>
                            <constraint firstItem="ib3-9r-SSK" firstAttribute="bottom" secondItem="mLJ-Fm-LBx" secondAttribute="bottom" id="oY0-3c-Fmt"/>
                            <constraint firstItem="ib3-9r-SSK" firstAttribute="trailing" secondItem="qe3-JB-Zwr" secondAttribute="trailing" id="t3p-NS-0la"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isDimmerViewTappable" value="NO"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="contentView" destination="qe3-JB-Zwr" id="nJd-wo-RWs"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="0Us-XE-J7d" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="416.80000000000001" y="6396.2518740629694"/>
        </scene>
        <!--Maya Prominent Disclosure View Controller-->
        <scene sceneID="skV-Ym-VSJ">
            <objects>
                <viewController storyboardIdentifier="MayaProminentDisclosureViewController" id="MmK-q3-BlT" customClass="MayaProminentDisclosureViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="sah-uI-Cs5">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZGb-kp-AKj">
                                <rect key="frame" x="0.0" y="32.5" width="375" height="634.5"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="tPu-CS-d8V">
                                        <rect key="frame" x="24" y="32" width="327" height="512.5"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Data Privacy" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="84j-P0-wPN">
                                                <rect key="frame" x="0.0" y="0.0" width="327" height="29.5"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_disclosure_title_label"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                                <color key="textColor" name="ContentPrimaryBlack"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" editable="NO" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="Pun-9t-eQM">
                                                <rect key="frame" x="0.0" y="45.5" width="327" height="467"/>
                                                <color key="backgroundColor" name="Grey1"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_disclosure_description_textview"/>
                                                <string key="text">We, at Maya, value your right to privacy and we are committed to inform you of what data we collect and how we process and protect your information.

We have updated the Maya Privacy Statement and below are the key things that you need to know from this update:

Key Highlights  In order to safeguard your account, customize our services and personalize your user experience, Maya will collect information about the device you use to access our products and services, such as: 

Additional information that Maya Collects

• Device attributes: information such as the operating system, hardware and software versions, battery level, signal strength, available storage space, browser type, and installed applications.

• Device identifiers: information such as device IDs and other unique identifiers.

• Device signals: information about Bluetooth signals and nearby wi-fi access points hotspots and cell towers.

• Data from network connections: information such as the name of your mobile network operator or Internet service provider, your mobile number, language, time zone, IP address, connection speed, and information about any devices that are nearby or on your network.

• Data from device settings: information that you allow Maya to receive through device settings you turn on, such as access to your GPS location, contacts, camera, or photos.

Who we share your information with 

Maya may share your information with its security partners which provide information and services to protect users against fraud and abuse. For example, information about security threats can help us notify you if we think your account has been compromised (at which point we can help you take steps to protect your account);

All of these are intended to protect your account in line with our risk management, fraud prevention, and security practices. 

By tapping "Agree and Continue", you acknowledge that you have been informed about this update and you agree to Maya's processing of your information.</string>
                                                <color key="textColor" name="ContentGrey6"/>
                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                            </textView>
                                        </subviews>
                                    </stackView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zr9-hx-M5k">
                                        <rect key="frame" x="0.0" y="544.5" width="375" height="90"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="h83-uN-Gco" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="10" width="327" height="56"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_disclosure_agree_button"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="DBv-6j-0Sg"/>
                                                </constraints>
                                                <state key="normal" title="Agree and Continue"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="didTapAgreeAndContinue:" destination="MmK-q3-BlT" eventType="touchUpInside" id="4rr-0j-OjK"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="h83-uN-Gco" secondAttribute="bottom" constant="24" id="dmV-fA-HV8"/>
                                            <constraint firstItem="h83-uN-Gco" firstAttribute="top" secondItem="zr9-hx-M5k" secondAttribute="top" constant="10" id="fUk-Cd-g7F"/>
                                            <constraint firstAttribute="trailing" secondItem="h83-uN-Gco" secondAttribute="trailing" constant="24" id="m7R-Nz-c0k"/>
                                            <constraint firstItem="h83-uN-Gco" firstAttribute="leading" secondItem="zr9-hx-M5k" secondAttribute="leading" constant="24" id="zjH-m3-nbb"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="zr9-hx-M5k" secondAttribute="trailing" id="Dc6-yv-dm6"/>
                                    <constraint firstItem="tPu-CS-d8V" firstAttribute="leading" secondItem="ZGb-kp-AKj" secondAttribute="leading" constant="24" id="JR9-YC-K6t"/>
                                    <constraint firstItem="zr9-hx-M5k" firstAttribute="leading" secondItem="ZGb-kp-AKj" secondAttribute="leading" id="ZWV-O8-wFn"/>
                                    <constraint firstAttribute="trailing" secondItem="tPu-CS-d8V" secondAttribute="trailing" constant="24" id="b4u-FY-WPp"/>
                                    <constraint firstItem="zr9-hx-M5k" firstAttribute="top" secondItem="tPu-CS-d8V" secondAttribute="bottom" id="hHd-Qn-c5k"/>
                                    <constraint firstItem="tPu-CS-d8V" firstAttribute="top" secondItem="ZGb-kp-AKj" secondAttribute="top" constant="32" id="veB-mC-4ka"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="zbQ-j8-1lK"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="zbQ-j8-1lK" firstAttribute="trailing" secondItem="ZGb-kp-AKj" secondAttribute="trailing" id="Avt-aG-Umd"/>
                            <constraint firstItem="Pun-9t-eQM" firstAttribute="height" secondItem="sah-uI-Cs5" secondAttribute="height" multiplier="7:10" id="D4n-ex-MND"/>
                            <constraint firstItem="ZGb-kp-AKj" firstAttribute="leading" secondItem="zbQ-j8-1lK" secondAttribute="leading" id="Ee8-FP-yAL"/>
                            <constraint firstItem="ZGb-kp-AKj" firstAttribute="top" relation="greaterThanOrEqual" secondItem="zbQ-j8-1lK" secondAttribute="top" id="fId-OR-K0D"/>
                            <constraint firstItem="zbQ-j8-1lK" firstAttribute="bottom" secondItem="zr9-hx-M5k" secondAttribute="bottom" id="mJZ-BF-XmH"/>
                            <constraint firstAttribute="bottom" secondItem="ZGb-kp-AKj" secondAttribute="bottom" id="pbp-rs-iUC"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isDimmerViewTappable" value="NO"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="contentView" destination="ZGb-kp-AKj" id="Dan-hm-DAH"/>
                        <outlet property="textView" destination="Pun-9t-eQM" id="tPm-zv-MKA"/>
                        <outlet property="textViewHeightConstraint" destination="D4n-ex-MND" id="10N-hO-tkZ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="UR4-eq-Zev" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1935.2" y="7105.0974512743633"/>
        </scene>
        <!--MayaLoginViewController-->
        <scene sceneID="rH8-y4-4EI">
            <objects>
                <viewController storyboardIdentifier="MayaLoginViewController" id="ucm-xK-zZr" userLabel="MayaLoginViewController" customClass="MayaLoginViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="jit-9S-oOQ">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="a56-qz-myY">
                                <rect key="frame" x="0.0" y="20" width="375" height="575"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1Gb-n1-O1y">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="432"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="imageMayaLogo" translatesAutoresizingMaskIntoConstraints="NO" id="HvF-0r-kBA">
                                                <rect key="frame" x="117.5" y="48" width="140" height="40"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" secondItem="HvF-0r-kBA" secondAttribute="height" multiplier="7:2" id="GRj-t2-4Aq"/>
                                                </constraints>
                                            </imageView>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="KHW-Mz-OoU">
                                                <rect key="frame" x="25" y="112" width="325" height="320"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Cpp-dW-hbE" customClass="MayaValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="325" height="128"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="placeholderText" value="Phone number"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="YES"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="e3C-zd-Mur" customClass="MayaValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="140" width="325" height="128"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="hintText" value="Password"/>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="placeholderText" value="Enter password"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="YES"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="I7s-mU-LVq">
                                                        <rect key="frame" x="0.0" y="280" width="325" height="40"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_login_forgot_password_button"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="40" id="qL7-pu-NUF"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal" title="Forgot your password?">
                                                            <color key="titleColor" name="ContentPrimaryGreen"/>
                                                        </state>
                                                        <connections>
                                                            <action selector="didTapForgotPassword:" destination="ucm-xK-zZr" eventType="touchUpInside" id="RrU-rR-CNu"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="KHW-Mz-OoU" firstAttribute="top" secondItem="HvF-0r-kBA" secondAttribute="bottom" constant="24" id="7q6-Ug-spC"/>
                                            <constraint firstAttribute="bottom" secondItem="KHW-Mz-OoU" secondAttribute="bottom" id="R9h-gn-7GC"/>
                                            <constraint firstItem="KHW-Mz-OoU" firstAttribute="leading" secondItem="1Gb-n1-O1y" secondAttribute="leading" constant="25" id="UnV-7e-dQi"/>
                                            <constraint firstAttribute="trailing" secondItem="KHW-Mz-OoU" secondAttribute="trailing" constant="25" id="asZ-YW-OGU"/>
                                            <constraint firstItem="HvF-0r-kBA" firstAttribute="centerX" secondItem="1Gb-n1-O1y" secondAttribute="centerX" id="i8y-FT-ZWg"/>
                                            <constraint firstItem="HvF-0r-kBA" firstAttribute="top" secondItem="1Gb-n1-O1y" secondAttribute="top" constant="48" id="pr3-XH-t3b"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="1Gb-n1-O1y" secondAttribute="trailing" id="nnv-Yp-tTo"/>
                                    <constraint firstItem="1Gb-n1-O1y" firstAttribute="width" secondItem="a56-qz-myY" secondAttribute="width" id="r1A-uc-mUS"/>
                                    <constraint firstItem="1Gb-n1-O1y" firstAttribute="top" secondItem="a56-qz-myY" secondAttribute="top" id="rxv-tU-ouw"/>
                                    <constraint firstItem="1Gb-n1-O1y" firstAttribute="leading" secondItem="a56-qz-myY" secondAttribute="leading" id="tKE-30-fUl"/>
                                    <constraint firstAttribute="bottom" secondItem="1Gb-n1-O1y" secondAttribute="bottom" id="tev-35-XwP"/>
                                </constraints>
                            </scrollView>
                            <button opaque="NO" contentMode="scaleToFill" enabled="NO" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="TVJ-rU-ydG" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="24" y="603" width="327" height="56"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_login_continue_button"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="56" id="CBw-G4-2uf"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="Log in"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                        <integer key="value" value="0"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                        <integer key="value" value="0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yyh-uG-Zyx" userLabel="Spacer View">
                                <rect key="frame" x="0.0" y="659" width="375" height="8"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="8" id="vaC-RB-hxx"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="X2N-Te-dRQ"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="yyh-uG-Zyx" firstAttribute="leading" secondItem="X2N-Te-dRQ" secondAttribute="leading" id="08T-5m-cNR"/>
                            <constraint firstItem="a56-qz-myY" firstAttribute="width" secondItem="jit-9S-oOQ" secondAttribute="width" id="4Wz-8T-L2q"/>
                            <constraint firstItem="X2N-Te-dRQ" firstAttribute="trailing" secondItem="yyh-uG-Zyx" secondAttribute="trailing" id="7zk-eu-efh"/>
                            <constraint firstItem="TVJ-rU-ydG" firstAttribute="leading" secondItem="X2N-Te-dRQ" secondAttribute="leading" constant="24" id="JFo-qJ-k4x"/>
                            <constraint firstItem="X2N-Te-dRQ" firstAttribute="trailing" secondItem="TVJ-rU-ydG" secondAttribute="trailing" constant="24" id="JSA-cw-Ubr"/>
                            <constraint firstItem="a56-qz-myY" firstAttribute="top" secondItem="X2N-Te-dRQ" secondAttribute="top" id="LIf-B4-Om1"/>
                            <constraint firstItem="TVJ-rU-ydG" firstAttribute="top" secondItem="a56-qz-myY" secondAttribute="bottom" constant="8" id="ddt-95-eee"/>
                            <constraint firstItem="a56-qz-myY" firstAttribute="centerX" secondItem="X2N-Te-dRQ" secondAttribute="centerX" id="uoU-SD-8HQ"/>
                            <constraint firstItem="X2N-Te-dRQ" firstAttribute="bottom" secondItem="yyh-uG-Zyx" secondAttribute="bottom" id="w1y-rC-czL"/>
                            <constraint firstItem="yyh-uG-Zyx" firstAttribute="top" secondItem="TVJ-rU-ydG" secondAttribute="bottom" id="wmv-9t-Vx7"/>
                        </constraints>
                        <connections>
                            <outletCollection property="gestureRecognizers" destination="8Vf-2q-lZN" appends="YES" id="73m-rb-rlg"/>
                        </connections>
                    </view>
                    <navigationItem key="navigationItem" id="mvd-Kk-1gz">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="ynx-kn-epB">
                            <connections>
                                <action selector="didTapBack:" destination="ucm-xK-zZr" id="0Xz-0z-Ggu"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isMayaForm" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="continueButton" destination="TVJ-rU-ydG" id="qEe-u5-ZJU"/>
                        <outlet property="numberView" destination="Cpp-dW-hbE" id="91Q-MW-ymO"/>
                        <outlet property="passwordView" destination="e3C-zd-Mur" id="5S3-d4-ald"/>
                        <outlet property="scrollView" destination="a56-qz-myY" id="JkK-e3-4v5"/>
                        <outlet property="stickyViewBottomConstraint" destination="w1y-rC-czL" id="GWe-4H-eFR"/>
                    </connections>
                </viewController>
                <tapGestureRecognizer id="8Vf-2q-lZN">
                    <connections>
                        <action selector="dismissKeyboard:" destination="ucm-xK-zZr" id="tkH-MP-EzE"/>
                    </connections>
                </tapGestureRecognizer>
                <placeholder placeholderIdentifier="IBFirstResponder" id="N3L-4t-pjS" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1935" y="7808"/>
        </scene>
        <!--MayaSessionTimeoutViewController-->
        <scene sceneID="cNf-FI-0dh">
            <objects>
                <viewController storyboardIdentifier="MayaSessionTimeoutViewController" id="4a6-TD-ZM9" userLabel="MayaSessionTimeoutViewController" customClass="MayaSessionTimeoutViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="n4h-lj-2rw">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eXR-QK-gWc">
                                <rect key="frame" x="0.0" y="20" width="375" height="575"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YIq-pe-SHG">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="575"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="imageMayaLogo" translatesAutoresizingMaskIntoConstraints="NO" id="EBl-Ya-nFd">
                                                <rect key="frame" x="117.5" y="60" width="140" height="40"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" secondItem="EBl-Ya-nFd" secondAttribute="height" multiplier="7:2" id="5CO-r6-XxM"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="+63 9478971199" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="16" translatesAutoresizingMaskIntoConstraints="NO" id="Rqc-8u-07r">
                                                <rect key="frame" x="24" y="124" width="327" height="29.5"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_session_timeout_number_label"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                                <color key="textColor" name="ContentPrimaryBlack"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="Whan Woong Kim Stransom" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="6" translatesAutoresizingMaskIntoConstraints="NO" id="lBP-4h-YGF">
                                                <rect key="frame" x="24" y="161.5" width="327" height="17"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_session_timeout_name_label"/>
                                                <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                <color key="textColor" name="ContentGrey6"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="7KT-gu-5Ia">
                                                <rect key="frame" x="25" y="202.5" width="325" height="222.5"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hVL-RX-UiF" customClass="MayaValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="325" height="170.5"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="hintText" value="Password"/>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="placeholderText" value="Enter password"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="YES"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Yx0-ca-TXp">
                                                        <rect key="frame" x="0.0" y="182.5" width="325" height="40"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_session_timeout_forgot_password_button"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="40" id="GLq-Xi-Frq"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal" title="Forgot your password?">
                                                            <color key="titleColor" name="ContentPrimaryGreen"/>
                                                        </state>
                                                        <connections>
                                                            <action selector="didTapForgotPasswordWithSender:" destination="4a6-TD-ZM9" eventType="touchUpInside" id="ykM-d5-rjz"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                            <button opaque="NO" contentMode="scaleToFill" placeholderIntrinsicWidth="160" placeholderIntrinsicHeight="32" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qqA-6J-4Sr" customClass="AdaptableSizeMayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="107.5" y="484" width="160" height="32"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_session_timeout_biometry_button"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="32" id="mD5-Ok-LWT"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="Log in with Face ID"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftHandImage" value="iconFaceId"/>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                        <integer key="value" value="2"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                        <integer key="value" value="2"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="didTapUseBiometricLogin:" destination="4a6-TD-ZM9" eventType="touchUpInside" id="Y4L-SO-IRt"/>
                                                </connections>
                                            </button>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fYT-z1-WMP">
                                                <rect key="frame" x="101" y="536" width="173" height="39"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="Dah-op-l9Z">
                                                        <rect key="frame" x="0.0" y="0.0" width="173" height="39"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Not you?" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="CJt-Q2-1B5">
                                                                <rect key="frame" x="0.0" y="0.0" width="61" height="39"/>
                                                                <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                                <color key="textColor" name="ContentPrimaryBlack"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="leading" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="zxT-5D-oMZ">
                                                                <rect key="frame" x="66" y="0.0" width="107" height="39"/>
                                                                <accessibility key="accessibilityConfiguration" identifier="pmios_session_timeout_switch_account_button"/>
                                                                <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <state key="normal" title="Switch account">
                                                                    <color key="titleColor" name="ContentPrimaryGreen"/>
                                                                </state>
                                                            </button>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="39" id="872-HE-wxt"/>
                                                    <constraint firstAttribute="bottom" secondItem="Dah-op-l9Z" secondAttribute="bottom" id="NkP-xU-Mq8"/>
                                                    <constraint firstItem="Dah-op-l9Z" firstAttribute="leading" secondItem="fYT-z1-WMP" secondAttribute="leading" id="QEP-4O-Zyn"/>
                                                    <constraint firstItem="Dah-op-l9Z" firstAttribute="top" secondItem="fYT-z1-WMP" secondAttribute="top" id="k2m-le-RcI"/>
                                                    <constraint firstAttribute="trailing" secondItem="Dah-op-l9Z" secondAttribute="trailing" id="sJp-kf-ImR"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="7KT-gu-5Ia" firstAttribute="top" secondItem="lBP-4h-YGF" secondAttribute="bottom" constant="24" id="3SJ-q7-fte"/>
                                            <constraint firstItem="qqA-6J-4Sr" firstAttribute="centerX" secondItem="YIq-pe-SHG" secondAttribute="centerX" id="6Zy-Sp-stF"/>
                                            <constraint firstItem="qqA-6J-4Sr" firstAttribute="top" relation="greaterThanOrEqual" secondItem="7KT-gu-5Ia" secondAttribute="bottom" constant="12" id="C1U-zj-BTj"/>
                                            <constraint firstItem="EBl-Ya-nFd" firstAttribute="centerX" secondItem="YIq-pe-SHG" secondAttribute="centerX" id="EHZ-hn-HhP"/>
                                            <constraint firstAttribute="trailing" secondItem="7KT-gu-5Ia" secondAttribute="trailing" constant="25" id="Lvg-Pe-nIJ"/>
                                            <constraint firstItem="lBP-4h-YGF" firstAttribute="trailing" secondItem="Rqc-8u-07r" secondAttribute="trailing" id="MNd-zM-kRD"/>
                                            <constraint firstAttribute="trailing" secondItem="Rqc-8u-07r" secondAttribute="trailing" constant="24" id="OiD-xc-Pyd"/>
                                            <constraint firstItem="7KT-gu-5Ia" firstAttribute="leading" secondItem="YIq-pe-SHG" secondAttribute="leading" constant="25" id="Qea-U3-bOC"/>
                                            <constraint firstItem="Rqc-8u-07r" firstAttribute="leading" secondItem="YIq-pe-SHG" secondAttribute="leading" constant="24" id="aHw-S5-99Q"/>
                                            <constraint firstItem="fYT-z1-WMP" firstAttribute="top" secondItem="qqA-6J-4Sr" secondAttribute="bottom" constant="20" id="cEK-lO-e2B"/>
                                            <constraint firstItem="lBP-4h-YGF" firstAttribute="leading" secondItem="Rqc-8u-07r" secondAttribute="leading" id="dMa-Ax-2Am"/>
                                            <constraint firstItem="fYT-z1-WMP" firstAttribute="centerX" secondItem="YIq-pe-SHG" secondAttribute="centerX" id="hZH-TS-CLC"/>
                                            <constraint firstItem="Rqc-8u-07r" firstAttribute="top" secondItem="EBl-Ya-nFd" secondAttribute="bottom" constant="24" id="lMS-55-qSD"/>
                                            <constraint firstAttribute="bottom" secondItem="fYT-z1-WMP" secondAttribute="bottom" id="oH0-cJ-AaY"/>
                                            <constraint firstItem="lBP-4h-YGF" firstAttribute="top" secondItem="Rqc-8u-07r" secondAttribute="bottom" constant="8" id="uO0-LS-1ws"/>
                                            <constraint firstItem="EBl-Ya-nFd" firstAttribute="top" secondItem="YIq-pe-SHG" secondAttribute="top" constant="60" id="zbi-iu-oPH"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="YIq-pe-SHG" firstAttribute="leading" secondItem="eXR-QK-gWc" secondAttribute="leading" id="2kT-Fq-OV2"/>
                                    <constraint firstAttribute="trailing" secondItem="YIq-pe-SHG" secondAttribute="trailing" id="IhK-im-CxO"/>
                                    <constraint firstItem="YIq-pe-SHG" firstAttribute="width" secondItem="eXR-QK-gWc" secondAttribute="width" id="SO2-Sg-PVj"/>
                                    <constraint firstItem="YIq-pe-SHG" firstAttribute="top" secondItem="eXR-QK-gWc" secondAttribute="top" id="ZfI-t9-BZ1"/>
                                    <constraint firstAttribute="bottom" secondItem="YIq-pe-SHG" secondAttribute="bottom" id="upZ-pw-iNg"/>
                                </constraints>
                            </scrollView>
                            <view userInteractionEnabled="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="spi-Jo-URC" userLabel="Height View">
                                <rect key="frame" x="0.0" y="20" width="375" height="575"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <view userInteractionEnabled="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ebo-NI-6vU" userLabel="Anchor View">
                                <rect key="frame" x="0.0" y="595" width="375" height="72"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="72" id="iQ6-mD-t1q"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="mLV-BK-AoK" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="24" y="603" width="327" height="56"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_session_timeout_login_button"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="56" id="rhm-MY-4ae"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="Log in"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                        <integer key="value" value="0"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                        <integer key="value" value="0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="na4-FT-zL7" userLabel="Spacer View">
                                <rect key="frame" x="0.0" y="659" width="375" height="8"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="8" id="Syy-PI-PIz"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="07u-AD-dQS"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="07u-AD-dQS" firstAttribute="bottom" secondItem="na4-FT-zL7" secondAttribute="bottom" id="0eE-3Y-WqJ"/>
                            <constraint firstItem="mLV-BK-AoK" firstAttribute="leading" secondItem="07u-AD-dQS" secondAttribute="leading" constant="24" id="55R-Sa-Wh9"/>
                            <constraint firstItem="YIq-pe-SHG" firstAttribute="height" secondItem="spi-Jo-URC" secondAttribute="height" id="CDy-1H-e5F"/>
                            <constraint firstItem="mLV-BK-AoK" firstAttribute="top" secondItem="eXR-QK-gWc" secondAttribute="bottom" constant="8" id="JMb-an-ZLp"/>
                            <constraint firstItem="eXR-QK-gWc" firstAttribute="width" secondItem="n4h-lj-2rw" secondAttribute="width" id="JTo-04-KVh"/>
                            <constraint firstItem="07u-AD-dQS" firstAttribute="trailing" secondItem="ebo-NI-6vU" secondAttribute="trailing" id="JuB-g9-6cN"/>
                            <constraint firstItem="07u-AD-dQS" firstAttribute="trailing" secondItem="spi-Jo-URC" secondAttribute="trailing" id="NEd-vC-CaW"/>
                            <constraint firstItem="eXR-QK-gWc" firstAttribute="top" secondItem="07u-AD-dQS" secondAttribute="top" id="P7D-pw-PwT"/>
                            <constraint firstItem="07u-AD-dQS" firstAttribute="trailing" secondItem="mLV-BK-AoK" secondAttribute="trailing" constant="24" id="Ykr-jy-l4g"/>
                            <constraint firstItem="07u-AD-dQS" firstAttribute="trailing" secondItem="na4-FT-zL7" secondAttribute="trailing" id="aPl-kf-woV"/>
                            <constraint firstItem="ebo-NI-6vU" firstAttribute="top" secondItem="spi-Jo-URC" secondAttribute="bottom" id="bIW-sk-XmZ"/>
                            <constraint firstItem="eXR-QK-gWc" firstAttribute="centerX" secondItem="07u-AD-dQS" secondAttribute="centerX" id="cbA-bP-Ocx"/>
                            <constraint firstItem="07u-AD-dQS" firstAttribute="bottom" secondItem="ebo-NI-6vU" secondAttribute="bottom" id="czT-ir-GyY"/>
                            <constraint firstItem="spi-Jo-URC" firstAttribute="leading" secondItem="07u-AD-dQS" secondAttribute="leading" id="iTb-2O-nMt"/>
                            <constraint firstItem="ebo-NI-6vU" firstAttribute="leading" secondItem="07u-AD-dQS" secondAttribute="leading" id="oro-zf-th7"/>
                            <constraint firstItem="na4-FT-zL7" firstAttribute="leading" secondItem="07u-AD-dQS" secondAttribute="leading" id="sag-pb-ykQ"/>
                            <constraint firstItem="spi-Jo-URC" firstAttribute="top" secondItem="07u-AD-dQS" secondAttribute="top" id="weG-TP-9sQ"/>
                            <constraint firstItem="na4-FT-zL7" firstAttribute="top" secondItem="mLV-BK-AoK" secondAttribute="bottom" id="yNO-Mq-5NU"/>
                        </constraints>
                        <connections>
                            <outletCollection property="gestureRecognizers" destination="A59-oB-Q9M" appends="YES" id="5ai-Qh-1F3"/>
                        </connections>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isMayaForm" value="YES"/>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="biometryButton" destination="qqA-6J-4Sr" id="TPu-GT-agJ"/>
                        <outlet property="loginButton" destination="mLV-BK-AoK" id="hva-7Z-uyB"/>
                        <outlet property="logoTopConstraint" destination="zbi-iu-oPH" id="MnP-Uj-NDm"/>
                        <outlet property="nameLabel" destination="lBP-4h-YGF" id="tEU-KH-ztJ"/>
                        <outlet property="numberLabel" destination="Rqc-8u-07r" id="uHb-DM-tuD"/>
                        <outlet property="passwordView" destination="hVL-RX-UiF" id="rfT-q7-MHW"/>
                        <outlet property="scrollView" destination="eXR-QK-gWc" id="ntd-gg-M1a"/>
                        <outlet property="stickyViewBottomConstraint" destination="0eE-3Y-WqJ" id="cHH-GL-J5Z"/>
                        <outlet property="switchAccountButton" destination="zxT-5D-oMZ" id="NCR-50-jwr"/>
                    </connections>
                </viewController>
                <tapGestureRecognizer id="A59-oB-Q9M">
                    <connections>
                        <action selector="dismissKeyboard:" destination="4a6-TD-ZM9" id="D9f-QG-pTb"/>
                    </connections>
                </tapGestureRecognizer>
                <placeholder placeholderIdentifier="IBFirstResponder" id="jC7-LE-eNh" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1266.*************" y="7807.*************"/>
        </scene>
        <!--MayaForgotPasswordViewController-->
        <scene sceneID="a7i-Uo-egd">
            <objects>
                <viewController storyboardIdentifier="MayaForgotPasswordViewController" id="hFU-9C-dkD" userLabel="MayaForgotPasswordViewController" customClass="MayaForgotPasswordViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Uza-N1-pMm">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="px8-6Q-FSl">
                                <rect key="frame" x="0.0" y="20" width="375" height="575"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="v44-CG-6kD">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="184"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="icP-xX-PV6">
                                                <rect key="frame" x="24" y="0.0" width="327" height="184"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Enter your number" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xDb-hy-vKv">
                                                        <rect key="frame" x="0.0" y="0.0" width="327" height="39"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="32"/>
                                                        <color key="textColor" name="PrimaryBlack"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Please use the number registered to your Maya account" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kLg-sX-Sbt">
                                                        <rect key="frame" x="0.0" y="47" width="327" height="37"/>
                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="14"/>
                                                        <color key="textColor" name="Grey6"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nve-0K-d2H" userLabel="Spacer View">
                                                        <rect key="frame" x="0.0" y="92" width="327" height="24"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="24" id="fhN-R1-Q0m"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="gg7-Xx-P3r" userLabel="MobileNumber" customClass="MayaProfileValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="124" width="327" height="60"/>
                                                        <color key="backgroundColor" name="Grey1"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnEndEditing" value="YES"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="NO"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="useBottomErrorIconImageView" value="YES"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="nve-0K-d2H" secondAttribute="trailing" id="1Aa-Q3-VZD"/>
                                                    <constraint firstItem="nve-0K-d2H" firstAttribute="leading" secondItem="icP-xX-PV6" secondAttribute="leading" id="IZX-AZ-KcE"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" name="Grey1"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="icP-xX-PV6" secondAttribute="bottom" id="38U-zL-rFp"/>
                                            <constraint firstItem="icP-xX-PV6" firstAttribute="top" secondItem="v44-CG-6kD" secondAttribute="top" id="Ayb-z8-L4N"/>
                                            <constraint firstAttribute="trailing" secondItem="icP-xX-PV6" secondAttribute="trailing" constant="24" id="wcq-gG-Ctm"/>
                                            <constraint firstItem="icP-xX-PV6" firstAttribute="leading" secondItem="v44-CG-6kD" secondAttribute="leading" constant="24" id="z73-nn-wll"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="v44-CG-6kD" firstAttribute="top" secondItem="px8-6Q-FSl" secondAttribute="top" id="0WA-7t-HwX"/>
                                    <constraint firstAttribute="bottom" secondItem="v44-CG-6kD" secondAttribute="bottom" id="Dz4-wu-eN1"/>
                                    <constraint firstItem="v44-CG-6kD" firstAttribute="leading" secondItem="px8-6Q-FSl" secondAttribute="leading" id="JL7-wA-YUw"/>
                                    <constraint firstItem="v44-CG-6kD" firstAttribute="width" secondItem="px8-6Q-FSl" secondAttribute="width" id="itV-gs-a8I"/>
                                    <constraint firstAttribute="trailing" secondItem="v44-CG-6kD" secondAttribute="trailing" id="lW2-NA-OLi"/>
                                </constraints>
                            </scrollView>
                            <button opaque="NO" contentMode="scaleToFill" enabled="NO" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="UrS-9d-4oL" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="24" y="603" width="327" height="56"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_login_continue_button"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="56" id="gbM-gh-DS6"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="Next"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                        <integer key="value" value="0"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                        <integer key="value" value="0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="didTapNextButton:" destination="hFU-9C-dkD" eventType="touchUpInside" id="0as-ec-WZZ"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Icq-n0-oui" userLabel="Spacer View">
                                <rect key="frame" x="0.0" y="659" width="375" height="8"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="8" id="lTp-TD-cMf"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="gO1-v7-kha"/>
                        <color key="backgroundColor" name="Grey1"/>
                        <constraints>
                            <constraint firstItem="Icq-n0-oui" firstAttribute="top" secondItem="UrS-9d-4oL" secondAttribute="bottom" id="7O4-p0-eoN"/>
                            <constraint firstItem="px8-6Q-FSl" firstAttribute="centerX" secondItem="gO1-v7-kha" secondAttribute="centerX" id="AJ8-yY-GyW"/>
                            <constraint firstItem="gO1-v7-kha" firstAttribute="trailing" secondItem="UrS-9d-4oL" secondAttribute="trailing" constant="24" id="CRA-dx-0R2"/>
                            <constraint firstItem="gO1-v7-kha" firstAttribute="trailing" secondItem="Icq-n0-oui" secondAttribute="trailing" id="JhS-Fi-n15"/>
                            <constraint firstItem="px8-6Q-FSl" firstAttribute="top" secondItem="gO1-v7-kha" secondAttribute="top" id="TVa-hg-LSe"/>
                            <constraint firstItem="px8-6Q-FSl" firstAttribute="width" secondItem="Uza-N1-pMm" secondAttribute="width" id="V7w-EZ-lQW"/>
                            <constraint firstItem="Icq-n0-oui" firstAttribute="leading" secondItem="gO1-v7-kha" secondAttribute="leading" id="WjF-0Y-kLL"/>
                            <constraint firstItem="UrS-9d-4oL" firstAttribute="leading" secondItem="gO1-v7-kha" secondAttribute="leading" constant="24" id="jP8-CA-6d9"/>
                            <constraint firstItem="gO1-v7-kha" firstAttribute="bottom" secondItem="Icq-n0-oui" secondAttribute="bottom" id="kQU-eV-Q1g"/>
                            <constraint firstItem="UrS-9d-4oL" firstAttribute="top" secondItem="px8-6Q-FSl" secondAttribute="bottom" constant="8" id="wmF-ac-pAi"/>
                        </constraints>
                        <connections>
                            <outletCollection property="gestureRecognizers" destination="IJH-mv-uSR" appends="YES" id="0ZZ-3q-cfV"/>
                        </connections>
                    </view>
                    <navigationItem key="navigationItem" id="fZx-I8-yw6">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="Vrg-2c-ej5">
                            <connections>
                                <action selector="didTapBack:" destination="hFU-9C-dkD" id="rKT-iu-DgH"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isMayaForm" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="mobileNumberView" destination="gg7-Xx-P3r" id="Kqt-sF-t2a"/>
                        <outlet property="nextButton" destination="UrS-9d-4oL" id="W0L-vz-JKQ"/>
                        <outlet property="scrollView" destination="px8-6Q-FSl" id="RhZ-Gm-8TU"/>
                        <outlet property="stickyViewBottomConstraint" destination="kQU-eV-Q1g" id="lkT-f3-SEZ"/>
                        <outlet property="subTitleLabel" destination="kLg-sX-Sbt" id="Rqv-i1-Mhc"/>
                        <outlet property="titleLabel" destination="xDb-hy-vKv" id="Qh8-ul-eO6"/>
                    </connections>
                </viewController>
                <tapGestureRecognizer id="IJH-mv-uSR">
                    <connections>
                        <action selector="dismissKeyboard:" destination="hFU-9C-dkD" id="hjR-2L-PUl"/>
                    </connections>
                </tapGestureRecognizer>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Wfs-Qw-Twp" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-2295" y="8530"/>
        </scene>
        <!--MayaSetNewPasswordViewController-->
        <scene sceneID="LcG-Lk-JDa">
            <objects>
                <viewController storyboardIdentifier="MayaSetNewPasswordViewController" id="pHS-3I-k3d" userLabel="MayaSetNewPasswordViewController" customClass="MayaSetNewPasswordViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="IxH-RB-TeQ">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZhX-z9-IhL">
                                <rect key="frame" x="0.0" y="20" width="375" height="575"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dSK-ow-7Nl">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="304"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="jDc-xj-qnH">
                                                <rect key="frame" x="24" y="0.0" width="327" height="292"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Set a new password" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9UU-Df-MVc">
                                                        <rect key="frame" x="0.0" y="0.0" width="327" height="39"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="32"/>
                                                        <color key="textColor" name="PrimaryBlack"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="This will be used for login and account recovery" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gyk-P4-Sl0">
                                                        <rect key="frame" x="0.0" y="47" width="327" height="18.5"/>
                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="14"/>
                                                        <color key="textColor" name="Grey6"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Tips for a secure password" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="g1b-eF-DHZ">
                                                        <rect key="frame" x="0.0" y="73.5" width="327" height="18.5"/>
                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Bold" family="Cerebri Sans Pro" pointSize="14"/>
                                                        <color key="textColor" name="InputPrimaryGreen"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2q6-SH-acH" userLabel="Spacer View">
                                                        <rect key="frame" x="0.0" y="100" width="327" height="32"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="32" id="aR4-JM-TMr"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="a9G-Hj-fRJ" userLabel="MobileNumber" customClass="MayaProfileValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="140" width="327" height="60"/>
                                                        <color key="backgroundColor" name="Grey1"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnEndEditing" value="YES"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="NO"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="useBottomErrorIconImageView" value="YES"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="n8g-y7-2VE" userLabel="Spacer View">
                                                        <rect key="frame" x="0.0" y="208" width="327" height="16"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="16" id="tUj-6G-nGN"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Qlw-Ps-NdC" userLabel="MobileNumber" customClass="MayaProfileValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="232" width="327" height="60"/>
                                                        <color key="backgroundColor" name="Grey1"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnEndEditing" value="YES"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="NO"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="useBottomErrorIconImageView" value="YES"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="2q6-SH-acH" firstAttribute="leading" secondItem="jDc-xj-qnH" secondAttribute="leading" id="J20-ux-cTu"/>
                                                    <constraint firstAttribute="trailing" secondItem="2q6-SH-acH" secondAttribute="trailing" id="PAT-9c-gxQ"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" name="Grey1"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="jDc-xj-qnH" secondAttribute="trailing" constant="24" id="Poc-Ci-aLd"/>
                                            <constraint firstItem="jDc-xj-qnH" firstAttribute="leading" secondItem="dSK-ow-7Nl" secondAttribute="leading" constant="24" id="Ys6-C5-S6P"/>
                                            <constraint firstItem="jDc-xj-qnH" firstAttribute="top" secondItem="dSK-ow-7Nl" secondAttribute="top" id="imO-08-h0L"/>
                                            <constraint firstAttribute="bottom" secondItem="jDc-xj-qnH" secondAttribute="bottom" constant="12" id="pUy-73-4tF"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="dSK-ow-7Nl" firstAttribute="width" secondItem="ZhX-z9-IhL" secondAttribute="width" id="3aW-3C-I4p"/>
                                    <constraint firstAttribute="bottom" secondItem="dSK-ow-7Nl" secondAttribute="bottom" id="DVP-19-D5c"/>
                                    <constraint firstAttribute="trailing" secondItem="dSK-ow-7Nl" secondAttribute="trailing" id="HKe-Lh-M5r"/>
                                    <constraint firstItem="dSK-ow-7Nl" firstAttribute="top" secondItem="ZhX-z9-IhL" secondAttribute="top" id="Lc0-qc-NYa"/>
                                    <constraint firstItem="dSK-ow-7Nl" firstAttribute="leading" secondItem="ZhX-z9-IhL" secondAttribute="leading" id="YNw-D8-Jko"/>
                                </constraints>
                            </scrollView>
                            <button opaque="NO" contentMode="scaleToFill" enabled="NO" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="UnD-p2-xmO" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="24" y="603" width="327" height="56"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_login_continue_button"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="56" id="OKu-64-yJZ"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="Next"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                        <integer key="value" value="0"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                        <integer key="value" value="0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="didTapNext:" destination="pHS-3I-k3d" eventType="touchUpInside" id="XCh-G4-AQn"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="z2P-KK-EyV" userLabel="Spacer View">
                                <rect key="frame" x="0.0" y="659" width="375" height="8"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="8" id="ZyR-2f-06W"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="yUL-Ww-YBC"/>
                        <color key="backgroundColor" name="Grey1"/>
                        <constraints>
                            <constraint firstItem="ZhX-z9-IhL" firstAttribute="width" secondItem="IxH-RB-TeQ" secondAttribute="width" id="04g-18-cPA"/>
                            <constraint firstItem="yUL-Ww-YBC" firstAttribute="trailing" secondItem="UnD-p2-xmO" secondAttribute="trailing" constant="24" id="0VL-xw-3Zq"/>
                            <constraint firstItem="UnD-p2-xmO" firstAttribute="top" secondItem="ZhX-z9-IhL" secondAttribute="bottom" constant="8" id="2Qa-LZ-JkH"/>
                            <constraint firstItem="ZhX-z9-IhL" firstAttribute="centerX" secondItem="yUL-Ww-YBC" secondAttribute="centerX" id="4Bk-Vv-dKd"/>
                            <constraint firstItem="UnD-p2-xmO" firstAttribute="leading" secondItem="yUL-Ww-YBC" secondAttribute="leading" constant="24" id="6BX-qT-oZH"/>
                            <constraint firstItem="ZhX-z9-IhL" firstAttribute="top" secondItem="yUL-Ww-YBC" secondAttribute="top" id="E7e-yn-WkI"/>
                            <constraint firstItem="yUL-Ww-YBC" firstAttribute="bottom" secondItem="z2P-KK-EyV" secondAttribute="bottom" id="PcP-Df-QVv"/>
                            <constraint firstItem="z2P-KK-EyV" firstAttribute="leading" secondItem="yUL-Ww-YBC" secondAttribute="leading" id="WWO-Av-ucz"/>
                            <constraint firstItem="z2P-KK-EyV" firstAttribute="top" secondItem="UnD-p2-xmO" secondAttribute="bottom" id="fCX-em-sxO"/>
                            <constraint firstItem="yUL-Ww-YBC" firstAttribute="trailing" secondItem="z2P-KK-EyV" secondAttribute="trailing" id="rbQ-T6-eCg"/>
                        </constraints>
                        <connections>
                            <outletCollection property="gestureRecognizers" destination="HF5-mX-QGf" appends="YES" id="LvL-N0-7m2"/>
                        </connections>
                    </view>
                    <navigationItem key="navigationItem" id="kmp-Q5-p8y">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="PyE-84-c4n">
                            <connections>
                                <action selector="didTapBack:" destination="pHS-3I-k3d" id="OJq-FC-c5C"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isMayaForm" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="confirmPasswordView" destination="Qlw-Ps-NdC" id="a0l-UG-euM"/>
                        <outlet property="newPasswordView" destination="a9G-Hj-fRJ" id="1Fg-Dc-dhy"/>
                        <outlet property="scrollView" destination="ZhX-z9-IhL" id="ky3-6q-6hH"/>
                        <outlet property="stickyViewBottomConstraint" destination="PcP-Df-QVv" id="rqU-ma-Oto"/>
                        <outlet property="subTitleLabel" destination="gyk-P4-Sl0" id="LGT-e7-TAe"/>
                        <outlet property="submitPasswordButton" destination="UnD-p2-xmO" id="7Y6-q8-132"/>
                        <outlet property="tipsLabel" destination="g1b-eF-DHZ" id="FKi-uL-gYK"/>
                        <outlet property="titleLabel" destination="9UU-Df-MVc" id="03n-Uo-5eb"/>
                    </connections>
                </viewController>
                <tapGestureRecognizer id="HF5-mX-QGf">
                    <connections>
                        <action selector="dismissKeyboard:" destination="pHS-3I-k3d" id="nso-Ip-kbd"/>
                    </connections>
                </tapGestureRecognizer>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Buf-6T-GwS" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-921" y="8530"/>
        </scene>
        <!--MayaPasswordChangedViewController-->
        <scene sceneID="SsL-4v-8H3">
            <objects>
                <viewController storyboardIdentifier="MayaPasswordChangedViewController" id="DgA-Rh-kKJ" userLabel="MayaPasswordChangedViewController" customClass="MayaPasswordChangedViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="hTh-pH-K63">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ou1-w7-jpy">
                                <rect key="frame" x="0.0" y="20" width="375" height="567"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bsq-Ab-wEL">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="508.5"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="image3DSuccess" translatesAutoresizingMaskIntoConstraints="NO" id="ynB-jN-Six">
                                                <rect key="frame" x="0.0" y="24" width="200" height="200"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="200" id="ocJ-ju-aTz"/>
                                                    <constraint firstAttribute="height" constant="200" id="vog-bI-4c7"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Password changed" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="2lm-nt-XFo">
                                                <rect key="frame" x="24" y="242" width="327" height="39"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="32"/>
                                                <color key="textColor" name="PrimaryBlack"/>
                                                <nil key="highlightedColor"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="highlightPhrase" value="here."/>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="highlightColor">
                                                        <color key="value" name="PrimaryGrownGreen"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="You can now login with your new password" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LTZ-AM-UCI">
                                                <rect key="frame" x="24" y="297" width="327" height="21"/>
                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="16"/>
                                                <color key="textColor" name="Grey6"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="J2Z-C1-dRa">
                                                <rect key="frame" x="24" y="334" width="327" height="150.5"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Remember this tip 💡" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yuc-Tq-f75">
                                                        <rect key="frame" x="12" y="12" width="303" height="18.5"/>
                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Bold" family="Cerebri Sans Pro" pointSize="14"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="Jtc-vo-Q6x">
                                                        <rect key="frame" x="12" y="42.5" width="303" height="96"/>
                                                        <subviews>
                                                            <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Maya's official representatives will never ask for your:" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SlD-SV-k3G">
                                                                <rect key="frame" x="0.0" y="0.0" width="303" height="37"/>
                                                                <gestureRecognizers/>
                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="14"/>
                                                                <color key="textColor" name="Grey6"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="highlightColor">
                                                                        <color key="value" red="1" green="0.56862745100000001" blue="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                            <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QFv-jR-rjh">
                                                                <rect key="frame" x="0.0" y="41" width="303" height="55"/>
                                                                <gestureRecognizers/>
                                                                <string key="text">•  Password
•  16-digit card number or CVV
•  One-Time Pin </string>
                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="14"/>
                                                                <color key="textColor" name="Grey6"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="highlightColor">
                                                                        <color key="value" red="1" green="0.56862745100000001" blue="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" name="FFFFFF"/>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="Jtc-vo-Q6x" secondAttribute="trailing" constant="12" id="0W4-cD-qYV"/>
                                                    <constraint firstItem="yuc-Tq-f75" firstAttribute="leading" secondItem="J2Z-C1-dRa" secondAttribute="leading" constant="12" id="7Hh-9Q-ZBa"/>
                                                    <constraint firstItem="Jtc-vo-Q6x" firstAttribute="top" secondItem="yuc-Tq-f75" secondAttribute="bottom" constant="12" id="8Gp-Pg-788"/>
                                                    <constraint firstAttribute="bottom" secondItem="Jtc-vo-Q6x" secondAttribute="bottom" constant="12" id="fU8-OP-Pgv"/>
                                                    <constraint firstAttribute="trailing" secondItem="yuc-Tq-f75" secondAttribute="trailing" constant="12" id="hZw-eC-Ozk"/>
                                                    <constraint firstItem="Jtc-vo-Q6x" firstAttribute="leading" secondItem="yuc-Tq-f75" secondAttribute="leading" id="m1m-Sl-mIW"/>
                                                    <constraint firstItem="yuc-Tq-f75" firstAttribute="top" secondItem="J2Z-C1-dRa" secondAttribute="top" constant="12" id="vfM-Ga-oEx"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" name="Grey1"/>
                                        <constraints>
                                            <constraint firstItem="2lm-nt-XFo" firstAttribute="leading" secondItem="bsq-Ab-wEL" secondAttribute="leading" constant="24" id="6g8-b3-HCE"/>
                                            <constraint firstAttribute="bottom" secondItem="J2Z-C1-dRa" secondAttribute="bottom" constant="24" id="6x1-b4-96i"/>
                                            <constraint firstItem="J2Z-C1-dRa" firstAttribute="leading" secondItem="bsq-Ab-wEL" secondAttribute="leading" constant="24" id="88r-sJ-rpD"/>
                                            <constraint firstItem="ynB-jN-Six" firstAttribute="leading" secondItem="bsq-Ab-wEL" secondAttribute="leading" id="Hg6-0d-K2O"/>
                                            <constraint firstAttribute="trailing" secondItem="LTZ-AM-UCI" secondAttribute="trailing" constant="24" id="IB8-SQ-BA4"/>
                                            <constraint firstItem="ynB-jN-Six" firstAttribute="top" secondItem="bsq-Ab-wEL" secondAttribute="top" constant="24" id="Kd5-QV-Qg4"/>
                                            <constraint firstAttribute="trailing" secondItem="2lm-nt-XFo" secondAttribute="trailing" constant="24" id="Tz8-4X-r1S"/>
                                            <constraint firstItem="LTZ-AM-UCI" firstAttribute="leading" secondItem="bsq-Ab-wEL" secondAttribute="leading" constant="24" id="VO1-2J-AqH"/>
                                            <constraint firstItem="J2Z-C1-dRa" firstAttribute="top" secondItem="LTZ-AM-UCI" secondAttribute="bottom" constant="16" id="XsL-Ao-40L"/>
                                            <constraint firstItem="2lm-nt-XFo" firstAttribute="top" secondItem="ynB-jN-Six" secondAttribute="bottom" constant="18" id="gHl-Gi-Bu8"/>
                                            <constraint firstItem="LTZ-AM-UCI" firstAttribute="top" secondItem="2lm-nt-XFo" secondAttribute="bottom" constant="16" id="gHx-hk-xIu"/>
                                            <constraint firstAttribute="trailing" secondItem="J2Z-C1-dRa" secondAttribute="trailing" constant="24" id="sln-WY-h4q"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" name="Grey1"/>
                                <constraints>
                                    <constraint firstItem="bsq-Ab-wEL" firstAttribute="top" secondItem="YjZ-YK-hgq" secondAttribute="top" id="GSJ-or-hCh"/>
                                    <constraint firstItem="bsq-Ab-wEL" firstAttribute="trailing" secondItem="8Xg-dS-7Rd" secondAttribute="trailing" id="H4d-FW-JT0"/>
                                    <constraint firstItem="bsq-Ab-wEL" firstAttribute="leading" secondItem="YjZ-YK-hgq" secondAttribute="leading" id="JEv-zc-8wk"/>
                                    <constraint firstItem="bsq-Ab-wEL" firstAttribute="leading" secondItem="8Xg-dS-7Rd" secondAttribute="leading" id="OX8-in-Prs"/>
                                    <constraint firstItem="bsq-Ab-wEL" firstAttribute="trailing" secondItem="YjZ-YK-hgq" secondAttribute="trailing" id="gNn-l3-tuH"/>
                                    <constraint firstItem="bsq-Ab-wEL" firstAttribute="bottom" secondItem="YjZ-YK-hgq" secondAttribute="bottom" id="lfk-Wz-TgK"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="YjZ-YK-hgq"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="8Xg-dS-7Rd"/>
                            </scrollView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xd7-5l-mxe">
                                <rect key="frame" x="24" y="587" width="327" height="64"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="a5n-D0-pA2" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="8" width="327" height="56"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Log In"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="didTapLogin:" destination="DgA-Rh-kKJ" eventType="touchUpInside" id="JXK-bu-cmR"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="a5n-D0-pA2" firstAttribute="top" secondItem="xd7-5l-mxe" secondAttribute="top" constant="8" id="1pM-cx-55o"/>
                                    <constraint firstAttribute="bottom" secondItem="a5n-D0-pA2" secondAttribute="bottom" id="Glq-qG-Zpl"/>
                                    <constraint firstItem="a5n-D0-pA2" firstAttribute="leading" secondItem="xd7-5l-mxe" secondAttribute="leading" id="NX6-fj-39a"/>
                                    <constraint firstAttribute="height" constant="64" id="pt5-eY-Cte"/>
                                    <constraint firstAttribute="trailing" secondItem="a5n-D0-pA2" secondAttribute="trailing" id="zzi-CN-EgX"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="kby-AK-3A7"/>
                        <color key="backgroundColor" name="Grey1"/>
                        <constraints>
                            <constraint firstItem="kby-AK-3A7" firstAttribute="trailing" secondItem="xd7-5l-mxe" secondAttribute="trailing" constant="24" id="HgE-8n-EYx"/>
                            <constraint firstItem="xd7-5l-mxe" firstAttribute="bottom" secondItem="kby-AK-3A7" secondAttribute="bottom" constant="-16" id="Ij0-e8-ubH"/>
                            <constraint firstItem="Ou1-w7-jpy" firstAttribute="top" secondItem="kby-AK-3A7" secondAttribute="top" id="OY6-TL-Eo1"/>
                            <constraint firstItem="xd7-5l-mxe" firstAttribute="top" secondItem="Ou1-w7-jpy" secondAttribute="bottom" id="SLD-rp-Zyc"/>
                            <constraint firstItem="kby-AK-3A7" firstAttribute="trailing" secondItem="Ou1-w7-jpy" secondAttribute="trailing" id="V55-gM-7yD"/>
                            <constraint firstItem="Ou1-w7-jpy" firstAttribute="leading" secondItem="kby-AK-3A7" secondAttribute="leading" id="xI2-fC-ZRi"/>
                            <constraint firstItem="xd7-5l-mxe" firstAttribute="leading" secondItem="kby-AK-3A7" secondAttribute="leading" constant="24" id="zaX-eh-h5m"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarBackButtonHidden" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="subtitleLabel" destination="LTZ-AM-UCI" id="8lq-xE-luc"/>
                        <outlet property="successImageView" destination="ynB-jN-Six" id="wFW-LE-AG2"/>
                        <outlet property="tipsContainerView" destination="J2Z-C1-dRa" id="psz-by-tTt"/>
                        <outlet property="tipsListLabel" destination="QFv-jR-rjh" id="4bE-fY-3OW"/>
                        <outlet property="tipsSubtitleLabel" destination="SlD-SV-k3G" id="li7-sn-R33"/>
                        <outlet property="tipsTitleLabel" destination="yuc-Tq-f75" id="yNK-O5-sq2"/>
                        <outlet property="titleLabel" destination="2lm-nt-XFo" id="u3q-h4-Z8M"/>
                    </connections>
                </viewController>
                <tapGestureRecognizer id="Qz8-nD-Ayx"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="OPW-Cg-UdO" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-250" y="8530"/>
        </scene>
        <!--Maya Prominent DisclosureV2 View Controller-->
        <scene sceneID="WJq-5Y-D06">
            <objects>
                <viewController storyboardIdentifier="MayaProminentDisclosureV2ViewController" id="Gpp-nY-vxk" customClass="MayaProminentDisclosureV2ViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="4cj-VB-z4d">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ClT-s3-OeI">
                                <rect key="frame" x="0.0" y="271" width="375" height="396"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="qb8-2K-bA0">
                                        <rect key="frame" x="24" y="32" width="327" height="269"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="k3j-D2-cVJ">
                                                <rect key="frame" x="0.0" y="0.0" width="327" height="87"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Welcome to Maya 👋" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rEZ-3v-hHF">
                                                        <rect key="frame" x="0.0" y="0.0" width="327" height="29.5"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_disclosure_title_label"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="B4Y-KM-vxu">
                                                        <rect key="frame" x="0.0" y="39.5" width="327" height="47.5"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_disclosure_title_label"/>
                                                        <string key="text">We collect and protect your personal and device info to keep your account secure and the app running smoothly – just as it should.</string>
                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="12"/>
                                                        <color key="textColor" name="ContentGrey6"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" editable="NO" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="gFt-eh-qPE">
                                                <rect key="frame" x="0.0" y="99" width="327" height="170"/>
                                                <color key="backgroundColor" name="Grey1"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_disclosure_description_textview"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="170" id="Xgs-am-8vk"/>
                                                </constraints>
                                                <string key="text">Data Privacy
We, at Maya, value your right to privacy and we are committed to inform you of what data we collect and how we process and protect your information.
We have updated the Maya Privacy Statement and below are the key things that you need to know from this update:
Key Highlights
In order to safeguard your account, customize our services and personalize your user experience, Maya will collect information about the device you use to access our products and services, such as:
Device attributes: information such as the operating system, hardware and software versions, battery level, signal strength, available storage space, browser type, and installed applications.
Device identifiers: information such as device IDs and other unique identifiers.
Device signals: information about Bluetooth signals and nearby wi-fi access points, hotspots, and cell towers.
Data from network connections: information such as the name of your mobile network operator or Internet service provider, your mobile number, language, time zone, IP address, connection speed, and information about any devices that are nearby or on your network.
Data from device settings: information that you allow Maya to receive through device settings you turn on, such as access to your GPS location, contacts, camera, or photos.
Installed applications 
With your express consent and as allowed by your device settings, we may collect a list of applications installed on your device. This information is used to help verify your identity, detect potential fraud, and tailor the services or features we provide. We do not access or collect any content within these applications.
Who we share your information with
Maya may share your information with its security partners which provide information and services to protect users against fraud and abuse. For example, information about security threats can help us notify you if we think your account has been compromised (at which point we can help you take steps to protect your account).
All of these are intended to protect your account in line with our risk management, fraud prevention, and security practices.
By tapping “Agree and Continue”, you acknowledge that you have been informed about this update and you agree to Maya’s processing of your information.</string>
                                                <color key="textColor" name="ContentGrey6"/>
                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="12"/>
                                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                            </textView>
                                        </subviews>
                                    </stackView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="W6A-iz-nj8">
                                        <rect key="frame" x="0.0" y="306" width="375" height="90"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="btO-fO-BdG" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="10" width="327" height="56"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_disclosure_agree_button"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="d3x-d1-rH2"/>
                                                </constraints>
                                                <state key="normal" title="Agree and continue"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="didTapAgreeAndContinue:" destination="Gpp-nY-vxk" eventType="touchUpInside" id="043-sC-ab3"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="btO-fO-BdG" firstAttribute="leading" secondItem="W6A-iz-nj8" secondAttribute="leading" constant="24" id="9Fa-7b-Am5"/>
                                            <constraint firstAttribute="trailing" secondItem="btO-fO-BdG" secondAttribute="trailing" constant="24" id="Hvu-Lm-SqA"/>
                                            <constraint firstAttribute="bottom" secondItem="btO-fO-BdG" secondAttribute="bottom" constant="24" id="YS8-qM-F88"/>
                                            <constraint firstItem="btO-fO-BdG" firstAttribute="top" secondItem="W6A-iz-nj8" secondAttribute="top" constant="10" id="xF2-sd-nYR"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="W6A-iz-nj8" firstAttribute="leading" secondItem="ClT-s3-OeI" secondAttribute="leading" id="BIf-ac-SM2"/>
                                    <constraint firstItem="qb8-2K-bA0" firstAttribute="top" secondItem="ClT-s3-OeI" secondAttribute="top" constant="32" id="Oef-Fe-C3U"/>
                                    <constraint firstAttribute="trailing" secondItem="qb8-2K-bA0" secondAttribute="trailing" constant="24" id="TTz-5d-IgZ"/>
                                    <constraint firstAttribute="trailing" secondItem="W6A-iz-nj8" secondAttribute="trailing" id="Wqi-30-Rys"/>
                                    <constraint firstItem="W6A-iz-nj8" firstAttribute="top" secondItem="qb8-2K-bA0" secondAttribute="bottom" constant="5" id="rE7-2F-s9M"/>
                                    <constraint firstItem="qb8-2K-bA0" firstAttribute="leading" secondItem="ClT-s3-OeI" secondAttribute="leading" constant="24" id="zbN-cq-pGM"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="HG4-Al-vnx"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="ClT-s3-OeI" firstAttribute="leading" secondItem="HG4-Al-vnx" secondAttribute="leading" id="5Yt-pY-rvh"/>
                            <constraint firstAttribute="bottom" secondItem="ClT-s3-OeI" secondAttribute="bottom" id="9TM-94-Qg7"/>
                            <constraint firstItem="HG4-Al-vnx" firstAttribute="bottom" secondItem="W6A-iz-nj8" secondAttribute="bottom" id="NST-VD-mva"/>
                            <constraint firstItem="ClT-s3-OeI" firstAttribute="top" relation="greaterThanOrEqual" secondItem="HG4-Al-vnx" secondAttribute="top" id="P8P-0C-PLx"/>
                            <constraint firstItem="HG4-Al-vnx" firstAttribute="trailing" secondItem="ClT-s3-OeI" secondAttribute="trailing" id="eb4-uE-LSM"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isDimmerViewTappable" value="NO"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="contentView" destination="ClT-s3-OeI" id="n5x-xm-6Bh"/>
                        <outlet property="descriptionLabel" destination="B4Y-KM-vxu" id="ijQ-44-ckz"/>
                        <outlet property="textView" destination="gFt-eh-qPE" id="LBI-Ep-b8v"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="qHA-RB-8JJ" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1214" y="7105"/>
        </scene>
    </scenes>
    <resources>
        <image name="iconBack" width="22" height="17"/>
        <image name="iconFaceId" width="24" height="24"/>
        <image name="iconInfo" width="17" height="17"/>
        <image name="iconInfoFilled" width="20" height="20"/>
        <image name="image3DSuccess" width="100" height="100"/>
        <image name="imageMayaLogo" width="140" height="40"/>
        <namedColor name="ButtonPrimaryGreen">
            <color red="0.0" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentGrey4">
            <color red="0.69411764705882351" green="0.70196078431372544" blue="0.70980392156862748" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentGrey6">
            <color red="0.33333333333333331" green="0.33725490196078434" blue="0.34901960784313724" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentPrimaryBlack">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentPrimaryGreen">
            <color red="0.0" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="FFFFFF">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey1">
            <color red="0.97647058823529409" green="0.97647058823529409" blue="0.98039215686274506" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey5">
            <color red="0.50196078431372548" green="0.50196078431372548" blue="0.50196078431372548" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey6">
            <color red="0.33333333333333331" green="0.33725490196078434" blue="0.34901960784313724" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="InputPrimaryGreen">
            <color red="0.0" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryBlack">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryGrownGreen">
            <color red="0.0" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
