<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="CerebriSansPro-Regular.otf">
            <string>CerebriSansPro-Regular</string>
        </array>
        <array key="Jeko-Bold.otf">
            <string>Jeko-Bold</string>
        </array>
        <array key="Jeko-SemiBold.otf">
            <string>Jeko-SemiBold</string>
        </array>
    </customFonts>
    <scenes>
        <!--Registration Form V2-->
        <scene sceneID="rUx-av-LdX">
            <objects>
                <viewController storyboardIdentifier="MayaRegistrationFormV2Controller" id="qsB-gZ-sbT" userLabel="Registration Form V2" customClass="MayaRegistrationFormV2Controller" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="lYP-H8-7Sb">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aUS-tT-6yu">
                                <rect key="frame" x="0.0" y="59" width="393" height="743"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hfQ-Kr-c8l">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="715"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="Uqi-fK-e69">
                                                <rect key="frame" x="24" y="24" width="345" height="667"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="253" text="Create an account" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6CC-Xl-9M5">
                                                        <rect key="frame" x="0.0" y="0.0" width="345" height="39"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_registration_page1_title_label"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="32"/>
                                                        <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MTi-H2-vZt" userLabel="First Name" customClass="MayaProfileValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="55" width="345" height="60"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnEndEditing" value="NO"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="NO"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="useBottomErrorIconImageView" value="YES"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnInitialState" value="NO"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="taf-8i-XdN">
                                                        <rect key="frame" x="0.0" y="131" width="345" height="90"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jtE-ao-hGt" userLabel="Middle Name" customClass="MayaProfileValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="345" height="60"/>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnEndEditing" value="NO"/>
                                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="NO"/>
                                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="useBottomErrorIconImageView" value="YES"/>
                                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnInitialState" value="NO"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="WLM-kS-PWR" userLabel="Middle Name Checkbox Stack View">
                                                                <rect key="frame" x="0.0" y="66" width="345" height="24"/>
                                                                <subviews>
                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3Ex-bf-QJ5" customClass="MayaCheckBox" customModule="PayMaya" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="0.0" width="24" height="24"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="24" id="72n-q9-jbz"/>
                                                                            <constraint firstAttribute="width" constant="24" id="uTS-ST-NkY"/>
                                                                        </constraints>
                                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                        <connections>
                                                                            <action selector="didTapNoMiddleNameCheckbox:" destination="qsB-gZ-sbT" eventType="touchUpInside" id="MRq-28-z5X"/>
                                                                        </connections>
                                                                    </button>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="I have no legal middle name" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DOo-Z8-MxI">
                                                                        <rect key="frame" x="36" y="0.0" width="309" height="24"/>
                                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="14"/>
                                                                        <color key="textColor" name="Grey6"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <directionalEdgeInsets key="directionalLayoutMargins" top="0.0" leading="0.0" bottom="0.0" trailing="0.0"/>
                                                            </stackView>
                                                        </subviews>
                                                    </stackView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="KKB-kG-1FL" userLabel="Last Name" customClass="MayaProfileValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="237" width="345" height="60"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnEndEditing" value="NO"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="NO"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="useBottomErrorIconImageView" value="YES"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnInitialState" value="NO"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eKM-xz-67u">
                                                        <rect key="frame" x="0.0" y="313" width="345" height="7"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="j42-k7-q3z">
                                                                <rect key="frame" x="0.0" y="0.0" width="345" height="1"/>
                                                                <color key="backgroundColor" name="Grey3"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="92W-57-aaT"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="bottom" secondItem="j42-k7-q3z" secondAttribute="bottom" constant="6" id="Fms-sL-0pa"/>
                                                            <constraint firstItem="j42-k7-q3z" firstAttribute="leading" secondItem="eKM-xz-67u" secondAttribute="leading" id="Ohp-Xk-y4c"/>
                                                            <constraint firstAttribute="trailing" secondItem="j42-k7-q3z" secondAttribute="trailing" id="pLT-T7-J6Q"/>
                                                            <constraint firstAttribute="height" constant="7" id="zgt-xM-W6k"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CWZ-TV-1c7" userLabel="Email Address" customClass="MayaProfileValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="336" width="345" height="60"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnEndEditing" value="NO"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="NO"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="useBottomErrorIconImageView" value="YES"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnInitialState" value="NO"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jaf-au-7Xa">
                                                        <rect key="frame" x="0.0" y="412" width="345" height="7"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2wa-vu-Mp7">
                                                                <rect key="frame" x="0.0" y="0.0" width="345" height="1"/>
                                                                <color key="backgroundColor" name="Grey3"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="kLG-IM-THa"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="2wa-vu-Mp7" secondAttribute="trailing" id="7T3-ZD-Fkt"/>
                                                            <constraint firstAttribute="height" constant="7" id="Gua-P2-C8P"/>
                                                            <constraint firstAttribute="bottom" secondItem="2wa-vu-Mp7" secondAttribute="bottom" constant="6" id="T9L-O1-mug"/>
                                                            <constraint firstItem="2wa-vu-Mp7" firstAttribute="leading" secondItem="jaf-au-7Xa" secondAttribute="leading" id="utT-N8-HlR"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Kie-yz-8HQ" userLabel="Mobile Number " customClass="MayaProfileValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="435" width="345" height="60"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="placeholderText" value="Enter a contact number"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnEndEditing" value="NO"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="NO"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="useBottomErrorIconImageView" value="YES"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnInitialState" value="NO"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="Llt-Fu-vHl">
                                                        <rect key="frame" x="0.0" y="510.99999999999994" width="345" height="82.333333333333314"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Z3F-Df-1ZL" userLabel="Password" customClass="MayaProfileValidationFieldView" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="345" height="60"/>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="placeholderText" value="Enter a contact number"/>
                                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnEndEditing" value="NO"/>
                                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isCopyPasteEnabled" value="NO"/>
                                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="useBottomErrorIconImageView" value="YES"/>
                                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="shouldValidateOnInitialState" value="NO"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                            <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="👌 Strong password tips" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Tf9-XZ-NbE">
                                                                <rect key="frame" x="0.0" y="64" width="345" height="14.333333333333329"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                                <color key="textColor" name="00A651"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="L4R-vZ-rhu">
                                                        <rect key="frame" x="0.0" y="587" width="345" height="8"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="8" id="fxm-li-YUk"/>
                                                        </constraints>
                                                    </view>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="5TJ-7l-IKx" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="611" width="345" height="56"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_registration_page1_continue_button"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="56" id="mnS-DB-4dg"/>
                                                        </constraints>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal" title="Continue"/>
                                                        <connections>
                                                            <action selector="didTapContinue:" destination="qsB-gZ-sbT" eventType="touchUpInside" id="8S0-qN-JuV"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="Uqi-fK-e69" secondAttribute="bottom" constant="24" id="A6o-tq-Dkh"/>
                                            <constraint firstItem="Uqi-fK-e69" firstAttribute="leading" secondItem="hfQ-Kr-c8l" secondAttribute="leading" constant="24" id="Dsi-oX-B5a"/>
                                            <constraint firstAttribute="trailing" secondItem="Uqi-fK-e69" secondAttribute="trailing" constant="24" id="FRo-kx-Way"/>
                                            <constraint firstItem="Uqi-fK-e69" firstAttribute="top" secondItem="hfQ-Kr-c8l" secondAttribute="top" constant="16" id="jjl-fB-Gik"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="hfQ-Kr-c8l" firstAttribute="width" secondItem="aUS-tT-6yu" secondAttribute="width" id="GWb-xv-ws1"/>
                                    <constraint firstItem="hfQ-Kr-c8l" firstAttribute="top" secondItem="aUS-tT-6yu" secondAttribute="top" id="Wcu-TK-KHY"/>
                                    <constraint firstItem="hfQ-Kr-c8l" firstAttribute="leading" secondItem="aUS-tT-6yu" secondAttribute="leading" id="a6E-Xy-FH0"/>
                                    <constraint firstAttribute="trailing" secondItem="hfQ-Kr-c8l" secondAttribute="trailing" id="wHT-z2-Hkj"/>
                                    <constraint firstAttribute="bottom" secondItem="hfQ-Kr-c8l" secondAttribute="bottom" id="yel-FQ-dLk"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="oFc-Ch-f0J"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="aUS-tT-6yu" firstAttribute="centerX" secondItem="oFc-Ch-f0J" secondAttribute="centerX" id="EvD-v2-RPN"/>
                            <constraint firstItem="oFc-Ch-f0J" firstAttribute="bottom" secondItem="aUS-tT-6yu" secondAttribute="bottom" constant="16" id="dHc-dm-feL"/>
                            <constraint firstItem="aUS-tT-6yu" firstAttribute="top" secondItem="oFc-Ch-f0J" secondAttribute="top" id="g43-YW-MBj"/>
                            <constraint firstItem="aUS-tT-6yu" firstAttribute="width" secondItem="lYP-H8-7Sb" secondAttribute="width" id="tBP-Sc-c8T"/>
                        </constraints>
                        <connections>
                            <outletCollection property="gestureRecognizers" destination="26u-8h-yjN" appends="YES" id="2R6-le-wG5"/>
                        </connections>
                    </view>
                    <navigationItem key="navigationItem" title="Start an account" id="6Or-2w-t0J">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="RNb-AF-uCo">
                            <connections>
                                <action selector="didTapBack:" destination="qsB-gZ-sbT" id="JQA-43-MgE"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="isMayaForm" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="continueButton" destination="5TJ-7l-IKx" id="l4d-dH-Og2"/>
                        <outlet property="emailView" destination="CWZ-TV-1c7" id="BHj-KP-gIx"/>
                        <outlet property="firstNameView" destination="MTi-H2-vZt" id="Lac-4H-OCM"/>
                        <outlet property="lastNameView" destination="KKB-kG-1FL" id="R8e-Ak-dbR"/>
                        <outlet property="middleNameView" destination="jtE-ao-hGt" id="2TA-Zd-Ziv"/>
                        <outlet property="mobileNumberView" destination="Kie-yz-8HQ" id="rB9-b6-ell"/>
                        <outlet property="noMiddleNameCheckBox" destination="3Ex-bf-QJ5" id="ah0-DL-DHs"/>
                        <outlet property="noMiddleNameLabel" destination="DOo-Z8-MxI" id="ZtL-cL-sqV"/>
                        <outlet property="noMiddleNameStackView" destination="WLM-kS-PWR" id="eeL-93-NvF"/>
                        <outlet property="passwordTipsLabel" destination="Tf9-XZ-NbE" id="RsF-1q-Wpk"/>
                        <outlet property="passwordView" destination="Z3F-Df-1ZL" id="taK-yB-kpg"/>
                        <outlet property="scrollView" destination="aUS-tT-6yu" id="hYt-dg-JzN"/>
                        <outlet property="stickyViewBottomConstraint" destination="A6o-tq-Dkh" id="7vK-V4-Kha"/>
                        <outlet property="titleLabel" destination="6CC-Xl-9M5" id="Xlh-AA-o3f"/>
                    </connections>
                </viewController>
                <tapGestureRecognizer id="26u-8h-yjN">
                    <connections>
                        <action selector="dismissKeyboard:" destination="qsB-gZ-sbT" id="cpc-9J-K2e"/>
                    </connections>
                </tapGestureRecognizer>
                <placeholder placeholderIdentifier="IBFirstResponder" id="YKo-H5-3dr" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="24" y="-56"/>
        </scene>
        <!--Auto Provision Data Privacy V2-->
        <scene sceneID="ftk-h2-CJh">
            <objects>
                <viewController storyboardIdentifier="MayaAutoProvisionV2ViewController" id="Wpk-p5-E3l" userLabel="Auto Provision Data Privacy V2" customClass="MayaAutoProvisionV2ViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="aVf-oL-R2G">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fha-YL-o5d">
                                <rect key="frame" x="0.0" y="59" width="393" height="743"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="EdL-Lu-FAO">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="1008.3333333333334"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="W1C-2G-4zb" userLabel="Main Stack View">
                                                <rect key="frame" x="25" y="30" width="343" height="953.33333333333337"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="B5t-Mf-JQ6" userLabel="Header Stack View">
                                                        <rect key="frame" x="0.0" y="0.0" width="343" height="79.666666666666671"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="253" text="Let's get started" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cKF-YM-UT9">
                                                                <rect key="frame" x="0.0" y="0.0" width="343" height="39"/>
                                                                <accessibility key="accessibilityConfiguration" identifier="pmios_registration_page1_title_label"/>
                                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="32"/>
                                                                <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="We’ll open the following accounts so you can start using them straight away" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="y82-qJ-gPb">
                                                                <rect key="frame" x="0.0" y="43" width="343" height="36.***************"/>
                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="14"/>
                                                                <color key="textColor" name="Grey6"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleAspectFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="mNh-lD-CjR" userLabel="Provision Items Stack View">
                                                        <rect key="frame" x="0.0" y="103.66666666666666" width="343" height="152"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="EKI-KD-eqB" userLabel="eWallet" customClass="MayaAutoProvisionItemView" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="343" height="70"/>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="70" id="bfv-0D-ZIz"/>
                                                                </constraints>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="iconBackgroundColor">
                                                                        <color key="value" name="OpacityPurple25"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="image" keyPath="iconImage" value="imageGreenHeart"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Euw-1K-6ZU" userLabel="savings" customClass="MayaAutoProvisionItemView" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="82.000000000000028" width="343" height="70"/>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="70" id="9XL-E1-g3D"/>
                                                                </constraints>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="iconBackgroundColor">
                                                                        <color key="value" name="SecondaryLightGreen"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="image" keyPath="iconImage" value="imagePig"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                        </subviews>
                                                    </stackView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="UmT-KF-dN8" userLabel="Allow Data Personalization View" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="279.66666666666669" width="343" height="459.00000000000006"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="0bS-bA-UXd">
                                                                <rect key="frame" x="16" y="12" width="311" height="435"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="VWo-BM-B97">
                                                                        <rect key="frame" x="0.0" y="0.0" width="311" height="112"/>
                                                                        <subviews>
                                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="uKN-Wa-onm">
                                                                                <rect key="frame" x="0.0" y="0.0" width="251" height="112"/>
                                                                                <subviews>
                                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="q1L-qD-co8">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="251" height="82"/>
                                                                                        <subviews>
                                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Allow data personalization" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6rP-uX-JlG">
                                                                                                <rect key="frame" x="0.0" y="0.0" width="251" height="46.333333333333336"/>
                                                                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                                                                <color key="textColor" name="ButtonPrimaryBlack"/>
                                                                                                <nil key="highlightedColor"/>
                                                                                            </label>
                                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="To make the most of your Maya experience, please allow us to process your data." textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FKx-9g-NGH">
                                                                                                <rect key="frame" x="0.0" y="50.333333333333307" width="251" height="31.666666666666664"/>
                                                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="12"/>
                                                                                                <color key="textColor" name="Grey6"/>
                                                                                                <nil key="highlightedColor"/>
                                                                                            </label>
                                                                                        </subviews>
                                                                                    </stackView>
                                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="tga-WF-UI2">
                                                                                        <rect key="frame" x="0.0" y="94" width="251" height="18"/>
                                                                                        <subviews>
                                                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rno-I1-fbV">
                                                                                                <rect key="frame" x="0.0" y="0.0" width="251" height="2"/>
                                                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                <constraints>
                                                                                                    <constraint firstAttribute="height" constant="2" id="EVs-pg-9Ob"/>
                                                                                                </constraints>
                                                                                            </view>
                                                                                            <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Show more" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hwP-Cq-7A8">
                                                                                                <rect key="frame" x="0.0" y="2" width="251" height="16"/>
                                                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="12"/>
                                                                                                <color key="textColor" name="00A651"/>
                                                                                                <nil key="highlightedColor"/>
                                                                                            </label>
                                                                                        </subviews>
                                                                                    </stackView>
                                                                                </subviews>
                                                                            </stackView>
                                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3Yc-GK-tuf">
                                                                                <rect key="frame" x="251" y="0.0" width="60" height="112"/>
                                                                                <subviews>
                                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="a9z-SG-F16" userLabel="Provision Checkbox" customClass="MayaCheckBox" customModule="PayMaya" customModuleProvider="target">
                                                                                        <rect key="frame" x="36" y="44" width="24" height="24"/>
                                                                                        <constraints>
                                                                                            <constraint firstAttribute="width" constant="24" id="Ob1-b3-hl2"/>
                                                                                            <constraint firstAttribute="height" constant="24" id="y55-ah-Hxh"/>
                                                                                        </constraints>
                                                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                        <connections>
                                                                                            <action selector="didTapEnableAllButton:" destination="Wpk-p5-E3l" eventType="touchUpInside" id="WNc-fF-Af5"/>
                                                                                        </connections>
                                                                                    </button>
                                                                                </subviews>
                                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="60" id="nUB-OC-AEd"/>
                                                                                    <constraint firstAttribute="trailing" secondItem="a9z-SG-F16" secondAttribute="trailing" id="rfb-I5-JwN"/>
                                                                                </constraints>
                                                                            </view>
                                                                        </subviews>
                                                                        <constraints>
                                                                            <constraint firstItem="a9z-SG-F16" firstAttribute="centerY" secondItem="uKN-Wa-onm" secondAttribute="centerY" id="WU5-RQ-8tM"/>
                                                                        </constraints>
                                                                    </stackView>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="rky-9O-dzg">
                                                                        <rect key="frame" x="0.0" y="124" width="311" height="311"/>
                                                                        <subviews>
                                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qNi-Rx-3dD">
                                                                                <rect key="frame" x="0.0" y="0.0" width="311" height="1"/>
                                                                                <color key="backgroundColor" name="Grey3"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="1" id="uKn-5J-JrD"/>
                                                                                </constraints>
                                                                            </view>
                                                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" placeholderIntrinsicWidth="343" placeholderIntrinsicHeight="300.00000000000006" alwaysBounceVertical="YES" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="KcD-jl-bW2">
                                                                                <rect key="frame" x="0.0" y="10.999999999999943" width="311" height="300"/>
                                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="300" id="MT3-bs-3uX"/>
                                                                                </constraints>
                                                                                <prototypes>
                                                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="0.0" reuseIdentifier="MayaEditPolicyConsentTableViewCell" rowHeight="100" id="09M-cu-lup" customClass="MayaEditPolicyConsentTableViewCell" customModule="PayMaya" customModuleProvider="target">
                                                                                        <rect key="frame" x="0.0" y="50" width="311" height="100"/>
                                                                                        <autoresizingMask key="autoresizingMask"/>
                                                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="09M-cu-lup" id="Rfb-eF-BQ9">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="311" height="100"/>
                                                                                            <autoresizingMask key="autoresizingMask"/>
                                                                                            <subviews>
                                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ewf-D1-Px1" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                                                                    <rect key="frame" x="0.0" y="6" width="311" height="88"/>
                                                                                                    <subviews>
                                                                                                        <stackView opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="aOZ-4A-uUh">
                                                                                                            <rect key="frame" x="0.0" y="0.0" width="311" height="88"/>
                                                                                                            <subviews>
                                                                                                                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="UyJ-2L-F7N">
                                                                                                                    <rect key="frame" x="0.0" y="0.0" width="311" height="88"/>
                                                                                                                    <subviews>
                                                                                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="AyH-uR-5I3" customClass="MayaCheckBox" customModule="PayMaya" customModuleProvider="target">
                                                                                                                            <rect key="frame" x="21" y="32" width="24" height="24"/>
                                                                                                                            <constraints>
                                                                                                                                <constraint firstAttribute="width" constant="24" id="Dbq-Hy-uVl"/>
                                                                                                                                <constraint firstAttribute="height" constant="24" id="d3x-8B-qhb"/>
                                                                                                                            </constraints>
                                                                                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                                                            <connections>
                                                                                                                                <action selector="didTapEnableConsent:" destination="09M-cu-lup" eventType="touchUpInside" id="MBs-Va-DIc"/>
                                                                                                                            </connections>
                                                                                                                        </button>
                                                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Profiling" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nEs-yY-edb">
                                                                                                                            <rect key="frame" x="61" y="16" width="194" height="56"/>
                                                                                                                            <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="16"/>
                                                                                                                            <nil key="textColor"/>
                                                                                                                            <nil key="highlightedColor"/>
                                                                                                                        </label>
                                                                                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sY7-qF-P0E">
                                                                                                                            <rect key="frame" x="271" y="32" width="24" height="24"/>
                                                                                                                            <constraints>
                                                                                                                                <constraint firstAttribute="width" constant="24" id="R4c-dy-bAx"/>
                                                                                                                                <constraint firstAttribute="height" constant="24" id="Vhk-aP-jb6"/>
                                                                                                                            </constraints>
                                                                                                                            <color key="tintColor" name="ButtonPrimaryBlack"/>
                                                                                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                                                            <state key="normal" image="iconPlus"/>
                                                                                                                            <connections>
                                                                                                                                <action selector="didTapToggleShowConsentDescription:" destination="09M-cu-lup" eventType="touchUpInside" id="zpm-tS-t3H"/>
                                                                                                                            </connections>
                                                                                                                        </button>
                                                                                                                    </subviews>
                                                                                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                                    <constraints>
                                                                                                                        <constraint firstAttribute="bottom" secondItem="nEs-yY-edb" secondAttribute="bottom" constant="16" id="80f-FM-Mel"/>
                                                                                                                        <constraint firstItem="sY7-qF-P0E" firstAttribute="centerY" secondItem="nEs-yY-edb" secondAttribute="centerY" id="8RB-hQ-Hd0"/>
                                                                                                                        <constraint firstItem="AyH-uR-5I3" firstAttribute="centerY" secondItem="nEs-yY-edb" secondAttribute="centerY" id="FvZ-cW-stf"/>
                                                                                                                        <constraint firstItem="sY7-qF-P0E" firstAttribute="leading" secondItem="nEs-yY-edb" secondAttribute="trailing" constant="16" id="Qf7-np-JcQ"/>
                                                                                                                        <constraint firstAttribute="trailing" secondItem="sY7-qF-P0E" secondAttribute="trailing" constant="16" id="Rvr-NI-bQg"/>
                                                                                                                        <constraint firstItem="nEs-yY-edb" firstAttribute="top" secondItem="UyJ-2L-F7N" secondAttribute="top" constant="16" id="Wrk-io-8UG"/>
                                                                                                                        <constraint firstItem="AyH-uR-5I3" firstAttribute="leading" secondItem="UyJ-2L-F7N" secondAttribute="leading" constant="21" id="anS-UI-lgf"/>
                                                                                                                        <constraint firstItem="nEs-yY-edb" firstAttribute="leading" secondItem="AyH-uR-5I3" secondAttribute="trailing" constant="16" id="zY9-EF-8Se"/>
                                                                                                                    </constraints>
                                                                                                                </view>
                                                                                                                <view hidden="YES" clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7Wp-sI-tsI" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                                                                                    <rect key="frame" x="0.0" y="0.0" width="311" height="18"/>
                                                                                                                    <subviews>
                                                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" placeholderIntrinsicWidth="334" placeholderIntrinsicHeight="18.5" text="" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QtV-0u-d37">
                                                                                                                            <rect key="frame" x="16" y="6" width="279" height="0.0"/>
                                                                                                                            <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="14"/>
                                                                                                                            <color key="textColor" name="ContentGrey6"/>
                                                                                                                            <nil key="highlightedColor"/>
                                                                                                                        </label>
                                                                                                                    </subviews>
                                                                                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                                    <constraints>
                                                                                                                        <constraint firstAttribute="trailing" secondItem="QtV-0u-d37" secondAttribute="trailing" constant="16" id="Adv-I2-aKZ"/>
                                                                                                                        <constraint firstItem="QtV-0u-d37" firstAttribute="leading" secondItem="7Wp-sI-tsI" secondAttribute="leading" constant="16" id="Hd3-Sw-2mI"/>
                                                                                                                        <constraint firstItem="QtV-0u-d37" firstAttribute="top" secondItem="7Wp-sI-tsI" secondAttribute="top" constant="6" id="Mgc-2B-WYq"/>
                                                                                                                        <constraint firstAttribute="bottom" secondItem="QtV-0u-d37" secondAttribute="bottom" constant="12" id="lcx-MT-3Zu"/>
                                                                                                                    </constraints>
                                                                                                                </view>
                                                                                                            </subviews>
                                                                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                        </stackView>
                                                                                                    </subviews>
                                                                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                                                    <constraints>
                                                                                                        <constraint firstAttribute="bottom" secondItem="aOZ-4A-uUh" secondAttribute="bottom" id="7dX-MM-ZJG"/>
                                                                                                        <constraint firstItem="aOZ-4A-uUh" firstAttribute="top" secondItem="ewf-D1-Px1" secondAttribute="top" id="8No-85-sRI"/>
                                                                                                        <constraint firstAttribute="trailing" secondItem="aOZ-4A-uUh" secondAttribute="trailing" id="Wk6-C4-ZBJ"/>
                                                                                                        <constraint firstItem="aOZ-4A-uUh" firstAttribute="leading" secondItem="ewf-D1-Px1" secondAttribute="leading" id="tT3-3A-u2S"/>
                                                                                                    </constraints>
                                                                                                    <userDefinedRuntimeAttributes>
                                                                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                                                                            <real key="value" value="1"/>
                                                                                                        </userDefinedRuntimeAttribute>
                                                                                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                                                                            <color key="value" name="MenuGrey3"/>
                                                                                                        </userDefinedRuntimeAttribute>
                                                                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                                                            <real key="value" value="16"/>
                                                                                                        </userDefinedRuntimeAttribute>
                                                                                                    </userDefinedRuntimeAttributes>
                                                                                                </view>
                                                                                            </subviews>
                                                                                            <constraints>
                                                                                                <constraint firstItem="ewf-D1-Px1" firstAttribute="leading" secondItem="Rfb-eF-BQ9" secondAttribute="leading" id="89D-RG-fiv"/>
                                                                                                <constraint firstAttribute="bottom" secondItem="ewf-D1-Px1" secondAttribute="bottom" constant="6" id="XXj-06-Jl8"/>
                                                                                                <constraint firstAttribute="trailing" secondItem="ewf-D1-Px1" secondAttribute="trailing" id="dgi-9e-UXb"/>
                                                                                                <constraint firstItem="ewf-D1-Px1" firstAttribute="top" secondItem="Rfb-eF-BQ9" secondAttribute="top" constant="6" id="gbK-5X-h7k"/>
                                                                                            </constraints>
                                                                                        </tableViewCellContentView>
                                                                                        <connections>
                                                                                            <outlet property="checkBox" destination="AyH-uR-5I3" id="MWZ-MQ-g2P"/>
                                                                                            <outlet property="containerView" destination="ewf-D1-Px1" id="UOg-sC-SjC"/>
                                                                                            <outlet property="descriptionContainerView" destination="7Wp-sI-tsI" id="BSy-08-a8P"/>
                                                                                            <outlet property="descriptionLabel" destination="QtV-0u-d37" id="Lwe-QM-DgA"/>
                                                                                            <outlet property="expandButton" destination="sY7-qF-P0E" id="CJM-pY-Csp"/>
                                                                                            <outlet property="titleLabel" destination="nEs-yY-edb" id="i7t-vX-o2P"/>
                                                                                        </connections>
                                                                                    </tableViewCell>
                                                                                </prototypes>
                                                                                <connections>
                                                                                    <outlet property="dataSource" destination="Wpk-p5-E3l" id="aqA-HU-X8H"/>
                                                                                </connections>
                                                                            </tableView>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstItem="0bS-bA-UXd" firstAttribute="top" secondItem="UmT-KF-dN8" secondAttribute="top" constant="12" id="Wvn-KR-6d3"/>
                                                            <constraint firstItem="0bS-bA-UXd" firstAttribute="leading" secondItem="UmT-KF-dN8" secondAttribute="leading" constant="16" id="XBl-7V-88R"/>
                                                            <constraint firstAttribute="trailing" secondItem="0bS-bA-UXd" secondAttribute="trailing" constant="16" id="j8c-rP-aiN"/>
                                                            <constraint firstAttribute="bottom" secondItem="0bS-bA-UXd" secondAttribute="bottom" constant="12" id="q9j-A6-OiW"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                                <real key="value" value="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                                <color key="value" name="Grey2"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                <real key="value" value="16"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="M6j-Bh-bU9" userLabel="Disclaimer Stack View">
                                                        <rect key="frame" x="0.0" y="762.66666666666663" width="343" height="110.66666666666663"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="By continuing, you consent to the following and certify that your information is true and complete" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4sK-Pc-V8H">
                                                                <rect key="frame" x="0.0" y="0.0" width="343" height="36.666666666666664"/>
                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="14"/>
                                                                <color key="textColor" name="Grey6"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="VBW-Z5-vCc" userLabel="Links Stack View">
                                                                <rect key="frame" x="0.0" y="40.666666666666742" width="343" height="70"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="ilZ-I8-6qs" userLabel="Wallet Stack View">
                                                                        <rect key="frame" x="0.0" y="0.0" width="343" height="34"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="T&amp;C Placeholder 1" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cJP-HS-L8A" customClass="MayaAutoProvisionLinkLabel" customModule="PayMaya" customModuleProvider="target">
                                                                                <rect key="frame" x="0.0" y="0.0" width="343" height="16"/>
                                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="12"/>
                                                                                <color key="textColor" name="ContentPrimaryGreen"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Privacy Data Placeholder 1" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7Wm-Oa-3LQ" customClass="MayaAutoProvisionLinkLabel" customModule="PayMaya" customModuleProvider="target">
                                                                                <rect key="frame" x="0.0" y="18" width="343" height="16"/>
                                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="12"/>
                                                                                <color key="textColor" name="ContentPrimaryGreen"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="ZPf-Hw-ehZ" userLabel="Savings Stack View">
                                                                        <rect key="frame" x="0.0" y="36" width="343" height="34"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="T&amp;C Placeholder 2" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dih-MB-C0K" customClass="MayaAutoProvisionLinkLabel" customModule="PayMaya" customModuleProvider="target">
                                                                                <rect key="frame" x="0.0" y="0.0" width="343" height="16"/>
                                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="12"/>
                                                                                <color key="textColor" name="ContentPrimaryGreen"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Privacy Data Placeholder 1" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2CX-xW-NRb" customClass="MayaAutoProvisionLinkLabel" customModule="PayMaya" customModuleProvider="target">
                                                                                <rect key="frame" x="0.0" y="18" width="343" height="16"/>
                                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="12"/>
                                                                                <color key="textColor" name="ContentPrimaryGreen"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                    </stackView>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="dCO-Ww-lCp" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="897.33333333333337" width="343" height="56"/>
                                                        <accessibility key="accessibilityConfiguration" identifier="pmios_registration_page1_continue_button"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="56" id="YPi-92-E34"/>
                                                        </constraints>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal" title="Continue"/>
                                                        <connections>
                                                            <action selector="didTapContinue:" destination="Wpk-p5-E3l" eventType="touchUpInside" id="0qu-NZ-vAy"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="W1C-2G-4zb" firstAttribute="top" secondItem="EdL-Lu-FAO" secondAttribute="top" constant="16" id="DI2-eB-d8T"/>
                                            <constraint firstAttribute="bottom" secondItem="W1C-2G-4zb" secondAttribute="bottom" constant="25" id="YmJ-dX-AxB"/>
                                            <constraint firstAttribute="trailing" secondItem="W1C-2G-4zb" secondAttribute="trailing" constant="25" id="cds-LO-jgJ"/>
                                            <constraint firstItem="W1C-2G-4zb" firstAttribute="leading" secondItem="EdL-Lu-FAO" secondAttribute="leading" constant="25" id="iG7-j6-ZDb"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="EdL-Lu-FAO" secondAttribute="bottom" id="gxG-1E-NoZ"/>
                                    <constraint firstAttribute="trailing" secondItem="EdL-Lu-FAO" secondAttribute="trailing" id="nXd-D4-FAZ"/>
                                    <constraint firstItem="EdL-Lu-FAO" firstAttribute="top" secondItem="fha-YL-o5d" secondAttribute="top" id="noL-ud-gJY"/>
                                    <constraint firstItem="EdL-Lu-FAO" firstAttribute="leading" secondItem="fha-YL-o5d" secondAttribute="leading" id="rrL-Pu-ovb"/>
                                    <constraint firstItem="EdL-Lu-FAO" firstAttribute="width" secondItem="fha-YL-o5d" secondAttribute="width" id="u3z-3a-cBf"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="2ly-Ce-5dI"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="fha-YL-o5d" firstAttribute="width" secondItem="aVf-oL-R2G" secondAttribute="width" id="Jg5-VB-ZHH"/>
                            <constraint firstItem="fha-YL-o5d" firstAttribute="centerX" secondItem="2ly-Ce-5dI" secondAttribute="centerX" id="axw-Qx-Xjr"/>
                            <constraint firstItem="2ly-Ce-5dI" firstAttribute="bottom" secondItem="fha-YL-o5d" secondAttribute="bottom" constant="16" id="s4g-iY-Q9T"/>
                            <constraint firstItem="fha-YL-o5d" firstAttribute="top" secondItem="2ly-Ce-5dI" secondAttribute="top" id="zEi-pI-J5M"/>
                        </constraints>
                        <connections>
                            <outletCollection property="gestureRecognizers" destination="nhN-f0-SI7" appends="YES" id="blM-eM-WYv"/>
                        </connections>
                    </view>
                    <navigationItem key="navigationItem" title="Start an account" id="9Yn-cB-GVP">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="q7g-U9-DU5">
                            <connections>
                                <action selector="didTapBack:" destination="Wpk-p5-E3l" id="5aR-oP-ZKF"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="consentsStackView" destination="rky-9O-dzg" id="1GF-sz-CSC"/>
                        <outlet property="dataPersonalizationDescription" destination="FKx-9g-NGH" id="hza-FC-7Aq"/>
                        <outlet property="dataPersonalizationTitle" destination="6rP-uX-JlG" id="NsT-c7-EGc"/>
                        <outlet property="eWalletAutoProvisionItem" destination="EKI-KD-eqB" id="09M-fD-IQt"/>
                        <outlet property="enableAllCheckBox" destination="a9z-SG-F16" id="8CO-0b-XjW"/>
                        <outlet property="linkSavingsPrivacyLabel" destination="2CX-xW-NRb" id="hmk-SV-EQr"/>
                        <outlet property="linkSavingsStackView" destination="ZPf-Hw-ehZ" id="PjQ-uA-EJ2"/>
                        <outlet property="linkSavingsTermLabel" destination="dih-MB-C0K" id="IWO-1K-tdz"/>
                        <outlet property="linkWalletPrivacyLabel" destination="7Wm-Oa-3LQ" id="ss9-jm-p1T"/>
                        <outlet property="linkWalletStackView" destination="ilZ-I8-6qs" id="l55-Ha-GAb"/>
                        <outlet property="linkWalletTermLabel" destination="cJP-HS-L8A" id="FjG-Np-HMv"/>
                        <outlet property="savingsAutoProvisionItem" destination="Euw-1K-6ZU" id="WNf-lE-em8"/>
                        <outlet property="showMoreLabel" destination="hwP-Cq-7A8" id="vSI-3d-Itw"/>
                        <outlet property="tableView" destination="KcD-jl-bW2" id="QcI-8a-Ybs"/>
                        <outlet property="tableViewHeightConstraint" destination="MT3-bs-3uX" id="ZGi-XZ-3RX"/>
                    </connections>
                </viewController>
                <tapGestureRecognizer id="nhN-f0-SI7"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Y0J-AM-TbI" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="864.8854961832061" y="-56.338028169014088"/>
        </scene>
    </scenes>
    <resources>
        <image name="iconBack" width="22" height="17"/>
        <image name="iconPlus" width="13" height="13"/>
        <image name="imageGreenHeart" width="24" height="24"/>
        <image name="imagePig" width="24" height="24"/>
        <namedColor name="00A651">
            <color red="0.0" green="0.65098039215686276" blue="0.31764705882352939" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ButtonPrimaryBlack">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentGrey6">
            <color red="0.33333333333333331" green="0.33725490196078434" blue="0.34901960784313724" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentPrimaryGreen">
            <color red="0.0" green="0.65098039215686276" blue="0.31764705882352939" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey2">
            <color red="0.95686274509803926" green="0.96078431372549022" blue="0.96078431372549022" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey3">
            <color red="0.92549019607843142" green="0.92941176470588238" blue="0.93725490196078431" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey6">
            <color red="0.33333333333333331" green="0.33725490196078434" blue="0.34901960784313724" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="MenuGrey3">
            <color red="0.92500001192092896" green="0.92900002002716064" blue="0.93699997663497925" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="OpacityPurple25">
            <color red="0.83529411764705885" green="0.62352941176470589" blue="1" alpha="0.25" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="SecondaryLightGreen">
            <color red="0.11372549019607843" green="0.7686274509803922" blue="0.22352941176470589" alpha="0.15000000596046448" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
