<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="yut-G3-qVs">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="CerebriSansPro-Bold.otf">
            <string>CerebriSansPro-Bold</string>
        </array>
        <array key="CerebriSansPro-Book.otf">
            <string>CerebriSansPro-Book</string>
        </array>
        <array key="CerebriSansPro-Medium.otf">
            <string>CerebriSansPro-Medium</string>
        </array>
        <array key="Jeko-Bold.otf">
            <string>Jeko-Bold</string>
        </array>
        <array key="Jeko-Regular.otf">
            <string>Jeko-Regular</string>
        </array>
        <array key="Jeko-SemiBold.otf">
            <string>Jeko-SemiBold</string>
        </array>
    </customFonts>
    <scenes>
        <!--Navigation Controller-->
        <scene sceneID="JSl-l2-ND8">
            <objects>
                <navigationController id="yut-G3-qVs" sceneMemberID="viewController">
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="Mg5-U8-Z0k">
                        <rect key="frame" x="0.0" y="59" width="393" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="Y6W-OH-hqX" kind="relationship" relationship="rootViewController" id="6y9-ED-YbC"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="K2j-4Y-8gr" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1485" y="43"/>
        </scene>
        <!--MayaEKYC Benefits View Controller-->
        <scene sceneID="s0d-6b-0kx">
            <objects>
                <viewController storyboardIdentifier="MayaEKYCBenefitsViewController" id="Y6W-OH-hqX" customClass="MayaEKYCBenefitsViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="5EZ-qb-Rvc">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="EJO-XZ-pYA">
                                <rect key="frame" x="0.0" y="103" width="393" height="649"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CcV-Fs-D8W" userLabel="Content View">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="576"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="imageMayaRainbow" translatesAutoresizingMaskIntoConstraints="NO" id="602-tQ-dbs">
                                                <rect key="frame" x="24" y="24" width="135" height="87"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" secondItem="602-tQ-dbs" secondAttribute="height" multiplier="160:103" id="bqN-sy-HiG"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="751" text="Upgrade  my account" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tAb-tU-b9z">
                                                <rect key="frame" x="24" y="135" width="345" height="97"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="40"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalCompressionResistancePriority="751" text="Expand your Maya experience for free and unlock these features:" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qN4-ln-tp9">
                                                <rect key="frame" x="24" y="256" width="345" height="39"/>
                                                <fontDescription key="fontDescription" name="Jeko-Regular" family="Jeko" pointSize="16"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Qbd-Mg-lg5" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="319" width="345" height="233"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" verticalCompressionResistancePriority="751" axis="vertical" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="HlJ-0v-lsd">
                                                        <rect key="frame" x="16" y="16" width="313" height="201"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="top" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="LL8-OG-q9P">
                                                                <rect key="frame" x="0.0" y="0.0" width="313" height="35"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconPhone" translatesAutoresizingMaskIntoConstraints="NO" id="71F-9V-Foh">
                                                                        <rect key="frame" x="0.0" y="0.0" width="18" height="24"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="24" id="ASn-gi-02B"/>
                                                                            <constraint firstAttribute="width" constant="18" id="eaG-EK-CrP"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <view contentMode="scaleToFill" verticalCompressionResistancePriority="751" translatesAutoresizingMaskIntoConstraints="NO" id="8Za-LX-gVl">
                                                                        <rect key="frame" x="30" y="0.0" width="283" height="35"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="751" text="Send money to your Maya friends and to other banks instantly" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FS4-5x-XtJ">
                                                                                <rect key="frame" x="0.0" y="0.0" width="283" height="35"/>
                                                                                <fontDescription key="fontDescription" name="Jeko-Regular" family="Jeko" pointSize="14"/>
                                                                                <nil key="textColor"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                        <color key="backgroundColor" name="Grey1"/>
                                                                        <constraints>
                                                                            <constraint firstItem="FS4-5x-XtJ" firstAttribute="top" secondItem="8Za-LX-gVl" secondAttribute="top" id="B94-W7-bL1"/>
                                                                            <constraint firstAttribute="trailing" secondItem="FS4-5x-XtJ" secondAttribute="trailing" id="Gb8-bL-LmQ"/>
                                                                            <constraint firstItem="FS4-5x-XtJ" firstAttribute="leading" secondItem="8Za-LX-gVl" secondAttribute="leading" id="X2x-AB-3pb"/>
                                                                            <constraint firstAttribute="bottom" secondItem="FS4-5x-XtJ" secondAttribute="bottom" id="hrH-TA-maY"/>
                                                                        </constraints>
                                                                    </view>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="4gK-v4-XTs">
                                                                <rect key="frame" x="0.0" y="55" width="313" height="1"/>
                                                                <subviews>
                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ONl-2x-7vV">
                                                                        <rect key="frame" x="0.0" y="0.0" width="313" height="1"/>
                                                                        <color key="backgroundColor" name="Grey3"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="1" id="Ym9-0e-ZH5"/>
                                                                        </constraints>
                                                                    </view>
                                                                </subviews>
                                                                <constraints>
                                                                    <constraint firstItem="ONl-2x-7vV" firstAttribute="width" secondItem="4gK-v4-XTs" secondAttribute="width" id="mlf-Rt-3UI"/>
                                                                </constraints>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="top" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="Ext-aU-Osx">
                                                                <rect key="frame" x="0.0" y="76" width="313" height="34"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconBank" translatesAutoresizingMaskIntoConstraints="NO" id="U5j-Sy-8Do">
                                                                        <rect key="frame" x="0.0" y="0.0" width="18" height="24"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="18" id="Rhx-07-Fne"/>
                                                                            <constraint firstAttribute="height" constant="24" id="V1G-kZ-Zkp"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <view contentMode="scaleToFill" verticalCompressionResistancePriority="751" translatesAutoresizingMaskIntoConstraints="NO" id="6oa-tC-xMQ">
                                                                        <rect key="frame" x="30" y="0.0" width="283" height="34"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="751" text="Withdraw from any BancNet ATM in the world with your Maya card" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="i9p-jz-5tM">
                                                                                <rect key="frame" x="0.0" y="0.0" width="283" height="34"/>
                                                                                <fontDescription key="fontDescription" name="Jeko-Regular" family="Jeko" pointSize="14"/>
                                                                                <nil key="textColor"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                        <color key="backgroundColor" name="Grey1"/>
                                                                        <constraints>
                                                                            <constraint firstItem="i9p-jz-5tM" firstAttribute="top" secondItem="6oa-tC-xMQ" secondAttribute="top" id="CP8-cL-L4u"/>
                                                                            <constraint firstAttribute="trailing" secondItem="i9p-jz-5tM" secondAttribute="trailing" id="F8P-JV-jlB"/>
                                                                            <constraint firstAttribute="bottom" secondItem="i9p-jz-5tM" secondAttribute="bottom" id="mFq-KY-91C"/>
                                                                            <constraint firstItem="i9p-jz-5tM" firstAttribute="leading" secondItem="6oa-tC-xMQ" secondAttribute="leading" id="yy8-F2-eQL"/>
                                                                        </constraints>
                                                                    </view>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="Zb6-iz-hzD">
                                                                <rect key="frame" x="0.0" y="130" width="313" height="1"/>
                                                                <subviews>
                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CsO-PF-GnS">
                                                                        <rect key="frame" x="0.0" y="0.0" width="313" height="1"/>
                                                                        <viewLayoutGuide key="safeArea" id="ImZ-2N-d9L"/>
                                                                        <color key="backgroundColor" name="Grey3"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="1" id="dQt-ST-7xm"/>
                                                                        </constraints>
                                                                    </view>
                                                                </subviews>
                                                                <constraints>
                                                                    <constraint firstItem="CsO-PF-GnS" firstAttribute="width" secondItem="Zb6-iz-hzD" secondAttribute="width" id="wZW-BN-URs"/>
                                                                </constraints>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="top" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="QUK-75-36X">
                                                                <rect key="frame" x="0.0" y="151" width="313" height="50"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconFlex" translatesAutoresizingMaskIntoConstraints="NO" id="Tj4-43-zoC">
                                                                        <rect key="frame" x="0.0" y="0.0" width="18" height="24"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="24" id="0Se-ix-O4N"/>
                                                                            <constraint firstAttribute="width" constant="18" id="kxZ-SR-26V"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <view contentMode="scaleToFill" verticalCompressionResistancePriority="751" translatesAutoresizingMaskIntoConstraints="NO" id="0sn-5X-fB6">
                                                                        <rect key="frame" x="30" y="0.0" width="283" height="50"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="751" text="Bump up your monthly limit from ₱50,000 to ₱100,000" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="G84-er-4wv">
                                                                                <rect key="frame" x="0.0" y="0.0" width="283" height="50"/>
                                                                                <fontDescription key="fontDescription" name="Jeko-Regular" family="Jeko" pointSize="14"/>
                                                                                <nil key="textColor"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                        <color key="backgroundColor" name="Grey1"/>
                                                                        <constraints>
                                                                            <constraint firstItem="G84-er-4wv" firstAttribute="leading" secondItem="0sn-5X-fB6" secondAttribute="leading" id="7Zg-jR-HbK"/>
                                                                            <constraint firstItem="G84-er-4wv" firstAttribute="top" secondItem="0sn-5X-fB6" secondAttribute="top" id="YII-8X-bgN"/>
                                                                            <constraint firstAttribute="trailing" secondItem="G84-er-4wv" secondAttribute="trailing" id="r2r-B1-1VC"/>
                                                                            <constraint firstAttribute="bottom" secondItem="G84-er-4wv" secondAttribute="bottom" id="sKF-7J-DuJ"/>
                                                                        </constraints>
                                                                    </view>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="4gK-v4-XTs" firstAttribute="top" secondItem="HlJ-0v-lsd" secondAttribute="top" constant="55" id="isO-e5-VZe"/>
                                                            <constraint firstItem="Zb6-iz-hzD" firstAttribute="top" secondItem="HlJ-0v-lsd" secondAttribute="top" constant="130" id="rwc-ob-1Gb"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" name="Grey1"/>
                                                <constraints>
                                                    <constraint firstItem="HlJ-0v-lsd" firstAttribute="top" secondItem="Qbd-Mg-lg5" secondAttribute="top" constant="16" id="JhQ-1W-9LZ"/>
                                                    <constraint firstAttribute="bottom" secondItem="HlJ-0v-lsd" secondAttribute="bottom" constant="16" id="NU4-9I-WtT"/>
                                                    <constraint firstAttribute="trailing" secondItem="HlJ-0v-lsd" secondAttribute="trailing" constant="16" id="VQV-pm-3RO"/>
                                                    <constraint firstItem="HlJ-0v-lsd" firstAttribute="leading" secondItem="Qbd-Mg-lg5" secondAttribute="leading" constant="16" id="YiH-aQ-ZdF"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                        <real key="value" value="8"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="602-tQ-dbs" firstAttribute="top" secondItem="CcV-Fs-D8W" secondAttribute="top" constant="24" id="0Bu-9t-iCK"/>
                                            <constraint firstItem="qN4-ln-tp9" firstAttribute="top" secondItem="tAb-tU-b9z" secondAttribute="bottom" constant="24" id="65y-eC-K4P"/>
                                            <constraint firstItem="Qbd-Mg-lg5" firstAttribute="leading" secondItem="CcV-Fs-D8W" secondAttribute="leading" constant="24" id="6IM-s5-iBZ"/>
                                            <constraint firstAttribute="bottom" secondItem="Qbd-Mg-lg5" secondAttribute="bottom" constant="24" id="8ce-rI-O0T"/>
                                            <constraint firstAttribute="trailing" secondItem="qN4-ln-tp9" secondAttribute="trailing" constant="24" id="Hvr-Mw-Lst"/>
                                            <constraint firstItem="Qbd-Mg-lg5" firstAttribute="top" secondItem="qN4-ln-tp9" secondAttribute="bottom" constant="24" id="KhJ-q1-aNY"/>
                                            <constraint firstItem="qN4-ln-tp9" firstAttribute="leading" secondItem="CcV-Fs-D8W" secondAttribute="leading" constant="24" id="WqS-jh-SZm"/>
                                            <constraint firstAttribute="trailing" secondItem="Qbd-Mg-lg5" secondAttribute="trailing" constant="24" id="c2T-4y-dS1"/>
                                            <constraint firstItem="tAb-tU-b9z" firstAttribute="top" secondItem="602-tQ-dbs" secondAttribute="bottom" constant="24" id="dNW-mh-jZc"/>
                                            <constraint firstItem="602-tQ-dbs" firstAttribute="leading" secondItem="CcV-Fs-D8W" secondAttribute="leading" constant="24" id="guz-N6-Bkt"/>
                                            <constraint firstAttribute="trailing" secondItem="tAb-tU-b9z" secondAttribute="trailing" constant="24" id="hKs-Sp-cpW"/>
                                            <constraint firstItem="tAb-tU-b9z" firstAttribute="leading" secondItem="CcV-Fs-D8W" secondAttribute="leading" constant="24" id="jqG-ex-3fM"/>
                                            <constraint firstAttribute="height" constant="576" id="uhS-1L-Vh3"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="CcV-Fs-D8W" secondAttribute="bottom" id="1eJ-0g-4nt"/>
                                    <constraint firstItem="CcV-Fs-D8W" firstAttribute="leading" secondItem="EJO-XZ-pYA" secondAttribute="leading" id="6jX-3R-RKE"/>
                                    <constraint firstItem="CcV-Fs-D8W" firstAttribute="leading" secondItem="EJO-XZ-pYA" secondAttribute="leading" id="J4D-EU-K7b"/>
                                    <constraint firstItem="CcV-Fs-D8W" firstAttribute="top" secondItem="EJO-XZ-pYA" secondAttribute="top" id="LBv-QN-rdv"/>
                                    <constraint firstAttribute="trailing" secondItem="CcV-Fs-D8W" secondAttribute="trailing" id="UkP-dh-ybu"/>
                                    <constraint firstItem="CcV-Fs-D8W" firstAttribute="trailing" secondItem="EJO-XZ-pYA" secondAttribute="trailing" id="mpt-oq-F4i"/>
                                    <constraint firstItem="CcV-Fs-D8W" firstAttribute="width" secondItem="EJO-XZ-pYA" secondAttribute="width" id="qBP-cR-sKv"/>
                                </constraints>
                            </scrollView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="p0W-dS-8U8" customClass="FadedBackgroundView" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="732" width="393" height="120"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="lYT-D9-Abn" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="24" y="32" width="345" height="56"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="7YG-KI-PAe"/>
                                        </constraints>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="Next"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="didTapNext:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="z1d-cY-5wy"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="lYT-D9-Abn" firstAttribute="leading" secondItem="p0W-dS-8U8" secondAttribute="leading" constant="24" id="e4f-7s-R03"/>
                                    <constraint firstItem="lYT-D9-Abn" firstAttribute="top" secondItem="p0W-dS-8U8" secondAttribute="top" constant="32" id="kSl-ga-TDo"/>
                                    <constraint firstAttribute="trailing" secondItem="lYT-D9-Abn" secondAttribute="trailing" constant="24" id="rQF-wA-2Ag"/>
                                    <constraint firstAttribute="bottom" secondItem="lYT-D9-Abn" secondAttribute="bottom" constant="32" id="wQP-7X-gda"/>
                                    <constraint firstAttribute="height" constant="120" id="x32-aa-17C"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="vDu-zF-Fre"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="EJO-XZ-pYA" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="07D-9H-YRU"/>
                            <constraint firstItem="EJO-XZ-pYA" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" id="2rT-x6-L2q"/>
                            <constraint firstItem="p0W-dS-8U8" firstAttribute="top" secondItem="EJO-XZ-pYA" secondAttribute="bottom" constant="-20" id="36u-XK-MPc"/>
                            <constraint firstItem="p0W-dS-8U8" firstAttribute="trailing" secondItem="vDu-zF-Fre" secondAttribute="trailing" id="755-Oq-DMp"/>
                            <constraint firstItem="EJO-XZ-pYA" firstAttribute="width" secondItem="5EZ-qb-Rvc" secondAttribute="width" id="9Eg-c3-z7C"/>
                            <constraint firstItem="EJO-XZ-pYA" firstAttribute="trailing" secondItem="vDu-zF-Fre" secondAttribute="trailing" id="PXq-OW-Dkq"/>
                            <constraint firstItem="p0W-dS-8U8" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="gTV-Uu-fds"/>
                            <constraint firstAttribute="bottom" secondItem="p0W-dS-8U8" secondAttribute="bottom" id="mlZ-vG-OT0"/>
                        </constraints>
                    </view>
                    <toolbarItems/>
                    <navigationItem key="navigationItem" id="Cl1-zu-XNO">
                        <barButtonItem key="leftBarButtonItem" title="Back" id="007-D8-dem">
                            <color key="tintColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                            <connections>
                                <action selector="didTapBack:" destination="Y6W-OH-hqX" id="jYJ-Ff-dLx"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="firstSeparatorTopConstraint" destination="isO-e5-VZe" id="mLL-66-aez"/>
                        <outlet property="secondSeparatorTopConstraint" destination="rwc-ob-1Gb" id="KRg-Jj-Q9k"/>
                        <outlet property="upgradeTitleLabel" destination="tAb-tU-b9z" id="405-1D-R2I"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Ief-a0-LHa" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-468" y="41.829085457271368"/>
        </scene>
        <!--MayaEKYC Reminders View Controller-->
        <scene sceneID="mE9-lR-xF7">
            <objects>
                <viewController storyboardIdentifier="MayaEKYCRemindersViewController" id="7SQ-wY-s0w" customClass="MayaEKYCRemindersViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="0FA-8b-i8R">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qWd-g7-HZH">
                                <rect key="frame" x="0.0" y="103" width="393" height="595"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8iy-bx-obC" userLabel="Content View">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="576"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="imageMayaRainbow" translatesAutoresizingMaskIntoConstraints="NO" id="E8Z-y5-Ako">
                                                <rect key="frame" x="24" y="24" width="161.**************" height="104"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" secondItem="E8Z-y5-Ako" secondAttribute="height" multiplier="160:103" id="cw0-qF-PzH"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="751" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nr5-LM-J1X">
                                                <rect key="frame" x="24" y="152" width="345" height="97"/>
                                                <string key="text">It takes just 3
easy steps</string>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="40"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="XAq-6L-BaN" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="273" width="345" height="196"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" verticalHuggingPriority="249" alignment="center" spacing="21" translatesAutoresizingMaskIntoConstraints="NO" id="6KK-M4-UVK">
                                                        <rect key="frame" x="16" y="18" width="313" height="36"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JrZ-W9-FUh" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="2" width="32" height="32"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="AgW-dF-YDs">
                                                                        <rect key="frame" x="13.3**************" y="7.6666666666666856" width="5.3333333333333339" height="17"/>
                                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="14"/>
                                                                        <color key="textColor" name="PrimaryGrownGreen"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" name="SecondaryLightGreen"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="32" id="BRX-Ds-ysa"/>
                                                                    <constraint firstItem="AgW-dF-YDs" firstAttribute="centerX" secondItem="JrZ-W9-FUh" secondAttribute="centerX" id="Een-4l-cbA"/>
                                                                    <constraint firstItem="AgW-dF-YDs" firstAttribute="centerY" secondItem="JrZ-W9-FUh" secondAttribute="centerY" id="nfN-8P-Jd4"/>
                                                                    <constraint firstAttribute="height" constant="32" id="xax-i2-7kF"/>
                                                                </constraints>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                        <real key="value" value="16"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="top" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="A0o-XU-RKu">
                                                                <rect key="frame" x="53" y="0.66666666666668561" width="260" height="35"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Submit your ID" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rke-Md-Opu">
                                                                        <rect key="frame" x="0.0" y="0.0" width="98.666666666666671" height="17"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="17" id="zjK-jg-kRR"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                                        <color key="textColor" name="PrimaryBlack"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Which IDs can I use?" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FXY-fQ-whC">
                                                                        <rect key="frame" x="0.0" y="18" width="138.33333333333334" height="17"/>
                                                                        <gestureRecognizers/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="17" id="ll0-ue-YYz"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                                        <color key="textColor" name="PrimaryGrownGreen"/>
                                                                        <nil key="highlightedColor"/>
                                                                        <connections>
                                                                            <outletCollection property="gestureRecognizers" destination="6vP-8N-afT" appends="YES" id="qM1-nW-obP"/>
                                                                        </connections>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="36" id="J1s-Nr-xZm"/>
                                                        </constraints>
                                                    </stackView>
                                                    <view contentMode="scaleToFill" verticalCompressionResistancePriority="751" translatesAutoresizingMaskIntoConstraints="NO" id="EfX-WK-kyy" customClass="DashedLineView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="31" y="62" width="2" height="12"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="2" id="OXl-Al-3XJ"/>
                                                            <constraint firstAttribute="height" constant="12" id="Wby-hv-xBu"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="lineWidth">
                                                                <real key="value" value="2"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="unpaintedSegmentLength">
                                                                <real key="value" value="1.5"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="paintedSegmentLength">
                                                                <real key="value" value="1.5"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="color" keyPath="color">
                                                                <color key="value" name="PrimaryGrownGreen"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isHorizontal" value="NO"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="21" translatesAutoresizingMaskIntoConstraints="NO" id="WqH-ab-PTK">
                                                        <rect key="frame" x="16" y="84" width="313" height="32"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" verticalCompressionResistancePriority="751" translatesAutoresizingMaskIntoConstraints="NO" id="U4w-ua-wsb" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="32" height="32"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="2" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="oiP-9s-IFQ">
                                                                        <rect key="frame" x="12" y="7.6666666666666856" width="8.3333333333333357" height="17"/>
                                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="14"/>
                                                                        <color key="textColor" name="PrimaryGrownGreen"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" name="SecondaryLightGreen"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="32" id="Gnh-8r-KNs"/>
                                                                    <constraint firstItem="oiP-9s-IFQ" firstAttribute="centerY" secondItem="U4w-ua-wsb" secondAttribute="centerY" id="T5q-ac-rSu"/>
                                                                    <constraint firstAttribute="height" constant="32" id="TJ0-j9-YJF"/>
                                                                    <constraint firstItem="oiP-9s-IFQ" firstAttribute="centerX" secondItem="U4w-ua-wsb" secondAttribute="centerX" id="j8g-4T-0Kh"/>
                                                                </constraints>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                        <real key="value" value="16"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Record a video" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="io9-L1-I6Y">
                                                                <rect key="frame" x="53" y="7.6666666666666856" width="260" height="17"/>
                                                                <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                                <color key="textColor" name="PrimaryBlack"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="32" id="trz-Mw-XTr"/>
                                                        </constraints>
                                                    </stackView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lgG-rc-DUw" customClass="DashedLineView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="31" y="126" width="2" height="12"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="12" id="13M-8c-dbz"/>
                                                            <constraint firstAttribute="width" constant="2" id="Xcf-dk-8vF"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="lineWidth">
                                                                <real key="value" value="2"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="unpaintedSegmentLength">
                                                                <real key="value" value="1.5"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="paintedSegmentLength">
                                                                <real key="value" value="1.5"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="color" keyPath="color">
                                                                <color key="value" name="PrimaryGrownGreen"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isHorizontal" value="NO"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="21" translatesAutoresizingMaskIntoConstraints="NO" id="KGM-q7-xi6">
                                                        <rect key="frame" x="16" y="148" width="313" height="32"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="laI-io-xPc" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="32" height="32"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="3" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ff6-GR-uUr">
                                                                        <rect key="frame" x="12" y="7.6666666666666288" width="8.3333333333333357" height="17"/>
                                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="14"/>
                                                                        <color key="textColor" name="PrimaryGrownGreen"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" name="SecondaryLightGreen"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="32" id="UNG-rQ-P37"/>
                                                                    <constraint firstAttribute="height" constant="32" id="owl-5q-KsI"/>
                                                                    <constraint firstItem="Ff6-GR-uUr" firstAttribute="centerY" secondItem="laI-io-xPc" secondAttribute="centerY" id="ygb-Ox-tCw"/>
                                                                    <constraint firstItem="Ff6-GR-uUr" firstAttribute="centerX" secondItem="laI-io-xPc" secondAttribute="centerX" id="zps-Lq-NND"/>
                                                                </constraints>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                        <real key="value" value="16"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Enter your personal information" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Vgn-c2-8lD">
                                                                <rect key="frame" x="53" y="7.6666666666666288" width="260" height="17"/>
                                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="14"/>
                                                                <color key="textColor" name="PrimaryBlack"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="32" id="Q8x-Pa-VR6"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" name="SecondaryLightGreen"/>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="KGM-q7-xi6" secondAttribute="trailing" constant="16" id="2BD-XC-mkX"/>
                                                    <constraint firstAttribute="bottom" secondItem="KGM-q7-xi6" secondAttribute="bottom" constant="16" id="CGh-b7-wJe"/>
                                                    <constraint firstAttribute="trailing" secondItem="WqH-ab-PTK" secondAttribute="trailing" constant="16" id="DZT-Mh-Nf5"/>
                                                    <constraint firstItem="KGM-q7-xi6" firstAttribute="leading" secondItem="XAq-6L-BaN" secondAttribute="leading" constant="16" id="DzX-MI-rPT"/>
                                                    <constraint firstItem="6KK-M4-UVK" firstAttribute="top" secondItem="XAq-6L-BaN" secondAttribute="top" constant="18" id="S4Y-0n-LoF"/>
                                                    <constraint firstItem="WqH-ab-PTK" firstAttribute="leading" secondItem="XAq-6L-BaN" secondAttribute="leading" constant="16" id="Um6-Xe-j93"/>
                                                    <constraint firstItem="EfX-WK-kyy" firstAttribute="top" secondItem="JrZ-W9-FUh" secondAttribute="bottom" constant="10" id="c70-Ew-pSp"/>
                                                    <constraint firstItem="WqH-ab-PTK" firstAttribute="top" secondItem="EfX-WK-kyy" secondAttribute="bottom" constant="10" id="feV-Ce-0n6"/>
                                                    <constraint firstItem="KGM-q7-xi6" firstAttribute="top" secondItem="lgG-rc-DUw" secondAttribute="bottom" constant="10" id="kyD-EU-lpM"/>
                                                    <constraint firstItem="lgG-rc-DUw" firstAttribute="top" secondItem="WqH-ab-PTK" secondAttribute="bottom" constant="10" id="qYo-9m-sSU"/>
                                                    <constraint firstItem="EfX-WK-kyy" firstAttribute="centerX" secondItem="JrZ-W9-FUh" secondAttribute="centerX" id="qbo-lC-CuX"/>
                                                    <constraint firstAttribute="trailing" secondItem="6KK-M4-UVK" secondAttribute="trailing" constant="16" id="sNM-yq-uiv"/>
                                                    <constraint firstItem="lgG-rc-DUw" firstAttribute="centerX" secondItem="EfX-WK-kyy" secondAttribute="centerX" id="vMp-i6-wuN"/>
                                                    <constraint firstItem="6KK-M4-UVK" firstAttribute="leading" secondItem="XAq-6L-BaN" secondAttribute="leading" constant="16" id="yrH-rL-cjI"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                        <real key="value" value="16"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jmM-XP-kF3">
                                                <rect key="frame" x="24" y="485" width="345" height="47.**************1"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconInfoNote" translatesAutoresizingMaskIntoConstraints="NO" id="2yO-Rt-jEl">
                                                        <rect key="frame" x="0.0" y="0.0" width="16.**************8" height="16.**************8"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" secondItem="2yO-Rt-jEl" secondAttribute="height" id="HPR-3i-Lqp"/>
                                                            <constraint firstAttribute="width" constant="16.6**************" id="Iyi-mP-FOa"/>
                                                        </constraints>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RZU-Gj-TD8">
                                                        <rect key="frame" x="22.***************" y="0.0" width="322.**************" height="47.***************"/>
                                                        <string key="text">Note: Please make sure you have a stable internet connection throughout the process. Standard telco rates apply if you’re using a data connection.</string>
                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="12"/>
                                                        <color key="textColor" name="Gray140S"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstItem="2yO-Rt-jEl" firstAttribute="leading" secondItem="jmM-XP-kF3" secondAttribute="leading" id="7as-me-mCk"/>
                                                    <constraint firstItem="RZU-Gj-TD8" firstAttribute="top" secondItem="jmM-XP-kF3" secondAttribute="top" id="CZP-gQ-N9F"/>
                                                    <constraint firstAttribute="trailing" secondItem="RZU-Gj-TD8" secondAttribute="trailing" id="YQW-WZ-Cgr"/>
                                                    <constraint firstAttribute="bottom" secondItem="RZU-Gj-TD8" secondAttribute="bottom" id="suQ-0S-qMT"/>
                                                    <constraint firstItem="RZU-Gj-TD8" firstAttribute="leading" secondItem="2yO-Rt-jEl" secondAttribute="trailing" constant="6" id="vLk-QK-6ke"/>
                                                    <constraint firstItem="2yO-Rt-jEl" firstAttribute="top" secondItem="jmM-XP-kF3" secondAttribute="top" id="ztn-zZ-2R3"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="jmM-XP-kF3" secondAttribute="trailing" constant="24" id="9Cq-aK-i5l"/>
                                            <constraint firstAttribute="height" constant="576" id="Hrk-aw-pjU"/>
                                            <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="jmM-XP-kF3" secondAttribute="bottom" constant="16" id="PaA-6W-1Nv"/>
                                            <constraint firstItem="E8Z-y5-Ako" firstAttribute="top" secondItem="8iy-bx-obC" secondAttribute="top" constant="24" id="TGk-gX-FOZ"/>
                                            <constraint firstItem="jmM-XP-kF3" firstAttribute="top" secondItem="XAq-6L-BaN" secondAttribute="bottom" constant="16" id="VhP-6X-K2V"/>
                                            <constraint firstItem="XAq-6L-BaN" firstAttribute="top" secondItem="nr5-LM-J1X" secondAttribute="bottom" constant="24" id="YbT-ZB-7lh"/>
                                            <constraint firstItem="E8Z-y5-Ako" firstAttribute="leading" secondItem="8iy-bx-obC" secondAttribute="leading" constant="24" id="fbj-hF-6Qp"/>
                                            <constraint firstAttribute="trailing" secondItem="nr5-LM-J1X" secondAttribute="trailing" constant="24" id="l4z-ns-zKW"/>
                                            <constraint firstItem="jmM-XP-kF3" firstAttribute="leading" secondItem="8iy-bx-obC" secondAttribute="leading" constant="24" id="oBG-U2-zA0"/>
                                            <constraint firstItem="nr5-LM-J1X" firstAttribute="top" secondItem="E8Z-y5-Ako" secondAttribute="bottom" constant="24" id="vYn-by-evv"/>
                                            <constraint firstAttribute="trailing" secondItem="XAq-6L-BaN" secondAttribute="trailing" constant="24" id="x6w-96-X46"/>
                                            <constraint firstItem="nr5-LM-J1X" firstAttribute="leading" secondItem="8iy-bx-obC" secondAttribute="leading" constant="24" id="xzi-Sp-CSa"/>
                                            <constraint firstItem="XAq-6L-BaN" firstAttribute="leading" secondItem="8iy-bx-obC" secondAttribute="leading" constant="24" id="zVE-A5-c1f"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="8iy-bx-obC" firstAttribute="leading" secondItem="qWd-g7-HZH" secondAttribute="leading" id="C8q-wf-Qap"/>
                                    <constraint firstItem="8iy-bx-obC" firstAttribute="width" secondItem="qWd-g7-HZH" secondAttribute="width" id="OFC-Se-ClI"/>
                                    <constraint firstItem="8iy-bx-obC" firstAttribute="leading" secondItem="qWd-g7-HZH" secondAttribute="leading" id="TrJ-S0-P25"/>
                                    <constraint firstAttribute="bottom" secondItem="8iy-bx-obC" secondAttribute="bottom" id="Yh9-Sh-GM6"/>
                                    <constraint firstAttribute="trailing" secondItem="8iy-bx-obC" secondAttribute="trailing" id="ZvA-nV-rnp"/>
                                    <constraint firstItem="8iy-bx-obC" firstAttribute="top" secondItem="qWd-g7-HZH" secondAttribute="top" id="pzH-Pc-ran"/>
                                    <constraint firstItem="8iy-bx-obC" firstAttribute="trailing" secondItem="qWd-g7-HZH" secondAttribute="trailing" id="shZ-XV-yrX"/>
                                </constraints>
                            </scrollView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4jE-Gh-csm" customClass="FadedBackgroundView" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="698" width="393" height="120"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="MqB-Qc-xU3" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="24" y="32" width="345" height="56"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="Vi4-iz-5se"/>
                                        </constraints>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="Next"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="didTapNext:" destination="7SQ-wY-s0w" eventType="touchUpInside" id="frw-fO-BV5"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="MqB-Qc-xU3" firstAttribute="top" secondItem="4jE-Gh-csm" secondAttribute="top" constant="32" id="6S2-sh-62z"/>
                                    <constraint firstAttribute="bottom" secondItem="MqB-Qc-xU3" secondAttribute="bottom" constant="32" id="Fyi-n3-1xX"/>
                                    <constraint firstAttribute="trailing" secondItem="MqB-Qc-xU3" secondAttribute="trailing" constant="24" id="UmF-IX-gQE"/>
                                    <constraint firstItem="MqB-Qc-xU3" firstAttribute="leading" secondItem="4jE-Gh-csm" secondAttribute="leading" constant="24" id="bFZ-eW-dGg"/>
                                    <constraint firstAttribute="height" constant="120" id="pkl-Zm-oPK"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="KIJ-ik-5sT"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="qWd-g7-HZH" firstAttribute="width" secondItem="0FA-8b-i8R" secondAttribute="width" id="1Gy-bg-Amj"/>
                            <constraint firstItem="qWd-g7-HZH" firstAttribute="top" secondItem="KIJ-ik-5sT" secondAttribute="top" id="MNm-x6-Dkp"/>
                            <constraint firstItem="4jE-Gh-csm" firstAttribute="top" secondItem="qWd-g7-HZH" secondAttribute="bottom" id="Quq-xP-avJ"/>
                            <constraint firstItem="4jE-Gh-csm" firstAttribute="trailing" secondItem="KIJ-ik-5sT" secondAttribute="trailing" id="UPi-Lw-L1o"/>
                            <constraint firstItem="qWd-g7-HZH" firstAttribute="leading" secondItem="KIJ-ik-5sT" secondAttribute="leading" id="WfL-Na-zka"/>
                            <constraint firstItem="KIJ-ik-5sT" firstAttribute="bottom" secondItem="4jE-Gh-csm" secondAttribute="bottom" id="cCv-k6-E9H"/>
                            <constraint firstItem="4jE-Gh-csm" firstAttribute="leading" secondItem="KIJ-ik-5sT" secondAttribute="leading" id="evX-u5-Re8"/>
                        </constraints>
                    </view>
                    <toolbarItems/>
                    <navigationItem key="navigationItem" id="FvP-Lk-8C7">
                        <barButtonItem key="leftBarButtonItem" title="Item" id="wXd-IE-gLF">
                            <connections>
                                <action selector="didTapBack:" destination="7SQ-wY-s0w" id="sxk-UA-8v6"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <connections>
                        <outlet property="noteLabel" destination="RZU-Gj-TD8" id="oVh-p9-mYJ"/>
                        <outlet property="titleLabel" destination="nr5-LM-J1X" id="zjK-qP-4Ix"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="YMA-yc-KgF" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
                <tapGestureRecognizer id="6vP-8N-afT">
                    <connections>
                        <action selector="didTapIdList:" destination="7SQ-wY-s0w" id="9Qb-rf-40B"/>
                    </connections>
                </tapGestureRecognizer>
            </objects>
            <point key="canvasLocation" x="227.536231884058" y="40.760869565217398"/>
        </scene>
        <!--Maya Upgrade Type View Controller-->
        <scene sceneID="69R-Zp-ojz">
            <objects>
                <viewController storyboardIdentifier="MayaUpgradeTypeViewController" id="clI-vM-6Ny" customClass="MayaUpgradeTypeViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="YUs-0y-hvd">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Dik-5l-wbz">
                                <rect key="frame" x="0.0" y="44" width="393" height="572"/>
                                <subviews>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="efm-3H-zct">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="536.**************"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="imageMayaRainbow" translatesAutoresizingMaskIntoConstraints="NO" id="f92-4O-4yp">
                                                <rect key="frame" x="24" y="24" width="161.**************" height="104"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" secondItem="f92-4O-4yp" secondAttribute="height" multiplier="160:103" id="IBA-pp-DXb"/>
                                                    <constraint firstAttribute="width" secondItem="f92-4O-4yp" secondAttribute="height" multiplier="160:103" id="T6x-nw-odo"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="751" text="How do you identify yourself?" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="AWI-iA-sSB">
                                                <rect key="frame" x="24" y="152" width="345" height="58.333333333333343"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="751" text="Select an option that best represents you" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rHO-sr-HG2">
                                                <rect key="frame" x="24" y="218.**************" width="345" height="17"/>
                                                <fontDescription key="fontDescription" name="Jeko-Regular" family="Jeko" pointSize="14"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" scrollEnabled="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="72" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="6px-zR-kdL" customClass="IntrinsicTableView" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="16" y="265.**************" width="361" height="269.99999999999994"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="270" id="tK8-EW-Dvh"/>
                                                </constraints>
                                                <prototypes>
                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="MayaUpgradeTypeTableViewCell" id="tDm-f4-wTk" customClass="MayaUpgradeTypeTableViewCell" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="50" width="361" height="67.666664123535156"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="tDm-f4-wTk" id="DlZ-lY-Eqr">
                                                            <rect key="frame" x="0.0" y="0.0" width="361" height="67.666664123535156"/>
                                                            <autoresizingMask key="autoresizingMask"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="orI-Ql-Mn1" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                                    <rect key="frame" x="8" y="5.**************64" width="345" height="55.***************"/>
                                                                    <subviews>
                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconRadioButtonSelected" translatesAutoresizingMaskIntoConstraints="NO" id="WaK-kw-o2W">
                                                                            <rect key="frame" x="16" y="18" width="24" height="24"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="width" constant="24" id="nM9-0T-yn3"/>
                                                                                <constraint firstAttribute="width" secondItem="WaK-kw-o2W" secondAttribute="height" id="yS7-eb-13K"/>
                                                                            </constraints>
                                                                        </imageView>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="751" text="I am a foreigner below 18 years old" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KGG-6f-AyR">
                                                                            <rect key="frame" x="52" y="18" width="277" height="19.666666666666671"/>
                                                                            <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="16"/>
                                                                            <nil key="textColor"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" name="PrimaryWhite"/>
                                                                    <constraints>
                                                                        <constraint firstItem="KGG-6f-AyR" firstAttribute="leading" secondItem="WaK-kw-o2W" secondAttribute="trailing" constant="12" id="0t8-o1-TDn"/>
                                                                        <constraint firstItem="KGG-6f-AyR" firstAttribute="top" secondItem="orI-Ql-Mn1" secondAttribute="top" constant="18" id="8ac-sn-Q1U"/>
                                                                        <constraint firstAttribute="bottom" secondItem="KGG-6f-AyR" secondAttribute="bottom" constant="18" id="Ctg-Tf-hT6"/>
                                                                        <constraint firstAttribute="trailing" secondItem="KGG-6f-AyR" secondAttribute="trailing" constant="16" id="QeD-0L-EnV"/>
                                                                        <constraint firstItem="WaK-kw-o2W" firstAttribute="top" secondItem="KGG-6f-AyR" secondAttribute="top" id="TZc-3Y-40C"/>
                                                                        <constraint firstItem="WaK-kw-o2W" firstAttribute="leading" secondItem="orI-Ql-Mn1" secondAttribute="leading" constant="16" id="WG0-xV-8nt"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                            <real key="value" value="16"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                                            <color key="value" name="Grey1"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                                            <real key="value" value="2"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                            </subviews>
                                                            <constraints>
                                                                <constraint firstItem="orI-Ql-Mn1" firstAttribute="leading" secondItem="DlZ-lY-Eqr" secondAttribute="leading" constant="8" id="BxD-zE-iCW"/>
                                                                <constraint firstAttribute="trailing" secondItem="orI-Ql-Mn1" secondAttribute="trailing" constant="8" id="UZP-nz-e5r"/>
                                                                <constraint firstItem="orI-Ql-Mn1" firstAttribute="top" secondItem="DlZ-lY-Eqr" secondAttribute="top" constant="6" id="drG-DS-mJr"/>
                                                                <constraint firstAttribute="bottom" secondItem="orI-Ql-Mn1" secondAttribute="bottom" constant="6" id="nc3-bX-els"/>
                                                            </constraints>
                                                        </tableViewCellContentView>
                                                        <connections>
                                                            <outlet property="containerView" destination="orI-Ql-Mn1" id="KPc-eT-Kpk"/>
                                                            <outlet property="descriptionLabel" destination="KGG-6f-AyR" id="H1E-Dv-4WU"/>
                                                            <outlet property="radioButtonImageView" destination="WaK-kw-o2W" id="g1S-la-XI9"/>
                                                        </connections>
                                                    </tableViewCell>
                                                </prototypes>
                                                <sections/>
                                                <connections>
                                                    <outlet property="dataSource" destination="clI-vM-6Ny" id="b2a-SU-7ax"/>
                                                    <outlet property="delegate" destination="clI-vM-6Ny" id="uce-a8-K2G"/>
                                                </connections>
                                            </tableView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="f92-4O-4yp" firstAttribute="top" secondItem="efm-3H-zct" secondAttribute="top" constant="24" id="1XP-LS-4D0"/>
                                            <constraint firstItem="AWI-iA-sSB" firstAttribute="top" secondItem="f92-4O-4yp" secondAttribute="bottom" constant="24" id="1eo-Oa-zKB"/>
                                            <constraint firstAttribute="trailing" secondItem="AWI-iA-sSB" secondAttribute="trailing" constant="24" id="27t-kH-nEy"/>
                                            <constraint firstAttribute="trailing" secondItem="rHO-sr-HG2" secondAttribute="trailing" constant="24" id="8bB-PA-sHR"/>
                                            <constraint firstItem="rHO-sr-HG2" firstAttribute="top" secondItem="AWI-iA-sSB" secondAttribute="bottom" constant="8" id="BSP-zV-pqj"/>
                                            <constraint firstItem="6px-zR-kdL" firstAttribute="top" secondItem="rHO-sr-HG2" secondAttribute="bottom" constant="30" id="UH0-le-ALS"/>
                                            <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="6px-zR-kdL" secondAttribute="bottom" constant="30" id="Wue-dF-7u1"/>
                                            <constraint firstItem="f92-4O-4yp" firstAttribute="leading" secondItem="efm-3H-zct" secondAttribute="leading" constant="24" id="kRo-mw-30l"/>
                                            <constraint firstAttribute="trailing" secondItem="6px-zR-kdL" secondAttribute="trailing" constant="16" id="mkJ-21-Bba"/>
                                            <constraint firstItem="6px-zR-kdL" firstAttribute="leading" secondItem="efm-3H-zct" secondAttribute="leading" constant="16" id="w17-gk-h7x"/>
                                            <constraint firstItem="rHO-sr-HG2" firstAttribute="leading" secondItem="efm-3H-zct" secondAttribute="leading" constant="24" id="z7y-ZK-lWx"/>
                                            <constraint firstItem="AWI-iA-sSB" firstAttribute="leading" secondItem="efm-3H-zct" secondAttribute="leading" constant="24" id="zcq-vO-ixp"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="efm-3H-zct" firstAttribute="leading" secondItem="Dik-5l-wbz" secondAttribute="leading" id="9dj-vN-Qrk"/>
                                    <constraint firstItem="efm-3H-zct" firstAttribute="top" secondItem="Dik-5l-wbz" secondAttribute="top" id="T2G-K9-FRh"/>
                                    <constraint firstAttribute="trailing" secondItem="efm-3H-zct" secondAttribute="trailing" id="Wxv-rT-xLc"/>
                                    <constraint firstAttribute="bottom" secondItem="efm-3H-zct" secondAttribute="bottom" id="hXZ-dZ-3jK"/>
                                </constraints>
                            </scrollView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tS7-Tt-Vzb" customClass="FadedBackgroundView" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="698" width="393" height="120"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9Oa-rL-EDD" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="24" y="32" width="345" height="56"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="0a6-2s-i0f"/>
                                        </constraints>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="Next"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="didTapNext:" destination="clI-vM-6Ny" eventType="touchUpInside" id="Nqr-81-GvR"/>
                                            <action selector="didTapNext:" destination="7SQ-wY-s0w" eventType="touchUpInside" id="a36-8R-wm6"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="9Oa-rL-EDD" firstAttribute="leading" secondItem="tS7-Tt-Vzb" secondAttribute="leading" constant="24" id="0fn-Ud-TWl"/>
                                    <constraint firstItem="9Oa-rL-EDD" firstAttribute="top" secondItem="tS7-Tt-Vzb" secondAttribute="top" constant="32" id="AfA-zQ-A4F"/>
                                    <constraint firstAttribute="bottom" secondItem="9Oa-rL-EDD" secondAttribute="bottom" constant="32" id="cBq-by-Zva"/>
                                    <constraint firstAttribute="height" constant="120" id="e2X-GS-aVZ"/>
                                    <constraint firstAttribute="trailing" secondItem="9Oa-rL-EDD" secondAttribute="trailing" constant="24" id="k1J-sM-cHl"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="arz-rG-R6D"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="arz-rG-R6D" firstAttribute="trailing" secondItem="Dik-5l-wbz" secondAttribute="trailing" id="6tO-fS-XNr"/>
                            <constraint firstItem="tS7-Tt-Vzb" firstAttribute="trailing" secondItem="arz-rG-R6D" secondAttribute="trailing" id="DK9-dn-GmK"/>
                            <constraint firstItem="arz-rG-R6D" firstAttribute="bottom" secondItem="tS7-Tt-Vzb" secondAttribute="bottom" id="Q9B-BQ-qqt"/>
                            <constraint firstItem="tS7-Tt-Vzb" firstAttribute="top" secondItem="Dik-5l-wbz" secondAttribute="bottom" id="SeT-y6-KXW"/>
                            <constraint firstItem="Dik-5l-wbz" firstAttribute="leading" secondItem="arz-rG-R6D" secondAttribute="leading" id="Tkl-2c-wak"/>
                            <constraint firstItem="Dik-5l-wbz" firstAttribute="top" secondItem="arz-rG-R6D" secondAttribute="top" id="WDX-Yu-REN"/>
                            <constraint firstItem="tS7-Tt-Vzb" firstAttribute="leading" secondItem="arz-rG-R6D" secondAttribute="leading" id="X0P-s9-AZM"/>
                            <constraint firstItem="efm-3H-zct" firstAttribute="width" secondItem="YUs-0y-hvd" secondAttribute="width" id="YdZ-DX-OY4"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="7tq-mc-s4L">
                        <barButtonItem key="leftBarButtonItem" title="Back" id="co3-F7-EJ9">
                            <connections>
                                <action selector="didTapBack:" destination="clI-vM-6Ny" id="e6T-tB-3p6"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <connections>
                        <outlet property="tableView" destination="6px-zR-kdL" id="rIo-cR-ctA"/>
                        <outlet property="tableViewHeightConstraint" destination="tK8-EW-Dvh" id="k2I-Dc-qSg"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="e22-Qm-bFj" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="905.79710144927549" y="41.576086956521742"/>
        </scene>
        <!--Maya ReKYC OtherID View Controller-->
        <scene sceneID="mB0-uQ-7tN">
            <objects>
                <viewController storyboardIdentifier="MayaReKYCOtherIDViewController" id="nbp-zt-eRO" customClass="MayaReKYCOtherIDViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="POp-GW-dqw">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="1000"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Qqj-qm-IyT">
                                <rect key="frame" x="0.0" y="103" width="414" height="863"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="DTz-01-wSh" userLabel="Content View">
                                        <rect key="frame" x="0.0" y="0.0" width="414" height="976"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="TuK-LV-t0U">
                                                <rect key="frame" x="24" y="16" width="366" height="910"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Updating your account with Other IDs" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yZ4-gb-KXP">
                                                        <rect key="frame" x="0.0" y="0.0" width="366" height="58.***************"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                                        <color key="textColor" name="PrimaryBlack"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="skeletonCornerRadius">
                                                                <real key="value" value="5"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="I6n-I2-5F1" userLabel="Spacer 24 pixel">
                                                        <rect key="frame" x="0.0" y="58.333333333333343" width="366" height="24"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="24" id="FII-7E-frs"/>
                                                        </constraints>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="top" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="iid-Eh-HgT" userLabel="First Section Stack View">
                                                        <rect key="frame" x="0.0" y="82.333333333333343" width="366" height="34"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="67c-g5-eze" userLabel="One - View" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="32" height="32"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TtN-me-ZZb">
                                                                        <rect key="frame" x="13.3**************" y="7.***************2" width="5.3333333333333339" height="17"/>
                                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="14"/>
                                                                        <color key="textColor" name="PrimaryGrownGreen"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" name="SecondaryLightGreen"/>
                                                                <constraints>
                                                                    <constraint firstItem="TtN-me-ZZb" firstAttribute="centerY" secondItem="67c-g5-eze" secondAttribute="centerY" id="6MB-RQ-uw5"/>
                                                                    <constraint firstItem="TtN-me-ZZb" firstAttribute="centerX" secondItem="67c-g5-eze" secondAttribute="centerX" id="bs3-fI-Nth"/>
                                                                    <constraint firstAttribute="width" constant="32" id="ySR-yJ-WSp"/>
                                                                    <constraint firstAttribute="height" constant="32" id="yjW-VR-bpt"/>
                                                                </constraints>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                        <real key="value" value="16"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="751" text="Take a picture of any of the IDs below. Make sure to include the FRONT and BACK of your ID." lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Nnv-va-Ife">
                                                                <rect key="frame" x="44" y="0.0" width="322" height="34"/>
                                                                <fontDescription key="fontDescription" name="Jeko-Regular" family="Jeko" pointSize="14"/>
                                                                <color key="textColor" name="PrimaryBlack"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="67c-g5-eze" firstAttribute="top" secondItem="iid-Eh-HgT" secondAttribute="top" id="JQ1-JM-FmC"/>
                                                            <constraint firstItem="67c-g5-eze" firstAttribute="leading" secondItem="iid-Eh-HgT" secondAttribute="leading" id="Njw-dU-12E"/>
                                                        </constraints>
                                                    </stackView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xgv-Zo-E2i" userLabel="Spacer 16 pixel">
                                                        <rect key="frame" x="0.0" y="116.33333333333334" width="366" height="16"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="16" id="AE7-MZ-c5V"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="377-Nd-0Qj" userLabel="First Section Detail View" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="132.**************" width="366" height="333"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="lvK-XW-fFI">
                                                                <rect key="frame" x="19.333333333333343" y="19.999999999999972" width="327.33333333333326" height="293"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ovs-nm-7Iu">
                                                                        <rect key="frame" x="0.0" y="0.0" width="327.**************" height="293"/>
                                                                        <string key="text">One of the following:
 • NBI Clearance
 • ACR i-Card
 • Government Office/GOCC ID
 • IBP Card
 • School ID
 • ePhil ID (printed Philsys ID)
Any 2 of the following:
 • PhilHealth ID
 • TIN ID
 • Voter's ID
 • Police Clearance
 • Senior Citizen’s Card
 • GSIS e-Card
 • OWWA ID/OFW e-Card/Related Product/Tech Requirements</string>
                                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                                        <color key="textColor" name="Grey6"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <color key="backgroundColor" name="Grey1"/>
                                                        <constraints>
                                                            <constraint firstItem="lvK-XW-fFI" firstAttribute="top" secondItem="377-Nd-0Qj" secondAttribute="top" constant="20" id="CXp-Re-ihu"/>
                                                            <constraint firstItem="lvK-XW-fFI" firstAttribute="leading" secondItem="377-Nd-0Qj" secondAttribute="leading" constant="19.25" id="Lfm-K5-6qi"/>
                                                            <constraint firstAttribute="bottom" secondItem="lvK-XW-fFI" secondAttribute="bottom" constant="20" id="Php-w7-HRO"/>
                                                            <constraint firstAttribute="trailing" secondItem="lvK-XW-fFI" secondAttribute="trailing" constant="19.25" id="cwF-FX-dp4"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                <real key="value" value="8"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="N7L-4m-tM3" userLabel="Spacer 32 pixel">
                                                        <rect key="frame" x="0.0" y="465.**************" width="366" height="32"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="32" id="xMY-y2-2lf"/>
                                                        </constraints>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="kjU-1c-N8K" userLabel="Second Section Stack View">
                                                        <rect key="frame" x="0.0" y="497.**************" width="366" height="32"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="leD-eV-fuj" userLabel="Two - View" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="32" height="32"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="2" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yqo-wg-zhO">
                                                                        <rect key="frame" x="12" y="7.6666666666666288" width="8.3333333333333357" height="17"/>
                                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="14"/>
                                                                        <color key="textColor" name="PrimaryGrownGreen"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" name="SecondaryLightGreen"/>
                                                                <constraints>
                                                                    <constraint firstItem="yqo-wg-zhO" firstAttribute="centerY" secondItem="leD-eV-fuj" secondAttribute="centerY" id="Elh-vZ-1Ow"/>
                                                                    <constraint firstAttribute="height" constant="32" id="ZGb-Lb-LlY"/>
                                                                    <constraint firstAttribute="width" constant="32" id="d60-9h-1Zx"/>
                                                                    <constraint firstItem="yqo-wg-zhO" firstAttribute="centerX" secondItem="leD-eV-fuj" secondAttribute="centerX" id="ePV-c5-cNE"/>
                                                                </constraints>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                        <real key="value" value="16"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="751" text="Email your <NAME_EMAIL>" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RFP-Eu-0zi">
                                                                <rect key="frame" x="44" y="0.0" width="322" height="32"/>
                                                                <fontDescription key="fontDescription" name="Jeko-Regular" family="Jeko" pointSize="14"/>
                                                                <color key="textColor" name="PrimaryBlack"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="leD-eV-fuj" firstAttribute="leading" secondItem="kjU-1c-N8K" secondAttribute="leading" id="9ct-On-xkV"/>
                                                            <constraint firstItem="leD-eV-fuj" firstAttribute="top" secondItem="kjU-1c-N8K" secondAttribute="top" id="VKY-5K-yie"/>
                                                        </constraints>
                                                    </stackView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZYQ-7C-eFl" userLabel="Spacer 16 pixel">
                                                        <rect key="frame" x="0.0" y="529.**************" width="366" height="16"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="16" id="pY2-HV-LId"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1sD-e7-hNs" userLabel="Second Section Detail View" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="545.**************" width="366" height="186.66666666666663"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="8BU-Id-hSh">
                                                                <rect key="frame" x="19.333333333333343" y="20" width="327.33333333333326" height="146.**************"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ceV-ow-frT">
                                                                        <rect key="frame" x="0.0" y="0.0" width="327.**************" height="146.**************"/>
                                                                        <string key="text">Email your ID with the subject: Account Update for &lt;your mobile number&gt;
Include in the email the following :
 • Your full name
 • Mobile number you use in Maya
 • The type of ID you submitted
 • ID number
 • ID expiry date</string>
                                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                                        <color key="textColor" name="Grey6"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <color key="backgroundColor" name="Grey1"/>
                                                        <constraints>
                                                            <constraint firstAttribute="bottom" secondItem="8BU-Id-hSh" secondAttribute="bottom" constant="20" id="2bu-qI-zh3"/>
                                                            <constraint firstItem="8BU-Id-hSh" firstAttribute="leading" secondItem="1sD-e7-hNs" secondAttribute="leading" constant="19.25" id="S2g-d7-ke1"/>
                                                            <constraint firstItem="8BU-Id-hSh" firstAttribute="top" secondItem="1sD-e7-hNs" secondAttribute="top" constant="20" id="bJj-rO-s76"/>
                                                            <constraint firstAttribute="trailing" secondItem="8BU-Id-hSh" secondAttribute="trailing" constant="19.25" id="rv6-QO-OHt"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                <real key="value" value="8"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5Tb-Qc-qBd" userLabel="Spacer 32 pixel">
                                                        <rect key="frame" x="0.0" y="732" width="366" height="32"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="32" id="3uo-xw-RtU"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4DW-qW-Izb" userLabel="Note View">
                                                        <rect key="frame" x="0.0" y="764" width="366" height="50"/>
                                                        <subviews>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconInfoNote" translatesAutoresizingMaskIntoConstraints="NO" id="JID-Wx-lI4">
                                                                <rect key="frame" x="0.0" y="0.0" width="16.**************8" height="16.**************8"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="16.6**************" id="nsY-Jr-NGG"/>
                                                                    <constraint firstAttribute="width" secondItem="JID-Wx-lI4" secondAttribute="height" id="vCX-US-puR"/>
                                                                </constraints>
                                                            </imageView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tGV-bg-yjz">
                                                                <rect key="frame" x="22.***************" y="0.0" width="343.**************" height="50"/>
                                                                <string key="text">We’ll notify you once we receive and validate your IDs. From there, you can continue opening or using your Maya Savings account.</string>
                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="12"/>
                                                                <color key="textColor" name="Grey6"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="JID-Wx-lI4" firstAttribute="top" secondItem="4DW-qW-Izb" secondAttribute="top" id="4N4-cB-WMv"/>
                                                            <constraint firstItem="tGV-bg-yjz" firstAttribute="leading" secondItem="JID-Wx-lI4" secondAttribute="trailing" constant="6" id="9jT-On-BwZ"/>
                                                            <constraint firstItem="JID-Wx-lI4" firstAttribute="leading" secondItem="4DW-qW-Izb" secondAttribute="leading" id="PD1-Qm-Y1O"/>
                                                            <constraint firstItem="tGV-bg-yjz" firstAttribute="top" secondItem="4DW-qW-Izb" secondAttribute="top" id="VFF-FD-dJR"/>
                                                            <constraint firstAttribute="bottom" secondItem="tGV-bg-yjz" secondAttribute="bottom" id="ZY4-2D-MPr"/>
                                                            <constraint firstItem="tGV-bg-yjz" firstAttribute="top" secondItem="4DW-qW-Izb" secondAttribute="top" id="cbf-Mv-JpQ"/>
                                                            <constraint firstAttribute="trailing" secondItem="tGV-bg-yjz" secondAttribute="trailing" id="ggm-6p-GNk"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qp5-DH-iFs" userLabel="Spacer 40 pixel">
                                                        <rect key="frame" x="0.0" y="814" width="366" height="40"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="40" id="YYv-ib-rfb"/>
                                                        </constraints>
                                                    </view>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="p1V-EF-AQk" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="854" width="366" height="56"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="56" id="xrA-21-1fe"/>
                                                        </constraints>
                                                        <state key="normal" title="Button"/>
                                                        <buttonConfiguration key="configuration" style="plain" title="Got it"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                <integer key="value" value="0"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                                <integer key="value" value="0"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="didTapBack:" destination="nbp-zt-eRO" eventType="touchUpInside" id="diH-aF-Bun"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="TuK-LV-t0U" firstAttribute="top" secondItem="DTz-01-wSh" secondAttribute="top" constant="16" id="TcV-ix-RNj"/>
                                            <constraint firstAttribute="trailing" secondItem="TuK-LV-t0U" secondAttribute="trailing" constant="24" id="nr0-z2-N5q"/>
                                            <constraint firstItem="TuK-LV-t0U" firstAttribute="leading" secondItem="DTz-01-wSh" secondAttribute="leading" constant="24" id="ttb-Od-8Jx"/>
                                            <constraint firstAttribute="bottom" secondItem="TuK-LV-t0U" secondAttribute="bottom" constant="50" id="ysU-7t-YRE"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="DTz-01-wSh" firstAttribute="top" secondItem="d4R-pR-5sK" secondAttribute="top" id="I8U-ee-I0C"/>
                                    <constraint firstItem="DTz-01-wSh" firstAttribute="width" secondItem="Smg-0b-7C3" secondAttribute="width" id="IDY-Vo-fXv"/>
                                    <constraint firstItem="d4R-pR-5sK" firstAttribute="trailing" secondItem="DTz-01-wSh" secondAttribute="trailing" id="SId-ia-cPA"/>
                                    <constraint firstItem="DTz-01-wSh" firstAttribute="leading" secondItem="d4R-pR-5sK" secondAttribute="leading" id="biY-rt-BYC"/>
                                    <constraint firstItem="d4R-pR-5sK" firstAttribute="bottom" secondItem="DTz-01-wSh" secondAttribute="bottom" id="mYx-a9-Hk8"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="d4R-pR-5sK"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="Smg-0b-7C3"/>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="MIr-8j-D4s"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="Qqj-qm-IyT" firstAttribute="leading" secondItem="MIr-8j-D4s" secondAttribute="leading" id="32q-iY-GLl"/>
                            <constraint firstItem="Qqj-qm-IyT" firstAttribute="top" secondItem="MIr-8j-D4s" secondAttribute="top" id="Ncz-pW-nVY"/>
                            <constraint firstItem="MIr-8j-D4s" firstAttribute="trailing" secondItem="Qqj-qm-IyT" secondAttribute="trailing" id="YFY-lY-SQN"/>
                            <constraint firstItem="MIr-8j-D4s" firstAttribute="bottom" secondItem="Qqj-qm-IyT" secondAttribute="bottom" id="Zae-XV-5UJ"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="fBk-mQ-sS6">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="V8C-CX-phW">
                            <connections>
                                <action selector="didTapBack:" destination="nbp-zt-eRO" id="KXC-4m-mAE"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <size key="freeformSize" width="414" height="1000"/>
                    <connections>
                        <outlet property="actionButton" destination="p1V-EF-AQk" id="Nso-nw-KDd"/>
                        <outlet property="firstSectionLabel" destination="TtN-me-ZZb" id="EOc-sr-G5s"/>
                        <outlet property="firstSectionNoteLabel" destination="ovs-nm-7Iu" id="0oP-az-DhV"/>
                        <outlet property="firstSectionTitleLabel" destination="Nnv-va-Ife" id="wpZ-b7-HHP"/>
                        <outlet property="noteLabel" destination="tGV-bg-yjz" id="U0f-gH-Xaj"/>
                        <outlet property="secondSectionLabel" destination="yqo-wg-zhO" id="zFg-FF-pnK"/>
                        <outlet property="secondSectionNoteLabel" destination="ceV-ow-frT" id="zAS-47-nZm"/>
                        <outlet property="secondSectionTitleLabel" destination="RFP-Eu-0zi" id="LSd-g1-vNQ"/>
                        <outlet property="titleLabel" destination="yZ4-gb-KXP" id="nG4-UK-OMv"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="seO-ft-UxX" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-797.10144927536237" y="1708.6956521739132"/>
        </scene>
        <!--MayaEKYC Simple CaptureID View Controller-->
        <scene sceneID="aBs-7G-B2O">
            <objects>
                <viewController storyboardIdentifier="MayaEKYCSimpleCaptureIDViewController" extendedLayoutIncludesOpaqueBars="YES" id="CkD-at-bPQ" customClass="MayaEKYCSimpleCaptureIDViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="qVy-zs-DZV">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view userInteractionEnabled="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="TaI-wK-mDC" userLabel="CameraPreview" customClass="RecordPreviewView" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4FG-da-hfG">
                                <rect key="frame" x="20" y="69" width="22" height="22"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_id_scanner_back_button"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="22" id="4aa-1A-VCq"/>
                                    <constraint firstAttribute="width" constant="22" id="GPS-F4-44v"/>
                                </constraints>
                                <state key="normal" image="iconBackWhite">
                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="didTapBack:" destination="CkD-at-bPQ" eventType="touchUpInside" id="O59-8s-ybm"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Front of ID" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4tw-Y8-0Il">
                                <rect key="frame" x="155.**************" y="70.***************" width="82" height="19.666666666666671"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_id_scanner_title_label"/>
                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="2/3" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="r8h-lk-3JL">
                                <rect key="frame" x="-311" y="71.666666666666671" width="684" height="17"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_id_scanner_title_label"/>
                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="14"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2Yn-qk-UVU" userLabel="Capture Frame View">
                                <rect key="frame" x="0.0" y="100" width="393" height="562.**************"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="1Sg-wZ-ukn">
                                        <rect key="frame" x="20" y="15" width="353" height="287.66666666666669"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Position your ID in the frame" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="wZO-Zo-fOX">
                                                <rect key="frame" x="0.0" y="0.0" width="353" height="19.**************8"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_id_scanner_position_id_label"/>
                                                <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="16"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <imageView opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" verticalCompressionResistancePriority="250" image="imageSimpleCaptureIDFrame" translatesAutoresizingMaskIntoConstraints="NO" id="HYf-P4-PJJ" userLabel="imageSimpleCaptureIDOverlay">
                                                <rect key="frame" x="0.0" y="27.***************" width="353" height="260"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_id_scanner_passport_guide">
                                                    <bool key="isElement" value="YES"/>
                                                </accessibility>
                                            </imageView>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="1Sg-wZ-ukn" secondAttribute="trailing" constant="20" id="IMy-l0-Pqv"/>
                                    <constraint firstItem="1Sg-wZ-ukn" firstAttribute="top" secondItem="2Yn-qk-UVU" secondAttribute="top" constant="15" id="cKD-Kw-3KJ"/>
                                    <constraint firstItem="1Sg-wZ-ukn" firstAttribute="centerX" secondItem="2Yn-qk-UVU" secondAttribute="centerX" id="pbc-LM-d36"/>
                                    <constraint firstItem="1Sg-wZ-ukn" firstAttribute="leading" secondItem="2Yn-qk-UVU" secondAttribute="leading" constant="20" id="wsU-lZ-PBS"/>
                                </constraints>
                            </view>
                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Focusing on ID" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3wq-Q0-9QT">
                                <rect key="frame" x="140.33333333333334" y="383" width="112.33333333333334" height="19.666666666666686"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_id_scanner_position_id_label"/>
                                <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="16"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4CN-eS-EkK" userLabel="tipsBGView">
                                <rect key="frame" x="20" y="672.**************" width="353" height="49.666666666666629"/>
                                <subviews>
                                    <view alpha="0.40000000000000002" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="DyG-hT-7Td" userLabel="blurryView">
                                        <rect key="frame" x="0.0" y="0.0" width="353" height="49.**************4"/>
                                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="16"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_id_scanner_tips_view">
                                    <bool key="isElement" value="YES"/>
                                </accessibility>
                                <constraints>
                                    <constraint firstItem="DyG-hT-7Td" firstAttribute="top" secondItem="4CN-eS-EkK" secondAttribute="top" id="Er7-Iz-R4a"/>
                                    <constraint firstAttribute="trailing" secondItem="DyG-hT-7Td" secondAttribute="trailing" id="are-6y-LVG"/>
                                    <constraint firstItem="DyG-hT-7Td" firstAttribute="leading" secondItem="4CN-eS-EkK" secondAttribute="leading" id="vDf-yx-Idj"/>
                                    <constraint firstAttribute="bottom" secondItem="DyG-hT-7Td" secondAttribute="bottom" id="ylN-TQ-NkW"/>
                                </constraints>
                            </view>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconInfoFilledWhite" translatesAutoresizingMaskIntoConstraints="NO" id="q9z-Ob-jIA">
                                <rect key="frame" x="38" y="690.**************" width="17" height="17"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="17" id="m5g-kL-4we"/>
                                    <constraint firstAttribute="width" constant="17" id="owU-p7-x2i"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Put tips here..." lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bZB-iW-Ktg">
                                <rect key="frame" x="61" y="690.**************" width="296" height="15.666666666666629"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_id_scanner_tips_label"/>
                                <fontDescription key="fontDescription" name="CerebriSansPro-Medium" family="Cerebri Sans Pro" pointSize="12"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="HPL-h9-uve" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="20" y="742" width="353" height="56"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="56" id="NbI-BX-6fO"/>
                                </constraints>
                                <color key="tintColor" name="PrimaryWhite"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="Take ID Photo">
                                    <fontDescription key="titleFontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                </buttonConfiguration>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="image" keyPath="leftHandImage" value="iconCameraWhite"/>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                        <integer key="value" value="3"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="didTapTakeAPhoto" destination="CkD-at-bPQ" eventType="touchUpInside" id="zcY-4m-nhc"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Hdw-lu-sfP"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <gestureRecognizers/>
                        <constraints>
                            <constraint firstItem="HPL-h9-uve" firstAttribute="leading" secondItem="Hdw-lu-sfP" secondAttribute="leading" constant="20" id="28T-61-nie"/>
                            <constraint firstItem="4FG-da-hfG" firstAttribute="leading" secondItem="Hdw-lu-sfP" secondAttribute="leading" constant="20" id="9Hb-V1-kYL"/>
                            <constraint firstItem="r8h-lk-3JL" firstAttribute="centerX" secondItem="4FG-da-hfG" secondAttribute="centerX" id="FeF-70-UqH"/>
                            <constraint firstItem="3wq-Q0-9QT" firstAttribute="centerX" secondItem="qVy-zs-DZV" secondAttribute="centerX" id="Fuv-Oe-fuZ"/>
                            <constraint firstItem="Hdw-lu-sfP" firstAttribute="trailing" secondItem="r8h-lk-3JL" secondAttribute="trailing" constant="20" id="J4u-Da-jam"/>
                            <constraint firstItem="2Yn-qk-UVU" firstAttribute="leading" secondItem="Hdw-lu-sfP" secondAttribute="leading" id="M0C-CN-emO"/>
                            <constraint firstItem="Hdw-lu-sfP" firstAttribute="trailing" secondItem="bZB-iW-Ktg" secondAttribute="trailing" constant="36" id="Or9-L2-oIM"/>
                            <constraint firstAttribute="bottom" secondItem="TaI-wK-mDC" secondAttribute="bottom" id="TM1-kj-zvy"/>
                            <constraint firstItem="2Yn-qk-UVU" firstAttribute="top" secondItem="4tw-Y8-0Il" secondAttribute="bottom" constant="10" id="TvA-1M-czZ"/>
                            <constraint firstItem="4FG-da-hfG" firstAttribute="top" secondItem="Hdw-lu-sfP" secondAttribute="top" constant="10" id="WGl-rK-2dW"/>
                            <constraint firstItem="4FG-da-hfG" firstAttribute="centerY" secondItem="4tw-Y8-0Il" secondAttribute="centerY" id="aYB-8S-gPr"/>
                            <constraint firstItem="bZB-iW-Ktg" firstAttribute="top" secondItem="4CN-eS-EkK" secondAttribute="top" constant="18" id="csq-tF-Za6"/>
                            <constraint firstItem="bZB-iW-Ktg" firstAttribute="top" secondItem="q9z-Ob-jIA" secondAttribute="top" id="dUw-xv-HJl"/>
                            <constraint firstItem="r8h-lk-3JL" firstAttribute="centerY" secondItem="4FG-da-hfG" secondAttribute="centerY" id="din-hh-Rf5"/>
                            <constraint firstItem="Hdw-lu-sfP" firstAttribute="bottom" secondItem="HPL-h9-uve" secondAttribute="bottom" constant="20" id="dsy-XL-vcG"/>
                            <constraint firstItem="bZB-iW-Ktg" firstAttribute="bottom" secondItem="4CN-eS-EkK" secondAttribute="bottom" constant="-16" id="ege-uq-2pE"/>
                            <constraint firstItem="4tw-Y8-0Il" firstAttribute="centerX" secondItem="Hdw-lu-sfP" secondAttribute="centerX" id="fPB-G2-pvK"/>
                            <constraint firstItem="HPL-h9-uve" firstAttribute="top" secondItem="bZB-iW-Ktg" secondAttribute="bottom" constant="36" id="fWZ-dU-rwu"/>
                            <constraint firstItem="Hdw-lu-sfP" firstAttribute="trailing" secondItem="bZB-iW-Ktg" secondAttribute="trailing" constant="36" id="ghm-LP-Vn8"/>
                            <constraint firstItem="bZB-iW-Ktg" firstAttribute="trailing" secondItem="4CN-eS-EkK" secondAttribute="trailing" constant="-16" id="hAJ-5g-q6n"/>
                            <constraint firstItem="Hdw-lu-sfP" firstAttribute="trailing" secondItem="2Yn-qk-UVU" secondAttribute="trailing" id="hMB-YZ-KHQ"/>
                            <constraint firstItem="3wq-Q0-9QT" firstAttribute="bottom" secondItem="HYf-P4-PJJ" secondAttribute="bottom" id="j2R-vF-Rg6"/>
                            <constraint firstItem="4CN-eS-EkK" firstAttribute="top" secondItem="2Yn-qk-UVU" secondAttribute="bottom" constant="10" id="jOa-85-J3k"/>
                            <constraint firstItem="4tw-Y8-0Il" firstAttribute="centerY" secondItem="4FG-da-hfG" secondAttribute="centerY" id="mWt-LV-Iui"/>
                            <constraint firstItem="q9z-Ob-jIA" firstAttribute="leading" secondItem="4CN-eS-EkK" secondAttribute="leading" constant="18" id="rBJ-da-a4v"/>
                            <constraint firstItem="TaI-wK-mDC" firstAttribute="top" secondItem="qVy-zs-DZV" secondAttribute="top" id="t3X-pq-tvn"/>
                            <constraint firstItem="TaI-wK-mDC" firstAttribute="leading" secondItem="Hdw-lu-sfP" secondAttribute="leading" id="uBQ-GP-RLX"/>
                            <constraint firstItem="q9z-Ob-jIA" firstAttribute="leading" secondItem="Hdw-lu-sfP" secondAttribute="leading" constant="38" id="wsR-a7-ueA"/>
                            <constraint firstItem="Hdw-lu-sfP" firstAttribute="trailing" secondItem="HPL-h9-uve" secondAttribute="trailing" constant="20" id="yo4-qc-nXw"/>
                            <constraint firstItem="bZB-iW-Ktg" firstAttribute="leading" secondItem="q9z-Ob-jIA" secondAttribute="trailing" constant="6" id="zMu-Nd-FOh"/>
                            <constraint firstItem="TaI-wK-mDC" firstAttribute="trailing" secondItem="Hdw-lu-sfP" secondAttribute="trailing" id="zWb-vj-Gl9"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backButton" destination="4FG-da-hfG" id="tso-bf-7CH"/>
                        <outlet property="cameraPreview" destination="TaI-wK-mDC" id="OOA-tx-rv3"/>
                        <outlet property="captureInstructionLabel" destination="wZO-Zo-fOX" id="oeu-jq-hJL"/>
                        <outlet property="captureProgressLabel" destination="r8h-lk-3JL" id="UZI-Ak-agf"/>
                        <outlet property="captureTitleLabel" destination="4tw-Y8-0Il" id="Mpt-8F-Gmh"/>
                        <outlet property="focusingLabel" destination="3wq-Q0-9QT" id="Hot-AL-jw2"/>
                        <outlet property="idFrameOverlay" destination="HYf-P4-PJJ" id="nGD-CP-KLC"/>
                        <outlet property="takePhotoButton" destination="HPL-h9-uve" id="uEt-Kl-ilA"/>
                        <outlet property="tapToFocusLabelBottomConstraint" destination="j2R-vF-Rg6" id="RpL-TJ-em2"/>
                        <outlet property="tipsLabel" destination="bZB-iW-Ktg" id="9mb-BU-d6a"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="XPf-gm-fKf" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1486.9565217391305" y="2589.9456521739135"/>
        </scene>
        <!--MayaEKYC Simple CaptureID Review View Controller-->
        <scene sceneID="4ew-Ad-Qqs">
            <objects>
                <viewController storyboardIdentifier="MayaEKYCSimpleCaptureIDReviewViewController" extendedLayoutIncludesOpaqueBars="YES" id="T8f-Nm-aGD" customClass="MayaEKYCSimpleCaptureIDReviewViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Zy3-zv-XNb">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" image="iconBadgeVerifiedInfo" translatesAutoresizingMaskIntoConstraints="NO" id="52g-b6-bx8">
                                <rect key="frame" x="24" y="87" width="21" height="28"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="21" id="a8e-WH-kbn"/>
                                    <constraint firstAttribute="height" constant="28" id="oid-UB-hqW"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="249" verticalHuggingPriority="251" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="751" text="All good?" textAlignment="natural" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3mz-6c-xiM">
                                <rect key="frame" x="53" y="87" width="316" height="28"/>
                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="23"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="751" text="Make sure all information on your ID can be read easily." textAlignment="natural" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YKy-pP-eay">
                                <rect key="frame" x="24" y="123.00000000000001" width="345" height="36.666666666666671"/>
                                <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                <color key="textColor" name="Grey5"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" translatesAutoresizingMaskIntoConstraints="NO" id="gJn-6P-676">
                                <rect key="frame" x="24" y="169.66666666666669" width="345" height="520.66666666666652"/>
                            </imageView>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillProportionally" translatesAutoresizingMaskIntoConstraints="NO" id="OZ2-U1-zQW">
                                <rect key="frame" x="24" y="700.**************" width="345" height="107.66666666666663"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="CTV-k2-t4K" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="345" height="56"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="qiB-GE-h89"/>
                                        </constraints>
                                        <color key="tintColor" name="PrimaryWhite"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="Use this photo">
                                            <fontDescription key="titleFontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                        </buttonConfiguration>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="didTapPrimaryButton" destination="T8f-Nm-aGD" eventType="touchUpInside" id="3Ob-4e-o2h"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6nc-c1-sxl" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="56" width="345" height="51.***************"/>
                                        <color key="tintColor" name="ButtonFocusedPrimaryGreen"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="Retake">
                                            <fontDescription key="titleFontDescription" name="Jeko-Regular" family="Jeko" pointSize="14"/>
                                        </buttonConfiguration>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                <integer key="value" value="4"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="didTapSecondaryButton" destination="T8f-Nm-aGD" eventType="touchUpInside" id="bBY-se-B5B"/>
                                        </connections>
                                    </button>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="b9q-cn-Vod"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="b9q-cn-Vod" firstAttribute="trailing" secondItem="OZ2-U1-zQW" secondAttribute="trailing" constant="24" id="1SM-gY-nDi"/>
                            <constraint firstItem="3mz-6c-xiM" firstAttribute="leading" secondItem="52g-b6-bx8" secondAttribute="trailing" constant="8" id="32d-TQ-jss"/>
                            <constraint firstItem="OZ2-U1-zQW" firstAttribute="leading" secondItem="b9q-cn-Vod" secondAttribute="leading" constant="24" id="4Cf-eD-9Le"/>
                            <constraint firstItem="52g-b6-bx8" firstAttribute="leading" secondItem="Zy3-zv-XNb" secondAttribute="leading" constant="24" id="4cg-9c-RbK"/>
                            <constraint firstItem="YKy-pP-eay" firstAttribute="leading" secondItem="b9q-cn-Vod" secondAttribute="leading" constant="24" id="DWf-Z3-A5u"/>
                            <constraint firstItem="OZ2-U1-zQW" firstAttribute="top" secondItem="gJn-6P-676" secondAttribute="bottom" constant="10" id="NfI-Dv-f1i"/>
                            <constraint firstItem="YKy-pP-eay" firstAttribute="top" secondItem="3mz-6c-xiM" secondAttribute="bottom" constant="8" id="Npo-GY-Y2O"/>
                            <constraint firstItem="b9q-cn-Vod" firstAttribute="bottom" secondItem="OZ2-U1-zQW" secondAttribute="bottom" constant="10" id="OFc-hd-qCo"/>
                            <constraint firstItem="b9q-cn-Vod" firstAttribute="trailing" secondItem="YKy-pP-eay" secondAttribute="trailing" constant="24" id="OWg-RN-K5S"/>
                            <constraint firstItem="b9q-cn-Vod" firstAttribute="trailing" secondItem="3mz-6c-xiM" secondAttribute="trailing" constant="24" id="Yfi-gN-8es"/>
                            <constraint firstItem="3mz-6c-xiM" firstAttribute="top" secondItem="b9q-cn-Vod" secondAttribute="top" constant="28" id="ceW-hM-p36"/>
                            <constraint firstItem="52g-b6-bx8" firstAttribute="top" secondItem="b9q-cn-Vod" secondAttribute="top" constant="28" id="ery-Cx-qa5"/>
                            <constraint firstItem="gJn-6P-676" firstAttribute="top" secondItem="YKy-pP-eay" secondAttribute="bottom" constant="10" id="pTN-C1-8dZ"/>
                            <constraint firstItem="b9q-cn-Vod" firstAttribute="trailing" secondItem="gJn-6P-676" secondAttribute="trailing" constant="24" id="t05-jM-WZX"/>
                            <constraint firstItem="gJn-6P-676" firstAttribute="leading" secondItem="b9q-cn-Vod" secondAttribute="leading" constant="24" id="z87-3o-OG4"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="capturedImageView" destination="gJn-6P-676" id="r3s-Mf-7SX"/>
                        <outlet property="primaryButton" destination="CTV-k2-t4K" id="C47-VR-KOQ"/>
                        <outlet property="secondaryButton" destination="6nc-c1-sxl" id="0dI-38-pZt"/>
                        <outlet property="statusImageView" destination="52g-b6-bx8" id="Wh7-oj-pkE"/>
                        <outlet property="subTitleLabel" destination="YKy-pP-eay" id="CX9-Ov-1WE"/>
                        <outlet property="titleLabel" destination="3mz-6c-xiM" id="4RL-Mn-z86"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ZSw-rM-09A" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-2152.5" y="1901.4084507042253"/>
        </scene>
        <!--Maya Liveness Tips View Controller-->
        <scene sceneID="bDp-cj-5Hk">
            <objects>
                <viewController storyboardIdentifier="MayaLivenessTipsViewController" id="Gzy-e4-4kc" customClass="MayaLivenessTipsViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="fM6-8C-6FS">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4K9-uI-Dgo">
                                <rect key="frame" x="0.0" y="103" width="393" height="643"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sLi-x1-w8y">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="259"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Title" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lAc-nj-NgR">
                                                <rect key="frame" x="24" y="30.000000000000004" width="345" height="58.333333333333343"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="48"/>
                                                <color key="textColor" name="PrimaryBlack"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Description" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="P71-X2-dIn">
                                                <rect key="frame" x="24" y="88.333333333333343" width="345" height="17"/>
                                                <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                <color key="textColor" name="Grey5"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="mRF-Cc-Xjd">
                                                <rect key="frame" x="24" y="135.33333333333334" width="345" height="19.***************"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="info" translatesAutoresizingMaskIntoConstraints="NO" id="dvH-gY-rul">
                                                        <rect key="frame" x="0.0" y="2" width="16" height="16"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="16" id="4Ef-9d-mqb"/>
                                                            <constraint firstAttribute="width" constant="16" id="rar-JF-aVz"/>
                                                        </constraints>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Information message" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Vg3-Vb-iIy">
                                                        <rect key="frame" x="24" y="0.0" width="321" height="19.**************8"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="jW4-zI-GEK">
                                                <rect key="frame" x="24" y="171" width="345" height="80"/>
                                                <color key="backgroundColor" name="Grey1"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" priority="250" constant="80" id="MZq-LC-Xia"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                        <integer key="value" value="16"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="lAc-nj-NgR" firstAttribute="top" secondItem="sLi-x1-w8y" secondAttribute="top" constant="30" id="FPe-3O-jzi"/>
                                            <constraint firstAttribute="trailing" secondItem="lAc-nj-NgR" secondAttribute="trailing" constant="24" id="HCQ-bo-Xcu"/>
                                            <constraint firstItem="mRF-Cc-Xjd" firstAttribute="leading" secondItem="sLi-x1-w8y" secondAttribute="leading" constant="24" id="LcR-Bg-45q"/>
                                            <constraint firstItem="mRF-Cc-Xjd" firstAttribute="top" secondItem="P71-X2-dIn" secondAttribute="bottom" constant="30" id="MhD-0M-Uix"/>
                                            <constraint firstItem="jW4-zI-GEK" firstAttribute="leading" secondItem="sLi-x1-w8y" secondAttribute="leading" constant="24" id="PAK-wu-0wz"/>
                                            <constraint firstItem="P71-X2-dIn" firstAttribute="leading" secondItem="sLi-x1-w8y" secondAttribute="leading" constant="24" id="Quc-S1-dsj"/>
                                            <constraint firstItem="jW4-zI-GEK" firstAttribute="top" secondItem="mRF-Cc-Xjd" secondAttribute="bottom" constant="16" id="S4M-xp-nxA"/>
                                            <constraint firstAttribute="bottom" secondItem="jW4-zI-GEK" secondAttribute="bottom" constant="8" id="cMi-t1-G9O"/>
                                            <constraint firstAttribute="trailing" secondItem="jW4-zI-GEK" secondAttribute="trailing" constant="24" id="gBn-5N-CHi"/>
                                            <constraint firstAttribute="trailing" secondItem="mRF-Cc-Xjd" secondAttribute="trailing" constant="24" id="i1I-bw-ZtF"/>
                                            <constraint firstAttribute="trailing" secondItem="P71-X2-dIn" secondAttribute="trailing" constant="24" id="isB-Ix-spq"/>
                                            <constraint firstItem="P71-X2-dIn" firstAttribute="top" secondItem="lAc-nj-NgR" secondAttribute="bottom" id="jea-BP-zJA"/>
                                            <constraint firstItem="lAc-nj-NgR" firstAttribute="leading" secondItem="sLi-x1-w8y" secondAttribute="leading" constant="24" id="pfM-bG-tqb"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="sLi-x1-w8y" firstAttribute="top" secondItem="v4B-GW-JzT" secondAttribute="top" id="3Ha-Vs-gRC"/>
                                    <constraint firstItem="sLi-x1-w8y" firstAttribute="bottom" secondItem="v4B-GW-JzT" secondAttribute="bottom" id="7ZX-dH-WWf"/>
                                    <constraint firstItem="sLi-x1-w8y" firstAttribute="leading" secondItem="v4B-GW-JzT" secondAttribute="leading" id="V5k-TF-SZG"/>
                                    <constraint firstItem="sLi-x1-w8y" firstAttribute="width" secondItem="ANG-51-oeE" secondAttribute="width" id="fYa-Cs-xlR"/>
                                    <constraint firstItem="sLi-x1-w8y" firstAttribute="trailing" secondItem="v4B-GW-JzT" secondAttribute="trailing" id="mtU-HW-Scp"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="v4B-GW-JzT"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="ANG-51-oeE"/>
                            </scrollView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="lpx-3n-Kna" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="24" y="754" width="345" height="56"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="56" id="UOr-Cq-nIq"/>
                                </constraints>
                                <color key="tintColor" name="PrimaryWhite"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="Continue">
                                    <fontDescription key="titleFontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                </buttonConfiguration>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                        <integer key="value" value="0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="didTapContinue:" destination="Gzy-e4-4kc" eventType="touchUpInside" id="FYc-Qk-vpC"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="sS0-PQ-67C"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="sS0-PQ-67C" firstAttribute="trailing" secondItem="lpx-3n-Kna" secondAttribute="trailing" constant="24" id="0l5-KY-PU1"/>
                            <constraint firstItem="sS0-PQ-67C" firstAttribute="trailing" secondItem="4K9-uI-Dgo" secondAttribute="trailing" id="1be-TY-g6k"/>
                            <constraint firstItem="lpx-3n-Kna" firstAttribute="leading" secondItem="sS0-PQ-67C" secondAttribute="leading" constant="24" id="8Pg-8L-y9J"/>
                            <constraint firstItem="4K9-uI-Dgo" firstAttribute="top" secondItem="sS0-PQ-67C" secondAttribute="top" id="RkE-1n-sRA"/>
                            <constraint firstItem="lpx-3n-Kna" firstAttribute="top" secondItem="4K9-uI-Dgo" secondAttribute="bottom" constant="8" id="SUd-M9-IV0"/>
                            <constraint firstItem="sS0-PQ-67C" firstAttribute="bottom" secondItem="lpx-3n-Kna" secondAttribute="bottom" constant="8" id="go3-AS-NPZ"/>
                            <constraint firstItem="4K9-uI-Dgo" firstAttribute="leading" secondItem="sS0-PQ-67C" secondAttribute="leading" id="i6A-LT-87S"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="but-JO-9Ik">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="2sE-5I-UpU">
                            <connections>
                                <action selector="didTapBack:" destination="Gzy-e4-4kc" id="Wo4-UO-HHx"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <connections>
                        <outlet property="continueButton" destination="lpx-3n-Kna" id="QRk-36-oO9"/>
                        <outlet property="descriptionLabel" destination="P71-X2-dIn" id="PEs-de-9lI"/>
                        <outlet property="infoLabel" destination="Vg3-Vb-iIy" id="POR-07-8Us"/>
                        <outlet property="tipsStackView" destination="jW4-zI-GEK" id="NwK-rF-dGm"/>
                        <outlet property="titleLabel" destination="lAc-nj-NgR" id="CAp-rT-ONI"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="cf0-OK-Cb0" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1751" y="98"/>
        </scene>
        <!--MayaEKYC SecondaryID View Controller-->
        <scene sceneID="i3Z-GQ-seA">
            <objects>
                <viewController storyboardIdentifier="MayaEKYCSecondaryIDViewController" id="4TE-Jp-ahz" customClass="MayaEKYCSecondaryIDViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="2n9-Cq-POd">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4hG-BS-Dno">
                                <rect key="frame" x="0.0" y="133" width="393" height="613"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="esc-xL-eit">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="228.33333333333334"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="18" translatesAutoresizingMaskIntoConstraints="NO" id="tfA-vD-gUJ">
                                                <rect key="frame" x="24" y="0.0" width="345" height="228.33333333333334"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="1000" text="Select one more ID" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BxO-BU-Wnk" customClass="Label" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="345" height="39"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="32"/>
                                                        <color key="textColor" name="PrimaryBlack"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizedKey" value="maya.ekyc.selectAnotherID.title"/>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizedDefaultValue" value="Select one more ID"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="1000" text="Your ID helps us confirm your identity and secure your account. Please submit a valid ID from the list below:" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eHc-2K-amI" customClass="Label" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="57" width="345" height="51"/>
                                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                        <color key="textColor" name="Grey5"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizedKey" value="maya.ekyc.selectAnotherID.subtitle"/>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizedDefaultValue" value="Your ID helps us confirm your identity and secure your account. Please submit a valid ID from the list below:"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" alignment="center" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="CFI-z2-w6V">
                                                        <rect key="frame" x="0.0" y="125.**************" width="345" height="24.***************"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="1000" text="Secondary IDs" textAlignment="natural" lineBreakMode="wordWrap" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kFT-Fg-Jxc" userLabel="Secondary Ids" customClass="Label" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="345" height="24.333333333333332"/>
                                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="20"/>
                                                                <color key="textColor" name="PrimaryBlack"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizedKey" value="maya.ekyc.selectAnotherID.pickerLabel"/>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizedDefaultValue" value="Secondary IDs"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xUE-Rd-l8z" customClass="MayaZolozIdSubmitPickerView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="168.**************" width="345" height="60"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" priority="250" constant="60" id="74z-OK-I2r"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="tfA-vD-gUJ" secondAttribute="trailing" constant="24" id="2FI-V8-zFI"/>
                                            <constraint firstAttribute="bottom" secondItem="tfA-vD-gUJ" secondAttribute="bottom" id="REG-L3-Egd"/>
                                            <constraint firstItem="tfA-vD-gUJ" firstAttribute="leading" secondItem="esc-xL-eit" secondAttribute="leading" constant="24" id="aPb-7D-4eA"/>
                                            <constraint firstItem="tfA-vD-gUJ" firstAttribute="top" secondItem="esc-xL-eit" secondAttribute="top" id="m3s-DE-c5A"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="esc-xL-eit" firstAttribute="trailing" secondItem="PfK-4C-748" secondAttribute="trailing" id="AQ6-BY-nnc"/>
                                    <constraint firstItem="esc-xL-eit" firstAttribute="leading" secondItem="PfK-4C-748" secondAttribute="leading" id="P8I-ka-YhD"/>
                                    <constraint firstItem="esc-xL-eit" firstAttribute="bottom" secondItem="PfK-4C-748" secondAttribute="bottom" id="RPu-D2-dbQ"/>
                                    <constraint firstItem="esc-xL-eit" firstAttribute="width" secondItem="VXb-RK-kys" secondAttribute="width" id="hM0-xF-asJ"/>
                                    <constraint firstItem="esc-xL-eit" firstAttribute="top" secondItem="PfK-4C-748" secondAttribute="top" id="reY-h1-w1N"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="PfK-4C-748"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="VXb-RK-kys"/>
                            </scrollView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="fyd-Z9-TOb" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="24" y="754" width="345" height="56"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="56" id="YA9-N5-T9g"/>
                                </constraints>
                                <color key="tintColor" name="PrimaryWhite"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="Continue">
                                    <fontDescription key="titleFontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                </buttonConfiguration>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                        <integer key="value" value="0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="didTapContinueButton:" destination="4TE-Jp-ahz" eventType="touchUpInside" id="laX-pp-JyW"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="D4B-7M-fHB"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="4hG-BS-Dno" firstAttribute="leading" secondItem="D4B-7M-fHB" secondAttribute="leading" id="07D-sO-0Lu"/>
                            <constraint firstItem="fyd-Z9-TOb" firstAttribute="top" secondItem="4hG-BS-Dno" secondAttribute="bottom" constant="8" id="5Dg-XR-VW5"/>
                            <constraint firstItem="4hG-BS-Dno" firstAttribute="top" secondItem="D4B-7M-fHB" secondAttribute="top" constant="30" id="8hv-Rg-qsR"/>
                            <constraint firstItem="fyd-Z9-TOb" firstAttribute="leading" secondItem="D4B-7M-fHB" secondAttribute="leading" constant="24" id="KmU-qv-cIr"/>
                            <constraint firstItem="D4B-7M-fHB" firstAttribute="trailing" secondItem="fyd-Z9-TOb" secondAttribute="trailing" constant="24" id="QlU-2d-Qzd"/>
                            <constraint firstItem="D4B-7M-fHB" firstAttribute="trailing" secondItem="4hG-BS-Dno" secondAttribute="trailing" id="edb-5w-go4"/>
                            <constraint firstItem="D4B-7M-fHB" firstAttribute="bottom" secondItem="fyd-Z9-TOb" secondAttribute="bottom" constant="8" id="fNe-wo-mRS"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="t28-Ss-TG5">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="2Jy-Qk-jbS">
                            <connections>
                                <action selector="didTapBack:" destination="4TE-Jp-ahz" id="LUM-2w-6KO"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <connections>
                        <outlet property="continueButton" destination="fyd-Z9-TOb" id="vu9-Vj-KAt"/>
                        <outlet property="pickerViewLabel" destination="kFT-Fg-Jxc" id="PnF-EM-QFR"/>
                        <outlet property="secondaryIDPickerView" destination="xUE-Rd-l8z" id="aCu-1E-ioP"/>
                        <outlet property="subTitleLabel" destination="eHc-2K-amI" id="aEU-NS-FTE"/>
                        <outlet property="titleLabel" destination="BxO-BU-Wnk" id="7bt-mp-tCy"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="RFa-mK-Gk6" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1099" y="773"/>
        </scene>
        <!--MayaEKYC Maintenance View Controller-->
        <scene sceneID="I5b-TW-srn">
            <objects>
                <viewController storyboardIdentifier="MayaEKYCMaintenanceViewController" id="svE-cC-sEH" customClass="MayaEKYCMaintenanceViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="qn4-yG-RgK">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wOo-aw-NEU">
                                <rect key="frame" x="0.0" y="103" width="393" height="715"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Q2R-MA-Ba2">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="425"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="vor-lB-xxa">
                                                <rect key="frame" x="57" y="32" width="279" height="279"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="279" id="Eil-g7-HoE"/>
                                                    <constraint firstAttribute="width" constant="279" id="hRB-ha-Izt"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Please try again later" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3nO-55-DmA">
                                                <rect key="frame" x="69.666666666666671" y="319" width="253.66666666666663" height="29.**************4"/>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="This feature is currently under maintenance and will be back soon" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XFa-c1-YPE">
                                                <rect key="frame" x="48" y="356.**************" width="297" height="36.666666666666686"/>
                                                <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                <color key="textColor" red="0.33333333329999998" green="0.33725490200000002" blue="0.34901960780000002" alpha="1" colorSpace="calibratedRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="vor-lB-xxa" firstAttribute="top" secondItem="Q2R-MA-Ba2" secondAttribute="top" constant="32" id="3L8-UV-SiS"/>
                                            <constraint firstItem="XFa-c1-YPE" firstAttribute="leading" secondItem="Q2R-MA-Ba2" secondAttribute="leading" constant="48" id="7NH-6O-GKr"/>
                                            <constraint firstAttribute="trailing" secondItem="XFa-c1-YPE" secondAttribute="trailing" constant="48" id="Agp-4Y-n1H"/>
                                            <constraint firstItem="XFa-c1-YPE" firstAttribute="top" secondItem="3nO-55-DmA" secondAttribute="bottom" constant="8" id="EAh-d6-2I4"/>
                                            <constraint firstItem="vor-lB-xxa" firstAttribute="centerX" secondItem="Q2R-MA-Ba2" secondAttribute="centerX" id="MwV-cM-mlT"/>
                                            <constraint firstAttribute="bottom" secondItem="XFa-c1-YPE" secondAttribute="bottom" constant="32" id="VOT-6I-Dj2"/>
                                            <constraint firstItem="XFa-c1-YPE" firstAttribute="centerX" secondItem="Q2R-MA-Ba2" secondAttribute="centerX" id="Vid-yh-cR9"/>
                                            <constraint firstItem="3nO-55-DmA" firstAttribute="centerX" secondItem="Q2R-MA-Ba2" secondAttribute="centerX" id="aNk-CV-omJ"/>
                                            <constraint firstItem="3nO-55-DmA" firstAttribute="top" secondItem="vor-lB-xxa" secondAttribute="bottom" constant="8" id="pOD-Re-YVI"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="Q2R-MA-Ba2" firstAttribute="bottom" secondItem="Qiq-D1-QZs" secondAttribute="bottom" id="XFC-of-3Kz"/>
                                    <constraint firstItem="Q2R-MA-Ba2" firstAttribute="width" secondItem="VXb-qL-jRj" secondAttribute="width" id="gDR-uJ-Q5g"/>
                                    <constraint firstItem="Q2R-MA-Ba2" firstAttribute="top" secondItem="Qiq-D1-QZs" secondAttribute="top" id="kHi-C5-9Cn"/>
                                    <constraint firstItem="Q2R-MA-Ba2" firstAttribute="leading" secondItem="Qiq-D1-QZs" secondAttribute="leading" id="lru-Z5-uJd"/>
                                    <constraint firstItem="Q2R-MA-Ba2" firstAttribute="trailing" secondItem="Qiq-D1-QZs" secondAttribute="trailing" id="qoQ-mq-j9e"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="Qiq-D1-QZs"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="VXb-qL-jRj"/>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="oh7-D9-O10"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="oh7-D9-O10" firstAttribute="trailing" secondItem="wOo-aw-NEU" secondAttribute="trailing" id="QsS-Xo-hJy"/>
                            <constraint firstItem="oh7-D9-O10" firstAttribute="bottom" secondItem="wOo-aw-NEU" secondAttribute="bottom" id="YoR-OB-KR8"/>
                            <constraint firstItem="wOo-aw-NEU" firstAttribute="leading" secondItem="oh7-D9-O10" secondAttribute="leading" id="nAk-lH-Tt4"/>
                            <constraint firstItem="wOo-aw-NEU" firstAttribute="top" secondItem="oh7-D9-O10" secondAttribute="top" id="zIH-Pi-eWI"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="u3o-jK-D8r">
                        <barButtonItem key="leftBarButtonItem" image="iconSystemCross" id="3B4-GP-DEp">
                            <connections>
                                <action selector="didTapBack:" destination="svE-cC-sEH" id="wci-iX-2ax"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <connections>
                        <outlet property="headerLabel" destination="3nO-55-DmA" id="7uI-ze-NjW"/>
                        <outlet property="maintenanceImageView" destination="vor-lB-xxa" id="WmG-6r-Ovw"/>
                        <outlet property="subheaderLabel" destination="XFa-c1-YPE" id="89W-da-cYH"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="hxD-hu-K4Y" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-3677.0992366412211" y="1910.5633802816903"/>
        </scene>
        <!--MayaEKYC Simple CaptureIDV3 View Controller-->
        <scene sceneID="Mki-Ar-A6O">
            <objects>
                <viewController storyboardIdentifier="MayaEKYCSimpleCaptureIDV3ViewController" extendedLayoutIncludesOpaqueBars="YES" id="jXt-Gv-XeR" customClass="MayaEKYCSimpleCaptureIDV3ViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="eVE-Ab-ajo">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view userInteractionEnabled="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="57R-fv-wxu" userLabel="CameraPreview" customClass="RecordPreviewView" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <view alpha="0.80000000000000004" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vBV-92-U51" userLabel="UpperDimView">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="194"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <view alpha="0.80000000000000004" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bDs-Iy-Re8" userLabel="LowerDimView">
                                <rect key="frame" x="0.0" y="604.66666666666663" width="393" height="247.**************"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="UVH-uF-wex">
                                <rect key="frame" x="20" y="69" width="22" height="22"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_id_scanner_back_button"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="22" id="jpO-Xy-xhB"/>
                                    <constraint firstAttribute="width" constant="22" id="r79-aj-POz"/>
                                </constraints>
                                <state key="normal" image="iconBackWhite">
                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="didTapBack:" destination="jXt-Gv-XeR" eventType="touchUpInside" id="xwr-M6-nOI"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Take an ID photo" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cVB-hV-MwW">
                                <rect key="frame" x="130.66666666666669" y="70.***************" width="131.66666666666669" height="19.666666666666671"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_id_scanner_title_label"/>
                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="s3N-qQ-FXl">
                                <rect key="frame" x="24" y="120" width="345" height="50"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Scan the front of your" textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BBc-Mu-dwX">
                                        <rect key="frame" x="0.0" y="0.0" width="345" height="21"/>
                                        <accessibility key="accessibilityConfiguration" identifier="pmios_id_scanner_position_id_label"/>
                                        <fontDescription key="fontDescription" name="CerebriSansPro-Bold" family="Cerebri Sans Pro" pointSize="16"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="driver's licence" textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bru-3e-Oyz">
                                        <rect key="frame" x="0.0" y="29" width="345" height="21"/>
                                        <accessibility key="accessibilityConfiguration" identifier="pmios_id_scanner_position_id_label"/>
                                        <fontDescription key="fontDescription" name="CerebriSansPro-Bold" family="Cerebri Sans Pro" pointSize="16"/>
                                        <color key="textColor" name="BackgroundPrimaryGreen"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                            </stackView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zO4-tC-lHg" userLabel="Capture Frame View">
                                <rect key="frame" x="0.0" y="194.00000000000003" width="393" height="410.66666666666674"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5nu-D0-kZ3" userLabel="tipsBGView">
                                <rect key="frame" x="24" y="628.66666666666663" width="345" height="73.**************1"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillProportionally" alignment="top" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="6fG-rH-zBZ">
                                        <rect key="frame" x="16" y="12.000000000000004" width="313" height="49.333333333333343"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Put tips here..." lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="naz-oK-IR8">
                                                <rect key="frame" x="0.0" y="0.0" width="111.66666666666667" height="21"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_id_scanner_tips_label"/>
                                                <fontDescription key="fontDescription" name="CerebriSansPro-Bold" family="Cerebri Sans Pro" pointSize="16"/>
                                                <color key="textColor" name="Grey1"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Put tips here..." lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Efj-df-wbV">
                                                <rect key="frame" x="0.0" y="31" width="95" height="18.***************"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_id_scanner_tips_label"/>
                                                <fontDescription key="fontDescription" name="CerebriSansPro-Medium" family="Cerebri Sans Pro" pointSize="14"/>
                                                <color key="textColor" white="1" alpha="0.79620240066225167" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_id_scanner_tips_view">
                                    <bool key="isElement" value="YES"/>
                                </accessibility>
                                <constraints>
                                    <constraint firstItem="6fG-rH-zBZ" firstAttribute="top" secondItem="5nu-D0-kZ3" secondAttribute="top" constant="12" id="CR1-C5-Smc"/>
                                    <constraint firstAttribute="bottom" secondItem="6fG-rH-zBZ" secondAttribute="bottom" constant="12" id="CVi-1M-IlD"/>
                                    <constraint firstItem="6fG-rH-zBZ" firstAttribute="leading" secondItem="5nu-D0-kZ3" secondAttribute="leading" constant="16" id="FqU-0u-RjX"/>
                                    <constraint firstAttribute="trailing" secondItem="6fG-rH-zBZ" secondAttribute="trailing" constant="16" id="spb-J8-CkW"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Flq-2U-pgm" customClass="Button" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="149.**************" y="742" width="94" height="70"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="70" id="GB3-JC-YDs"/>
                                </constraints>
                                <color key="tintColor" name="PrimaryWhite"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" image="CaptureId"/>
                                <connections>
                                    <action selector="didTapTakeAPhoto" destination="jXt-Gv-XeR" eventType="touchUpInside" id="Jbi-Tj-xfd"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="SLg-pr-ZRq"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <gestureRecognizers/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="bDs-Iy-Re8" secondAttribute="bottom" id="2yv-cw-gTo"/>
                            <constraint firstItem="5nu-D0-kZ3" firstAttribute="leading" secondItem="SLg-pr-ZRq" secondAttribute="leading" constant="24" id="7A3-Sb-rKJ"/>
                            <constraint firstItem="5nu-D0-kZ3" firstAttribute="top" secondItem="zO4-tC-lHg" secondAttribute="bottom" constant="24" id="CWd-s4-RmG"/>
                            <constraint firstItem="SLg-pr-ZRq" firstAttribute="trailing" secondItem="bDs-Iy-Re8" secondAttribute="trailing" id="DQ0-U6-6FS"/>
                            <constraint firstItem="57R-fv-wxu" firstAttribute="trailing" secondItem="SLg-pr-ZRq" secondAttribute="trailing" id="Dvf-af-Yre"/>
                            <constraint firstItem="cVB-hV-MwW" firstAttribute="centerY" secondItem="UVH-uF-wex" secondAttribute="centerY" id="HO2-Lm-qEn"/>
                            <constraint firstItem="s3N-qQ-FXl" firstAttribute="top" secondItem="cVB-hV-MwW" secondAttribute="bottom" constant="30" id="Qj0-qE-4lB"/>
                            <constraint firstItem="57R-fv-wxu" firstAttribute="leading" secondItem="SLg-pr-ZRq" secondAttribute="leading" id="SYv-iH-yGG"/>
                            <constraint firstItem="Flq-2U-pgm" firstAttribute="centerX" secondItem="SLg-pr-ZRq" secondAttribute="centerX" id="T0M-RH-WUx"/>
                            <constraint firstItem="UVH-uF-wex" firstAttribute="centerY" secondItem="cVB-hV-MwW" secondAttribute="centerY" id="TBv-OY-f3w"/>
                            <constraint firstItem="vBV-92-U51" firstAttribute="top" secondItem="eVE-Ab-ajo" secondAttribute="top" id="Tyo-ED-85x"/>
                            <constraint firstItem="UVH-uF-wex" firstAttribute="top" secondItem="SLg-pr-ZRq" secondAttribute="top" constant="10" id="Utk-3z-YNI"/>
                            <constraint firstItem="zO4-tC-lHg" firstAttribute="leading" secondItem="SLg-pr-ZRq" secondAttribute="leading" id="WYD-et-xEk"/>
                            <constraint firstItem="SLg-pr-ZRq" firstAttribute="trailing" secondItem="zO4-tC-lHg" secondAttribute="trailing" id="XwL-24-C8G"/>
                            <constraint firstItem="SLg-pr-ZRq" firstAttribute="trailing" secondItem="vBV-92-U51" secondAttribute="trailing" id="Xyk-v6-qc6"/>
                            <constraint firstItem="cVB-hV-MwW" firstAttribute="centerX" secondItem="SLg-pr-ZRq" secondAttribute="centerX" id="cJU-iD-gR4"/>
                            <constraint firstItem="SLg-pr-ZRq" firstAttribute="trailing" secondItem="5nu-D0-kZ3" secondAttribute="trailing" constant="24" id="dIL-en-hom"/>
                            <constraint firstItem="zO4-tC-lHg" firstAttribute="top" secondItem="s3N-qQ-FXl" secondAttribute="bottom" constant="24" id="dPc-SM-KPQ"/>
                            <constraint firstItem="bDs-Iy-Re8" firstAttribute="leading" secondItem="SLg-pr-ZRq" secondAttribute="leading" id="g6r-1C-rT4"/>
                            <constraint firstAttribute="bottom" secondItem="57R-fv-wxu" secondAttribute="bottom" id="g7L-5G-zp7"/>
                            <constraint firstItem="UVH-uF-wex" firstAttribute="leading" secondItem="SLg-pr-ZRq" secondAttribute="leading" constant="20" id="iIB-rb-au1"/>
                            <constraint firstItem="s3N-qQ-FXl" firstAttribute="leading" secondItem="SLg-pr-ZRq" secondAttribute="leading" constant="24" id="ivH-jI-EDQ"/>
                            <constraint firstItem="SLg-pr-ZRq" firstAttribute="trailing" secondItem="s3N-qQ-FXl" secondAttribute="trailing" constant="24" id="lFF-id-3xS"/>
                            <constraint firstAttribute="bottom" secondItem="Flq-2U-pgm" secondAttribute="bottom" constant="40" id="lds-tO-YGU"/>
                            <constraint firstItem="vBV-92-U51" firstAttribute="leading" secondItem="SLg-pr-ZRq" secondAttribute="leading" id="maI-iW-bCp"/>
                            <constraint firstItem="vBV-92-U51" firstAttribute="bottom" secondItem="zO4-tC-lHg" secondAttribute="top" id="p0L-jM-6zf"/>
                            <constraint firstItem="57R-fv-wxu" firstAttribute="top" secondItem="eVE-Ab-ajo" secondAttribute="top" id="v4M-Ks-RG6"/>
                            <constraint firstItem="bDs-Iy-Re8" firstAttribute="top" secondItem="zO4-tC-lHg" secondAttribute="bottom" id="vHh-nI-kbL"/>
                            <constraint firstItem="s3N-qQ-FXl" firstAttribute="centerX" secondItem="eVE-Ab-ajo" secondAttribute="centerX" id="vdU-do-72f"/>
                            <constraint firstItem="Flq-2U-pgm" firstAttribute="top" secondItem="5nu-D0-kZ3" secondAttribute="bottom" constant="40" id="z1w-ow-dH7"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backButton" destination="UVH-uF-wex" id="HUx-Dz-8p3"/>
                        <outlet property="cameraPreview" destination="57R-fv-wxu" id="XFr-uu-dsB"/>
                        <outlet property="captureFrameView" destination="zO4-tC-lHg" id="fWT-YQ-gyI"/>
                        <outlet property="captureIdLabel" destination="bru-3e-Oyz" id="MPk-7Z-UBS"/>
                        <outlet property="captureScanTypeLabel" destination="BBc-Mu-dwX" id="wTd-k2-QGH"/>
                        <outlet property="captureTitleLabel" destination="cVB-hV-MwW" id="SZN-ZE-8ie"/>
                        <outlet property="takePhotoButton" destination="Flq-2U-pgm" id="AwG-bg-qJC"/>
                        <outlet property="tipsBackgroundView" destination="5nu-D0-kZ3" id="WQy-SP-l4s"/>
                        <outlet property="tipsLabel" destination="Efj-df-wbV" id="YPF-WT-DFf"/>
                        <outlet property="tipsTitleLabel" destination="naz-oK-IR8" id="jMV-fc-3Nc"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="bJP-wL-aY0" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-2172" y="2589"/>
        </scene>
        <!--MayaEKYC PhilsysID Upload View Controller-->
        <scene sceneID="b12-1W-dzl">
            <objects>
                <viewController storyboardIdentifier="MayaEKYCPhilsysIDUploadViewController" id="pwH-DM-XWv" customClass="MayaEKYCPhilsysIDUploadViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="2P6-Ld-9uA">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="1000"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="uyD-bt-RWn">
                                <rect key="frame" x="0.0" y="133" width="393" height="823"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OTU-JO-XCS">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="654"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="HJs-qN-fdK">
                                                <rect key="frame" x="24" y="0.0" width="345" height="100"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="1000" text="Upload your Philippine National ID" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7ZH-up-lrr" customClass="Label" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="345" height="77.666666666666671"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="32"/>
                                                        <color key="textColor" name="PrimaryBlack"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="1000" text="Please upload the front and back of your ID" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Rng-eo-OI5" customClass="Label" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="81.***************" width="345" height="18.***************"/>
                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                        <color key="textColor" name="Grey6"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Front side" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XHB-WO-8r3">
                                                <rect key="frame" x="24" y="124" width="345" height="21"/>
                                                <fontDescription key="fontDescription" name="CerebriSansPro-Medium" family="Cerebri Sans Pro" pointSize="16"/>
                                                <color key="textColor" name="PrimaryBlack"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="6bz-aS-Qnf">
                                                <rect key="frame" x="24" y="157" width="345" height="164"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="a0x-CV-NLP" customClass="DashedLineBorderView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="345" height="164"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="H99-ph-U0A">
                                                                <rect key="frame" x="65.666666666666671" y="27.333333333333307" width="213.66666666666663" height="109.**************"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" horizontalCompressionResistancePriority="751" axis="vertical" alignment="center" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="xdL-cR-0Eo">
                                                                        <rect key="frame" x="0.0" y="0.0" width="213.**************" height="65.***************"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="📄" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="q6r-Xn-vby">
                                                                                <rect key="frame" x="95.***************" y="0.0" width="23" height="20.333333333333332"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                <nil key="textColor"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="Upload file" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7R5-pL-AHE">
                                                                                <rect key="frame" x="66.***************" y="24.**************1" width="80.***************" height="21"/>
                                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Medium" family="Cerebri Sans Pro" pointSize="16"/>
                                                                                <color key="textColor" name="PrimaryBlack"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="1000" text="JPEG, PNG, or HEIC up to 10 MB in size" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5Ej-rE-DjA">
                                                                                <rect key="frame" x="0.0" y="49.**************1" width="213.**************" height="16"/>
                                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="12"/>
                                                                                <color key="textColor" name="Grey6"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <button opaque="NO" contentMode="scaleToFill" verticalHuggingPriority="251" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="OyU-OO-1a6" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                                        <rect key="frame" x="46.999999999999986" y="77.**************1" width="120.00000000000001" height="32"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="32" id="Pdg-Ao-CZA"/>
                                                                            <constraint firstAttribute="width" constant="120" id="VEQ-sq-DQz"/>
                                                                        </constraints>
                                                                        <color key="tintColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="8" maxY="0.0"/>
                                                                        <state key="normal" title="Add a file" image="iconAddToContacts"/>
                                                                        <userDefinedRuntimeAttributes>
                                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                                <integer key="value" value="3"/>
                                                                            </userDefinedRuntimeAttribute>
                                                                        </userDefinedRuntimeAttributes>
                                                                        <connections>
                                                                            <action selector="addFrontPhotoPressed:" destination="pwH-DM-XWv" eventType="touchUpInside" id="6gJ-aA-KJI"/>
                                                                        </connections>
                                                                    </button>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="H99-ph-U0A" firstAttribute="centerY" secondItem="a0x-CV-NLP" secondAttribute="centerY" id="LE2-V3-xL8"/>
                                                            <constraint firstAttribute="height" constant="164" id="Mnp-zf-xdx"/>
                                                            <constraint firstItem="H99-ph-U0A" firstAttribute="centerX" secondItem="a0x-CV-NLP" secondAttribute="centerX" id="PkT-Z0-YnF"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="color" keyPath="color">
                                                                <color key="value" name="Grey3"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                <real key="value" value="16"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="paintedSegmentLength">
                                                                <integer key="value" value="12"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="unpaintedSegmentLength">
                                                                <integer key="value" value="3"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="sampleIdDocument" translatesAutoresizingMaskIntoConstraints="NO" id="TAP-AQ-3zr">
                                                        <rect key="frame" x="0.0" y="0.0" width="345" height="200"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="200" id="HD9-IY-ZIu"/>
                                                        </constraints>
                                                    </imageView>
                                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="arN-57-NQF" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="345" height="32"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="32" id="JUQ-Ky-jhU"/>
                                                        </constraints>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="3" maxY="0.0"/>
                                                        <state key="normal" title="Replace front photo"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                <integer key="value" value="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="image" keyPath="leftHandImage" value="iconCamera"/>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="replaceFrontButtonPressed:" destination="pwH-DM-XWv" eventType="touchUpInside" id="HMr-G2-Hxj"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="16"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="paintedSegmentLength">
                                                        <integer key="value" value="12"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="unpaintedSegmentLength">
                                                        <integer key="value" value="3"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="color">
                                                        <color key="value" name="Grey3"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </stackView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Back side" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="k8c-q4-JIc">
                                                <rect key="frame" x="24" y="345" width="345" height="21"/>
                                                <fontDescription key="fontDescription" name="CerebriSansPro-Medium" family="Cerebri Sans Pro" pointSize="16"/>
                                                <color key="textColor" name="PrimaryBlack"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="8qN-lf-n9Z">
                                                <rect key="frame" x="24" y="378" width="345" height="164"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lfK-mB-Yeh" customClass="DashedLineBorderView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="345" height="164"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="gXB-vl-zqR">
                                                                <rect key="frame" x="65.666666666666671" y="27.333333333333364" width="213.66666666666663" height="109.**************"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" horizontalCompressionResistancePriority="751" axis="vertical" alignment="center" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="WoF-yO-xBi">
                                                                        <rect key="frame" x="0.0" y="0.0" width="213.**************" height="65.***************"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="📄" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="R3Y-CG-gyg">
                                                                                <rect key="frame" x="95.***************" y="0.0" width="23" height="20.333333333333332"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                <nil key="textColor"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="Upload file" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nqN-cJ-P0L">
                                                                                <rect key="frame" x="66.***************" y="24.333333333333258" width="80.***************" height="21"/>
                                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Medium" family="Cerebri Sans Pro" pointSize="16"/>
                                                                                <color key="textColor" name="PrimaryBlack"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="1000" text="JPEG, PNG, or HEIC up to 10 MB in size" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ljx-ID-Q3x">
                                                                                <rect key="frame" x="0.0" y="49.333333333333258" width="213.**************" height="16"/>
                                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="12"/>
                                                                                <color key="textColor" name="Grey6"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <button opaque="NO" contentMode="scaleToFill" verticalHuggingPriority="251" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="eA9-hs-4eN" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                                        <rect key="frame" x="46.999999999999986" y="77.333333333333258" width="120.00000000000001" height="32"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="120" id="Iv8-vO-Oqt"/>
                                                                            <constraint firstAttribute="height" constant="32" id="QzV-F9-dRl"/>
                                                                        </constraints>
                                                                        <color key="tintColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="8" maxY="0.0"/>
                                                                        <state key="normal" title="Add a file" image="iconAddToContacts"/>
                                                                        <userDefinedRuntimeAttributes>
                                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                                <integer key="value" value="3"/>
                                                                            </userDefinedRuntimeAttribute>
                                                                        </userDefinedRuntimeAttributes>
                                                                        <connections>
                                                                            <action selector="addBackPhotoPressed:" destination="pwH-DM-XWv" eventType="touchUpInside" id="QgS-qc-qNy"/>
                                                                        </connections>
                                                                    </button>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="gXB-vl-zqR" firstAttribute="centerX" secondItem="lfK-mB-Yeh" secondAttribute="centerX" id="O0f-cx-rXo"/>
                                                            <constraint firstAttribute="height" constant="164" id="tex-YU-h16"/>
                                                            <constraint firstItem="gXB-vl-zqR" firstAttribute="centerY" secondItem="lfK-mB-Yeh" secondAttribute="centerY" id="yBg-WB-RZi"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="color" keyPath="color">
                                                                <color key="value" name="Grey3"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                <real key="value" value="16"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="paintedSegmentLength">
                                                                <integer key="value" value="12"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="unpaintedSegmentLength">
                                                                <integer key="value" value="3"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="imagePhilsysPhysicalBack" translatesAutoresizingMaskIntoConstraints="NO" id="CzI-94-7V4">
                                                        <rect key="frame" x="0.0" y="0.0" width="345" height="200"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="200" id="C24-0d-IsD"/>
                                                        </constraints>
                                                    </imageView>
                                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="CjC-tp-rri" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="345" height="32"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="32" id="RKF-By-8MT"/>
                                                        </constraints>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="3" maxY="0.0"/>
                                                        <state key="normal" title="Replace back photo"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                <integer key="value" value="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="image" keyPath="leftHandImage" value="iconCamera"/>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="replaceBackButtonPressed:" destination="pwH-DM-XWv" eventType="touchUpInside" id="jcD-7N-MND"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="16"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="paintedSegmentLength">
                                                        <integer key="value" value="12"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="unpaintedSegmentLength">
                                                        <integer key="value" value="3"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="color">
                                                        <color key="value" name="Grey3"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </stackView>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="HaZ-Yb-2mt" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="574" width="345" height="56"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="vHx-pJ-oC1"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="Continue"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                        <integer key="value" value="0"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="continueButtonPressed:" destination="pwH-DM-XWv" eventType="touchUpInside" id="J5o-Yb-tAm"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="k8c-q4-JIc" secondAttribute="trailing" constant="24" id="0Bj-4B-24m"/>
                                            <constraint firstItem="XHB-WO-8r3" firstAttribute="leading" secondItem="OTU-JO-XCS" secondAttribute="leading" constant="24" id="6Ox-EH-bqQ"/>
                                            <constraint firstItem="8qN-lf-n9Z" firstAttribute="leading" secondItem="OTU-JO-XCS" secondAttribute="leading" constant="24" id="9C3-u4-UgC"/>
                                            <constraint firstItem="6bz-aS-Qnf" firstAttribute="leading" secondItem="OTU-JO-XCS" secondAttribute="leading" constant="24" id="9LD-xM-sLU"/>
                                            <constraint firstItem="8qN-lf-n9Z" firstAttribute="top" secondItem="k8c-q4-JIc" secondAttribute="bottom" constant="12" id="Ecc-lT-Num"/>
                                            <constraint firstItem="k8c-q4-JIc" firstAttribute="top" secondItem="6bz-aS-Qnf" secondAttribute="bottom" constant="24" id="FTt-AP-11x"/>
                                            <constraint firstItem="6bz-aS-Qnf" firstAttribute="top" secondItem="XHB-WO-8r3" secondAttribute="bottom" constant="12" id="Fnh-vg-JdD"/>
                                            <constraint firstAttribute="trailing" secondItem="6bz-aS-Qnf" secondAttribute="trailing" constant="24" id="GiH-5Q-Q5t"/>
                                            <constraint firstAttribute="trailing" secondItem="HJs-qN-fdK" secondAttribute="trailing" constant="24" id="Ir3-c3-mrj"/>
                                            <constraint firstItem="HaZ-Yb-2mt" firstAttribute="leading" secondItem="OTU-JO-XCS" secondAttribute="leading" constant="24" id="PhB-t2-qkU"/>
                                            <constraint firstItem="k8c-q4-JIc" firstAttribute="leading" secondItem="OTU-JO-XCS" secondAttribute="leading" constant="24" id="ULQ-vc-5fX"/>
                                            <constraint firstAttribute="bottom" secondItem="HaZ-Yb-2mt" secondAttribute="bottom" constant="24" id="V6G-JR-60V"/>
                                            <constraint firstItem="HJs-qN-fdK" firstAttribute="leading" secondItem="OTU-JO-XCS" secondAttribute="leading" constant="24" id="YLb-Tg-vwI"/>
                                            <constraint firstItem="HJs-qN-fdK" firstAttribute="top" secondItem="OTU-JO-XCS" secondAttribute="top" id="b2a-B3-ZAz"/>
                                            <constraint firstAttribute="trailing" secondItem="XHB-WO-8r3" secondAttribute="trailing" constant="24" id="cSW-ds-lOs"/>
                                            <constraint firstAttribute="trailing" secondItem="HaZ-Yb-2mt" secondAttribute="trailing" constant="24" id="dEq-hW-Qo8"/>
                                            <constraint firstItem="HaZ-Yb-2mt" firstAttribute="top" secondItem="8qN-lf-n9Z" secondAttribute="bottom" constant="32" id="dKL-kM-oeD"/>
                                            <constraint firstAttribute="trailing" secondItem="8qN-lf-n9Z" secondAttribute="trailing" constant="24" id="k76-97-nXi"/>
                                            <constraint firstItem="XHB-WO-8r3" firstAttribute="top" secondItem="HJs-qN-fdK" secondAttribute="bottom" constant="24" id="rhN-nH-e1t"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="OTU-JO-XCS" firstAttribute="trailing" secondItem="eqX-2p-rLl" secondAttribute="trailing" id="7zQ-KV-oG9"/>
                                    <constraint firstItem="OTU-JO-XCS" firstAttribute="bottom" secondItem="eqX-2p-rLl" secondAttribute="bottom" id="L1Y-Ih-Vum"/>
                                    <constraint firstItem="OTU-JO-XCS" firstAttribute="width" secondItem="g6L-4N-geu" secondAttribute="width" id="RXZ-jB-9rD"/>
                                    <constraint firstItem="OTU-JO-XCS" firstAttribute="top" secondItem="eqX-2p-rLl" secondAttribute="top" id="bYW-2V-Tf8"/>
                                    <constraint firstItem="OTU-JO-XCS" firstAttribute="leading" secondItem="eqX-2p-rLl" secondAttribute="leading" id="zSr-ty-2VB"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="eqX-2p-rLl"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="g6L-4N-geu"/>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="yOW-ew-xHW"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="uyD-bt-RWn" firstAttribute="leading" secondItem="yOW-ew-xHW" secondAttribute="leading" id="7DH-5U-g6R"/>
                            <constraint firstItem="yOW-ew-xHW" firstAttribute="trailing" secondItem="uyD-bt-RWn" secondAttribute="trailing" id="Pem-XL-hO2"/>
                            <constraint firstItem="yOW-ew-xHW" firstAttribute="bottom" secondItem="uyD-bt-RWn" secondAttribute="bottom" constant="10" id="Y6V-dY-oDb"/>
                            <constraint firstItem="uyD-bt-RWn" firstAttribute="top" secondItem="yOW-ew-xHW" secondAttribute="top" constant="30" id="wkO-n5-pyX"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="a3X-Yu-jMZ">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="58Y-k5-IwJ">
                            <connections>
                                <action selector="didTapBack:" destination="pwH-DM-XWv" id="pZS-PM-CsX"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <size key="freeformSize" width="393" height="1000"/>
                    <connections>
                        <outlet property="backAddFileButton" destination="eA9-hs-4eN" id="byD-VD-Gs5"/>
                        <outlet property="backDescriptionLabel" destination="Ljx-ID-Q3x" id="BkR-nC-RHa"/>
                        <outlet property="backImageHeightConstraint" destination="C24-0d-IsD" id="wVP-K7-npO"/>
                        <outlet property="backImageView" destination="CzI-94-7V4" id="q3B-AK-K8U"/>
                        <outlet property="backStackView" destination="8qN-lf-n9Z" id="q9c-gl-ZTa"/>
                        <outlet property="backUIView" destination="lfK-mB-Yeh" id="isn-g0-ZE4"/>
                        <outlet property="continueButton" destination="HaZ-Yb-2mt" id="hgQ-xY-fC6"/>
                        <outlet property="frontAddFileButton" destination="OyU-OO-1a6" id="ptt-JM-fxO"/>
                        <outlet property="frontDescriptionLabel" destination="5Ej-rE-DjA" id="rAw-q4-roh"/>
                        <outlet property="frontImageHeightConstraint" destination="HD9-IY-ZIu" id="IEV-DQ-UBW"/>
                        <outlet property="frontImageView" destination="TAP-AQ-3zr" id="0GP-8M-Qea"/>
                        <outlet property="frontStackView" destination="6bz-aS-Qnf" id="nf2-KJ-aAl"/>
                        <outlet property="frontUIView" destination="a0x-CV-NLP" id="DBf-hq-axF"/>
                        <outlet property="headerLabel" destination="7ZH-up-lrr" id="oy2-Ov-6WF"/>
                        <outlet property="replaceBackButton" destination="CjC-tp-rri" id="zbP-gW-yu2"/>
                        <outlet property="replaceFrontButton" destination="arN-57-NQF" id="khM-Y4-jgQ"/>
                        <outlet property="subHeaderLabel" destination="Rng-eo-OI5" id="eKh-u5-JbO"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ztb-Pj-5ph" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="3271" y="1015"/>
        </scene>
        <!--MayaEKYC PhilsysID Guide View Controller-->
        <scene sceneID="JAn-Yf-jMq">
            <objects>
                <viewController storyboardIdentifier="MayaEKYCPhilsysIDGuideViewController" id="hd0-T5-rmW" customClass="MayaEKYCPhilsysIDGuideViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="QgG-Md-2xM">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="1000"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MNG-dt-vlU">
                                <rect key="frame" x="0.0" y="133" width="393" height="823"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sMQ-bc-ueq">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="1192"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="2Wu-tv-wlw">
                                                <rect key="frame" x="24" y="0.0" width="345" height="100"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="1000" text="Upload your Philippine National ID" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Zcf-uo-7Fs" customClass="Label" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="345" height="77.666666666666671"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="32"/>
                                                        <color key="textColor" name="PrimaryBlack"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="1000" text="Please upload the front and back of your ID" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Xdd-RA-WJi" customClass="Label" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="81.***************" width="345" height="18.***************"/>
                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                        <color key="textColor" name="Grey6"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" scrollEnabled="NO" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="yAd-d1-aWd">
                                                <rect key="frame" x="24" y="100" width="345" height="1000"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="1000" id="NYd-fR-hOd"/>
                                                </constraints>
                                                <prototypes>
                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" reuseIdentifier="MayaIDGuideCell" id="Tn8-vy-uOC" customClass="MayaIDGuideTableViewCell" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="50" width="345" height="202.33332824707031"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="Tn8-vy-uOC" id="7L1-bh-g7S">
                                                            <rect key="frame" x="0.0" y="0.0" width="345" height="202.33332824707031"/>
                                                            <autoresizingMask key="autoresizingMask"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="5Q9-PM-YP8">
                                                                    <rect key="frame" x="0.0" y="23.**************3" width="345" height="81.**************4"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="eGovPH app National ID" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dNg-xL-TJj">
                                                                            <rect key="frame" x="0.0" y="0.0" width="345" height="23.**************8"/>
                                                                            <fontDescription key="fontDescription" name="CerebriSansPro-Medium" family="Cerebri Sans Pro" pointSize="18"/>
                                                                            <color key="textColor" name="PrimaryBlack"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" editable="NO" text="Upload a screenshot of your National ID from the eGov app. Please do not use a printout of your ID." textAlignment="natural" selectable="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bVL-aS-MKp">
                                                                            <rect key="frame" x="0.0" y="28.**************8" width="345" height="52.***************"/>
                                                                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                            <color key="textColor" name="Grey6"/>
                                                                            <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                                            <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                                                        </textView>
                                                                    </subviews>
                                                                </stackView>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="mDH-55-vaI">
                                                                    <rect key="frame" x="0.0" y="121.33333333333333" width="345" height="0.3333333333333286"/>
                                                                </stackView>
                                                                <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" editable="NO" text="Upload a screenshot of your National ID from the eGov app. Please do not use a printout of your ID." textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="DcR-VP-LsU">
                                                                    <rect key="frame" x="0.0" y="137.**************" width="345" height="52.***************"/>
                                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                    <color key="textColor" name="Grey6"/>
                                                                    <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                                                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                                                </textView>
                                                            </subviews>
                                                            <constraints>
                                                                <constraint firstItem="mDH-55-vaI" firstAttribute="leading" secondItem="7L1-bh-g7S" secondAttribute="leading" id="9iJ-Ng-w19"/>
                                                                <constraint firstAttribute="bottom" secondItem="DcR-VP-LsU" secondAttribute="bottom" constant="12" id="Lye-dZ-6Ko"/>
                                                                <constraint firstAttribute="trailing" secondItem="DcR-VP-LsU" secondAttribute="trailing" id="Yok-Yy-3LB"/>
                                                                <constraint firstItem="mDH-55-vaI" firstAttribute="top" secondItem="5Q9-PM-YP8" secondAttribute="bottom" constant="16" id="fNk-eu-nGb"/>
                                                                <constraint firstAttribute="trailing" secondItem="mDH-55-vaI" secondAttribute="trailing" id="kij-Yh-o6T"/>
                                                                <constraint firstItem="DcR-VP-LsU" firstAttribute="leading" secondItem="7L1-bh-g7S" secondAttribute="leading" id="nDT-Eb-t6E"/>
                                                                <constraint firstItem="5Q9-PM-YP8" firstAttribute="leading" secondItem="7L1-bh-g7S" secondAttribute="leading" id="rHQ-Lp-bM0"/>
                                                                <constraint firstItem="DcR-VP-LsU" firstAttribute="top" secondItem="mDH-55-vaI" secondAttribute="bottom" constant="16" id="rYx-FK-saT"/>
                                                                <constraint firstAttribute="trailing" secondItem="5Q9-PM-YP8" secondAttribute="trailing" id="sdY-fs-8DS"/>
                                                                <constraint firstItem="5Q9-PM-YP8" firstAttribute="top" secondItem="7L1-bh-g7S" secondAttribute="top" constant="24" id="tuS-4A-jFT"/>
                                                            </constraints>
                                                        </tableViewCellContentView>
                                                        <connections>
                                                            <outlet property="additionalDescriptionTextView" destination="DcR-VP-LsU" id="bQ2-5F-m9A"/>
                                                            <outlet property="descriptionTextView" destination="bVL-aS-MKp" id="OFb-M4-6uY"/>
                                                            <outlet property="imagesStackView" destination="mDH-55-vaI" id="kC9-ud-8ye"/>
                                                            <outlet property="titleLabel" destination="dNg-xL-TJj" id="tvT-HF-Ul2"/>
                                                        </connections>
                                                    </tableViewCell>
                                                </prototypes>
                                                <connections>
                                                    <outlet property="dataSource" destination="hd0-T5-rmW" id="scK-4c-6aA"/>
                                                    <outlet property="delegate" destination="hd0-T5-rmW" id="P0q-CF-7UC"/>
                                                </connections>
                                            </tableView>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="bYz-9Q-dpo">
                                                <rect key="frame" x="24" y="1112" width="345" height="56"/>
                                                <subviews>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bCM-8L-Ge5" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="167.**************" height="56"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="56" id="Vb3-U8-rVg"/>
                                                        </constraints>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal" title="Upload ID"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                <integer key="value" value="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="uploadIDButtonClicked:" destination="hd0-T5-rmW" eventType="touchUpInside" id="3Ch-0n-mMP"/>
                                                        </connections>
                                                    </button>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="5h8-j9-ofc" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="177.66666666666663" y="0.0" width="167.**************" height="56"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="56" id="K5j-RX-HeI"/>
                                                        </constraints>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal" title="Take photo"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                <integer key="value" value="0"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="takePhotoButtonClicked:" destination="hd0-T5-rmW" eventType="touchUpInside" id="0em-9p-0RX"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="2Wu-tv-wlw" firstAttribute="top" secondItem="sMQ-bc-ueq" secondAttribute="top" id="5mE-kr-lpe"/>
                                            <constraint firstItem="2Wu-tv-wlw" firstAttribute="leading" secondItem="sMQ-bc-ueq" secondAttribute="leading" constant="24" id="IDL-M8-q0A"/>
                                            <constraint firstAttribute="trailing" secondItem="yAd-d1-aWd" secondAttribute="trailing" constant="24" id="JyK-SA-ZZx"/>
                                            <constraint firstAttribute="bottom" secondItem="bYz-9Q-dpo" secondAttribute="bottom" constant="24" id="NZN-5Z-H9l"/>
                                            <constraint firstItem="bYz-9Q-dpo" firstAttribute="leading" secondItem="sMQ-bc-ueq" secondAttribute="leading" constant="24" id="Vg4-so-hBp"/>
                                            <constraint firstAttribute="trailing" secondItem="bYz-9Q-dpo" secondAttribute="trailing" constant="24" id="b8b-wP-4Ca"/>
                                            <constraint firstItem="yAd-d1-aWd" firstAttribute="leading" secondItem="sMQ-bc-ueq" secondAttribute="leading" constant="24" id="bDp-Ov-KKm"/>
                                            <constraint firstItem="yAd-d1-aWd" firstAttribute="top" secondItem="2Wu-tv-wlw" secondAttribute="bottom" id="m8l-Bp-Ckt"/>
                                            <constraint firstItem="bYz-9Q-dpo" firstAttribute="top" secondItem="yAd-d1-aWd" secondAttribute="bottom" constant="12" id="pQC-VV-Klm"/>
                                            <constraint firstAttribute="trailing" secondItem="2Wu-tv-wlw" secondAttribute="trailing" constant="24" id="uPv-r0-ia9"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="sMQ-bc-ueq" firstAttribute="trailing" secondItem="a8o-wG-gGx" secondAttribute="trailing" id="CMb-pm-sUb"/>
                                    <constraint firstItem="sMQ-bc-ueq" firstAttribute="leading" secondItem="a8o-wG-gGx" secondAttribute="leading" id="K3h-oH-4cY"/>
                                    <constraint firstItem="sMQ-bc-ueq" firstAttribute="top" secondItem="a8o-wG-gGx" secondAttribute="top" id="ebk-ln-tRK"/>
                                    <constraint firstItem="sMQ-bc-ueq" firstAttribute="bottom" secondItem="a8o-wG-gGx" secondAttribute="bottom" id="itp-Jc-jKs"/>
                                    <constraint firstItem="sMQ-bc-ueq" firstAttribute="width" secondItem="lSZ-Zw-SaI" secondAttribute="width" id="o71-W5-8DL"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="a8o-wG-gGx"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="lSZ-Zw-SaI"/>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="eQz-HC-gXW"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="eQz-HC-gXW" firstAttribute="bottom" secondItem="MNG-dt-vlU" secondAttribute="bottom" constant="10" id="5N3-fd-Fg2"/>
                            <constraint firstItem="MNG-dt-vlU" firstAttribute="leading" secondItem="eQz-HC-gXW" secondAttribute="leading" id="Hgv-lR-i2e"/>
                            <constraint firstItem="eQz-HC-gXW" firstAttribute="trailing" secondItem="MNG-dt-vlU" secondAttribute="trailing" id="KKO-fW-4gk"/>
                            <constraint firstItem="MNG-dt-vlU" firstAttribute="top" secondItem="eQz-HC-gXW" secondAttribute="top" constant="30" id="heD-w9-ghz"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="bjb-Qq-AXD">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="KOG-jp-kZh">
                            <connections>
                                <action selector="didTapBack:" destination="hd0-T5-rmW" id="Vat-hr-xLI"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <size key="freeformSize" width="393" height="1000"/>
                    <connections>
                        <outlet property="guideTableView" destination="yAd-d1-aWd" id="hTf-mQ-ytv"/>
                        <outlet property="mainScrollView" destination="MNG-dt-vlU" id="ZMI-Kd-gFw"/>
                        <outlet property="subtitleLabel" destination="Xdd-RA-WJi" id="GWT-B7-8Yg"/>
                        <outlet property="tableHeightConstraint" destination="NYd-fR-hOd" id="zag-lO-Eqy"/>
                        <outlet property="takePhotoButton" destination="5h8-j9-ofc" id="qLz-Wp-0I1"/>
                        <outlet property="titleLabel" destination="Zcf-uo-7Fs" id="Rba-zt-w6M"/>
                        <outlet property="uploadIDButton" destination="bCM-8L-Ge5" id="VrL-pk-3kh"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="kXG-FE-toS" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="4258.7786259541981" y="1014.7887323943662"/>
        </scene>
        <!--MayaEKYC SecondaryID View Controller-->
        <scene sceneID="VTk-e6-Xz1">
            <objects>
                <viewController storyboardIdentifier="MayaEKYCNewSecondaryIDViewController" id="lxq-Ep-sn7" customClass="MayaEKYCSecondaryIDViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="pqJ-Sg-Lch">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yIa-Fh-bLy">
                                <rect key="frame" x="0.0" y="119" width="393" height="699"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="HhX-Y7-2JE">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="211.33333333333334"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="18" translatesAutoresizingMaskIntoConstraints="NO" id="Gxr-WE-I9D">
                                                <rect key="frame" x="24" y="0.0" width="345" height="211.33333333333334"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="1000" text="Select another ID" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Fbo-E4-7a0" customClass="Label" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="345" height="39"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="32"/>
                                                        <color key="textColor" name="PrimaryBlack"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="1000" text="Your ID helps us confirm your identity. Please choose a valid ID to submit from the list below:" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kDb-xh-Eju" customClass="Label" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="57" width="345" height="34"/>
                                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="14"/>
                                                        <color key="textColor" name="Grey5"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" alignment="center" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="gKi-Sz-87J">
                                                        <rect key="frame" x="0.0" y="109" width="345" height="24.333333333333343"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="1000" text="SECONDARY ID" textAlignment="natural" lineBreakMode="wordWrap" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="twF-zj-GWs" userLabel="Secondary Ids" customClass="Label" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="146" height="24.333333333333332"/>
                                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="20"/>
                                                                <color key="textColor" name="PrimaryBlack"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Two IDs are required" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rWz-pC-To1">
                                                                <rect key="frame" x="188.66666666666663" y="2" width="156.**************" height="20.333333333333332"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" name="Grey6"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3n2-FU-jZu" customClass="MayaZolozIdSubmitPickerView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="151.**************" width="345" height="60"/>
                                                        <color key="backgroundColor" name="Grey1"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" priority="250" constant="60" id="fPK-DW-YUd"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" name="Grey1"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="Gxr-WE-I9D" secondAttribute="trailing" constant="24" id="4vT-yQ-2QA"/>
                                            <constraint firstItem="Gxr-WE-I9D" firstAttribute="leading" secondItem="HhX-Y7-2JE" secondAttribute="leading" constant="24" id="6eI-zH-hrW"/>
                                            <constraint firstItem="Gxr-WE-I9D" firstAttribute="top" secondItem="HhX-Y7-2JE" secondAttribute="top" id="cc2-5f-LgX"/>
                                            <constraint firstAttribute="bottom" secondItem="Gxr-WE-I9D" secondAttribute="bottom" id="w9T-xN-5h3"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" name="Grey1"/>
                                <constraints>
                                    <constraint firstItem="HhX-Y7-2JE" firstAttribute="top" secondItem="7IR-Kl-0ew" secondAttribute="top" id="3T4-k9-zO9"/>
                                    <constraint firstItem="HhX-Y7-2JE" firstAttribute="leading" secondItem="7IR-Kl-0ew" secondAttribute="leading" id="A73-KL-h4D"/>
                                    <constraint firstItem="HhX-Y7-2JE" firstAttribute="bottom" secondItem="7IR-Kl-0ew" secondAttribute="bottom" id="BU4-fH-HDK"/>
                                    <constraint firstItem="HhX-Y7-2JE" firstAttribute="width" secondItem="plg-Q8-KZm" secondAttribute="width" id="J20-tV-owE"/>
                                    <constraint firstItem="HhX-Y7-2JE" firstAttribute="trailing" secondItem="7IR-Kl-0ew" secondAttribute="trailing" id="n6Z-b1-CVB"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="7IR-Kl-0ew"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="plg-Q8-KZm"/>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="L4c-zQ-rf7"/>
                        <color key="backgroundColor" name="Grey1"/>
                        <constraints>
                            <constraint firstItem="L4c-zQ-rf7" firstAttribute="trailing" secondItem="yIa-Fh-bLy" secondAttribute="trailing" id="3pV-aT-t09"/>
                            <constraint firstItem="yIa-Fh-bLy" firstAttribute="top" secondItem="L4c-zQ-rf7" secondAttribute="top" constant="16" id="6ux-jh-hVM"/>
                            <constraint firstItem="L4c-zQ-rf7" firstAttribute="bottom" secondItem="yIa-Fh-bLy" secondAttribute="bottom" id="Zx0-dV-2DN"/>
                            <constraint firstItem="yIa-Fh-bLy" firstAttribute="leading" secondItem="L4c-zQ-rf7" secondAttribute="leading" id="ooc-Zw-Ags"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="P0g-1a-OKm">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="LU1-op-EKK">
                            <connections>
                                <action selector="didTapBack:" destination="lxq-Ep-sn7" id="dmO-1v-HRu"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <connections>
                        <outlet property="pickerViewLabel" destination="twF-zj-GWs" id="pWS-Yq-zaU"/>
                        <outlet property="pickerViewSubLabel" destination="rWz-pC-To1" id="fnG-Bc-WaB"/>
                        <outlet property="secondaryIDPickerView" destination="3n2-FU-jZu" id="oGS-Dk-IbN"/>
                        <outlet property="subTitleLabel" destination="kDb-xh-Eju" id="KGs-Hj-RY1"/>
                        <outlet property="titleLabel" destination="Fbo-E4-7a0" id="THl-Kb-rg8"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="mLL-KE-QTI" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="184" y="794"/>
        </scene>
        <!--MayaEKYC Selfie Tips View Controller-->
        <scene sceneID="l8h-od-96H">
            <objects>
                <viewController storyboardIdentifier="MayaEKYCSelfieTipsViewController" id="N2M-66-YI5" customClass="MayaEKYCSelfieTipsViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="LTp-vW-pM6">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YZ2-sC-bmX">
                                <rect key="frame" x="0.0" y="103" width="248" height="627"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="40" translatesAutoresizingMaskIntoConstraints="NO" id="7WS-hd-WMY">
                                        <rect key="frame" x="24" y="171.33333333333329" width="200" height="284.66666666666674"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="X9W-Ka-Lhe">
                                                <rect key="frame" x="0.0" y="0.0" width="200" height="200"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="200" id="qdP-md-4JF"/>
                                                    <constraint firstAttribute="width" constant="200" id="vws-CF-KJe"/>
                                                </constraints>
                                            </imageView>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="mEP-AH-fP6">
                                                <rect key="frame" x="0.0" y="240.00000000000003" width="200" height="44.***************"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1y2-PB-bCl">
                                                        <rect key="frame" x="0.0" y="0.0" width="200" height="20.333333333333332"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="CYt-eA-eAU">
                                                        <rect key="frame" x="0.0" y="24.333333333333258" width="200" height="20.***************"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" name="Grey1"/>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" name="Grey1"/>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" name="Grey1"/>
                                <constraints>
                                    <constraint firstItem="7WS-hd-WMY" firstAttribute="centerX" secondItem="YZ2-sC-bmX" secondAttribute="centerX" id="by9-yk-1WZ"/>
                                    <constraint firstItem="7WS-hd-WMY" firstAttribute="centerY" secondItem="YZ2-sC-bmX" secondAttribute="centerY" id="mpt-9X-28r"/>
                                    <constraint firstAttribute="trailing" secondItem="7WS-hd-WMY" secondAttribute="trailing" constant="24" id="puc-rs-hfW"/>
                                    <constraint firstItem="7WS-hd-WMY" firstAttribute="leading" secondItem="YZ2-sC-bmX" secondAttribute="leading" constant="24" id="s0j-Nk-wUa"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="aW8-tW-mLQ" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="24" y="738" width="200" height="56"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="56" id="Wjp-mQ-3ig"/>
                                </constraints>
                                <color key="tintColor" name="PrimaryWhite"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="Continue">
                                    <fontDescription key="titleFontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                </buttonConfiguration>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                        <integer key="value" value="0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="continueButtonPressed:" destination="N2M-66-YI5" eventType="touchUpInside" id="cRb-Cj-QIa"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="7NT-jK-kf0"/>
                        <color key="backgroundColor" name="Grey1"/>
                        <constraints>
                            <constraint firstItem="7NT-jK-kf0" firstAttribute="trailing" secondItem="aW8-tW-mLQ" secondAttribute="trailing" constant="24" id="7k1-o0-nrG"/>
                            <constraint firstItem="7NT-jK-kf0" firstAttribute="bottom" secondItem="aW8-tW-mLQ" secondAttribute="bottom" constant="24" id="Dq4-qD-YMf"/>
                            <constraint firstItem="aW8-tW-mLQ" firstAttribute="leading" secondItem="7NT-jK-kf0" secondAttribute="leading" constant="24" id="F6F-XQ-AYg"/>
                            <constraint firstItem="aW8-tW-mLQ" firstAttribute="top" secondItem="YZ2-sC-bmX" secondAttribute="bottom" constant="8" id="eJK-rY-xLb"/>
                            <constraint firstItem="YZ2-sC-bmX" firstAttribute="top" secondItem="7NT-jK-kf0" secondAttribute="top" id="hNq-qu-TUI"/>
                            <constraint firstItem="7NT-jK-kf0" firstAttribute="trailing" secondItem="YZ2-sC-bmX" secondAttribute="trailing" id="swr-Y9-aY0"/>
                            <constraint firstItem="YZ2-sC-bmX" firstAttribute="leading" secondItem="7NT-jK-kf0" secondAttribute="leading" id="uRK-9Y-euy"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="gpn-O2-z1e">
                        <barButtonItem key="leftBarButtonItem" image="iconBack" id="QLh-9Y-gqx">
                            <connections>
                                <action selector="didTapBack:" destination="N2M-66-YI5" id="6t8-hE-rtA"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <connections>
                        <outlet property="continueButton" destination="aW8-tW-mLQ" id="Sqo-3Q-9rl"/>
                        <outlet property="descriptionLabel" destination="CYt-eA-eAU" id="lhN-2z-3ze"/>
                        <outlet property="headerLabel" destination="1y2-PB-bCl" id="ybl-F8-RXV"/>
                        <outlet property="selfieIconImageView" destination="X9W-Ka-Lhe" id="hHF-Kk-5e2"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="m5r-Ux-MaU" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2503" y="98"/>
        </scene>
    </scenes>
    <resources>
        <image name="CaptureId" width="70" height="70"/>
        <image name="iconAddToContacts" width="12" height="12"/>
        <image name="iconBack" width="22" height="17"/>
        <image name="iconBackWhite" width="24" height="24"/>
        <image name="iconBadgeVerifiedInfo" width="16" height="16"/>
        <image name="iconBank" width="18" height="18"/>
        <image name="iconCamera" width="18.***************" height="18.***************"/>
        <image name="iconCameraWhite" width="20" height="20"/>
        <image name="iconFlex" width="18" height="18"/>
        <image name="iconInfoFilledWhite" width="17" height="17"/>
        <image name="iconInfoNote" width="17" height="17"/>
        <image name="iconPhone" width="18" height="18"/>
        <image name="iconRadioButtonSelected" width="24" height="24"/>
        <image name="iconSystemCross" width="24" height="24"/>
        <image name="imageMayaRainbow" width="160" height="104"/>
        <image name="imagePhilsysPhysicalBack" width="327" height="205"/>
        <image name="imageSimpleCaptureIDFrame" width="327" height="260"/>
        <image name="info" width="20" height="20"/>
        <image name="sampleIdDocument" width="1080" height="1920"/>
        <namedColor name="BackgroundPrimaryGreen">
            <color red="0.0" green="0.*****************" blue="0.31799998879432678" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ButtonFocusedPrimaryGreen">
            <color red="0.0" green="0.65098039215686276" blue="0.31764705882352939" alpha="0.87999999523162842" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Gray140S">
            <color red="0.44299998879432678" green="0.45500001311302185" blue="0.49000000953674316" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey1">
            <color red="0.97647058823529409" green="0.97647058823529409" blue="0.98039215686274506" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey3">
            <color red="0.92549019607843142" green="0.92941176470588238" blue="0.93725490196078431" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey5">
            <color red="0.50196078431372548" green="0.50196078431372548" blue="0.50196078431372548" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey6">
            <color red="0.333**************" green="0.33725490196078434" blue="0.34901960784313724" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryBlack">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryGrownGreen">
            <color red="0.0" green="0.65098039215686276" blue="0.31764705882352939" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryWhite">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="SecondaryLightGreen">
            <color red="0.11372549019607843" green="0.7686274509803922" blue="0.22352941176470589" alpha="0.15000000596046448" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
