<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="CerebriSansPro-Book.otf">
            <string>CerebriSansPro-Book</string>
        </array>
        <array key="Jeko-Bold.otf">
            <string>Jeko-Bold</string>
        </array>
        <array key="Jeko-SemiBold.otf">
            <string>Jeko-SemiBold</string>
        </array>
    </customFonts>
    <scenes>
        <!--Tab Bar Controller-->
        <scene sceneID="ckP-KN-Tt5">
            <objects>
                <tabBarController storyboardIdentifier="MainTabBarController" id="DVn-qF-5oG" sceneMemberID="viewController">
                    <tabBar key="tabBar" contentMode="scaleToFill" id="kqE-Oe-Ybe">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="49"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="color" keyPath="tintColor">
                                <color key="value" red="0.**********" green="0.63921568630000003" blue="0.87843137250000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </tabBar>
                </tabBarController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="0EC-rK-BCo" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1518" y="-866"/>
        </scene>
        <!--Importer View Controller-->
        <scene sceneID="EZx-Py-pvW">
            <objects>
                <viewController storyboardIdentifier="ImporterViewController" modalTransitionStyle="crossDissolve" modalPresentationStyle="overCurrentContext" id="a77-ce-8cX" customClass="ImporterViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="H4P-ti-TGf">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="YH6-PB-60D">
                                <rect key="frame" x="16" y="75" width="361" height="727"/>
                                <subviews>
                                    <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="mBX-GP-4eQ" customClass="TextView" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="361" height="667"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                <color key="value" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                <real key="value" value="1"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="2"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="string" keyPath="placeholder" value="Importer Service Not Started..."/>
                                        </userDefinedRuntimeAttributes>
                                    </textView>
                                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Muw-it-g4b" customClass="Button" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="687" width="361" height="40"/>
                                        <color key="backgroundColor" red="0.**********" green="0.63921568630000003" blue="0.87843137250000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="KQW-rq-aAp"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                        <state key="normal" title="START">
                                            <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <state key="highlighted">
                                            <color key="titleColor" red="0.**********" green="0.63921568630000003" blue="0.87843137250000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                <real key="value" value="2"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                <real key="value" value="1"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                <color key="value" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="didTapStart:" destination="a77-ce-8cX" eventType="touchUpInside" id="Aar-NN-JkI"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="mBX-GP-4eQ" firstAttribute="width" secondItem="YH6-PB-60D" secondAttribute="width" id="Knf-DN-w8V"/>
                                    <constraint firstItem="Muw-it-g4b" firstAttribute="width" secondItem="YH6-PB-60D" secondAttribute="width" id="MdQ-RZ-ce9"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="bAj-D4-QQ8"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="bAj-D4-QQ8" firstAttribute="trailing" secondItem="YH6-PB-60D" secondAttribute="trailing" constant="16" id="SdZ-dp-Bds"/>
                            <constraint firstItem="YH6-PB-60D" firstAttribute="top" secondItem="bAj-D4-QQ8" secondAttribute="top" constant="16" id="XPY-if-Isq"/>
                            <constraint firstItem="bAj-D4-QQ8" firstAttribute="bottom" secondItem="YH6-PB-60D" secondAttribute="bottom" constant="16" id="kgA-lm-5Iq"/>
                            <constraint firstItem="YH6-PB-60D" firstAttribute="leading" secondItem="bAj-D4-QQ8" secondAttribute="leading" constant="16" id="mPX-gm-3pD"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="logsTextView" destination="mBX-GP-4eQ" id="Dkd-3V-d9D"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="XlV-mj-q4k" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="690" y="-1598"/>
        </scene>
        <!--Maya Initial View Controller-->
        <scene sceneID="BmE-oZ-7Oi">
            <objects>
                <viewController storyboardIdentifier="MayaInitialViewController" id="ac0-E0-NrP" customClass="MayaInitialViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="IWs-bS-5Yg">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconMayaLogoPlainGreen" translatesAutoresizingMaskIntoConstraints="NO" id="BjD-hT-N7a">
                                <rect key="frame" x="90.666666666666686" y="408" width="212" height="61"/>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Pvv-zq-jG5"/>
                        <color key="backgroundColor" name="PrimaryBlack"/>
                        <constraints>
                            <constraint firstItem="BjD-hT-N7a" firstAttribute="centerX" secondItem="Pvv-zq-jG5" secondAttribute="centerX" id="2Nd-6Z-TOn"/>
                            <constraint firstItem="BjD-hT-N7a" firstAttribute="centerY" secondItem="Pvv-zq-jG5" secondAttribute="centerY" id="9vS-9L-DCA"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                        <userDefinedRuntimeAttribute type="number" keyPath="statusBarStyle">
                            <integer key="value" value="1"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="8JO-IS-HRh" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="701.25" y="2012.323943661972"/>
        </scene>
        <!--Maya Welcome View Controller-->
        <scene sceneID="PBD-e0-WmF">
            <objects>
                <viewController storyboardIdentifier="MayaWelcomeViewController" id="S1s-4i-PnE" customClass="MayaWelcomeViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Mul-Lv-WN8">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="imageMayaLogoTopBlackDescription" translatesAutoresizingMaskIntoConstraints="NO" id="46a-AI-SCs">
                                <rect key="frame" x="76.666666666666686" y="299" width="240" height="79"/>
                                <accessibility key="accessibilityConfiguration" identifier="pmios_initial_image_view">
                                    <bool key="isElement" value="YES"/>
                                </accessibility>
                                <constraints>
                                    <constraint firstAttribute="height" constant="79" id="jbt-uI-hBd"/>
                                    <constraint firstAttribute="width" constant="240" id="xTW-fE-ln4"/>
                                </constraints>
                            </imageView>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="eU4-td-331">
                                <rect key="frame" x="24" y="604.66666666666663" width="382" height="189.33333333333337"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="dBE-mt-Ot4" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="382" height="56"/>
                                        <accessibility key="accessibilityConfiguration" identifier="pmios_initial_register_button"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="yza-r6-kYE"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Start an account"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="didTapRegister:" destination="S1s-4i-PnE" eventType="touchUpInside" id="hcc-nN-GvK"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="v4N-kI-3Fk" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="68" width="382" height="56"/>
                                        <accessibility key="accessibilityConfiguration" identifier="pmios_initial_login_button"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="wvI-nd-Lhz"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Log in"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                <integer key="value" value="2"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="didTapLogin:" destination="S1s-4i-PnE" eventType="touchUpInside" id="xQd-VE-Yla"/>
                                        </connections>
                                    </button>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ngk-bC-zMd">
                                        <rect key="frame" x="0.0" y="136" width="382" height="12"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="12" id="2TO-XN-Sah"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Regulated by the Bangko Sentral ng Pilipinas. Deposits are insured by PDIC up to P1,000,000 per depositor." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uFF-aC-LOP">
                                        <rect key="frame" x="0.0" y="160" width="382" height="29.333333333333343"/>
                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="12"/>
                                        <color key="textColor" name="ContentGrey4"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label hidden="YES" opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Device ID: ---" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" id="d03-xR-9EH">
                                        <rect key="frame" x="0.0" y="189.33333333333334" width="382" height="0.0"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="12"/>
                                        <color key="textColor" name="ContentGrey4"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="iPZ-fw-5Au"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="iPZ-fw-5Au" firstAttribute="bottom" secondItem="eU4-td-331" secondAttribute="bottom" constant="24" id="VaX-Xe-2FS"/>
                            <constraint firstItem="eU4-td-331" firstAttribute="leading" secondItem="iPZ-fw-5Au" secondAttribute="leading" constant="24" id="ceV-p8-vrA"/>
                            <constraint firstItem="iPZ-fw-5Au" firstAttribute="trailing" secondItem="eU4-td-331" secondAttribute="trailing" constant="24" id="e3m-p5-D1H"/>
                            <constraint firstItem="46a-AI-SCs" firstAttribute="centerX" secondItem="iPZ-fw-5Au" secondAttribute="centerX" id="f4c-Ot-AXF"/>
                            <constraint firstItem="46a-AI-SCs" firstAttribute="centerY" secondItem="iPZ-fw-5Au" secondAttribute="centerY" constant="-100" id="i8e-6p-vDm"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="deviceIDLabel" destination="d03-xR-9EH" id="yKV-ao-xfH"/>
                        <outlet property="footerLabel" destination="uFF-aC-LOP" id="P4O-aw-8W6"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="3vf-cl-0bw" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="701.60000000000002" y="2682.9085457271367"/>
        </scene>
        <!--Maya Introduction View Controller-->
        <scene sceneID="PcM-wH-8xZ">
            <objects>
                <viewController storyboardIdentifier="MayaIntroductionViewController" id="96v-hk-gB1" customClass="MayaIntroductionViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ee8-j1-MSi">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <pageControl opaque="NO" contentMode="scaleToFill" enabled="NO" contentHorizontalAlignment="center" contentVerticalAlignment="center" numberOfPages="3" translatesAutoresizingMaskIntoConstraints="NO" id="NaZ-tX-hrH">
                                <rect key="frame" x="160" y="672" width="73" height="26"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="26" id="BqD-Tl-rFR"/>
                                </constraints>
                                <color key="pageIndicatorTintColor" name="OpacityGreen25"/>
                                <color key="currentPageIndicatorTintColor" name="ContentPrimaryGreen"/>
                            </pageControl>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="mnB-LG-LaH" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="24" y="738" width="345" height="56"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="56" id="fYS-hU-1dT"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="Get started"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                        <integer key="value" value="0"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                        <integer key="value" value="0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="didTapGetStarted:" destination="96v-hk-gB1" eventType="touchUpInside" id="4Gk-C0-0RF"/>
                                </connections>
                            </button>
                            <containerView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BBe-rO-8Ct">
                                <rect key="frame" x="0.0" y="59" width="393" height="597"/>
                            </containerView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="dhx-21-hCg"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="NaZ-tX-hrH" firstAttribute="centerX" secondItem="Ee8-j1-MSi" secondAttribute="centerX" id="0ph-He-eLt"/>
                            <constraint firstItem="NaZ-tX-hrH" firstAttribute="top" secondItem="BBe-rO-8Ct" secondAttribute="bottom" constant="16" id="5Ei-1L-iMx"/>
                            <constraint firstItem="mnB-LG-LaH" firstAttribute="leading" secondItem="dhx-21-hCg" secondAttribute="leading" constant="24" id="A8J-ic-VJr"/>
                            <constraint firstItem="BBe-rO-8Ct" firstAttribute="leading" secondItem="dhx-21-hCg" secondAttribute="leading" id="DNO-15-q2N"/>
                            <constraint firstItem="mnB-LG-LaH" firstAttribute="top" secondItem="NaZ-tX-hrH" secondAttribute="bottom" constant="40" id="LlB-D4-gWq"/>
                            <constraint firstItem="BBe-rO-8Ct" firstAttribute="top" secondItem="dhx-21-hCg" secondAttribute="top" id="hJs-q4-WNw"/>
                            <constraint firstItem="dhx-21-hCg" firstAttribute="bottom" secondItem="mnB-LG-LaH" secondAttribute="bottom" constant="24" id="oIv-6M-q6j"/>
                            <constraint firstItem="dhx-21-hCg" firstAttribute="trailing" secondItem="mnB-LG-LaH" secondAttribute="trailing" constant="24" id="qol-ed-j91"/>
                            <constraint firstItem="dhx-21-hCg" firstAttribute="trailing" secondItem="BBe-rO-8Ct" secondAttribute="trailing" id="xaV-Bq-uVI"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="containerView" destination="BBe-rO-8Ct" id="1aM-ec-Mki"/>
                        <outlet property="pageControl" destination="NaZ-tX-hrH" id="x4c-s5-Dkj"/>
                        <outlet property="pageControlAndButtonSpacingConstraint" destination="LlB-D4-gWq" id="bg5-XD-fZH"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="0gv-C7-8m2" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1447.6923076923076" y="3356.8720379146916"/>
        </scene>
        <!--Maya Introduction Page View Controller-->
        <scene sceneID="BCO-VF-6eF">
            <objects>
                <viewController storyboardIdentifier="MayaIntroductionPageViewController" id="VRG-K1-aVF" customClass="MayaIntroductionPageViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="cpP-Rw-fHa">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tF0-ae-KEH">
                                <rect key="frame" x="0.0" y="190.66666666666666" width="393" height="470.66666666666674"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Save" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Km7-ba-LY3">
                                        <rect key="frame" x="138.33333333333334" y="331" width="116.66666666666666" height="59"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="59" id="Fvd-uB-Zy4"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="48"/>
                                        <color key="textColor" name="PrimaryBlack"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="751" text="Forget phone numbers and send money with a @username. Put it on your Maya card, too!​" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="da0-6F-n4g">
                                        <rect key="frame" x="20" y="402" width="353" height="44.666666666666686"/>
                                        <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="17"/>
                                        <color key="textColor" name="Grey6"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="pAX-PM-JYk">
                                        <rect key="frame" x="24" y="24" width="345" height="283"/>
                                        <constraints>
                                            <constraint firstAttribute="width" secondItem="pAX-PM-JYk" secondAttribute="height" multiplier="1.22" id="A5U-eU-xTb"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="pAX-PM-JYk" secondAttribute="trailing" constant="24" id="4Y4-Df-1br"/>
                                    <constraint firstAttribute="trailing" secondItem="da0-6F-n4g" secondAttribute="trailing" constant="20" id="62U-Qw-eKI"/>
                                    <constraint firstItem="Km7-ba-LY3" firstAttribute="top" secondItem="pAX-PM-JYk" secondAttribute="bottom" constant="24" id="Lir-Uq-ZnW"/>
                                    <constraint firstItem="da0-6F-n4g" firstAttribute="leading" secondItem="tF0-ae-KEH" secondAttribute="leading" constant="20" id="aiD-4Y-4Ur"/>
                                    <constraint firstAttribute="bottom" secondItem="da0-6F-n4g" secondAttribute="bottom" constant="24" id="b8Z-6Q-UXZ"/>
                                    <constraint firstAttribute="height" priority="750" constant="388" id="f7k-4w-Q6P"/>
                                    <constraint firstItem="pAX-PM-JYk" firstAttribute="top" secondItem="tF0-ae-KEH" secondAttribute="top" constant="24" id="gZQ-wd-ME7"/>
                                    <constraint firstItem="pAX-PM-JYk" firstAttribute="leading" secondItem="tF0-ae-KEH" secondAttribute="leading" constant="24" id="lT5-iD-SmL"/>
                                    <constraint firstItem="da0-6F-n4g" firstAttribute="top" secondItem="Km7-ba-LY3" secondAttribute="bottom" constant="12" id="n2S-LT-bHf"/>
                                    <constraint firstItem="Km7-ba-LY3" firstAttribute="centerX" secondItem="tF0-ae-KEH" secondAttribute="centerX" id="rNx-RD-qZP"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Hnm-Im-lEz"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="tF0-ae-KEH" firstAttribute="centerY" secondItem="cpP-Rw-fHa" secondAttribute="centerY" id="2XG-av-m1m"/>
                            <constraint firstItem="Hnm-Im-lEz" firstAttribute="trailing" secondItem="tF0-ae-KEH" secondAttribute="trailing" id="MaP-uf-KPk"/>
                            <constraint firstItem="tF0-ae-KEH" firstAttribute="leading" secondItem="Hnm-Im-lEz" secondAttribute="leading" id="Rqm-cx-MNX"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="featureDescriptionLabel" destination="da0-6F-n4g" id="1l9-af-Hvg"/>
                        <outlet property="featureImageView" destination="pAX-PM-JYk" id="xri-Kg-Q0s"/>
                        <outlet property="featureTitleLabel" destination="Km7-ba-LY3" id="oSA-uG-Xv9"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="fnd-eG-F1O" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1448" y="4050"/>
        </scene>
        <!--Maya WelcomeV2 View Controller-->
        <scene sceneID="9aV-zI-43e">
            <objects>
                <viewController storyboardIdentifier="MayaWelcomeViewControllerV2" id="NQB-Ms-CYk" customClass="MayaWelcomeV2ViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" ambiguous="YES" id="Pj6-RH-z84">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="wV2-lu-1z8">
                                <rect key="frame" x="24" y="604.66666666666663" width="382" height="189.33333333333337"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4FU-Bj-QHY" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="382" height="56"/>
                                        <accessibility key="accessibilityConfiguration" identifier="pmios_initial_register_button"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="gTh-oD-NEy"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Start an account"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                <integer key="value" value="5"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                <integer key="value" value="3"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isRoundedCorners" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="didTapRegister:" destination="NQB-Ms-CYk" eventType="touchUpInside" id="PNN-J4-FJk"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="C4N-qM-z8g" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="68" width="382" height="56"/>
                                        <accessibility key="accessibilityConfiguration" identifier="pmios_initial_login_button"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="M0J-kF-l7Z"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Log in"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                <integer key="value" value="6"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                <integer key="value" value="3"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isRoundedCorners" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="didTapLogin:" destination="NQB-Ms-CYk" eventType="touchUpInside" id="6LU-Pe-kn9"/>
                                        </connections>
                                    </button>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ycm-d9-hZ7">
                                        <rect key="frame" x="0.0" y="136" width="382" height="12"/>
                                        <color key="backgroundColor" name="BackgroundPrimaryBlack"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="12" id="gPi-Vf-kb8"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Regulated by the Bangko Sentral ng Pilipinas. Deposits are insured by PDIC up to P1,000,000 per depositor." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NIo-P5-FPr">
                                        <rect key="frame" x="0.0" y="160" width="382" height="29.333333333333343"/>
                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="12"/>
                                        <color key="textColor" name="ContentGrey4"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label hidden="YES" opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Device ID: ---" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" id="jiB-Y0-MxJ">
                                        <rect key="frame" x="0.0" y="189.33333333333334" width="382" height="0.0"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="12"/>
                                        <color key="textColor" name="ContentGrey4"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="tP1-dI-ks3"/>
                        <color key="backgroundColor" name="BackgroundPrimaryBlack"/>
                        <constraints>
                            <constraint firstItem="tP1-dI-ks3" firstAttribute="trailing" secondItem="wV2-lu-1z8" secondAttribute="trailing" constant="24" id="BDy-nr-G00"/>
                            <constraint firstItem="tP1-dI-ks3" firstAttribute="bottom" secondItem="wV2-lu-1z8" secondAttribute="bottom" constant="24" id="N9q-Ua-XW6"/>
                            <constraint firstItem="wV2-lu-1z8" firstAttribute="leading" secondItem="tP1-dI-ks3" secondAttribute="leading" constant="24" id="boU-9b-S4Z"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="deviceIDLabel" destination="jiB-Y0-MxJ" id="BxY-Sb-uXG"/>
                        <outlet property="footerLabel" destination="NIo-P5-FPr" id="3VU-RE-gDK"/>
                        <outlet property="registerButton" destination="4FU-Bj-QHY" id="UN2-Zk-oIQ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="meo-tz-jd9" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1447" y="2683"/>
        </scene>
    </scenes>
    <resources>
        <image name="iconMayaLogoPlainGreen" width="212" height="61"/>
        <image name="imageMayaLogoTopBlackDescription" width="194" height="79"/>
        <namedColor name="BackgroundPrimaryBlack">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentGrey4">
            <color red="0.69411764705882351" green="0.70196078431372544" blue="0.70980392156862748" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentPrimaryGreen">
            <color red="0.0" green="0.65098039215686276" blue="0.31764705882352939" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey6">
            <color red="0.33333333333333331" green="0.33725490196078434" blue="0.34901960784313724" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="OpacityGreen25">
            <color red="0.11372549019607843" green="0.7686274509803922" blue="0.22352941176470589" alpha="0.25" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryBlack">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
