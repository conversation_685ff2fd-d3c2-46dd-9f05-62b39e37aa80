<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina5_9" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="collection view cell content view" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="CerebriSansPro-Book.otf">
            <string>CerebriSansPro-Book</string>
        </array>
        <array key="CerebriSansPro-Medium.otf">
            <string>CerebriSansPro-Medium</string>
        </array>
        <array key="CerebriSansPro-Regular.otf">
            <string>CerebriSansPro-Regular</string>
        </array>
        <array key="Jeko-Bold.otf">
            <string>Jeko-Bold</string>
        </array>
        <array key="Jeko-Regular.otf">
            <string>Jeko-Regular</string>
        </array>
        <array key="Jeko-SemiBold.otf">
            <string>Jeko-SemiBold</string>
        </array>
        <array key="WorkSans-Bold.ttf">
            <string>WorkSans-Bold</string>
        </array>
        <array key="WorkSans-Medium.ttf">
            <string>WorkSans-Medium</string>
        </array>
    </customFonts>
    <scenes>
        <!--Maya Dashboard Menu Pager View Controller-->
        <scene sceneID="sdp-Nm-4bQ">
            <objects>
                <viewController storyboardIdentifier="MayaDashboardMenuPagerViewController" extendedLayoutIncludesOpaqueBars="YES" definesPresentationContext="YES" id="nLZ-wz-4TR" customClass="MayaDashboardMenuPagerViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="skK-Tq-oqp"/>
                        <viewControllerLayoutGuide type="bottom" id="kFk-yi-mdc"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="EW4-WQ-kIx">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ATj-li-NSS" customClass="MayaMenuBar" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="110" width="375" height="34"/>
                                <color key="backgroundColor" name="Grey1"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="34" id="nOd-XC-are"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="color" keyPath="accentBackgroundColor">
                                        <color key="value" name="MenuPrimaryBlack"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="accentColor">
                                        <color key="value" name="MenuPrimaryWhite"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <outlet property="delegate" destination="nLZ-wz-4TR" id="8Gv-m3-WXV"/>
                                </connections>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="HHf-5x-5Cf">
                                <rect key="frame" x="0.0" y="168" width="375" height="644"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Dmm-AR-yLX"/>
                        <color key="backgroundColor" name="Grey1"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="ATj-li-NSS" secondAttribute="trailing" id="5Ip-E3-ZIa"/>
                            <constraint firstItem="HHf-5x-5Cf" firstAttribute="leading" secondItem="EW4-WQ-kIx" secondAttribute="leading" id="Go7-eH-9tW"/>
                            <constraint firstItem="ATj-li-NSS" firstAttribute="top" secondItem="Dmm-AR-yLX" secondAttribute="top" constant="16" id="HIR-aR-fs3"/>
                            <constraint firstAttribute="bottom" secondItem="HHf-5x-5Cf" secondAttribute="bottom" id="apb-E3-cA5"/>
                            <constraint firstItem="ATj-li-NSS" firstAttribute="leading" secondItem="EW4-WQ-kIx" secondAttribute="leading" id="mmW-EQ-3Wq"/>
                            <constraint firstAttribute="trailing" secondItem="HHf-5x-5Cf" secondAttribute="trailing" id="rZU-sB-InZ"/>
                            <constraint firstItem="HHf-5x-5Cf" firstAttribute="top" secondItem="ATj-li-NSS" secondAttribute="bottom" constant="24" id="zQo-lc-LF8"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="jhU-Zr-iUy"/>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
                    <connections>
                        <outlet property="containerView" destination="HHf-5x-5Cf" id="Itl-Mw-lUL"/>
                        <outlet property="inboxNotificationIconView" destination="rHc-b0-8b1" id="S3U-jd-Vtm"/>
                        <outlet property="inboxNotificationUnreadIndicator" destination="108-Tx-fCb" id="HkU-mB-lkZ"/>
                        <outlet property="inboxWithNotification" destination="CUm-tz-Ybf" id="QEX-VV-dGP"/>
                        <outlet property="inboxWithoutNotification" destination="C5M-FU-w9x" id="GbA-q4-WOK"/>
                        <outlet property="menuBar" destination="ATj-li-NSS" id="KTB-0h-lyR"/>
                        <outlet property="profile" destination="I1Z-8f-tVJ" id="2wa-gT-ahl"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="7zm-OO-XKt" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
                <view contentMode="scaleToFill" id="C5M-FU-w9x" userLabel="InboxWithoutNotifView">
                    <rect key="frame" x="0.0" y="0.0" width="69" height="29"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconInboxV2" translatesAutoresizingMaskIntoConstraints="NO" id="s0i-FG-Tap">
                            <rect key="frame" x="52" y="11" width="19" height="21"/>
                            <gestureRecognizers/>
                            <constraints>
                                <constraint firstAttribute="width" constant="19" id="7vu-Dz-Nbr"/>
                                <constraint firstAttribute="height" constant="21" id="Rk7-d5-3og"/>
                            </constraints>
                        </imageView>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconInboxSupport" translatesAutoresizingMaskIntoConstraints="NO" id="K87-R2-lv1">
                            <rect key="frame" x="0.0" y="9.6666666666666643" width="24" height="24"/>
                            <gestureRecognizers/>
                            <constraints>
                                <constraint firstAttribute="width" constant="24" id="6eY-MG-WEO"/>
                                <constraint firstAttribute="height" constant="24" id="wPb-2D-ZlC"/>
                            </constraints>
                        </imageView>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6Qy-Oa-wRt" userLabel="InboxSupportTapArea">
                            <rect key="frame" x="0.0" y="0.0" width="38" height="32"/>
                            <accessibility key="accessibilityConfiguration">
                                <accessibilityTraits key="traits" button="YES"/>
                            </accessibility>
                            <gestureRecognizers/>
                            <connections>
                                <outletCollection property="gestureRecognizers" destination="6na-Kh-drN" appends="YES" id="scX-NM-Jal"/>
                            </connections>
                        </view>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="RA6-ma-qAR" userLabel="InboxWithoutNotifTapArea">
                            <rect key="frame" x="38" y="0.0" width="45" height="32"/>
                            <accessibility key="accessibilityConfiguration">
                                <accessibilityTraits key="traits" button="YES"/>
                            </accessibility>
                            <gestureRecognizers/>
                            <connections>
                                <outletCollection property="gestureRecognizers" destination="r80-zF-tRG" appends="YES" id="bfq-rJ-C06"/>
                            </connections>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <gestureRecognizers/>
                    <constraints>
                        <constraint firstItem="6Qy-Oa-wRt" firstAttribute="top" secondItem="C5M-FU-w9x" secondAttribute="top" id="4rn-Yk-IsU"/>
                        <constraint firstAttribute="trailing" secondItem="s0i-FG-Tap" secondAttribute="trailing" constant="12" id="5fz-IA-epm"/>
                        <constraint firstItem="s0i-FG-Tap" firstAttribute="top" secondItem="C5M-FU-w9x" secondAttribute="top" constant="11" id="757-3L-iBp"/>
                        <constraint firstItem="K87-R2-lv1" firstAttribute="top" secondItem="C5M-FU-w9x" secondAttribute="top" constant="9.5" id="8U6-7B-dxD"/>
                        <constraint firstItem="RA6-ma-qAR" firstAttribute="leading" secondItem="K87-R2-lv1" secondAttribute="trailing" constant="14" id="CcN-kq-nqW"/>
                        <constraint firstItem="s0i-FG-Tap" firstAttribute="leading" secondItem="C5M-FU-w9x" secondAttribute="leading" constant="52" id="DiA-qD-jTY"/>
                        <constraint firstAttribute="bottom" secondItem="RA6-ma-qAR" secondAttribute="bottom" id="GDI-3K-Vpg"/>
                        <constraint firstItem="RA6-ma-qAR" firstAttribute="leading" secondItem="C5M-FU-w9x" secondAttribute="leading" constant="38" id="Gx2-qe-Mzs"/>
                        <constraint firstItem="6Qy-Oa-wRt" firstAttribute="leading" secondItem="C5M-FU-w9x" secondAttribute="leading" id="HHm-gO-SHH"/>
                        <constraint firstAttribute="trailing" secondItem="K87-R2-lv1" secondAttribute="trailing" constant="59" id="JTF-Im-2d6"/>
                        <constraint firstItem="K87-R2-lv1" firstAttribute="leading" secondItem="C5M-FU-w9x" secondAttribute="leading" id="NZR-Uy-iQa"/>
                        <constraint firstItem="RA6-ma-qAR" firstAttribute="top" secondItem="C5M-FU-w9x" secondAttribute="top" id="SSb-VP-nd8"/>
                        <constraint firstItem="s0i-FG-Tap" firstAttribute="leading" secondItem="K87-R2-lv1" secondAttribute="trailing" constant="28" id="c0p-qa-OVU"/>
                        <constraint firstAttribute="trailing" secondItem="RA6-ma-qAR" secondAttribute="trailing" id="gyr-oI-XrZ"/>
                        <constraint firstAttribute="bottom" secondItem="6Qy-Oa-wRt" secondAttribute="bottom" id="qVw-6j-QoP"/>
                        <constraint firstItem="s0i-FG-Tap" firstAttribute="leading" secondItem="6Qy-Oa-wRt" secondAttribute="trailing" constant="14" id="sLH-Xi-kG7"/>
                        <constraint firstAttribute="bottom" secondItem="K87-R2-lv1" secondAttribute="bottom" constant="-1.5" id="u8l-Fl-3Ri"/>
                        <constraint firstAttribute="bottom" secondItem="s0i-FG-Tap" secondAttribute="bottom" id="xa4-6I-zh2"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" id="rHc-b0-8b1" userLabel="InboxWithNotifView">
                    <rect key="frame" x="0.0" y="0.0" width="79" height="29"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconInboxV2" translatesAutoresizingMaskIntoConstraints="NO" id="2RC-Ew-6sh">
                            <rect key="frame" x="52" y="11" width="19" height="21"/>
                            <gestureRecognizers/>
                            <constraints>
                                <constraint firstAttribute="width" constant="19" id="b97-xM-ErV"/>
                                <constraint firstAttribute="height" constant="21" id="dOm-yk-IvE"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="8" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="108-Tx-fCb" customClass="MayaInboxNotificationUnreadIndicator" customModule="PayMaya" customModuleProvider="target">
                            <rect key="frame" x="67.333333333333329" y="3.666666666666667" width="7.6666666666666714" height="14.666666666666664"/>
                            <color key="backgroundColor" name="ButtonPrimaryGreen"/>
                            <gestureRecognizers/>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                    <color key="value" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="topInset">
                                    <real key="value" value="4"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="bottomInset">
                                    <real key="value" value="4"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                    <real key="value" value="11.33"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                    <real key="value" value="2"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                    <real key="value" value="11.33"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconInboxSupport" translatesAutoresizingMaskIntoConstraints="NO" id="mxE-RT-i8X">
                            <rect key="frame" x="0.0" y="9.6666666666666643" width="24" height="24"/>
                            <gestureRecognizers/>
                            <constraints>
                                <constraint firstAttribute="width" constant="24" id="Fdg-PF-jjo"/>
                                <constraint firstAttribute="height" constant="24" id="Uw4-nS-rCa"/>
                            </constraints>
                        </imageView>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="PYN-Bu-3Te" userLabel="InboxWithNotifTapArea">
                            <rect key="frame" x="38" y="0.0" width="41" height="29"/>
                            <accessibility key="accessibilityConfiguration">
                                <accessibilityTraits key="traits" button="YES"/>
                            </accessibility>
                            <gestureRecognizers/>
                            <connections>
                                <outletCollection property="gestureRecognizers" destination="7dR-5Y-8eY" appends="YES" id="BEU-kW-x4O"/>
                            </connections>
                        </view>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rmG-si-kYZ" userLabel="InboxSupportTapArea">
                            <rect key="frame" x="0.0" y="0.0" width="38" height="29"/>
                            <accessibility key="accessibilityConfiguration">
                                <accessibilityTraits key="traits" button="YES"/>
                            </accessibility>
                            <gestureRecognizers/>
                            <connections>
                                <outletCollection property="gestureRecognizers" destination="cNu-Gm-pZm" appends="YES" id="P5T-0N-nIz"/>
                            </connections>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <gestureRecognizers/>
                    <constraints>
                        <constraint firstItem="2RC-Ew-6sh" firstAttribute="top" secondItem="rHc-b0-8b1" secondAttribute="top" constant="11" id="3aw-l3-ihw"/>
                        <constraint firstItem="2RC-Ew-6sh" firstAttribute="leading" secondItem="mxE-RT-i8X" secondAttribute="trailing" constant="28" id="69R-cv-msv"/>
                        <constraint firstItem="PYN-Bu-3Te" firstAttribute="leading" secondItem="mxE-RT-i8X" secondAttribute="trailing" constant="14" id="8Cp-Rq-FIO"/>
                        <constraint firstItem="2RC-Ew-6sh" firstAttribute="leading" secondItem="rmG-si-kYZ" secondAttribute="trailing" constant="14" id="Hb1-lC-NBG"/>
                        <constraint firstAttribute="bottom" secondItem="2RC-Ew-6sh" secondAttribute="bottom" id="J0y-Yp-PCx"/>
                        <constraint firstItem="108-Tx-fCb" firstAttribute="centerX" secondItem="2RC-Ew-6sh" secondAttribute="trailing" id="K15-SY-SEw"/>
                        <constraint firstItem="rmG-si-kYZ" firstAttribute="top" secondItem="rHc-b0-8b1" secondAttribute="top" id="RJj-dl-3Pn"/>
                        <constraint firstItem="PYN-Bu-3Te" firstAttribute="top" secondItem="rHc-b0-8b1" secondAttribute="top" id="Tcu-j5-btq"/>
                        <constraint firstItem="PYN-Bu-3Te" firstAttribute="leading" secondItem="rHc-b0-8b1" secondAttribute="leading" constant="38" id="WMR-FO-foi"/>
                        <constraint firstAttribute="trailing" secondItem="2RC-Ew-6sh" secondAttribute="trailing" constant="12" id="Yj8-HI-KV7"/>
                        <constraint firstAttribute="trailing" secondItem="PYN-Bu-3Te" secondAttribute="trailing" id="a97-0Z-6uA"/>
                        <constraint firstItem="rmG-si-kYZ" firstAttribute="leading" secondItem="rHc-b0-8b1" secondAttribute="leading" id="eZl-Ay-dwC"/>
                        <constraint firstAttribute="bottom" secondItem="PYN-Bu-3Te" secondAttribute="bottom" id="gBv-la-e9c"/>
                        <constraint firstItem="mxE-RT-i8X" firstAttribute="leading" secondItem="rHc-b0-8b1" secondAttribute="leading" id="gDf-wz-YP0"/>
                        <constraint firstItem="2RC-Ew-6sh" firstAttribute="leading" secondItem="rHc-b0-8b1" secondAttribute="leading" constant="52" id="igM-S3-Rbq"/>
                        <constraint firstItem="108-Tx-fCb" firstAttribute="centerY" secondItem="2RC-Ew-6sh" secondAttribute="top" id="jQh-q2-oN7"/>
                        <constraint firstAttribute="bottom" secondItem="rmG-si-kYZ" secondAttribute="bottom" id="kG7-wJ-JZM"/>
                        <constraint firstAttribute="bottom" secondItem="mxE-RT-i8X" secondAttribute="bottom" constant="-1.5" id="m6A-hV-vZD"/>
                        <constraint firstItem="mxE-RT-i8X" firstAttribute="top" secondItem="rHc-b0-8b1" secondAttribute="top" constant="9.5" id="nzI-tm-8gD"/>
                        <constraint firstAttribute="trailing" secondItem="mxE-RT-i8X" secondAttribute="trailing" constant="59" id="zaC-1z-vn3"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" ambiguous="YES" insetsLayoutMarginsFromSafeArea="NO" id="CUm-tz-Ybf">
                    <rect key="frame" x="0.0" y="0.0" width="33.5" height="34.666666666666664"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconInbox" translatesAutoresizingMaskIntoConstraints="NO" id="SVQ-cb-vtA">
                            <rect key="frame" x="0.0" y="14.000000000000002" width="19.666666666666668" height="20.666666666666671"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="20.5" id="cC3-7r-Nwo"/>
                                <constraint firstAttribute="width" constant="19.5" id="oFW-s1-Qdo"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="99+" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4Hd-bQ-xnl" customClass="PaddingLabel" customModule="PayMaya" customModuleProvider="target">
                            <rect key="frame" x="6" y="6" width="23" height="15"/>
                            <color key="backgroundColor" name="ButtonPrimaryGreen"/>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconGreenDot" translatesAutoresizingMaskIntoConstraints="NO" id="tUg-Ab-kqr">
                            <rect key="frame" x="10" y="8" width="16" height="16"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="16" id="HnK-9o-Ce7"/>
                                <constraint firstAttribute="height" constant="16" id="rGg-9W-tO8"/>
                            </constraints>
                        </imageView>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <gestureRecognizers/>
                    <constraints>
                        <constraint firstItem="tUg-Ab-kqr" firstAttribute="top" secondItem="CUm-tz-Ybf" secondAttribute="top" constant="8" id="1XP-Hd-hcX"/>
                        <constraint firstAttribute="bottom" secondItem="SVQ-cb-vtA" secondAttribute="bottom" id="7Br-dL-xcY"/>
                        <constraint firstAttribute="trailing" secondItem="4Hd-bQ-xnl" secondAttribute="trailing" constant="5" id="E4N-f2-BH0"/>
                        <constraint firstItem="SVQ-cb-vtA" firstAttribute="top" secondItem="tUg-Ab-kqr" secondAttribute="bottom" constant="-10" id="WmB-Sk-KJ2"/>
                        <constraint firstItem="SVQ-cb-vtA" firstAttribute="leading" secondItem="CUm-tz-Ybf" secondAttribute="leading" id="h2l-PZ-ltV"/>
                        <constraint firstItem="SVQ-cb-vtA" firstAttribute="leading" secondItem="tUg-Ab-kqr" secondAttribute="leading" constant="-10" id="iTP-wY-RdV"/>
                        <constraint firstAttribute="trailing" secondItem="tUg-Ab-kqr" secondAttribute="trailing" constant="8" id="qId-J9-0Mm"/>
                    </constraints>
                    <variation key="default">
                        <mask key="subviews">
                            <exclude reference="4Hd-bQ-xnl"/>
                        </mask>
                        <mask key="constraints">
                            <exclude reference="E4N-f2-BH0"/>
                        </mask>
                    </variation>
                    <connections>
                        <outletCollection property="gestureRecognizers" destination="JUU-QQ-eFy" appends="YES" id="CrF-Pd-2qz"/>
                    </connections>
                </view>
                <tapGestureRecognizer id="Cy1-AY-rwc">
                    <connections>
                        <action selector="didTapProfileWithSender:" destination="nLZ-wz-4TR" id="H4z-wx-PCo"/>
                    </connections>
                </tapGestureRecognizer>
                <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="I1Z-8f-tVJ">
                    <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="iconProfilePlaceholder" translatesAutoresizingMaskIntoConstraints="NO" id="nOo-rk-AXX">
                            <rect key="frame" x="8" y="8" width="32" height="32"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="32" id="Cy4-fH-oW8"/>
                                <constraint firstAttribute="height" constant="32" id="FYk-Py-hp4"/>
                            </constraints>
                        </imageView>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <gestureRecognizers/>
                    <constraints>
                        <constraint firstItem="nOo-rk-AXX" firstAttribute="leading" secondItem="I1Z-8f-tVJ" secondAttribute="leading" constant="8" id="F31-8B-iHd"/>
                        <constraint firstItem="nOo-rk-AXX" firstAttribute="top" secondItem="I1Z-8f-tVJ" secondAttribute="top" constant="8" id="HcX-U7-gmr"/>
                        <constraint firstAttribute="bottom" secondItem="nOo-rk-AXX" secondAttribute="bottom" id="RgD-Ex-7N7"/>
                        <constraint firstAttribute="trailing" secondItem="nOo-rk-AXX" secondAttribute="trailing" id="wxF-sA-8Zu"/>
                    </constraints>
                    <connections>
                        <outletCollection property="gestureRecognizers" destination="Cy1-AY-rwc" appends="YES" id="Fq2-Z9-w5x"/>
                    </connections>
                </view>
                <tapGestureRecognizer id="cNu-Gm-pZm" userLabel="TapInboxSupportWithNotif">
                    <connections>
                        <action selector="didTapInboxSupportWithSender:" destination="nLZ-wz-4TR" id="PVV-nL-cQa"/>
                    </connections>
                </tapGestureRecognizer>
                <tapGestureRecognizer id="7dR-5Y-8eY" userLabel="TapInboxIconWithNotif">
                    <connections>
                        <action selector="didTapInboxNotificationWithSender:" destination="nLZ-wz-4TR" id="dJF-ma-vtc"/>
                    </connections>
                </tapGestureRecognizer>
                <tapGestureRecognizer id="r80-zF-tRG" userLabel="TapInboxIconWithoutNotif">
                    <connections>
                        <action selector="didTapInboxNotificationWithSender:" destination="nLZ-wz-4TR" id="NGT-vn-TPn"/>
                    </connections>
                </tapGestureRecognizer>
                <tapGestureRecognizer id="6na-Kh-drN" userLabel="TapInboxSupportWithoutNotif">
                    <connections>
                        <action selector="didTapInboxSupportWithSender:" destination="nLZ-wz-4TR" id="x8H-Er-bLO"/>
                    </connections>
                </tapGestureRecognizer>
                <tapGestureRecognizer id="JUU-QQ-eFy">
                    <connections>
                        <action selector="didTapinboxIconView:" destination="nLZ-wz-4TR" id="ZwY-Dh-pFr"/>
                    </connections>
                </tapGestureRecognizer>
            </objects>
            <point key="canvasLocation" x="-255" y="745"/>
        </scene>
        <!--Maya Dashboard View Controller-->
        <scene sceneID="IlL-7l-ybf">
            <objects>
                <viewController storyboardIdentifier="MayaDashboardViewController" modalTransitionStyle="crossDissolve" modalPresentationStyle="overFullScreen" id="BAm-KB-B6s" customClass="MayaDashboardViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="G89-MG-FrU">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="1300"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView multipleTouchEnabled="YES" contentMode="scaleToFill" ambiguous="YES" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" contentInsetAdjustmentBehavior="never" bouncesZoom="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fG5-VF-2Km">
                                <rect key="frame" x="0.0" y="0.0" width="320" height="1300"/>
                                <subviews>
                                    <view contentMode="scaleToFill" ambiguous="YES" placeholderIntrinsicWidth="infinite" placeholderIntrinsicHeight="766" translatesAutoresizingMaskIntoConstraints="NO" id="mAm-Ix-fKD">
                                        <rect key="frame" x="0.0" y="0.0" width="320" height="1566"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" alignment="center" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="2QD-OA-STw">
                                                <rect key="frame" x="0.0" y="0.0" width="320" height="1946"/>
                                                <subviews>
                                                    <view hidden="YES" clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jTr-1H-csA" customClass="MayaSnackBarView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="24" y="0.0" width="272" height="0.0"/>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" alignment="center" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="drp-vb-Y2y">
                                                        <rect key="frame" x="0.0" y="0.0" width="320" height="1946"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="seR-Dh-twY">
                                                                <rect key="frame" x="24" y="0.0" width="272" height="410"/>
                                                                <subviews>
                                                                    <containerView opaque="NO" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Rsp-nH-See">
                                                                        <rect key="frame" x="0.0" y="0.0" width="272" height="154"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" priority="250" constant="154" id="KZN-p9-pPB"/>
                                                                        </constraints>
                                                                    </containerView>
                                                                    <containerView opaque="NO" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="b44-ro-gYJ">
                                                                        <rect key="frame" x="0.0" y="154" width="272" height="128"/>
                                                                    </containerView>
                                                                    <containerView opaque="NO" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="EAO-Z5-QeN">
                                                                        <rect key="frame" x="0.0" y="282" width="272" height="128"/>
                                                                    </containerView>
                                                                </subviews>
                                                            </stackView>
                                                            <containerView opaque="NO" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="cWH-pl-7fs">
                                                                <rect key="frame" x="0.0" y="434" width="320" height="218"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="218" id="9re-Vh-bS9"/>
                                                                </constraints>
                                                            </containerView>
                                                            <view opaque="NO" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="V9a-0d-ea2">
                                                                <rect key="frame" x="0.0" y="676" width="320" height="100"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="100" id="j7V-jd-etM"/>
                                                                </constraints>
                                                            </view>
                                                            <containerView opaque="NO" contentMode="scaleToFill" ambiguous="YES" placeholderIntrinsicWidth="infinite" placeholderIntrinsicHeight="148" translatesAutoresizingMaskIntoConstraints="NO" id="CXd-ZR-RbB">
                                                                <rect key="frame" x="0.0" y="800" width="320" height="148"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="148" id="iH3-ef-gEd"/>
                                                                </constraints>
                                                            </containerView>
                                                            <containerView opaque="NO" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="mFz-YK-drx">
                                                                <rect key="frame" x="24" y="972" width="272" height="412"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="412" id="CBg-On-Mhj"/>
                                                                </constraints>
                                                            </containerView>
                                                            <containerView opaque="NO" contentMode="scaleToFill" ambiguous="YES" placeholderIntrinsicWidth="infinite" placeholderIntrinsicHeight="200" translatesAutoresizingMaskIntoConstraints="NO" id="fKK-Aw-8WO">
                                                                <rect key="frame" x="24" y="1408" width="272" height="200"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="200" id="f5p-tV-hgk"/>
                                                                </constraints>
                                                            </containerView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="6w5-wS-QTq" customClass="MayaBSPView" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="40" y="1632" width="240" height="200"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            </stackView>
                                                            <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="734-6D-dA1" userLabel="Spacer View">
                                                                <rect key="frame" x="24" y="1856" width="272" height="90"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="90" id="NR8-Cm-y81"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstItem="fKK-Aw-8WO" firstAttribute="leading" secondItem="drp-vb-Y2y" secondAttribute="leading" constant="24" id="0d6-jU-y9i"/>
                                                            <constraint firstAttribute="trailing" secondItem="cWH-pl-7fs" secondAttribute="trailing" id="3X8-UN-rgA"/>
                                                            <constraint firstAttribute="trailing" secondItem="CXd-ZR-RbB" secondAttribute="trailing" id="6Ec-Rz-8zR"/>
                                                            <constraint firstItem="CXd-ZR-RbB" firstAttribute="leading" secondItem="drp-vb-Y2y" secondAttribute="leading" id="6zy-iH-bKo"/>
                                                            <constraint firstItem="cWH-pl-7fs" firstAttribute="leading" secondItem="drp-vb-Y2y" secondAttribute="leading" id="DaW-rE-ML9"/>
                                                            <constraint firstItem="mFz-YK-drx" firstAttribute="leading" secondItem="drp-vb-Y2y" secondAttribute="leading" constant="24" id="KS7-tm-dyt"/>
                                                            <constraint firstItem="6w5-wS-QTq" firstAttribute="leading" secondItem="fKK-Aw-8WO" secondAttribute="leading" constant="16" id="Vf7-Bc-Cgu"/>
                                                            <constraint firstAttribute="trailing" secondItem="V9a-0d-ea2" secondAttribute="trailing" id="Xah-zH-G7o"/>
                                                            <constraint firstItem="734-6D-dA1" firstAttribute="leading" secondItem="drp-vb-Y2y" secondAttribute="leading" constant="24" id="gA1-ar-QgA"/>
                                                            <constraint firstItem="6w5-wS-QTq" firstAttribute="trailing" secondItem="fKK-Aw-8WO" secondAttribute="trailing" constant="-16" id="iq6-Ar-63c"/>
                                                            <constraint firstAttribute="trailing" secondItem="734-6D-dA1" secondAttribute="trailing" constant="24" id="k2E-C6-Kjo"/>
                                                            <constraint firstAttribute="trailing" secondItem="fKK-Aw-8WO" secondAttribute="trailing" constant="24" id="koY-KQ-u7N"/>
                                                            <constraint firstItem="V9a-0d-ea2" firstAttribute="leading" secondItem="drp-vb-Y2y" secondAttribute="leading" id="nOM-if-w19"/>
                                                            <constraint firstAttribute="trailing" secondItem="seR-Dh-twY" secondAttribute="trailing" constant="24" id="sas-Br-OLc"/>
                                                            <constraint firstItem="seR-Dh-twY" firstAttribute="leading" secondItem="drp-vb-Y2y" secondAttribute="leading" constant="24" id="toh-N4-4zb"/>
                                                            <constraint firstAttribute="trailing" secondItem="mFz-YK-drx" secondAttribute="trailing" constant="24" id="tps-iY-u55"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="jTr-1H-csA" secondAttribute="trailing" constant="24" id="9V5-Fy-e3a"/>
                                                    <constraint firstItem="drp-vb-Y2y" firstAttribute="leading" secondItem="2QD-OA-STw" secondAttribute="leading" id="jhC-tQ-ijl"/>
                                                    <constraint firstItem="jTr-1H-csA" firstAttribute="leading" secondItem="2QD-OA-STw" secondAttribute="leading" constant="24" id="sRO-gd-6L4"/>
                                                    <constraint firstAttribute="trailing" secondItem="drp-vb-Y2y" secondAttribute="trailing" id="wug-Z2-5D9"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="2QD-OA-STw" firstAttribute="leading" secondItem="mAm-Ix-fKD" secondAttribute="leading" id="0Lm-AJ-MJP"/>
                                            <constraint firstItem="2QD-OA-STw" firstAttribute="top" secondItem="mAm-Ix-fKD" secondAttribute="top" id="3D5-k1-gZz"/>
                                            <constraint firstAttribute="bottom" secondItem="2QD-OA-STw" secondAttribute="bottom" id="PYM-nl-RcI"/>
                                            <constraint firstAttribute="trailing" secondItem="2QD-OA-STw" secondAttribute="trailing" id="PlX-Tb-fHZ"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5Qq-3W-Eic" customClass="MayaRefreshControl" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="-46" width="320" height="46"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="46" id="3QH-F4-j9w"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="mAm-Ix-fKD" secondAttribute="bottom" id="2S1-sa-0wq"/>
                                    <constraint firstAttribute="trailing" secondItem="5Qq-3W-Eic" secondAttribute="trailing" id="CqT-nw-qk7"/>
                                    <constraint firstItem="mAm-Ix-fKD" firstAttribute="top" secondItem="fG5-VF-2Km" secondAttribute="top" id="Dmw-au-quL"/>
                                    <constraint firstItem="mAm-Ix-fKD" firstAttribute="width" secondItem="fG5-VF-2Km" secondAttribute="width" id="IQc-f3-p2T"/>
                                    <constraint firstAttribute="trailing" secondItem="mAm-Ix-fKD" secondAttribute="trailing" id="UTp-mE-bnh"/>
                                    <constraint firstItem="5Qq-3W-Eic" firstAttribute="width" secondItem="fG5-VF-2Km" secondAttribute="width" id="cx1-BK-y1U"/>
                                    <constraint firstItem="5Qq-3W-Eic" firstAttribute="leading" secondItem="fG5-VF-2Km" secondAttribute="leading" id="dBd-nX-Pyp"/>
                                    <constraint firstItem="mAm-Ix-fKD" firstAttribute="top" secondItem="5Qq-3W-Eic" secondAttribute="bottom" id="eJj-l1-S79"/>
                                    <constraint firstItem="mAm-Ix-fKD" firstAttribute="leading" secondItem="fG5-VF-2Km" secondAttribute="leading" id="q0O-kd-Vf4"/>
                                </constraints>
                                <connections>
                                    <outlet property="delegate" destination="BAm-KB-B6s" id="RAu-Sq-j2t"/>
                                </connections>
                            </scrollView>
                            <containerView hidden="YES" opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="SqX-iQ-sJm" customClass="MayaDefaultErrorView" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="320" height="1193"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="color" keyPath="contentBackgroundColor">
                                        <color key="value" name="Grey1"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </containerView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="z9D-6M-cVF"/>
                        <color key="backgroundColor" name="Grey1"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="fG5-VF-2Km" secondAttribute="trailing" id="49M-61-MjU"/>
                            <constraint firstAttribute="top" secondItem="fG5-VF-2Km" secondAttribute="top" id="8KW-b3-URt"/>
                            <constraint firstItem="SqX-iQ-sJm" firstAttribute="top" secondItem="G89-MG-FrU" secondAttribute="top" id="Yll-e2-T04"/>
                            <constraint firstItem="fG5-VF-2Km" firstAttribute="bottom" secondItem="G89-MG-FrU" secondAttribute="bottom" id="awL-oz-pF7"/>
                            <constraint firstItem="SqX-iQ-sJm" firstAttribute="bottom" secondItem="G89-MG-FrU" secondAttribute="bottom" constant="-107" id="eOa-Uz-QT9"/>
                            <constraint firstItem="SqX-iQ-sJm" firstAttribute="leading" secondItem="G89-MG-FrU" secondAttribute="leading" id="fn2-Ub-0hp"/>
                            <constraint firstItem="fG5-VF-2Km" firstAttribute="leading" secondItem="G89-MG-FrU" secondAttribute="leading" id="ys7-94-tfv"/>
                            <constraint firstItem="SqX-iQ-sJm" firstAttribute="trailing" secondItem="G89-MG-FrU" secondAttribute="trailing" id="ytW-pQ-O2s"/>
                        </constraints>
                    </view>
                    <nil key="simulatedTopBarMetrics"/>
                    <nil key="simulatedBottomBarMetrics"/>
                    <size key="freeformSize" width="320" height="1300"/>
                    <connections>
                        <outlet property="activityContainerViewHeightConstraint" destination="CBg-On-Mhj" id="wRA-ii-UVd"/>
                        <outlet property="adCarouselView" destination="CXd-ZR-RbB" id="BKk-eu-luG"/>
                        <outlet property="adCarouselViewHeightConstraint" destination="iH3-ef-gEd" id="tsL-l2-LCE"/>
                        <outlet property="alertSnackBar" destination="jTr-1H-csA" id="VBg-pX-ORZ"/>
                        <outlet property="bspFooterView" destination="6w5-wS-QTq" id="bcK-49-eGb"/>
                        <outlet property="dashboardStackView" destination="drp-vb-Y2y" id="a4z-qI-OAh"/>
                        <outlet property="easyCreditApplicationView" destination="EAO-Z5-QeN" id="9Fv-vT-NDD"/>
                        <outlet property="easyCreditView" destination="b44-ro-gYJ" id="wXq-K8-gsi"/>
                        <outlet property="googleAdCarouselView" destination="V9a-0d-ea2" id="sRM-wF-2c1"/>
                        <outlet property="latestActivitiesView" destination="mFz-YK-drx" id="Zfc-vh-Lr6"/>
                        <outlet property="mainScrollView" destination="fG5-VF-2Km" id="pZx-J6-vnw"/>
                        <outlet property="mainStackView" destination="2QD-OA-STw" id="Hl7-Du-Mm5"/>
                        <outlet property="refreshControl" destination="5Qq-3W-Eic" id="Kpp-zB-j2B"/>
                        <outlet property="rewardsView" destination="fKK-Aw-8WO" id="fxU-sG-ZcD"/>
                        <outlet property="rewardsViewHeightConstraint" destination="f5p-tV-hgk" id="fxa-Eh-Ut6"/>
                        <outlet property="serverErrorView" destination="SqX-iQ-sJm" id="lH1-Zi-J1H"/>
                        <outlet property="tilesView" destination="cWH-pl-7fs" id="0HN-O4-LX2"/>
                        <outlet property="walletBalanceHeightConstraint" destination="KZN-p9-pPB" id="Xt4-qf-8bW"/>
                        <outlet property="walletBalanceStackView" destination="seR-Dh-twY" id="bcH-cP-8xg"/>
                        <outlet property="walletBalanceView" destination="Rsp-nH-See" id="jTv-iG-6VQ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="taP-gb-6ft" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="473" y="358"/>
        </scene>
        <!--Maya Savings View Controller-->
        <scene sceneID="JaB-cm-tSg">
            <objects>
                <viewController storyboardIdentifier="MayaSavingsViewController" modalTransitionStyle="crossDissolve" modalPresentationStyle="overFullScreen" id="5p6-2f-gfG" customClass="MayaSavingsViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="ExZ-at-nm9">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="pE0-b2-tGC"/>
                        <color key="backgroundColor" name="BackgroundPrimaryWhite"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="h3V-ri-zfQ" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1254" y="745"/>
        </scene>
        <!--Services-->
        <scene sceneID="hiE-4K-u5f">
            <objects>
                <viewController storyboardIdentifier="MayaServicesViewController" modalTransitionStyle="crossDissolve" modalPresentationStyle="overFullScreen" id="cDZ-jT-VjG" customClass="MayaServicesViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="AUV-li-pGK">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="718"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="i6p-I2-yNn">
                                <rect key="frame" x="0.0" y="50" width="375" height="762"/>
                                <subviews>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="dgu-KH-kpl">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="762"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Jf8-Mi-Oc4">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="189"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Services" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vXK-9Y-ois">
                                                        <rect key="frame" x="24" y="16" width="327" height="39"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="32"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BP4-9Y-a43">
                                                        <rect key="frame" x="0.0" y="71" width="375" height="0.0"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" id="6Ow-z1-CUt"/>
                                                        </constraints>
                                                    </view>
                                                    <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="Ma5-xR-VxG">
                                                        <rect key="frame" x="0.0" y="71" width="375" height="94"/>
                                                        <color key="backgroundColor" name="PrimaryWhite"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="94" id="Uk8-D9-YHK"/>
                                                        </constraints>
                                                        <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="8" minimumInteritemSpacing="4" id="4rS-v6-elH">
                                                            <size key="itemSize" width="45.700000000000003" height="70"/>
                                                            <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                                            <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                                            <inset key="sectionInset" minX="32" minY="0.0" maxX="32" maxY="0.0"/>
                                                        </collectionViewFlowLayout>
                                                        <cells/>
                                                    </collectionView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstItem="vXK-9Y-ois" firstAttribute="top" secondItem="Jf8-Mi-Oc4" secondAttribute="top" constant="16" id="3QT-Wq-1NI"/>
                                                    <constraint firstItem="BP4-9Y-a43" firstAttribute="leading" secondItem="Jf8-Mi-Oc4" secondAttribute="leading" id="5mp-I0-asM"/>
                                                    <constraint firstAttribute="trailing" secondItem="Ma5-xR-VxG" secondAttribute="trailing" id="8g3-bn-kHX"/>
                                                    <constraint firstItem="Ma5-xR-VxG" firstAttribute="top" secondItem="BP4-9Y-a43" secondAttribute="bottom" id="JKB-oq-Bcy"/>
                                                    <constraint firstItem="vXK-9Y-ois" firstAttribute="leading" secondItem="Jf8-Mi-Oc4" secondAttribute="leading" constant="24" id="Pki-DG-HrU"/>
                                                    <constraint firstItem="Ma5-xR-VxG" firstAttribute="leading" secondItem="Jf8-Mi-Oc4" secondAttribute="leading" id="QZt-CR-DZJ"/>
                                                    <constraint firstAttribute="trailing" secondItem="BP4-9Y-a43" secondAttribute="trailing" id="ZBy-cQ-JIM"/>
                                                    <constraint firstAttribute="bottom" secondItem="Ma5-xR-VxG" secondAttribute="bottom" constant="24" id="bTx-ru-hDm"/>
                                                    <constraint firstAttribute="trailing" secondItem="vXK-9Y-ois" secondAttribute="trailing" constant="24" id="kAr-KY-ntk"/>
                                                    <constraint firstItem="BP4-9Y-a43" firstAttribute="top" secondItem="vXK-9Y-ois" secondAttribute="bottom" constant="16" id="o2u-jc-Kc4"/>
                                                </constraints>
                                            </view>
                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" ambiguous="YES" alwaysBounceVertical="YES" scrollEnabled="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="264" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="0XR-6h-X3D" customClass="ContentSizedTableView" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="185" width="375" height="204"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            </tableView>
                                            <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="dQA-Q8-MwN" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="401" width="327" height="269"/>
                                                <subviews>
                                                    <tableView clipsSubviews="YES" contentMode="scaleToFill" ambiguous="YES" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="60" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="0ce-v9-efv" customClass="ContentSizedTableView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="3" width="327" height="269"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </tableView>
                                                </subviews>
                                                <color key="backgroundColor" name="PrimaryWhite"/>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="0ce-v9-efv" secondAttribute="trailing" id="4f1-L1-ICC"/>
                                                    <constraint firstItem="0ce-v9-efv" firstAttribute="leading" secondItem="dQA-Q8-MwN" secondAttribute="leading" id="VkF-o2-cff"/>
                                                    <constraint firstAttribute="bottom" secondItem="0ce-v9-efv" secondAttribute="bottom" id="bJs-M5-u6D"/>
                                                    <constraint firstItem="0ce-v9-efv" firstAttribute="top" secondItem="dQA-Q8-MwN" secondAttribute="top" id="mk2-mu-m0c"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                        <real key="value" value="16"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="Jf8-Mi-Oc4" secondAttribute="trailing" id="5gy-H2-9IN"/>
                                            <constraint firstItem="dQA-Q8-MwN" firstAttribute="top" secondItem="0XR-6h-X3D" secondAttribute="bottom" constant="8" id="GeA-34-GIr"/>
                                            <constraint firstItem="Jf8-Mi-Oc4" firstAttribute="top" secondItem="dgu-KH-kpl" secondAttribute="top" id="Pb6-23-eev"/>
                                            <constraint firstItem="0XR-6h-X3D" firstAttribute="leading" secondItem="dgu-KH-kpl" secondAttribute="leading" id="bEm-Qh-gGE"/>
                                            <constraint firstItem="Jf8-Mi-Oc4" firstAttribute="leading" secondItem="dgu-KH-kpl" secondAttribute="leading" id="eSs-2D-EWh"/>
                                            <constraint firstAttribute="trailing" secondItem="0XR-6h-X3D" secondAttribute="trailing" id="gkK-DY-VAh"/>
                                            <constraint firstItem="0XR-6h-X3D" firstAttribute="top" secondItem="Jf8-Mi-Oc4" secondAttribute="bottom" constant="12" id="jqJ-YX-96d"/>
                                            <constraint firstItem="dQA-Q8-MwN" firstAttribute="leading" secondItem="dgu-KH-kpl" secondAttribute="leading" constant="24" id="n9O-ma-3F7"/>
                                            <constraint firstAttribute="bottom" secondItem="dQA-Q8-MwN" secondAttribute="bottom" constant="118" id="nvj-gp-Z1b"/>
                                            <constraint firstAttribute="trailing" secondItem="dQA-Q8-MwN" secondAttribute="trailing" constant="24" id="yLf-om-A1j"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" name="Grey1"/>
                                <constraints>
                                    <constraint firstItem="dgu-KH-kpl" firstAttribute="top" secondItem="PGj-yP-QZ2" secondAttribute="top" id="QEg-0J-HUS"/>
                                    <constraint firstItem="dgu-KH-kpl" firstAttribute="trailing" secondItem="PGj-yP-QZ2" secondAttribute="trailing" id="RJi-Ff-EwF"/>
                                    <constraint firstItem="dgu-KH-kpl" firstAttribute="width" secondItem="lC0-kC-eAz" secondAttribute="width" id="Ugg-NZ-m2W"/>
                                    <constraint firstItem="dgu-KH-kpl" firstAttribute="leading" secondItem="PGj-yP-QZ2" secondAttribute="leading" id="pKn-HD-z07"/>
                                    <constraint firstItem="dgu-KH-kpl" firstAttribute="bottom" secondItem="PGj-yP-QZ2" secondAttribute="bottom" id="pui-PY-nEU"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="PGj-yP-QZ2"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="lC0-kC-eAz"/>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="bsH-N2-V0K"/>
                        <color key="backgroundColor" name="PrimaryWhite"/>
                        <constraints>
                            <constraint firstItem="i6p-I2-yNn" firstAttribute="leading" secondItem="bsH-N2-V0K" secondAttribute="leading" id="085-cm-Czc"/>
                            <constraint firstItem="bsH-N2-V0K" firstAttribute="trailing" secondItem="i6p-I2-yNn" secondAttribute="trailing" id="Mh5-yB-y0K"/>
                            <constraint firstItem="i6p-I2-yNn" firstAttribute="top" secondItem="bsH-N2-V0K" secondAttribute="top" id="YZx-bm-UoU"/>
                            <constraint firstAttribute="bottom" secondItem="i6p-I2-yNn" secondAttribute="bottom" id="rze-uT-tEP"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="Services" id="vji-kD-aJC">
                        <barButtonItem key="leftBarButtonItem" image="iconCloseDark" id="pcH-tH-Yik">
                            <color key="tintColor" name="PrimaryBlack"/>
                            <connections>
                                <action selector="didTapBack:" destination="cDZ-jT-VjG" id="mDa-5z-QML"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" translucent="NO" prompted="NO"/>
                    <connections>
                        <outlet property="actionsBackgroundView" destination="dQA-Q8-MwN" id="SEI-0A-wDl"/>
                        <outlet property="actionsTableView" destination="0ce-v9-efv" id="HpL-Z2-bAA"/>
                        <outlet property="adCarouselView" destination="BP4-9Y-a43" id="KH9-4M-HhN"/>
                        <outlet property="adCarouselViewHeightConstraint" destination="6Ow-z1-CUt" id="tXJ-Pt-9qR"/>
                        <outlet property="categoriesTableView" destination="0XR-6h-X3D" id="r1l-GL-ycV"/>
                        <outlet property="coreServicesCollectionView" destination="Ma5-xR-VxG" id="EU0-BM-WJa"/>
                        <outlet property="leftBarButtonItem" destination="pcH-tH-Yik" id="Qsp-BU-GvB"/>
                        <outlet property="scrollContainerView" destination="dgu-KH-kpl" id="kOm-m8-Vof"/>
                        <outlet property="scrollView" destination="i6p-I2-yNn" id="LNL-MT-EUj"/>
                        <outlet property="servicesLabel" destination="vXK-9Y-ois" id="sqa-O4-YVf"/>
                        <outlet property="servicesLabelTopConstraint" destination="3QT-Wq-1NI" id="fyX-11-wBu"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Jer-zn-hcH" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2668" y="744.82758620689663"/>
        </scene>
        <!--Maya Credit Balance Banner View Controller-->
        <scene sceneID="hu7-ZG-sQ5">
            <objects>
                <viewController storyboardIdentifier="MayaCreditBalanceBannerViewController" id="B8A-ls-zaH" customClass="MayaCreditBalanceBannerViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="ell-Qr-BdI">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="500"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hIb-K6-5ei" userLabel="Easy Credit Card Container" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="50" width="375" height="82"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" translatesAutoresizingMaskIntoConstraints="NO" id="qyP-LQ-QPL">
                                        <rect key="frame" x="16" y="28" width="343" height="40"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="PVO-9Z-Npg" userLabel="Value Container View">
                                                <rect key="frame" x="0.0" y="0.0" width="343" height="28"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="zwe-JK-E5l" userLabel="Easy Credit Container">
                                                        <rect key="frame" x="0.0" y="0.0" width="343" height="28"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CnY-SW-3DF">
                                                                <rect key="frame" x="0.0" y="0.0" width="159.66666666666666" height="28"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" image="iconGreenRightChevron" translatesAutoresizingMaskIntoConstraints="NO" id="Qkr-ML-Pg6">
                                                                        <rect key="frame" x="101.66666666666667" y="0.0" width="16" height="28"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="16" id="AAO-Gj-Ebf"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" text="Easy Credit" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="F7d-QV-gBj" userLabel="Easy Credit Label">
                                                                        <rect key="frame" x="0.0" y="0.0" width="101.66666666666667" height="28"/>
                                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="18"/>
                                                                        <color key="textColor" name="PrimaryBlack"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <constraints>
                                                                    <constraint firstAttribute="bottom" secondItem="F7d-QV-gBj" secondAttribute="bottom" id="IGm-9r-GjY"/>
                                                                    <constraint firstItem="Qkr-ML-Pg6" firstAttribute="trailing" secondItem="F7d-QV-gBj" secondAttribute="trailing" constant="16" id="KTF-48-UmY"/>
                                                                    <constraint firstItem="Qkr-ML-Pg6" firstAttribute="top" secondItem="CnY-SW-3DF" secondAttribute="top" id="QCh-Dz-xbL"/>
                                                                    <constraint firstItem="F7d-QV-gBj" firstAttribute="leading" secondItem="CnY-SW-3DF" secondAttribute="leading" id="nKL-wC-hJ2"/>
                                                                    <constraint firstItem="F7d-QV-gBj" firstAttribute="top" secondItem="CnY-SW-3DF" secondAttribute="top" id="u5R-cw-M5e"/>
                                                                    <constraint firstAttribute="bottom" secondItem="Qkr-ML-Pg6" secondAttribute="bottom" id="uDj-cT-mNk"/>
                                                                </constraints>
                                                            </view>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" text="₱••••••••" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="EVJ-NL-EjY" userLabel="MEC Balance">
                                                                <rect key="frame" x="183.66666666666663" y="0.0" width="159.33333333333337" height="28"/>
                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Medium" family="Cerebri Sans Pro" pointSize="18"/>
                                                                <nil key="textColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="28" id="qyl-UR-6ou"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="zwe-JK-E5l" secondAttribute="trailing" id="51O-AJ-wwV"/>
                                                    <constraint firstAttribute="bottom" secondItem="zwe-JK-E5l" secondAttribute="bottom" id="AAr-kX-pwg"/>
                                                    <constraint firstItem="zwe-JK-E5l" firstAttribute="top" secondItem="PVO-9Z-Npg" secondAttribute="top" id="MN7-cq-71B"/>
                                                    <constraint firstItem="zwe-JK-E5l" firstAttribute="leading" secondItem="PVO-9Z-Npg" secondAttribute="leading" id="mte-bg-YFw"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aSe-Hl-Qqc" userLabel="Label Container View">
                                                <rect key="frame" x="0.0" y="28" width="343" height="12"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="CdQ-qR-VoS">
                                                        <rect key="frame" x="0.0" y="0.0" width="343" height="12"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" text="Borrow Instantly" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ZUC-q3-Kt2">
                                                                <rect key="frame" x="0.0" y="0.0" width="171.66666666666666" height="12"/>
                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="12"/>
                                                                <color key="textColor" name="Grey5"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" text="Available credit" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ji8-65-Avf">
                                                                <rect key="frame" x="171.66666666666663" y="0.0" width="171.33333333333337" height="12"/>
                                                                <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="12"/>
                                                                <color key="textColor" name="Grey5"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="12" id="x3c-yh-yyq"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="CdQ-qR-VoS" firstAttribute="top" secondItem="aSe-Hl-Qqc" secondAttribute="top" id="2oQ-TE-HxH"/>
                                                    <constraint firstAttribute="trailing" secondItem="CdQ-qR-VoS" secondAttribute="trailing" id="5B0-oG-k5Z"/>
                                                    <constraint firstItem="CdQ-qR-VoS" firstAttribute="leading" secondItem="aSe-Hl-Qqc" secondAttribute="leading" id="SSc-jC-8bc"/>
                                                    <constraint firstAttribute="bottom" secondItem="CdQ-qR-VoS" secondAttribute="bottom" id="dxX-XW-sp4"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" name="Grey3"/>
                                <constraints>
                                    <constraint firstItem="qyP-LQ-QPL" firstAttribute="leading" secondItem="hIb-K6-5ei" secondAttribute="leading" constant="16" id="3hp-Ck-XdH"/>
                                    <constraint firstAttribute="bottom" secondItem="qyP-LQ-QPL" secondAttribute="bottom" constant="14" id="6Db-Uj-F7f"/>
                                    <constraint firstAttribute="trailing" secondItem="qyP-LQ-QPL" secondAttribute="trailing" constant="16" id="Sru-Nh-o9Q"/>
                                    <constraint firstItem="qyP-LQ-QPL" firstAttribute="top" secondItem="hIb-K6-5ei" secondAttribute="top" constant="28" id="jHX-YR-hsM"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <outletCollection property="gestureRecognizers" destination="PE6-5D-aY0" appends="YES" id="ULm-hG-Onl"/>
                                </connections>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="gBY-pw-xRb"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="gBY-pw-xRb" firstAttribute="trailing" secondItem="hIb-K6-5ei" secondAttribute="trailing" id="37b-mP-lna"/>
                            <constraint firstItem="gBY-pw-xRb" firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="hIb-K6-5ei" secondAttribute="bottom" id="ZZx-2O-cHs"/>
                            <constraint firstItem="gBY-pw-xRb" firstAttribute="top" secondItem="hIb-K6-5ei" secondAttribute="top" id="aAG-sX-Hyv"/>
                            <constraint firstItem="gBY-pw-xRb" firstAttribute="leading" secondItem="hIb-K6-5ei" secondAttribute="leading" id="ved-a4-3n8"/>
                        </constraints>
                    </view>
                    <size key="freeformSize" width="375" height="500"/>
                    <connections>
                        <outlet property="availableCreditLabel" destination="EVJ-NL-EjY" id="5sL-Qb-bsb"/>
                        <outlet property="easyCreditCardContainer" destination="hIb-K6-5ei" id="UO7-h0-G96"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="NKJ-ct-a6U" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
                <tapGestureRecognizer id="PE6-5D-aY0">
                    <connections>
                        <action selector="didTapEasyCreditCard:" destination="B8A-ls-zaH" id="pZ2-BZ-ICv"/>
                    </connections>
                </tapGestureRecognizer>
            </objects>
            <point key="canvasLocation" x="-1743.2" y="1322.6600985221676"/>
        </scene>
        <!--Maya Wallet Balance View Controller-->
        <scene sceneID="yal-vC-QxZ">
            <objects>
                <viewController storyboardIdentifier="MayaWalletBalanceViewController" id="MOY-gs-XSW" customClass="MayaWalletBalanceViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="4VA-0D-Fht">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="500"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="85M-bF-Anv" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="50" width="375" height="154"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="drQ-un-IOC">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="154"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YCK-bV-kSw" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="154"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aPb-VJ-udC" userLabel="Balance Shimmer">
                                                        <rect key="frame" x="16" y="20" width="226" height="34"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="34" id="NBj-vP-glL"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="skeletonCornerRadius">
                                                                <real key="value" value="17"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="₱••••••••" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="16" translatesAutoresizingMaskIntoConstraints="NO" id="n0u-CY-ZX8">
                                                        <rect key="frame" x="16" y="20" width="303" height="39"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="32"/>
                                                        <color key="textColor" name="ContentPrimaryBlack"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isHiddenWhenSkeletonIsActive" value="YES"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="btK-dV-enU" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="16" y="94" width="165.66666666666666" height="44"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="44" id="PX0-lV-u0w"/>
                                                        </constraints>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal" title="Cash in"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="image" keyPath="leftHandImage" value="cashInV2Icon"/>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                <integer key="value" value="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="didTapCashIn:" destination="MOY-gs-XSW" eventType="touchUpInside" id="cAt-VY-Int"/>
                                                        </connections>
                                                    </button>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Plc-YU-LG4" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="193.66666666666663" y="94" width="165.33333333333337" height="44"/>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <state key="normal" title="Send"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="image" keyPath="leftHandImage" value="sendMoneyV2Icon"/>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                                <integer key="value" value="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="didTapSendMoney:" destination="MOY-gs-XSW" eventType="touchUpInside" id="wIA-SV-kmD"/>
                                                        </connections>
                                                    </button>
                                                    <imageView clipsSubviews="YES" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="eyeOpenedIcon" translatesAutoresizingMaskIntoConstraints="NO" id="gO8-GJ-dtG">
                                                        <rect key="frame" x="335" y="26" width="24" height="24"/>
                                                        <gestureRecognizers/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="24" id="Kj4-oZ-mb1"/>
                                                            <constraint firstAttribute="width" constant="24" id="v3c-QB-JmK"/>
                                                        </constraints>
                                                        <connections>
                                                            <outletCollection property="gestureRecognizers" destination="sFF-4Z-OUZ" appends="YES" id="MP1-oR-2oY"/>
                                                        </connections>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Wallet balance" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FsD-qt-HHh">
                                                        <rect key="frame" x="16.000000000000007" y="60" width="102.66666666666669" height="17"/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="14"/>
                                                        <color key="textColor" name="ContentGrey5"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Auto cash in" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dBD-eh-tcK">
                                                        <rect key="frame" x="126.66666666666669" y="60" width="85.333333333333314" height="17"/>
                                                        <gestureRecognizers/>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="14"/>
                                                        <color key="textColor" name="ContentPrimaryGreen"/>
                                                        <nil key="highlightedColor"/>
                                                        <connections>
                                                            <outletCollection property="gestureRecognizers" destination="45L-SP-MrD" appends="YES" id="Gfv-oW-gT0"/>
                                                        </connections>
                                                    </label>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="n0u-CY-ZX8" firstAttribute="top" secondItem="YCK-bV-kSw" secondAttribute="top" constant="20" id="1oP-l5-2NA"/>
                                                    <constraint firstAttribute="bottom" secondItem="btK-dV-enU" secondAttribute="bottom" constant="16" id="24I-Hd-0BV"/>
                                                    <constraint firstItem="dBD-eh-tcK" firstAttribute="leading" secondItem="FsD-qt-HHh" secondAttribute="trailing" constant="8" id="3Gh-tT-g02"/>
                                                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="dBD-eh-tcK" secondAttribute="trailing" symbolic="YES" id="3d1-e2-ghK"/>
                                                    <constraint firstItem="n0u-CY-ZX8" firstAttribute="leading" secondItem="YCK-bV-kSw" secondAttribute="leading" constant="16" id="3gR-lC-FMF"/>
                                                    <constraint firstItem="dBD-eh-tcK" firstAttribute="centerY" secondItem="FsD-qt-HHh" secondAttribute="centerY" id="851-F6-u8h"/>
                                                    <constraint firstItem="btK-dV-enU" firstAttribute="leading" secondItem="YCK-bV-kSw" secondAttribute="leading" constant="16" id="85u-ZW-5mm"/>
                                                    <constraint firstAttribute="height" constant="154" id="A9W-FL-BNx"/>
                                                    <constraint firstItem="aPb-VJ-udC" firstAttribute="top" secondItem="YCK-bV-kSw" secondAttribute="top" constant="20" id="BBB-lf-PIk"/>
                                                    <constraint firstItem="gO8-GJ-dtG" firstAttribute="leading" secondItem="n0u-CY-ZX8" secondAttribute="trailing" constant="16" id="Dp8-Z9-P8n"/>
                                                    <constraint firstItem="Plc-YU-LG4" firstAttribute="height" secondItem="btK-dV-enU" secondAttribute="height" id="Dvj-34-uu7"/>
                                                    <constraint firstItem="FsD-qt-HHh" firstAttribute="leading" secondItem="n0u-CY-ZX8" secondAttribute="leading" id="Ean-px-I4q"/>
                                                    <constraint firstItem="aPb-VJ-udC" firstAttribute="leading" secondItem="n0u-CY-ZX8" secondAttribute="leading" id="Ewi-p6-IGx"/>
                                                    <constraint firstItem="aPb-VJ-udC" firstAttribute="width" secondItem="YCK-bV-kSw" secondAttribute="width" multiplier="197:327" id="HmM-rw-IdA"/>
                                                    <constraint firstItem="FsD-qt-HHh" firstAttribute="top" secondItem="n0u-CY-ZX8" secondAttribute="bottom" constant="1" id="Luf-3x-li3"/>
                                                    <constraint firstItem="gO8-GJ-dtG" firstAttribute="top" secondItem="YCK-bV-kSw" secondAttribute="top" constant="26" id="Xd3-iP-p7m"/>
                                                    <constraint firstItem="Plc-YU-LG4" firstAttribute="leading" secondItem="btK-dV-enU" secondAttribute="trailing" constant="12" id="ZdR-Tt-CZP"/>
                                                    <constraint firstItem="Plc-YU-LG4" firstAttribute="width" secondItem="btK-dV-enU" secondAttribute="width" id="aMz-W6-TIL"/>
                                                    <constraint firstAttribute="trailing" secondItem="gO8-GJ-dtG" secondAttribute="trailing" constant="16" id="dW4-bf-IoE"/>
                                                    <constraint firstAttribute="trailing" secondItem="Plc-YU-LG4" secondAttribute="trailing" constant="16" id="hvp-K2-14C"/>
                                                    <constraint firstAttribute="bottom" secondItem="Plc-YU-LG4" secondAttribute="bottom" constant="16" id="mJK-IY-Tag"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="9gO-zK-Rgt">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="16"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Bxh-0b-CTq" customClass="ActionCardView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="16" y="0.0" width="343" height="0.0"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="skeletonCornerRadius">
                                                                <real key="value" value="16"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="Bxh-0b-CTq" firstAttribute="leading" secondItem="9gO-zK-Rgt" secondAttribute="leading" constant="16" id="2ti-oY-2i9"/>
                                                    <constraint firstAttribute="trailing" secondItem="Bxh-0b-CTq" secondAttribute="trailing" constant="16" id="oQa-ft-OmZ"/>
                                                    <constraint firstItem="Bxh-0b-CTq" firstAttribute="top" secondItem="9gO-zK-Rgt" secondAttribute="top" id="peb-11-Pmx"/>
                                                    <constraint firstAttribute="bottom" secondItem="Bxh-0b-CTq" secondAttribute="bottom" constant="16" id="ver-kx-Vru"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                        </subviews>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" name="BackgroundPrimaryWhite"/>
                                <constraints>
                                    <constraint firstItem="drQ-un-IOC" firstAttribute="leading" secondItem="85M-bF-Anv" secondAttribute="leading" id="7eM-HA-LWG"/>
                                    <constraint firstItem="drQ-un-IOC" firstAttribute="top" secondItem="85M-bF-Anv" secondAttribute="top" id="PxL-I0-039"/>
                                    <constraint firstAttribute="trailing" secondItem="drQ-un-IOC" secondAttribute="trailing" id="aR6-xW-iML"/>
                                    <constraint firstAttribute="bottom" secondItem="drQ-un-IOC" secondAttribute="bottom" id="njA-XN-63c"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                        <real key="value" value="1"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                        <color key="value" name="Grey3"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                        <real key="value" value="16"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="wYg-lf-BTg"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="wYg-lf-BTg" firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="85M-bF-Anv" secondAttribute="bottom" id="eGw-xD-O5u"/>
                            <constraint firstItem="wYg-lf-BTg" firstAttribute="leading" secondItem="85M-bF-Anv" secondAttribute="leading" id="gGo-bi-NrA"/>
                            <constraint firstItem="wYg-lf-BTg" firstAttribute="trailing" secondItem="85M-bF-Anv" secondAttribute="trailing" id="sdg-uE-SYz"/>
                            <constraint firstItem="wYg-lf-BTg" firstAttribute="top" secondItem="85M-bF-Anv" secondAttribute="top" id="tAk-EV-afW"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <size key="freeformSize" width="375" height="500"/>
                    <connections>
                        <outlet property="actionCardContainerView" destination="9gO-zK-Rgt" id="Pus-ZC-zDY"/>
                        <outlet property="actionCardView" destination="Bxh-0b-CTq" id="EQ2-af-9Lx"/>
                        <outlet property="balanceContainerView" destination="YCK-bV-kSw" id="Sds-a7-7jG"/>
                        <outlet property="balanceLabel" destination="n0u-CY-ZX8" id="fY1-Mc-RTt"/>
                        <outlet property="balanceShimmerView" destination="aPb-VJ-udC" id="36o-MO-iqo"/>
                        <outlet property="balanceToggleImageView" destination="gO8-GJ-dtG" id="85m-II-m7N"/>
                        <outlet property="optionsLabel" destination="dBD-eh-tcK" id="PcQ-Lh-Xa4"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="AZY-hr-7Gz" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
                <tapGestureRecognizer id="45L-SP-MrD">
                    <connections>
                        <action selector="didTapOptions:" destination="MOY-gs-XSW" id="3At-rd-cDT"/>
                    </connections>
                </tapGestureRecognizer>
                <tapGestureRecognizer id="sFF-4Z-OUZ">
                    <connections>
                        <action selector="didTapToggle:" destination="MOY-gs-XSW" id="Bmy-XH-U5u"/>
                    </connections>
                </tapGestureRecognizer>
            </objects>
            <point key="canvasLocation" x="-1084" y="1323"/>
        </scene>
        <!--Maya Ad Carousel View Controller-->
        <scene sceneID="jVV-oD-Pvi">
            <objects>
                <viewController storyboardIdentifier="MayaAdCarouselViewController" id="aSi-mr-LEr" customClass="MayaAdCarouselViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="mSG-rp-chw">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <collectionView clipsSubviews="YES" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" bounces="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="yk0-JN-pyJ" customClass="InfiniteCollectionView" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="0.0" minimumInteritemSpacing="12" id="QNf-3v-kOI">
                                    <size key="itemSize" width="225" height="80"/>
                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                </collectionViewFlowLayout>
                                <cells>
                                    <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="MayaCreatorStoreAdBannerCollectionViewCell" id="OJf-pS-Woz" customClass="MayaCreatorStoreAdBannerCollectionViewCell" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="370" height="226"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <collectionViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="fkP-JG-BfL">
                                            <rect key="frame" x="0.0" y="0.0" width="370" height="226"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="VGO-pF-sEQ" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                    <rect key="frame" x="0.0" y="0.0" width="370" height="226"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="8iF-lO-4CY">
                                                            <rect key="frame" x="0.0" y="0.0" width="370" height="226"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="226" id="XyF-ub-NCi"/>
                                                            </constraints>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </imageView>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="CWs-ke-l1T">
                                                            <rect key="frame" x="0.0" y="0.0" width="370" height="180"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="180" id="nnW-Xj-BOI"/>
                                                            </constraints>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="skeletonCornerRadius">
                                                                    <real key="value" value="16"/>
                                                                </userDefinedRuntimeAttribute>
                                                                <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </imageView>
                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="n4S-h0-tlq">
                                                            <rect key="frame" x="0.0" y="188" width="370" height="38"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nJc-lo-yQm" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                                    <rect key="frame" x="0.0" y="0.0" width="370" height="16"/>
                                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="16" id="JLI-Hn-S8b"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="skeletonCornerRadius">
                                                                            <real key="value" value="8"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lQE-fB-uyG" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                                    <rect key="frame" x="0.0" y="22" width="370" height="16"/>
                                                                    <subviews>
                                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3wg-g2-nyV" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                                            <rect key="frame" x="0.0" y="0.0" width="240" height="16"/>
                                                                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="width" constant="240" id="fHa-R2-vgE"/>
                                                                            </constraints>
                                                                            <userDefinedRuntimeAttributes>
                                                                                <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                                                <userDefinedRuntimeAttribute type="number" keyPath="skeletonCornerRadius">
                                                                                    <real key="value" value="8"/>
                                                                                </userDefinedRuntimeAttribute>
                                                                            </userDefinedRuntimeAttributes>
                                                                        </view>
                                                                    </subviews>
                                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="16" id="9T4-Vz-Lsq"/>
                                                                        <constraint firstItem="3wg-g2-nyV" firstAttribute="top" secondItem="lQE-fB-uyG" secondAttribute="top" id="bpq-pH-iZv"/>
                                                                        <constraint firstItem="3wg-g2-nyV" firstAttribute="leading" secondItem="lQE-fB-uyG" secondAttribute="leading" id="qDi-PK-mFk"/>
                                                                        <constraint firstAttribute="bottom" secondItem="3wg-g2-nyV" secondAttribute="bottom" id="v5O-TO-tvh"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="skeletonCornerRadius">
                                                                            <real key="value" value="0.0"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                            </subviews>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </stackView>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ar4-n6-3SO">
                                                            <rect key="frame" x="0.0" y="182" width="370" height="44"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="HmM-fn-nwv">
                                                                    <rect key="frame" x="0.0" y="0.0" width="370" height="35.333333333333336"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mji-KP-DMP">
                                                                            <rect key="frame" x="0.0" y="0.0" width="370" height="18.333333333333332"/>
                                                                            <fontDescription key="fontDescription" name="CerebriSansPro-Medium" family="Cerebri Sans Pro" pointSize="14"/>
                                                                            <color key="textColor" name="ContentPrimaryBlack"/>
                                                                            <nil key="highlightedColor"/>
                                                                            <userDefinedRuntimeAttributes>
                                                                                <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="NO"/>
                                                                            </userDefinedRuntimeAttributes>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Yca-Fs-FX4">
                                                                            <rect key="frame" x="0.0" y="19.333333333333343" width="370" height="16"/>
                                                                            <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="12"/>
                                                                            <color key="textColor" name="ContentGrey6"/>
                                                                            <nil key="highlightedColor"/>
                                                                            <userDefinedRuntimeAttributes>
                                                                                <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="NO"/>
                                                                                <userDefinedRuntimeAttribute type="boolean" keyPath="useFontLineHeight" value="YES"/>
                                                                            </userDefinedRuntimeAttributes>
                                                                        </label>
                                                                    </subviews>
                                                                </stackView>
                                                            </subviews>
                                                            <color key="backgroundColor" name="BackgroundPrimaryWhite"/>
                                                            <constraints>
                                                                <constraint firstItem="HmM-fn-nwv" firstAttribute="leading" secondItem="Ar4-n6-3SO" secondAttribute="leading" id="2VD-bE-IBt"/>
                                                                <constraint firstAttribute="bottom" relation="lessThanOrEqual" secondItem="HmM-fn-nwv" secondAttribute="bottom" constant="17.670000000000002" id="Dez-ng-CGm"/>
                                                                <constraint firstAttribute="trailing" secondItem="HmM-fn-nwv" secondAttribute="trailing" id="oeG-ky-C2i"/>
                                                                <constraint firstItem="HmM-fn-nwv" firstAttribute="top" secondItem="Ar4-n6-3SO" secondAttribute="top" id="wBj-La-yTP"/>
                                                            </constraints>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                    <constraints>
                                                        <constraint firstItem="Ar4-n6-3SO" firstAttribute="trailing" secondItem="CWs-ke-l1T" secondAttribute="trailing" id="NTb-6D-erl"/>
                                                        <constraint firstItem="n4S-h0-tlq" firstAttribute="trailing" secondItem="CWs-ke-l1T" secondAttribute="trailing" id="O77-3r-BAi"/>
                                                        <constraint firstItem="8iF-lO-4CY" firstAttribute="top" secondItem="VGO-pF-sEQ" secondAttribute="top" id="QPH-95-dzg"/>
                                                        <constraint firstItem="CWs-ke-l1T" firstAttribute="leading" secondItem="VGO-pF-sEQ" secondAttribute="leading" id="R95-bb-bHt"/>
                                                        <constraint firstItem="Ar4-n6-3SO" firstAttribute="leading" secondItem="CWs-ke-l1T" secondAttribute="leading" id="Ufb-27-k3z"/>
                                                        <constraint firstAttribute="bottom" secondItem="8iF-lO-4CY" secondAttribute="bottom" id="YTf-md-pNO"/>
                                                        <constraint firstAttribute="trailing" secondItem="CWs-ke-l1T" secondAttribute="trailing" id="ZCO-TO-5BV"/>
                                                        <constraint firstAttribute="bottom" secondItem="Ar4-n6-3SO" secondAttribute="bottom" id="ZkG-Ue-Fhv"/>
                                                        <constraint firstItem="n4S-h0-tlq" firstAttribute="top" secondItem="CWs-ke-l1T" secondAttribute="bottom" constant="8" id="emc-aQ-KF0"/>
                                                        <constraint firstItem="CWs-ke-l1T" firstAttribute="top" secondItem="VGO-pF-sEQ" secondAttribute="top" id="fla-FL-cxX"/>
                                                        <constraint firstItem="n4S-h0-tlq" firstAttribute="leading" secondItem="CWs-ke-l1T" secondAttribute="leading" id="fwv-rF-dCu"/>
                                                        <constraint firstAttribute="trailing" secondItem="8iF-lO-4CY" secondAttribute="trailing" id="fyA-ku-8WW"/>
                                                        <constraint firstItem="Ar4-n6-3SO" firstAttribute="top" secondItem="CWs-ke-l1T" secondAttribute="bottom" constant="2" id="woe-nk-ZKj"/>
                                                        <constraint firstItem="8iF-lO-4CY" firstAttribute="leading" secondItem="VGO-pF-sEQ" secondAttribute="leading" id="xEe-NH-VOl"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="skeletonCornerRadius">
                                                            <real key="value" value="12"/>
                                                        </userDefinedRuntimeAttribute>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                            <real key="value" value="12"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="VGO-pF-sEQ" firstAttribute="top" secondItem="fkP-JG-BfL" secondAttribute="top" id="Lyl-9d-o8b"/>
                                                <constraint firstItem="VGO-pF-sEQ" firstAttribute="leading" secondItem="fkP-JG-BfL" secondAttribute="leading" id="PxO-Nr-nF3"/>
                                                <constraint firstAttribute="bottom" secondItem="VGO-pF-sEQ" secondAttribute="bottom" id="cyh-yZ-H6m"/>
                                                <constraint firstAttribute="trailing" secondItem="VGO-pF-sEQ" secondAttribute="trailing" id="kMF-i3-ips"/>
                                            </constraints>
                                        </collectionViewCellContentView>
                                        <size key="customSize" width="370" height="226"/>
                                        <connections>
                                            <outlet property="bannerImageView" destination="CWs-ke-l1T" id="RDN-nB-aYl"/>
                                            <outlet property="bannerView" destination="VGO-pF-sEQ" id="RTc-xc-WYg"/>
                                            <outlet property="largerBannerImageView" destination="8iF-lO-4CY" id="8QI-it-iqs"/>
                                            <outlet property="loadingStackView" destination="n4S-h0-tlq" id="z8b-oN-uow"/>
                                            <outlet property="messageLabel" destination="Yca-Fs-FX4" id="4vh-mA-3UU"/>
                                            <outlet property="secondLineLoadingViewWidthConstraint" destination="fHa-R2-vgE" id="QkO-yp-eLR"/>
                                            <outlet property="textContentView" destination="Ar4-n6-3SO" id="gPw-zE-sgS"/>
                                            <outlet property="titleLabel" destination="mji-KP-DMP" id="hDf-Tf-pMt"/>
                                        </connections>
                                    </collectionViewCell>
                                    <collectionViewCell opaque="NO" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="AdBannerCollectionViewCell" id="naf-to-SrL" customClass="AdBannerCollectionViewCell" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="72.666666666666671" y="226" width="225" height="80"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO">
                                            <rect key="frame" x="0.0" y="0.0" width="225" height="80"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Alb-He-r76" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                    <rect key="frame" x="0.0" y="0.0" width="225" height="80"/>
                                                    <subviews>
                                                        <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="gkm-lt-wJ3">
                                                            <rect key="frame" x="0.0" y="0.0" width="225" height="80"/>
                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        </imageView>
                                                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yJy-mH-NKZ">
                                                            <rect key="frame" x="0.0" y="0.0" width="225" height="80"/>
                                                            <subviews>
                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bannerBackgroundGradient" translatesAutoresizingMaskIntoConstraints="NO" id="R1m-2y-ToU">
                                                                    <rect key="frame" x="0.0" y="0.0" width="225" height="80"/>
                                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </imageView>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="top" translatesAutoresizingMaskIntoConstraints="NO" id="J3u-UC-9dN">
                                                                    <rect key="frame" x="8" y="29.333333333333332" width="209" height="42.666666666666671"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Title" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xUz-2i-cJN">
                                                                            <rect key="frame" x="0.0" y="0.0" width="27.333333333333332" height="14.333333333333334"/>
                                                                            <fontDescription key="fontDescription" name="WorkSans-Bold" family="Work Sans" pointSize="12"/>
                                                                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalCompressionResistancePriority="751" text="Sample text only with multiple lines do not use" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RB9-C8-Jdj">
                                                                            <rect key="frame" x="0.0" y="14.333333333333334" width="186" height="28.333333333333329"/>
                                                                            <fontDescription key="fontDescription" name="WorkSans-Medium" family="Work Sans" pointSize="12"/>
                                                                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                </stackView>
                                                            </subviews>
                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="trailing" secondItem="J3u-UC-9dN" secondAttribute="trailing" constant="8" id="3c3-oU-hIu"/>
                                                                <constraint firstItem="R1m-2y-ToU" firstAttribute="leading" secondItem="yJy-mH-NKZ" secondAttribute="leading" id="FI1-Nu-mnx"/>
                                                                <constraint firstAttribute="bottom" secondItem="J3u-UC-9dN" secondAttribute="bottom" constant="8" id="Zlm-9V-hnN"/>
                                                                <constraint firstItem="R1m-2y-ToU" firstAttribute="top" secondItem="yJy-mH-NKZ" secondAttribute="top" id="aHn-L6-A3o"/>
                                                                <constraint firstItem="J3u-UC-9dN" firstAttribute="leading" secondItem="yJy-mH-NKZ" secondAttribute="leading" constant="8" id="bNk-xe-IGq"/>
                                                                <constraint firstAttribute="trailing" secondItem="R1m-2y-ToU" secondAttribute="trailing" id="ckx-6A-S5D"/>
                                                                <constraint firstAttribute="bottom" secondItem="R1m-2y-ToU" secondAttribute="bottom" id="it8-if-dF5"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="trailing" secondItem="gkm-lt-wJ3" secondAttribute="trailing" id="3WB-xq-EFQ"/>
                                                        <constraint firstAttribute="bottom" secondItem="gkm-lt-wJ3" secondAttribute="bottom" id="4Sf-vx-lwa"/>
                                                        <constraint firstAttribute="bottom" secondItem="yJy-mH-NKZ" secondAttribute="bottom" id="EiP-nL-5KZ"/>
                                                        <constraint firstItem="gkm-lt-wJ3" firstAttribute="leading" secondItem="Alb-He-r76" secondAttribute="leading" id="IdN-RB-2AX"/>
                                                        <constraint firstAttribute="trailing" secondItem="yJy-mH-NKZ" secondAttribute="trailing" id="Jls-ak-dj4"/>
                                                        <constraint firstItem="yJy-mH-NKZ" firstAttribute="leading" secondItem="Alb-He-r76" secondAttribute="leading" id="Qb3-Gn-fzh"/>
                                                        <constraint firstItem="yJy-mH-NKZ" firstAttribute="top" secondItem="Alb-He-r76" secondAttribute="top" id="aJx-OW-X2R"/>
                                                        <constraint firstItem="gkm-lt-wJ3" firstAttribute="top" secondItem="Alb-He-r76" secondAttribute="top" id="paL-Ub-Rvy"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                            <integer key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                        <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                            </subviews>
                                        </view>
                                        <constraints>
                                            <constraint firstItem="Alb-He-r76" firstAttribute="leading" secondItem="naf-to-SrL" secondAttribute="leading" id="M4h-w1-L3T"/>
                                            <constraint firstAttribute="bottom" secondItem="Alb-He-r76" secondAttribute="bottom" id="aSF-Gt-x6N"/>
                                            <constraint firstItem="Alb-He-r76" firstAttribute="top" secondItem="naf-to-SrL" secondAttribute="top" id="tec-VI-cXa"/>
                                            <constraint firstAttribute="trailing" secondItem="Alb-He-r76" secondAttribute="trailing" id="xsP-ag-aGQ"/>
                                        </constraints>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <outlet property="bannerImageView" destination="gkm-lt-wJ3" id="hNU-lg-WiW"/>
                                            <outlet property="bannerView" destination="Alb-He-r76" id="pSu-IS-MhF"/>
                                            <outlet property="messageLabel" destination="RB9-C8-Jdj" id="vt3-Th-KnM"/>
                                            <outlet property="textContentView" destination="yJy-mH-NKZ" id="hdV-23-nUW"/>
                                            <outlet property="titleLabel" destination="xUz-2i-cJN" id="TCI-3z-v7w"/>
                                        </connections>
                                    </collectionViewCell>
                                    <collectionViewCell opaque="NO" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="CashInAdBannerCollectionViewCell" id="nWK-VO-PNs" userLabel="CashInAdBannerCollectionViewCell" customClass="CashInAdBannerCollectionViewCell" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="72.666666666666671" y="306" width="225" height="80"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO">
                                            <rect key="frame" x="0.0" y="0.0" width="225" height="80"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0ER-H1-4Qo" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                    <rect key="frame" x="0.0" y="0.0" width="225" height="80"/>
                                                    <subviews>
                                                        <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="8E8-Pv-VEB">
                                                            <rect key="frame" x="0.0" y="0.0" width="225" height="80"/>
                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        </imageView>
                                                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Sjr-d7-q9E">
                                                            <rect key="frame" x="0.0" y="0.0" width="225" height="80"/>
                                                            <subviews>
                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bannerBackgroundGradient" translatesAutoresizingMaskIntoConstraints="NO" id="i7x-HF-f6w">
                                                                    <rect key="frame" x="0.0" y="0.0" width="225" height="80"/>
                                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </imageView>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="top" translatesAutoresizingMaskIntoConstraints="NO" id="jbJ-gh-44v">
                                                                    <rect key="frame" x="16" y="-3" width="193" height="67"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Title" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Sfu-ve-GdC">
                                                                            <rect key="frame" x="0.0" y="0.0" width="46" height="28.666666666666668"/>
                                                                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="24"/>
                                                                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalCompressionResistancePriority="751" text="Sample text only with multiple lines do not use" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SHg-uM-nkg">
                                                                            <rect key="frame" x="0.0" y="28.666666666666668" width="176" height="38.333333333333329"/>
                                                                            <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                </stackView>
                                                            </subviews>
                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="bottom" secondItem="i7x-HF-f6w" secondAttribute="bottom" id="EGp-4Z-a8R"/>
                                                                <constraint firstItem="jbJ-gh-44v" firstAttribute="leading" secondItem="Sjr-d7-q9E" secondAttribute="leading" constant="16" id="OfC-tV-TNW"/>
                                                                <constraint firstAttribute="trailing" secondItem="jbJ-gh-44v" secondAttribute="trailing" constant="16" id="Vmb-AZ-daA"/>
                                                                <constraint firstAttribute="bottom" secondItem="jbJ-gh-44v" secondAttribute="bottom" constant="16" id="c7B-ob-RCC"/>
                                                                <constraint firstAttribute="trailing" secondItem="i7x-HF-f6w" secondAttribute="trailing" id="jgs-Z7-axk"/>
                                                                <constraint firstItem="i7x-HF-f6w" firstAttribute="leading" secondItem="Sjr-d7-q9E" secondAttribute="leading" id="ldA-o4-JdL"/>
                                                                <constraint firstItem="i7x-HF-f6w" firstAttribute="top" secondItem="Sjr-d7-q9E" secondAttribute="top" id="oLV-TT-LQ7"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstItem="8E8-Pv-VEB" firstAttribute="top" secondItem="0ER-H1-4Qo" secondAttribute="top" id="0tD-hI-mCl"/>
                                                        <constraint firstAttribute="trailing" secondItem="8E8-Pv-VEB" secondAttribute="trailing" id="AjI-8m-ONd"/>
                                                        <constraint firstAttribute="bottom" secondItem="Sjr-d7-q9E" secondAttribute="bottom" id="PJ2-Ke-gQB"/>
                                                        <constraint firstAttribute="trailing" secondItem="Sjr-d7-q9E" secondAttribute="trailing" id="X15-Vy-Ib5"/>
                                                        <constraint firstItem="Sjr-d7-q9E" firstAttribute="top" secondItem="0ER-H1-4Qo" secondAttribute="top" id="XNH-6d-prP"/>
                                                        <constraint firstAttribute="bottom" secondItem="8E8-Pv-VEB" secondAttribute="bottom" id="eJE-JO-40Z"/>
                                                        <constraint firstItem="Sjr-d7-q9E" firstAttribute="leading" secondItem="0ER-H1-4Qo" secondAttribute="leading" id="mgd-hQ-QDb"/>
                                                        <constraint firstItem="8E8-Pv-VEB" firstAttribute="leading" secondItem="0ER-H1-4Qo" secondAttribute="leading" id="ws7-Eu-XuX"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                            <integer key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                        <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                            </subviews>
                                        </view>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="0ER-H1-4Qo" secondAttribute="trailing" id="89O-xC-H9a"/>
                                            <constraint firstItem="0ER-H1-4Qo" firstAttribute="leading" secondItem="nWK-VO-PNs" secondAttribute="leading" id="Sqb-Kv-5jm"/>
                                            <constraint firstAttribute="bottom" secondItem="0ER-H1-4Qo" secondAttribute="bottom" id="a6m-ph-iRi"/>
                                            <constraint firstItem="0ER-H1-4Qo" firstAttribute="top" secondItem="nWK-VO-PNs" secondAttribute="top" id="iPi-Kd-2eE"/>
                                        </constraints>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <outlet property="bannerImageView" destination="8E8-Pv-VEB" id="mHn-oa-M7H"/>
                                            <outlet property="bannerView" destination="0ER-H1-4Qo" id="bCs-mE-tyw"/>
                                            <outlet property="messageLabel" destination="SHg-uM-nkg" id="Gi4-d9-UmT"/>
                                            <outlet property="textContentView" destination="Sjr-d7-q9E" id="Yp9-XD-0PK"/>
                                            <outlet property="titleLabel" destination="Sfu-ve-GdC" id="E4e-wW-ten"/>
                                        </connections>
                                    </collectionViewCell>
                                    <collectionViewCell opaque="NO" multipleTouchEnabled="YES" contentMode="center" restorationIdentifier="MayaAdBannerCollectionViewCell" reuseIdentifier="MayaAdBannerCollectionViewCell" id="fzD-bb-zX5" userLabel="MayaAdBannerCollectionViewCell" customClass="MayaAdBannerCollectionViewCell" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="72.666666666666671" y="386" width="225" height="80"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO">
                                            <rect key="frame" x="0.0" y="0.0" width="225" height="80"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jAH-Oh-y3Y" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                    <rect key="frame" x="0.0" y="0.0" width="225" height="80"/>
                                                    <subviews>
                                                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="M2y-p6-Sdh">
                                                            <rect key="frame" x="0.0" y="0.0" width="225" height="80"/>
                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        </imageView>
                                                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="n0y-P9-HDa" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                            <rect key="frame" x="0.0" y="0.0" width="225" height="80"/>
                                                            <subviews>
                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bannerBackgroundGradient" translatesAutoresizingMaskIntoConstraints="NO" id="aWb-RA-EyQ">
                                                                    <rect key="frame" x="0.0" y="0.0" width="225" height="80"/>
                                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </imageView>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="top" translatesAutoresizingMaskIntoConstraints="NO" id="mX1-kr-eh8">
                                                                    <rect key="frame" x="8" y="23" width="209" height="49"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Title" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TNF-6j-PWJ">
                                                                            <rect key="frame" x="0.0" y="0.0" width="34.666666666666664" height="19.666666666666668"/>
                                                                            <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                                            <color key="textColor" name="ContentPrimaryWhite"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalCompressionResistancePriority="751" text="Sample text only with multiple lines do not use" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kxW-Mz-fdx">
                                                                            <rect key="frame" x="0.0" y="19.666666666666664" width="208.66666666666666" height="29.333333333333336"/>
                                                                            <fontDescription key="fontDescription" name="Jeko-SemiBold" family="Jeko" pointSize="12"/>
                                                                            <color key="textColor" name="ContentPrimaryWhite"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                </stackView>
                                                            </subviews>
                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="bottom" secondItem="mX1-kr-eh8" secondAttribute="bottom" constant="8" id="2xz-vB-0iD"/>
                                                                <constraint firstItem="aWb-RA-EyQ" firstAttribute="top" secondItem="n0y-P9-HDa" secondAttribute="top" id="ApP-5G-vOB"/>
                                                                <constraint firstItem="aWb-RA-EyQ" firstAttribute="leading" secondItem="n0y-P9-HDa" secondAttribute="leading" id="B6S-GO-Whk"/>
                                                                <constraint firstAttribute="trailing" secondItem="aWb-RA-EyQ" secondAttribute="trailing" id="gQj-js-cTW"/>
                                                                <constraint firstAttribute="trailing" secondItem="mX1-kr-eh8" secondAttribute="trailing" constant="8" id="jk9-Ym-3c9"/>
                                                                <constraint firstItem="mX1-kr-eh8" firstAttribute="leading" secondItem="n0y-P9-HDa" secondAttribute="leading" constant="8" id="prY-IR-1kO"/>
                                                                <constraint firstAttribute="bottom" secondItem="aWb-RA-EyQ" secondAttribute="bottom" id="rrJ-Gj-Vvm"/>
                                                            </constraints>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                                    <integer key="value" value="16"/>
                                                                </userDefinedRuntimeAttribute>
                                                            </userDefinedRuntimeAttributes>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstItem="n0y-P9-HDa" firstAttribute="top" secondItem="jAH-Oh-y3Y" secondAttribute="top" id="Dfv-ZL-Fb1"/>
                                                        <constraint firstAttribute="bottom" secondItem="M2y-p6-Sdh" secondAttribute="bottom" id="PfM-Cu-fe9"/>
                                                        <constraint firstAttribute="trailing" secondItem="n0y-P9-HDa" secondAttribute="trailing" id="QLY-Jr-4QJ"/>
                                                        <constraint firstAttribute="trailing" secondItem="M2y-p6-Sdh" secondAttribute="trailing" id="XGK-DX-4LU"/>
                                                        <constraint firstAttribute="bottom" secondItem="n0y-P9-HDa" secondAttribute="bottom" id="apI-11-P07"/>
                                                        <constraint firstItem="M2y-p6-Sdh" firstAttribute="top" secondItem="jAH-Oh-y3Y" secondAttribute="top" id="cvq-t3-jbw"/>
                                                        <constraint firstItem="n0y-P9-HDa" firstAttribute="leading" secondItem="jAH-Oh-y3Y" secondAttribute="leading" id="eh0-7o-hHe"/>
                                                        <constraint firstItem="M2y-p6-Sdh" firstAttribute="leading" secondItem="jAH-Oh-y3Y" secondAttribute="leading" id="qHo-CZ-ly9"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                            <real key="value" value="16"/>
                                                        </userDefinedRuntimeAttribute>
                                                        <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                            </subviews>
                                        </view>
                                        <constraints>
                                            <constraint firstItem="jAH-Oh-y3Y" firstAttribute="leading" secondItem="fzD-bb-zX5" secondAttribute="leading" id="2SJ-ff-SD1"/>
                                            <constraint firstItem="jAH-Oh-y3Y" firstAttribute="top" secondItem="fzD-bb-zX5" secondAttribute="top" id="HIx-bL-aDo"/>
                                            <constraint firstAttribute="trailing" secondItem="jAH-Oh-y3Y" secondAttribute="trailing" id="K8E-FY-c6R"/>
                                            <constraint firstAttribute="bottom" secondItem="jAH-Oh-y3Y" secondAttribute="bottom" id="lgI-oy-0wd"/>
                                        </constraints>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <outlet property="bannerImageView" destination="M2y-p6-Sdh" id="TeF-7d-UJK"/>
                                            <outlet property="bannerView" destination="jAH-Oh-y3Y" id="m2r-RR-ond"/>
                                            <outlet property="messageLabel" destination="kxW-Mz-fdx" id="BN9-98-UR4"/>
                                            <outlet property="textContentView" destination="n0y-P9-HDa" id="x0K-7f-S4A"/>
                                            <outlet property="titleLabel" destination="TNF-6j-PWJ" id="Usm-Oo-4DS"/>
                                        </connections>
                                    </collectionViewCell>
                                </cells>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="rect" keyPath="contentInset">
                                        <rect key="value" x="0.0" y="12" width="0.0" height="12"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </collectionView>
                            <pageControl hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" hidesForSinglePage="YES" numberOfPages="3" translatesAutoresizingMaskIntoConstraints="NO" id="o7D-Pr-pVF">
                                <rect key="frame" x="151" y="812" width="73" height="0.0"/>
                                <constraints>
                                    <constraint firstAttribute="height" id="v8c-Z6-azm"/>
                                </constraints>
                                <color key="pageIndicatorTintColor" name="Grey4"/>
                                <color key="currentPageIndicatorTintColor" name="Grey5"/>
                            </pageControl>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="hZS-hX-paL"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="yk0-JN-pyJ" secondAttribute="trailing" id="97T-9o-JJ7"/>
                            <constraint firstItem="o7D-Pr-pVF" firstAttribute="top" secondItem="yk0-JN-pyJ" secondAttribute="bottom" id="Plu-dX-cCI"/>
                            <constraint firstAttribute="top" secondItem="yk0-JN-pyJ" secondAttribute="top" id="Vxa-4f-caK"/>
                            <constraint firstAttribute="bottom" secondItem="o7D-Pr-pVF" secondAttribute="bottom" id="Wd7-XU-Kcy"/>
                            <constraint firstItem="o7D-Pr-pVF" firstAttribute="centerX" secondItem="hZS-hX-paL" secondAttribute="centerX" id="qne-ul-lbU"/>
                            <constraint firstItem="yk0-JN-pyJ" firstAttribute="leading" secondItem="mSG-rp-chw" secondAttribute="leading" id="seK-me-kVv"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="collectionView" destination="yk0-JN-pyJ" id="Ndi-lD-Boe"/>
                        <outlet property="pageControl" destination="o7D-Pr-pVF" id="Vg9-Js-Ejn"/>
                        <outlet property="pageControlHeightConstraint" destination="v8c-Z6-azm" id="Ga3-vX-euB"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="b7R-cw-6qZ" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="504.80000000000001" y="1467.4876847290641"/>
        </scene>
        <!--Maya No Activity View Controller-->
        <scene sceneID="qdK-ca-rXM">
            <objects>
                <viewController storyboardIdentifier="MayaNoActivityViewController" id="nxc-sW-P2P" customClass="MayaNoActivityViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="Rat-Ha-fQH">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Transactions" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5AQ-jJ-x3w">
                                <rect key="frame" x="16.000000000000007" y="20" width="126.66666666666669" height="24.333333333333329"/>
                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="20"/>
                                <color key="textColor" name="ContentPrimaryBlack"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="ZeZ-fH-pdY">
                                <rect key="frame" x="0.0" y="55.333333333333343" width="375" height="77"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="1000" verticalCompressionResistancePriority="1000" text="😮 No transactions yet!" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" minimumFontSize="12" adjustsLetterSpacingToFitWidth="YES" translatesAutoresizingMaskIntoConstraints="NO" id="nwm-TA-HKd">
                                        <rect key="frame" x="108.33333333333333" y="0.0" width="158.66666666666669" height="17"/>
                                        <fontDescription key="fontDescription" name="Jeko-Regular" family="Jeko" pointSize="14"/>
                                        <color key="textColor" name="ContentGrey5"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xpw-8M-kyG" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="128" y="32.999999999999993" width="119" height="44.000000000000007"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <accessibility key="accessibilityConfiguration" identifier="pmios_dashboard_add_money_now_button"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="119" id="c8q-xs-jqj"/>
                                            <constraint firstAttribute="height" constant="44" id="pGm-5h-6FQ"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                        <state key="normal" title="Cash in now"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonColor">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="mayaThemeButtonSize">
                                                <integer key="value" value="1"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="didTapAddMoneyNow:" destination="nxc-sW-P2P" eventType="touchUpInside" id="CDX-eD-1YR"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="nwm-TA-HKd" secondAttribute="trailing" id="0WH-fS-3yI"/>
                                    <constraint firstItem="nwm-TA-HKd" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="ZeZ-fH-pdY" secondAttribute="leading" id="GgZ-vT-sag"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="40E-rb-EaM"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="5AQ-jJ-x3w" firstAttribute="top" secondItem="Rat-Ha-fQH" secondAttribute="top" constant="20" id="24f-Z5-2GW"/>
                            <constraint firstItem="ZeZ-fH-pdY" firstAttribute="top" secondItem="5AQ-jJ-x3w" secondAttribute="bottom" constant="11" id="Nj3-4m-E82"/>
                            <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="5AQ-jJ-x3w" secondAttribute="trailing" id="UYu-g1-UEr"/>
                            <constraint firstItem="40E-rb-EaM" firstAttribute="trailing" secondItem="ZeZ-fH-pdY" secondAttribute="trailing" id="V3s-ne-XeF"/>
                            <constraint firstItem="5AQ-jJ-x3w" firstAttribute="leading" secondItem="Rat-Ha-fQH" secondAttribute="leading" constant="16" id="fsD-xB-8Gi"/>
                            <constraint firstItem="ZeZ-fH-pdY" firstAttribute="leading" secondItem="40E-rb-EaM" secondAttribute="leading" id="hey-21-8O1"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="yC9-Up-fzj" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-255" y="2178"/>
        </scene>
        <!--Maya Latest Activities View Controller-->
        <scene sceneID="QOR-Lm-fEW">
            <objects>
                <viewController storyboardIdentifier="MayaLatestActivitiesViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="dYd-Kh-Jlu" customClass="MayaLatestActivitiesViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="0PU-WT-geH">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Eoe-cV-8GJ">
                                <rect key="frame" x="0.0" y="50" width="375" height="561.66666666666663"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8mY-ZQ-MeR" userLabel="Header View" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="16" y="5" width="343" height="48.666666666666664"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yzp-3c-Lux">
                                                <rect key="frame" x="0.0" y="12.000000000000002" width="127" height="24.666666666666671"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="127" id="8Nl-uQ-Nvl"/>
                                                    <constraint firstAttribute="height" constant="24.5" id="XsM-AE-IMH"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="18"/>
                                                <color key="textColor" name="ContentPrimaryBlack"/>
                                                <nil key="highlightedColor"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="linesCornerRadius">
                                                        <integer key="value" value="8"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="lastLineFillPercent">
                                                        <integer key="value" value="100"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="BTw-Ki-zNK">
                                                <rect key="frame" x="296" y="9.6666666666666714" width="47" height="29"/>
                                                <accessibility key="accessibilityConfiguration" identifier="pmios_dashboard_view_all_button"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="47" id="wDL-zL-1pd"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="14"/>
                                                <state key="normal" title="See all">
                                                    <color key="titleColor" name="ContentPrimaryGreen"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="skeletonCornerRadius">
                                                        <real key="value" value="16"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="didTapViewAll:" destination="dYd-Kh-Jlu" eventType="touchUpInside" id="dPz-vp-D1o"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="BTw-Ki-zNK" firstAttribute="centerY" secondItem="yzp-3c-Lux" secondAttribute="centerY" id="1La-fo-XWh"/>
                                            <constraint firstItem="yzp-3c-Lux" firstAttribute="top" secondItem="8mY-ZQ-MeR" secondAttribute="top" constant="12" id="OPA-HY-S0K"/>
                                            <constraint firstAttribute="bottom" secondItem="yzp-3c-Lux" secondAttribute="bottom" constant="12" id="Pat-mN-Nec"/>
                                            <constraint firstItem="BTw-Ki-zNK" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="yzp-3c-Lux" secondAttribute="trailing" constant="12" id="RRK-Hh-9Tj"/>
                                            <constraint firstItem="yzp-3c-Lux" firstAttribute="leading" secondItem="8mY-ZQ-MeR" secondAttribute="leading" id="auH-ve-ECr"/>
                                            <constraint firstAttribute="trailing" secondItem="BTw-Ki-zNK" secondAttribute="trailing" id="dzQ-x1-p57"/>
                                        </constraints>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                <real key="value" value="0.0"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                <real key="value" value="0.0"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                <color key="value" red="0.91764705879999997" green="0.91764705879999997" blue="0.91764705879999997" alpha="1" colorSpace="calibratedRGB"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </view>
                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Nv7-Fq-x1y" userLabel="List View">
                                        <rect key="frame" x="0.0" y="53.666666666666686" width="375" height="500.00000000000006"/>
                                        <subviews>
                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" bounces="NO" scrollEnabled="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" sectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="B2g-iI-714">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="500"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="500" id="b4S-Q5-Ulx"/>
                                                </constraints>
                                                <inset key="separatorInset" minX="12" minY="0.0" maxX="12" maxY="0.0"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="isSkeletonable" value="YES"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="dataSource" destination="dYd-Kh-Jlu" id="RiH-FO-WBA"/>
                                                    <outlet property="delegate" destination="dYd-Kh-Jlu" id="Aj1-Zc-Ggb"/>
                                                </connections>
                                            </tableView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="B2g-iI-714" firstAttribute="top" secondItem="Nv7-Fq-x1y" secondAttribute="top" id="3zM-sq-t4E"/>
                                            <constraint firstAttribute="bottom" secondItem="B2g-iI-714" secondAttribute="bottom" id="6se-s3-Wyu"/>
                                            <constraint firstAttribute="trailing" secondItem="B2g-iI-714" secondAttribute="trailing" id="fhl-XB-zuv"/>
                                            <constraint firstItem="B2g-iI-714" firstAttribute="leading" secondItem="Nv7-Fq-x1y" secondAttribute="leading" id="j7h-Km-C7M"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="8mY-ZQ-MeR" firstAttribute="top" secondItem="Eoe-cV-8GJ" secondAttribute="top" constant="5" id="9t1-fI-KZe"/>
                                    <constraint firstItem="Nv7-Fq-x1y" firstAttribute="leading" secondItem="Eoe-cV-8GJ" secondAttribute="leading" id="NCN-UA-5ab"/>
                                    <constraint firstAttribute="trailing" secondItem="Nv7-Fq-x1y" secondAttribute="trailing" id="gAG-C5-Ja8"/>
                                    <constraint firstAttribute="trailing" secondItem="8mY-ZQ-MeR" secondAttribute="trailing" constant="16" id="qGZ-Eh-w9m"/>
                                    <constraint firstItem="8mY-ZQ-MeR" firstAttribute="leading" secondItem="Eoe-cV-8GJ" secondAttribute="leading" constant="16" id="wuZ-VO-wc2"/>
                                    <constraint firstAttribute="bottom" secondItem="Nv7-Fq-x1y" secondAttribute="bottom" constant="8" id="xYY-I4-38c"/>
                                    <constraint firstItem="Nv7-Fq-x1y" firstAttribute="top" secondItem="8mY-ZQ-MeR" secondAttribute="bottom" id="zQ2-1d-oyG"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="JT6-Y7-qkP"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Eoe-cV-8GJ" firstAttribute="leading" secondItem="JT6-Y7-qkP" secondAttribute="leading" id="1w1-oi-D0r"/>
                            <constraint firstItem="Eoe-cV-8GJ" firstAttribute="top" secondItem="JT6-Y7-qkP" secondAttribute="top" id="tC6-Pf-sfP"/>
                            <constraint firstItem="JT6-Y7-qkP" firstAttribute="trailing" secondItem="Eoe-cV-8GJ" secondAttribute="trailing" id="zwp-pa-ZpJ"/>
                        </constraints>
                    </view>
                    <nil key="simulatedTopBarMetrics"/>
                    <connections>
                        <outlet property="activitiesTableView" destination="B2g-iI-714" id="hQw-N7-i7p"/>
                        <outlet property="activitiesTableViewHeightConstraint" destination="b4S-Q5-Ulx" id="ApC-NO-aep"/>
                        <outlet property="baseContainerView" destination="Nv7-Fq-x1y" id="f8T-mi-271"/>
                        <outlet property="containerView" destination="Eoe-cV-8GJ" id="q6g-0Y-gjm"/>
                        <outlet property="headerView" destination="8mY-ZQ-MeR" id="0AC-Qw-Vnh"/>
                        <outlet property="recentActivitiesLabel" destination="yzp-3c-Lux" id="eUD-Rv-ue4"/>
                        <outlet property="viewAllButton" destination="BTw-Ki-zNK" id="e4l-rU-2Pz"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Lxb-da-DIB" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="504" y="2177"/>
        </scene>
        <!--Maya Creator Store View Controller-->
        <scene sceneID="BGy-Wn-ask">
            <objects>
                <viewController storyboardIdentifier="MayaCreatorStoreViewController" id="Zw5-bS-R6p" customClass="MayaCreatorStoreViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="280-XS-3rW">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CkM-YU-K5D">
                                <rect key="frame" x="0.0" y="50" width="375" height="728"/>
                                <subviews>
                                    <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="ftX-x9-NqT">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="728"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="15" minimumInteritemSpacing="15" id="kgj-bx-5mP">
                                            <size key="itemSize" width="156" height="182"/>
                                            <size key="headerReferenceSize" width="50" height="100"/>
                                            <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                            <inset key="sectionInset" minX="24" minY="16" maxX="24" maxY="16"/>
                                        </collectionViewFlowLayout>
                                        <cells/>
                                        <collectionReusableView key="sectionHeaderView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" reuseIdentifier="MayaCreatorStoreBannerHeaderView" id="rdo-Rd-p6S" customClass="MayaCreatorStoreBannerHeaderView" customModule="PayMaya" customModuleProvider="target">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="100"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yph-mu-DIi">
                                                    <rect key="frame" x="0.0" y="0.0" width="375" height="100"/>
                                                    <color key="backgroundColor" name="BackgroundPrimaryWhite"/>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" name="AdditionalPink"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="yph-mu-DIi" secondAttribute="trailing" id="6rY-hY-TKC"/>
                                                <constraint firstItem="yph-mu-DIi" firstAttribute="top" secondItem="rdo-Rd-p6S" secondAttribute="top" id="QWy-1V-sQ8"/>
                                                <constraint firstItem="yph-mu-DIi" firstAttribute="leading" secondItem="rdo-Rd-p6S" secondAttribute="leading" id="fPO-Zt-GA6"/>
                                                <constraint firstAttribute="bottom" secondItem="yph-mu-DIi" secondAttribute="bottom" id="g3O-F7-uAo"/>
                                            </constraints>
                                            <connections>
                                                <outlet property="containerView" destination="yph-mu-DIi" id="CmD-KX-rDC"/>
                                            </connections>
                                        </collectionReusableView>
                                        <connections>
                                            <outlet property="dataSource" destination="Zw5-bS-R6p" id="cbG-VR-q5R"/>
                                            <outlet property="delegate" destination="Zw5-bS-R6p" id="lAL-Pi-koU"/>
                                        </connections>
                                    </collectionView>
                                </subviews>
                                <color key="backgroundColor" name="BackgroundPrimaryWhite"/>
                                <constraints>
                                    <constraint firstItem="ftX-x9-NqT" firstAttribute="top" secondItem="CkM-YU-K5D" secondAttribute="top" id="L7d-Nk-bFD"/>
                                    <constraint firstAttribute="trailing" secondItem="ftX-x9-NqT" secondAttribute="trailing" id="O8g-HG-7T2"/>
                                    <constraint firstItem="ftX-x9-NqT" firstAttribute="leading" secondItem="CkM-YU-K5D" secondAttribute="leading" id="Pg9-d9-lo6"/>
                                    <constraint firstAttribute="bottom" secondItem="ftX-x9-NqT" secondAttribute="bottom" id="a5w-LW-Xh1"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="zL4-hf-Qek"/>
                        <color key="backgroundColor" name="BackgroundPrimaryWhite"/>
                        <constraints>
                            <constraint firstItem="zL4-hf-Qek" firstAttribute="bottom" secondItem="CkM-YU-K5D" secondAttribute="bottom" id="1h0-W0-yhM"/>
                            <constraint firstItem="CkM-YU-K5D" firstAttribute="leading" secondItem="zL4-hf-Qek" secondAttribute="leading" id="idb-Lf-0u2"/>
                            <constraint firstItem="CkM-YU-K5D" firstAttribute="top" secondItem="zL4-hf-Qek" secondAttribute="top" id="rha-0o-g7L"/>
                            <constraint firstItem="zL4-hf-Qek" firstAttribute="trailing" secondItem="CkM-YU-K5D" secondAttribute="trailing" id="tWd-zU-nzl"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="AXe-qr-DCG">
                        <barButtonItem key="leftBarButtonItem" title="Back" id="A1p-RO-PwH">
                            <connections>
                                <action selector="didTapBack:" destination="Zw5-bS-R6p" id="qpy-x4-4eQ"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarBackButtonHidden" value="NO"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="baseContainerView" destination="CkM-YU-K5D" id="yVk-9j-jjJ"/>
                        <outlet property="collectionView" destination="ftX-x9-NqT" id="u4i-FU-ho0"/>
                        <outlet property="emptyView" destination="d44-ON-eEl" id="z6f-Up-o7C"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="TJx-Ty-7OL" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
                <view contentMode="scaleToFill" id="d44-ON-eEl">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="500"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cyQ-20-FmE">
                            <rect key="frame" x="48" y="97.333333333333343" width="279" height="321.33333333333326"/>
                            <subviews>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="imageMissedTarget" translatesAutoresizingMaskIntoConstraints="NO" id="Sbc-r3-fRi">
                                    <rect key="frame" x="39.666666666666686" y="0.0" width="200" height="200"/>
                                    <constraints>
                                        <constraint firstAttribute="width" secondItem="Sbc-r3-fRi" secondAttribute="height" id="oJj-qn-REe"/>
                                    </constraints>
                                </imageView>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="No featured partners available" textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WXR-Wu-t1X">
                                    <rect key="frame" x="0.0" y="200" width="279" height="58.333333333333314"/>
                                    <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="24"/>
                                    <color key="textColor" name="ContentPrimaryBlack"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="There are currently no partners available at the moment. Please check back again soon!" textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1bZ-kg-65V">
                                    <rect key="frame" x="0.0" y="266.33333333333337" width="279" height="55"/>
                                    <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="14"/>
                                    <color key="textColor" name="ContentGrey6"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstItem="1bZ-kg-65V" firstAttribute="top" secondItem="WXR-Wu-t1X" secondAttribute="bottom" constant="8" id="6Hr-Nz-XXz"/>
                                <constraint firstItem="WXR-Wu-t1X" firstAttribute="leading" secondItem="cyQ-20-FmE" secondAttribute="leading" id="DwT-JD-UJ9"/>
                                <constraint firstAttribute="trailing" secondItem="1bZ-kg-65V" secondAttribute="trailing" id="HUW-kg-NBG"/>
                                <constraint firstAttribute="bottom" secondItem="1bZ-kg-65V" secondAttribute="bottom" id="LhF-Pm-ioP"/>
                                <constraint firstItem="WXR-Wu-t1X" firstAttribute="top" secondItem="Sbc-r3-fRi" secondAttribute="bottom" id="WIK-2H-62J"/>
                                <constraint firstItem="1bZ-kg-65V" firstAttribute="leading" secondItem="cyQ-20-FmE" secondAttribute="leading" id="ayJ-OT-nrB"/>
                                <constraint firstItem="Sbc-r3-fRi" firstAttribute="centerX" secondItem="cyQ-20-FmE" secondAttribute="centerX" id="bug-CK-D0Q"/>
                                <constraint firstItem="Sbc-r3-fRi" firstAttribute="top" secondItem="cyQ-20-FmE" secondAttribute="top" id="qJJ-Aa-WV2"/>
                                <constraint firstAttribute="trailing" secondItem="WXR-Wu-t1X" secondAttribute="trailing" id="reS-8d-Bzo"/>
                            </constraints>
                        </view>
                    </subviews>
                    <viewLayoutGuide key="safeArea" id="eUq-gR-kc2"/>
                    <color key="backgroundColor" name="BackgroundPrimaryWhite"/>
                    <constraints>
                        <constraint firstItem="Sbc-r3-fRi" firstAttribute="width" secondItem="d44-ON-eEl" secondAttribute="width" multiplier="200:375" id="3qj-Cx-RFg"/>
                        <constraint firstItem="cyQ-20-FmE" firstAttribute="centerY" secondItem="eUq-gR-kc2" secondAttribute="centerY" id="FBg-sv-kLo"/>
                        <constraint firstItem="eUq-gR-kc2" firstAttribute="trailing" secondItem="cyQ-20-FmE" secondAttribute="trailing" constant="48" id="ksp-Ft-XCn"/>
                        <constraint firstItem="cyQ-20-FmE" firstAttribute="leading" secondItem="eUq-gR-kc2" secondAttribute="leading" constant="48" id="nuz-Dt-8In"/>
                    </constraints>
                </view>
            </objects>
            <point key="canvasLocation" x="-255.19999999999999" y="3576.3546798029556"/>
        </scene>
        <!--Transactions-->
        <scene sceneID="5AL-od-amU">
            <objects>
                <viewController storyboardIdentifier="MayaAllActivitiesViewController" id="A4H-Zi-GcA" customClass="MayaAllActivitiesViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Fyf-dj-oFt">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView contentMode="scaleToFill" alwaysBounceVertical="YES" contentInsetAdjustmentBehavior="never" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="56" estimatedSectionHeaderHeight="-1" sectionFooterHeight="28" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="R7I-9D-k4X">
                                <rect key="frame" x="0.0" y="50" width="375" height="728"/>
                                <color key="backgroundColor" name="Grey1"/>
                                <connections>
                                    <outlet property="dataSource" destination="A4H-Zi-GcA" id="k8u-Fw-mqf"/>
                                    <outlet property="delegate" destination="A4H-Zi-GcA" id="W2z-vL-fF7"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="N0q-dT-yQV"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="R7I-9D-k4X" firstAttribute="top" secondItem="N0q-dT-yQV" secondAttribute="top" id="c38-4s-cZM"/>
                            <constraint firstItem="R7I-9D-k4X" firstAttribute="leading" secondItem="N0q-dT-yQV" secondAttribute="leading" id="vUD-f2-hnh"/>
                            <constraint firstItem="N0q-dT-yQV" firstAttribute="bottom" secondItem="R7I-9D-k4X" secondAttribute="bottom" id="wgV-NG-Zso"/>
                            <constraint firstItem="N0q-dT-yQV" firstAttribute="trailing" secondItem="R7I-9D-k4X" secondAttribute="trailing" id="yBK-Bl-2rN"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="Transactions" id="mmi-Sa-wgW">
                        <barButtonItem key="leftBarButtonItem" title="Back" id="8ye-Xm-vPB">
                            <connections>
                                <action selector="didTapBack:" destination="A4H-Zi-GcA" id="bbB-Su-seg"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarBackButtonHidden" value="NO"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="tableView" destination="R7I-9D-k4X" id="twi-fx-daq"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="UEu-Cr-uto" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="503" y="2894"/>
        </scene>
        <!--Maya Banner Interstitial View Controller-->
        <scene sceneID="aTl-zs-019">
            <objects>
                <viewController storyboardIdentifier="MayaBannerInterstitialViewController" id="IV6-Xw-aDq" customClass="MayaBannerInterstitialViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="pWu-eR-fur">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="dO4-sB-MIN">
                                <rect key="frame" x="0.0" y="20" width="375" height="476"/>
                                <subviews>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="x7T-RP-nmx">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="56"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="68C-rr-Wbn">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="632.33333333333337"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="9jS-1T-H7n">
                                                        <rect key="frame" x="0.0" y="0.0" width="375" height="0.0"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" id="hSA-sl-Ddv"/>
                                                        </constraints>
                                                    </imageView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="O2g-Qx-IIp">
                                                        <rect key="frame" x="0.0" y="0.0" width="375" height="281"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" secondItem="O2g-Qx-IIp" secondAttribute="height" multiplier="327:245" id="Qv6-QQ-6Yv"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="zwN-x3-AUq">
                                                        <rect key="frame" x="0.0" y="281" width="375" height="351.33333333333326"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="G7M-Aw-gVm">
                                                                <rect key="frame" x="24" y="16" width="327" height="161"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalCompressionResistancePriority="751" text="Lorem ipsum dolor sit" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qpu-0d-rNa">
                                                                        <rect key="frame" x="0.0" y="0.0" width="327" height="26.333333333333332"/>
                                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Medium" family="Cerebri Sans Pro" pointSize="20"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="f0k-7L-kZi">
                                                                        <rect key="frame" x="0.0" y="161" width="327" height="0.0"/>
                                                                    </stackView>
                                                                </subviews>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="trailing" secondItem="qpu-0d-rNa" secondAttribute="trailing" id="0bi-D0-Lwa"/>
                                                                    <constraint firstItem="f0k-7L-kZi" firstAttribute="leading" secondItem="G7M-Aw-gVm" secondAttribute="leading" id="0gQ-hB-Xf0"/>
                                                                    <constraint firstItem="qpu-0d-rNa" firstAttribute="leading" secondItem="G7M-Aw-gVm" secondAttribute="leading" id="Kp8-Yi-Pkr"/>
                                                                    <constraint firstAttribute="trailing" secondItem="f0k-7L-kZi" secondAttribute="trailing" id="dzL-gS-YvI"/>
                                                                    <constraint firstItem="qpu-0d-rNa" firstAttribute="top" secondItem="G7M-Aw-gVm" secondAttribute="top" id="g5f-wJ-qan"/>
                                                                    <constraint firstItem="f0k-7L-kZi" firstAttribute="top" secondItem="qpu-0d-rNa" secondAttribute="bottom" constant="16" id="hS9-F8-LTc"/>
                                                                    <constraint firstAttribute="bottom" secondItem="f0k-7L-kZi" secondAttribute="bottom" id="rrV-P6-6Fg"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="G7M-Aw-gVm" firstAttribute="top" secondItem="zwN-x3-AUq" secondAttribute="top" constant="16" id="4cV-tW-x5w"/>
                                                            <constraint firstAttribute="bottom" secondItem="G7M-Aw-gVm" secondAttribute="bottom" constant="16" id="GeX-Y7-v0Z"/>
                                                            <constraint firstAttribute="trailing" secondItem="G7M-Aw-gVm" secondAttribute="trailing" constant="24" id="Nya-hm-LUV"/>
                                                            <constraint firstItem="G7M-Aw-gVm" firstAttribute="leading" secondItem="zwN-x3-AUq" secondAttribute="leading" constant="24" id="PX1-RY-kBo"/>
                                                        </constraints>
                                                    </view>
                                                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="PyQ-y0-jYb">
                                                        <rect key="frame" x="0.0" y="632.33333333333337" width="375" height="72"/>
                                                        <subviews>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ZkC-pM-rt5" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="24" y="0.0" width="327" height="56"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="56" id="OsP-gW-KQ5"/>
                                                                </constraints>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <connections>
                                                                    <action selector="didTapContinue" destination="IV6-Xw-aDq" eventType="touchUpInside" id="RK8-7Y-TIP"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="ZkC-pM-rt5" firstAttribute="top" secondItem="PyQ-y0-jYb" secondAttribute="top" id="11N-3Y-mmK"/>
                                                            <constraint firstItem="ZkC-pM-rt5" firstAttribute="leading" secondItem="PyQ-y0-jYb" secondAttribute="leading" constant="24" id="GlJ-ZQ-Tah"/>
                                                            <constraint firstAttribute="bottom" secondItem="ZkC-pM-rt5" secondAttribute="bottom" constant="16" id="HyT-An-RUz"/>
                                                            <constraint firstAttribute="trailing" secondItem="ZkC-pM-rt5" secondAttribute="trailing" constant="24" id="ik0-cN-tGV"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="68C-rr-Wbn" secondAttribute="trailing" id="2xx-ZB-7Va"/>
                                            <constraint firstItem="68C-rr-Wbn" firstAttribute="top" secondItem="x7T-RP-nmx" secondAttribute="top" id="Fp6-g2-R0i"/>
                                            <constraint firstAttribute="bottom" secondItem="68C-rr-Wbn" secondAttribute="bottom" id="eLU-S3-LMn"/>
                                            <constraint firstItem="68C-rr-Wbn" firstAttribute="leading" secondItem="x7T-RP-nmx" secondAttribute="leading" id="yx1-yn-s1q"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="x7T-RP-nmx" firstAttribute="leading" secondItem="CqD-sO-MUo" secondAttribute="leading" id="IAE-8C-cNB"/>
                                    <constraint firstItem="x7T-RP-nmx" firstAttribute="trailing" secondItem="CqD-sO-MUo" secondAttribute="trailing" id="OJt-uj-nxo"/>
                                    <constraint firstItem="x7T-RP-nmx" firstAttribute="top" secondItem="CqD-sO-MUo" secondAttribute="top" id="Ykz-H6-emh"/>
                                    <constraint firstItem="x7T-RP-nmx" firstAttribute="bottom" secondItem="CqD-sO-MUo" secondAttribute="bottom" id="eK7-OO-mhE"/>
                                    <constraint firstItem="x7T-RP-nmx" firstAttribute="width" secondItem="Qgg-lf-tT5" secondAttribute="width" id="wwH-6c-PHj"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="CqD-sO-MUo"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="Qgg-lf-tT5"/>
                            </scrollView>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="SlE-uj-9DR">
                                <rect key="frame" x="0.0" y="558.66666666666663" width="375" height="219.33333333333337"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="92S-eX-A2b">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="139.33333333333334"/>
                                        <subviews>
                                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" editable="NO" textAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="lmp-25-kpt">
                                                <rect key="frame" x="24" y="15.999999999999993" width="327" height="111.33333333333331"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <string key="text">By continuing, you fully understand the Terms and Conditions on the use of this service, accept all the risks involved, and acknowledge that profits and returns are not guaranteed</string>
                                                <color key="textColor" systemColor="labelColor"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                            </textView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="lmp-25-kpt" firstAttribute="leading" secondItem="92S-eX-A2b" secondAttribute="leading" constant="24" id="WCJ-bC-MRT"/>
                                            <constraint firstAttribute="bottom" secondItem="lmp-25-kpt" secondAttribute="bottom" constant="12" id="k29-rm-hJI"/>
                                            <constraint firstAttribute="trailing" secondItem="lmp-25-kpt" secondAttribute="trailing" constant="24" id="pKl-NF-Kvt"/>
                                            <constraint firstItem="lmp-25-kpt" firstAttribute="top" secondItem="92S-eX-A2b" secondAttribute="top" constant="16" id="zzs-0b-FY6"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="HHZ-tK-OUw">
                                        <rect key="frame" x="0.0" y="139.33333333333337" width="375" height="80"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="s5R-D5-CJv" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="8" width="327" height="56"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="GW9-b6-24o"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <connections>
                                                    <action selector="didTapContinue" destination="IV6-Xw-aDq" eventType="touchUpInside" id="Tjs-Ax-eQS"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="s5R-D5-CJv" secondAttribute="bottom" constant="16" id="Nvk-Ip-TG3"/>
                                            <constraint firstItem="s5R-D5-CJv" firstAttribute="leading" secondItem="HHZ-tK-OUw" secondAttribute="leading" constant="24" id="VjR-ca-CEt"/>
                                            <constraint firstAttribute="trailing" secondItem="s5R-D5-CJv" secondAttribute="trailing" constant="24" id="Xdj-Tb-JjV"/>
                                            <constraint firstItem="s5R-D5-CJv" firstAttribute="top" secondItem="HHZ-tK-OUw" secondAttribute="top" constant="8" id="boJ-kA-RzK"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="J1L-3I-gck"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="J1L-3I-gck" firstAttribute="bottom" secondItem="SlE-uj-9DR" secondAttribute="bottom" id="1FT-fL-b14"/>
                            <constraint firstItem="J1L-3I-gck" firstAttribute="trailing" secondItem="SlE-uj-9DR" secondAttribute="trailing" id="dN4-Ti-hMo"/>
                            <constraint firstItem="dO4-sB-MIN" firstAttribute="top" secondItem="J1L-3I-gck" secondAttribute="top" id="edx-Ev-1NG"/>
                            <constraint firstItem="SlE-uj-9DR" firstAttribute="leading" secondItem="J1L-3I-gck" secondAttribute="leading" id="f6g-i0-eFh"/>
                            <constraint firstItem="dO4-sB-MIN" firstAttribute="leading" secondItem="J1L-3I-gck" secondAttribute="leading" id="oYF-MJ-R88"/>
                            <constraint firstItem="J1L-3I-gck" firstAttribute="trailing" secondItem="dO4-sB-MIN" secondAttribute="trailing" id="q66-Fu-GwP"/>
                            <constraint firstItem="SlE-uj-9DR" firstAttribute="top" secondItem="dO4-sB-MIN" secondAttribute="bottom" id="txV-gZ-nVd"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="CdL-3z-KTT">
                        <barButtonItem key="leftBarButtonItem" title="Back" id="pZP-Db-OoB">
                            <connections>
                                <action selector="didTapBack:" destination="IV6-Xw-aDq" id="u3k-Kh-5rW"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="adCarouselView" destination="O2g-Qx-IIp" id="dZv-iy-qMa"/>
                        <outlet property="iconImageView" destination="9jS-1T-H7n" id="wU6-HO-caR"/>
                        <outlet property="messageStackView" destination="f0k-7L-kZi" id="c0x-Da-tmt"/>
                        <outlet property="scrollView" destination="dO4-sB-MIN" id="2ga-sw-jKZ"/>
                        <outlet property="scrollViewContainerView" destination="x7T-RP-nmx" id="aj0-q7-4mf"/>
                        <outlet property="scrollingContinueButton" destination="ZkC-pM-rt5" id="oEV-ez-bBN"/>
                        <outlet property="scrollingContinueContainerView" destination="PyQ-y0-jYb" id="Auk-8X-XYT"/>
                        <outlet property="stickyContinueButton" destination="s5R-D5-CJv" id="juI-td-9R1"/>
                        <outlet property="stickyContinueContainerView" destination="HHZ-tK-OUw" id="6bK-W0-HMV"/>
                        <outlet property="stickyTermsContainerView" destination="92S-eX-A2b" id="B80-Ym-eB9"/>
                        <outlet property="stickyTermsTextView" destination="lmp-25-kpt" id="G1l-vK-1wh"/>
                        <outlet property="titleLabel" destination="qpu-0d-rNa" id="kyg-8D-SjC"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="uyr-35-zqj" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="501.60000000000002" y="3576.3546798029556"/>
        </scene>
        <!--Maya Rewards View Controller-->
        <scene sceneID="CAl-0y-a50">
            <objects>
                <viewController storyboardIdentifier="MayaRewardsViewController" id="Pzi-UZ-0TJ" customClass="MayaRewardsViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="BlP-kF-EZB">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" text="Get rewards" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6Cr-Gv-nNi">
                                <rect key="frame" x="0.0" y="50" width="108" height="22"/>
                                <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="18"/>
                                <color key="textColor" name="ContentPrimaryBlack"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" bounces="NO" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" bouncesZoom="NO" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="KhA-2r-fUs">
                                <rect key="frame" x="0.0" y="88" width="375" height="724"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="12" minimumInteritemSpacing="12" id="6S7-1R-TKC">
                                    <size key="itemSize" width="156" height="76"/>
                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                </collectionViewFlowLayout>
                                <cells>
                                    <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="MayaRewardCollectionViewCell" id="O9n-MY-b39" customClass="MayaRewardCollectionViewCell" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="156" height="76"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <collectionViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="uYE-2e-g3K">
                                            <rect key="frame" x="0.0" y="0.0" width="156" height="76"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                            <subviews>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="walletRewardsMissionIconV2" translatesAutoresizingMaskIntoConstraints="NO" id="R9N-7t-7Au">
                                                    <rect key="frame" x="80" y="2" width="128" height="128"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" secondItem="R9N-7t-7Au" secondAttribute="height" multiplier="1:1" id="l2w-Gw-Gxn"/>
                                                        <constraint firstAttribute="width" constant="128" id="ooQ-m3-8YR"/>
                                                    </constraints>
                                                </imageView>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" text="Missions" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="oKZ-75-ouE">
                                                    <rect key="frame" x="16" y="12" width="66" height="19"/>
                                                    <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="16"/>
                                                    <color key="textColor" name="ContentPrimaryWhite"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" text="Earn rewards for completing tasks" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WeS-YY-qRx">
                                                    <rect key="frame" x="16" y="35" width="124" height="26.333333333333329"/>
                                                    <fontDescription key="fontDescription" name="CerebriSansPro-Book" family="Cerebri Sans Pro" pointSize="10"/>
                                                    <color key="textColor" name="ContentPrimaryWhite"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="R9N-7t-7Au" firstAttribute="leading" secondItem="uYE-2e-g3K" secondAttribute="leading" constant="80" id="2Fm-Vn-bR3"/>
                                                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="oKZ-75-ouE" secondAttribute="trailing" constant="16" id="AT8-Xf-5r4"/>
                                                <constraint firstItem="oKZ-75-ouE" firstAttribute="top" secondItem="uYE-2e-g3K" secondAttribute="top" constant="12" id="MRs-Ts-nBw"/>
                                                <constraint firstItem="WeS-YY-qRx" firstAttribute="top" secondItem="oKZ-75-ouE" secondAttribute="bottom" constant="4" id="Zdb-3Z-seu"/>
                                                <constraint firstItem="WeS-YY-qRx" firstAttribute="leading" secondItem="uYE-2e-g3K" secondAttribute="leading" constant="16" id="dfz-xu-Ixl"/>
                                                <constraint firstItem="R9N-7t-7Au" firstAttribute="top" secondItem="uYE-2e-g3K" secondAttribute="top" constant="2" id="gRv-xV-X6d"/>
                                                <constraint firstAttribute="trailing" secondItem="WeS-YY-qRx" secondAttribute="trailing" constant="16" id="rNo-pK-Bzv"/>
                                                <constraint firstItem="oKZ-75-ouE" firstAttribute="leading" secondItem="uYE-2e-g3K" secondAttribute="leading" constant="16" id="yeX-Mm-xuN"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                    <integer key="value" value="16"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </collectionViewCellContentView>
                                        <connections>
                                            <outlet property="imageView" destination="R9N-7t-7Au" id="CqX-Js-foj"/>
                                            <outlet property="subtitleLabel" destination="WeS-YY-qRx" id="wts-qd-HL1"/>
                                            <outlet property="titleLabel" destination="oKZ-75-ouE" id="QgK-qG-rhG"/>
                                        </connections>
                                    </collectionViewCell>
                                </cells>
                                <connections>
                                    <outlet property="dataSource" destination="Pzi-UZ-0TJ" id="OUX-Wf-U7L"/>
                                    <outlet property="delegate" destination="Pzi-UZ-0TJ" id="nPF-NW-wDD"/>
                                </connections>
                            </collectionView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="1Gx-Jm-FmI"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="1Gx-Jm-FmI" firstAttribute="trailing" secondItem="KhA-2r-fUs" secondAttribute="trailing" id="02N-TH-uAo"/>
                            <constraint firstItem="6Cr-Gv-nNi" firstAttribute="top" secondItem="1Gx-Jm-FmI" secondAttribute="top" id="9rb-3a-uqV"/>
                            <constraint firstItem="1Gx-Jm-FmI" firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="6Cr-Gv-nNi" secondAttribute="trailing" id="GxX-2h-ygG"/>
                            <constraint firstItem="KhA-2r-fUs" firstAttribute="top" secondItem="6Cr-Gv-nNi" secondAttribute="bottom" constant="16" id="Rh0-O0-5Gj"/>
                            <constraint firstItem="6Cr-Gv-nNi" firstAttribute="leading" secondItem="1Gx-Jm-FmI" secondAttribute="leading" id="SzG-3u-u2N"/>
                            <constraint firstItem="KhA-2r-fUs" firstAttribute="leading" secondItem="1Gx-Jm-FmI" secondAttribute="leading" id="XPa-mr-QSD"/>
                            <constraint firstAttribute="bottom" secondItem="KhA-2r-fUs" secondAttribute="bottom" id="zp1-H5-ohT"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="A8J-2c-TcU" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-255" y="2893"/>
        </scene>
        <!--Maya Wallet Services View Controller-->
        <scene sceneID="Kgq-aL-J8B">
            <objects>
                <viewController storyboardIdentifier="MayaWalletServicesViewController" id="gms-Vn-N93" customClass="MayaWalletServicesViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" ambiguous="YES" id="lDu-VH-Urz">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" bounces="NO" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" bouncesZoom="NO" dataMode="prototypes" prefetchingEnabled="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ojt-ht-5fB">
                                <rect key="frame" x="24" y="0.0" width="327" height="218"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="218" placeholder="YES" id="zZR-pg-syG"/>
                                </constraints>
                                <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="10" minimumInteritemSpacing="10" id="5zL-yi-yjs">
                                    <size key="itemSize" width="64" height="104"/>
                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                </collectionViewFlowLayout>
                                <cells/>
                                <connections>
                                    <outlet property="dataSource" destination="gms-Vn-N93" id="KkF-Ns-EuH"/>
                                    <outlet property="delegate" destination="gms-Vn-N93" id="Mqg-es-WoA"/>
                                </connections>
                            </collectionView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="hwO-AZ-1GG"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="ojt-ht-5fB" firstAttribute="leading" secondItem="lDu-VH-Urz" secondAttribute="leading" constant="24" id="CCV-sZ-vzd"/>
                            <constraint firstAttribute="bottom" secondItem="ojt-ht-5fB" secondAttribute="bottom" id="CFB-fu-zlj"/>
                            <constraint firstAttribute="trailing" secondItem="ojt-ht-5fB" secondAttribute="trailing" constant="24" id="P01-RF-Lbw"/>
                            <constraint firstItem="ojt-ht-5fB" firstAttribute="top" secondItem="lDu-VH-Urz" secondAttribute="top" id="kLr-MS-jUM"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="collectionView" destination="ojt-ht-5fB" id="yUd-xa-Xrn"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="FW7-cY-cjd" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-997.5" y="2175"/>
        </scene>
        <!--Maya Activity Details View Controller-->
        <scene sceneID="ASU-L7-0WH">
            <objects>
                <viewController storyboardIdentifier="MayaActivityDetailsViewController" id="mln-cF-Kz1" customClass="MayaActivityDetailsViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="nZp-Ko-ONT">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="m0f-Ny-Ffp">
                                <rect key="frame" x="0.0" y="50" width="375" height="728"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="hXw-30-YaL">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="50"/>
                                        <color key="backgroundColor" name="BackgroundGrey1"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="50" placeholder="YES" id="Mwj-kQ-a87"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="hXw-30-YaL" firstAttribute="top" secondItem="Tcv-kx-EGU" secondAttribute="top" id="18M-wy-Cr2"/>
                                    <constraint firstItem="hXw-30-YaL" firstAttribute="trailing" secondItem="Tcv-kx-EGU" secondAttribute="trailing" id="Hgw-OL-3wY"/>
                                    <constraint firstItem="hXw-30-YaL" firstAttribute="width" secondItem="xiz-Zk-RPB" secondAttribute="width" id="LdI-Bh-aIf"/>
                                    <constraint firstItem="hXw-30-YaL" firstAttribute="bottom" secondItem="Tcv-kx-EGU" secondAttribute="bottom" id="SHB-l1-dfC"/>
                                    <constraint firstItem="hXw-30-YaL" firstAttribute="leading" secondItem="Tcv-kx-EGU" secondAttribute="leading" id="iKV-wA-qt5"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="Tcv-kx-EGU"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="xiz-Zk-RPB"/>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="79Y-Gg-iQ5"/>
                        <color key="backgroundColor" name="BackgroundGrey1"/>
                        <constraints>
                            <constraint firstItem="m0f-Ny-Ffp" firstAttribute="leading" secondItem="79Y-Gg-iQ5" secondAttribute="leading" id="3RN-Ke-nsF"/>
                            <constraint firstItem="79Y-Gg-iQ5" firstAttribute="trailing" secondItem="m0f-Ny-Ffp" secondAttribute="trailing" id="EpC-JI-fh1"/>
                            <constraint firstItem="79Y-Gg-iQ5" firstAttribute="bottom" secondItem="m0f-Ny-Ffp" secondAttribute="bottom" id="P2j-QE-5o5"/>
                            <constraint firstItem="m0f-Ny-Ffp" firstAttribute="top" secondItem="79Y-Gg-iQ5" secondAttribute="top" id="kx3-cV-vOf"/>
                        </constraints>
                    </view>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="navigationBarHidden" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outlet property="mainStackView" destination="hXw-30-YaL" id="Q8F-xj-TBX"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="8CV-QF-j0a" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2743" y="2177"/>
        </scene>
        <!--Maya DashboardV2 View Controller-->
        <scene sceneID="w2a-ag-Euv">
            <objects>
                <viewController storyboardIdentifier="MayaDashboardV2ViewController" modalTransitionStyle="crossDissolve" modalPresentationStyle="overFullScreen" id="xJw-yc-0eA" customClass="MayaDashboardV2ViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="wu9-mk-ooA">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="1300"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView multipleTouchEnabled="YES" contentMode="scaleToFill" ambiguous="YES" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" contentInsetAdjustmentBehavior="never" bouncesZoom="NO" translatesAutoresizingMaskIntoConstraints="NO" id="VXp-eb-UqM">
                                <rect key="frame" x="0.0" y="0.0" width="320" height="1300"/>
                                <subviews>
                                    <view contentMode="scaleToFill" ambiguous="YES" placeholderIntrinsicWidth="infinite" placeholderIntrinsicHeight="766" translatesAutoresizingMaskIntoConstraints="NO" id="VAq-yt-Q6Q">
                                        <rect key="frame" x="0.0" y="0.0" width="320" height="1566"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" alignment="center" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="SX7-2j-wLP">
                                                <rect key="frame" x="0.0" y="0.0" width="320" height="1918"/>
                                                <subviews>
                                                    <view hidden="YES" clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="IPG-20-037" customClass="MayaSnackBarView" customModule="PayMaya" customModuleProvider="target">
                                                        <rect key="frame" x="24" y="0.0" width="272" height="0.0"/>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" alignment="center" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="9ug-JV-cjY">
                                                        <rect key="frame" x="0.0" y="0.0" width="320" height="1918"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" spacing="-14" translatesAutoresizingMaskIntoConstraints="NO" id="aXz-El-nrW" userLabel="Wallet Balance Stack View">
                                                                <rect key="frame" x="24" y="0.0" width="272" height="382"/>
                                                                <subviews>
                                                                    <containerView opaque="NO" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="K8i-68-ISC">
                                                                        <rect key="frame" x="0.0" y="0.0" width="272" height="154"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" priority="250" constant="154" id="Ocw-ji-miU"/>
                                                                        </constraints>
                                                                    </containerView>
                                                                    <containerView opaque="NO" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="QTQ-ov-jQx">
                                                                        <rect key="frame" x="0.0" y="140" width="272" height="128"/>
                                                                    </containerView>
                                                                    <containerView opaque="NO" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="eVw-xw-wsg" userLabel="Easy Credit Application View">
                                                                        <rect key="frame" x="0.0" y="254" width="272" height="128"/>
                                                                    </containerView>
                                                                </subviews>
                                                            </stackView>
                                                            <containerView opaque="NO" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="ABF-ZR-cYE">
                                                                <rect key="frame" x="0.0" y="406" width="320" height="218"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="218" id="LUr-Kd-Plh"/>
                                                                </constraints>
                                                            </containerView>
                                                            <view opaque="NO" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Bzt-NY-G19">
                                                                <rect key="frame" x="0.0" y="648" width="320" height="100"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="100" id="7hr-3z-1jY"/>
                                                                </constraints>
                                                            </view>
                                                            <containerView opaque="NO" contentMode="scaleToFill" ambiguous="YES" placeholderIntrinsicWidth="infinite" placeholderIntrinsicHeight="148" translatesAutoresizingMaskIntoConstraints="NO" id="eIc-OH-N2j">
                                                                <rect key="frame" x="0.0" y="772" width="320" height="148"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="148" id="ots-zJ-aHg"/>
                                                                </constraints>
                                                            </containerView>
                                                            <containerView opaque="NO" contentMode="scaleToFill" ambiguous="YES" placeholderIntrinsicWidth="infinite" placeholderIntrinsicHeight="200" translatesAutoresizingMaskIntoConstraints="NO" id="QQh-ve-CT8">
                                                                <rect key="frame" x="24" y="944" width="272" height="200"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="200" id="lWt-It-ota"/>
                                                                </constraints>
                                                            </containerView>
                                                            <containerView opaque="NO" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="SSq-Ut-tAk">
                                                                <rect key="frame" x="24" y="1168" width="272" height="412"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="412" id="mh8-2s-TXY"/>
                                                                </constraints>
                                                            </containerView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="oMV-gs-GFq" customClass="MayaBSPView" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="40" y="1604" width="240" height="200"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            </stackView>
                                                            <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="jKD-l5-E6h" userLabel="Spacer View">
                                                                <rect key="frame" x="24" y="1828" width="272" height="90"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="90" id="5gi-hC-1vM"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="QQh-ve-CT8" secondAttribute="trailing" constant="24" id="1wx-I2-JGz"/>
                                                            <constraint firstItem="Bzt-NY-G19" firstAttribute="leading" secondItem="9ug-JV-cjY" secondAttribute="leading" id="5XK-OY-dYl"/>
                                                            <constraint firstAttribute="trailing" secondItem="SSq-Ut-tAk" secondAttribute="trailing" constant="24" id="7Dh-la-d78"/>
                                                            <constraint firstAttribute="trailing" secondItem="aXz-El-nrW" secondAttribute="trailing" constant="24" id="Bcb-uv-Len"/>
                                                            <constraint firstItem="QQh-ve-CT8" firstAttribute="leading" secondItem="9ug-JV-cjY" secondAttribute="leading" constant="24" id="ClE-DW-xYC"/>
                                                            <constraint firstItem="jKD-l5-E6h" firstAttribute="leading" secondItem="9ug-JV-cjY" secondAttribute="leading" constant="24" id="IGG-By-6Ei"/>
                                                            <constraint firstAttribute="trailing" secondItem="eIc-OH-N2j" secondAttribute="trailing" id="Pe9-VG-Xjr"/>
                                                            <constraint firstAttribute="trailing" secondItem="ABF-ZR-cYE" secondAttribute="trailing" id="Q1W-ye-fYZ"/>
                                                            <constraint firstItem="oMV-gs-GFq" firstAttribute="trailing" secondItem="QQh-ve-CT8" secondAttribute="trailing" constant="-16" id="Roe-Cr-Fhj"/>
                                                            <constraint firstItem="SSq-Ut-tAk" firstAttribute="leading" secondItem="9ug-JV-cjY" secondAttribute="leading" constant="24" id="W0s-Ss-LaG"/>
                                                            <constraint firstAttribute="trailing" secondItem="jKD-l5-E6h" secondAttribute="trailing" constant="24" id="cer-hK-nIv"/>
                                                            <constraint firstItem="oMV-gs-GFq" firstAttribute="leading" secondItem="QQh-ve-CT8" secondAttribute="leading" constant="16" id="efd-4j-PqS"/>
                                                            <constraint firstItem="ABF-ZR-cYE" firstAttribute="leading" secondItem="9ug-JV-cjY" secondAttribute="leading" id="mr7-AN-ufo"/>
                                                            <constraint firstItem="eIc-OH-N2j" firstAttribute="leading" secondItem="9ug-JV-cjY" secondAttribute="leading" id="p1L-hh-4ZR"/>
                                                            <constraint firstItem="aXz-El-nrW" firstAttribute="leading" secondItem="9ug-JV-cjY" secondAttribute="leading" constant="24" id="xdw-GA-NsS"/>
                                                            <constraint firstAttribute="trailing" secondItem="Bzt-NY-G19" secondAttribute="trailing" id="zXC-bv-tBJ"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="IPG-20-037" firstAttribute="leading" secondItem="SX7-2j-wLP" secondAttribute="leading" constant="24" id="EHJ-M5-HdY"/>
                                                    <constraint firstAttribute="trailing" secondItem="9ug-JV-cjY" secondAttribute="trailing" id="Jfb-KN-GOd"/>
                                                    <constraint firstAttribute="trailing" secondItem="IPG-20-037" secondAttribute="trailing" constant="24" id="QHH-Qh-rD6"/>
                                                    <constraint firstItem="9ug-JV-cjY" firstAttribute="leading" secondItem="SX7-2j-wLP" secondAttribute="leading" id="f6M-eo-Fai"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="SX7-2j-wLP" secondAttribute="trailing" id="7u0-bx-bcp"/>
                                            <constraint firstItem="SX7-2j-wLP" firstAttribute="leading" secondItem="VAq-yt-Q6Q" secondAttribute="leading" id="CA3-ui-NiE"/>
                                            <constraint firstAttribute="bottom" secondItem="SX7-2j-wLP" secondAttribute="bottom" id="V3F-go-cxI"/>
                                            <constraint firstItem="SX7-2j-wLP" firstAttribute="top" secondItem="VAq-yt-Q6Q" secondAttribute="top" id="vRS-Ru-V4g"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pJL-gf-NhM" customClass="MayaRefreshControl" customModule="PayMaya" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="-46" width="320" height="46"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="46" id="6G3-bS-bDS"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="VAq-yt-Q6Q" firstAttribute="leading" secondItem="VXp-eb-UqM" secondAttribute="leading" id="5QH-Eu-nFU"/>
                                    <constraint firstItem="VAq-yt-Q6Q" firstAttribute="top" secondItem="pJL-gf-NhM" secondAttribute="bottom" id="B51-tG-tDc"/>
                                    <constraint firstItem="VAq-yt-Q6Q" firstAttribute="width" secondItem="VXp-eb-UqM" secondAttribute="width" id="QgR-wB-Nd2"/>
                                    <constraint firstItem="pJL-gf-NhM" firstAttribute="leading" secondItem="VXp-eb-UqM" secondAttribute="leading" id="S2M-zu-aL6"/>
                                    <constraint firstItem="VAq-yt-Q6Q" firstAttribute="top" secondItem="VXp-eb-UqM" secondAttribute="top" id="STe-f0-3HO"/>
                                    <constraint firstAttribute="trailing" secondItem="VAq-yt-Q6Q" secondAttribute="trailing" id="bfT-Gk-p8R"/>
                                    <constraint firstAttribute="trailing" secondItem="pJL-gf-NhM" secondAttribute="trailing" id="iDM-GR-gDs"/>
                                    <constraint firstItem="pJL-gf-NhM" firstAttribute="width" secondItem="VXp-eb-UqM" secondAttribute="width" id="nVC-tV-UUT"/>
                                    <constraint firstAttribute="bottom" secondItem="VAq-yt-Q6Q" secondAttribute="bottom" id="x0L-8f-ff8"/>
                                </constraints>
                                <connections>
                                    <outlet property="delegate" destination="xJw-yc-0eA" id="jBy-cA-rav"/>
                                </connections>
                            </scrollView>
                            <containerView hidden="YES" opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zAl-oc-wg1" customClass="MayaDefaultErrorView" customModule="PayMaya" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="320" height="1193"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="color" keyPath="contentBackgroundColor">
                                        <color key="value" name="Grey1"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </containerView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="H0v-Ay-Txu"/>
                        <color key="backgroundColor" name="Grey1"/>
                        <constraints>
                            <constraint firstItem="VXp-eb-UqM" firstAttribute="leading" secondItem="wu9-mk-ooA" secondAttribute="leading" id="0Ao-Wc-9x0"/>
                            <constraint firstItem="zAl-oc-wg1" firstAttribute="bottom" secondItem="wu9-mk-ooA" secondAttribute="bottom" constant="-107" id="4b8-nQ-L58"/>
                            <constraint firstItem="zAl-oc-wg1" firstAttribute="leading" secondItem="wu9-mk-ooA" secondAttribute="leading" id="SOa-BS-eAB"/>
                            <constraint firstItem="zAl-oc-wg1" firstAttribute="trailing" secondItem="wu9-mk-ooA" secondAttribute="trailing" id="Uxq-BJ-QK3"/>
                            <constraint firstAttribute="top" secondItem="VXp-eb-UqM" secondAttribute="top" id="Zf2-Wc-yzW"/>
                            <constraint firstAttribute="trailing" secondItem="VXp-eb-UqM" secondAttribute="trailing" id="aQq-FG-j1Q"/>
                            <constraint firstItem="VXp-eb-UqM" firstAttribute="bottom" secondItem="wu9-mk-ooA" secondAttribute="bottom" id="mID-NB-M3k"/>
                            <constraint firstItem="zAl-oc-wg1" firstAttribute="top" secondItem="wu9-mk-ooA" secondAttribute="top" id="uac-od-FV6"/>
                        </constraints>
                    </view>
                    <nil key="simulatedTopBarMetrics"/>
                    <nil key="simulatedBottomBarMetrics"/>
                    <size key="freeformSize" width="320" height="1300"/>
                    <connections>
                        <outlet property="activityContainerViewHeightConstraint" destination="mh8-2s-TXY" id="dba-Mm-JRo"/>
                        <outlet property="adCarouselView" destination="eIc-OH-N2j" id="Elg-88-27H"/>
                        <outlet property="adCarouselViewHeightConstraint" destination="ots-zJ-aHg" id="8Ix-t5-W6S"/>
                        <outlet property="alertSnackBar" destination="IPG-20-037" id="f28-G2-rit"/>
                        <outlet property="bspFooterView" destination="oMV-gs-GFq" id="h1h-7C-oi8"/>
                        <outlet property="dashboardStackView" destination="9ug-JV-cjY" id="nxd-Il-MZR"/>
                        <outlet property="easyCreditApplicationView" destination="eVw-xw-wsg" id="uQ3-m8-VKF"/>
                        <outlet property="easyCreditView" destination="QTQ-ov-jQx" id="NDb-OI-gRM"/>
                        <outlet property="googleAdCarouselView" destination="Bzt-NY-G19" id="YQ7-ww-hJv"/>
                        <outlet property="latestActivitiesView" destination="SSq-Ut-tAk" id="5eu-Gi-AVy"/>
                        <outlet property="mainScrollView" destination="VXp-eb-UqM" id="FXV-Zw-8eF"/>
                        <outlet property="mainStackView" destination="SX7-2j-wLP" id="Rhp-fT-Q8O"/>
                        <outlet property="refreshControl" destination="pJL-gf-NhM" id="hwZ-zf-Ci0"/>
                        <outlet property="rewardsView" destination="QQh-ve-CT8" id="lQZ-Se-UbF"/>
                        <outlet property="rewardsViewHeightConstraint" destination="lWt-It-ota" id="hRE-iY-WWU"/>
                        <outlet property="serverErrorView" destination="zAl-oc-wg1" id="tgC-QQ-7xK"/>
                        <outlet property="tilesView" destination="ABF-ZR-cYE" id="uJV-MS-QY3"/>
                        <outlet property="walletBalanceHeightConstraint" destination="Ocw-ji-miU" id="8zn-i5-8an"/>
                        <outlet property="walletBalanceStackView" destination="aXz-El-nrW" id="cCK-Yz-PxT"/>
                        <outlet property="walletBalanceView" destination="K8i-68-ISC" id="AFA-56-fhb"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="E3v-go-Bn9" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="473" y="358"/>
        </scene>
        <!--Maya Credit Application Banner View Controller-->
        <scene sceneID="2qu-qv-hsY">
            <objects>
                <viewController storyboardIdentifier="MayaCreditApplicationBannerViewController" id="7Jg-Kz-Kla" customClass="MayaCreditApplicationBannerViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="EPp-d5-rCK">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="500"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xJ3-6k-9gG" userLabel="Easy Credit Card Container">
                                <rect key="frame" x="0.0" y="50" width="375" height="82"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="heX-3w-lXT">
                                        <rect key="frame" x="16" y="28" width="343" height="40"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="DSN-sS-3EF">
                                                <rect key="frame" x="0.0" y="0.0" width="103" height="40"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" text="Easy Credit" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="10P-ux-q8y" userLabel="Easy Credit Label">
                                                        <rect key="frame" x="0.0" y="0.0" width="103" height="28"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="28" id="P5k-82-vHJ"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="18"/>
                                                        <color key="textColor" name="PrimaryBlack"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Borrow up to ₱30K" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="e9f-8n-uAp">
                                                        <rect key="frame" x="0.0" y="28" width="103" height="12"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="12" id="reb-PY-4nj"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Regular" family="Cerebri Sans Pro" pointSize="12"/>
                                                        <color key="textColor" name="Grey5"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kM2-Mb-ngf" customClass="View" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="248" y="6" width="95" height="28"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Get it now" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KVy-y6-03j">
                                                        <rect key="frame" x="12" y="4" width="71" height="20"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="20" id="iEY-ey-FFC"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="CerebriSansPro-Medium" family="Cerebri Sans Pro" pointSize="14"/>
                                                        <color key="textColor" name="PrimaryWhite"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" name="ButtonDisabledPrimaryGreen"/>
                                                <constraints>
                                                    <constraint firstItem="KVy-y6-03j" firstAttribute="top" secondItem="kM2-Mb-ngf" secondAttribute="top" constant="4" id="FV6-or-yrq"/>
                                                    <constraint firstAttribute="trailing" secondItem="KVy-y6-03j" secondAttribute="trailing" constant="12" id="Gkt-Ru-NbK"/>
                                                    <constraint firstAttribute="bottom" secondItem="KVy-y6-03j" secondAttribute="bottom" constant="4" id="Ol7-pa-L5Q"/>
                                                    <constraint firstItem="KVy-y6-03j" firstAttribute="leading" secondItem="kM2-Mb-ngf" secondAttribute="leading" constant="12" id="YQw-d9-V47"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderRadius">
                                                        <real key="value" value="10"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" name="Grey3"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="heX-3w-lXT" secondAttribute="bottom" constant="14" id="6IE-TF-Dqd"/>
                                    <constraint firstAttribute="trailing" secondItem="heX-3w-lXT" secondAttribute="trailing" constant="16" id="FkR-UM-PeD"/>
                                    <constraint firstItem="heX-3w-lXT" firstAttribute="top" secondItem="xJ3-6k-9gG" secondAttribute="top" constant="28" id="soJ-Aa-MCA"/>
                                    <constraint firstItem="heX-3w-lXT" firstAttribute="leading" secondItem="xJ3-6k-9gG" secondAttribute="leading" constant="16" id="yqY-I3-sq2"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="skeletonCornerRadius">
                                        <real key="value" value="0.0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <outletCollection property="gestureRecognizers" destination="Hkr-MJ-aPV" appends="YES" id="Vv2-Io-W6d"/>
                                </connections>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6J9-I1-G8i"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="6J9-I1-G8i" firstAttribute="trailing" secondItem="xJ3-6k-9gG" secondAttribute="trailing" id="CHa-F9-WJ6"/>
                            <constraint firstItem="6J9-I1-G8i" firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="xJ3-6k-9gG" secondAttribute="bottom" id="hep-e5-ahp"/>
                            <constraint firstItem="xJ3-6k-9gG" firstAttribute="leading" secondItem="6J9-I1-G8i" secondAttribute="leading" id="lz9-CM-pQC"/>
                            <constraint firstItem="xJ3-6k-9gG" firstAttribute="top" secondItem="6J9-I1-G8i" secondAttribute="top" id="xBq-Is-ytH"/>
                        </constraints>
                    </view>
                    <size key="freeformSize" width="375" height="500"/>
                    <connections>
                        <outlet property="easyCreditCardContainer" destination="xJ3-6k-9gG" id="ica-0E-ndE"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="CdE-xS-Gyb" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
                <tapGestureRecognizer id="Hkr-MJ-aPV">
                    <connections>
                        <action selector="didTapCreditApplicationBanner:" destination="7Jg-Kz-Kla" id="YBb-hu-0N6"/>
                    </connections>
                </tapGestureRecognizer>
            </objects>
            <point key="canvasLocation" x="-1743" y="1739"/>
        </scene>
        <!--Maya Interstitial View Controller-->
        <scene sceneID="pTX-4C-4pB">
            <objects>
                <viewController storyboardIdentifier="MayaInterstitialViewController" id="yoZ-Xv-LIB" customClass="MayaInterstitialViewController" customModule="PayMaya" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="umK-m9-n17">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="URq-Oc-h9X">
                                <rect key="frame" x="0.0" y="20" width="375" height="476"/>
                                <subviews>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="HNV-Ae-Fui">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="56"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="ErX-cE-xvo">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="316"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="s7X-sb-zjV">
                                                        <rect key="frame" x="0.0" y="0.0" width="375" height="316"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="2tb-Eg-6RN">
                                                                <rect key="frame" x="24" y="16" width="327" height="161"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="YbG-2T-QNA">
                                                                        <rect key="frame" x="0.0" y="0.0" width="174.33333333333334" height="174.33333333333334"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" secondItem="YbG-2T-QNA" secondAttribute="height" multiplier="1:1" id="nTQ-Ug-G2P"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalCompressionResistancePriority="751" text="Lorem ipsum dolor sit" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tu3-1t-WkN">
                                                                        <rect key="frame" x="0.0" y="190.33333333333331" width="327" height="77.666666666666686"/>
                                                                        <fontDescription key="fontDescription" name="Jeko-Bold" family="Jeko" pointSize="32"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="iS7-ZX-7Kg">
                                                                        <rect key="frame" x="0.0" y="161" width="327" height="0.0"/>
                                                                    </stackView>
                                                                </subviews>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="trailing" secondItem="tu3-1t-WkN" secondAttribute="trailing" id="BJK-l6-4RK"/>
                                                                    <constraint firstItem="YbG-2T-QNA" firstAttribute="top" secondItem="2tb-Eg-6RN" secondAttribute="top" id="O8O-Kc-QAx"/>
                                                                    <constraint firstItem="iS7-ZX-7Kg" firstAttribute="top" secondItem="tu3-1t-WkN" secondAttribute="bottom" constant="16" id="eOf-qG-M0o"/>
                                                                    <constraint firstAttribute="bottom" secondItem="iS7-ZX-7Kg" secondAttribute="bottom" id="eqg-bP-CgW"/>
                                                                    <constraint firstItem="YbG-2T-QNA" firstAttribute="width" secondItem="2tb-Eg-6RN" secondAttribute="width" multiplier="0.533333" id="evU-nR-tho"/>
                                                                    <constraint firstItem="tu3-1t-WkN" firstAttribute="top" secondItem="YbG-2T-QNA" secondAttribute="bottom" constant="16" id="fdJ-qU-onu"/>
                                                                    <constraint firstItem="YbG-2T-QNA" firstAttribute="leading" secondItem="2tb-Eg-6RN" secondAttribute="leading" id="jh2-ld-QoR"/>
                                                                    <constraint firstAttribute="trailing" secondItem="iS7-ZX-7Kg" secondAttribute="trailing" id="lHv-J7-aQi"/>
                                                                    <constraint firstItem="iS7-ZX-7Kg" firstAttribute="leading" secondItem="2tb-Eg-6RN" secondAttribute="leading" id="oI8-rI-HZh"/>
                                                                    <constraint firstItem="tu3-1t-WkN" firstAttribute="leading" secondItem="2tb-Eg-6RN" secondAttribute="leading" id="yHd-Hh-UrP"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="2tb-Eg-6RN" firstAttribute="top" secondItem="s7X-sb-zjV" secondAttribute="top" constant="16" id="2U5-uw-3od"/>
                                                            <constraint firstAttribute="trailing" secondItem="2tb-Eg-6RN" secondAttribute="trailing" constant="24" id="Zgn-Jf-zfq"/>
                                                            <constraint firstItem="2tb-Eg-6RN" firstAttribute="leading" secondItem="s7X-sb-zjV" secondAttribute="leading" constant="24" id="duK-uy-VUn"/>
                                                            <constraint firstAttribute="bottom" secondItem="2tb-Eg-6RN" secondAttribute="bottom" constant="16" id="pip-Ew-4Xd"/>
                                                        </constraints>
                                                    </view>
                                                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3HF-ZM-0w0">
                                                        <rect key="frame" x="0.0" y="0.0" width="375" height="72"/>
                                                        <subviews>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cKb-dQ-NVK" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                                <rect key="frame" x="24" y="0.0" width="327" height="56"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="56" id="Xm7-jd-rgB"/>
                                                                </constraints>
                                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                <connections>
                                                                    <action selector="didTapContinue" destination="yoZ-Xv-LIB" eventType="touchUpInside" id="Fhh-dk-fSb"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="cKb-dQ-NVK" firstAttribute="top" secondItem="3HF-ZM-0w0" secondAttribute="top" id="FMf-k9-iWK"/>
                                                            <constraint firstItem="cKb-dQ-NVK" firstAttribute="leading" secondItem="3HF-ZM-0w0" secondAttribute="leading" constant="24" id="Lb8-uZ-fgD"/>
                                                            <constraint firstAttribute="trailing" secondItem="cKb-dQ-NVK" secondAttribute="trailing" constant="24" id="PTS-5B-Ubo"/>
                                                            <constraint firstAttribute="bottom" secondItem="cKb-dQ-NVK" secondAttribute="bottom" constant="16" id="dTZ-UP-i1Z"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="ErX-cE-xvo" firstAttribute="top" secondItem="HNV-Ae-Fui" secondAttribute="top" id="4TR-nS-57t"/>
                                            <constraint firstAttribute="trailing" secondItem="ErX-cE-xvo" secondAttribute="trailing" id="FKe-f0-gGp"/>
                                            <constraint firstAttribute="bottom" secondItem="ErX-cE-xvo" secondAttribute="bottom" id="oFz-9l-tl8"/>
                                            <constraint firstItem="ErX-cE-xvo" firstAttribute="leading" secondItem="HNV-Ae-Fui" secondAttribute="leading" id="vDD-p0-xIv"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="HNV-Ae-Fui" firstAttribute="trailing" secondItem="C4B-WY-J7M" secondAttribute="trailing" id="cAB-hK-J4b"/>
                                    <constraint firstItem="HNV-Ae-Fui" firstAttribute="top" secondItem="C4B-WY-J7M" secondAttribute="top" id="fEt-bE-jTI"/>
                                    <constraint firstItem="HNV-Ae-Fui" firstAttribute="width" secondItem="ScK-CN-2yJ" secondAttribute="width" id="hUP-Rp-Rfa"/>
                                    <constraint firstItem="HNV-Ae-Fui" firstAttribute="leading" secondItem="C4B-WY-J7M" secondAttribute="leading" id="lSO-8h-pjw"/>
                                    <constraint firstItem="HNV-Ae-Fui" firstAttribute="bottom" secondItem="C4B-WY-J7M" secondAttribute="bottom" id="xTY-Xi-INa"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="C4B-WY-J7M"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="ScK-CN-2yJ"/>
                            </scrollView>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="t3e-pH-cEB">
                                <rect key="frame" x="0.0" y="778" width="375" height="0.0"/>
                                <subviews>
                                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="iZA-83-n5W">
                                        <rect key="frame" x="0.0" y="0.0" width="320" height="72"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9Gc-Hc-wpR" customClass="MayaButton" customModule="PayMaya" customModuleProvider="target">
                                                <rect key="frame" x="24" y="0.0" width="272" height="56"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="SO0-Le-VxL"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <connections>
                                                    <action selector="didTapContinue" destination="yoZ-Xv-LIB" eventType="touchUpInside" id="c3k-h1-YVB"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="9Gc-Hc-wpR" firstAttribute="leading" secondItem="iZA-83-n5W" secondAttribute="leading" constant="24" id="9Mt-m8-33K"/>
                                            <constraint firstAttribute="trailing" secondItem="9Gc-Hc-wpR" secondAttribute="trailing" constant="24" id="eRu-bP-cQx"/>
                                            <constraint firstAttribute="bottom" secondItem="9Gc-Hc-wpR" secondAttribute="bottom" constant="16" id="hNO-ny-Byh"/>
                                            <constraint firstItem="9Gc-Hc-wpR" firstAttribute="top" secondItem="iZA-83-n5W" secondAttribute="top" id="p49-qW-kci"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Mph-pg-PE3"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="Mph-pg-PE3" firstAttribute="trailing" secondItem="t3e-pH-cEB" secondAttribute="trailing" id="FTM-9P-2WU"/>
                            <constraint firstItem="URq-Oc-h9X" firstAttribute="top" secondItem="Mph-pg-PE3" secondAttribute="top" id="Mof-sl-rKT"/>
                            <constraint firstItem="Mph-pg-PE3" firstAttribute="bottom" secondItem="t3e-pH-cEB" secondAttribute="bottom" id="Ush-Jk-ZM2"/>
                            <constraint firstItem="Mph-pg-PE3" firstAttribute="trailing" secondItem="URq-Oc-h9X" secondAttribute="trailing" id="ZGr-C3-xbF"/>
                            <constraint firstItem="t3e-pH-cEB" firstAttribute="top" secondItem="URq-Oc-h9X" secondAttribute="bottom" id="i6F-PV-QCO"/>
                            <constraint firstItem="t3e-pH-cEB" firstAttribute="leading" secondItem="Mph-pg-PE3" secondAttribute="leading" id="is2-FH-hTg"/>
                            <constraint firstItem="URq-Oc-h9X" firstAttribute="leading" secondItem="Mph-pg-PE3" secondAttribute="leading" id="rrB-io-fqH"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="ort-hs-ofk">
                        <barButtonItem key="leftBarButtonItem" title="Back" id="4Bp-4m-Gil">
                            <connections>
                                <action selector="didTapBack:" destination="yoZ-Xv-LIB" id="XOj-m8-L0B"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="iconImageView" destination="YbG-2T-QNA" id="18e-FB-CFq"/>
                        <outlet property="messageStackView" destination="iS7-ZX-7Kg" id="oQI-96-Yrw"/>
                        <outlet property="scrollView" destination="URq-Oc-h9X" id="uhU-7x-JBv"/>
                        <outlet property="scrollViewContainerView" destination="HNV-Ae-Fui" id="one-5F-ehB"/>
                        <outlet property="scrollingContinueButton" destination="cKb-dQ-NVK" id="ewZ-12-KFs"/>
                        <outlet property="scrollingContinueContainerView" destination="3HF-ZM-0w0" id="GY6-ow-S6t"/>
                        <outlet property="stickyContinueButton" destination="9Gc-Hc-wpR" id="9h4-Qd-kmP"/>
                        <outlet property="stickyContinueContainerView" destination="iZA-83-n5W" id="3Wj-UP-TFo"/>
                        <outlet property="titleLabel" destination="tu3-1t-WkN" id="Ygu-Uz-6se"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="DPh-3W-RSc" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1244" y="3576"/>
        </scene>
    </scenes>
    <resources>
        <image name="bannerBackgroundGradient" width="225" height="80"/>
        <image name="cashInV2Icon" width="20" height="20"/>
        <image name="eyeOpenedIcon" width="24" height="24"/>
        <image name="iconCloseDark" width="24" height="24"/>
        <image name="iconGreenDot" width="18" height="18"/>
        <image name="iconGreenRightChevron" width="24" height="24"/>
        <image name="iconInbox" width="20" height="21"/>
        <image name="iconInboxSupport" width="24" height="24"/>
        <image name="iconInboxV2" width="19.333333969116211" height="21.333333969116211"/>
        <image name="iconProfilePlaceholder" width="32" height="32"/>
        <image name="imageMissedTarget" width="200.33332824707031" height="200"/>
        <image name="sendMoneyV2Icon" width="20" height="20"/>
        <image name="walletRewardsMissionIconV2" width="128" height="128"/>
        <namedColor name="AdditionalPink">
            <color red="1" green="0.68627450980392157" blue="0.96470588235294119" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="BackgroundGrey1">
            <color red="0.97647058823529409" green="0.97647058823529409" blue="0.98039215686274506" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="BackgroundPrimaryWhite">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ButtonDisabledPrimaryGreen">
            <color red="0.0" green="0.65098039215686276" blue="0.31764705882352939" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ButtonPrimaryGreen">
            <color red="0.0" green="0.65098039215686276" blue="0.31764705882352939" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentGrey5">
            <color red="0.50196078431372548" green="0.50196078431372548" blue="0.50196078431372548" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentGrey6">
            <color red="0.33333333333333331" green="0.33725490196078434" blue="0.34901960784313724" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentPrimaryBlack">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentPrimaryGreen">
            <color red="0.0" green="0.65098039215686276" blue="0.31764705882352939" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="ContentPrimaryWhite">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey1">
            <color red="0.97647058823529409" green="0.97647058823529409" blue="0.98039215686274506" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey3">
            <color red="0.92549019607843142" green="0.92941176470588238" blue="0.93725490196078431" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey4">
            <color red="0.69411764705882351" green="0.70196078431372544" blue="0.70980392156862748" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey5">
            <color red="0.50196078431372548" green="0.50196078431372548" blue="0.50196078431372548" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="MenuPrimaryBlack">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="MenuPrimaryWhite">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryBlack">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryWhite">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
